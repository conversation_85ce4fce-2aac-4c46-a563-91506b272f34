<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android" >

    <!-- 背景  gradient是渐变,corners定义的是圆角 -->
    <item android:id="@android:id/background">
        <shape>
            <corners android:radius="10dp" />

            <solid android:color="#E5E5E5" />
        </shape>
    </item>
    <!-- 第二条进度条颜色 -->
    <!--<item android:id="@android:id/secondaryProgress">-->
        <!--<clip>-->
            <!--<shape>-->
                <!--<corners android:radius="10dip" />-->

                <!--<gradient-->
                    <!--android:angle="90.0"-->
                    <!--android:centerColor="#ac6079"-->
                    <!--android:centerY="0.45"-->
                    <!--android:endColor="#6c213a"-->
                    <!--android:startColor="#e71a5e" />-->
            <!--</shape>-->
        <!--</clip>-->
    <!--</item>-->
    <!-- 进度条 -->
    <item android:id="@android:id/progress">

        <clip>
            <shape>
                <corners android:radius="10dip" />
<!--                <solid android:color="#1C8EFF" />-->
                <gradient
                    android:endColor="#1C8EFF"
                    android:startColor="#ffffff"/>
            </shape>
        </clip>
    </item>

</layer-list>