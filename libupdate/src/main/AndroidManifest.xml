<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.allenliu.versionchecklib">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <application
        android:allowBackup="true"
        android:supportsRtl="true"
        android:requestLegacyExternalStorage="true"
        android:usesCleartextTraffic="true">
        <activity
            android:name=".core.VersionDialogActivity"
            android:launchMode="singleTask"
            android:theme="@style/versionCheckLibvtransparentTheme" />

        <provider
            android:name=".core.VersionFileProvider"
            android:authorities="${applicationId}.versionProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/versionchecklib_file_paths" />
        </provider>

        <activity
            android:name=".core.PermissionDialogActivity"
            android:launchMode="singleTask"
            android:theme="@style/versionCheckLibvtransparentTheme" />

        <service
            android:name=".core.MyService"
            android:enabled="true"

            android:exported="false" />
        <service
            android:name=".v2.ui.VersionService"
            android:exported="false"
            android:priority="1000" />

        <activity
            android:name=".v2.ui.UIActivity"
            android:launchMode="singleTask"
            android:theme="@style/versionCheckLibvtransparentTheme" />
        <activity
            android:name=".v2.ui.DownloadingActivity"
            android:launchMode="singleTask"
            android:theme="@style/versionCheckLibvtransparentTheme" />
        <activity
            android:name=".v2.ui.DownloadFailedActivity"
            android:launchMode="singleTask"
            android:theme="@style/versionCheckLibvtransparentTheme"></activity>
    </application>

</manifest>