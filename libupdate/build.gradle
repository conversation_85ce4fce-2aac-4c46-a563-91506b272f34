apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'

group='com.github.jikun2008'
android {
    compileSdkVersion 33
    resourcePrefix "versionchecklib"
    defaultConfig {
        minSdkVersion 14
        targetSdkVersion 33
        versionCode 1
        versionName version
    }
    lintOptions {
        abortOnError false
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    compileOnly 'androidx.appcompat:appcompat:1.1.0'
    implementation 'com.squareup.okhttp3:okhttp:4.3.1'
    implementation 'org.greenrobot:eventbus:3.1.1'
}
