pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
        maven {
            allowInsecureProtocol(true)
            url 'https://jitpack.io'
        }
        maven {
            allowInsecureProtocol(true)
            url "https://s01.oss.sonatype.org/content/groups/public"
        }
        maven{ url 'https://maven.aliyun.com/repository/jcenter'}
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url "https://repo.eclipse.org/content/repositories/paho-snapshots/" }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven {
            allowInsecureProtocol(true)
            url 'https://jitpack.io'
        }
        maven {
            allowInsecureProtocol(true)
            url "https://s01.oss.sonatype.org/content/groups/public"
        }
        maven{ url 'https://maven.aliyun.com/repository/jcenter'}
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url "https://repo.eclipse.org/content/repositories/paho-snapshots/" }
    }
}
rootProject.name = "android-cashier-retail"
include ':app'
include ':libyxlcommon'
include ':libupdate'
include ':MQTTLibrary'
