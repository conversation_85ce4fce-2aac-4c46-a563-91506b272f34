# Android收银零售系统 - 业务流程与通信交互文档

## 概述

本文档详细描述了Android收银零售系统中涉及的HTTP接口、MQTT通信、网络监听等与外部系统的通信交互，以业务流程为主线，提供完整的技术架构和实现细节。

## 系统架构

### 通信技术栈
- **HTTP通信**: 基于RxHttp框架的RESTful API调用
- **MQTT通信**: 基于Eclipse Paho的消息队列遥测传输
- **网络监听**: 实时监控网络状态变化
- **串口通信**: 电子秤、打印机等硬件设备通信
- **USB通信**: 小票打印机连接

### 核心模块
1. **收银模块** - 商品扫码、支付处理、订单管理
2. **会员模块** - 会员管理、积分系统、储值卡
3. **商品模块** - 商品管理、库存管理、供应商管理
4. **统计模块** - 销售统计、数据分析
5. **网单模块** - 在线订单处理、配送管理

## 业务流程详解

### 1. 系统初始化流程

#### 1.1 应用启动初始化
**入口**: [`MyApplication.java`](../app/src/main/java/com/yxl/cashier_retail/MyApplication.java)

```java
// 网络监听初始化
IntentFilter filter = new IntentFilter();
filter.addAction("android.net.conn.CONNECTIVITY_CHANGE");
registerReceiver(networkConnectChangedReceiver, filter);

// MQTT连接初始化
initMqtt();

// HTTP框架初始化
RxHttpManager.init(this);
```

**关键组件**:
- **网络监听**: [`NetworkConnectChangedReceiver.java`](../app/src/main/java/com/yxl/cashier_retail/network/NetworkConnectChangedReceiver.java)
- **MQTT助手**: [`MQTTHelper.java`](../app/src/main/java/com/yxl/cashier_retail/mqtt/MQTTHelper.java)
- **HTTP管理**: [`RxHttpManager.java`](../libyxlcommon/src/main/java/com/yxl/commonlibrary/http/RxHttpManager.java)

#### 1.2 数据同步流程
**触发点**: 启动页面 [`LauncherActivity.java`](../app/src/main/java/com/yxl/cashier_retail/ui/activity/LauncherActivity.java)

**API调用序列**:
1. **商品分类同步**: `ZURL.getCatePcList()` → `shopmanager/goodsKindInvented/queryGoodsInventedList.do`
2. **商品数据同步**: `ZURL.getGoodsListPc()` → `shopmanager/pc/pcGoods.do`
3. **支付方式配置**: `ZURL.getPaymentData()` → 本地数据库
4. **优惠活动同步**: `ZURL.getDiscountData()` → 本地数据库

### 2. 收银业务流程

#### 2.1 商品扫码识别
**主界面**: [`MainActivity.java`](../app/src/main/java/com/yxl/cashier_retail/ui/activity/MainActivity.java)

**扫码处理逻辑**:
```java
private void setScanResult(String code) {
    // 1. 商品条码识别
    if (code.length() == 13 || code.length() == 8) {
        // 查询商品信息
        searchGoodsByBarcode(code);
    }
    // 2. 支付码识别 (18位或特定格式)
    else if (code.length() == 18 || isPaymentCode(code)) {
        // 扫码支付流程
        getSaleListUnique(13); // 扫码支付
    }
}
```

#### 2.2 支付处理流程

**支付方式枚举**:
- 1: 现金支付
- 2: 支付宝
- 3: 微信支付  
- 4: 银行卡
- 5: 储值卡
- 8: 组合支付
- 13: 扫码支付
- 15: POS机支付
- 16: 刷脸支付

**核心支付API**:

##### 2.2.1 普通线下支付
**接口**: `ZURL.getPayment()` → `harricane/payOnline/cashierPay.do`
**实现**: `MainActivity.postPayment(int type)`

```java
// 支付参数构建
Map<String, Object> params = new HashMap<>();
params.put("shopUnique", getShopUnique());
params.put("saleListState", 3); // 已付款
params.put("sale_list_payment", type); // 支付方式
params.put("saleListPayDetail", arrayPayment.toString()); // 支付详情
```

##### 2.2.2 扫码支付
**接口**: `ZURL.getPaymentScan()` → `harricane/payOnline/yiTongPay.do`
**实现**: `MainActivity.postPaymentScan(int type)`

```java
// 扫码支付特有参数
params.put("auth_code", scanCode); // 授权码
params.put("saleListTotal", (int)(total * 100)); // 金额(分)
```

##### 2.2.3 刷脸支付
**接口**: `ZURL.getPaymentFace()` → `goBuy/cart/collectMoneyPay.do`
**实现**: `MainActivity.postPaymentFace()`

#### 2.3 订单状态查询
**接口**: `ZURL.getPaymentStatus()` → `harricane/payOnline/queryOrderYT.do`
**用途**: 查询扫码支付结果，处理支付中状态

### 3. 会员业务流程

#### 3.1 会员管理API
**基础接口**:
- **会员列表**: `ZURL.getMemberList()` → `harricane/customerCheckout/customerPageList.do`
- **会员详情**: `ZURL.getMemberInfo()` → `queryShopCusDetail.do`
- **会员新增**: `ZURL.getMemberAdd()` → `harricane/cuscheckout/addCus.do`
- **会员编辑**: `ZURL.getMemberEdit()` → `harricane/customerCheckout/saveCustomer.do`

#### 3.2 会员充值流程
**接口**: `ZURL.getMemberRecharge()` → `harricane/cuscheckout/recharge.do`
**充值方式**:
- 1: 现金充值
- 2: 微信充值
- 3: 支付宝充值
- 4: 存零充值

#### 3.3 人脸识别会员
**接口**: `ZURL.getFaceToMemberInfo()` → `shopmanager/app/shop/searchCustomerMsg.do`
**实现**: 通过摄像头捕获人脸特征，调用后端识别接口

### 4. 商品管理流程

#### 4.1 商品基础操作
**核心API**:
- **商品列表**: `ZURL.getGoodsList()` → `shopUpdate/goods/queryGoodsMessage.do`
- **商品详情**: `ZURL.getGoodsInfo()` → `shopUpdate/goods/goodsDetailAndroid.do`
- **商品新增**: `ZURL.getGoodsAdd()` → `shopUpdate/goods/v2/addGoods.do`
- **商品编辑**: `ZURL.getGoodsEdit()` → `shopUpdate/goods/v2/updateGoods.do`
- **商品删除**: `ZURL.getGoodsDel()` → `shopUpdate/goods/deleteShopsGoods.do`

#### 4.2 库存管理
**出入库接口**:
- **库存修改**: `ZURL.getGoodsStockEdit()` → `shopUpdate/stock/newStockRecord.do`
- **批量出入库**: `ZURL.getGoodsStockBatch()` → `shopUpdate/stock/addBatchStockRecord.do`
- **库存记录查询**: `ZURL.getStockRecord()` → `shopUpdate/stock/queryShopStockRecord.do`

#### 4.3 供应商管理
**供应商API**:
- **供应商列表**: `ZURL.getSupplierList()` → `harricane/html/supplier/querySupplierList.do`
- **供应商新增**: `ZURL.getSupplierAdd()` → `shopmanager/pc/addSupplier.do`
- **供应商详情**: `ZURL.getSupplierInfo()` → `shopUpdate/shopSupplier/querySupInfo.do`

### 5. 网单处理流程

#### 5.1 订单管理
**订单API**:
- **订单数量**: `ZURL.getOrderCount()` → `shopmanager/pc/selectOnlineOrderCountByStatus.do`
- **订单列表**: `ZURL.getOrderList()` → `shopmanager/pc/pcQuerySaleList.do`
- **订单详情**: `ZURL.getOrderInfo()` → `shopUpdate/saleList/querySaleListDetail.do`

#### 5.2 配送管理
**配送API**:
- **创建配送**: `ZURL.getOrderCreate()` → `shop/peisong/createOrder.do`
- **确认收货**: `ZURL.getOrderConfirm()` → `goBuy/my/confirmReceipt.do`
- **取消配送**: `ZURL.getOrderDeliveryCancel()` → `http://delivery.buyhoo.cc/outside/cancelDelivery`

#### 5.3 退款处理
**退款API**:
- **退款列表**: `ZURL.getRefundList()` → `shopUpdate/saleList/queryRetLists.do`
- **退款详情**: `ZURL.getRefundInfo()` → `shopUpdate/saleList/queryReturnDetail.do`
- **退款审核**: `ZURL.getRefundUpdate()` → `shopUpdate/saleList/modifyReturnMsg.do`

### 6. MQTT实时通信

#### 6.1 MQTT连接配置
**配置信息**:
```java
// MQTT服务器配置
private String mqttUrl = "tcp://**************:1883";
private String userName = "name";
private String password = "pass";

// 主题配置
public static final String TOPIC_MSG = "win_qt_cash_1.0";
public static final String TOPIC_SEND = "win_qt_cash_" + deviceId;
```

#### 6.2 消息处理流程
**实现**: [`MyApplication.setMqttData()`](../app/src/main/java/com/yxl/cashier_retail/MyApplication.java)

**消息类型**:
- `msg_init`: 设备初始化消息
- `msg_shutdown`: 设备关闭消息
- `goods_update`: 商品信息更新
- `order_notify`: 订单通知

#### 6.3 设备状态同步
```java
// 设备上线消息
String initMsg = "{\"ctrl\":\"msg_init\",\"ID\":" + deviceId + 
                ",\"data\":{\"shop_unique\":" + shopUnique + "}}";
publish(initMsg);

// 设备下线消息  
String shutdownMsg = "{\"ctrl\":\"msg_shutdown\",\"ID\":" + deviceId + "}";
publish(shutdownMsg);
```

### 7. 网络状态监控

#### 7.1 网络监听实现
**监听器**: [`NetworkConnectChangedReceiver.java`](../app/src/main/java/com/yxl/cashier_retail/network/NetworkConnectChangedReceiver.java)

**网络类型识别**:
- TYPE_WIFI (1): WiFi网络
- TYPE_MOBILE (2): 移动数据网络  
- TYPE_ETHERNET (3): 以太网
- TYPE_NONE (0): 无网络连接

#### 7.2 网络状态处理
```java
// 网络状态变化通知
NetworkChange.getInstance().notifyDataChange(network);

// UI状态更新
private void setNetwork() {
    if (MyApplication.mNetwork.isConnect()) {
        // 显示网络连接状态
        mBinding.vRight.ivNetwork.setImageResource(R.mipmap.ic_network001);
        mBinding.vRight.tvNetwork.setText("已连接");
    }
}
```

### 8. 统计分析流程

#### 8.1 销售统计API
**统计接口**:
- **主页统计**: `ZURL.getStatisticsMain()` → `shopmanager/pc/mainMessageForPC.do`
- **销售走势**: `ZURL.getStatisticsSales()` → `shopmanager/pc/salesTrend.do`
- **品类占比**: `ZURL.getStatisticsClass()` → `shopmanager/pc/salesCategoryRatio.do`
- **支付占比**: `ZURL.getStatisticsPayType()` → `shopmanager/pc/paymentTypeRatio.do`

#### 8.2 商品统计
**商品分析API**:
- **热销商品**: `ZURL.getTopGoods()` → `shopmanager/pc/topGoods.do`
- **滞销商品**: `ZURL.getUnSalableTopGoods()` → `shopmanager/pc/queryUnmarketableTop5.do`
- **销售统计**: `ZURL.getGoodsSaleStatistics()` → `shopmanager/pc/queryGoodsSaleStatistics.do`

### 9. 系统设置与配置

#### 9.1 员工管理
**员工API**:
- **员工登录**: `ZURL.getLogin()` → `shopmanager/pc/pcStaffLogin.do`
- **员工退出**: `ZURL.getLoginOut()` → `shopmanager/pc/staffSignOut.do`
- **交班记录**: `ZURL.getShiftRecord()` → `shopUpdate/shopsStaff/queryHandoverRecord.do`

#### 9.2 店铺配置
**配置API**:
- **店铺信息**: `ZURL.getShopInfoEdit()` → `shopUpdate/shopsStaff/updateShopsMessage.do`
- **配送设置**: `ZURL.getShopDelivery()` → `shopUpdate/cash/queryShopDelivery.do`

### 10. 商城补货流程

#### 10.1 商城商品API
**商城接口**:
- **首页活动**: `ZURL.getMallMarketing()` → `purchase-app/shopping/getMarketing.do`
- **商品分类**: `ZURL.getMallCateList()` → `shop/shopping/getGoodsKindList.do`
- **商品列表**: `ZURL.getMallGoodList()` → `purchase-app/shopping/v2/getGoodList.do`
- **商品详情**: `ZURL.getMallGoodsInfo()` → `purchase-app/shopping/v2/getGoodDetail.do`

#### 10.2 购物车管理
**购物车API**:
- **购物车列表**: `ZURL.getMallCartList()` → `purchase-app/shopping/getCartListNew.do`
- **购物车更新**: `ZURL.getMallCartUpdate()` → `purchase-app/shopping/updateShoppingCart.do`
- **删除购物车**: `ZURL.getMallCartDel()` → `purchase-app/shopping/deleteShoppingCartMore.do`
- **购物车结算**: `ZURL.getMallCartSettlement()` → `purchase-app/shopping/getSettlementPageNew.do`

#### 10.3 补货计划管理
**补货API**:
- **补货计划列表**: `ZURL.getRestockPlanList()` → `shopUpdate/restockPlan/queryRestockPlanList.do`
- **添加补货计划**: `ZURL.getRestockAdd()` → `shopUpdate/restockPlan/addRestockPlan.do`
- **修改补货状态**: `ZURL.getRestockUpdate()` → `shopUpdate/restockPlan/updatePlanStatus.do`
- **删除补货计划**: `ZURL.getRestockDel()` → `shopUpdate/restockPlan/deleteRestockPlan.do`

### 11. 营销活动管理

#### 11.1 促销活动API
**营销接口**:
- **促销活动列表**: `ZURL.getPromotionActivityList()` → `shopUpdate/goods/queryPromotionActivityList.do`

**活动类型**:
- **1**: 商品折扣
- **2**: 商品满赠
- **3**: 订单促销
- **4**: 单品促销

**活动状态**:
- **时间状态**: 1.未开始 2.已开始 3.已结束
- **启用状态**: 1.正常 2.停用
- **进度状态**: 0.未开始 1.进行中 2.过期

#### 11.2 营销数据结构
**实体类**: [`MarketingData.java`](../app/src/main/java/com/yxl/cashier_retail/ui/bean/MarketingData.java)

```java
// 营销活动数据结构
public class MarketingData {
    private int promotion_activity_id; // 活动ID
    private String start_time; // 开始时间
    private String end_time; // 结束时间
    private int isProgress; // 时间状态：0未开始 1进行中 2过期
    private String type; // 活动类型：1商品折扣 2商品满赠 3订单促销 4单品促销
    private String promotion_activity_name; // 活动名称
    private int status; // 状态：1正常 2停用
}
```

### 12. 系统维护功能

#### 12.1 版本更新管理
**版本检查**: `ZURL.getVersion()` → `shopmanager/app/shop/updateAppCheck.do`
**实现**: [`MainActivity.checkUpgrade()`](../app/src/main/java/com/yxl/cashier_retail/ui/activity/MainActivity.java)

**更新流程**:
```java
// 版本检查
AllenVersionChecker
    .getInstance()
    .requestVersion()
    .setRequestUrl(ZURL.getVersion())
    .request();

// 下载更新
AllenVersionChecker
    .getInstance()
    .downloadOnly()
    .setDownloadUrl(downloadUrl)
    .executeMission(context);
```

#### 12.2 文件上传功能
**文件上传**: `ZURL.getUploadFile()` → `shopUpdate/loanMoney/uploadFile.do`
**用途**: 购销单据凭证上传、商品图片上传等

**上传实现**:
```java
// 文件上传参数
Map<String, Object> params = new HashMap<>();
params.put("file", imageFile); // 文件对象
params.put("type", uploadType); // 上传类型

// 调用上传接口
RXHttpUtil.requestByFormPostAsResponse(this,
    ZURL.getUploadFile(), params, String.class, listener);
```

#### 12.3 密码管理
**验证码获取**: `ZURL.getCode()` → `shopUpdate/shopsStaff/sendMessage.do`
**密码修改**: `ZURL.getUpdatePwd()` → `shopUpdate/cash/updateShopsPwd.do`

**密码修改流程**:
1. 获取短信验证码
2. 验证原密码
3. 设置新密码（MD5加密）
4. 提交修改请求

## 环境配置

### 服务器环境
```java
// 开发环境
public final static String URL_DEV = "https://dev-global.buyhoo.cc/";

// 测试环境
public final static String URL_DEBUG = "http://test170.buyhoo.cc/";

// 生产环境
public final static String URL_RELEASE = "http://buyhoo.cc/";

// 动态切换
public static String ONLINE_URL = BuildConfig.DEBUG ? URL_DEBUG : URL_RELEASE;
```

### MQTT配置
```java
// MQTT服务器
private String mqttUrl = "tcp://**************:1883";

// 连接参数
connectOptions.setAutomaticReconnect(true);
connectOptions.setCleanSession(false);
connectOptions.setConnectionTimeout(30);
connectOptions.setKeepAliveInterval(10);
```

## 硬件设备集成

### 1. 串口通信
**用途**: 电子秤数据读取
**实现**: [`MyApplication.initSerialPort()`](../app/src/main/java/com/yxl/cashier_retail/MyApplication.java)

```java
// 串口配置
String portPath = "/dev/ttyS1"; // 串口路径
int baudRate = 9600; // 波特率

// 数据处理
weight = ByteUtils.getSerialPortScaleData(event.getBytes());
```

### 2. USB打印机
**用途**: 小票打印
**实现**: [`PrintReceiptUsbUtils.java`](../app/src/main/java/com/yxl/cashier_retail/utils/PrintReceiptUsbUtils.java)

```java
// USB连接
POSConnect.createDevice(POSConnect.DEVICE_TYPE_USB);

// 打印小票
PrintReceiptUsbUtils.printPayment(context, cartList, discount,
    total, totalReal, arrayPayment, memberData, salePoints,
    cusPoints, saleListUnique, bitmap);
```

### 3. 摄像头集成
**用途**: 扫码、人脸识别
**实现**: [`CameraUtils.java`](../app/src/main/java/com/yxl/cashier_retail/utils/CameraUtils.java)

```java
// 摄像头检查
if (!CameraUtils.hasCamera()) {
    showToast("请连接摄像头");
    return;
}

// 扫码结果处理
mBinding.vLeft.etSearch.setScanResultListener(this::setScanResult);
```

## 错误处理与重试机制

### HTTP请求错误处理
```java
// 网络检查
if (!NetworkUtils.isConnected()) {
    requestListener.onError("网络连接异常");
    return;
}

// 超时重试
RXHttpUtil.requestByFormPostAsOriginalResponseTimeOut(
    this, url, params, timeout, listener);
```

### MQTT重连机制
```java
// 自动重连配置
connectOptions.setAutomaticReconnect(true);

// 连接失败处理
public void onFailure(IMqttToken asyncActionToken) {
    isMqttConnect = false;
    // 重连逻辑
}
```

### 离线支付处理
```java
// 离线收银
if (!NetworkUtils.isConnected()) {
    OrderOfflineData orderOfflineData = new OrderOfflineData(
        DateUtils.getCurrentDate(DateUtils.PATTERN_DAY),
        3, // 已付款状态
        total, totalReal, type, "", 2,
        arrayPayment.toString()
    );
    if (orderOfflineData.save()) {
        showDialogPaymentStatus(0, "", type);
    }
}
```

## 安全机制

### 数据加密
- 密码MD5加密存储
- HTTPS通信加密
- 本地数据库加密

### 权限控制
- 员工角色权限验证
- 操作日志记录
- 敏感操作二次确认

### 支付安全
```java
// 支付验证
params.put("saleListCashier", getStaffUnique()); // 收银员验证
params.put("shopUnique", getShopUnique()); // 店铺验证

// 金额验证
params.put("saleListTotal", (int)(total * 100)); // 金额转分，避免精度问题
```

## 性能优化

### 数据缓存
- 商品信息本地缓存
- 图片缓存机制
- 网络请求缓存

### 内存管理
- 图片压缩处理
- 列表数据分页加载
- 及时释放资源

### 数据库优化
```java
// 使用LitePal ORM框架
LitePal.findAll(GoodsData.class);
LitePal.where("goods_barcode = ?", barcode).findFirst(GoodsData.class);

// 批量操作
LitePal.saveAll(dataList);
```

## 业务流程图

### 收银流程
```
扫码/输入商品 → 商品识别 → 添加到购物车 → 选择支付方式 →
支付处理 → 支付结果 → 打印小票 → 完成交易
```

### 支付流程
```
选择支付方式 → 创建订单号 → 调用支付接口 →
支付状态查询 → 支付成功/失败处理 → 更新订单状态
```

### MQTT通信流程
```
设备启动 → MQTT连接 → 发送初始化消息 →
订阅主题 → 接收消息 → 处理业务逻辑 →
发送响应消息 → 设备关闭时发送下线消息
```

## 总结

本Android收银零售系统通过HTTP RESTful API、MQTT消息队列、网络状态监听等多种通信方式，实现了完整的零售业务流程。系统具备良好的扩展性、稳定性和用户体验，支持离线操作和实时数据同步，满足现代零售场景的各种需求。

### 关键技术特点：
1. **多协议通信**: HTTP + MQTT + 串口通信
2. **实时数据同步**: MQTT消息推送机制
3. **离线支持**: 本地数据库缓存
4. **硬件集成**: 扫码枪、电子秤、打印机
5. **支付集成**: 多种支付方式支持
6. **数据统计**: 完整的销售分析功能

### 系统优势：
- **稳定可靠**: 完善的错误处理和重试机制
- **安全保障**: 多层次的安全验证机制
- **性能优化**: 缓存机制和内存管理
- **扩展性强**: 模块化设计，易于扩展新功能
- **用户友好**: 直观的操作界面和语音提示

该系统为零售商提供了一个功能完整、技术先进的收银解决方案，能够有效提升收银效率和用户体验。

## 附录：API接口索引

### A. 收银支付相关
| 功能 | 接口方法 | 接口路径 | 说明 |
|------|----------|----------|------|
| 创建订单号 | `getSaleListUnique()` | `shopUpdate/appPay/createSaleListUnique.do` | 支付前创建唯一订单号 |
| 普通支付 | `getPayment()` | `harricane/payOnline/cashierPay.do` | 现金、微信、支付宝等支付 |
| 扫码支付 | `getPaymentScan()` | `harricane/payOnline/yiTongPay.do` | 易通金服扫码支付 |
| 刷脸支付 | `getPaymentFace()` | `goBuy/cart/collectMoneyPay.do` | 人脸识别支付 |
| 支付状态查询 | `getPaymentStatus()` | `harricane/payOnline/queryOrderYT.do` | 查询支付结果 |
| 取消收款 | `getPaymentCancel()` | `harricane/payOnline/cancelOrder.do` | 取消支付订单 |

### B. 会员管理相关
| 功能 | 接口方法 | 接口路径 | 说明 |
|------|----------|----------|------|
| 会员列表 | `getMemberList()` | `harricane/customerCheckout/customerPageList.do` | 分页查询会员列表 |
| 会员详情 | `getMemberInfo()` | `queryShopCusDetail.do` | 查询会员详细信息 |
| 会员新增 | `getMemberAdd()` | `harricane/cuscheckout/addCus.do` | 新增会员信息 |
| 会员编辑 | `getMemberEdit()` | `harricane/customerCheckout/saveCustomer.do` | 修改会员信息 |
| 会员充值 | `getMemberRecharge()` | `harricane/cuscheckout/recharge.do` | 储值卡充值 |
| 人脸识别 | `getFaceToMemberInfo()` | `shopmanager/app/shop/searchCustomerMsg.do` | 通过人脸识别会员 |

### C. 商品管理相关
| 功能 | 接口方法 | 接口路径 | 说明 |
|------|----------|----------|------|
| 商品列表 | `getGoodsList()` | `shopUpdate/goods/queryGoodsMessage.do` | 分页查询商品列表 |
| 商品详情 | `getGoodsInfo()` | `shopUpdate/goods/goodsDetailAndroid.do` | 查询商品详细信息 |
| 商品新增 | `getGoodsAdd()` | `shopUpdate/goods/v2/addGoods.do` | 新增商品信息 |
| 商品编辑 | `getGoodsEdit()` | `shopUpdate/goods/v2/updateGoods.do` | 修改商品信息 |
| 商品删除 | `getGoodsDel()` | `shopUpdate/goods/deleteShopsGoods.do` | 删除商品 |
| 库存修改 | `getGoodsStockEdit()` | `shopUpdate/stock/newStockRecord.do` | 商品出入库 |

### D. 订单管理相关
| 功能 | 接口方法 | 接口路径 | 说明 |
|------|----------|----------|------|
| 订单数量 | `getOrderCount()` | `shopmanager/pc/selectOnlineOrderCountByStatus.do` | 按状态统计订单数量 |
| 订单列表 | `getOrderList()` | `shopmanager/pc/pcQuerySaleList.do` | 查询订单列表 |
| 订单详情 | `getOrderInfo()` | `shopUpdate/saleList/querySaleListDetail.do` | 查询订单详情 |
| 创建配送 | `getOrderCreate()` | `shop/peisong/createOrder.do` | 创建配送订单 |
| 确认收货 | `getOrderConfirm()` | `goBuy/my/confirmReceipt.do` | 确认收货完成 |
| 取消订单 | `getOrderCancel()` | `goBuy/my/cancelSaleList.do` | 取消订单 |

### E. 统计分析相关
| 功能 | 接口方法 | 接口路径 | 说明 |
|------|----------|----------|------|
| 主页统计 | `getStatisticsMain()` | `shopmanager/pc/mainMessageForPC.do` | 收银主页统计数据 |
| 销售走势 | `getStatisticsSales()` | `shopmanager/pc/salesTrend.do` | 销售额趋势分析 |
| 品类占比 | `getStatisticsClass()` | `shopmanager/pc/salesCategoryRatio.do` | 销售品类占比 |
| 支付占比 | `getStatisticsPayType()` | `shopmanager/pc/paymentTypeRatio.do` | 支付方式占比 |
| 热销商品 | `getTopGoods()` | `shopmanager/pc/topGoods.do` | 热销商品TOP5 |
| 滞销商品 | `getUnSalableTopGoods()` | `shopmanager/pc/queryUnmarketableTop5.do` | 滞销商品TOP5 |

### F. 系统管理相关
| 功能 | 接口方法 | 接口路径 | 说明 |
|------|----------|----------|------|
| 员工登录 | `getLogin()` | `shopmanager/pc/pcStaffLogin.do` | 员工登录接班 |
| 员工退出 | `getLoginOut()` | `shopmanager/pc/staffSignOut.do` | 员工退出交班 |
| 版本检查 | `getVersion()` | `shopmanager/app/shop/updateAppCheck.do` | 检查应用更新 |
| 文件上传 | `getUploadFile()` | `shopUpdate/loanMoney/uploadFile.do` | 上传文件 |
| 获取验证码 | `getCode()` | `shopUpdate/shopsStaff/sendMessage.do` | 获取短信验证码 |
| 修改密码 | `getUpdatePwd()` | `shopUpdate/cash/updateShopsPwd.do` | 修改登录密码 |

## 技术支持

### 开发环境配置
- **Android Studio**: 4.0+
- **Gradle**: 6.0+
- **Android SDK**: API 21+
- **Java版本**: JDK 8+

### 依赖库版本
- **RxHttp**: 网络请求框架
- **Eclipse Paho**: MQTT通信库
- **LitePal**: 数据库ORM框架
- **FastJson**: JSON解析库
- **EventBus**: 事件总线框架

### 联系方式
如有技术问题或需要支持，请联系开发团队。

---

*文档版本: v1.0*
*最后更新: 2024年*
*维护团队: Android收银零售系统开发组*
