<?xml version="1.0" encoding="utf-8"?>
<litepal>
    <dbname value="cashier_retail_data" />
    <version value="7" />

    <!--登录-->
    <list>
        <mapping class="com.yxl.commonlibrary.bean.LoginData" />
    </list>

    <!--商品列表-->
    <list>
        <mapping class="com.yxl.cashier_retail.ui.bean.GoodsData" />
    </list>

    <!--虚拟分类-->
    <list>
        <mapping class="com.yxl.cashier_retail.ui.bean.CatePcData" />
    </list>

    <!--虚拟分类下商品列表-->
    <list>
        <mapping class="com.yxl.cashier_retail.ui.bean.GoodsPcData" />
    </list>

    <!--商品一级分类-->
    <list>
        <mapping class="com.yxl.cashier_retail.ui.bean.CateData" />
    </list>

    <!--商品二级分类-->
    <list>
        <mapping class="com.yxl.cashier_retail.ui.bean.CateChildData" />
    </list>

    <!--支付方式-->
    <list>
        <mapping class="com.yxl.cashier_retail.ui.bean.PaymentData" />
    </list>

    <!--折扣-->
    <list>
        <mapping class="com.yxl.cashier_retail.ui.bean.DiscountData" />
    </list>

    <!--挂单-->
    <list>
        <mapping class="com.yxl.cashier_retail.ui.bean.HangData" />
    </list>

    <!--离线收银-->
    <list>
        <mapping class="com.yxl.cashier_retail.ui.bean.OrderOfflineData" />
    </list>

</litepal>