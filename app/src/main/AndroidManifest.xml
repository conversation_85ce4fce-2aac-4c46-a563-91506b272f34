<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <!-- 高德地图定位 -->
    <!-- 允许获取设备和运营商信息，用于问题排查和网络定位（无gps情况下的定位），若需网络定位功能则必选 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" /> <!-- 后台获取位置信息，若需后台定位则必选 -->
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" /> <!-- 用于申请调用A-GPS模块,卫星定位加速 -->
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" /> <!-- 允许写设备缓存，用于问题排查 -->
    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" /> <!-- 高德地图定位 -->
    <!-- 允许程序改变网络连接状态 -->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" /> <!-- 从扩展存储读取权限 -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" /> <!-- 允许程序收到广播后快速收到下一个广播 -->
    <uses-permission android:name="android.permission.BROADCAST_STICKY" /> <!-- 忽略电池优化权限 -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" /> <!-- 29以后程序需要使用全屏通知 -->
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" /> <!-- 悬浮窗权限 -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /> <!-- IM SDK required start -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" /> <!-- 允许程序振动 -->
    <uses-permission android:name="android.permission.VIBRATE" /> <!-- 访问网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" /> <!-- 麦克风权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" /> <!-- 相机权限 -->
    <uses-permission android:name="android.permission.CAMERA" /> <!-- 获取运营商信息，用于支持提供运营商信息相关的接口 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 这个权限用于访问GPS定位(用于定位消息，如果不用定位相关可以移除) -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- 用于访问wifi网络信息 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 用于获取wifi的获取权限 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- 允许程序在手机屏幕关闭后后台进程仍然运行 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- 允许程序修改声音设置信息 -->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" /> <!-- 捕获屏幕所需权限，Q后新增权限(多人音视频屏幕分享使用) -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" /> <!-- 对于 Android 12.0 及以上设备，还需要添加如下权限： -->
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" /> <!-- IM SDK required end -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" /> <!-- 百度地图定位配置(其他定位权限SDK必需权限中已包含) start -->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> <!-- 这个权限用于进行网络定位 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- 百度地图定位配置 end -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" /> <!-- bugly start -->
    <uses-permission
        android:name="android.permission.READ_LOGS"
        tools:ignore="ProtectedPermissions" /> <!-- bugly end -->
    <!-- 魅族推送配置 start -->
    <!-- 兼容 flyme5.0 以下版本，魅族内部集成 pushSDK 必填，不然无法收到消息 -->
    <uses-permission android:name="com.meizu.flyme.push.permission.RECEIVE" />

    <permission
        android:name="${applicationId}.push.permission.MESSAGE"
        android:protectionLevel="signature" />

    <uses-permission android:name="${applicationId}.push.permission.MESSAGE" /> <!-- 兼容 flyme3.0 配置权限 -->
    <uses-permission android:name="com.meizu.c2dm.permission.RECEIVE" />

    <permission
        android:name="${applicationId}.permission.C2D_MESSAGE"
        android:protectionLevel="signature" />

    <uses-permission android:name="${applicationId}.permission.C2D_MESSAGE" /> <!-- 魅族推送配置 end -->
    <!-- Oppo推送配置（如果应用无透传权限，则不用配置） start -->
    <uses-permission android:name="com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE" />
    <uses-permission android:name="com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE" /> <!-- Oppo推送配置 end -->
    <!-- Mi推送配置 start -->
    <permission
        android:name="${applicationId}.permission.MIPUSH_RECEIVE"
        android:protectionLevel="signature" />

    <uses-permission android:name="${applicationId}.permission.MIPUSH_RECEIVE" /> <!-- 微信支付，android11以上防止双开app，无法唤起 -->
    <queries>
        <package android:name="com.tencent.mm" />
    </queries> <!-- 适配Android13，弹出通知必须权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Android 13及以上相册、视频权限 -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

    <!--广播-开机启动-->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <!--    <uses-permission android:name="android.permission.SH"-->

    <!--语音播报：speak failed: not bound to TTS engine-->
    <queries>
        <intent>
            <action android:name="android.intent.action.TTS_SERVICE" />
        </intent>
    </queries>

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">

        <meta-data
            android:name="design_width_in_dp"
            android:value="960" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="540" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- 开机自启广播 -->
        <receiver
            android:name=".receiver.BootCompleteReceiver"
            android:enabled="true"
            android:exported="true">
            <!-- 接收启动完成的广播 -->
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>

        <!--网络监听广播-->
        <receiver android:name=".network.NetworkConnectChangedReceiver" />

        <!--MQTT服务-->
        <service android:name="com.itfitness.mqttlibrary.MqttService" />

        <!--启动页-->
        <activity
            android:name=".ui.activity.LauncherActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true"
            android:screenOrientation="landscape">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

        </activity>

        <!--登录-->
        <activity
            android:name=".ui.activity.LoginActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--选择域名-->
        <activity
            android:name=".ui.activity.TestActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape" />

        <!--忘记密码-->
        <activity
            android:name=".ui.activity.ForgetActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--查看大图-->
        <activity
            android:name=".ui.activity.ImgBigActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape" />

        <!--主函数（收银）-->
        <activity
            android:name=".ui.activity.MainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--挂单-->
        <activity
            android:name=".ui.activity.HangOrderActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--商品管理-->
        <activity
            android:name=".ui.activity.GoodsActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--商品管理-添加商品到首页分类-->
        <activity
            android:name=".ui.activity.CateAddGoodsActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--入库-->
        <activity
            android:name=".ui.activity.InActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--商品详情-->
        <activity
            android:name=".ui.activity.GoodsEditActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--查询-->
        <activity
            android:name=".ui.activity.QueryActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--统计-->
        <activity
            android:name=".ui.activity.StatisticsActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape" />

        <!--网单-->
        <activity
            android:name=".ui.activity.OrderActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--会员管理-->
        <activity
            android:name=".ui.activity.MemberActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--补货-->
        <activity
            android:name=".ui.activity.MallActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--商城-商品列表-->
        <activity
            android:name=".ui.activity.MallGoodsActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--商城-商品详情-->
        <activity
            android:name=".ui.activity.MallGoodsInfoActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--商城-购物车-->
        <activity
            android:name=".ui.activity.MallCartActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--商城-提交订单-->
        <activity
            android:name=".ui.activity.MallOrderSettlementActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--商城-付款-->
        <activity
            android:name=".ui.activity.MallPaymentActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--自采补货-创建补货计划-->
        <activity
            android:name=".ui.activity.RestockEditActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--自采补货-补货计划预览-->
        <activity
            android:name=".ui.activity.RestockPreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--供货商管理-分类管理-->
        <activity
            android:name=".ui.activity.SupplierCateActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--供货商管理-新增/编辑-->
        <activity
            android:name=".ui.activity.SupplierEditActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--供货商管理-详情-->
        <activity
            android:name=".ui.activity.SupplierInfoActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--营销-->
        <activity
            android:name=".ui.activity.MarketingActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--交班-->
        <activity
            android:name=".ui.activity.ShiftActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--设置-->
        <activity
            android:name=".ui.activity.SettingActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--设置-配件设置-价签设置-自定义价签模版-->
        <activity
            android:name=".ui.activity.TagsCustomActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--连接打印机-蓝牙-->
        <activity
            android:name=".printer.PrinterSettingActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <!--交接班-离线订单-->
        <activity
            android:name=".ui.activity.OrderOfflineActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="landscape"
            android:windowSoftInputMode="stateHidden|adjustPan" />

    </application>

</manifest>