package com.yxl.cashier_retail.network;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.util.Log;

/**
 * Describe:网络监听广播
 * Created by jinga<PERSON> on 2024/7/5
 */
public class NetworkConnectChangedReceiver extends BroadcastReceiver {

    Network network = new Network();

    @Override
    public void onReceive(Context context, Intent intent) {

        //网络状态监听
        if (ConnectivityManager.CONNECTIVITY_ACTION.equals(intent.getAction())) {
            ConnectivityManager manager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            @SuppressLint("MissingPermission") NetworkInfo activeNetworkInfo = manager.getActiveNetworkInfo();
            if (activeNetworkInfo != null) {
                if (activeNetworkInfo.isConnected()) {
//                    network.setConnected(true);
                    network.setConnect(true);
                    /**
                     * TYPE_NONE: 表示没有网络连接，值为-1。
                     * TYPE_MOBILE: 表示设备正在使用移动数据网络，比如2G, 3G, 4G, 5G等。这个类型的值为0。
                     * TYPE_WIFI: 表示设备正在使用Wi-Fi网络。这个类型的值为1。
                     * TYPE_WIMAX: 表示设备正在使用WiMAX（Worldwide Interoperability for Microwave Access）网络，这是一种提供城域网范围内的无线宽带接入技术。这个类型的值为2。
                     * TYPE_BLUETOOTH: 表示设备正在使用蓝牙网络进行数据传输。这个类型的值为3。
                     * TYPE_VPN: 表示设备正在使用虚拟专用网络（Virtual Private Network）。这个类型的值为4。
                     * TYPE_ETHERNET: 表示设备正在使用以太网进行连接。这个类型的值为5。
                     * TYPE_MOBILE_DUN: 表示设备正在使用移动数据网络作为拨号网络（Dial-up Networking）。这个类型的值为11。
                     * TYPE_MOBILE_HIPRI: 表示设备正在使用高优先级的移动数据网络，通常用于语音通话等。这个类型的值为12。
                     * TYPE_MOBILE_MMS: 表示设备正在使用移动数据网络传输多媒体消息服务（Multimedia Messaging Service）。这个类型的值为13。
                     * TYPE_MOBILE_SUPL: 表示设备正在使用移动数据网络进行辅助全球定位系统（Assisted Global Positioning System）。这个类型的值为14。
                     * TYPE_MOBILE_FOTA: 表示设备正在使用移动数据网络进行固件空中升级（Firmware Over-The-Air）。这个类型的值为15。
                     * TYPE_MOBILE_IMS: 表示设备正在使用移动数据网络进行IP多媒体子系统（IP Multimedia Subsystem）。这个类型的值为16。
                     * TYPE_MOBILE_CBS: 表示设备正在使用移动数据网络进行小区广播服务（Cell Broadcast Service）。这个类型的值为17。
                     */
                    switch (activeNetworkInfo.getType()) {
                        case ConnectivityManager.TYPE_WIFI:
//                            network.setWifi(true);
                            network.setConnectType(1);
                            //通知观察者网络状态已改变
                            NetworkChange.getInstance().notifyDataChange(network);
                            Log.e("111111", "当前wifi连接可用");
                            break;
                        case ConnectivityManager.TYPE_MOBILE:
//                            network.setMobile(true);
                            network.setConnectType(2);
                            //通知观察者网络状态已改变
                            NetworkChange.getInstance().notifyDataChange(network);
                            Log.e("111111", "当前移动网络连接可用");
                            break;
                        case ConnectivityManager.TYPE_ETHERNET:
                            network.setConnectType(3);
                            break;
                        default:
                            network.setConnectType(4);
                            break;
                    }
                    //通知观察者网络状态已改变
                    NetworkChange.getInstance().notifyDataChange(network);
                } else {
//                    network.setConnected(false);
                    network.setConnect(false);
                    network.setConnectType(0);
                    //通知观察者网络状态已改变
                    NetworkChange.getInstance().notifyDataChange(network);
                    Log.e("111111", "当前没有网络连接，请确保你已经打开网络");
                }
            } else {
//                network.setWifi(false);
//                network.setMobile(false);
//                network.setConnected(false);
                network.setConnect(false);
                network.setConnectType(0);
                //通知观察者网络状态已改变
                NetworkChange.getInstance().notifyDataChange(network);
                Log.e("111111", "当前没有网络连接，请确保你已经打开网络");
            }
        }
    }
}
