package com.yxl.cashier_retail.network;

import java.util.Observable;

/**
 * Describe:被观察者类
 * Created by jingang on 2024/7/5
 */
public class NetworkChange extends Observable {

    private static NetworkChange instance = null;

    public static NetworkChange getInstance() {
        if (null == instance) {
            instance = new NetworkChange();
        }
        return instance;
    } //通知观察者数据改变

    public void notifyDataChange(Network net) {
        //被观察者怎么通知观察者数据有改变了呢？？这里的两个方法是关键。
        setChanged();
        notifyObservers(net);
    }

}