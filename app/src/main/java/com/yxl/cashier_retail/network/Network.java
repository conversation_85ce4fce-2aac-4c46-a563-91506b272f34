package com.yxl.cashier_retail.network;

/**
 * Describe:Network数据类
 * Created by jingang on 2024/7/5
 */
public class Network {
    private boolean connect;//是否连接
    private int connectType;//连接方式 0.未连接 1.WiFi 2.移动网络 3.以太网 4.未知
    private int level;//信号强弱等级

    public Network() {
    }

    public boolean isConnect() {
        return connect;
    }

    public void setConnect(boolean connect) {
        this.connect = connect;
    }

    public int getConnectType() {
        return connectType;
    }

    public void setConnectType(int connectType) {
        this.connectType = connectType;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }
}
