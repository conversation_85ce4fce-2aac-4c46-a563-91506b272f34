package com.yxl.cashier_retail.ui.bean;

/**
 * Describe:统计-营业额24H分布（实体类）
 * Created by jingang on 2024/8/30
 */
public class StatisticsSalesByHourData {
    /**
     * yesSaleTotal : 0.0
     * saleTotal : 0.0
     * time : 0
     */

    private double yesSaleTotal;//昨日的营业额
    private double saleTotal;//今日的营业额
    private int time;//时间

    public double getYesSaleTotal() {
        return yesSaleTotal;
    }

    public void setYesSaleTotal(double yesSaleTotal) {
        this.yesSaleTotal = yesSaleTotal;
    }

    public double getSaleTotal() {
        return saleTotal;
    }

    public void setSaleTotal(double saleTotal) {
        this.saleTotal = saleTotal;
    }

    public int getTime() {
        return time;
    }

    public void setTime(int time) {
        this.time = time;
    }
}
