package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.GoodsBatchData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:商品出库-批次（适配器）
 * Created by jingang on 2024/7/2
 */
public class GoodsStockBatchAdapter extends BaseAdapter<GoodsBatchData> {

    public GoodsStockBatchAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_stock_batch;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvNo, tvDateStart, tvDateEnd, tvPrice, tvCount;
        tvNo = holder.getView(R.id.tvItemNo);
        tvDateStart = holder.getView(R.id.tvItemDateStart);
        tvDateEnd = holder.getView(R.id.tvItemDateEnd);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvCount = holder.getView(R.id.tvItemCount);
        EditText etCount = holder.getView(R.id.etItemCount);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
        }
        double goodsCount = mDataList.get(position).getGoodsCount();
        TextWatcher watcher = new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String str = s.toString().trim();
                double count;
                if (TextUtils.isEmpty(str)) {
                    count = 0;
                } else {
                    count = Double.parseDouble(str);
                    if (count > goodsCount) {
                        count = goodsCount;
                        etCount.setText(DFUtils.getNum4(count));
                    }
                    mDataList.get(position).setCount(count);
                }
                if (listener != null) {
                    listener.onCount(count, position);
                }
            }
        };
        etCount.setOnFocusChangeListener((v, hasFocus) -> {
            EditText mV = (EditText) v;
            if (hasFocus) {
                mV.addTextChangedListener(watcher);
            } else {
                mV.removeTextChangedListener(watcher);
            }
        });
        tvNo.setText(mDataList.get(position).getBatchUnique());
        tvDateStart.setText(TextUtils.isEmpty(mDataList.get(position).getGoodsProd()) ? "-" : mDataList.get(position).getGoodsProd());
        tvDateEnd.setText(TextUtils.isEmpty(mDataList.get(position).getGoodsExp()) ? "-" : mDataList.get(position).getGoodsExp());
        tvPrice.setText(DFUtils.getNum2(mDataList.get(position).getGoodsInPrice()));
        tvCount.setText(DFUtils.getNum4(mDataList.get(position).getGoodsCount()));
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onCount(double count, int position);
    }
}
