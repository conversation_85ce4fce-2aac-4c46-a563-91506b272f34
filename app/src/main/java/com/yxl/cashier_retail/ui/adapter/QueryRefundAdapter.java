package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.QueryRefundData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.utils.DateUtils;

import java.util.List;

/**
 * Describe: 查询-查退款（适配器）
 * Created by jingang on 2025/2/24
 */
public class QueryRefundAdapter extends BaseAdapter<QueryRefundData.ListBean> {
    public QueryRefundAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_query_refund;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvTime, tvName, tvNo, tvType, tvTotal, tvCount, tvMobile;
        tvTime = holder.getView(R.id.tvItemTime);
        tvName = holder.getView(R.id.tvItemName);
        tvNo = holder.getView(R.id.tvItemNo);
        tvType = holder.getView(R.id.tvItemType);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvCount = holder.getView(R.id.tvItemCount);
        tvMobile = holder.getView(R.id.tvItemMobile);

        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvTime.setTextColor(mContext.getResources().getColor(R.color.white));
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
            tvNo.setTextColor(mContext.getResources().getColor(R.color.white));
            tvType.setTextColor(mContext.getResources().getColor(R.color.white));
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.white));
            tvCount.setTextColor(mContext.getResources().getColor(R.color.white));
            tvMobile.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            tvTime.setTextColor(mContext.getResources().getColor(R.color.black));
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
            tvNo.setTextColor(mContext.getResources().getColor(R.color.black));
            tvType.setTextColor(mContext.getResources().getColor(R.color.black));
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.black));
            tvCount.setTextColor(mContext.getResources().getColor(R.color.black));
            tvMobile.setTextColor(mContext.getResources().getColor(R.color.black));
            if (position % 2 == 0) {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
            } else {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
            }
        }
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvTime, tvName, tvNo, tvType, tvTotal, tvCount, tvMobile;
        tvTime = holder.getView(R.id.tvItemTime);
        tvName = holder.getView(R.id.tvItemName);
        tvNo = holder.getView(R.id.tvItemNo);
        tvType = holder.getView(R.id.tvItemType);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvCount = holder.getView(R.id.tvItemCount);
        tvMobile = holder.getView(R.id.tvItemMobile);

        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvTime.setTextColor(mContext.getResources().getColor(R.color.white));
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
            tvNo.setTextColor(mContext.getResources().getColor(R.color.white));
            tvType.setTextColor(mContext.getResources().getColor(R.color.white));
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.white));
            tvCount.setTextColor(mContext.getResources().getColor(R.color.white));
            tvMobile.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            tvTime.setTextColor(mContext.getResources().getColor(R.color.black));
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
            tvNo.setTextColor(mContext.getResources().getColor(R.color.black));
            tvType.setTextColor(mContext.getResources().getColor(R.color.black));
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.black));
            tvCount.setTextColor(mContext.getResources().getColor(R.color.black));
            tvMobile.setTextColor(mContext.getResources().getColor(R.color.black));
            if (position % 2 == 0) {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
            } else {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
            }
        }

        tvTime.setText(DateUtils.getDateToString(mDataList.get(position).getReturnTime(), DateUtils.PATTERN_SECOND));
        tvName.setText(TextUtils.isEmpty(mDataList.get(position).getStaffName()) ? "-" : mDataList.get(position).getStaffName());
        tvNo.setText(mDataList.get(position).getReturnSaleListUnique());
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getReturnAmount()));
        tvCount.setText(DFUtils.getNum4(mDataList.get(position).getReturnSum()));
        tvMobile.setText(TextUtils.isEmpty(mDataList.get(position).getPhone()) ? "-" : mDataList.get(position).getPhone());
        //退款状态 1、现金；2、支付宝；3、微信；4、银行卡；5、储值卡  6 易通（原路退回）
        if (TextUtils.isEmpty(mDataList.get(position).getReturnMethod())) {
            tvType.setText("");
        } else {
            switch (mDataList.get(position).getReturnMethod()) {
                case "1":
                    tvType.setText(getRstr(R.string.cash));
                    break;
                case "2":
                    tvType.setText(getRstr(R.string.alipay));
                    break;
                case "3":
                    tvType.setText(getRstr(R.string.wechat));
                    break;
                case "4":
                    tvType.setText(getRstr(R.string.bank_card));
                    break;
                case "5":
                    tvType.setText(getRstr(R.string.member_type1));
                    break;
                case "6":
                    tvType.setText(getRstr(R.string.jinquan));
                    break;
                default:
                    tvType.setText("");
                    break;
            }
        }
    }
}
