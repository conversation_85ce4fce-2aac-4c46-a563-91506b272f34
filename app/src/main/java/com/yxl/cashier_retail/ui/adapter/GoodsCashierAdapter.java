package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.utils.DFUtils;

import java.util.List;

/**
 * Describe:商品列表-收银页（适配器）
 * Created by jingang on 2024/12/30
 */
public class GoodsCashierAdapter extends BaseAdapter<GoodsData> {
    //是否展示库存
    private boolean isShowStock;

    public void setShowStock(boolean showStock) {
        isShowStock = showStock;
    }

    public GoodsCashierAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_cashier;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        TextView tvCount = holder.getView(R.id.tvItemCount);
        //购物车数量
        if (mDataList.get(position).getCartNum() > 0) {
            tvCount.setVisibility(View.VISIBLE);
            tvCount.setText(DFUtils.getNum4(mDataList.get(position).getCartNum()));
        } else {
            tvCount.setVisibility(View.GONE);
        }
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivScale = holder.getView(R.id.ivItemScale);
        TextView tvCount, tvName, tvBarcode, tvShofar, tvPrice, tvUnit, tvStock, tvHouse, tvNoBarcodeWeight, tvNoBarcode;
        tvCount = holder.getView(R.id.tvItemCount);
        LinearLayout linGoods = holder.getView(R.id.linItemGoods);
        tvName = holder.getView(R.id.tvItemName);
        tvBarcode = holder.getView(R.id.tvItemBarcode);
        tvShofar = holder.getView(R.id.tvItemShofar);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvUnit = holder.getView(R.id.tvItemUnit);
        tvStock = holder.getView(R.id.tvItemStock);
        tvHouse = holder.getView(R.id.tvItemHouse);
        tvNoBarcodeWeight = holder.getView(R.id.tvItemNoBarcodeWeight);
        tvNoBarcode = holder.getView(R.id.tvItemNoBarcode);

        //0.有码商品 1.无码称重 2.无码商品
        int type;
        String barcode = mDataList.get(position).getGoods_barcode();
        if (TextUtils.isEmpty(barcode)) {
            type = 0;
        } else {
            switch (barcode) {
                case "999999999":
                    type = 1;
                    break;
                case "999999998":
                    type = 2;
                    break;
                default:
                    type = 0;
                    break;
            }
        }

        //购物车数量
        if (mDataList.get(position).getCartNum() > 0) {
            tvCount.setVisibility(View.VISIBLE);
            tvCount.setText(DFUtils.getNum4(mDataList.get(position).getCartNum()));
        } else {
            tvCount.setVisibility(View.GONE);
        }
        //称重类型 0.标品 1.称重
        if (mDataList.get(position).getGoodsChengType() == 1) {
            ivScale.setVisibility(View.VISIBLE);
            tvShofar.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.orange));
            tvPrice.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.orange));
            tvStock.setBackgroundResource(R.drawable.shape_orange_tm_left_2);
            tvHouse.setBackgroundResource(R.drawable.shape_orange_right_2);
        } else {
            ivScale.setVisibility(View.GONE);
            tvShofar.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvPrice.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvStock.setBackgroundResource(R.drawable.shape_green_tm_left_2);
            tvHouse.setBackgroundResource(R.drawable.shape_green_right_2);
        }
        //是否展示库存
        if (isShowStock) {
            tvStock.setVisibility(View.VISIBLE);
            tvHouse.setVisibility(View.VISIBLE);
        } else {
            tvStock.setVisibility(View.GONE);
            tvHouse.setVisibility(View.GONE);
        }

        switch (type) {
            case 1:
                //无码称重
                linGoods.setVisibility(View.GONE);
                tvNoBarcodeWeight.setVisibility(View.VISIBLE);
                tvNoBarcode.setVisibility(View.GONE);
                break;
            case 2:
                //无码商品
                linGoods.setVisibility(View.GONE);
                tvNoBarcodeWeight.setVisibility(View.GONE);
                tvNoBarcode.setVisibility(View.VISIBLE);
                break;
            default:
                linGoods.setVisibility(View.VISIBLE);
                tvNoBarcodeWeight.setVisibility(View.GONE);
                tvNoBarcode.setVisibility(View.GONE);
                tvName.setText(mDataList.get(position).getGoods_name());
                tvBarcode.setText(mDataList.get(position).getGoods_barcode());
                tvPrice.setText(DFUtils.getNum2(mDataList.get(position).getGoods_sale_price()));
                tvUnit.setText(TextUtils.isEmpty(mDataList.get(position).getGoods_unit()) ? "" : "/" + mDataList.get(position).getGoods_unit());
                tvStock.setText(DFUtils.getNum4(mDataList.get(position).getGoods_count()));
                break;
        }

    }
}
