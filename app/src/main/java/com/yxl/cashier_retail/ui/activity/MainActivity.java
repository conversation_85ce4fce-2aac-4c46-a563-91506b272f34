package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.hardware.display.DisplayManager;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.Settings;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.util.TypedValue;
import android.view.Display;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.allenliu.versionchecklib.core.http.HttpParams;
import com.allenliu.versionchecklib.v2.AllenVersionChecker;
import com.allenliu.versionchecklib.v2.builder.DownloadBuilder;
import com.allenliu.versionchecklib.v2.builder.NotificationBuilder;
import com.allenliu.versionchecklib.v2.builder.UIData;
import com.allenliu.versionchecklib.v2.callback.CustomDownloadFailedListener;
import com.allenliu.versionchecklib.v2.callback.CustomDownloadingDialogListener;
import com.allenliu.versionchecklib.v2.callback.CustomVersionDialogListener;
import com.allenliu.versionchecklib.v2.callback.RequestVersionListener;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.SPUtils;
import com.google.gson.Gson;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.MyApplication;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.ActivityMainBinding;
import com.yxl.cashier_retail.databinding.DialogDownloadFailBinding;
import com.yxl.cashier_retail.databinding.DialogDownloadingBinding;
import com.yxl.cashier_retail.databinding.DialogVersionBinding;
import com.yxl.cashier_retail.ui.adapter.CartAdapter;
import com.yxl.cashier_retail.ui.adapter.CatePcAdapter;
import com.yxl.cashier_retail.ui.adapter.GoodsCashierAdapter;
import com.yxl.cashier_retail.ui.adapter.PaymentAdapter;
import com.yxl.cashier_retail.ui.bean.CashierPayData;
import com.yxl.cashier_retail.ui.bean.CashierStatusData;
import com.yxl.cashier_retail.ui.bean.CatePcData;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.ui.bean.HangData;
import com.yxl.cashier_retail.ui.bean.MemberData;
import com.yxl.cashier_retail.ui.bean.OrderOfflineData;
import com.yxl.cashier_retail.ui.bean.PaymentData;
import com.yxl.cashier_retail.ui.bean.SaleListUniqueData;
import com.yxl.cashier_retail.ui.bean.VersionData;
import com.yxl.cashier_retail.ui.dialog.AccountDialog;
import com.yxl.cashier_retail.ui.dialog.DiscountDialog;
import com.yxl.cashier_retail.ui.dialog.CartEditDialog;
import com.yxl.cashier_retail.ui.dialog.GoodsAddQuickDialog;
import com.yxl.cashier_retail.ui.dialog.GoodsNoBarcodeDialog;
import com.yxl.cashier_retail.ui.dialog.MemberDialog;
import com.yxl.cashier_retail.ui.dialog.MenuDialog;
import com.yxl.cashier_retail.ui.dialog.PaymentCashDialog;
import com.yxl.cashier_retail.ui.dialog.PaymentCombinationDialog;
import com.yxl.cashier_retail.ui.dialog.PaymentDoingDialog;
import com.yxl.cashier_retail.ui.dialog.PaymentFaceDialog;
import com.yxl.cashier_retail.ui.dialog.PaymentFailDialog;
import com.yxl.cashier_retail.ui.dialog.PaymentMemberDialog;
import com.yxl.cashier_retail.ui.dialog.PaymentSuccessDialog;
import com.yxl.cashier_retail.utils.ByteUtils;
import com.yxl.cashier_retail.utils.CameraUtils;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.utils.DateUtils;
import com.yxl.cashier_retail.utils.PermissionUtils;
import com.yxl.cashier_retail.utils.PrintReceiptUsbUtils;
import com.yxl.cashier_retail.utils.SystemTTS;
import com.yxl.cashier_retail.utils.ZxingUtils;
import com.yxl.cashier_retail.view.DifferentDisplay;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.base.BaseData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.EventBusManager;
import com.yxl.commonlibrary.utils.PackageUtils;

import org.greenrobot.eventbus.Subscribe;
import org.litepal.LitePal;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 主函数（收银）
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n", "MissingPermission"})
public class MainActivity extends BaseActivity<ActivityMainBinding> implements View.OnClickListener {
    private String cateUnique, keyWords,
            scanCode,//扫码结果
            saleListUnique;//订单编号（接口创建）
    private double total,//合计
            discount,//优惠
            totalReal,//应收
            memberMoney,//储值卡支付金额（会员余额）
            weight,//称重
            saveMoney,//现金收款后存零金额
            salePoints,//本单积分
            cusPoints;//剩余积分
    private int posCate,//分类列表下标
            paymentType;//
    private boolean isEnter,//是否点击软键盘回车键
            isDifferentPlayUpdate;//是否更新副屏购物车：收款成功时单独更新
    private JSONArray arrayPayment;//支付详情json串

    private MemberData memberData;//会员信息

    //购物车
    private CartAdapter cartAdapter;
    //    private List<GoodsData> cartList = new ArrayList<>();
    private List<GoodsData> cartList = new ArrayList<>();

    //分类列表
    private CatePcAdapter cateAdapter;
    private List<CatePcData> cateList = new ArrayList<>();

    //商品列表
    private GoodsCashierAdapter mAdapter;
    //    private List<GoodsData> dataList = new ArrayList<>();
    private List<GoodsData> dataList = new ArrayList<>();

    //支付方式
    private PaymentAdapter paymentAdapter;
    private final List<PaymentData> paymentList = new ArrayList<>();

    //语音播报
    private SystemTTS mSystemTTS;

    @Override
    protected ActivityMainBinding getViewBinding() {
        return ActivityMainBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.vLeft.ivLogo.setOnClickListener(this);
        mBinding.vLeft.ivSearchClear.setOnClickListener(this);
        mBinding.vLeft.tvMemberSelect.setOnClickListener(this);
        mBinding.vLeft.linMember.setOnClickListener(this);
        mBinding.vLeft.linWeight.setOnClickListener(this);
        mBinding.vLeft.linHangOrder.setOnClickListener(this);
        mBinding.vLeft.linPreviousOrder.setOnClickListener(this);
        mBinding.vLeft.ivVerify.setOnClickListener(this);
        mBinding.vLeft.ivPrint.setOnClickListener(this);
        mBinding.vLeft.tvDiscount.setOnClickListener(this);
        mBinding.vLeft.linClear.setOnClickListener(this);

        mBinding.vRight.linAccount.setOnClickListener(this);
        mBinding.vRight.flStatistics.setOnClickListener(this);
        mBinding.vRight.flReplenishment.setOnClickListener(this);
        mBinding.vRight.flOrder.setOnClickListener(this);
        mBinding.vRight.flVersion.setOnClickListener(this);

        mBinding.vLeft.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                mBinding.vLeft.ivSearchClear.setVisibility(TextUtils.isEmpty(keyWords) ? View.GONE : View.VISIBLE);
                if (isEnter) {
                    isEnter = false;
                    if (posCate != 0) {
                        posCate = 0;
                    }
                } else {
                    if (posCate != (cateList.size() - 1)) {
                        posCate = cateList.size() - 1;
                    }
                }
                for (int i = 0; i < cateList.size(); i++) {
                    if (cateList.get(i).isCheck()) {
                        cateList.get(i).setCheck(false);
                        cateAdapter.notifyItemChanged(i);
                    }
                }
                cateList.get(posCate).setCheck(true);
                cateAdapter.notifyItemChanged(posCate);
                cateUnique = cateList.get(posCate).getGoods_kind_unique();
                max = 0;
                getGoodsList();
            }
        });
        mBinding.vLeft.etSearch.setOnEditorActionListener((v, actionId, event) -> {
            hideSoftInput(this);
            double price = DFUtils.isNumeric(keyWords);
            if (price > 0 && price < 10000 && isHomeInputDigitCreateGoodsNoBarcode()) {
                //首页输入数字创建无码商品
                for (int i = 0; i < dataList.size(); i++) {
                    if (!TextUtils.isEmpty(dataList.get(i).getGoods_barcode())) {
                        if (dataList.get(i).getGoods_barcode().equals("999999998")) {
                            dataList.get(i).setCartNum(dataList.get(i).getCartNum() + 1);
                            mAdapter.notifyItemChanged(i, dataList.get(i));
                        }
                    }
                }
//                cartList.add(0, new GoodsData("999999998",
//                        "0",
//                        "",
//                        getRstr(R.string.no_barcode_goods),
//                        price,
//                        1)
//                );
                cartList.add(0, new GoodsData("999999998",
                        "0",
                        "",
                        getRstr(R.string.no_barcode_goods),
                        price,
                        1)
                );
                cartAdapter.setDataList(cartList);
                isDifferentPlayUpdate = true;
                getTotal();
                isEnter = true;
                mBinding.vLeft.etSearch.setText("");
            }
            return true;
        });
        mBinding.vLeft.etSearch.requestFocus();
        //获取焦点不弹出软键盘
        mBinding.vLeft.etSearch.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                InputMethodManager imm = (InputMethodManager) v.getContext().getSystemService(INPUT_METHOD_SERVICE);
                if (imm != null) {
                    imm.hideSoftInputFromWindow(v.getWindowToken(), 0);
                }
            }
        });
        mBinding.vLeft.etSearch.setScanResultListener(this::setScanResult);

        checkPermissionDifferentDisplay();
        checkPermissionMqtt();
        checkPermissionSerialPort();
        if (!MyApplication.getInstance().isReceiptPrinterConnect) {
            MyApplication.getInstance().connectReceiptPrinter();
        }
        setAdapter();
    }

    @Override
    protected void initData() {
        mSystemTTS = SystemTTS.getInstance(this);
        if (getLoginData() != null) {
            if (getLoginData().getStaff_position() == 3) {
                mBinding.vRight.tvName.setText(getRstr(R.string.shopowner) + "-" + getLoginData().getStaffName());
            } else {
                mBinding.vRight.tvName.setText(getRstr(R.string.cashier_staff) + "-" + getLoginData().getStaffName());
            }
        }
        setNetwork();
        mBinding.vLeft.ivVerify.setSelected(isVerify());//条码校验
        mBinding.vLeft.ivPrint.setSelected(isPrint());//打印小票
        getCateList();
    }

    @Override
    public void getNetwork() {
        setNetwork();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivLogo:
                //菜单
                MenuDialog.showDialog(this, type -> {
                    //0.收银 1.入库 2.查询 3.统计 4.网单 5.会员 6.补货 7.营销 8.交班 9.设置
                    switch (type) {
                        case 1:
                            goToActivity(InActivity.class);
                            break;
                        case 2:
                            goToActivity(QueryActivity.class);
                            break;
                        case 3:
                            goToActivity(StatisticsActivity.class);
                            break;
                        case 4:
                            goToActivity(OrderActivity.class);
                            break;
                        case 5:
                            goToActivity(MemberActivity.class);
                            break;
                        case 6:
                            goToActivity(MallActivity.class);
                            break;
                        case 7:
                            goToActivity(MarketingActivity.class);
                            break;
                        case 8:
                            goToActivity(ShiftActivity.class);
                            break;
                        case 9:
                            goToActivity(SettingActivity.class);
                            break;
                    }
                });
                break;
            case R.id.ivSearchClear:
                //清除搜索输入
                mBinding.vLeft.etSearch.setText("");
                break;
            case R.id.tvMemberSelect:
                //选择会员
                if (isQuicklyClick()) {
                    return;
                }
                showDialogMember(0);
                break;
            case R.id.linMember:
                //取消选择会员
                if (isQuicklyClick()) {
                    return;
                }
                memberData = null;
                isDifferentPlayUpdate = true;
                setUIMember();
                break;
            case R.id.linWeight:
                //重连串口
                if (isQuicklyClick()) {
                    return;
                }
                if (!MyApplication.getInstance().isSerialPortConnect) {
                    checkPermissionSerialPort();
                }
                break;
            case R.id.linHangOrder:
                //挂单
                setHang();
                break;
            case R.id.linPreviousOrder:
                //上一单
                goToActivity(QueryActivity.class);
                break;
            case R.id.ivVerify:
                //条码检验
                if (isVerify()) {
                    mBinding.vLeft.ivVerify.setSelected(false);
                    SPUtils.getInstance().put(Constants.IS_VERIFY, "");
                } else {
                    mBinding.vLeft.ivVerify.setSelected(true);
                    SPUtils.getInstance().put(Constants.IS_VERIFY, Constants.IS_VERIFY);
                }
                break;
            case R.id.ivPrint:
                //打印小票
                if (isPrint()) {
                    mBinding.vLeft.ivPrint.setSelected(false);
                    SPUtils.getInstance().put(Constants.IS_PRINT, "");
                } else {
                    mBinding.vLeft.ivPrint.setSelected(true);
                    SPUtils.getInstance().put(Constants.IS_PRINT, Constants.IS_PRINT);
                }
                break;
            case R.id.tvDiscount:
                //整单折扣
                if (cartList.size() < 1) {
                    showToast(1, getRstr(R.string.select_goods));
                    return;
                }
                showDialogDiscount();
                break;
            case R.id.linClear:
                //清空商品
                clearGoods(0, -1, "");
                break;
            case R.id.linAccount:
                //当前登录账号
                AccountDialog.showDialog(this, new AccountDialog.MyListener() {
                    @Override
                    public void onPwdClick() {
                        //修改密码
                        goToActivity(ForgetActivity.class);
                    }

                    @Override
                    public void onShiftClick() {
                        //交班
                        postLoginOut();
                    }
                });
                break;
            case R.id.flStatistics:
                //统计
                goToActivity(StatisticsActivity.class);
                break;
            case R.id.flReplenishment:
                //补货
                goToActivity(MallActivity.class);
                break;
            case R.id.flOrder:
                //网单
                goToActivity(OrderActivity.class);
                break;
            case R.id.flVersion:
                //版本更新
                checkUpgrade();
                break;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        initPaymentData();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.SERIALPORT:
                //串口数据
                runOnUiThread(() -> {
                    if (MyApplication.getInstance().isSerialPortConnect) {
                        mBinding.vLeft.tvSerialPortOff.setVisibility(View.GONE);
                        mBinding.vLeft.tvWeight.setVisibility(View.VISIBLE);
                        mBinding.vLeft.tvUnit.setVisibility(View.VISIBLE);
                        weight = ByteUtils.getSerialPortScaleData(event.getBytes());
                    } else {
                        mBinding.vLeft.tvSerialPortOff.setVisibility(View.VISIBLE);
                        mBinding.vLeft.tvWeight.setVisibility(View.GONE);
                        mBinding.vLeft.tvUnit.setVisibility(View.GONE);
                        weight = 0;
                    }
                    mBinding.vLeft.tvWeight.setText(DFUtils.getNum2(weight));
                    if (GoodsNoBarcodeDialog.getDialog() != null) {
                        GoodsNoBarcodeDialog.getDialog().setWeight(weight);
                    }
                });
                break;
            case Constants.CATE_LIST:
                //商品分类
                max = 0;
                getCateList();
                break;
            case Constants.GOODS_LIST:
                //商品列表
                max = 0;
                getGoodsList();
                break;
            case Constants.IS_USE_SECONDARY_SCREEN:
                //启用副屏
                setCartListDifferentDisplay2(-1, "");
                break;
        }
    }

    /**
     * 网络监听
     */
    private void setNetwork() {
        if (MyApplication.mNetwork != null) {
            if (MyApplication.mNetwork.isConnect()) {
                switch (MyApplication.mNetwork.getConnectType()) {
                    case 1:
                        mBinding.vRight.ivNetwork.setImageResource(R.mipmap.ic_network001);
                        mBinding.vRight.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 2:
                        mBinding.vRight.ivNetwork.setImageResource(R.mipmap.ic_network002);
                        mBinding.vRight.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 3:
                        mBinding.vRight.ivNetwork.setImageResource(R.mipmap.ic_network003);
                        mBinding.vRight.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    default:
                        mBinding.vRight.ivNetwork.setImageResource(R.mipmap.ic_network004);
                        mBinding.vRight.tvNetwork.setText(getRstr(R.string.no_network));
                        break;
                }
            } else {
                mBinding.vRight.ivNetwork.setImageResource(R.mipmap.ic_network004);
                mBinding.vRight.tvNetwork.setText(getRstr(R.string.no_network));
            }
        } else {
            mBinding.vRight.ivNetwork.setImageResource(R.mipmap.ic_network004);
            mBinding.vRight.tvNetwork.setText(getRstr(R.string.no_network));
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //购物车
        cartAdapter = new CartAdapter(this);
        mBinding.vLeft.rvCart.setAdapter(cartAdapter);
        cartAdapter.setListener(new CartAdapter.MyListener() {
            @Override
            public void onPriceClick(View view, int position) {
                //修改单价
                if (!cartList.get(position).isEditPrice()) {
                    for (int i = 0; i < cartList.size(); i++) {
                        if (cartList.get(i).isEditPrice()) {
                            cartList.get(i).setEditPrice(false);
                            cartAdapter.notifyItemChanged(i);
                        }
                        if (cartList.get(i).isEditCount()) {
                            cartList.get(i).setEditCount(false);
                            cartAdapter.notifyItemChanged(i);
                        }
                        if (cartList.get(i).isEditTotal()) {
                            cartList.get(i).setEditTotal(false);
                            cartAdapter.notifyItemChanged(i);
                        }
                    }
                    cartList.get(position).setEditPrice(true);
                    cartAdapter.notifyItemChanged(position);
                }
                double price;
                if (cartList.get(position).getNewPrice() > 0) {
                    price = cartList.get(position).getNewPrice();
                } else {
                    if (memberData == null) {
                        price = cartList.get(position).getGoods_sale_price();
                    } else {
                        price = TextUtils.isEmpty(cartList.get(position).getGoods_cus_price()) ?
                                0 : Double.parseDouble(cartList.get(position).getGoods_cus_price());
                    }
                }
                showDialogCartEdit(0, price, cartList.get(position).getGoodsChengType(), position);
            }

            @Override
            public void onCountClick(View view, int position) {
                //修改数量
                if (!cartList.get(position).isEditCount()) {
                    for (int i = 0; i < cartList.size(); i++) {
                        if (cartList.get(i).isEditPrice()) {
                            cartList.get(i).setEditPrice(false);
                            cartAdapter.notifyItemChanged(i);
                        }
                        if (cartList.get(i).isEditCount()) {
                            cartList.get(i).setEditCount(false);
                            cartAdapter.notifyItemChanged(i);
                        }
                        if (cartList.get(i).isEditTotal()) {
                            cartList.get(i).setEditTotal(false);
                            cartAdapter.notifyItemChanged(i);
                        }
                    }
                    cartList.get(position).setEditCount(true);
                    cartAdapter.notifyItemChanged(position);
                }
                showDialogCartEdit(1, cartList.get(position).getCartNum(), cartList.get(position).getGoodsChengType(), position);
            }

            @Override
            public void onTotalClick(View view, int position) {
                //修改总价
                if (!cartList.get(position).isEditTotal()) {
                    for (int i = 0; i < cartList.size(); i++) {
                        if (cartList.get(i).isEditPrice()) {
                            cartList.get(i).setEditPrice(false);
                            cartAdapter.notifyItemChanged(i);
                        }
                        if (cartList.get(i).isEditCount()) {
                            cartList.get(i).setEditCount(false);
                            cartAdapter.notifyItemChanged(i);
                        }
                        if (cartList.get(i).isEditTotal()) {
                            cartList.get(i).setEditTotal(false);
                            cartAdapter.notifyItemChanged(i);
                        }
                    }
                    cartList.get(position).setEditTotal(true);
                    cartAdapter.notifyItemChanged(position);
                }
                double total;
                if (cartList.get(position).getNewPrice() > 0) {
                    total = cartList.get(position).getNewPrice() * cartList.get(position).getCartNum();
                } else {
                    if (memberData == null) {
                        total = cartList.get(position).getGoods_sale_price() * cartList.get(position).getCartNum();
                    } else {
                        double memberPrice = TextUtils.isEmpty(cartList.get(position).getGoods_cus_price()) ?
                                0 : Double.parseDouble(cartList.get(position).getGoods_cus_price());
                        total = memberPrice * cartList.get(position).getCartNum();
                    }
                }
                showDialogCartEdit(2, total, cartList.get(position).getGoodsChengType(), position);
            }

            @Override
            public void onDelClick(View view, int position) {
                //删除
                if (isQuicklyClick()) {
                    return;
                }
                double count = cartList.get(position).getCartNum();
                String barcode = cartList.get(position).getGoods_barcode();
                for (int i = 0; i < dataList.size(); i++) {
                    if (!TextUtils.isEmpty(barcode) && !TextUtils.isEmpty(dataList.get(i).getGoods_barcode())) {
                        if (barcode.equals(dataList.get(i).getGoods_barcode())) {
                            dataList.get(i).setCartNum(dataList.get(i).getCartNum() - count);
                            dataList.get(i).setNewPrice(0);
                            dataList.get(i).setEditCount(false);
                            dataList.get(i).setEditPrice(false);
                            dataList.get(i).setEditTotal(false);
                            mAdapter.notifyItemChanged(i, dataList.get(i));
                        }
                    }
                }
                cartList.remove(position);
                cartAdapter.remove(position);
                isDifferentPlayUpdate = true;
                getTotal();
            }
        });

        //分类列表
        cateAdapter = new CatePcAdapter(this);
        mBinding.vRight.rvCate.setAdapter(cateAdapter);
        cateAdapter.setOnItemClickListener((view, position) -> {
            int size = cateList.size();
            if (!cateList.get(position).isCheck()) {
                for (int i = 0; i < size; i++) {
                    if (cateList.get(i).isCheck()) {
                        cateList.get(i).setCheck(false);
                        cateAdapter.notifyItemChanged(i);
                    }
                }
                cateList.get(position).setCheck(true);
                cateAdapter.notifyItemChanged(position);
                posCate = position;
                cateUnique = cateList.get(position).getGoods_kind_unique();
                max = 0;
                getGoodsList();
            }
        });

        //商品列表
        mAdapter = new GoodsCashierAdapter(this);
        mBinding.vRight.recyclerView.setAdapter(mAdapter);
        mAdapter.setShowStock(TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_SHOW_STOCK, "")));
        mAdapter.setOnItemClickListener((view, position) -> {
            //0.有码商品 1.无码称重 2.无码商品
            int type;
            String barcode = dataList.get(position).getGoods_barcode();
            if (TextUtils.isEmpty(barcode)) {
                type = 0;
            } else {
                switch (barcode) {
                    case "999999999":
                        type = 1;
                        break;
                    case "999999998":
                        type = 2;
                        break;
                    default:
                        type = 0;
                        break;
                }
            }
            //0.有码商品 1.无码称重 2.无码商品
            switch (type) {
                case 1:
                case 2:
                    showDialogNoBarcode(type, position);
                    break;
                default:
                    if (dataList.get(position).getGoodsChengType() == 1) {
                        //称重
                        dataList.get(position).setCartNum(weight > 0 ? weight : 1);
                    } else {
                        //标品
                        dataList.get(position).setCartNum(dataList.get(position).getCartNum() + 1);
                    }
//                    dataList.get(position).setCartNum(dataList.get(position).getGoodsChengType() == 1 ? 1 : dataList.get(position).getCartNum() + 1);
                    mAdapter.notifyItemChanged(position, dataList.get(position));
                    int pos = isAdd(dataList.get(position).getGoods_barcode());//是否添加过 -1.未添加
                    /*更新购物车start*/
                    if (pos == -1) {
                        cartList.add(0, dataList.get(position));
                    } else {
                        cartList.set(pos, dataList.get(position));
                    }
                    cartAdapter.setDataList(cartList);
                    /*更新购物车end*/
                    isDifferentPlayUpdate = true;
                    getTotal();
                    break;
            }
        });
        mBinding.vRight.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getGoodsList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                max = 0;
                getGoodsList();
            }
        });

        //收款方式
        paymentList.clear();
        for (int i = 0; i < 7; i++) {
            paymentList.add(new PaymentData(i, true));
        }
        paymentAdapter = new PaymentAdapter(this);
        mBinding.vRight.rvPayment.setAdapter(paymentAdapter);
        paymentAdapter.setOnItemClickListener((view, position) -> {
            if (isQuicklyClick()) {
                return;
            }
            //1.现金 2.支付宝 3.微信 4.银行卡 5.人脸 6.储值卡 7.组合 8.商品分类 15.POS机
            if (paymentList.get(position).getPaymentId() == 8) {
                goToActivity(GoodsActivity.class);
                return;
            }
            if (cartList.size() < 1) {
                showToast(1, getRstr(R.string.select_goods));
                return;
            }
            switch (paymentList.get(position).getPaymentId()) {
                case 1:
                    //支付前弹钱箱
                    PrintReceiptUsbUtils.openCashBox(isUseBeforePayBox());
                    PaymentCashDialog.showDialog(this, memberData, totalReal, total, new PaymentCashDialog.MyListener() {

                        @Override
                        public void onCashClick(double money, double save) {
                            //现金收款  saveMoney(存零金额大于0时，会员充值)
                            saveMoney = save;
                            totalReal = money;
                            memberMoney = 0;
                            getSaleListUnique(1);
                        }

                        @Override
                        public void onCombinationClick(double total, double cashMoney, double wechatMoney, double alipayMoney, double bankMoney, double memberMoneys) {
                            //组合收款
                            memberMoney = memberMoneys;
                            if (total == cashMoney) {
                                //现金收款
                                getSaleListUnique(1);
                                return;
                            }
                            if (total == alipayMoney) {
                                //支付宝收款
                                getSaleListUnique(2);
                                return;
                            }
                            if (total == wechatMoney) {
                                //微信收款
                                getSaleListUnique(3);
                                return;
                            }
                            if (total == bankMoney) {
                                //银行卡收款
                                getSaleListUnique(4);
                            }
                            if (total == memberMoney) {
                                //储值卡收款
                                getSaleListUnique(5);
                                return;
                            }
                            //组合收款
                            arrayPayment = new JSONArray();
                            if (cashMoney > 0) {
                                JSONObject object = new JSONObject();
                                object.put("pay_method", 1);
                                object.put("pay_money", cashMoney);
                                arrayPayment.add(object);
                            }
                            if (wechatMoney > 0) {
                                JSONObject object = new JSONObject();
                                object.put("pay_method", 3);
                                object.put("pay_money", wechatMoney);
                                arrayPayment.add(object);
                            }
                            if (alipayMoney > 0) {
                                JSONObject object = new JSONObject();
                                object.put("pay_method", 2);
                                object.put("pay_money", alipayMoney);
                                arrayPayment.add(object);
                            }
                            if (bankMoney > 0) {
                                JSONObject object = new JSONObject();
                                object.put("pay_method", 4);
                                object.put("pay_money", bankMoney);
                                arrayPayment.add(object);
                            }
                            if (memberMoney > 0) {
                                JSONObject object = new JSONObject();
                                object.put("pay_method", 5);
                                object.put("pay_money", memberMoney);
                                arrayPayment.add(object);
                            }
                            getSaleListUnique(8);
                        }
                    });
                    break;
                case 2:
                    memberMoney = 0;
                    getSaleListUnique(2);
                    break;
                case 3:
                    memberMoney = 0;
                    getSaleListUnique(3);
                    break;
                case 4:
                    memberMoney = 0;
//                    if (isUsePos()) {
//                        if (TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.POS_ID, ""))) {
//                            showToast(1, getRstr(R.string.set_pos_id));
//                            return;
//                        }
//                        getSaleListUnique(15);
//                    } else {
//                        getSaleListUnique(4);
//                    }
                    getSaleListUnique(4);
                    break;
                case 5:
                    if (!NetworkUtils.isConnected()) {
                        showToast(1, getRstr(R.string.no_network_connect));
                        return;
                    }
                    if (PermissionUtils.checkPermissionsGroup(this, 2)) {
                        showDialogPaymentFace();
                    } else {
                        PermissionUtils.requestPermissions(this, Constants.PERMISSION_CAMERA, 2);
                    }
                    break;
                case 6:
                    if (!NetworkUtils.isConnected()) {
                        showToast(1, getRstr(R.string.no_network_connect));
                        return;
                    }
                    //支付前弹钱箱
                    PrintReceiptUsbUtils.openCashBox(isUseBeforePayBox());
                    if (memberData == null) {
                        showDialogMember(1);
                    } else {
                        showDialogPaymentMember();
                    }
                    break;
                case 7:
                    if (!NetworkUtils.isConnected()) {
                        showToast(1, getRstr(R.string.no_network_connect));
                        return;
                    }
                    //支付前弹钱箱
                    PrintReceiptUsbUtils.openCashBox(isUseBeforePayBox());
                    PaymentCombinationDialog.showDialog(this, memberData, totalReal, new PaymentCombinationDialog.MyListener() {
                        @Override
                        public void onPaymentJinq(double total, String saleListUnique) {
                            //金圈收款
                            memberMoney = 0;
                            scanCode = saleListUnique;
                            getSaleListUnique(13);
                        }

                        @Override
                        public void onPaymentCash(double total) {
                            //现金收款
                            memberMoney = 0;
                            getSaleListUnique(1);
                        }

                        @Override
                        public void onPaymentCombination(double total, double cashTotal, double jinqTotal) {
                            //组合收款
                            memberMoney = 0;
                            arrayPayment = new JSONArray();
                            if (jinqTotal > 0) {
                                JSONObject object = new JSONObject();
                                object.put("pay_method", 13);
                                object.put("pay_money", jinqTotal);
                                arrayPayment.add(object);
                            }
                            if (cashTotal > 0) {
                                JSONObject object = new JSONObject();
                                object.put("pay_method", 1);
                                object.put("pay_money", cashTotal);
                                arrayPayment.add(object);
                            }
                            getSaleListUnique(8);
                        }
                    });
                    break;
            }
        });
    }

    /**
     * 清空商品
     *
     * @param clearType 1.修改库存
     * @param status    收款状态 -1.无收款状态 0.收款成功 1.收款失败
     * @param statusMsg 收款失败原因
     */
    private void clearGoods(int clearType, int status, String statusMsg) {
        //修改库存
        if (clearType == 1) {
            for (int i = 0; i < cartList.size(); i++) {
//                GoodsData data = LitePal.where("goods_barcode = ?", cartList.get(i).getGoods_barcode()).findFirst(GoodsData.class);
//                if (data != null) {
//                    data.setGoods_count(data.getGoods_count() - cartList.get(i).getCartNum());
//                    data.save();
//                }
                GoodsData data = LitePal.where("goods_barcode = ?", cartList.get(i).getGoods_barcode()).findFirst(GoodsData.class);
                if (data != null) {
                    data.setGoods_count(data.getGoods_count() - cartList.get(i).getCartNum());
                    data.save();
                }
            }
            max = 0;
            getGoodsList();
        }

        //清空购物车
        cartList.clear();
        cartAdapter.clear();
        setCartListDifferentDisplay2(status, statusMsg);

        //清空计算
        total = 0;
        mBinding.vLeft.tvCount.setText("0");
        mBinding.vLeft.tvTotal.setText("0.00");
        mBinding.vLeft.tvDiscountMoney.setText("-0.00");
        mBinding.vLeft.tvTotalReal.setText("0.00");

        for (int i = 0; i < dataList.size(); i++) {
            //购物车数量归0
            if (dataList.get(i).getCartNum() > 0) {
                dataList.get(i).setCartNum(0);
                mAdapter.notifyItemChanged(i);
            }
            //编辑后单价归0
            if (dataList.get(i).getNewPrice() > 0) {
                dataList.get(i).setNewPrice(0);
                mAdapter.notifyItemChanged(i);
            }
            //选中item归为未选中
            if (dataList.get(i).isSelect()) {
                dataList.get(i).setSelect(false);
                mAdapter.notifyItemChanged(i);
            }
        }
    }

    /**
     * 小计
     */
    private void getTotal() {
        double num = 0;
        total = 0;
        discount = 0;
        for (int i = 0; i < cartList.size(); i++) {
            double price = cartList.get(i).getNewPrice(),
                    salePrice = cartList.get(i).getGoods_sale_price(),
                    memberPrice = TextUtils.isEmpty(cartList.get(i).getGoods_cus_price()) ? 0 : Double.parseDouble(cartList.get(i).getGoods_cus_price()),
                    cartNum = cartList.get(i).getCartNum();
            num = num + cartNum;
            if (price > 0) {
                //有改价
                total = total + price * cartNum;
                if (price < salePrice) {
                    //有折扣
                    discount = discount + (salePrice - price) * cartNum;
                }
            } else {
                if (memberData == null) {
                    total = total + cartNum * salePrice;
                } else {
                    total = total + cartNum * memberPrice;
                    discount = discount + (salePrice - memberPrice) * cartNum;
                }
            }
        }
        totalReal = total;
        mBinding.vLeft.tvCount.setText(DFUtils.getNum4(num));
        if (total > 9999.99) {
            mBinding.vLeft.tvTotal.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10);
        } else {
            mBinding.vLeft.tvTotal.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
        }
        mBinding.vLeft.tvTotal.setText(DFUtils.getNum2(total));
        mBinding.vLeft.tvDiscountMoney.setText("-" + DFUtils.getNum2(discount));
        if (totalReal > 9999.99) {
            mBinding.vLeft.tvTotalReal.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10);
        } else {
            mBinding.vLeft.tvTotalReal.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
        }
        mBinding.vLeft.tvTotalReal.setText(DFUtils.getNum2(totalReal));
        if (isDifferentPlayUpdate) {
            setCartListDifferentDisplay2(-1, "");
        }
    }

    /**
     * 设置副屏购物车
     */
    private void setCartListDifferentDisplay2(int status, String msg) {
        if (mPresentation != null) {
            mPresentation.setCart(cartList, discount, totalReal, memberData, status, msg);
        }
    }


    /**
     * 判断是否已添加
     *
     * @return -1.未添加 其他.已添加
     */
    private int isAdd(String barcode) {
        int pos = -1;
        if (TextUtils.isEmpty(barcode)) {
            return -1;
        }
        if (cartList.size() < 1) {
            return -1;
        }
        for (int i = 0; i < cartList.size(); i++) {
            if (cartList.get(i).getGoods_barcode().equals(barcode)) {
                if (cartList.get(i).getGoodsChengType() != 1) {
                    pos = i;
                }
            }
        }
        return pos;
    }

    /**
     * 得到扫码结果
     */
    private void setScanResult(String code) {
        Log.e(tag, "扫码结果 = " + code);
        scanCode = code;
        if (TextUtils.isEmpty(scanCode)) {
            return;
        }
        if (scanCode.length() < 2) {
            return;
        }
        String chooseCode = scanCode.substring(0, 2);
        if (scanCode.length() == 11) {
            //会员码
            if (cartList.size() < 1) {
                showToast(1, getRstr(R.string.select_goods));
                return;
            }
            if (!NetworkUtils.isConnected()) {
                showToast(1, getRstr(R.string.no_network_connect));
                return;
            }
            //1.查询会员信息，判断余额是否足够 2.储值卡收款
            getMemberList();
        } else {
            switch (chooseCode) {
                case "62":
                case "28":
                case "13":
                    if (scanCode.length() == 18
                            || ("62".equals(chooseCode) && scanCode.length() == 19)
                            || ("28".equals(chooseCode) && scanCode.length() == 24)) {
                        //支付码
                        if (cartList.size() < 1) {
                            showToast(1, getRstr(R.string.select_goods));
                            return;
                        }
                        if (!NetworkUtils.isConnected()) {
                            showToast(1, getRstr(R.string.no_network_connect));
                            return;
                        }
                        //扫码支付
                        getSaleListUnique(13);
                    }
                    break;
                default:
                    //商品码
//                    GoodsData data = LitePal
//                            .where("goods_name like ? or goods_barcode like ?", "%" + scanCode + "%", "%" + scanCode + "%")
//                            .findFirst(GoodsData.class);
                    GoodsData data = LitePal
                            .where("goods_name like ? or goods_barcode like ?", "%" + scanCode + "%", "%" + scanCode + "%")
                            .findFirst(GoodsData.class);
                    if (data == null) {
                        GoodsAddQuickDialog.showDialog(this, getRstr(R.string.find_new_goods_or_add), scanCode);
                        return;
                    }
                    //1.更新商品列表-购物车数量
                    for (int i = 0; i < dataList.size(); i++) {
                        if (!TextUtils.isEmpty(dataList.get(i).getGoods_barcode()) && !TextUtils.isEmpty(data.getGoods_barcode())) {
                            if (dataList.get(i).getGoods_barcode().equals(data.getGoods_barcode())) {
                                data.setCartNum(dataList.get(i).getCartNum() + 1);
                                dataList.get(i).setCartNum(dataList.get(i).getCartNum() + 1);
                                mAdapter.notifyItemChanged(i);
                            }
                        }
                    }
                    //2.添加到购物车
                    int pos = isAdd(data.getGoods_barcode());
                    if (pos == -1) {
                        data.setCartNum(1);
                        cartList.add(0, data);
                    } else {
                        data.setCartNum(cartList.get(pos).getCartNum() + 1);
                        cartList.set(pos, data);
                    }
                    cartAdapter.setDataList(cartList);
                    isDifferentPlayUpdate = true;
                    getTotal();
                    break;
            }
        }
    }

    /**
     * 挂单操作
     */
    private void setHang() {
        if (cartList.size() > 0) {
            String memberName, memberStr;
            double total = 0;
            int type;
            if (memberData != null) {
                memberName = memberData.getCusName();
                memberStr = new Gson().toJson(memberData);
                type = 2;
            } else {
                memberName = "";
                memberStr = "";
                type = 1;
            }
            for (int i = 0; i < cartList.size(); i++) {
                if (cartList.get(i).getNewPrice() > 0) {
                    total = total + cartList.get(i).getNewPrice() * cartList.get(i).getCartNum();
                } else {
                    if (memberData == null) {
                        total = total + cartList.get(i).getGoods_sale_price() * cartList.get(i).getCartNum();
                    } else {
                        double memberPrice = TextUtils.isEmpty(cartList.get(i).getGoods_cus_price()) ?
                                0 : Double.parseDouble(cartList.get(i).getGoods_cus_price());
                        total = total + memberPrice * cartList.get(i).getCartNum();
                    }
                }
            }
            HangData data = new HangData(type,
                    String.valueOf(System.currentTimeMillis() - 2000000000),
                    memberName,
                    memberStr,
                    total,
                    cartList.size(),
                    new Gson().toJson(cartList));
            if (data.save()) {
                //挂单打印小票
                Log.e(tag, "挂单成功 = " + isUseHangPrintReceipt());
                printReceiptHang();
                clearGoods(0, -1, "");
                goToActivityForResult(HangOrderActivity.class, Constants.HANG_ORDER);
            } else {
                goToActivityForResult(HangOrderActivity.class, Constants.HANG_ORDER);
            }
        } else {
            goToActivityForResult(HangOrderActivity.class, Constants.HANG_ORDER);
        }
    }

    /**
     * dialog(添加无码商品)
     *
     * @param type 0.无码称重 1.无码商品
     */
    private void showDialogNoBarcode(int type, int position) {
        GoodsNoBarcodeDialog.showDialog(this, type, (price, count) -> {
            Log.e(tag, "count = " + count);
            dataList.get(position).setCartNum(dataList.get(position).getCartNum() + count);
            mAdapter.notifyItemChanged(position, dataList.get(position));
//            dataList.get(position).setNewPrice(price);
//            dataList.get(position).setCartNum(count);
//            mAdapter.notifyItemChanged(position, dataList.get(position));
//            cartList.add(0, dataList.get(position));
//            cartAdapter.setDataList(cartList);
//            isDifferentPlayUpdate = true;
//            GoodsData data = new GoodsData(dataList.get(position).getGoods_barcode(),
//                    dataList.get(position).getGoods_id(),
//                    dataList.get(position).getGoods_kind_unique(),
//                    dataList.get(position).getGoods_name(),
//                    price,
//                    count);
            GoodsData data = new GoodsData(dataList.get(position).getGoods_barcode(),
                    dataList.get(position).getGoods_id(),
                    dataList.get(position).getGoods_kind_unique(),
                    dataList.get(position).getGoods_name(),
                    price,
                    count);
            cartList.add(0, data);
            cartAdapter.setDataList(cartList);
            isDifferentPlayUpdate = true;
            getTotal();
        });
    }

    /**
     * dialog(修改购物车单价、数量、总价)
     *
     * @param type      0.单价 1.数量 2.总价
     * @param chengType 0.标品 1.称重
     * @param position  数组下标
     */
    private void showDialogCartEdit(int type, double value, int chengType, int position) {
        CartEditDialog.showDialog(this, type, value, chengType, new CartEditDialog.MyListener() {
            @Override
            public void onValueClick(double value) {
                switch (type) {
                    case 1:
                        //修改数量
                        double countChange = value - cartList.get(position).getCartNum();//改变数量
                        Log.e(tag, "修改数量 = " + countChange + " cartNum = " + cartList.get(position).getCartNum());
                        cartList.get(position).setCartNum(value);
                        cartList.get(position).setEditCount(false);
                        cartAdapter.notifyItemChanged(position);
                        for (int i = 0; i < dataList.size(); i++) {
                            if (!TextUtils.isEmpty(cartList.get(position).getGoods_barcode()) && !TextUtils.isEmpty(dataList.get(i).getGoods_barcode())) {
                                if (cartList.get(position).getGoods_barcode().equals(dataList.get(i).getGoods_barcode())) {
                                    Log.e(tag, "cartNum = " + dataList.get(i).getCartNum() + " change = " + countChange);
                                    if (dataList.get(i).getGoods_barcode().equals("999999999") || dataList.get(i).getGoods_barcode().equals("999999998")) {
                                        dataList.get(i).setCartNum(dataList.get(i).getCartNum() + countChange);
                                    } else {
                                        dataList.get(i).setCartNum(value);
                                    }
//                                    dataList.get(i).setCartNum(dataList.get(i).getCartNum() + countChange);
                                    mAdapter.notifyItemChanged(i);
                                }
                            }
                        }
                        isDifferentPlayUpdate = true;
                        getTotal();
                        break;
                    case 2:
                        //修改总价
                        cartList.get(position).setNewPrice(value / cartList.get(position).getCartNum());
                        cartList.get(position).setEditTotal(false);
                        cartAdapter.notifyItemChanged(position);
                        isDifferentPlayUpdate = true;
                        getTotal();
                        break;
                    default:
                        //修改单价
                        cartList.get(position).setNewPrice(value);
                        cartList.get(position).setEditPrice(false);
                        cartAdapter.notifyItemChanged(position);
                        isDifferentPlayUpdate = true;
                        getTotal();
                        break;
                }
            }

            @Override
            public void onCancelClick() {
                //取消
                if (cartList.get(position).isEditPrice()) {
                    cartList.get(position).setEditPrice(false);
                }
                if (cartList.get(position).isEditCount()) {
                    cartList.get(position).setEditCount(false);
                }
                if (cartList.get(position).isEditTotal()) {
                    cartList.get(position).setEditTotal(false);
                }
                cartAdapter.notifyItemChanged(position);
            }
        });
    }

    /**
     * dialog(选择会员)
     *
     * @param type 0.选择会员 1.储值卡收款
     */
    private void showDialogMember(int type) {
        MemberDialog.showDialog(this, data -> {
            memberData = data;
            isDifferentPlayUpdate = true;
            setUIMember();
            if (type == 1) {
                showDialogPaymentMember();
            }
        });
    }

    /**
     * 更新UI-会员信息
     */
    private void setUIMember() {
        if (memberData == null) {
            mBinding.vLeft.tvMemberSelect.setVisibility(View.VISIBLE);
            mBinding.vLeft.linMember.setVisibility(View.GONE);
        } else {
            mBinding.vLeft.tvMemberSelect.setVisibility(View.GONE);
            mBinding.vLeft.linMember.setVisibility(View.VISIBLE);
//            Glide.with(mContext)
//                    .load(StringUtils.handledImgUrl(memberData.getCusHeadPath()))
//                    .apply(new RequestOptions().error(R.mipmap.ic_head003))
//                    .into(mBinding.vLeft.ivMemberHead);
            mBinding.vLeft.tvMemberName.setText(memberData.getCusName());
            mBinding.vLeft.tvMemberMobile.setText(memberData.getCusPhone());
            mBinding.vLeft.tvMemberPoints.setText(DFUtils.getNum2(memberData.getCusPoints()));
            mBinding.vLeft.tvMemberBalance.setText(DFUtils.getNum2(memberData.getTotalBalance()));
        }
        cartAdapter.setMemberData(memberData);
        getTotal();
    }

    /**
     * dialog(储值卡收款)
     */
    private void showDialogPaymentMember() {
        PaymentMemberDialog.showDialog(this, memberData, totalReal, new PaymentMemberDialog.MyListener() {
            @Override
            public void onPaymentStore(double total) {
                //储值卡收款
                memberMoney = total;
                getSaleListUnique(5);
            }

            @Override
            public void onPaymentJinq(double total, String saleListUnique) {
                //金圈收款
                memberMoney = 0;
                scanCode = saleListUnique;
                getSaleListUnique(13);
            }

            @Override
            public void onPaymentCash(double total) {
                //现金收款
                memberMoney = 0;
                getSaleListUnique(1);
            }

            @Override
            public void onPaymentCombination(double total, double storeTotal, double jinqTotal, double cashTotal) {
                //组合收款
                memberMoney = storeTotal;
                arrayPayment = new JSONArray();
                if (storeTotal > 0) {
                    JSONObject object = new JSONObject();
                    object.put("pay_method", 5);
                    object.put("pay_money", storeTotal);
                    arrayPayment.add(object);
                }
                if (jinqTotal > 0) {
                    JSONObject object = new JSONObject();
                    object.put("pay_method", 13);
                    object.put("pay_money", jinqTotal);
                    arrayPayment.add(object);
                }
                if (cashTotal > 0) {
                    JSONObject object = new JSONObject();
                    object.put("pay_method", 1);
                    object.put("pay_money", cashTotal);
                    arrayPayment.add(object);
                }
                getSaleListUnique(8);
            }

            @Override
            public void onMemberRecharge(double money) {
                //会员充值
                if (memberData != null) {
                    mBinding.vLeft.tvMemberBalance.setText(DFUtils.getNum2(memberData.getTotalBalance() + money));
                }
            }
        });
    }

    /**
     * dialog（刷脸付）
     */
    private void showDialogPaymentFace() {
        if (!CameraUtils.hasCamera()) {
            showToast(1, getRstr(R.string.place_connect_camera));
            mSystemTTS.playText(getRstr(R.string.place_connect_camera));
            return;
        }
        PaymentFaceDialog.showDialog(this, mPresentation, total, new PaymentFaceDialog.MyListener() {
            @Override
            public void onPaymentMember(MemberData data) {
                memberData = data;
                getSaleListUnique(16);
            }

            @Override
            public void onCameraFail(String msg) {
                //获取相机失败
                showToast(1, msg);
                mSystemTTS.playText(msg);
            }
        });
    }

    /**
     * 获取支付方式数据
     */
    private void initPaymentData() {
        paymentList.clear();
        List<PaymentData> list = LitePal.findAll(PaymentData.class);
        if (list != null) {
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i).isSelect()) {
                    paymentList.add(list.get(i));
                }
            }
        }
        paymentAdapter.setDataList(paymentList);
    }

    /**
     * dialog(整单折扣)
     */
    private void showDialogDiscount() {
        double total = 0;
        for (int i = 0; i < cartList.size(); i++) {
            if (cartList.get(i).getNewPrice() > 0) {
                total = total + cartList.get(i).getCartNum() * cartList.get(i).getNewPrice();
            } else {
                if (memberData == null) {
                    total = total + cartList.get(i).getCartNum() * cartList.get(i).getGoods_sale_price();
                } else {
                    double memberPrice = TextUtils.isEmpty(cartList.get(i).getGoods_cus_price()) ?
                            0 : Double.parseDouble(cartList.get(i).getGoods_cus_price());
                    total = total + cartList.get(i).getCartNum() * memberPrice;
                }
            }
        }
        DiscountDialog.showDialog(this, total, (money, discount1) -> {
            discount = discount1;
            totalReal = money;
            mBinding.vLeft.tvTotalReal.setText(DFUtils.getNum2(totalReal));
            mBinding.vLeft.tvDiscountMoney.setText("-" + DFUtils.getNum2(discount));
            setCartListDifferentDisplay2(-1, "");
        });
    }

    /**
     * dialog(收款状态)
     *
     * @param status   0.收款成功 1.收款失败 2.收款中
     * @param errorMsg 收款失败原因
     * @param type     收款方式
     */
    private void showDialogPaymentStatus(int status, String errorMsg, int type) {
        //收款成功
        if (status == 0) {
            String msg;
            switch (type) {
                case 1:
                    msg = getRstr(R.string.cash) + getRstr(R.string.collection_success) + DFUtils.getNum2(totalReal) + getRstr(R.string.yuan);
                    break;
                case 2:
                    msg = getRstr(R.string.alipay) + getRstr(R.string.collection_success) + DFUtils.getNum2(totalReal) + getRstr(R.string.yuan);
                    break;
                case 3:
                    msg = getRstr(R.string.wechat) + getRstr(R.string.collection_success) + DFUtils.getNum2(totalReal) + getRstr(R.string.yuan);
                    break;
                case 4:
                    msg = getRstr(R.string.bank_card) + getRstr(R.string.collection_success) + DFUtils.getNum2(totalReal) + getRstr(R.string.yuan);
                    break;
                case 5:
                    msg = getRstr(R.string.stored_card) + getRstr(R.string.collection_success) + DFUtils.getNum2(totalReal) + getRstr(R.string.yuan);
                    break;
                case 8:
                    msg = getRstr(R.string.combination) + getRstr(R.string.collection_success) + DFUtils.getNum2(totalReal) + getRstr(R.string.yuan);
                    break;
                case 15:
                    msg = getRstr(R.string.pos) + getRstr(R.string.collection_success) + DFUtils.getNum2(totalReal) + getRstr(R.string.yuan);
                    break;
                default:
                    msg = getRstr(R.string.collection_success) + DFUtils.getNum2(totalReal) + getRstr(R.string.yuan);
                    break;
            }
            //播报
            switch (type) {
                case 1:
                case 2:
                case 3:
                case 4:
                    if (isVoicePaymentOffline()) {
                        mSystemTTS.playText(msg);
                    }
                    break;
                case 5:
                    if (isVoicePaymentMember()) {
                        mSystemTTS.playText(msg);
                    }
                    break;
                case 8:
                    if (isVoicePaymentCombination()) {
                        mSystemTTS.playText(msg);
                    }
                    break;
                default:
                    if (isVoicePaymentOnline()) {
                        mSystemTTS.playText(msg);
                    }
                    break;
            }
//            mSystemTTS.playText(msg);
            //收款成功：补打小票
            PaymentSuccessDialog.showDialog(this, totalReal, this::printReceipt);
            //打印小票
            printReceipt();
            clearGoods(1, 0, "");
            memberData = null;
            isDifferentPlayUpdate = false;
            setUIMember();
            return;
        }
        //收款失败
        if (status == 1) {
            String msg;
            switch (type) {
                case 1:
                    msg = getRstr(R.string.cash) + getRstr(R.string.collection_fail);
                    break;
                case 2:
                    msg = getRstr(R.string.alipay) + getRstr(R.string.collection_fail);
                    break;
                case 3:
                    msg = getRstr(R.string.wechat) + getRstr(R.string.collection_fail);
                    break;
                case 4:
                    msg = getRstr(R.string.bank_card) + getRstr(R.string.collection_fail);
                    break;
                case 5:
                    msg = getRstr(R.string.stored_card) + getRstr(R.string.collection_fail);
                    break;
                case 8:
                    msg = getRstr(R.string.combination) + getRstr(R.string.collection_fail);
                    break;
                case 15:
                    msg = getRstr(R.string.pos) + getRstr(R.string.collection_fail);
                    break;
                default:
                    msg = getRstr(R.string.collection_fail);
                    break;
            }
            //播报
            switch (type) {
                case 1:
                case 2:
                case 3:
                case 4:
                    if (isVoicePaymentOffline()) {
                        mSystemTTS.playText(msg);
                    }
                    break;
                case 5:
                    if (isVoicePaymentMember()) {
                        mSystemTTS.playText(msg);
                    }
                    break;
                case 8:
                    if (isVoicePaymentCombination()) {
                        mSystemTTS.playText(msg);
                    }
                    break;
                default:
                    if (isVoicePaymentOnline()) {
                        mSystemTTS.playText(msg);
                    }
                    break;
            }
//            mSystemTTS.playText(msg);
            setCartListDifferentDisplay2(1, errorMsg);
            PaymentFailDialog.showDialog(this, totalReal, errorMsg, () -> {
                //重新收款
                if (!NetworkUtils.isConnected()) {
                    showToast(1, getRstr(R.string.no_network_connect));
                    return;
                }
                switch (paymentType) {
                    case 13:
                    case 15:
                        postPaymentScan(paymentType);
                        break;
                    default:
                        postPayment(paymentType);
                        break;
                }
            });
            return;
        }
        //收款中
        if (status == 2) {
            PaymentDoingDialog.showDialog(this, 0, totalReal, saleListUnique, new PaymentDoingDialog.MyListener() {
                @Override
                public void onPaymentSuccess(double salePoints0, double cusPoints0) {
                    salePoints = salePoints0;
                    cusPoints = cusPoints0;
//                    mSystemTTS.playText(getRstr(R.string.collection_success) + DFUtils.getNum2(totalReal) + getRstr(R.string.yuan));
                    if (isVoicePaymentOnline()) {
                        mSystemTTS.playText(getRstr(R.string.collection_success) + DFUtils.getNum2(totalReal) + getRstr(R.string.yuan));
                    }
                    PaymentSuccessDialog.showDialog(MainActivity.this, totalReal, () -> {
                        //补打小票
                        printReceipt();
                    });
                    clearGoods(1, 0, "");
                    memberData = null;
                    isDifferentPlayUpdate = false;
                    setUIMember();
                }

                @Override
                public void onPaymentFail(String msg) {
//                    mSystemTTS.playText(msg);
                    if (isVoicePaymentOnline()) {
                        mSystemTTS.playText(msg);
                    }
                    PaymentFailDialog.showDialog(MainActivity.this, totalReal, errorMsg, () -> {
                        //重新收款
                        postPaymentScan(13);
                    });
                }
            });
        }
    }

    /**
     * 打印收款小票
     */
    private void printReceipt() {
        if (!isPrint()) {
            return;
        }
        if (!MyApplication.getInstance().isReceiptPrinterConnect) {
            showToast(1, getRstr(R.string.printer_no_connect));
            return;
        }
        Bitmap bitmap = ZxingUtils.createBarcode(saleListUnique, 614, 100);
        PrintReceiptUsbUtils.printPayment(this,
                cartList, discount, total, totalReal,
                arrayPayment,
                memberData, salePoints, cusPoints,
                saleListUnique, bitmap);
    }

    /**
     * 打印挂单小票
     */
    private void printReceiptHang() {
        if (!isUseHangPrintReceipt()) {
            return;
        }
        if (!MyApplication.getInstance().isReceiptPrinterConnect) {
            showToast(1, getRstr(R.string.printer_no_connect));
            return;
        }
        PrintReceiptUsbUtils.printHang(this, cartList, discount, total, memberData);
    }

    /***************************MQTTstart****************************/

    /**
     * 检查读写权限
     */
    private void checkPermissionMqtt() {
        if (PermissionUtils.checkPermissionsGroup(this, 5)) {
            MyApplication.getInstance().initMqtt();
        } else {
            PermissionUtils.requestPermissions(this, Constants.PERMISSION_PHONE, 5);
        }
    }

    /***************************MQTTend****************************/

    /***************************串口start****************************/

    /**
     * 检查读写权限
     */
    private void checkPermissionSerialPort() {
        if (PermissionUtils.checkPermissionsGroup(this, 3)) {
            MyApplication.getInstance().initSerialPort();
        } else {
            PermissionUtils.requestPermissions(this, Constants.PERMISSION_READ, 3);
        }
    }

    /***************************串口end****************************/

    /***************************副屏start****************************/
    private DifferentDisplay mPresentation;

    /**
     * 检查副屏权限
     */
    private void checkPermissionDifferentDisplay() {
        if (Build.VERSION.SDK_INT >= 23) {
            if (Settings.canDrawOverlays(this)) {
                initDifferentDisplay();
            } else {
                startActivityForResult(new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:" + getPackageName())),
                        Constants.PERMISSION_OVERLAY);
            }
        } else {
            initDifferentDisplay();
        }
    }

    /**
     * 获取屏幕
     */
    private void initDifferentDisplay() {
        DisplayManager mDisplayManager = (DisplayManager) getSystemService(Context.DISPLAY_SERVICE); //屏幕管理类
        Display[] displays = mDisplayManager.getDisplays();//得到显示器数组
        if (displays != null && displays.length > 1) {
            mPresentation = new DifferentDisplay(this, displays[1]); //displays[1]是副屏
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                mPresentation.getWindow().setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
            } else {
                mPresentation.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
            }
            mPresentation.show();
        }
    }

    /***************************副屏end****************************/

    /******************************版本更新start*******************/
    private VersionData versionData;

    private void checkUpgrade() {
        HttpParams map = new HttpParams();
        map.put("app_id", "9");
        map.put("app_type", "1");
        DownloadBuilder builder = AllenVersionChecker
                .getInstance()
                .requestVersion()
                .setRequestUrl(ZURL.getVersion())
                .setRequestParams(map)
                .request(new RequestVersionListener() {
                    @Nullable
                    @Override
                    public UIData onRequestVersionSuccess(DownloadBuilder downloadBuilder, String result) {
                        Log.e(tag, "版本更新 = " + result);
                        versionData = new Gson().fromJson(result, VersionData.class);
                        if (versionData == null) {
                            return null;
                        }
                        if (versionData.getStatus() != 0) {
                            showToast(1, versionData.getMsg());
                            return null;
                        }
                        if (versionData.getData() == null) {
                            showToast(1, versionData.getMsg());
                            return null;
                        }
                        if (versionData.getData().getCode() <= PackageUtils.getPackageCode(mContext)) {
                            showToast(0, getRstr(R.string.version_newed));
                            return null;
                        }
                        if (versionData.getData().getUpdate_install() == 1) {
                            //强制更新
                            downloadBuilder.setForceUpdateListener(() -> {
                                finish();
                            });
                        }
                        return crateUIData();
                    }

                    @Override
                    public void onRequestVersionFailure(String message) {

                    }
                });
        builder.setNotificationBuilder(createCustomNotification());
        builder.setDownloadAPKPath(this.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS).getPath() + "/");
        builder.setCustomVersionDialogListener(createCustomDialog());
        builder.setCustomDownloadingDialogListener(createCustomDownloadingDialog());
        builder.setCustomDownloadFailedListener(createCustomDownloadFailedDialog());
        builder.setForceRedownload(true);
        builder.executeMission(mContext);
    }

    /**
     * 自定义下载中对话框，下载中会连续回调此方法 updateUI
     * 务必用库传回来的context 实例化你的dialog
     *
     * @return
     */
    private CustomDownloadingDialogListener createCustomDownloadingDialog() {
        return new CustomDownloadingDialogListener() {
            @Override
            public Dialog getCustomDownloadingDialog(Context context, int progress, UIData versionBundle) {
                BaseDialog<DialogDownloadingBinding> dialog = new BaseDialog<DialogDownloadingBinding>(context, R.style.dialog_style) {
                    @Override
                    protected DialogDownloadingBinding getViewBinding() {
                        return DialogDownloadingBinding.inflate(getLayoutInflater());
                    }
                };
                dialog.setCancelable(false);
                return dialog;
            }

            @Override
            public void updateUI(Dialog dialog, int progress, UIData versionBundle) {
                TextView tvProgress = dialog.findViewById(R.id.tv_progress);
                ProgressBar progressBar = dialog.findViewById(R.id.pb);
                progressBar.setProgress(progress);
                tvProgress.setText(getString(R.string.versionchecklib_progress, progress));
            }
        };
    }

    /**
     * 务必用库传回来的context 实例化你的dialog
     *
     * @return
     */
    private CustomDownloadFailedListener createCustomDownloadFailedDialog() {
        return (context, versionBundle) -> {
            BaseDialog<DialogDownloadFailBinding> dialog = new BaseDialog<DialogDownloadFailBinding>(context, R.style.dialog_style) {
                @Override
                protected DialogDownloadFailBinding getViewBinding() {
                    return DialogDownloadFailBinding.inflate(getLayoutInflater());
                }
            };
            dialog.setCancelable(false);
            return dialog;
        };
    }

    /**
     * dialog（发现新版本）
     *
     * @return
     */
    private CustomVersionDialogListener createCustomDialog() {
        return (context, versionBundle) -> {
            BaseDialog<DialogVersionBinding> dialog = new BaseDialog<DialogVersionBinding>(context, R.style.dialog_style) {
                @Override
                protected DialogVersionBinding getViewBinding() {
                    return DialogVersionBinding.inflate(getLayoutInflater());
                }
            };
            dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
            dialog.setCancelable(false);
            TextView tvVersionNew = dialog.findViewById(R.id.tvDialogVersionNew),
                    tvVersion = dialog.findViewById(R.id.tvDialogVersion),
                    tvContent = dialog.findViewById(R.id.tvDialogContent);
            tvVersionNew.setText(getRstr(R.string.version_find) + " for V" + versionData.getData().getUpdate_version());
            tvVersion.setText(getRstr(R.string.version_now) + " V" + PackageUtils.getPackageName(mContext));
            tvContent.setText(versionData.getData().getUpdate_log());
            dialog.setCanceledOnTouchOutside(false);
            return dialog;
        };
    }

    /**
     * 创建通知
     *
     * @return
     */
    private NotificationBuilder createCustomNotification() {
        return NotificationBuilder.create()
                .setRingtone(true)
                .setIcon(R.mipmap.ic_launcher)
                .setTicker("custom_ticker")
                .setContentTitle(getRstr(R.string.version_notification_title))
                .setContentText(getRstr(R.string.version_notification_content));
    }

    /**
     * @return
     * @important 使用请求版本功能，可以在这里设置downloadUrl
     * 这里可以构造UI需要显示的数据
     * UIData 内部是一个Bundle
     */
    private UIData crateUIData() {
        UIData uiData = UIData.create();
        uiData.setTitle(versionData.getData().getUpdate_des());
        uiData.setDownloadUrl(versionData.getData().getUpdate_url());
        uiData.setContent(versionData.getData().getUpdate_log());
        return uiData;
    }

    /******************************版本更新end*************************/

    /**
     * 获取商品分类数据
     */
    private void getCateList() {
        cateList.clear();
        List<CatePcData> list = LitePal.findAll(CatePcData.class);
        if (list != null) {
            if (list.size() > 11) {
                for (int i = 0; i < 11; i++) {
                    cateList.add(list.get(i));
                }
            } else {
                cateList.addAll(list);
            }
        }
        cateList.add(new CatePcData("", getRstr(R.string.all)));
        posCate = 0;
        cateUnique = cateList.get(0).getGoods_kind_unique();
        cateList.get(0).setCheck(true);
        getGoodsList();
        cateAdapter.setDataList(cateList);
    }

    /**
     * 获取商品列表数据
     */
    private void getGoodsList() {
//        List<GoodsData> goodsList = new ArrayList<>();//全部商品列表
//        if (TextUtils.isEmpty(cateUnique)) {
//            //全部
//            List<GoodsData> list;
//            if (TextUtils.isEmpty(keyWords)) {
//                list = LitePal
//                        .limit(Constants.limit)
//                        .offset(max)
//                        .find(GoodsData.class);
//            } else {
//                list = LitePal
//                        .limit(Constants.limit)
//                        .offset(max)
//                        .where("goods_name like ? or goods_barcode like ?", "%" + keyWords + "%", "%" + keyWords + "%")
//                        .find(GoodsData.class);
//            }
//            if (list != null) {
//                goodsList.addAll(list);
//            }
//        } else {
//            int size = Constants.limit;
//            List<GoodsPcData> goodsPcList = LitePal.where("cateUnique = ?", cateUnique).find(GoodsPcData.class);
//            if (cateUnique.equals("99990") && max == 0 && isShowGoodsNoBarcode()) {
//                size = Constants.limit - 2;
//            }
//            if (goodsPcList != null) {
//                for (int i = 0; i < goodsPcList.size(); i++) {
//                    List<GoodsData> list = LitePal
//                            .limit(size)
//                            .offset(max)
//                            .where("goods_barcode = ?", goodsPcList.get(i).getGoods_barcode()).find(GoodsData.class);
//                    if (list != null) {
//                        goodsList.addAll(list);
//                    }
//                }
//            }
//        }

        mBinding.vRight.smartRefreshLayout.finishRefresh();
        mBinding.vRight.smartRefreshLayout.finishLoadMore();
        List<GoodsData> goodsList = new ArrayList<>();//全部商品列表
        if (TextUtils.isEmpty(cateUnique)) {
            List<GoodsData> list;
            if (TextUtils.isEmpty(keyWords)) {
                list = LitePal
                        .limit(Constants.limit)
                        .offset(max)
                        .find(GoodsData.class);
            } else {
                list = LitePal
                        .limit(Constants.limit)
                        .offset(max)
                        .where("goods_name like ? or goods_barcode like ?", "%" + keyWords + "%", "%" + keyWords + "%")
                        .find(GoodsData.class);
            }
            if (list != null) {
                goodsList.addAll(list);
            }
        } else {
            List<GoodsData> list;
            if (TextUtils.isEmpty(keyWords)) {
                list = LitePal
                        .limit(Constants.limit)
                        .offset(max)
                        .where("virtual_kind_unique = ?", cateUnique)
                        .find(GoodsData.class);
            } else {
                list = LitePal
                        .limit(Constants.limit)
                        .offset(max)
                        .where("virtual_kind_unique = ? and goods_name like ? or goods_barcode like ?", cateUnique, "%" + keyWords + "%", "%" + keyWords + "%")
                        .find(GoodsData.class);
            }
            if (list != null) {
                goodsList.addAll(list);
            }
        }
        if (max == 0) {
            dataList.clear();
            mAdapter.clear();
            if (cateUnique.equals("99990") && isShowGoodsNoBarcode()) {
                if (isUseChangeGoodsNoBarcodeAndWeight()) {
//                    dataList.add(new GoodsData("999999998", "0", "99990", getRstr(R.string.no_barcode_goods), 0, 0));
//                    dataList.add(new GoodsData("999999999", "0", "99990", getRstr(R.string.no_barcode_weight), 0, 0));
                    dataList.add(new GoodsData("999999998", "0", "99990", getRstr(R.string.no_barcode_goods), 0, 0));
                    dataList.add(new GoodsData("999999999", "0", "99990", getRstr(R.string.no_barcode_weight), 0, 0));
                } else {
//                    dataList.add(new GoodsData("999999999", "0", "99990", getRstr(R.string.no_barcode_weight), 0, 0));
//                    dataList.add(new GoodsData("999999998", "0", "99990", getRstr(R.string.no_barcode_goods), 0, 0));
                    dataList.add(new GoodsData("999999999", "0", "99990", getRstr(R.string.no_barcode_weight), 0, 0));
                    dataList.add(new GoodsData("999999998", "0", "99990", getRstr(R.string.no_barcode_goods), 0, 0));
                }
            }
        }
        max += goodsList.size();
        dataList.addAll(goodsList);
        for (int i = 0; i < cartList.size(); i++) {
            for (int j = 0; j < dataList.size(); j++) {
                String barcode = cartList.get(i).getGoods_barcode(),
                        barcode1 = dataList.get(j).getGoods_barcode();
                if (!TextUtils.isEmpty(barcode) && !TextUtils.isEmpty(barcode1)) {
                    if (barcode.equals(barcode1)) {
                        if (barcode.equals("999999999") || barcode.equals("999999998")) {
                            dataList.get(j).setCartNum(dataList.get(j).getCartNum() + cartList.get(i).getCartNum());
                        } else {
                            dataList.get(j).setCartNum(cartList.get(i).getCartNum());
                        }
                    }
                }
            }
        }
        if (!dataList.isEmpty()) {
            mBinding.vRight.recyclerView.setVisibility(View.VISIBLE);
            mBinding.vRight.linEmpty.setVisibility(View.GONE);
            mAdapter.setDataList(dataList);
        } else {
            mBinding.vRight.recyclerView.setVisibility(View.GONE);
            mBinding.vRight.linEmpty.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 会员列表
     */
    private void getMemberList() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("searchParam", scanCode);//搜索关键字（会员卡号/手机号/姓名）
        params.put("pageNum", page);
        params.put("pageSize", 10000);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getMemberList(),
                params,
                MemberData.class,
                new RequestListListener<MemberData>() {
                    @Override
                    public void onResult(List<MemberData> list) {
                        if (list.size() < 1) {
                            showToast(1, getRstr(R.string.member_information_fail));
                            return;
                        }
                        if (list.get(0).getTotalBalance() < total) {
                            showToast(1, getRstr(R.string.balance_not_enough));
                            return;
                        }
                        getSaleListUnique(5);
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 创建订单编号
     *
     * @param type -1.扫码支付
     */
    private void getSaleListUnique(int type) {
        if (!NetworkUtils.isConnected()) {
            postPayment(type);
            return;
        }
        salePoints = 0;
        cusPoints = 0;
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getSaleListUniqueCreate(),
                new HashMap<>(),
                SaleListUniqueData.class,
                new RequestListener<SaleListUniqueData>() {
                    @Override
                    public void success(SaleListUniqueData data) {
                        saleListUnique = data.getSale_list_unique();
                        //-1.扫码支付
                        switch (type) {
                            case 13:
                            case 15:
                                postPaymentScan(type);
                                break;
                            case 16:
                                //刷脸
                                postPaymentFace();
                                break;
                            default:
                                postPayment(type);
                                break;
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 普通线下订单结算
     *
     * @param type 支付方式 1.现金 2.支付宝 3.微信 4.银行卡 5.储值卡 8.混合支付 9.免密支付
     */
    private void postPayment(int type) {
        this.paymentType = type;
        String inPrice = "",//商品进价
                salePrice = "",//商品售价
                oldPrice = "",//商品原价
                count = "",//商品数量
                name = "",//商品名称
                barcode = "",//商品条码
                goodsId = "";//商品id
        double goodsCount = 0;//商品总数量
        for (int i = 0; i < cartList.size(); i++) {
            inPrice = TextUtils.isEmpty(inPrice) ? DFUtils.getNum2(cartList.get(i).getGoods_in_price()) : inPrice + ";" + DFUtils.getNum2(cartList.get(i).getGoods_in_price());
            if (cartList.get(i).getNewPrice() > 0) {
                salePrice = DFUtils.getNum2(cartList.get(i).getNewPrice());
            } else {
                if (memberData != null) {
                    salePrice = cartList.get(i).getGoods_cus_price();
                } else {
                    salePrice = TextUtils.isEmpty(salePrice) ? DFUtils.getNum2(cartList.get(i).getGoods_sale_price()) : salePrice + ";" + DFUtils.getNum2(cartList.get(i).getGoods_sale_price());
                }
            }
            oldPrice = TextUtils.isEmpty(oldPrice) ? DFUtils.getNum2(cartList.get(i).getGoods_sale_price()) : oldPrice + ";" + DFUtils.getNum2(cartList.get(i).getGoods_sale_price());
            count = TextUtils.isEmpty(count) ? DFUtils.getNum4(cartList.get(i).getCartNum()) : count + ";" + DFUtils.getNum4(cartList.get(i).getCartNum());
            goodsCount = goodsCount + cartList.get(i).getCartNum();
            name = TextUtils.isEmpty(name) ? cartList.get(i).getGoods_name() : name + ";" + cartList.get(i).getGoods_name();
            barcode = TextUtils.isEmpty(barcode) ? cartList.get(i).getGoods_barcode() : barcode + ";" + cartList.get(i).getGoods_barcode();
            goodsId = TextUtils.isEmpty(goodsId) ? String.valueOf(cartList.get(i).getGoods_id()) : goodsId + ";" + cartList.get(i).getGoods_id();
        }

        if (type != 8) {
            arrayPayment = new JSONArray();
            JSONObject object = new JSONObject();
            object.put("pay_method", type);
            object.put("pay_money", totalReal);
            arrayPayment.add(object);
        }
        /*离线收银start*/
        if (!NetworkUtils.isConnected()) {
            OrderOfflineData orderOfflineData = new OrderOfflineData(DateUtils.getCurrentDate(DateUtils.PATTERN_DAY),
                    3,
                    inPrice,
                    salePrice,
                    oldPrice,
                    count,
                    name,
                    barcode,
                    goodsId,
                    total,
                    goodsCount,
                    "",
                    "",
                    totalReal,
                    type,
                    "",
                    2,
                    arrayPayment.toString());
            if (orderOfflineData.save()) {
                showDialogPaymentStatus(0, "", type);
            } else {
                showDialogPaymentStatus(1, "", type);
            }
            return;
        }
        /*离线收银end*/
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("saleListState", 3);//付款状态 3.已付款 4.赊账
        params.put("saleListUnique", saleListUnique);//订单编号(接口创建)
        params.put("goodsPurprice", inPrice);//商品进价 “,”隔开
        params.put("saleListDetailPrice", salePrice);//商品售价 “,”隔开
        params.put("goods_old_price", oldPrice);//商品原价 “,”隔开
        params.put("saleListDetailCount", count);//商品数量 “,”隔开
        params.put("goodsName", name);//商品名称 “,”隔开
        params.put("goodsBarcode", barcode);//商品条码 “,”隔开
        params.put("goodsId", goodsId);//商品id ","隔开
        params.put("saleListTotal", total);//订单总金额
        params.put("saleListCashier", getStaffUnique());//员工id
//        params.put("saleListRemarks", );//订单备注
//        params.put("machine_num", );//机器编号
        params.put("saleListActuallyReceived", totalReal);
        params.put("sale_list_payment", type);//支付方式 1.现金 2.支付宝 3.微信 4.银行卡 5.储值卡 8.混合支付 9.免密支付
//        params.put("machineTime", );//失败时上传当前时间
        params.put("type", 2);//固定值2
        if (memberData != null) {
            params.put("memberCard", memberData.getCusUnique());//会员编号
            params.put("storedCard", memberData.getCusUnique());//会员编号
            params.put("storedCardMoney", memberMoney);//储值卡消费金额
            params.put("pointsRatio", getPointsRatio());//积分比例 10 pointsRatio=-1时后台单独配置商品积分比例
//            params.put("cusPassword", );//密码 MD5加密
//            params.put("isCusPassword", );//是否使用密码支付 0.否 1.是
        }
//        params.put("wholesale_phone", );//批发客户的手机号
//        params.put("saleListPayDetail", arrayPayment);//支付详情
        params.put("saleListPayDetail", arrayPayment.toString());//支付详情
        showDialog();
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getPayment(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        CashierPayData data = new Gson().fromJson(s, CashierPayData.class);
                        if (data == null) {
                            showDialogPaymentStatus(1, "", type);
                            return;
                        }
                        if (data.getStatus() != Constants.SUCCESS_CODE) {
                            showDialogPaymentStatus(1, data.getMsg(), type);
                            return;
                        }
                        if (data.getCusData() != null) {
                            salePoints = data.getCusData().getSale_points();
                            cusPoints = data.getCusData().getCusPoints();
                        }
                        if (saveMoney > 0 && type == 1) {
                            postRecharge(type);
                        } else {
                            hideDialog();
                            showDialogPaymentStatus(0, "", type);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showDialogPaymentStatus(1, msg, type);
                    }
                });
    }

    /**
     * 普通线下订单结算（刷脸）
     */
    private void postPaymentFace() {
        int type = 5;
        this.paymentType = type;
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("sale_list_unique", saleListUnique);
        params.put("sale_list_cashier", getStaffUnique());//收银员ID
        params.put("sale_list_total", total);//商品总价
        params.put("sale_list_actually_received", totalReal);//实收金额
        params.put("shop_coupon_id", 0);//优惠券ID 无传 0
        params.put("point_val", 0);//积分抵扣积分 无传0
        params.put("point_deduction", 0);//积分抵扣金额 无传0
        params.put("beans_use", 0);//百货豆使用数量 无传0
        params.put("beans_money", 0);//百货豆抵扣金额 无传0
        params.put("sale_list_remarks", "");//订单备注
        params.put("sale_type", 7);//1:APP订单; 2:微信小程序; 7:收银端平台会员结算
        params.put("sale_list_paysment", 5);//支付方式”支付方式：1-现金，2-支付宝，3-微信，4-银行卡 ，5-储值卡 ,6-美团外卖,7-饿了么外卖,9-免密支付”,
        if (memberData != null) {
            params.put("cus_unique", memberData.getCusUnique());//用户编号
            params.put("card_deduction", memberMoney);//储值卡抵扣金额 无传0
        }
        //商品信息
        JSONArray arrayGoods = new JSONArray();
        for (int i = 0; i < cartList.size(); i++) {
            JSONObject object = new JSONObject();
            object.put("goods_barcode", cartList.get(i).getGoods_barcode());
            object.put("goods_name", cartList.get(i).getGoods_name());
            object.put("sale_list_detail_count", cartList.get(i).getCartNum());
            object.put("goods_id", cartList.get(i).getGoods_id());
            object.put("goods_purprice", cartList.get(i).getGoods_in_price());
            object.put("goods_old_price", cartList.get(i).getGoods_sale_price());

            double salePrice;
            if (cartList.get(i).getNewPrice() > 0) {
                salePrice = cartList.get(i).getNewPrice();
            } else {
                if (memberData != null) {
                    salePrice = TextUtils.isEmpty(cartList.get(i).getGoods_cus_price()) ? 0 : Double.parseDouble(cartList.get(i).getGoods_cus_price());
                } else {
                    salePrice = cartList.get(i).getGoods_sale_price();
                }
            }
            object.put("sale_list_detail_price", salePrice);
            arrayGoods.add(object);
        }
        params.put("goods_list", arrayGoods.toString());
        //支付详情
        arrayPayment = new JSONArray();
        JSONObject object = new JSONObject();
        object.put("pay_method", type);
        object.put("pay_money", totalReal);
        arrayPayment.add(object);
        params.put("saleListPayDetail", arrayPayment.toString());
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getPaymentFace(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showDialogPaymentStatus(0, "", type);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showDialogPaymentStatus(1, msg, type);
                    }
                });
    }

    /**
     * 存零（会员充值）
     */
    private void postRecharge(int type) {
        if (memberData == null) {
            hideDialog();
            showDialogPaymentStatus(0, "", type);
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("cusUnique", memberData.getCusUnique());
        params.put("money", saveMoney);
        params.put("type", 1);//1.充值 2.提现
        params.put("saleListCashier", getStaffUnique());
        params.put("recharge_method", 1);//充值方式 1.现金 2.微信 3.支付宝 4.存零
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getMemberRecharge(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showDialogPaymentStatus(0, "", type);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 扫码支付
     */
    private void postPaymentScan(int type) {
        if (!NetworkUtils.isConnected()) {
            showToast(1, getRstr(R.string.no_network_connect));
            return;
        }
        this.paymentType = type;
        List array = new ArrayList();
        try {
            for (int i = 0; i < cartList.size(); i++) {
                Map object = new HashMap();
                object.put("goodsBarcode", cartList.get(i).getGoods_barcode());
                object.put("goodsName", cartList.get(i).getGoods_name());
                object.put("saleListDetailCount", cartList.get(i).getCartNum());
//                object.put("saleListDetailPrice", cartList.get(i).getNewPrice());
                String salePrice = "";
                if (cartList.get(i).getNewPrice() > 0) {
                    salePrice = DFUtils.getNum2(cartList.get(i).getNewPrice());
                } else {
                    if (memberData != null) {
                        salePrice = cartList.get(i).getGoods_cus_price();
                    } else {
                        salePrice = TextUtils.isEmpty(salePrice) ? DFUtils.getNum2(cartList.get(i).getGoods_sale_price()) : salePrice + ";" + DFUtils.getNum2(cartList.get(i).getGoods_sale_price());
                    }
                }
                object.put("saleListDetailPrice", salePrice);
                object.put("goodsId", cartList.get(i).getGoods_id());
                object.put("goodsInPrice", (int) (cartList.get(i).getGoods_in_price() * 100));
                array.add(object);
            }
        } catch (Exception ignored) {
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("saleListUnique", saleListUnique);
        params.put("saleListTotal", (int) (total * 100));//订单总金额：必须为整数
        params.put("saleListCashier", getStaffUnique());//收银员编号
        params.put("machine_num", 1);//机器编号
        params.put("saleListActuallyReceived", (int) (totalReal * 100));//实收金额：必须为整数
        params.put("sale_list_paysment", type);//支付方式
        params.put("auth_code", scanCode);//授权码
        params.put("pointsRatio", getPointsRatio());//会员积分比例
        if (memberData != null) {
            params.put("memberCard", memberData.getCusUnique());//会员编号
            params.put("pointsRatio", getPointsRatio());//积分比例 10 pointsRatio=-1时后台单独配置商品积分比例
        }
        params.put("sale_list_detail", array.toString());
        if (type == 15) {
            //POS机收款
            params.put("equipmentNo", SPUtils.getInstance().getString(Constants.POS_ID, ""));
        }
        //支付详情
        arrayPayment = new JSONArray();
        JSONObject object = new JSONObject();
        object.put("pay_method", type);
        object.put("pay_money", totalReal);
        arrayPayment.add(object);
        showDialog();
//        RXHttpUtil.requestByFormPostAsResponseTimeOut(this,
//                ZURL.getPaymentScan(),
//                params,
//                CashierStatusDataOld.class,
//                new RequestListener<CashierStatusDataOld>() {
//                    @Override
//                    public void success(CashierStatusDataOld data) {
//                        hideDialog();
//                        if (data == null) {
//                            showDialogPaymentStatus(1, "", 13);
//                            return;
//                        }
//                        if (TextUtils.isEmpty(data.getTrade_state())) {
//                            showDialogPaymentStatus(1, "", 13);
//                            return;
//                        }
//                        //支付状态、SUCCESS:代表成功；DOING或USERPAYING:代表支付中;FAIL或ERROR代表失败；其他按失败处理
//                        switch (data.getTrade_state()) {
//                            case "SUCCESS":
//                                showDialogPaymentStatus(0, "", 13);
//                                break;
//                            case "DOING":
//                            case "USERPAYING":
//                                showDialogPaymentStatus(2, data.getOut_trade_no(), 13);
//                                break;
//                            default:
//                                showDialogPaymentStatus(1, "", 13);
//                                break;
//                        }
//                    }
//
//                    @Override
//                    public void onError(String msg) {
//                        hideDialog();
//                        showDialogPaymentStatus(1, msg, 13);
//                    }
//                });
        RXHttpUtil.requestByBodyPostAsOriginalResponseTimeOut(this,
                ZURL.getPaymentScan(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        CashierStatusData data = new Gson().fromJson(s, CashierStatusData.class);
                        if (data == null) {
                            showDialogPaymentStatus(1, getRstr(R.string.collection_fail), 13);
                            return;
                        }
                        if (data.getStatus() != Constants.SUCCESS_CODE) {
                            showDialogPaymentStatus(1, data.getMsg(), 13);
                            return;
                        }
                        if (data.getData() == null) {
                            showDialogPaymentStatus(1, data.getMsg(), 13);
                            return;
                        }
                        if (data.getCusData() != null) {
                            salePoints = TextUtils.isEmpty(data.getCusData().getSale_points()) ? 0 : Double.parseDouble(data.getCusData().getSale_points());
                            cusPoints = data.getCusData().getCus_points();
                        }
                        //支付状态、SUCCESS:代表成功；DOING或USERPAYING:代表支付中;FAIL或ERROR代表失败；其他按失败处理
                        switch (data.getData().getTrade_state()) {
                            case "SUCCESS":
                                showDialogPaymentStatus(0, "", 13);
                                break;
                            case "DOING":
                            case "USERPAYING":
                                showDialogPaymentStatus(2, data.getData().getOut_trade_no(), 13);
                                break;
                            default:
                                showDialogPaymentStatus(1, "", 13);
                                break;
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showDialogPaymentStatus(1, msg, 13);
                    }
                });
    }

    /**
     * 退出登录（交班）
     */
    private void postLoginOut() {
        Map<String, Object> params = new HashMap<>();
        params.put("login_id", getLoginId());
        showDialog();
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getLoginOut(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        if (data.getStatus() == 0) {
                            showToast(0, data.getMsg());
                            SPUtils.getInstance().put(Constants.LOGIN_ID, "");
                            MyApplication.getInstance().disConnect_mqtt();
                            MyApplication.getInstance().disConnectSerialPort();
                            AppManager.getInstance().finishAllActivity();
                            goToActivity(LoginActivity.class);
                        } else {
                            showToast(1, data.getMsg());
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION_CAMERA:
                //拍照、读写
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showToast(1, getRstr(R.string.no_permission_tips));
                } else {
                    showDialogPaymentFace();
                }
                break;
            case Constants.PERMISSION_READ:
                //读写
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showToast(1, getRstr(R.string.no_permission_tips));
                } else {
                    MyApplication.getInstance().initSerialPort();
                }
                break;

            case Constants.PERMISSION_PHONE:
                //设备信息
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showToast(1, getRstr(R.string.no_permission_tips));
                } else {
                    MyApplication.getInstance().initMqtt();
                }
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case Constants.PERMISSION_OVERLAY:
                //副屏
                if (Build.VERSION.SDK_INT >= 23) {
                    if (!Settings.canDrawOverlays(this)) {
                        showToast(1, getRstr(R.string.no_permission));
                    } else {
                        initDifferentDisplay();
                    }
                }
                break;
            case Constants.HANG_ORDER:
                //挂单
                if (data == null) {
                    return;
                }
                HangData hangData = (HangData) data.getSerializableExtra("hangData");
                if (hangData == null) {
                    return;
                }
                if (TextUtils.isEmpty(hangData.getJsonGoods())) {
                    return;
                }
                cartList.clear();
//                List<GoodsData> list = JSON.parseArray(hangData.getJsonGoods(), GoodsData.class);
                List<GoodsData> list = JSON.parseArray(hangData.getJsonGoods(), GoodsData.class);
                if (list != null) {
                    cartList.addAll(list);
                }
                cartAdapter.setDataList(cartList);
                isDifferentPlayUpdate = true;
                if (TextUtils.isEmpty(hangData.getJsonMember())) {
                    getTotal();
                    return;
                }
                memberData = new Gson().fromJson(hangData.getJsonMember(), MemberData.class);
                setUIMember();
                break;
        }
    }
}