package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:订单详情（实体类）
 * Created by jingang on 2024/5/21
 */
public class QueryOrderInfoData implements Serializable {

    /**
     * status : 0
     * msg : 查询成功！
     * data : {"sale_list_id":119138,"sale_list_unique":"1715962100613786","sale_list_datetime":"2024.05.18 00:08","sale_list_total":6.2,"coupon_amount":0,"saleType":"普通订单","sale_list_name":"1","sale_list_phone":"15065358459","sale_list_address":"","sale_list_state":"已付款","sale_list_handlestate":"已完成","sale_list_handlestatecode":"4","sale_list_payment":null,"sale_list_remarks":"","receipt_datetime":"","send_datetime":"","delivery_type":null,"distributionFee":0,"subDelfee":null,"payDetail":[{"payDetailId":119201,"saleListUnique":null,"payMethodCode":5,"payMethod":"储值卡","payMoney":6.2}],"payDetailStr":"储值卡:6.2","listDetail":[{"sale_list_detail_id":2262147,"sale_list_unique":null,"goods_barcode":"512019776186","goods_name":"雪碧听装330ml","goods_picturepath":"/image/suppliers/GS112379213/f940189f-e2bb-4917-8011-0689728002d8.jpg","sale_list_detail_count":1,"sale_list_detail_price":6.2,"goods_purprice":3,"goods_standard":"","sub_total":6.2,"retCount":0,"retPrice":0,"saleListExpressId":"-1"}],"ret_list_total_money":0,"return_price":null,"beans_money":"0.00","beans_get":"0.00","cus_name":"1","saleListVerifyList":null}
     * data1 : 0
     * address : null
     * cus_data : null
     */

    private int status;
    private String msg;
    private DataBean data;
    private int data1;
    private Object address;
    private Object cus_data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public int getData1() {
        return data1;
    }

    public void setData1(int data1) {
        this.data1 = data1;
    }

    public Object getAddress() {
        return address;
    }

    public void setAddress(Object address) {
        this.address = address;
    }

    public Object getCus_data() {
        return cus_data;
    }

    public void setCus_data(Object cus_data) {
        this.cus_data = cus_data;
    }

    public static class DataBean {
        /**
         * sale_list_id : 119138
         * sale_list_unique : 1715962100613786
         * sale_list_datetime : 2024.05.18 00:08
         * sale_list_total : 6.2
         * coupon_amount : 0.0
         * saleType : 普通订单
         * sale_list_name : 1
         * sale_list_phone : 15065358459
         * sale_list_address :
         * sale_list_state : 已付款
         * sale_list_handlestate : 已完成
         * sale_list_handlestatecode : 4
         * sale_list_payment : null
         * sale_list_remarks :
         * receipt_datetime :
         * send_datetime :
         * delivery_type : null
         * distributionFee : 0.0
         * subDelfee : null
         * payDetail : [{"payDetailId":119201,"saleListUnique":null,"payMethodCode":5,"payMethod":"储值卡","payMoney":6.2}]
         * payDetailStr : 储值卡:6.2
         * listDetail : [{"sale_list_detail_id":2262147,"sale_list_unique":null,"goods_barcode":"512019776186","goods_name":"雪碧听装330ml","goods_picturepath":"/image/suppliers/GS112379213/f940189f-e2bb-4917-8011-0689728002d8.jpg","sale_list_detail_count":1,"sale_list_detail_price":6.2,"goods_purprice":3,"goods_standard":"","sub_total":6.2,"retCount":0,"retPrice":0,"saleListExpressId":"-1"}]
         * ret_list_total_money : 0.0
         * return_price : null
         * beans_money : 0.00
         * beans_get : 0.00
         * cus_name : 1
         * saleListVerifyList : null
         */

        private String sale_list_id;
        private String sale_list_unique;
        private String sale_list_datetime;//下单时间
        private double sale_list_total;//订单总金额
        private double coupon_amount;
        private String saleType;
        private String sale_list_name;//姓名
        private String sale_list_phone;//电话
        private String sale_list_address;//地址
        private String sale_list_state;//订单支付方式
        private String sale_list_handlestate;//订单状态
        private String sale_list_handlestatecode;
        private String sale_list_payment;
        private String sale_list_remarks;//订单备注
        private String receipt_datetime;//完成时间
        private String send_datetime;//发货时间
        private String delivery_type;//-1.自提 0.自配送 1.美团配送 2.一刻钟
        private double distributionFee;//配送费
        private String subDelfee;
        private String payDetailStr;//订单支付方式详情
        private double ret_list_total_money;
        private String return_price;//退换差价金额
        private String beans_money;
        private String beans_get;
        private String cus_name;
        private String saleListVerifyList;
        private List<PayDetailBean> payDetail;
        private List<ListDetailBean> listDetail;

        public String getSale_list_id() {
            return sale_list_id;
        }

        public void setSale_list_id(String sale_list_id) {
            this.sale_list_id = sale_list_id;
        }

        public String getSale_list_unique() {
            return sale_list_unique;
        }

        public void setSale_list_unique(String sale_list_unique) {
            this.sale_list_unique = sale_list_unique;
        }

        public String getSale_list_datetime() {
            return sale_list_datetime;
        }

        public void setSale_list_datetime(String sale_list_datetime) {
            this.sale_list_datetime = sale_list_datetime;
        }

        public double getSale_list_total() {
            return sale_list_total;
        }

        public void setSale_list_total(double sale_list_total) {
            this.sale_list_total = sale_list_total;
        }

        public double getCoupon_amount() {
            return coupon_amount;
        }

        public void setCoupon_amount(double coupon_amount) {
            this.coupon_amount = coupon_amount;
        }

        public String getSaleType() {
            return saleType;
        }

        public void setSaleType(String saleType) {
            this.saleType = saleType;
        }

        public String getSale_list_name() {
            return sale_list_name;
        }

        public void setSale_list_name(String sale_list_name) {
            this.sale_list_name = sale_list_name;
        }

        public String getSale_list_phone() {
            return sale_list_phone;
        }

        public void setSale_list_phone(String sale_list_phone) {
            this.sale_list_phone = sale_list_phone;
        }

        public String getSale_list_address() {
            return sale_list_address;
        }

        public void setSale_list_address(String sale_list_address) {
            this.sale_list_address = sale_list_address;
        }

        public String getSale_list_state() {
            return sale_list_state;
        }

        public void setSale_list_state(String sale_list_state) {
            this.sale_list_state = sale_list_state;
        }

        public String getSale_list_handlestate() {
            return sale_list_handlestate;
        }

        public void setSale_list_handlestate(String sale_list_handlestate) {
            this.sale_list_handlestate = sale_list_handlestate;
        }

        public String getSale_list_handlestatecode() {
            return sale_list_handlestatecode;
        }

        public void setSale_list_handlestatecode(String sale_list_handlestatecode) {
            this.sale_list_handlestatecode = sale_list_handlestatecode;
        }

        public String getSale_list_payment() {
            return sale_list_payment;
        }

        public void setSale_list_payment(String sale_list_payment) {
            this.sale_list_payment = sale_list_payment;
        }

        public String getSale_list_remarks() {
            return sale_list_remarks;
        }

        public void setSale_list_remarks(String sale_list_remarks) {
            this.sale_list_remarks = sale_list_remarks;
        }

        public String getReceipt_datetime() {
            return receipt_datetime;
        }

        public void setReceipt_datetime(String receipt_datetime) {
            this.receipt_datetime = receipt_datetime;
        }

        public String getSend_datetime() {
            return send_datetime;
        }

        public void setSend_datetime(String send_datetime) {
            this.send_datetime = send_datetime;
        }

        public String getDelivery_type() {
            return delivery_type;
        }

        public void setDelivery_type(String delivery_type) {
            this.delivery_type = delivery_type;
        }

        public double getDistributionFee() {
            return distributionFee;
        }

        public void setDistributionFee(double distributionFee) {
            this.distributionFee = distributionFee;
        }

        public String getSubDelfee() {
            return subDelfee;
        }

        public void setSubDelfee(String subDelfee) {
            this.subDelfee = subDelfee;
        }

        public String getPayDetailStr() {
            return payDetailStr;
        }

        public void setPayDetailStr(String payDetailStr) {
            this.payDetailStr = payDetailStr;
        }

        public double getRet_list_total_money() {
            return ret_list_total_money;
        }

        public void setRet_list_total_money(double ret_list_total_money) {
            this.ret_list_total_money = ret_list_total_money;
        }

        public String getReturn_price() {
            return return_price;
        }

        public void setReturn_price(String return_price) {
            this.return_price = return_price;
        }

        public String getBeans_money() {
            return beans_money;
        }

        public void setBeans_money(String beans_money) {
            this.beans_money = beans_money;
        }

        public String getBeans_get() {
            return beans_get;
        }

        public void setBeans_get(String beans_get) {
            this.beans_get = beans_get;
        }

        public String getCus_name() {
            return cus_name;
        }

        public void setCus_name(String cus_name) {
            this.cus_name = cus_name;
        }

        public String getSaleListVerifyList() {
            return saleListVerifyList;
        }

        public void setSaleListVerifyList(String saleListVerifyList) {
            this.saleListVerifyList = saleListVerifyList;
        }

        public List<PayDetailBean> getPayDetail() {
            return payDetail;
        }

        public void setPayDetail(List<PayDetailBean> payDetail) {
            this.payDetail = payDetail;
        }

        public List<ListDetailBean> getListDetail() {
            return listDetail;
        }

        public void setListDetail(List<ListDetailBean> listDetail) {
            this.listDetail = listDetail;
        }

        public static class PayDetailBean {
            /**
             * 订单核单列表
             * payDetailId : 119201
             * saleListUnique : null
             * payMethodCode : 5
             * payMethod : 储值卡
             * payMoney : 6.2
             */

            private String payDetailId;
            private String saleListUnique;
            private int payMethodCode;//支付方式
            private String payMethod;//支付方式名称
            private double payMoney;//支付金额

            public String getPayDetailId() {
                return payDetailId;
            }

            public void setPayDetailId(String payDetailId) {
                this.payDetailId = payDetailId;
            }

            public String getSaleListUnique() {
                return saleListUnique;
            }

            public void setSaleListUnique(String saleListUnique) {
                this.saleListUnique = saleListUnique;
            }

            public int getPayMethodCode() {
                return payMethodCode;
            }

            public void setPayMethodCode(int payMethodCode) {
                this.payMethodCode = payMethodCode;
            }

            public String getPayMethod() {
                return payMethod;
            }

            public void setPayMethod(String payMethod) {
                this.payMethod = payMethod;
            }

            public double getPayMoney() {
                return payMoney;
            }

            public void setPayMoney(double payMoney) {
                this.payMoney = payMoney;
            }
        }

        public class ListDetailBean {
            /**
             * 查询订单详情
             * sale_list_detail_id : 2262147
             * sale_list_unique : null
             * goods_barcode : 512019776186
             * goods_name : 雪碧听装330ml
             * goods_picturepath : /image/suppliers/GS112379213/f940189f-e2bb-4917-8011-0689728002d8.jpg
             * sale_list_detail_count : 1.0
             * sale_list_detail_price : 6.2
             * goods_purprice : 3.0
             * goods_standard :
             * sub_total : 6.2
             * retCount : 0.0
             * retPrice : 0.0
             * saleListExpressId : -1
             */
            private boolean select;
            private String sale_list_detail_id;
            private String sale_list_unique;
            private String goods_barcode;
            private String goods_name;
            private String goods_picturepath;
            private double sale_list_detail_count;//数量
            private double sale_list_detail_price;//单价
            private double goods_purprice;
            private String goods_standard;
            private double sub_total;//小计
            private double retCount;//退货数量
            private double retPrice;//退货价格
            private String saleListExpressId;
            private double refundCount;//退货数量
            private int goods_cheng_type;//0.标品 1.称重

            public boolean isSelect() {
                return select;
            }

            public void setSelect(boolean select) {
                this.select = select;
            }

            public String getSale_list_detail_id() {
                return sale_list_detail_id;
            }

            public void setSale_list_detail_id(String sale_list_detail_id) {
                this.sale_list_detail_id = sale_list_detail_id;
            }

            public String getSale_list_unique() {
                return sale_list_unique;
            }

            public void setSale_list_unique(String sale_list_unique) {
                this.sale_list_unique = sale_list_unique;
            }

            public String getGoods_barcode() {
                return goods_barcode;
            }

            public void setGoods_barcode(String goods_barcode) {
                this.goods_barcode = goods_barcode;
            }

            public String getGoods_name() {
                return goods_name;
            }

            public void setGoods_name(String goods_name) {
                this.goods_name = goods_name;
            }

            public String getGoods_picturepath() {
                return goods_picturepath;
            }

            public void setGoods_picturepath(String goods_picturepath) {
                this.goods_picturepath = goods_picturepath;
            }

            public double getSale_list_detail_count() {
                return sale_list_detail_count;
            }

            public void setSale_list_detail_count(double sale_list_detail_count) {
                this.sale_list_detail_count = sale_list_detail_count;
            }

            public double getSale_list_detail_price() {
                return sale_list_detail_price;
            }

            public void setSale_list_detail_price(double sale_list_detail_price) {
                this.sale_list_detail_price = sale_list_detail_price;
            }

            public double getGoods_purprice() {
                return goods_purprice;
            }

            public void setGoods_purprice(double goods_purprice) {
                this.goods_purprice = goods_purprice;
            }

            public String getGoods_standard() {
                return goods_standard;
            }

            public void setGoods_standard(String goods_standard) {
                this.goods_standard = goods_standard;
            }

            public double getSub_total() {
                return sub_total;
            }

            public void setSub_total(double sub_total) {
                this.sub_total = sub_total;
            }

            public double getRetCount() {
                return retCount;
            }

            public void setRetCount(double retCount) {
                this.retCount = retCount;
            }

            public double getRetPrice() {
                return retPrice;
            }

            public void setRetPrice(double retPrice) {
                this.retPrice = retPrice;
            }

            public String getSaleListExpressId() {
                return saleListExpressId;
            }

            public void setSaleListExpressId(String saleListExpressId) {
                this.saleListExpressId = saleListExpressId;
            }

            public double getRefundCount() {
                return refundCount;
            }

            public void setRefundCount(double refundCount) {
                this.refundCount = refundCount;
            }

            public int getGoods_cheng_type() {
                return goods_cheng_type;
            }

            public void setGoods_cheng_type(int goods_cheng_type) {
                this.goods_cheng_type = goods_cheng_type;
            }
        }
    }
}
