package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;

import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogPurchaseVoucherAddBinding;
import com.yxl.cashier_retail.ui.activity.ImgBigActivity;
import com.yxl.cashier_retail.ui.adapter.ImgSelectAdapter;
import com.yxl.cashier_retail.ui.bean.UrlData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.utils.picture.GlideEngine;
import com.yxl.cashier_retail.utils.picture.ImageFileCompressEngine;
import com.yxl.cashier_retail.utils.picture.ImageFileCropEngine;
import com.yxl.cashier_retail.utils.picture.MeSandboxFileEngine;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Describe:dialog（购销单详情-添加单据凭证）
 * Created by jingang on 2024/6/14
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class PurchaseVoucherAddDialog extends BaseDialog<DialogPurchaseVoucherAddBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private static double total;

    private ImgSelectAdapter imgAdapter;
    private List<String> imgList = new ArrayList<>();

    public static void showDialog(Activity activity, double total, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        PurchaseVoucherAddDialog.listener = listener;
        PurchaseVoucherAddDialog.total = total;
        PurchaseVoucherAddDialog dialog = new PurchaseVoucherAddDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public PurchaseVoucherAddDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        mBinding.etDialogMoney.setText(total > 0 ? DFUtils.getNum4(total) : "");
        setAdapter();
    }

    @Override
    protected DialogPurchaseVoucherAddBinding getViewBinding() {
        return DialogPurchaseVoucherAddBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //确认
                double money = TextUtils.isEmpty(mBinding.etDialogMoney.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etDialogMoney.getText().toString().trim());
                if (money == 0) {
                    showToast(1, getRstr(R.string.input_repayment_total));
                    return;
                }
                String remarks = mBinding.etDialogRemarks.getText().toString().trim();
                if (imgList.size() < 1) {
                    showToast(1, getRstr(R.string.plase_purchase_voucher_upload));
                    return;
                }
                List array = new ArrayList();
                try {
                    for (int i = 0; i < imgList.size(); i++) {
                        array.add(imgList.get(i));
                    }
                } catch (Exception ignored) {
                }
                if (listener != null) {
                    listener.onConfirm(money, remarks, array);
                    dismiss();
                }
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        imgAdapter = new ImgSelectAdapter(mActivity);
        mBinding.rvDialog.setAdapter(imgAdapter);
        imgAdapter.setSize(3);
        imgAdapter.setListener(new ImgSelectAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                if (position == imgAdapter.getDataList().size()) {
                    //选择图片
                    showDialogCamera();
                } else {
                    //查看图片
                    mActivity.startActivity(new Intent(mActivity, ImgBigActivity.class)
                            .putExtra("img", (Serializable) imgList)
                            .putExtra("index", position)
                    );
                }
            }

            @Override
            public void onDelClick(View view, int position) {
                //删除
                IAlertDialog.showDialog(mActivity,
                        getRstr(R.string.confirm_del_img),
                        getRstr(R.string.confirm),
                        (dialog, which) -> {
                            imgList.remove(position);
                            imgAdapter.setDataList(imgList);
                            mBinding.tvDialogCount.setText("(" + imgList.size() + "/3)");
                        });
            }
        });
    }

    /**
     * 选择图片方式弹窗
     */
    private void showDialogCamera() {
        CameraDialog.showDialog(mActivity, (type) -> {
            //type: 0.拍照 1.从相册选择
            if (type == 1) {
                pickPhoto();
            } else {
                takePhoto();
            }
        });
    }

    //拍照获取图片
    private void takePhoto() {
        PictureSelector.create(mActivity)
                .openCamera(SelectMimeType.ofImage())
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())
                .setCropEngine(new ImageFileCropEngine(mActivity))
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        mediaList.clear();
                        mediaList.addAll(result);
                        i = 0;
                        postFile();
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    //从相册中取图片
    private void pickPhoto() {
        PictureSelector.create(mActivity)
                .openGallery(SelectMimeType.ofImage())
                .setImageEngine(GlideEngine.createGlideEngine())
                .setMaxSelectNum(3 - imgList.size())
                .setCropEngine(new ImageFileCropEngine(mActivity))
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        mediaList.clear();
                        mediaList.addAll(result);
                        i = 0;
                        postFile();
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    private List<LocalMedia> mediaList = new ArrayList<>();
    private int i;

    /**
     * 上传文件
     */
    private void postFile() {
        if (mediaList.size() < 1) {
            return;
        }
        showDialog();
        File file = new File(mediaList.get(i).getAvailablePath());
        RXHttpUtil.requestByPostUploadFile((LifecycleOwner) mActivity,
                ZURL.getUploadFile(),
                file,
                UrlData.class,
                new RequestListener<UrlData>() {
                    @Override
                    public void success(UrlData data) {
                        imgList.add(data.getUrl());
                        imgAdapter.setDataList(imgList);
                        if (mediaList.size() > i) {
                            i++;
                            mBinding.tvDialogCount.setText("(" + imgList.size() + "/3)");
                            postFile();
                        } else {
                            hideDialog();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        if (mediaList.size() > i) {
                            i++;
                            postFile();
                        } else {
                            hideDialog();
                        }
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(double money, String remarks, List array);
    }
}
