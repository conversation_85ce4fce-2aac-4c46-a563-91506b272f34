package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:供货商分类（实体类）
 * Created by jingang on 2024/6/7
 */
public class SupplierCateData implements Serializable {
    /**
     * supplierKindUnique : SSK202309161236110110000
     * supplierKindName : 食品
     * subKindList : [{"supplierKindUnique":"SSK202309161236110110001","supplierKindName":"蔬菜"}]
     */
    private boolean select;
    private String supplierKindUnique;
    private String supplierKindName;
    private List<SubKindListBean> subKindList;

    public SupplierCateData() {
    }

    public SupplierCateData(boolean select, String supplierKindUnique, String supplierKindName) {
        this.select = select;
        this.supplierKindUnique = supplierKindUnique;
        this.supplierKindName = supplierKindName;
    }

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }

    public String getSupplierKindUnique() {
        return supplierKindUnique;
    }

    public void setSupplierKindUnique(String supplierKindUnique) {
        this.supplierKindUnique = supplierKindUnique;
    }

    public String getSupplierKindName() {
        return supplierKindName;
    }

    public void setSupplierKindName(String supplierKindName) {
        this.supplierKindName = supplierKindName;
    }

    public List<SubKindListBean> getSubKindList() {
        return subKindList;
    }

    public void setSubKindList(List<SubKindListBean> subKindList) {
        this.subKindList = subKindList;
    }

    public static class SubKindListBean {
        /**
         * supplierKindUnique : SSK202309161236110110001
         * supplierKindName : 蔬菜
         */

        private String supplierKindUnique;
        private String supplierKindName;

        public String getSupplierKindUnique() {
            return supplierKindUnique;
        }

        public void setSupplierKindUnique(String supplierKindUnique) {
            this.supplierKindUnique = supplierKindUnique;
        }

        public String getSupplierKindName() {
            return supplierKindName;
        }

        public void setSupplierKindName(String supplierKindName) {
            this.supplierKindName = supplierKindName;
        }
    }
}
