package com.yxl.cashier_retail.ui.bean;

import org.litepal.crud.LitePalSupport;

import java.io.Serializable;

/**
 * Describe:离线订单（实体类）
 * Created by jingang on 2024/9/18
 */
public class OrderOfflineData extends LitePalSupport implements Serializable {
    private int id;
    private String createTime;//创建时间
    private int saleListState;//付款状态 3.已付款 4.赊账
    private String goodsPurprice;//商品进价 “,”隔开
    private String saleListDetailPrice;//商品售价 “,”隔开
    private String goods_old_price;//商品原价 “,”隔开
    private String saleListDetailCount;//商品数量 “,”隔开
    private String goodsName;//商品名称 “,”隔开
    private String goodsBarcode;//商品条码 “,”隔开
    private String goodsId;//商品id “,”隔开
    private double saleListTotal;//订单总金额
    private double count;//商品总数量
    private String saleListRemarks;//订单备注
    private String machine_num;//机器编号
    private double saleListActuallyReceived;//实收金额
    private int sale_list_payment;//支付方式 1.现金 2.支付宝 3.微信 4.银行卡 5.储值卡 8.混合支付 9.免密支付
    private String machineTime;//失败上传当前时间
    private int type;//固定值 传2
    private String saleListPayDetail;//支付详情 “[{pay_method:1,pay_money:1}]”

    public OrderOfflineData() {
    }

    public OrderOfflineData(String createTime,int saleListState, String goodsPurprice, String saleListDetailPrice, String goods_old_price, String saleListDetailCount, String goodsName, String goodsBarcode, String goodsId, double saleListTotal, double count, String saleListRemarks, String machine_num, double saleListActuallyReceived, int sale_list_payment, String machineTime, int type, String saleListPayDetail) {
        this.createTime = createTime;
        this.saleListState = saleListState;
        this.goodsPurprice = goodsPurprice;
        this.saleListDetailPrice = saleListDetailPrice;
        this.goods_old_price = goods_old_price;
        this.saleListDetailCount = saleListDetailCount;
        this.goodsName = goodsName;
        this.goodsBarcode = goodsBarcode;
        this.goodsId = goodsId;
        this.saleListTotal = saleListTotal;
        this.count = count;
        this.saleListRemarks = saleListRemarks;
        this.machine_num = machine_num;
        this.saleListActuallyReceived = saleListActuallyReceived;
        this.sale_list_payment = sale_list_payment;
        this.machineTime = machineTime;
        this.type = type;
        this.saleListPayDetail = saleListPayDetail;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public int getSaleListState() {
        return saleListState;
    }

    public void setSaleListState(int saleListState) {
        this.saleListState = saleListState;
    }

    public String getGoodsPurprice() {
        return goodsPurprice;
    }

    public void setGoodsPurprice(String goodsPurprice) {
        this.goodsPurprice = goodsPurprice;
    }

    public String getSaleListDetailPrice() {
        return saleListDetailPrice;
    }

    public void setSaleListDetailPrice(String saleListDetailPrice) {
        this.saleListDetailPrice = saleListDetailPrice;
    }

    public String getGoods_old_price() {
        return goods_old_price;
    }

    public void setGoods_old_price(String goods_old_price) {
        this.goods_old_price = goods_old_price;
    }

    public String getSaleListDetailCount() {
        return saleListDetailCount;
    }

    public void setSaleListDetailCount(String saleListDetailCount) {
        this.saleListDetailCount = saleListDetailCount;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public double getSaleListTotal() {
        return saleListTotal;
    }

    public void setSaleListTotal(double saleListTotal) {
        this.saleListTotal = saleListTotal;
    }

    public double getCount() {
        return count;
    }

    public void setCount(double count) {
        this.count = count;
    }

    public String getSaleListRemarks() {
        return saleListRemarks;
    }

    public void setSaleListRemarks(String saleListRemarks) {
        this.saleListRemarks = saleListRemarks;
    }

    public String getMachine_num() {
        return machine_num;
    }

    public void setMachine_num(String machine_num) {
        this.machine_num = machine_num;
    }

    public double getSaleListActuallyReceived() {
        return saleListActuallyReceived;
    }

    public void setSaleListActuallyReceived(double saleListActuallyReceived) {
        this.saleListActuallyReceived = saleListActuallyReceived;
    }

    public int getSale_list_payment() {
        return sale_list_payment;
    }

    public void setSale_list_payment(int sale_list_payment) {
        this.sale_list_payment = sale_list_payment;
    }

    public String getMachineTime() {
        return machineTime;
    }

    public void setMachineTime(String machineTime) {
        this.machineTime = machineTime;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getSaleListPayDetail() {
        return saleListPayDetail;
    }

    public void setSaleListPayDetail(String saleListPayDetail) {
        this.saleListPayDetail = saleListPayDetail;
    }
}
