package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.OrderListData;

/**
 * Describe:订单列表（适配器）
 * Created by jingang on 2024/5/20
 */
public class OrderQueryAdapter extends BaseAdapter<OrderListData>{

    public OrderQueryAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_order_query;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvTime, tvName, tvNo, tvType, tvTotal, tvCount, tvMobile;
        tvTime = holder.getView(R.id.tvItemTime);
        tvName = holder.getView(R.id.tvItemName);
        tvNo = holder.getView(R.id.tvItemNo);
        tvType = holder.getView(R.id.tvItemType);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvCount = holder.getView(R.id.tvItemCount);
        tvMobile = holder.getView(R.id.tvItemMobile);

//        if (mDataList.get(position).isSelect()) {
//            lin.setBackgroundColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
//            tvTime.setTextColor(mContext.getResources().getColor(R.color.white));
//            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
//            tvNo.setTextColor(mContext.getResources().getColor(R.color.white));
//            tvType.setTextColor(mContext.getResources().getColor(R.color.white));
//            tvTotal.setTextColor(mContext.getResources().getColor(R.color.white));
//            tvCount.setTextColor(mContext.getResources().getColor(R.color.white));
//            tvMobile.setTextColor(mContext.getResources().getColor(R.color.white));
//        } else {
//            tvTime.setTextColor(mContext.getResources().getColor(R.color.black));
//            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
//            tvNo.setTextColor(mContext.getResources().getColor(R.color.black));
//            tvType.setTextColor(mContext.getResources().getColor(R.color.black));
//            tvTotal.setTextColor(mContext.getResources().getColor(R.color.black));
//            tvCount.setTextColor(mContext.getResources().getColor(R.color.black));
//            tvMobile.setTextColor(mContext.getResources().getColor(R.color.black));
//            if (holder.getLayoutPosition() % 2 == 0) {
//                lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
//            } else {
//                lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
//            }
//        }
//
//        if (TextUtils.isEmpty(mDataList.get(position).getSale_list_datetime())) {
//            tvTime.setText("-");
//        } else {
//            tvTime.setText(mDataList.get(position).getSale_list_datetime());
//        }
//        if (TextUtils.isEmpty(mDataList.get(position).getSale_list_name())) {
//            tvName.setText("-");
//        } else {
//            tvName.setText(mDataList.get(position).getSale_list_name());
//        }
//        tvNo.setText(mDataList.get(position).getSale_list_unique());
//        if (TextUtils.isEmpty(mDataList.get(position).getPayMent())) {
//            tvType.setText("-");
//        } else {
//            tvType.setText(mDataList.get(position).getPayMent());
//        }
//        tvTotal.setText(mDataList.get(position).getSale_list_total());
//        tvCount.setText(mDataList.get(position).getSale_list_totalCount());
//        if (TextUtils.isEmpty(mDataList.get(position).getSale_list_phone())) {
//            tvMobile.setText("-");
//        } else {
//            tvMobile.setText(mDataList.get(position).getSale_list_phone());
//        }
    }
}