package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:商城-首页活动列表（实体类）
 * Created by jingang on 2024/6/3
 */
public class MallMarketingData implements Serializable {
    /**
     * msg : 查询成功
     * activity : {"activityImg":"https://file.buyhoo.cc/image/suppliers/brand/01fde91d-eb0b-4602-95f9-ab29c8f7b256.png","id":1,"title":"https://file.buyhoo.cc/image/suppliers/brand/75c67350-5392-4bdd-bf60-ab0a0c8a15ef.png","activityList":[{"goods_name":"古越龙山状元红八年陈酿","price":19.5,"goods_id":1000000414,"goods_img":"/spider/c321791f-54e6-4837-aef2-e842dd4c1fc3"},{"goods_name":"百事可乐1.25L","price":4.2,"goods_id":1000000327,"goods_img":"/spider/08f7c5ba-e7b8-4674-9996-9df20a590da9"},{"goods_name":"今麦郎上品红烧牛肉桶面136g","price":4,"goods_id":1000000756,"goods_img":"/spider/8e4caf19-ccfa-44c0-8566-3f0d0c71dd67.jpg"},{"goods_name":"可口可乐1.25L()","price":9.5,"goods_id":1000000316,"goods_img":"/image/suppliers/GS371306159/2f79467b-4e67-4271-8526-a03e0920cc3b.jpg"},{"goods_name":"可口可乐汽水600ml","price":3.05,"goods_id":1000000762,"goods_img":"/spider/7e97f21c-21f6-4543-9622-497fd30fabce"},{"goods_name":"国庆大促","price":8.5,"goods_id":1000000481,"goods_img":"/image/suppliers/GS371306159/d874c9b9-55b7-4ffd-ae5f-a98666ce3a4d.jpg"}],"activityDesc":"优惠好货大爆料33"}
     * banner : [{"creation_time":"2022-01-19 11:09:05","bann_id":62,"start_time":"2022-01-19 00:00:00","bann_title":"领券中心","banner_type":"3","enable":"1","rich_text":"<p><br><\/p>","end_time":"2028-01-20 23:59:59","shop_type":-1,"bann_image":"/image/suppliers/banner/39aa87a2-08af-46a8-a036-3913f2381139.png","state":"1","disable_type":"1"}]
     * ppjx : [{"goodsbrandDetail_img":"https://file.buyhoo.cc/image/suppliers/brand/6c4cb6b4-c0de-474a-91a6-8044e55d814f.png","goodsbrand_id":2018000003,"goodsbrand_img":"https://file.buyhoo.cc/image/suppliers/brand/9a24354f-6fb2-4716-8575-6d413cf601ce.gif"},{"goodsbrandDetail_img":"https://file.buyhoo.cc/image/suppliers/brand/144a3258-35a8-4ffb-a764-f60544ef2f92.png","goodsbrand_id":2018000063,"goodsbrand_img":"https://file.buyhoo.cc/image/suppliers/brand/401b51bf-b6d3-4ba4-8bbb-9722b2484446.jpg"},{"goodsbrandDetail_img":"https://file.buyhoo.cc/image/GS371307744/mwB.png","goodsbrand_id":2018000069,"goodsbrand_img":"https://file.buyhoo.cc/image/GS371307744/mw.png"},{"goodsbrandDetail_img":"https://file.buyhoo.cc/image/suppliers/brand/ce97b168-70e2-498a-af35-807fc4d63278.png","goodsbrand_id":2018000005,"goodsbrand_img":"https://file.buyhoo.cc/image/suppliers/brand/9ae3b274-fa95-40ca-97a2-6bca5f77df20.png"},{"goodsbrandDetail_img":"https://file.buyhoo.cc/image/GS371307744/jlB.png","goodsbrand_id":2018000071,"goodsbrand_img":"https://file.buyhoo.cc/image/GS371307744/jl.png"},{"goodsbrandDetail_img":"https://file.buyhoo.cc/image/GS371307744/kkklB.png","goodsbrand_id":2018000072,"goodsbrand_img":"https://file.buyhoo.cc/image/GS371307744/kkkl.png"},{"goodsbrandDetail_img":"https://file.buyhoo.cc/image/GS371307744/hnB.png","goodsbrand_id":2018000073,"goodsbrand_img":"https://file.buyhoo.cc/image/GS371307744/hn.png"},{"goodsbrandDetail_img":"https://file.buyhoo.cc/image/GS371307744/flmB.png","goodsbrand_id":2018000074,"goodsbrand_img":"https://file.buyhoo.cc/image/GS371307744/flm.png"}]
     * presale : {"goods_name":"太空椒整袋100斤","gold_deduct":0,"stock_count":1,"group_num":100000,"goods_id":1000000805,"start_order":1,"is_binding":0,"goodsunit_name":"斤","title":"https://file.buyhoo.cc/image/GS371307744/yhbt.png","activityDesc":"优惠好货早知道","book_num":0,"goods_barcode":"1671760891242","online_price":0.01,"company_name":"测试陈少明","wholesale_price":0.01,"is_activity":7,"company_code":"GS371301766","activity_price":"0.02","auto_fxiaoshou":1,"goods_img":"/image/suppliers/GS371301766/a16c8615-e9c2-4f7f-a237-a86101554be0.jpg"}
     * status : 1
     */

    private String msg;
    private ActivityBean activity;//硬核补贴
    private PresaleData presale;//预售
    private int status;
    private List<BannerBean> banner;
    private List<PpjxBean> ppjx;//品牌精选

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public ActivityBean getActivity() {
        return activity;
    }

    public void setActivity(ActivityBean activity) {
        this.activity = activity;
    }

    public PresaleData getPresale() {
        return presale;
    }

    public void setPresale(PresaleData presale) {
        this.presale = presale;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public List<BannerBean> getBanner() {
        return banner;
    }

    public void setBanner(List<BannerBean> banner) {
        this.banner = banner;
    }

    public List<PpjxBean> getPpjx() {
        return ppjx;
    }

    public void setPpjx(List<PpjxBean> ppjx) {
        this.ppjx = ppjx;
    }

    public static class ActivityBean {
        /**
         * activityImg : https://file.buyhoo.cc/image/suppliers/brand/01fde91d-eb0b-4602-95f9-ab29c8f7b256.png
         * id : 1
         * title : https://file.buyhoo.cc/image/suppliers/brand/75c67350-5392-4bdd-bf60-ab0a0c8a15ef.png
         * activityList : [{"goods_name":"古越龙山状元红八年陈酿","price":19.5,"goods_id":1000000414,"goods_img":"/spider/c321791f-54e6-4837-aef2-e842dd4c1fc3"},{"goods_name":"百事可乐1.25L","price":4.2,"goods_id":1000000327,"goods_img":"/spider/08f7c5ba-e7b8-4674-9996-9df20a590da9"},{"goods_name":"今麦郎上品红烧牛肉桶面136g","price":4,"goods_id":1000000756,"goods_img":"/spider/8e4caf19-ccfa-44c0-8566-3f0d0c71dd67.jpg"},{"goods_name":"可口可乐1.25L()","price":9.5,"goods_id":1000000316,"goods_img":"/image/suppliers/GS371306159/2f79467b-4e67-4271-8526-a03e0920cc3b.jpg"},{"goods_name":"可口可乐汽水600ml","price":3.05,"goods_id":1000000762,"goods_img":"/spider/7e97f21c-21f6-4543-9622-497fd30fabce"},{"goods_name":"国庆大促","price":8.5,"goods_id":1000000481,"goods_img":"/image/suppliers/GS371306159/d874c9b9-55b7-4ffd-ae5f-a98666ce3a4d.jpg"}]
         * activityDesc : 优惠好货大爆料33
         */

        private String activityImg;
        private int id;
        private String title;
        private String activityDesc;
        private List<ActivityListBean> activityList;

        public String getActivityImg() {
            return activityImg;
        }

        public void setActivityImg(String activityImg) {
            this.activityImg = activityImg;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getActivityDesc() {
            return activityDesc;
        }

        public void setActivityDesc(String activityDesc) {
            this.activityDesc = activityDesc;
        }

        public List<ActivityListBean> getActivityList() {
            return activityList;
        }

        public void setActivityList(List<ActivityListBean> activityList) {
            this.activityList = activityList;
        }

        public static class ActivityListBean {
            /**
             * goods_name : 古越龙山状元红八年陈酿
             * price : 19.5
             * goods_id : 1000000414
             * goods_img : /spider/c321791f-54e6-4837-aef2-e842dd4c1fc3
             */

            private String goods_name;
            private double price;
            private int goods_id;
            private String goods_img;

            public String getGoods_name() {
                return goods_name;
            }

            public void setGoods_name(String goods_name) {
                this.goods_name = goods_name;
            }

            public double getPrice() {
                return price;
            }

            public void setPrice(double price) {
                this.price = price;
            }

            public int getGoods_id() {
                return goods_id;
            }

            public void setGoods_id(int goods_id) {
                this.goods_id = goods_id;
            }

            public String getGoods_img() {
                return goods_img;
            }

            public void setGoods_img(String goods_img) {
                this.goods_img = goods_img;
            }
        }
    }

    public class PresaleData {
        /**
         * goods_name : 太空椒整袋100斤
         * gold_deduct : 0.0
         * stock_count : 1
         * group_num : 100000
         * goods_id : 1000000805
         * start_order : 1
         * is_binding : 0
         * goodsunit_name : 斤
         * title : https://file.buyhoo.cc/image/GS371307744/yhbt.png
         * activityDesc : 优惠好货早知道
         * book_num : 0
         * goods_barcode : 1671760891242
         * online_price : 0.01
         * company_name : 测试陈少明
         * wholesale_price : 0.01
         * is_activity : 7
         * company_code : GS371301766
         * activity_price : 0.02
         * auto_fxiaoshou : 1
         * goods_img : /image/suppliers/GS371301766/a16c8615-e9c2-4f7f-a237-a86101554be0.jpg
         */

        private String goods_name;
        private double gold_deduct;
        private int stock_count;
        private int group_num;
        private int goods_id;
        private int start_order;
        private int is_binding;
        private String goodsunit_name;
        private String title;
        private String activityDesc;
        private int book_num;
        private String goods_barcode;
        private String online_price;
        private String company_name;
        private double wholesale_price;
        private int is_activity;
        private String company_code;
        private String activity_price;
        private int auto_fxiaoshou;
        private String goods_img;

        private String start_time;
        private long startTimeLong;
        private String end_time;
        private long endTimeLong;

        public String getGoods_name() {
            return goods_name;
        }

        public void setGoods_name(String goods_name) {
            this.goods_name = goods_name;
        }

        public double getGold_deduct() {
            return gold_deduct;
        }

        public void setGold_deduct(double gold_deduct) {
            this.gold_deduct = gold_deduct;
        }

        public int getStock_count() {
            return stock_count;
        }

        public void setStock_count(int stock_count) {
            this.stock_count = stock_count;
        }

        public int getGroup_num() {
            return group_num;
        }

        public void setGroup_num(int group_num) {
            this.group_num = group_num;
        }

        public int getGoods_id() {
            return goods_id;
        }

        public void setGoods_id(int goods_id) {
            this.goods_id = goods_id;
        }

        public int getStart_order() {
            return start_order;
        }

        public void setStart_order(int start_order) {
            this.start_order = start_order;
        }

        public int getIs_binding() {
            return is_binding;
        }

        public void setIs_binding(int is_binding) {
            this.is_binding = is_binding;
        }

        public String getGoodsunit_name() {
            return goodsunit_name;
        }

        public void setGoodsunit_name(String goodsunit_name) {
            this.goodsunit_name = goodsunit_name;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getActivityDesc() {
            return activityDesc;
        }

        public void setActivityDesc(String activityDesc) {
            this.activityDesc = activityDesc;
        }

        public int getBook_num() {
            return book_num;
        }

        public void setBook_num(int book_num) {
            this.book_num = book_num;
        }

        public String getGoods_barcode() {
            return goods_barcode;
        }

        public void setGoods_barcode(String goods_barcode) {
            this.goods_barcode = goods_barcode;
        }

        public String getOnline_price() {
            return online_price;
        }

        public void setOnline_price(String online_price) {
            this.online_price = online_price;
        }

        public String getCompany_name() {
            return company_name;
        }

        public void setCompany_name(String company_name) {
            this.company_name = company_name;
        }

        public double getWholesale_price() {
            return wholesale_price;
        }

        public void setWholesale_price(double wholesale_price) {
            this.wholesale_price = wholesale_price;
        }

        public int getIs_activity() {
            return is_activity;
        }

        public void setIs_activity(int is_activity) {
            this.is_activity = is_activity;
        }

        public String getCompany_code() {
            return company_code;
        }

        public void setCompany_code(String company_code) {
            this.company_code = company_code;
        }

        public String getActivity_price() {
            return activity_price;
        }

        public void setActivity_price(String activity_price) {
            this.activity_price = activity_price;
        }

        public int getAuto_fxiaoshou() {
            return auto_fxiaoshou;
        }

        public void setAuto_fxiaoshou(int auto_fxiaoshou) {
            this.auto_fxiaoshou = auto_fxiaoshou;
        }

        public String getGoods_img() {
            return goods_img;
        }

        public void setGoods_img(String goods_img) {
            this.goods_img = goods_img;
        }

        public String getStart_time() {
            return start_time;
        }

        public void setStart_time(String start_time) {
            this.start_time = start_time;
        }

        public long getStartTimeLong() {
            return startTimeLong;
        }

        public void setStartTimeLong(long startTimeLong) {
            this.startTimeLong = startTimeLong;
        }

        public String getEnd_time() {
            return end_time;
        }

        public void setEnd_time(String end_time) {
            this.end_time = end_time;
        }

        public long getEndTimeLong() {
            return endTimeLong;
        }

        public void setEndTimeLong(long endTimeLong) {
            this.endTimeLong = endTimeLong;
        }
    }

    public static class BannerBean {
        /**
         * creation_time : 2022-01-19 11:09:05
         * bann_id : 62
         * start_time : 2022-01-19 00:00:00
         * bann_title : 领券中心
         * banner_type : 3
         * enable : 1
         * rich_text : <p><br></p>
         * end_time : 2028-01-20 23:59:59
         * shop_type : -1
         * bann_image : /image/suppliers/banner/39aa87a2-08af-46a8-a036-3913f2381139.png
         * state : 1
         * disable_type : 1
         */

        private String creation_time;
        private int bann_id;
        private String start_time;
        private String bann_title;
        private String banner_type;
        private String enable;
        private String rich_text;
        private String end_time;
        private int shop_type;
        private String bann_image;
        private String state;
        private String disable_type;
        private String banner_code;

        public String getCreation_time() {
            return creation_time;
        }

        public void setCreation_time(String creation_time) {
            this.creation_time = creation_time;
        }

        public int getBann_id() {
            return bann_id;
        }

        public void setBann_id(int bann_id) {
            this.bann_id = bann_id;
        }

        public String getStart_time() {
            return start_time;
        }

        public void setStart_time(String start_time) {
            this.start_time = start_time;
        }

        public String getBann_title() {
            return bann_title;
        }

        public void setBann_title(String bann_title) {
            this.bann_title = bann_title;
        }

        public String getBanner_type() {
            return banner_type;
        }

        public void setBanner_type(String banner_type) {
            this.banner_type = banner_type;
        }

        public String getEnable() {
            return enable;
        }

        public void setEnable(String enable) {
            this.enable = enable;
        }

        public String getRich_text() {
            return rich_text;
        }

        public void setRich_text(String rich_text) {
            this.rich_text = rich_text;
        }

        public String getEnd_time() {
            return end_time;
        }

        public void setEnd_time(String end_time) {
            this.end_time = end_time;
        }

        public int getShop_type() {
            return shop_type;
        }

        public void setShop_type(int shop_type) {
            this.shop_type = shop_type;
        }

        public String getBann_image() {
            return bann_image;
        }

        public void setBann_image(String bann_image) {
            this.bann_image = bann_image;
        }

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public String getDisable_type() {
            return disable_type;
        }

        public void setDisable_type(String disable_type) {
            this.disable_type = disable_type;
        }

        public String getBanner_code() {
            return banner_code;
        }

        public void setBanner_code(String banner_code) {
            this.banner_code = banner_code;
        }
    }

    public static class PpjxBean {
        /**
         * goodsbrandDetail_img : https://file.buyhoo.cc/image/suppliers/brand/6c4cb6b4-c0de-474a-91a6-8044e55d814f.png
         * goodsbrand_id : 2018000003
         * goodsbrand_img : https://file.buyhoo.cc/image/suppliers/brand/9a24354f-6fb2-4716-8575-6d413cf601ce.gif
         */

        private String goodsbrandDetail_img;
        private int goodsbrand_id;
        private String goodsbrand_img;

        public String getGoodsbrandDetail_img() {
            return goodsbrandDetail_img;
        }

        public void setGoodsbrandDetail_img(String goodsbrandDetail_img) {
            this.goodsbrandDetail_img = goodsbrandDetail_img;
        }

        public int getGoodsbrand_id() {
            return goodsbrand_id;
        }

        public void setGoodsbrand_id(int goodsbrand_id) {
            this.goodsbrand_id = goodsbrand_id;
        }

        public String getGoodsbrand_img() {
            return goodsbrand_img;
        }

        public void setGoodsbrand_img(String goodsbrand_img) {
            this.goodsbrand_img = goodsbrand_img;
        }
    }
}
