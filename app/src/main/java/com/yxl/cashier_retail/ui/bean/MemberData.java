package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;

/**
 * Describe:会员列表（实体类）
 * Created by jingang on 2024/5/11
 */
public class MemberData implements Serializable {
    /**
     * cusId : 436958
     * cusUnique : 112233445566
     * cusName : 日志测试
     * cusPhone : 13512345678
     * cusSex : 1
     * cusBalance : 0.0
     * cusRebate : 0.0
     * totalBalance : 0.0
     * cusLevelId : 1549
     * cusLevelVal : 1
     * cusLevelName : 铜牌会员
     * cusLevelPoints : 589
     * cusRemark : null
     * creditLimit : 0.0
     */

    private boolean select;
    private int cusId;
    private int ccusId;//消费记录接口查询是使用该值
    private String cusUnique;
    private String cusName;
    private String cusPhone;
    private int cusSex;//1.男 2.女
    private double cusBalance;//余额
    private double cusRebate;//宁宇会员赠送余额
    private double totalBalance;//总余额
    private int cusLevelId;//会员等级id
    private int cusLevelVal;//会员等级 1.铜牌 2.白银 3.黄金 4.钻石
    private String cusLevelName;
    private double cusPoints;//会员积分
    private String cusRemark;//备注
    private double creditLimit;//欠款限额

    public MemberData() {
    }

    public MemberData(String cusUnique) {
        this.cusUnique = cusUnique;
    }

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }

    public int getCusId() {
        return cusId;
    }

    public void setCusId(int cusId) {
        this.cusId = cusId;
    }

    public int getCcusId() {
        return ccusId;
    }

    public void setCcusId(int ccusId) {
        this.ccusId = ccusId;
    }

    public String getCusUnique() {
        return cusUnique;
    }

    public void setCusUnique(String cusUnique) {
        this.cusUnique = cusUnique;
    }

    public String getCusName() {
        return cusName;
    }

    public void setCusName(String cusName) {
        this.cusName = cusName;
    }

    public String getCusPhone() {
        return cusPhone;
    }

    public void setCusPhone(String cusPhone) {
        this.cusPhone = cusPhone;
    }

    public int getCusSex() {
        return cusSex;
    }

    public void setCusSex(int cusSex) {
        this.cusSex = cusSex;
    }

    public double getCusBalance() {
        return cusBalance;
    }

    public void setCusBalance(double cusBalance) {
        this.cusBalance = cusBalance;
    }

    public double getCusRebate() {
        return cusRebate;
    }

    public void setCusRebate(double cusRebate) {
        this.cusRebate = cusRebate;
    }

    public double getTotalBalance() {
        return totalBalance;
    }

    public void setTotalBalance(double totalBalance) {
        this.totalBalance = totalBalance;
    }

    public int getCusLevelId() {
        return cusLevelId;
    }

    public void setCusLevelId(int cusLevelId) {
        this.cusLevelId = cusLevelId;
    }

    public int getCusLevelVal() {
        return cusLevelVal;
    }

    public void setCusLevelVal(int cusLevelVal) {
        this.cusLevelVal = cusLevelVal;
    }

    public String getCusLevelName() {
        return cusLevelName;
    }

    public void setCusLevelName(String cusLevelName) {
        this.cusLevelName = cusLevelName;
    }

    public double getCusPoints() {
        return cusPoints;
    }

    public void setCusPoints(double cusPoints) {
        this.cusPoints = cusPoints;
    }

    public String getCusRemark() {
        return cusRemark;
    }

    public void setCusRemark(String cusRemark) {
        this.cusRemark = cusRemark;
    }

    public double getCreditLimit() {
        return creditLimit;
    }

    public void setCreditLimit(double creditLimit) {
        this.creditLimit = creditLimit;
    }
}
