package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmInGoodsBinding;
import com.yxl.cashier_retail.ui.activity.GoodsEditActivity;
import com.yxl.cashier_retail.ui.adapter.GoodsAdapter;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.ui.dialog.CateDialog;

import org.litepal.LitePal;

import java.util.ArrayList;
import java.util.List;

/**
 * Describe:入库-商品管理
 * Created by jingang on 2024/6/29
 */
@SuppressLint("NonConstantResourceId")
public class InGoodsFragment extends BaseFragment<FmInGoodsBinding> implements View.OnClickListener {
    private String keyWords;
    private String cateUnique,//一级分类编号
            cateChildUnique;//二级分类编号

    private GoodsAdapter mAdapter;
    private List<GoodsData> dataList = new ArrayList<>();

    @Override
    protected FmInGoodsBinding getViewBinding() {
        return FmInGoodsBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.linCate0.setOnClickListener(this);
        mBinding.linCate1.setOnClickListener(this);
        mBinding.tvSyn.setOnClickListener(this);
        mBinding.tvAdd.setOnClickListener(this);

        setAdapter();
    }

    @Override
    protected void initData() {
        mBinding.smartRefreshLayout.autoRefresh();
    }

    @Override
    public void onClick(View v) {
        if (isQuicklyClick()) {
            return;
        }
        switch (v.getId()) {
            case R.id.linCate0:
                //选择一级分类
                CateDialog.showDialog(getActivity(), cateUnique, cateChildUnique, mBinding.ivCate0, 1, (unique, name, childUnique, childName) -> {
                    cateUnique = unique;
                    cateChildUnique = childUnique;
                    mBinding.tvCate0.setText(name);
                    mBinding.tvCate1.setText(childName);
                    mBinding.smartRefreshLayout.autoRefresh();
                });
                break;
            case R.id.linCate1:
                //选择二级分类
                CateDialog.showDialog(getActivity(), cateUnique, cateChildUnique, mBinding.ivCate1, 1, (unique, name, childUnique, childName) -> {
                    cateUnique = unique;
                    cateChildUnique = childUnique;
                    mBinding.tvCate0.setText(name);
                    mBinding.tvCate1.setText(childName);
                    mBinding.smartRefreshLayout.autoRefresh();
                });
                break;
            case R.id.tvSyn:
                //下载到秤
                showToast(1, "暂未开发");
                break;
            case R.id.tvAdd:
                //新增商品
                goToActivity(GoodsEditActivity.class);
                break;
        }
    }

    /**
     * 搜索
     *
     * @param keyWords
     */
    public void setKeyWords(String keyWords) {
        if (getActivity() == null) {
            return;
        }
        this.keyWords = keyWords;
        mBinding.smartRefreshLayout.autoRefresh();
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new GoodsAdapter(getActivity());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            startActivity(new Intent(getActivity(), GoodsEditActivity.class)
                    .putExtra("barcode", dataList.get(position).getGoods_barcode())
            );
        });
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                getGoodsList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                max = 0;
                getGoodsList();
            }
        });
    }

    /**
     * 商品列表
     */
    private void getGoodsList() {
        mBinding.smartRefreshLayout.finishRefresh();
        mBinding.smartRefreshLayout.finishLoadMore();
        List<GoodsData> list;
        if (TextUtils.isEmpty(cateUnique)) {
            if (TextUtils.isEmpty(cateChildUnique)) {
                if (TextUtils.isEmpty(keyWords)) {
                    list = LitePal
                            .limit(30)
                            .offset(max)
                            .find(GoodsData.class);
                } else {
                    list = LitePal
                            .limit(30)
                            .offset(max)
                            .where("goods_name like ? or goods_barcode like ?", "%" + keyWords + "%", "%" + keyWords + "%")
                            .find(GoodsData.class);
                }
            } else {
                if (TextUtils.isEmpty(keyWords)) {
                    list = LitePal
                            .limit(30)
                            .offset(max)
                            .where("goods_kind_unique = ?", cateChildUnique)
                            .find(GoodsData.class);
                } else {
                    list = LitePal
                            .limit(30)
                            .offset(max)
                            .where("goods_kind_unique = ? and goods_name like ? or goods_barcode like ?", cateChildUnique, "%" + keyWords + "%", "%" + keyWords + "%")
                            .find(GoodsData.class);
                }
            }
        } else {
            if (TextUtils.isEmpty(cateChildUnique)) {
                if (TextUtils.isEmpty(keyWords)) {
                    list = LitePal
                            .limit(30)
                            .offset(max)
                            .where("goods_kind_parunique = ?", cateUnique)
                            .find(GoodsData.class);
                } else {
                    list = LitePal
                            .limit(30)
                            .offset(max)
                            .where("goods_kind_parunique = ? and goods_name like ? or goods_barcode like ?", cateUnique, "%" + keyWords + "%", "%" + keyWords + "%")
                            .find(GoodsData.class);
                }
            } else {
                if (TextUtils.isEmpty(keyWords)) {
                    list = LitePal
                            .limit(30)
                            .offset(max)
                            .where("goods_kind_parunique = ? and goods_kind_unique = ?", cateUnique, cateChildUnique)
                            .find(GoodsData.class);
                } else {
                    list = LitePal
                            .limit(30)
                            .offset(max)
                            .where("goods_kind_parunique = ? and goods_kind_unique = ? and goods_name like ? or goods_barcode like ?", cateUnique, cateChildUnique, "%" + keyWords + "%", "%" + keyWords + "%")
                            .find(GoodsData.class);
                }
            }
        }
        if (list != null) {
            if (max == 0) {
                dataList.clear();
            }
            dataList.addAll(list);
            max += dataList.size();
        }
        if (!dataList.isEmpty()) {
            mBinding.recyclerView.setVisibility(View.VISIBLE);
            mBinding.linEmpty.setVisibility(View.GONE);
            mAdapter.setDataList(dataList);
        } else {
            mBinding.recyclerView.setVisibility(View.GONE);
            mBinding.linEmpty.setVisibility(View.VISIBLE);
        }
    }
}
