package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;
import android.graphics.Typeface;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;

import com.alibaba.fastjson.JSONArray;
import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.MyApplication;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmSettingPartsBinding;
import com.yxl.cashier_retail.ui.activity.TagsCustomActivity;
import com.yxl.cashier_retail.ui.adapter.ScaleAdapter;
import com.yxl.cashier_retail.ui.bean.ConditionData;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.ScaleData;
import com.yxl.cashier_retail.ui.dialog.ReceiptPrintWifiDialog;
import com.yxl.cashier_retail.ui.dialog.ScaleAddDialog;
import com.yxl.cashier_retail.ui.popupwindow.ConditionPop;
import com.yxl.cashier_retail.utils.ByteUtils;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.utils.DateUtils;
import com.yxl.cashier_retail.utils.PermissionUtils;
import com.yxl.cashier_retail.utils.PrintReceiptUsbUtils;
import com.yxl.cashier_retail.utils.ZxingUtils;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.List;

/**
 * Describe:设置-配件设置
 * Created by jingang on 2024/5/15
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class SettingPartsFragment extends BaseFragment<FmSettingPartsBinding> implements View.OnClickListener {
    private int index,//0.小票 1.价签 2.称重 3.摄像头 4.其他
            currentIndex,
            receiptPrintType,//小票打印机连接方式 1.蓝牙 2.端口 3.USB 4.WIFI
            printCount,//
            weightSerialPort,//称重设置：端口号 1./dev/ttyS3
            BAUDId,//波特率 0.2400 1.4800 2.9600 3.115200
            dataParsingType;//数据解析方式 0.方式1 1.方式2 2.方式3 3.方式4
    private boolean isReceiptPrinterConnect,//小票打印机是否连接
            isUseCateringReceipt,//是否使用餐饮小票
            isUseSerialPortScale,//启用串口秤
            isUsePos,//启用POS收银
            isUseSecondaryScreen,//启用副屏
            isUseBeforePayBox;//
    private String receiptTips,//小票：温馨提示
            receiptRemarks,//小票：备注
            posId;//pos设备id

    private final List<ConditionData> receiptPrintTypeList = new ArrayList<>(),//小票打印机连接方式
            weightSerialPortList = new ArrayList<>(),//称重设置：端口号
            BAUDList = new ArrayList<>(),//波特率
            dataParsingTypeList = new ArrayList<>();//数据解析方式

    //秤列表
    private ScaleAdapter scaleAdapter;
    private List<ScaleData> scaleList = new ArrayList<>();

    @Override
    protected FmSettingPartsBinding getViewBinding() {
        return FmSettingPartsBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.tvReceipt.setOnClickListener(this);
        mBinding.tvTags.setOnClickListener(this);
        mBinding.tvWeight.setOnClickListener(this);
        mBinding.tvCamera.setOnClickListener(this);
        mBinding.tvPos.setOnClickListener(this);
        mBinding.tvOther.setOnClickListener(this);

        //小票设置
        mBinding.linReceiptPrint.setOnClickListener(this);
        mBinding.ivReceiptCatering.setOnClickListener(this);
        mBinding.tvReceiptCateringReset.setOnClickListener(this);
        mBinding.ivReceiptPrintCountSub.setOnClickListener(this);
        mBinding.ivReceiptPrintCountAdd.setOnClickListener(this);
        mBinding.tvReceiptPrintTest.setOnClickListener(this);
        mBinding.etReceiptTips.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                receiptTips = s.toString().trim();
                mBinding.tvReceiptTipsCount.setText("（" + receiptTips.length() + "/50)");
                mBinding.tvReceiptTips.setText(getRstr(R.string.tips_warm_colon) + receiptTips);
            }
        });
        mBinding.etReceiptTips.setOnEditorActionListener((v, actionId, event) -> {
            SPUtils.getInstance().put(Constants.RECEIPT_TIPS, receiptTips);
            hideSoftInput(getActivity());
            return true;
        });
        mBinding.etReceiptRemarks.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                receiptRemarks = s.toString().trim();
                mBinding.tvRemarksCount.setText("(" + receiptRemarks.length() + "/50)");
                mBinding.tvReceiptRemarks.setText(receiptRemarks);
            }
        });
        mBinding.etReceiptRemarks.setOnEditorActionListener((v, actionId, event) -> {
            if (TextUtils.isEmpty(receiptRemarks)) {
                showToast(1, getRstr(R.string.input_remarks_info));
            } else {
                SPUtils.getInstance().put(Constants.RECEIPT_REMARKS, receiptRemarks);
                hideSoftInput(getActivity());
            }
            return true;
        });
        //价签设置
        mBinding.linTagsPrint.setOnClickListener(this);
        mBinding.linTagsTemplate.setOnClickListener(this);
        mBinding.linTagsType0.setOnClickListener(this);
        mBinding.linTagsType1.setOnClickListener(this);
        mBinding.linTagsType2.setOnClickListener(this);
        mBinding.linTagsType3.setOnClickListener(this);
        mBinding.tvTagsPrintTest.setOnClickListener(this);
        //称重设置
        mBinding.ivWeightEnable.setOnClickListener(this);
        mBinding.linWeightPort.setOnClickListener(this);
        mBinding.linWeightBAUD.setOnClickListener(this);
        mBinding.linWeightDataParsingType.setOnClickListener(this);
        mBinding.tvWeightAdd.setOnClickListener(this);
        mBinding.tvWeightDownload.setOnClickListener(this);
        //POS设置
        mBinding.ivPosEnable.setOnClickListener(this);
        mBinding.etPosId.setOnEditorActionListener((v, actionId, event) -> {
            posId = v.getText().toString().trim();
            SPUtils.getInstance().put(Constants.POS_ID, posId);
            hideSoftInput(getActivity());
            return true;
        });
        //其他设置
        mBinding.ivOtherEnable.setOnClickListener(this);
        mBinding.ivOtherEnableBox.setOnClickListener(this);
        mBinding.linOtherBox.setOnClickListener(this);
        mBinding.linOtherTTSEngine.setOnClickListener(this);
        mBinding.linOtherTTSLanguage.setOnClickListener(this);
        mBinding.linOtherTTSVoice.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected void initData() {
        setUI();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvReceipt:
                //小票设置
                index = 0;
                fragmentControl();
                break;
            case R.id.tvTags:
                //价签设置
                index = 1;
                fragmentControl();
                break;
            case R.id.tvWeight:
                //称重设置
                index = 2;
                fragmentControl();
                break;
            case R.id.tvCamera:
                //摄像头设置
                index = 3;
                fragmentControl();
                break;
            case R.id.tvPos:
                //POS设置
                index = 4;
                fragmentControl();
                break;
            case R.id.tvOther:
                //其他设置
                index = 5;
                fragmentControl();
                break;
            /*小票设置start*/
            case R.id.linReceiptPrint:
                //选择打印机连接方式
                ConditionPop.showDialog(getActivity(),
                        mBinding.ivReceiptPrint,
                        mBinding.linReceiptPrint,
                        mBinding.linReceiptPrint.getMeasuredWidth(),
                        receiptPrintTypeList,
                        receiptPrintType,
                        data -> {
                            receiptPrintType = data.getId();
                            mBinding.tvReceiptPrint.setText(data.getName());
                            SPUtils.getInstance().put(Constants.RECEIPT_PRINTER_CONNECT_TYPE, data.getId());
                            switch (receiptPrintType) {
                                case 3:
                                    MyApplication.getInstance().connectReceiptPrinter();
                                    break;
                                case 4:
                                    ReceiptPrintWifiDialog.showDialog(getActivity(), ip -> {
                                        MyApplication.getInstance().connectNet(ip);
                                    });
                                    break;
                            }
                        });
                break;
            case R.id.ivReceiptCatering:
                //是否使用餐饮小票
                SPUtils.getInstance().put(Constants.IS_USE_CATERING_RECEIPT, isUseCateringReceipt ? "" : Constants.IS_USE_CATERING_RECEIPT);
                isUseCateringReceipt = !isUseCateringReceipt;
                mBinding.ivReceiptCatering.setSelected(isUseCateringReceipt);
                mBinding.linReceiptCatering.setVisibility(isUseCateringReceipt ? View.VISIBLE : View.GONE);
                break;
            case R.id.tvReceiptCateringReset:
                //重置餐饮小票计数
                break;
            case R.id.ivReceiptPrintCountSub:
                //默认打印数量-减少
                if (printCount > 1) {
                    printCount--;
                    SPUtils.getInstance().put(Constants.DEFAULT_PRINT_COUNT, printCount);
                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.DEFAULT_PRINT_COUNT));
                }
                break;
            case R.id.ivReceiptPrintCountAdd:
                //默认打印数量-增加
                if (printCount < 100) {
                    printCount++;
                    SPUtils.getInstance().put(Constants.DEFAULT_PRINT_COUNT, printCount);
                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.DEFAULT_PRINT_COUNT));
                }
                break;
            case R.id.tvReceiptPrintTest:
                //打印测试
                if (!isReceiptPrinterConnect) {
                    showToast(1, getRstr(R.string.printer_no_connect));
                    return;
                }
                Bitmap bitmap = ZxingUtils.createBarcode("123456", 614, 100);
                PrintReceiptUsbUtils.printPayment(getActivity(),
                        new ArrayList<>(), 0.00, 0.00, 0.00,
                        new JSONArray(),
                        null, 0, 0,
                        "123456", bitmap);
                break;
            /*小票设置end*/
            /*价签设置start*/
            case R.id.linTagsPrint:
                //选择打印机连接方式
                break;
            case R.id.linTagsTemplate:
                //自定义价签模版
                goToActivity(TagsCustomActivity.class);
                break;
            case R.id.linTagsType0:
                //打印模版0
                break;
            case R.id.linTagsType1:
                //打印模版1
                break;
            case R.id.linTagsType2:
                //打印模版2
                break;
            case R.id.linTagsType3:
                //打印模版3
                break;
            case R.id.tvTagsPrintTest:
                //打印测试
                break;
            /*价签设置end*/
            /*称重设置start*/
            case R.id.ivWeightEnable:
                //启用串口称
                SPUtils.getInstance().put(Constants.IS_USE_SERIAL_PORT_SCALE, isUseSerialPortScale ? Constants.IS_USE_SERIAL_PORT_SCALE : "");
                isUseSerialPortScale = !isUseSerialPortScale;
                mBinding.ivWeightEnable.setSelected(isUseSerialPortScale);
                checkPermissionSerialPort();
                break;
            case R.id.linWeightPort:
                //选择端口号
                ConditionPop.showDialog(getActivity(),
                        mBinding.ivWeightPort,
                        mBinding.linWeightPort,
                        mBinding.linWeightPort.getMeasuredWidth(),
                        weightSerialPortList,
                        weightSerialPort,
                        data -> {
                            weightSerialPort = data.getId();
                            mBinding.tvWeightPort.setText(data.getName());
                            SPUtils.getInstance().put(Constants.WEIGHT_SERIAL_PORT, data.getId());
                            checkPermissionSerialPort();
                        });
                break;
            case R.id.linWeightBAUD:
                //选择波特率
                ConditionPop.showDialog(getActivity(),
                        mBinding.ivWeightBAUD,
                        mBinding.linWeightBAUD,
                        mBinding.linWeightBAUD.getMeasuredWidth(),
                        BAUDList,
                        BAUDId,
                        data -> {
                            BAUDId = data.getId();
                            mBinding.tvWeightBAUD.setText(data.getName());
                            SPUtils.getInstance().put(Constants.BAUD, data.getId());
                            checkPermissionSerialPort();
                        });
                break;
            case R.id.linWeightDataParsingType:
                //选择数据解析方式
                ConditionPop.showDialog(getActivity(),
                        mBinding.ivWeightDataParsingType,
                        mBinding.linWeightDataParsingType,
                        mBinding.linWeightDataParsingType.getMeasuredWidth(),
                        dataParsingTypeList,
                        dataParsingType,
                        data -> {
                            dataParsingType = data.getId();
                            mBinding.tvWeightDataParsingType.setText(data.getName());
                            SPUtils.getInstance().put(Constants.DATA_PARSING_TYPE, data.getId());
                        });
                break;
            case R.id.tvWeightAdd:
                //新增条码秤
                ScaleAddDialog.showDialog(getActivity(), name -> {
                    showToast(0, name);
                });
                break;
            case R.id.tvWeightDownload:
                //下载到秤
                showToast(1, "暂未开发");
                break;
            /*称重设置end*/
            case R.id.ivPosEnable:
                //启用POS收银
                SPUtils.getInstance().put(Constants.IS_USE_POS, isUsePos ? "" : Constants.IS_USE_POS);
                isUsePos = !isUsePos;
                mBinding.ivPosEnable.setSelected(isUsePos);
                break;
            /*其他设置start*/
            case R.id.ivOtherEnable:
                //启用副屏
                SPUtils.getInstance().put(Constants.IS_USE_SECONDARY_SCREEN, isUseSecondaryScreen ? Constants.IS_USE_SECONDARY_SCREEN : "");
                isUseSecondaryScreen = !isUseSecondaryScreen;
                mBinding.ivOtherEnable.setSelected(isUseSecondaryScreen);
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.IS_USE_SECONDARY_SCREEN));
                break;
            case R.id.ivOtherEnableBox:
                //支付前弹钱箱
                Log.e(tag, "isUseBeforePayBox = " + isUseBeforePayBox);
                SPUtils.getInstance().put(Constants.IS_USE_BEFORE_PAY_BOX, isUseBeforePayBox ? Constants.IS_USE_BEFORE_PAY_BOX : "");
                isUseBeforePayBox = !isUseBeforePayBox;
                mBinding.ivOtherEnableBox.setSelected(isUseBeforePayBox);
                break;
            case R.id.linOtherBox:
                //选择钱箱打印机
                break;
            case R.id.linOtherTTSEngine:
                //选择TTS引擎
                break;
            case R.id.linOtherTTSLanguage:
                //选择TTS语言
                break;
            case R.id.linOtherTTSVoice:
                //选择TTS声音
                break;
            /*其他设置end*/
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        String msg = event.getMsg();
        if (TextUtils.isEmpty(msg)) {
            return;
        }
        switch (msg) {
            case Constants.XPRINTER:
                //小票打印机状态监听 1.连接成功 2.连接失败 3.发送失败 4.连接中断 5.USB设备已连上 6.USB设备已断开
                switch (event.getNum()) {
                    case 1:
                        isReceiptPrinterConnect = true;
                        mBinding.vReceiptPrinterStatus.setBackgroundResource(R.drawable.shape_yuan_green);
                        mBinding.tvReceiptPrinterStatus.setText(getRstr(R.string.connected));
                        mBinding.tvReceiptPrinterStatus.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                        break;
                    case 3:
                        showToast(1, getRstr(R.string.send_fail));
                        break;
                    case 5:
                        isReceiptPrinterConnect = true;
                        mBinding.vReceiptPrinterStatus.setBackgroundResource(R.drawable.shape_yuan_green);
                        mBinding.tvReceiptPrinterStatus.setText(getRstr(R.string.connected));
                        mBinding.tvReceiptPrinterStatus.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                        break;
                    default:
                        isReceiptPrinterConnect = false;
                        mBinding.vReceiptPrinterStatus.setBackgroundResource(R.drawable.shape_yuan_red);
                        mBinding.tvReceiptPrinterStatus.setText(getRstr(R.string.connect_no));
                        mBinding.tvReceiptPrinterStatus.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.red));
                        break;
                }
                if (isReceiptPrinterConnect) {
                    showToast(0, getRstr(R.string.printer_connect_success));
                } else {
                    showToast(1, getRstr(R.string.printer_connect_fail));
                }
                break;
            case Constants.DEFAULT_PRINT_COUNT:
                //默认打印数量
                mBinding.tvReceiptPrintCount.setText(String.valueOf(SPUtils.getInstance().getInt(Constants.DEFAULT_PRINT_COUNT, 1)));
                break;
            case Constants.SERIALPORT:
                //串口数据
                getActivity().runOnUiThread(() -> {
                    if (MyApplication.getInstance().isSerialPortConnect) {
                        mBinding.vWeightScaleStatus.setBackgroundResource(R.drawable.shape_yuan_green);
                        mBinding.tvWeightScaleStatus.setText(getRstr(R.string.connected));
                        mBinding.tvWeightScaleStatus.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                        mBinding.tvWeightWeight.setText(DFUtils.getNum2(ByteUtils.getSerialPortScaleData(event.getBytes())) + "kg");
                    } else {
                        mBinding.vWeightScaleStatus.setBackgroundResource(R.drawable.shape_yuan_red);
                        mBinding.tvWeightScaleStatus.setText(getRstr(R.string.connect_no));
                        mBinding.tvWeightScaleStatus.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.red));
                        mBinding.tvWeightWeight.setText("0.00kg");
                    }
                });
                break;
        }
    }

    /**
     * 更新UI
     */
    private void setUI() {
        /*小票设置start*/
        //打印模版
        mBinding.tvReceiptPrinter.setText(getRstr(R.string.cash_register_colon) + "001");
        mBinding.tvReceiptStaff.setText(getRstr(R.string.cashier_staff_colon) + getRstr(R.string.cash_staff_name));
        mBinding.tvReceiptDiscount.setText(getRstr(R.string.discount_colon) + "0.00" + getRstr(R.string.yuan));
        mBinding.tvReceiptPayment.setText(getRstr(R.string.payable_colon) + "3.00" + getRstr(R.string.yuan));
        mBinding.tvReceiptActualPayment.setText(getRstr(R.string.paid_colon) + "3.00" + getRstr(R.string.yuan));
        mBinding.tvReceiptBalance.setText(getRstr(R.string.balance_colon) + "0.00" + getRstr(R.string.yuan));
        mBinding.tvReceiptCash.setText(getRstr(R.string.cash_colon) + "3.00" + getRstr(R.string.yuan));
        mBinding.tvReceiptMember.setText(getRstr(R.string.member_colon) + "*** 138****8888");
        mBinding.tvReceiptPoints.setText(getRstr(R.string.order_points_colon) + "0");
        mBinding.tvReceiptMemberPoints.setText(getRstr(R.string.member_points_colon) + "0");
        mBinding.tvReceiptMobile.setText(getRstr(R.string.contact_mobile_colon) + "13888888888");
        //小票打印机连接方式
        receiptPrintType = SPUtils.getInstance().getInt(Constants.RECEIPT_PRINTER_CONNECT_TYPE, 3);
        receiptPrintTypeList.clear();
//        receiptPrintTypeList.add(new ConditionData(1, getRstr(R.string.connect_bt), false));
//        receiptPrintTypeList.add(new ConditionData(2, getRstr(R.string.connect_serail), false));
        receiptPrintTypeList.add(new ConditionData(3, getRstr(R.string.connect_usb), false));
        receiptPrintTypeList.add(new ConditionData(4, getRstr(R.string.connect_wifi), false));
        switch (receiptPrintType) {
            case 1:
                mBinding.tvReceiptPrint.setText(getRstr(R.string.connect_bt));
                break;
            case 2:
                mBinding.tvReceiptPrint.setText(getRstr(R.string.connect_serail));
                break;
            case 4:
                mBinding.tvReceiptPrint.setText(getRstr(R.string.connect_wifi));
                break;
            default:
                mBinding.tvReceiptPrint.setText(getRstr(R.string.connect_usb));
                break;
        }
        this.isReceiptPrinterConnect = MyApplication.getInstance().isReceiptPrinterConnect;
        if (isReceiptPrinterConnect) {
            mBinding.vReceiptPrinterStatus.setBackgroundResource(R.drawable.shape_yuan_green);
            mBinding.tvReceiptPrinterStatus.setText(getRstr(R.string.connected));
            mBinding.tvReceiptPrinterStatus.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
        } else {
            mBinding.vReceiptPrinterStatus.setBackgroundResource(R.drawable.shape_yuan_red);
            mBinding.tvReceiptPrinterStatus.setText(getRstr(R.string.connect_no));
            mBinding.tvReceiptPrinterStatus.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.red));
        }
        mBinding.tvReceiptDate.setText(DateUtils.getCurrentDate(DateUtils.PATTERN_SECOND));
        //是否使用餐饮小票
        isUseCateringReceipt = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_CATERING_RECEIPT, ""));
        mBinding.ivReceiptCatering.setSelected(isUseCateringReceipt);
        mBinding.linReceiptCatering.setVisibility(isUseCateringReceipt ? View.VISIBLE : View.GONE);
        //默认打印数量
        printCount = SPUtils.getInstance().getInt(Constants.DEFAULT_PRINT_COUNT, 1);
        mBinding.tvReceiptPrintCount.setText(String.valueOf(printCount));
        //温馨提示
        receiptTips = SPUtils.getInstance().getString(Constants.RECEIPT_TIPS, getRstr(R.string.thanks_welcome_again));
        mBinding.tvReceiptTipsCount.setText("（" + receiptTips.length() + "/50)");
        mBinding.etReceiptTips.setText(receiptTips);
        mBinding.tvReceiptTips.setText(getRstr(R.string.tips_warm_colon) + receiptTips);
        //备注
        receiptRemarks = SPUtils.getInstance().getString(Constants.RECEIPT_REMARKS, getRstr(R.string.thanks_welcome_again));
        mBinding.tvRemarksCount.setText("（" + receiptTips.length() + "/50)");
        mBinding.etReceiptRemarks.setText(receiptTips);
        mBinding.tvReceiptRemarks.setText(receiptTips);
        //pos设置
        isUsePos = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_POS, ""));
        mBinding.ivPosEnable.setSelected(isUsePos);
        posId = SPUtils.getInstance().getString(Constants.POS_ID);
        mBinding.etPosId.setText(posId);
        /*小票设置end*/

        /*称重设置start*/
        //启用串口秤
        isUseSerialPortScale = TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_SERIAL_PORT_SCALE, ""));
        mBinding.ivWeightEnable.setSelected(isUseSerialPortScale);
        //端口号
        weightSerialPort = SPUtils.getInstance().getInt(Constants.WEIGHT_SERIAL_PORT, 0);
        weightSerialPortList.clear();
        weightSerialPortList.add(new ConditionData(0, "/dev/ttyS3", false));
        switch (weightSerialPort) {
            case 0:
                mBinding.tvWeightPort.setText("/dev/ttyS3");
                break;
        }
        //波特率
        BAUDId = SPUtils.getInstance().getInt(Constants.BAUD, 2);
        switch (BAUDId) {
            case 0:
                mBinding.tvWeightBAUD.setText("2400");
                break;
            case 1:
                mBinding.tvWeightBAUD.setText("4800");
                break;
            case 3:
                mBinding.tvWeightBAUD.setText("115200");
                break;
            default:
                mBinding.tvWeightBAUD.setText("9600");
                break;
        }
        BAUDList.clear();
        BAUDList.add(new ConditionData(0, "2400", false));
        BAUDList.add(new ConditionData(1, "4800", false));
        BAUDList.add(new ConditionData(2, "9600", false));
        BAUDList.add(new ConditionData(3, "115200", false));
        //数据解析方式
        dataParsingType = SPUtils.getInstance().getInt(Constants.DATA_PARSING_TYPE, 0);
        switch (dataParsingType) {
            case 1:
                mBinding.tvWeightDataParsingType.setText(getRstr(R.string.data_parsing_type1));
                break;
            case 2:
                mBinding.tvWeightDataParsingType.setText(getRstr(R.string.data_parsing_type2));
                break;
            case 3:
                mBinding.tvWeightDataParsingType.setText(getRstr(R.string.data_parsing_type3));
                break;
            default:
                mBinding.tvWeightDataParsingType.setText(getRstr(R.string.data_parsing_type0));
                break;
        }
        dataParsingTypeList.clear();
        dataParsingTypeList.add(new ConditionData(0, getRstr(R.string.data_parsing_type0), false));
        dataParsingTypeList.add(new ConditionData(1, getRstr(R.string.data_parsing_type1), false));
        dataParsingTypeList.add(new ConditionData(2, getRstr(R.string.data_parsing_type2), false));
        dataParsingTypeList.add(new ConditionData(3, getRstr(R.string.data_parsing_type3), false));
        /*称重设置end*/

        //启用副屏
        isUseSecondaryScreen = TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_SECONDARY_SCREEN, ""));
        mBinding.ivOtherEnable.setSelected(isUseSecondaryScreen);
        //支付前弹钱箱
        isUseBeforePayBox = TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_BEFORE_PAY_BOX, ""));
        mBinding.ivOtherEnableBox.setSelected(isUseBeforePayBox);

    }

    /**
     * 控制fragment的变化
     */
    public void fragmentControl() {
        if (currentIndex != index) {
            removeBottomColor();
            setBottomColor();
            currentIndex = index;
        }
    }

    /**
     * 设置底部栏按钮变色
     */
    private void setBottomColor() {
        switch (index) {
            case 0:
                mBinding.tvReceipt.setBackgroundResource(R.drawable.shape_white_topleft_5);
                mBinding.tvReceipt.setTextColor(getResources().getColor(R.color.black));
                mBinding.tvReceipt.setTypeface(Typeface.DEFAULT_BOLD);
                mBinding.linReceipt.setVisibility(View.VISIBLE);
                break;
            case 1:
                mBinding.tvTags.setBackgroundColor(getResources().getColor(R.color.white));
                mBinding.tvTags.setTextColor(getResources().getColor(R.color.black));
                mBinding.tvTags.setTypeface(Typeface.DEFAULT_BOLD);
                mBinding.linTags.setVisibility(View.VISIBLE);
                break;
            case 2:
                mBinding.tvWeight.setBackgroundColor(getResources().getColor(R.color.white));
                mBinding.tvWeight.setTextColor(getResources().getColor(R.color.black));
                mBinding.tvWeight.setTypeface(Typeface.DEFAULT_BOLD);
                mBinding.linWeight.setVisibility(View.VISIBLE);
                break;
            case 3:
                mBinding.tvCamera.setBackgroundColor(getResources().getColor(R.color.white));
                mBinding.tvCamera.setTextColor(getResources().getColor(R.color.black));
                mBinding.tvCamera.setTypeface(Typeface.DEFAULT_BOLD);
                mBinding.linCamera.setVisibility(View.VISIBLE);
                break;
            case 4:
                mBinding.tvPos.setBackgroundColor(getResources().getColor(R.color.white));
                mBinding.tvPos.setTextColor(getResources().getColor(R.color.black));
                mBinding.tvPos.setTypeface(Typeface.DEFAULT_BOLD);
                mBinding.linPos.setVisibility(View.VISIBLE);
                break;
            case 5:
                mBinding.tvOther.setBackgroundColor(getResources().getColor(R.color.white));
                mBinding.tvOther.setTextColor(getResources().getColor(R.color.black));
                mBinding.tvOther.setTypeface(Typeface.DEFAULT_BOLD);
                mBinding.linOther.setVisibility(View.VISIBLE);
                break;
            default:
                break;
        }
    }

    /**
     * 清除底部栏颜色
     */
    private void removeBottomColor() {
        switch (currentIndex) {
            case 0:
                mBinding.tvReceipt.setBackgroundResource(0);
                mBinding.tvReceipt.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
                mBinding.tvReceipt.setTypeface(Typeface.DEFAULT);
                mBinding.linReceipt.setVisibility(View.GONE);
                break;
            case 1:
                mBinding.tvTags.setBackgroundResource(0);
                mBinding.tvTags.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
                mBinding.tvTags.setTypeface(Typeface.DEFAULT);
                mBinding.linTags.setVisibility(View.GONE);
                break;
            case 2:
                mBinding.tvWeight.setBackgroundResource(0);
                mBinding.tvWeight.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
                mBinding.tvWeight.setTypeface(Typeface.DEFAULT);
                mBinding.linWeight.setVisibility(View.GONE);
                break;
            case 3:
                mBinding.tvCamera.setBackgroundResource(0);
                mBinding.tvCamera.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
                mBinding.tvCamera.setTypeface(Typeface.DEFAULT);
                mBinding.linCamera.setVisibility(View.GONE);
                break;
            case 4:
                mBinding.tvPos.setBackgroundResource(0);
                mBinding.tvPos.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
                mBinding.tvPos.setTypeface(Typeface.DEFAULT);
                mBinding.linPos.setVisibility(View.GONE);
                break;
            case 5:
                mBinding.tvOther.setBackgroundResource(0);
                mBinding.tvOther.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
                mBinding.tvOther.setTypeface(Typeface.DEFAULT);
                mBinding.linOther.setVisibility(View.GONE);
                break;
            default:
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        scaleAdapter = new ScaleAdapter(scaleList);
        mBinding.rvWeightScale.setAdapter(scaleAdapter);
        scaleAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            switch (view.getId()) {
                case R.id.ivItemDel:
                    //删除
                    break;
                case R.id.ivItemSwitch:
                    //开关
                    break;
            }
        });
    }

    /**
     * 检查读写权限
     */
    private void checkPermissionSerialPort() {
        if (PermissionUtils.checkPermissionsGroup(getActivity(), 3)) {
            MyApplication.getInstance().initSerialPort();
        } else {
            PermissionUtils.requestPermissions(this, Constants.PERMISSION_READ, 3);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION_READ:
                //读写
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showToast(1, getRstr(R.string.no_permission_tips));
                } else {
                    MyApplication.getInstance().initSerialPort();
                }
                break;
        }
    }

}
