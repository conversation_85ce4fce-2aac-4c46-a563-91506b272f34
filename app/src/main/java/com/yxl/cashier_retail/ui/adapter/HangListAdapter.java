package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.HangListData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:挂单列表-已月筛选（适配器）
 * Created by jingang on 2025/1/20
 */
public class HangListAdapter extends BaseAdapter<HangListData> {
    public HangListAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_hang_list;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvMonth, tvCount, tvTotal;
        tvMonth = holder.getView(R.id.tvItemMonth);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTotal = holder.getView(R.id.tvItemTotal);
        RecyclerView recyclerView = holder.getView(R.id.rvItem);

        double total = 0;
        for (int i = 0; i < mDataList.get(position).getData().size(); i++) {
            total = total + mDataList.get(position).getData().get(i).getTotal();
        }
        tvMonth.setText(String.valueOf(mDataList.get(position).getMonth()));
        tvCount.setText("挂单数量：" + mDataList.get(position).getData().size());
        tvTotal.setText("挂单总额：" + DFUtils.getNum2(total));

        if (mDataList.get(position).getData().isEmpty()) {
            recyclerView.setVisibility(View.GONE);
        } else {
            HangOrderAdapter adapter = new HangOrderAdapter(mContext);
            recyclerView.setAdapter(adapter);
            adapter.setDataList(mDataList.get(position).getData());
            adapter.setListener(new HangOrderAdapter.MyListener() {
                @Override
                public void onItemClick(View view, int position1) {
                    if (listener != null) {
                        listener.onItemClick(position, position1);
                    }
                }

                @Override
                public void onDelClick(View view, int position1) {
                    if (listener != null) {
                        listener.onDelClick(position, position1);
                    }
                }
            });
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(int position, int position1);

        void onDelClick(int position, int position1);
    }
}
