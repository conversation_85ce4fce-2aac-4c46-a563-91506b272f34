package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;

/**
 * Describe:统计-经营统计（适配器）
 * Created by jingang on 2024/6/17
 */
public class StatisticsManageAdapter extends BaseAdapter<String> {

    public StatisticsManageAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_statistics_manage;
    }

    @Override
    public int getItemCount() {
        return 12;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg, ivArrow;
        TextView tvName, tvCount, tvProfit;
        ivImg = holder.getView(R.id.ivItemImg);
        tvName = holder.getView(R.id.tvItemName);
        ivArrow = holder.getView(R.id.ivItemArrow);
        tvCount = holder.getView(R.id.tvItemCount);
        tvProfit = holder.getView(R.id.tvItemProfit);
    }
}
