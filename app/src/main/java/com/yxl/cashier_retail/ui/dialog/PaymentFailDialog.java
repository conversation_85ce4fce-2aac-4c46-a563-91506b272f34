package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.Gravity;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogPaymentFailBinding;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:dialog（收款失败）
 * Created by jingang on 2024/09/07
 */
@SuppressLint("SetTextI18n")
public class PaymentFailDialog extends BaseDialog<DialogPaymentFailBinding> {
    private static double total;//收款金额
    private static String reason;//收款失败原因

    public static void showDialog(Activity activity, double total, String reason, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        PaymentFailDialog.listener = listener;
        PaymentFailDialog.total = total;
        PaymentFailDialog.reason = reason;
        PaymentFailDialog dialog = new PaymentFailDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 10 * 3, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 8);
        dialog.show();
    }

    public PaymentFailDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.tvDialogConfirm.setOnClickListener(v -> {
            if (listener != null) {
                listener.onConfirm();
                dismiss();
            }
        });
        mBinding.tvDialogTotal.setText(getRstr(R.string.money) + DFUtils.getNum2(total));
        mBinding.tvDialogReason.setText(reason);
    }

    @Override
    protected DialogPaymentFailBinding getViewBinding() {
        return DialogPaymentFailBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm();
    }
}
