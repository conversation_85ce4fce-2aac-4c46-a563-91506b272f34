package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;

/**
 * Describe:统计-打款统计（适配器）
 * Created by jingang on 2024/6/17
 */
public class StatisticsPaymentAdapter extends BaseAdapter<String> {

    public StatisticsPaymentAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_statistics_payment;
    }

    @Override
    public int getItemCount() {
        return 5;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvTime, tvMoney, tvMoney1, tvPayTime, tvTotal, tvStatus;
        tvTime = holder.getView(R.id.tvItemTime);
        tvMoney = holder.getView(R.id.tvItemMoney);
        tvMoney1 = holder.getView(R.id.tvItemMoney1);
        tvPayTime = holder.getView(R.id.tvItemPayTime);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvStatus = holder.getView(R.id.tvItemStatus);
    }
}
