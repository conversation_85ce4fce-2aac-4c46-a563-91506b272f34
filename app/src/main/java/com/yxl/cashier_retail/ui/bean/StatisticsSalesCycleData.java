package com.yxl.cashier_retail.ui.bean;

/**
 * Describe:统计-销售数据周期占比（实体类）
 * Created by jingang on 2024/8/30
 */
public class StatisticsSalesCycleData {
    /**
     * date : 2024-08-30
     * listCountRadio : 260
     * saleTotalRadio : 166
     * grossProfitRadio : 266
     * netListCountRadio : 0
     */

    private String date;
    private int listCountRadio;//订单量周期占比
    private int saleTotalRadio;//订单销售额周期占比
    private int grossProfitRadio;//毛利润周期占比
    private int netListCountRadio;//网单量周期占比

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public int getListCountRadio() {
        return listCountRadio;
    }

    public void setListCountRadio(int listCountRadio) {
        this.listCountRadio = listCountRadio;
    }

    public int getSaleTotalRadio() {
        return saleTotalRadio;
    }

    public void setSaleTotalRadio(int saleTotalRadio) {
        this.saleTotalRadio = saleTotalRadio;
    }

    public int getGrossProfitRadio() {
        return grossProfitRadio;
    }

    public void setGrossProfitRadio(int grossProfitRadio) {
        this.grossProfitRadio = grossProfitRadio;
    }

    public int getNetListCountRadio() {
        return netListCountRadio;
    }

    public void setNetListCountRadio(int netListCountRadio) {
        this.netListCountRadio = netListCountRadio;
    }
}
