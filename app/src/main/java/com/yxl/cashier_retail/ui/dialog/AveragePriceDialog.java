package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.Gravity;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogAveragePriceBinding;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:dialog（库存均价说明）
 * Created by jingang on 2025/01/15
 */
@SuppressLint("NonConstantResourceId")
public class AveragePriceDialog extends BaseDialog<DialogAveragePriceBinding> {

    public static void showDialog(Activity activity) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        AveragePriceDialog dialog = new AveragePriceDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 3, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public AveragePriceDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.tvDialogConfirm.setOnClickListener(v -> dismiss());
    }

    @Override
    protected DialogAveragePriceBinding getViewBinding() {
        return DialogAveragePriceBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

}
