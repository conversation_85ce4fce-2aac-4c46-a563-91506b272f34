package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.PaymentData;

/**
 * Describe:支付方式（适配器）
 * Created by jingang on 2024/5/15
 */
public class PaymentAdapter extends BaseAdapter<PaymentData> {

    public PaymentAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_payment;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        ImageView ivIcon = holder.getView(R.id.ivItemIcon);
        TextView tvName = holder.getView(R.id.tvItemName);
        //1.现金 2.支付宝 3.微信 4.银行卡 5.人脸 6.储值卡 7.组合 8.商品管理
        switch (mDataList.get(position).getPaymentId()) {
            case 2:
                lin.setBackgroundResource(R.mipmap.ic_payment_bg002);
                ivIcon.setVisibility(View.VISIBLE);
                ivIcon.setImageResource(R.mipmap.ic_payment_img002);
                tvName.setText(mContext.getResources().getString(R.string.alipay));
                break;
            case 3:
                lin.setBackgroundResource(R.mipmap.ic_payment_bg003);
                ivIcon.setVisibility(View.VISIBLE);
                ivIcon.setImageResource(R.mipmap.ic_payment_img003);
                tvName.setText(mContext.getResources().getString(R.string.wechat));
                break;
            case 4:
                lin.setBackgroundResource(R.mipmap.ic_payment_bg002);
                ivIcon.setVisibility(View.VISIBLE);
                ivIcon.setImageResource(R.mipmap.ic_payment_img004);
                tvName.setText(mContext.getResources().getString(R.string.bank_card));
                break;
            case 5:
                lin.setBackgroundResource(R.mipmap.ic_payment_bg004);
                ivIcon.setVisibility(View.VISIBLE);
                ivIcon.setImageResource(R.mipmap.ic_payment_img005);
                tvName.setText(mContext.getResources().getString(R.string.face_swiping));
                break;
            case 6:
                lin.setBackgroundResource(R.mipmap.ic_payment_bg001);
                ivIcon.setVisibility(View.VISIBLE);
                ivIcon.setImageResource(R.mipmap.ic_payment_img007);
                tvName.setText(mContext.getResources().getString(R.string.stored_card));
                break;
            case 7:
                lin.setBackgroundResource(R.mipmap.ic_payment_bg003);
                ivIcon.setVisibility(View.VISIBLE);
                ivIcon.setImageResource(R.mipmap.ic_payment_img006);
                tvName.setText(mContext.getResources().getString(R.string.combination));
                break;
            case 8:
                lin.setBackgroundResource(R.mipmap.ic_payment_bg002);
                ivIcon.setVisibility(View.GONE);
                tvName.setText(mContext.getResources().getString(R.string.goods_manage));
                break;
            default:
                lin.setBackgroundResource(R.mipmap.ic_payment_bg001);
                ivIcon.setVisibility(View.VISIBLE);
                ivIcon.setImageResource(R.mipmap.ic_payment_img001);
                tvName.setText(mContext.getResources().getString(R.string.cash));
                break;
        }
    }
}
