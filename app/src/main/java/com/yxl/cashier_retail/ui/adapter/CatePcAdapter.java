package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.graphics.Typeface;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.CateData;
import com.yxl.cashier_retail.ui.bean.CatePcData;

/**
 * Describe:虚拟分类列表（适配器）
 * Created by jingang on 2024/12/21
 */
public class CatePcAdapter extends BaseAdapter<CatePcData> {

    public CatePcAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_cate_pc;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName = holder.getView(R.id.tvItemName);
        tvName.setText(mDataList.get(position).getGoods_kind_name());

        if (mDataList.get(position).isCheck()) {
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
            tvName.setTypeface(Typeface.DEFAULT_BOLD);
            if (position % 6 == 0) {
                if (position == (mDataList.size() - 1)) {
                    tvName.setBackgroundResource(R.drawable.shape_green_5);
                } else {
                    tvName.setBackgroundResource(R.drawable.shape_green_left_5);
                }
            } else if (position % 6 == 5) {
                tvName.setBackgroundResource(R.drawable.shape_green_right_5);
            } else {
                if (position == (mDataList.size() - 1)) {
                    tvName.setBackgroundResource(R.drawable.shape_green_right_5);
                } else {
                    tvName.setBackgroundColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
                }
            }
        } else {
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
            tvName.setTypeface(Typeface.DEFAULT);
            if (position % 6 == 0) {
                if (position == (mDataList.size() - 1)) {
                    tvName.setBackgroundResource(R.drawable.shape_white_5);
                } else {
                    tvName.setBackgroundResource(R.drawable.shape_white_left_5);
                }
            } else if (position % 6 == 5) {
                tvName.setBackgroundResource(R.drawable.shape_white_right_5);
            } else {
                if (position == (mDataList.size() - 1)) {
                    tvName.setBackgroundResource(R.drawable.shape_white_right_5);
                } else {
                    tvName.setBackgroundColor(mContext.getResources().getColor(R.color.white));
                }
            }
        }
    }
}
