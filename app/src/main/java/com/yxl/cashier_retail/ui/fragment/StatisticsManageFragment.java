package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.graphics.Color;
import android.view.View;

import com.github.mikephil.charting.animation.Easing;
import com.github.mikephil.charting.components.Legend;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.data.PieData;
import com.github.mikephil.charting.data.PieDataSet;
import com.github.mikephil.charting.data.PieEntry;
import com.github.mikephil.charting.formatter.IndexAxisValueFormatter;
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet;
import com.github.mikephil.charting.utils.ColorTemplate;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.LazyBaseFragment;
import com.yxl.cashier_retail.databinding.FmStatisticsManageBinding;
import com.yxl.cashier_retail.ui.activity.StatisticsActivity;
import com.yxl.cashier_retail.ui.adapter.StatisticsTopGoodsAdapter;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.StatisticsClassData;
import com.yxl.cashier_retail.ui.bean.StatisticsInfoData;
import com.yxl.cashier_retail.ui.bean.StatisticsMainData;
import com.yxl.cashier_retail.ui.bean.StatisticsPayTypeData;
import com.yxl.cashier_retail.ui.bean.StatisticsSalesByHourData;
import com.yxl.cashier_retail.ui.bean.StatisticsSalesCycleData;
import com.yxl.cashier_retail.ui.bean.StatisticsSalesData;
import com.yxl.cashier_retail.ui.bean.StatisticsTopGoodsData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.utils.DateUtils;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:统计-经营统计
 * Created by jingang on 2024/6/15
 */
@SuppressLint("SetTextI18n")
public class StatisticsManageFragment extends LazyBaseFragment<FmStatisticsManageBinding> implements View.OnClickListener {
    private int daySales;//销售额走势 0.周 1.月 2.年
    private boolean isHidden, isKeywords;

    //热销、滞销商品TOP5
    private StatisticsTopGoodsAdapter hotAdapter,//热销
            goodsProfitAdapter,//累计利润
            unHotAdapter;//滞销

    @Override
    protected FmStatisticsManageBinding getViewBinding() {
        return FmStatisticsManageBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        //销售额走势
        mBinding.tvDaySales0.setOnClickListener(this);
        mBinding.tvDaySales1.setOnClickListener(this);
        mBinding.tvDaySales2.setOnClickListener(this);
        setChartSales();
        setChartClass();
        setChartPayType();
        setChartSalesByHour();
        setAdapter();
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void onFragmentFirstVisible() {
        super.onFragmentFirstVisible();
        onRefresh();
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        this.isHidden = hidden;
        if (!isHidden && isKeywords) {
            onRefresh();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        int num = event.getNum();
        switch (event.getMsg()) {
            case Constants.STATISTICS_LIST:
                if (!isHidden) {
                    onRefresh();
                    isKeywords = false;
                } else {
                    isKeywords = true;
                }
                break;
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvDaySales0:
                //销售额走势-周
                if (daySales != 0) {
                    daySales = 0;
                    mBinding.tvDaySales0.setBackgroundResource(R.drawable.shape_green_left_5);
                    mBinding.tvDaySales0.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvDaySales1.setBackgroundResource(0);
                    mBinding.tvDaySales1.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvDaySales2.setBackgroundResource(0);
                    mBinding.tvDaySales2.setTextColor(getResources().getColor(R.color.black));
                    getStatisticsSales();
                }
                break;
            case R.id.tvDaySales1:
                //销售额走势-月
                if (daySales != 1) {
                    daySales = 1;
                    mBinding.tvDaySales0.setBackgroundResource(0);
                    mBinding.tvDaySales0.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvDaySales1.setBackgroundColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    mBinding.tvDaySales1.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvDaySales2.setBackgroundResource(0);
                    mBinding.tvDaySales2.setTextColor(getResources().getColor(R.color.black));
                    getStatisticsSales();
                }
                break;
            case R.id.tvDaySales2:
                //销售额走势-年
                if (daySales != 2) {
                    daySales = 2;
                    mBinding.tvDaySales0.setBackgroundResource(0);
                    mBinding.tvDaySales0.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvDaySales1.setBackgroundResource(0);
                    mBinding.tvDaySales1.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvDaySales2.setBackgroundResource(R.drawable.shape_green_right_5);
                    mBinding.tvDaySales2.setTextColor(getResources().getColor(R.color.white));
                    getStatisticsSales();
                }
                break;
        }
    }

    /**
     * 刷新
     */
    private void onRefresh() {
        getStatisticsMain();
//        getStatisticsPageInfo();
        getStatisticsSales();
        getStatisticsSalesByHour();
        getStatisticsSalesCycle();
        getStatisticsClass();
        getStatisticsPayType();
        getTopGoods();
        getTopGoodsProfit();
        getUnSalableTopGoods();
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //热销商品TOP5
        hotAdapter = new StatisticsTopGoodsAdapter(getActivity());
        mBinding.rvHot.setAdapter(hotAdapter);

        //累计利润商品TOP5
        goodsProfitAdapter = new StatisticsTopGoodsAdapter(getActivity());
        mBinding.rvProfit.setAdapter(goodsProfitAdapter);

        //滞销商品TOP5
        unHotAdapter = new StatisticsTopGoodsAdapter(getActivity());
        mBinding.rvUnHot.setAdapter(unHotAdapter);
    }

    /**
     * 销售额走势（折线图）
     */
    private void setChartSales() {
        //设置x轴的位置
        XAxis xAxis = mBinding.chartSales.getXAxis();
        xAxis.setDrawGridLines(false);//是否绘制网格线
        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);//位置
        xAxis.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        xAxis.setTextSize(6f);

        //设置Y轴
        YAxis yAxis = mBinding.chartSales.getAxisLeft();
        yAxis.setAxisMinimum(0f);//设置Y轴最小值
        yAxis.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        yAxis.setTextSize(6f);
        yAxis.setDrawGridLines(false); //是否y轴绘制网格线

        //是否绘制右侧轴线
        mBinding.chartSales.getAxisRight().setDrawGridLines(false);
        mBinding.chartSales.getAxisRight().setEnabled(false);

        mBinding.chartSales.getDescription().setEnabled(false);

        //获取图例的实例
        Legend legend = mBinding.chartSales.getLegend();
        //图例文字的大小
        legend.setTextSize(8f);
        //图例文字的颜色
        legend.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        //图例的大小
        legend.setFormSize(8f);
        //图例的模式
        legend.setForm(Legend.LegendForm.CIRCLE);
        //图例水平方向
        legend.setHorizontalAlignment(Legend.LegendHorizontalAlignment.RIGHT);
        //图例垂直方向
        legend.setVerticalAlignment(Legend.LegendVerticalAlignment.TOP);
        //图例分布模式(这里是垂直分布，默认的为水平分布)
        legend.setOrientation(Legend.LegendOrientation.HORIZONTAL);
    }

    /**
     * 销售品类占比（饼状图）
     */
    private void setChartClass() {
        mBinding.chartClass.setDrawHoleEnabled(false);//设置图形是否绘制空洞
        mBinding.chartClass.getDescription().setEnabled(false);//是否显示标签
        mBinding.chartClass.setHoleRadius(DensityUtils.dip2px(getActivity(), 100));//半径
        mBinding.chartClass.setDrawEntryLabels(false);//饼图上的文字

        //获取图例的实例
        Legend legend = mBinding.chartClass.getLegend();
        //图例文字的大小
        legend.setTextSize(6f);
        //图例文字的颜色
        legend.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        //图例的大小
        legend.setFormSize(6f);
        //图例的模式
        legend.setForm(Legend.LegendForm.CIRCLE);
        //图例的位置
        //图例水平方向为靠右
        legend.setHorizontalAlignment(Legend.LegendHorizontalAlignment.RIGHT);
        //图例垂直方向居中
        legend.setVerticalAlignment(Legend.LegendVerticalAlignment.CENTER);
        //图例分布模式(这里是垂直分布，默认的为水平分布)
        legend.setOrientation(Legend.LegendOrientation.VERTICAL);
    }

    /**
     * 支付分类销量占比（饼状图-圆环）
     */
    private void setChartPayType() {
        mBinding.chartPayType.getDescription().setEnabled(false);//是否显示标签
        mBinding.chartPayType.setHoleRadius(DensityUtils.dip2px(getActivity(), 84));//半径
        mBinding.chartPayType.setDrawEntryLabels(false);//饼图上的文字

        //获取图例的实例
        Legend legend = mBinding.chartPayType.getLegend();
        //图例文字的大小
        legend.setTextSize(6f);
        //图例文字的颜色
        legend.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        //图例的大小
        legend.setFormSize(6f);
        //图例的模式
        legend.setForm(Legend.LegendForm.CIRCLE);
        //图例的位置
        //图例水平方向为靠右
        legend.setHorizontalAlignment(Legend.LegendHorizontalAlignment.RIGHT);
        //图例垂直方向居中
        legend.setVerticalAlignment(Legend.LegendVerticalAlignment.CENTER);
        //图例分布模式(这里是垂直分布，默认的为水平分布)
        legend.setOrientation(Legend.LegendOrientation.VERTICAL);

        //设置图形是否绘制空洞
        mBinding.chartPayType.setDrawHoleEnabled(true);
        //设置空心圆半径的大小
        mBinding.chartPayType.setHoleRadius(50f);
        //设置空心圆颜色
        mBinding.chartPayType.setHoleColor(Color.TRANSPARENT);
        //设置透明圈的透明度
        mBinding.chartPayType.setTransparentCircleAlpha(0);
    }

    /**
     * 营业额24H分布（折线图）
     */
    private void setChartSalesByHour() {
        //设置x轴的位置
        XAxis xAxis = mBinding.chartSaleByHour.getXAxis();
        xAxis.setDrawGridLines(false);//是否绘制网格线
        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);//位置
        xAxis.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        xAxis.setTextSize(6f);

        //设置Y轴
        YAxis yAxis = mBinding.chartSaleByHour.getAxisLeft();
        yAxis.setAxisMinimum(0f);//设置Y轴最小值
        yAxis.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        yAxis.setTextSize(6f);
        yAxis.setDrawGridLines(false); //是否y轴绘制网格线

        //是否绘制右侧轴线
        mBinding.chartSaleByHour.getAxisRight().setDrawGridLines(false);
        mBinding.chartSaleByHour.getAxisRight().setEnabled(false);

        mBinding.chartSaleByHour.getDescription().setEnabled(false);

        //获取图例的实例
        Legend legend = mBinding.chartSaleByHour.getLegend();
        //图例文字的大小
        legend.setTextSize(8f);
        //图例文字的颜色
        legend.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        //图例的大小
        legend.setFormSize(8f);
        //图例的模式
        legend.setForm(Legend.LegendForm.CIRCLE);
        //图例水平方向
        legend.setHorizontalAlignment(Legend.LegendHorizontalAlignment.RIGHT);
        //图例垂直方向
        legend.setVerticalAlignment(Legend.LegendVerticalAlignment.TOP);
        //图例分布模式(这里是垂直分布，默认的为水平分布)
        legend.setOrientation(Legend.LegendOrientation.HORIZONTAL);
    }

    /**
     * 收银界面新版-横轴界面(啥呀)
     * 销售额、毛利润、订单量、客单价、网单量、到店会员
     */
    private void getStatisticsMain() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("dayType", StatisticsActivity.day + 1);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getStatisticsMain(),
                params,
                StatisticsMainData.class,
                new RequestListener<StatisticsMainData>() {
                    @Override
                    public void success(StatisticsMainData data) {
                        //今日营业额
                        mBinding.tvSaleTotal.setText(DFUtils.getNum2(data.getSaleTotal()));
                        mBinding.tvSaleTotalRatio.setText(DFUtils.getNum2(data.getSaleTotalRatio()) + "%");
                        if (data.getSaleTotalRatio() > 0) {
                            mBinding.ivSaleTotal.setSelected(true);
                            mBinding.tvSaleTotalRatio.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                        } else {
                            mBinding.ivSaleTotal.setSelected(false);
                            mBinding.tvSaleTotalRatio.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.red));
                        }
                        //毛利润
                        mBinding.tvProfit.setText(DFUtils.getNum2(data.getGrossProfit()));
                        mBinding.tvProfitRatio.setText(DFUtils.getNum2(data.getGrossProfitRatio()) + "%");
                        if (data.getGrossProfitRatio() > 0) {
                            mBinding.ivProfit.setSelected(true);
                            mBinding.tvProfitRatio.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                        } else {
                            mBinding.ivProfit.setSelected(false);
                            mBinding.tvProfitRatio.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.red));
                        }
                        //订单量
                        mBinding.tvListCount.setText(String.valueOf(data.getListCount()));
                        mBinding.tvListCountRatio.setText(DFUtils.getNum2(data.getListCountRatio()) + "%");
                        if (data.getListCountRatio() > 0) {
                            mBinding.ivListCount.setSelected(true);
                            mBinding.tvListCountRatio.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                        } else {
                            mBinding.ivListCount.setSelected(false);
                            mBinding.tvListCountRatio.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.red));
                        }
                        //客单价
                        mBinding.tvAver.setText(DFUtils.getNum2(data.getAverPrice()));
                        mBinding.tvAverRatio.setText(DFUtils.getNum2(data.getAverPriceRatio()) + "%");
                        if (data.getAverPriceRatio() > 0) {
                            mBinding.ivAver.setSelected(true);
                            mBinding.tvAverRatio.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                        } else {
                            mBinding.ivAver.setSelected(false);
                            mBinding.tvAverRatio.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.red));
                        }
                        //网单量
                        mBinding.tvNetListCount.setText(String.valueOf(data.getNetListCount()));
                        mBinding.tvNetListCountRatio.setText(DFUtils.getNum2(data.getNetListCountRatio()) + "%");
                        if (data.getNetListCountRatio() > 0) {
                            mBinding.ivNetListCount.setSelected(true);
                            mBinding.tvNetListCountRatio.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                        } else {
                            mBinding.ivNetListCount.setSelected(false);
                            mBinding.tvNetListCountRatio.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.red));
                        }
                        //到店会员
                        mBinding.tvMemberCount.setText(String.valueOf(data.getCusSum()));
                    }
                });
    }

    /**
     * 充值金额、储值金额、退款金额、过期预警、需补货商品、商品总种类
     */
    private void getStatisticsPageInfo() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("timeFlg", StatisticsActivity.day + 1);
        RXHttpUtil.requestByFormPostAsResponse(getActivity(),
                ZURL.getStatisticsPageInfo(),
                params,
                StatisticsInfoData.class,
                new RequestListener<StatisticsInfoData>() {
                    @Override
                    public void success(StatisticsInfoData data) {
                        mBinding.tvRechargeMoney.setText(DFUtils.getNum2(data.getRechargeAmount()));
                        mBinding.tvStoredMoney.setText(DFUtils.getNum2(data.getStoredAmount()));
                        mBinding.tvRefundMoney.setText(DFUtils.getNum2(data.getTodayReturnTotal()));
                        mBinding.tvExpiredWarning.setText(String.valueOf(data.getWarningNum()));
                        mBinding.tvRestockGoods.setText(String.valueOf(data.getSupplementNum()));
                        mBinding.tvGoodsTotalType.setText(String.valueOf(data.getGoodsTypeNum()));
                    }
                });
    }

    /**
     * 销售额走势
     */
    private void getStatisticsSales() {
        String startTime;
        switch (daySales) {
            case 1:
                startTime = DateUtils.getOldDate(-29);
                break;
            case 2:
                startTime = DateUtils.getOldDate(-364);
                break;
            default:
                startTime = DateUtils.getOldDate(-6);
                break;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("startTime", startTime);
        params.put("endTime", DateUtils.getOldDate(0));
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getStatisticsSales(),
                params,
                StatisticsSalesData.class,
                new RequestListListener<StatisticsSalesData>() {
                    @Override
                    public void onResult(List<StatisticsSalesData> list) {
                        List<String> xLabels = new ArrayList<>();
                        List<Entry> totalList = new ArrayList<>(),
                                countList = new ArrayList<>();
                        for (int i = 0; i < list.size(); i++) {
                            totalList.add(new Entry(i, (float) list.get(i).getSaleTotal()));
                            countList.add(new Entry(i, (float) list.get(i).getListCount()));
                            xLabels.add(DateUtils.getDateToString(list.get(i).getDatelist(), DateUtils.PATTERN_DAY));
                        }
                        //第一条线
                        LineDataSet lineDataSet = new LineDataSet(totalList, getRstr(R.string.sales_volume));
                        lineDataSet.setColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));//折线颜色
                        lineDataSet.setLineWidth(0.5f);//折线宽度
                        lineDataSet.setDrawCircles(false);//隐藏圆点
                        lineDataSet.setMode(LineDataSet.Mode.CUBIC_BEZIER);
                        lineDataSet.setCubicIntensity(0.2f);
                        lineDataSet.setFillColor(getResources().getColor(com.yxl.commonlibrary.R.color.green_20));  // 设置填充颜色

                        //第二条线
                        LineDataSet lineDataSet1 = new LineDataSet(countList, getRstr(R.string.order_quantity));
                        lineDataSet1.setColor(getResources().getColor(com.yxl.commonlibrary.R.color.orange));//折线颜色
                        lineDataSet1.setLineWidth(0.5f);//折线宽度
                        lineDataSet1.setDrawCircles(false);//隐藏圆点
                        lineDataSet1.setMode(LineDataSet.Mode.CUBIC_BEZIER);
                        lineDataSet1.setCubicIntensity(0.2f);
                        lineDataSet1.setFillColor(getResources().getColor(com.yxl.commonlibrary.R.color.orange_tm));  // 设置填充颜色

                        // 将两条线的数据集合放到一个ArrayList中
                        List<ILineDataSet> lineDataSets = new ArrayList<>();
                        lineDataSets.add(lineDataSet);
                        lineDataSets.add(lineDataSet1);
                        LineData data = new LineData(lineDataSets);
                        data.setDrawValues(false);//隐藏数值
                        mBinding.chartSales.setData(data);
                        mBinding.chartSales.getXAxis().setValueFormatter(new IndexAxisValueFormatter(xLabels));
                        mBinding.chartSales.animateY(Constants.CHART_ANIMATE_TIME, Easing.EaseInOutQuad);//设置进来动画
                        mBinding.chartSales.invalidate();
                    }
                });
    }

    /**
     * 销售数据周期占比
     */
    private void getStatisticsSalesCycle() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("dateType", StatisticsActivity.day + 1);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getStatisticsSalesCycle(),
                params,
                StatisticsSalesCycleData.class,
                new RequestListener<StatisticsSalesCycleData>() {
                    @Override
                    public void success(StatisticsSalesCycleData data) {
                        mBinding.progress.setValue(data.getSaleTotalRadio());
                        mBinding.tvSalesCycleNetListCount.setText(data.getNetListCountRadio() + "%");
                        mBinding.tvSalesCycleProfit.setText(data.getGrossProfitRadio() + "%");
                        mBinding.tvSalesCycleListCount.setText(data.getListCountRadio() + "%");
                    }
                });
    }

    /**
     * 销售品类占比
     */
    private void getStatisticsClass() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("dateType", StatisticsActivity.day + 1);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getStatisticsClass(),
                params,
                StatisticsClassData.class,
                new RequestListListener<StatisticsClassData>() {
                    @Override
                    public void onResult(List<StatisticsClassData> list) {
                        if (list.size() > 0) {
                            mBinding.chartClass.setVisibility(View.VISIBLE);
                            mBinding.ivEmptyClass.setVisibility(View.GONE);
                            List<PieEntry> pieList = new ArrayList<>();
                            for (int i = 0; i < list.size(); i++) {
                                pieList.add(new PieEntry((float) list.get(i).getSaleTotal(), list.get(i).getKindName()));
                            }
                            PieDataSet pieDataSet = new PieDataSet(pieList, "");
                            //为每个分组设置颜色，如果不为每个都设置颜色最后饼图只会有一个颜色，这样无法有效看出占比
//                        pieDataSet.setColors(getResources().getColor(R.color.color_statistics1),
//                                getResources().getColor(R.color.color_statistics3),
//                                getResources().getColor(R.color.color_statistics0));
                            ArrayList<Integer> colors = new ArrayList<>();
                            for (int c : ColorTemplate.VORDIPLOM_COLORS)
                                colors.add(c);
                            for (int c : ColorTemplate.JOYFUL_COLORS)
                                colors.add(c);
                            for (int c : ColorTemplate.COLORFUL_COLORS)
                                colors.add(c);
                            for (int c : ColorTemplate.LIBERTY_COLORS)
                                colors.add(c);
                            for (int c : ColorTemplate.PASTEL_COLORS)
                                colors.add(c);
                            colors.add(ColorTemplate.getHoloBlue());
                            pieDataSet.setColors(colors);
                            pieDataSet.setSliceSpace(0f); //设置饼块与饼块之间的距离
                            pieDataSet.setDrawValues(false);//是否绘制饼图上的文字
                            PieData pieData = new PieData(pieDataSet);
                            mBinding.chartClass.setData(pieData);
                            mBinding.chartClass.animateY(Constants.CHART_ANIMATE_TIME, Easing.EaseInOutQuad);//设置进来动画
                            mBinding.chartClass.invalidate();
                        } else {
                            mBinding.chartClass.setVisibility(View.GONE);
                            mBinding.ivEmptyClass.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 支付分类销售占比
     */
    private void getStatisticsPayType() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("dateType", StatisticsActivity.day + 1);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getStatisticsPayType(),
                params,
                StatisticsPayTypeData.class,
                new RequestListListener<StatisticsPayTypeData>() {
                    @Override
                    public void onResult(List<StatisticsPayTypeData> list) {
                        if (list.size() > 0) {
                            mBinding.chartPayType.setVisibility(View.VISIBLE);
                            mBinding.ivEmptyPayType.setVisibility(View.GONE);
                            List<PieEntry> pieList = new ArrayList<>();
                            for (int i = 0; i < list.size(); i++) {
                                pieList.add(new PieEntry((float) list.get(i).getSaleTotal(), list.get(i).getPayment()));
                            }
                            PieDataSet pieDataSet = new PieDataSet(pieList, "");
                            //为每个分组设置颜色，如果不为每个都设置颜色最后饼图只会有一个颜色，这样无法有效看出占比
//                        pieDataSet.setColors(getResources().getColor(R.color.color_statistics1),
//                                getResources().getColor(R.color.color_statistics3),
//                                getResources().getColor(R.color.color_statistics0),
//                                getResources().getColor(R.color.color_statistics2));
                            ArrayList<Integer> colors = new ArrayList<>();
                            for (int c : ColorTemplate.VORDIPLOM_COLORS)
                                colors.add(c);
                            for (int c : ColorTemplate.JOYFUL_COLORS)
                                colors.add(c);
                            for (int c : ColorTemplate.COLORFUL_COLORS)
                                colors.add(c);
                            for (int c : ColorTemplate.LIBERTY_COLORS)
                                colors.add(c);
                            for (int c : ColorTemplate.PASTEL_COLORS)
                                colors.add(c);
                            colors.add(ColorTemplate.getHoloBlue());
                            pieDataSet.setColors(colors);
                            pieDataSet.setSliceSpace(0f); //设置饼块与饼块之间的距离
                            pieDataSet.setDrawValues(false);//是否绘制饼图上的文字
                            PieData pieData = new PieData(pieDataSet);
                            mBinding.chartPayType.setData(pieData);
                            mBinding.chartPayType.animateY(Constants.CHART_ANIMATE_TIME, Easing.EaseInOutQuad);//设置进来动画
                            mBinding.chartPayType.invalidate();
                        } else {
                            mBinding.chartPayType.setVisibility(View.GONE);
                            mBinding.ivEmptyPayType.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 营业额24H分布
     */
    private void getStatisticsSalesByHour() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getStatisticsSalesByHour(),
                params,
                StatisticsSalesByHourData.class,
                new RequestListListener<StatisticsSalesByHourData>() {
                    @Override
                    public void onResult(List<StatisticsSalesByHourData> list) {
                        List<Entry> totalList = new ArrayList<>(),
                                yesTotalList = new ArrayList<>();
                        for (int i = 0; i < list.size(); i++) {
                            totalList.add(new Entry(list.get(i).getTime(), (float) list.get(i).getSaleTotal()));
                            yesTotalList.add(new Entry(list.get(i).getTime(), (float) list.get(i).getYesSaleTotal()));
                        }
                        //第一条线
                        LineDataSet lineDataSet = new LineDataSet(totalList, getRstr(R.string.today_turnover));
                        lineDataSet.setColor(getResources().getColor(com.yxl.commonlibrary.R.color.blue));//折线颜色
                        lineDataSet.setLineWidth(0.5f);//折线宽度
                        lineDataSet.setDrawCircles(false);//隐藏圆点
                        lineDataSet.setMode(LineDataSet.Mode.CUBIC_BEZIER);
                        lineDataSet.setCubicIntensity(0.2f);
                        lineDataSet.setFillColor(getResources().getColor(com.yxl.commonlibrary.R.color.green_20));  // 设置填充颜色

                        //第二条线
                        LineDataSet lineDataSet1 = new LineDataSet(yesTotalList, getRstr(R.string.yesterday_turnover));
                        lineDataSet1.setColor(getResources().getColor(com.yxl.commonlibrary.R.color.red));//折线颜色
                        lineDataSet1.setLineWidth(0.5f);//折线宽度
                        lineDataSet1.setDrawCircles(false);//隐藏圆点
                        lineDataSet1.setMode(LineDataSet.Mode.CUBIC_BEZIER);
                        lineDataSet1.setCubicIntensity(0.2f);
                        lineDataSet1.setFillColor(getResources().getColor(com.yxl.commonlibrary.R.color.orange_tm));  // 设置填充颜色

                        // 将两条线的数据集合放到一个ArrayList中
                        List<ILineDataSet> lineDataSets = new ArrayList<>();
                        lineDataSets.add(lineDataSet);
                        lineDataSets.add(lineDataSet1);
                        LineData data = new LineData(lineDataSets);
                        data.setDrawValues(false);//隐藏数值
                        mBinding.chartSaleByHour.setData(data);
                        mBinding.chartSaleByHour.getXAxis().setLabelCount(totalList.size());
                        mBinding.chartSaleByHour.animateY(Constants.CHART_ANIMATE_TIME, Easing.EaseInOutQuad);//设置进来动画
                        mBinding.chartSaleByHour.invalidate();
                    }
                });
    }

    /**
     * 热销商品TOP5
     */
    private void getTopGoods() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("dateType", StatisticsActivity.day + 1);
        params.put("order", 1);//1.热销商品 2.累计利润TOP5
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getTopGoods(),
                params,
                StatisticsTopGoodsData.class,
                new RequestListListener<StatisticsTopGoodsData>() {
                    @Override
                    public void onResult(List<StatisticsTopGoodsData> list) {
                        hotAdapter.clear();
                        if (list.size() > 0) {
                            mBinding.rvHot.setVisibility(View.VISIBLE);
                            mBinding.ivEmptyHot.setVisibility(View.GONE);
                            hotAdapter.addAll(list);
                        } else {
                            mBinding.rvHot.setVisibility(View.GONE);
                            mBinding.ivEmptyHot.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 劣迹利润商品TOP5
     */
    private void getTopGoodsProfit() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("dateType", StatisticsActivity.day + 1);
        params.put("order", 2);//1.热销商品 2.累计利润TOP5
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getTopGoods(),
                params,
                StatisticsTopGoodsData.class,
                new RequestListListener<StatisticsTopGoodsData>() {
                    @Override
                    public void onResult(List<StatisticsTopGoodsData> list) {
                        goodsProfitAdapter.clear();
                        if (list.size() > 0) {
                            mBinding.rvProfit.setVisibility(View.VISIBLE);
                            mBinding.ivEmptyProfit.setVisibility(View.GONE);
                            goodsProfitAdapter.addAll(list);
                        } else {
                            mBinding.rvProfit.setVisibility(View.GONE);
                            mBinding.ivEmptyProfit.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 滞销商品TOP5
     */
    private void getUnSalableTopGoods() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("dataType", StatisticsActivity.day + 1);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getUnSalableTopGoods(),
                params,
                StatisticsTopGoodsData.class,
                new RequestListListener<StatisticsTopGoodsData>() {
                    @Override
                    public void onResult(List<StatisticsTopGoodsData> list) {
                        unHotAdapter.clear();
                        if (list.size() > 0) {
                            mBinding.rvUnHot.setVisibility(View.VISIBLE);
                            mBinding.ivEmptyUnHot.setVisibility(View.GONE);
                            unHotAdapter.addAll(list);
                        } else {
                            mBinding.rvUnHot.setVisibility(View.GONE);
                            mBinding.ivEmptyUnHot.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

}
