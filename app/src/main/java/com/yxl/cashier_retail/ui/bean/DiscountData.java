package com.yxl.cashier_retail.ui.bean;

import org.litepal.crud.LitePalSupport;

import java.io.Serializable;

/**
 * Describe:折扣（实体类）
 * Created by jingang on 2024/5/30
 */
public class DiscountData extends LitePalSupport implements Serializable {
    private double discount;//折扣
    private boolean select;//是否选择

    public DiscountData() {
    }

    public DiscountData(double discount) {
        this.discount = discount;
    }

    public double getDiscount() {
        return discount;
    }

    public void setDiscount(double discount) {
        this.discount = discount;
    }

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }
}
