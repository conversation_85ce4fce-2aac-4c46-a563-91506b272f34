package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.MyApplication;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityCateAddGoodsBinding;
import com.yxl.cashier_retail.ui.adapter.CatePcAdapter;
import com.yxl.cashier_retail.ui.adapter.GoodsCateAddAdapter;
import com.yxl.cashier_retail.ui.adapter.GoodsManageAdapter;
import com.yxl.cashier_retail.ui.bean.CatePcData;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.ui.dialog.CateDialog;
import com.yxl.cashier_retail.ui.dialog.IAlertDialog;
import com.yxl.cashier_retail.ui.dialog.MenuDialog;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.json.JSONArray;
import org.litepal.LitePal;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:商品管理-添加商品到首页分类
 * Created by jingang on 2024/8/22
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class CateAddGoodsActivity extends BaseActivity<ActivityCateAddGoodsBinding> implements View.OnClickListener {
    private int posCate,//虚拟分类列表下标
            parentId,//虚拟分类id
            maxGoods;//左侧商品列表页数
    private String keyWords,
            parentUnique,//虚拟分类编号
            cateUnique,//一级分类编号
            cateChildUnique;//二级分类编号

    //商品列表
    private GoodsCateAddAdapter goodsAdapter;
    private List<GoodsData> goodsList = new ArrayList<>();

    //虚拟分类
    private CatePcAdapter cateAdapter;
    private List<CatePcData> cateList = new ArrayList<>();

    //虚拟分类下的商品列表
    private GoodsManageAdapter mAdapter;
    private List<GoodsData> dataList = new ArrayList<>();

    @Override
    protected ActivityCateAddGoodsBinding getViewBinding() {
        return ActivityCateAddGoodsBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivLogo.setOnClickListener(this);
        mBinding.ivSearchClear.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.linCate0.setOnClickListener(this);
        mBinding.linCate1.setOnClickListener(this);
        mBinding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                mBinding.ivSearchClear.setVisibility(TextUtils.isEmpty(keyWords) ? View.GONE : View.VISIBLE);
            }
        });
        mBinding.etSearch.setOnEditorActionListener((v, actionId, event) -> {
            max = 0;
            getGoodsList();
            return true;
        });
        setAdapter();
    }

    @Override
    protected void initData() {
        getNetwork();
    }

    @Override
    public void getNetwork() {
        setNetwork();
        getCateList();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivLogo:
                //菜单
                MenuDialog.showDialog(this, type -> {
                    //0.收银 1.入库 2.查询 3.统计 4.网单 5.会员 6.补货 7.营销 8.交班 9.设置
                    switch (type) {
                        case 1:
                            goToActivity(InActivity.class);
                            break;
                        case 2:
                            goToActivity(QueryActivity.class);
                            break;
                        case 3:
                            goToActivity(StatisticsActivity.class);
                            break;
                        case 4:
                            goToActivity(OrderActivity.class);
                            break;
                        case 5:
                            goToActivity(MemberActivity.class);
                            break;
                        case 6:
                            goToActivity(MallActivity.class);
                            break;
                        case 7:
                            goToActivity(MarketingActivity.class);
                            break;
                        case 8:
                            goToActivity(ShiftActivity.class);
                            break;
                        case 9:
                            goToActivity(SettingActivity.class);
                            break;
                    }
                    finish();
                });
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.ivSearchClear:
                //清除搜索输入
                keyWords = "";
                mBinding.etSearch.setText("");
                max = 0;
                getGoodsList();
                break;
            case R.id.linCate0:
                //一级分类
                CateDialog.showDialog(this, cateUnique, cateChildUnique, mBinding.ivCate0, 1, (unique, name, childUnique, childName) -> {
                    cateUnique = unique;
                    cateChildUnique = childUnique;
                    mBinding.tvCate0.setText(name);
                    mBinding.tvCate1.setText(childName);
                    maxGoods = 0;
                    getGoodsListLeft();
                });
                break;
            case R.id.linCate1:
                //二级分类
                CateDialog.showDialog(this, cateUnique, cateChildUnique, mBinding.ivCate1, 1, (unique, name, childUnique, childName) -> {
                    cateUnique = unique;
                    cateChildUnique = childUnique;
                    mBinding.tvCate0.setText(name);
                    mBinding.tvCate1.setText(childName);
                    maxGoods = 0;
                    getGoodsListLeft();
                });
                break;
        }
    }

    /**
     * 网络监听
     */
    private void setNetwork() {
        if (MyApplication.mNetwork != null) {
            if (MyApplication.mNetwork.isConnect()) {
                switch (MyApplication.mNetwork.getConnectType()) {
                    case 1:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network001);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 2:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network002);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 3:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network003);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    default:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                        mBinding.tvNetwork.setText(getRstr(R.string.no_network));
                        break;
                }
            } else {
                mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                mBinding.tvNetwork.setText(getRstr(R.string.no_network));
            }
        } else {
            mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
            mBinding.tvNetwork.setText(getRstr(R.string.no_network));
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //商品列表
        goodsAdapter = new GoodsCateAddAdapter(this);
        mBinding.rvGoods.setAdapter(goodsAdapter);
        goodsAdapter.setOnItemClickListener((view, position) -> {
            postCatePcGoodsEdit(goodsList.get(position).getGoods_barcode(), 1, 0);
        });
        mBinding.srlGoods.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                maxGoods = 0;
                getGoodsListLeft();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                maxGoods = 0;
                getGoodsListLeft();
            }
        });

        //虚拟分类
        cateAdapter = new CatePcAdapter(this);
        mBinding.rvCate.setAdapter(cateAdapter);
        cateAdapter.setOnItemClickListener((view, position) -> {
            if (!cateList.get(position).isCheck()) {
                for (int i = 0; i < cateList.size(); i++) {
                    if (cateList.get(i).isCheck()) {
                        cateList.get(i).setCheck(false);
                    }
                }
                posCate = position;
                cateList.get(position).setCheck(true);
                cateAdapter.setDataList(cateList);
                parentId = cateList.get(position).getGoodsKindInventedId();
                parentUnique = cateList.get(position).getGoods_kind_unique();
                mBinding.tvCate.setText(getRstr(R.string.add_goods_to_cate) + "-" + cateList.get(position).getGoods_kind_name());
                max = 0;
                getGoodsList();
            }
        });

        //虚拟分类下的商品列表
        mAdapter = new GoodsManageAdapter(this);
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new GoodsManageAdapter.MyListener() {
            @Override
            public void onItemClick(int position) {
                if (!dataList.get(position).isSelect()) {
                    for (int i = 0; i < dataList.size(); i++) {
                        if (dataList.get(i).isSelect()) {
                            dataList.get(i).setSelect(false);
                            mAdapter.notifyItemChanged(i);
                        }
                    }
                    dataList.get(position).setSelect(true);
                    mAdapter.notifyItemChanged(position);
                } else {
                    dataList.get(position).setSelect(false);
                    mAdapter.notifyItemChanged(position);
                }
            }

            @Override
            public void onDelClick(int position) {
                IAlertDialog.showDialog(CateAddGoodsActivity.this,
                        getRstr(R.string.confirm_del_goods_in_cate),
                        getRstr(R.string.confirm),
                        (dialog, which) -> {
                            postCatePcGoodsEdit(dataList.get(position).getGoods_barcode(), 0, position);
                        });
            }
        });
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                getGoodsList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                max = 0;
                getGoodsList();
            }
        });
    }

    /**
     * 获取商品分类数据
     */
    private void getCateList() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getCatePcList(),
                map,
                CatePcData.class,
                new RequestListListener<CatePcData>() {
                    @Override
                    public void onResult(List<CatePcData> list) {
                        if (LitePal.findFirst(CatePcData.class) != null) {
                            LitePal.deleteAll(CatePcData.class);
                        }
                        LitePal.saveAll(list);
                        cateList.clear();
                        cateList.addAll(list);
                        if (cateList.size() > posCate) {
                            cateList.get(posCate).setCheck(true);
                            cateAdapter.setDataList(cateList);
                            parentId = cateList.get(posCate).getGoodsKindInventedId();
                            parentUnique = cateList.get(posCate).getGoods_kind_unique();
                            max = 0;
                            getGoodsList();
                        }
                    }
                });
    }

    /**
     * 获取商品列表数据
     */
    private void getGoodsList() {
        hideSoftInput(this);
        mBinding.smartRefreshLayout.finishRefresh();
        mBinding.smartRefreshLayout.finishLoadMore();
        List<GoodsData> list;
        if (TextUtils.isEmpty(parentUnique)) {
            if (TextUtils.isEmpty(keyWords)) {
                list = LitePal
                        .limit(Constants.limit)
                        .offset(max)
                        .find(GoodsData.class);
            } else {
                list = LitePal
                        .limit(Constants.limit)
                        .offset(max)
                        .where("goods_name like ? or goods_barcode like ?", "%" + keyWords + "%", "%" + keyWords + "%")
                        .find(GoodsData.class);
            }
        } else {
            if (TextUtils.isEmpty(keyWords)) {
                list = LitePal
                        .limit(Constants.limit)
                        .offset(max)
                        .where("virtual_kind_unique = ?", parentUnique)
                        .find(GoodsData.class);
            } else {
                list = LitePal
                        .limit(Constants.limit)
                        .offset(max)
                        .where("virtual_kind_unique = ? and goods_name like ? or goods_barcode like ?", parentUnique, "%" + keyWords + "%", "%" + keyWords + "%")
                        .find(GoodsData.class);
            }
        }
        if (list != null) {
            if (max == 0) {
                dataList.clear();
            }
            dataList.addAll(list);
            max += dataList.size();
        }
        mAdapter.setDataList(dataList);
        mAdapter.setVirtual_kind_unique(parentUnique);
        if (dataList.isEmpty()) {
            mBinding.recyclerView.setVisibility(View.GONE);
            mBinding.linEmpty.setVisibility(View.VISIBLE);
        } else {
            mBinding.recyclerView.setVisibility(View.VISIBLE);
            mBinding.linEmpty.setVisibility(View.GONE);
        }
    }

    /**
     * 获取商品列表数据(左侧)
     */
    private void getGoodsListLeft() {
        mBinding.srlGoods.finishRefresh();
        mBinding.srlGoods.finishLoadMore();
        List<GoodsData> list;
        if (TextUtils.isEmpty(cateUnique)) {
            if (TextUtils.isEmpty(cateChildUnique)) {
                list = LitePal
                        .limit(Constants.limit)
                        .offset(maxGoods)
                        .find(GoodsData.class);
            } else {
                list = LitePal
                        .limit(Constants.limit)
                        .offset(maxGoods)
                        .where("goods_kind_unique = ?", cateChildUnique)
                        .find(GoodsData.class);
            }
        } else {
            if (TextUtils.isEmpty(cateChildUnique)) {
                list = LitePal
                        .limit(Constants.limit)
                        .offset(maxGoods)
                        .where("goods_kind_parunique = ?", cateUnique)
                        .find(GoodsData.class);
            } else {
                list = LitePal
                        .limit(Constants.limit)
                        .offset(maxGoods)
                        .where("goods_kind_parunique = ? and goods_kind_unique = ?", cateUnique, cateChildUnique)
                        .find(GoodsData.class);
            }
        }
        if (list != null) {
            if (maxGoods == 0) {
                goodsList.clear();
            }
            goodsList.addAll(list);
            maxGoods += goodsList.size();
        }
        if (!goodsList.isEmpty()) {
            mBinding.rvGoods.setVisibility(View.VISIBLE);
            mBinding.linEmptyGoods.setVisibility(View.GONE);
            goodsAdapter.setDataList(goodsList);
        } else {
            mBinding.rvGoods.setVisibility(View.GONE);
            mBinding.linEmptyGoods.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 删除或增加虚拟分类下的商品信息
     *
     * @param type 0.删除 1.增加
     */
    public void postCatePcGoodsEdit(String barcode, int type, int position) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("goodsKindUnique", parentUnique);
        map.put("goodskindInventedId", parentId);
        map.put("operateType", type);//0.删除 1.增加
        JSONArray array = new JSONArray();
        array.put(goodsList.get(position).getGoods_barcode());
        map.put("goodsList", array.toString());
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getCatePcGoodsEdit(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        GoodsData data = LitePal.where("goods_barcode = ?", barcode).findFirst(GoodsData.class);
                        if (type == 0) {
                            if (data != null) {
                                data.setVirtual_kind_unique("");
                                data.save();
                            }
                            if (dataList.size() > position) {
                                dataList.remove(position);
                                mAdapter.remove(position);
                                if (dataList.isEmpty()) {
                                    mBinding.recyclerView.setVisibility(View.GONE);
                                    mBinding.linEmpty.setVisibility(View.VISIBLE);
                                } else {
                                    mBinding.recyclerView.setVisibility(View.VISIBLE);
                                    mBinding.linEmpty.setVisibility(View.GONE);
                                }
                            }
                        } else {
                            if (data != null) {
                                data.setVirtual_kind_unique(parentUnique);
                                data.save();
                            }
                            getCateList();
                        }
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.CATE_LIST));
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }
}
