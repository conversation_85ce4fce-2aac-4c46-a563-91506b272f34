package com.yxl.cashier_retail.ui.presenter;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;

import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.ui.bean.RefundInfoData;
import com.yxl.cashier_retail.ui.bean.RefundData;
import com.yxl.cashier_retail.ui.contract.RefundContact;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:退款订单
 * Created by jingang on 2024/5/22
 */
public class RefundPresenter implements RefundContact.Presenter {
    private Context context;
    private RefundContact.View mView;

    public RefundPresenter(Context context) {
        this.context = context;
    }

    @Override
    public void attachView(@NonNull RefundContact.View view) {
        this.mView = view;
    }

    @Override
    public void detachView() {
        this.mView = null;
    }

    @Override
    public void getRefundList(String shopUnique, String startTime, String endTime, int page, int limit) {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", shopUnique);
        params.put("page", page);
        params.put("pageSize", limit);
        RXHttpUtil.requestByFormPostAsResponseList((LifecycleOwner) context,
                ZURL.getRefundList(),
                params,
                RefundData.class,
                new RequestListListener<RefundData>() {
                    @Override
                    public void onResult(List<RefundData> list) {
                        if (mView != null) {
                            mView.onListSuccess(list);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        if (mView != null) {
                            mView.onListError(msg);
                        }
                    }
                });
    }

    @Override
    public void getRefundInfo(String retListUnique) {
        Map<String, Object> params = new HashMap<>();
        params.put("retListUnique", retListUnique);
        RXHttpUtil.requestByFormPostAsResponse((LifecycleOwner) context,
                ZURL.getRefundInfo(),
                params,
                RefundInfoData.class,
                new RequestListener<RefundInfoData>() {
                    @Override
                    public void success(RefundInfoData data) {
                        if (mView != null) {
                            mView.onInfoSuccess(data);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        if (mView != null) {
                            mView.onInfoError(msg);
                        }
                    }
                });
    }
}