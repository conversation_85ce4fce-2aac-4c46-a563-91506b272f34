package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityMallGoodsBinding;
import com.yxl.cashier_retail.ui.adapter.MallGoodsAdapter;
import com.yxl.cashier_retail.ui.bean.MallGoodsData;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:商城-商品列表
 * Created by jingang on 2024/6/3
 */
@SuppressLint("NonConstantResourceId")
public class MallGoodsActivity extends BaseActivity<ActivityMallGoodsBinding> implements View.OnClickListener {
    private String cateUnique,//分类编号
            keyWords;

    private MallGoodsAdapter mAdapter;
    private List<MallGoodsData> dataList = new ArrayList<>();

    @Override
    protected ActivityMallGoodsBinding getViewBinding() {
        return ActivityMallGoodsBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivBack.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.ivSearchClear.setOnClickListener(this);
        mBinding.tvCredit.setOnClickListener(this);
        mBinding.tvOrder.setOnClickListener(this);
        mBinding.relCart.setOnClickListener(this);
        mBinding.tvCoupons.setOnClickListener(this);
        mBinding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                if (TextUtils.isEmpty(keyWords)) {
                    mBinding.ivSearchClear.setVisibility(View.GONE);
                } else {
                    mBinding.ivSearchClear.setVisibility(View.VISIBLE);
                }
            }
        });
        mBinding.etSearch.setOnEditorActionListener((v, actionId, event) -> {
            //
            return true;
        });
        setAdapter();
    }

    @Override
    protected void initData() {
        mBinding.tvTitle.setText(getIntent().getStringExtra("title"));
        cateUnique = getIntent().getStringExtra("unique");
        getGoodsList();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.tvCredit:
                //赊销
                break;
            case R.id.tvOrder:
                //订单
                break;
            case R.id.relCart:
                //购物车
                break;
            case R.id.tvCoupons:
                //优惠券
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new MallGoodsAdapter(this);
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {

        });
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                mBinding.smartRefreshLayout.finishLoadMore();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                mBinding.smartRefreshLayout.finishRefresh();
            }
        });
    }

    /**
     * 商品列表
     */
    private void getGoodsList() {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("area_dict_num", getAreaDictNum());
        params.put("page", page);
        params.put("limit", Constants.limit);
        params.put("sort_collection", "asc");
        params.put("class_id", cateUnique);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getMallGoodList(),
                params,
                MallGoodsData.class,
                new RequestListListener<MallGoodsData>() {
                    @Override
                    public void onResult(List<MallGoodsData> list) {
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                        if (page == 1) {
                            dataList.clear();
                        }
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }
}
