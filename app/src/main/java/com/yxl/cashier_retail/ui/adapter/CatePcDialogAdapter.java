package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.CatePcData;

/**
 * Describe:首页虚拟分类
 * Created by jingang on 2024/12/21
 */
public class CatePcDialogAdapter extends BaseAdapter<CatePcData> {

    public CatePcDialogAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_cate_pc_dialog;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName = holder.getView(R.id.tvItemName);
        ImageView ivDel = holder.getView(R.id.ivItemDel);

        tvName.setText(mDataList.get(position).getGoods_kind_name());
        if (mDataList.get(position).getGoods_kind_unique().equals("99990")) {
            ivDel.setVisibility(View.GONE);
        } else {
            ivDel.setVisibility(View.VISIBLE);
        }

        if (listener != null) {
            if (!mDataList.get(position).getGoods_kind_unique().equals("99990")) {
                tvName.setOnClickListener(v -> {
                    listener.onItemClick(position);
                });
            }
            ivDel.setOnClickListener(v -> {
                listener.onDelClick(position);
            });
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(int position);

        void onDelClick(int position);
    }
}
