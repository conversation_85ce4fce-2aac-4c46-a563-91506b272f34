package com.yxl.cashier_retail.ui.adapter;

/**
 * Describe:商城-商品详情-图片（实体类）
 * Created by jingang on 2024/6/4
 */
public class MallGoodsInfoImgData {
    private String url;
    private int index;

    public MallGoodsInfoImgData() {
    }

    public MallGoodsInfoImgData(String url, int index) {
        this.url = url;
        this.index = index;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }
}
