package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.StatisticsStaffData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:统计-员工统计（适配器）
 * Created by jingang on 2024/6/17
 */
public class StatisticsStaffAdapter extends BaseAdapter<StatisticsStaffData> {

    public StatisticsStaffAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_statistics_staff;
    }


    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvReal, tvFree, tvCash, tvTotal, tvDiscount, tvRefund, tvCount, tvCountRefund;
        tvName = holder.getView(R.id.tvItemName);
        tvReal = holder.getView(R.id.tvItemReal);
        tvFree = holder.getView(R.id.tvItemFree);
        tvCash = holder.getView(R.id.tvItemCash);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvDiscount = holder.getView(R.id.tvItemDiscount);
        tvRefund = holder.getView(R.id.tvItemRefund);
        tvCount = holder.getView(R.id.tvItemCount);
        tvCountRefund = holder.getView(R.id.tvItemCountRefund);

        tvName.setText(mDataList.get(position).getStaffName());
        tvReal.setText("￥" + DFUtils.getNum2(mDataList.get(position).getSaleListActuallyReceived()));
        tvFree.setText("￥" + DFUtils.getNum2(mDataList.get(position).getSecretFree()));
        tvCash.setText("￥" + DFUtils.getNum2(mDataList.get(position).getCash()));
        tvTotal.setText("￥" + DFUtils.getNum2(mDataList.get(position).getTurnover()));
        tvDiscount.setText("￥" + DFUtils.getNum2(mDataList.get(position).getDiscountAmount()));
        tvRefund.setText("￥" + DFUtils.getNum2(mDataList.get(position).getReturnAmount()));
        tvCount.setText(String.valueOf(mDataList.get(position).getSaleListCount()));
        tvCountRefund.setText(String.valueOf(mDataList.get(position).getReturnSaleListCount()));
    }
}
