package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:商品列表-添加商品到首页分类（适配器）
 * Created by jingang on 2024/12/30
 */
public class GoodsCateAddAdapter extends BaseAdapter<GoodsData> {
    public GoodsCateAddAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_cate_add;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivScale = holder.getView(R.id.ivItemScale);
        TextView tvName, tvBarcode, tvShofar,tvPrice, tvUnit, tvStock, tvHouse;
        tvName = holder.getView(R.id.tvItemName);
        tvBarcode = holder.getView(R.id.tvItemBarcode);
        tvShofar = holder.getView(R.id.tvItemShofar);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvUnit = holder.getView(R.id.tvItemUnit);
        tvStock = holder.getView(R.id.tvItemStock);
        tvHouse = holder.getView(R.id.tvItemHouse);

        //0.标品 1.称重
        if(mDataList.get(position).getGoodsChengType()==1){
            ivScale.setVisibility(View.VISIBLE);
            tvShofar.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.orange));
            tvPrice.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.orange));
            tvStock.setBackgroundResource(R.drawable.shape_orange_tm_left_2);
            tvHouse.setBackgroundResource(R.drawable.shape_orange_right_2);
        }else{
            ivScale.setVisibility(View.GONE);
            tvShofar.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvPrice.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvStock.setBackgroundResource(R.drawable.shape_green_tm_left_2);
            tvHouse.setBackgroundResource(R.drawable.shape_green_right_2);
        }
        tvName.setText(mDataList.get(position).getGoods_name());
        tvBarcode.setText(mDataList.get(position).getGoods_barcode());
        tvPrice.setText(DFUtils.getNum2(mDataList.get(position).getGoods_sale_price()));
        tvUnit.setText(TextUtils.isEmpty(mDataList.get(position).getGoods_unit()) ? "" : "/" + mDataList.get(position).getGoods_unit());
        tvStock.setText(DFUtils.getNum4(mDataList.get(position).getGoods_count()));
    }
}
