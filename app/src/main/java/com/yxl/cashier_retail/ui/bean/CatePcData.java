package com.yxl.cashier_retail.ui.bean;

import org.litepal.crud.LitePalSupport;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:虚拟分类列表（实体类）
 * Created by jingang on 2024/12/21
 */
public class CatePcData extends LitePalSupport implements Serializable {
    /**
     * goodsKindInventedId : 45
     * goods_kind_unique : 99990
     * goods_kind_name : 常用
     * goodsList : [{"goods_id":1543246048,"shop_unique":1536215939565,"goods_barcode":"10085"},{"goods_id":1543302949,"shop_unique":1536215939565,"goods_barcode":"3152291"}]
     */

    private boolean check;
    private boolean show;
    private int goodsKindInventedId;
    private String goods_kind_unique;
    private String goods_kind_name;
    private List<GoodsPcData> goodsList;

    public CatePcData() {
    }

    public CatePcData(String goods_kind_unique, String goods_kind_name) {
        this.goods_kind_unique = goods_kind_unique;
        this.goods_kind_name = goods_kind_name;
    }

    public boolean isCheck() {
        return check;
    }

    public void setCheck(boolean check) {
        this.check = check;
    }

    public boolean isShow() {
        return show;
    }

    public void setShow(boolean show) {
        this.show = show;
    }

    public int getGoodsKindInventedId() {
        return goodsKindInventedId;
    }

    public void setGoodsKindInventedId(int goodsKindInventedId) {
        this.goodsKindInventedId = goodsKindInventedId;
    }

    public String getGoods_kind_unique() {
        return goods_kind_unique;
    }

    public void setGoods_kind_unique(String goods_kind_unique) {
        this.goods_kind_unique = goods_kind_unique;
    }

    public String getGoods_kind_name() {
        return goods_kind_name;
    }

    public void setGoods_kind_name(String goods_kind_name) {
        this.goods_kind_name = goods_kind_name;
    }

    public List<GoodsPcData> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<GoodsPcData> goodsList) {
        this.goodsList = goodsList;
    }
}
