package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.CateData;
import com.yxl.commonlibrary.utils.StringUtils;

/**
 * Describe:首页一级分类（适配器）
 * Created by jingang on 2024/8/21
 */
public class CateHomeAdapter extends BaseAdapter<CateData> {

    public CateHomeAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_cate_home;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg, ivMore, ivEdit, ivDel, ivAdd;
        TextView tvName, tvDefault;
        ivImg = holder.getView(R.id.ivItemImg);
        tvName = holder.getView(R.id.tvItemName);
        ivMore = holder.getView(R.id.ivItemMore);
        tvDefault = holder.getView(R.id.tvItemDefault);
        ivEdit = holder.getView(R.id.ivItemEdit);
        ivDel = holder.getView(R.id.ivItemDel);
        ivAdd = holder.getView(R.id.ivItemAdd);
        RecyclerView rvCate = holder.getView(R.id.rvItemCate);

        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getKindIcon()))
                .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img).circleCrop())
                .into(ivImg);
        tvName.setText(mDataList.get(position).getGroupName());
        //是否可编辑，1、不可编辑；2、可编辑
        if (mDataList.get(position).getEditType() == 2) {
            tvDefault.setVisibility(View.GONE);
            ivEdit.setVisibility(View.VISIBLE);
            ivDel.setVisibility(View.VISIBLE);
            ivAdd.setVisibility(View.VISIBLE);
        } else {
            tvDefault.setVisibility(View.VISIBLE);
            ivEdit.setVisibility(View.GONE);
            ivDel.setVisibility(View.GONE);
            ivAdd.setVisibility(View.GONE);
        }
        if (mDataList.get(position).getKindDetail() != null) {
            if (mDataList.get(position).getKindDetail().size() > 0) {
                ivMore.setVisibility(View.VISIBLE);
                rvCate.setVisibility(View.VISIBLE);
                CateChildHomeAdapter adapter = new CateChildHomeAdapter(mContext);
                rvCate.setAdapter(adapter);
                adapter.setDataList(mDataList.get(position).getKindDetail());
                adapter.setListener(new CateChildHomeAdapter.MyListener() {
                    @Override
                    public void onEditClick(View view, int position1) {
                        if (listener != null) {
                            listener.onChildEditClick(view, position, position1);
                        }
                    }

                    @Override
                    public void onDelClick(View view, int position1) {
                        if (listener != null) {
                            listener.onChildDelClick(view, position, position1);
                        }
                    }
                });
            } else {
                ivMore.setVisibility(View.GONE);
                rvCate.setVisibility(View.GONE);
            }
        } else {
            ivMore.setVisibility(View.GONE);
            rvCate.setVisibility(View.GONE);
        }
        if (mDataList.get(position).isCheck()) {
            ivMore.setImageResource(R.mipmap.ic_arrow007);
            rvCate.setVisibility(View.VISIBLE);
        } else {
            ivMore.setImageResource(R.mipmap.ic_arrow006);
            rvCate.setVisibility(View.GONE);
        }

        if (listener != null) {
            ivMore.setOnClickListener(v -> listener.onMoreClick(v, position));
            ivEdit.setOnClickListener(v -> listener.onEditClick(v, position));
            ivDel.setOnClickListener(v -> listener.onDelClick(v, position));
            ivAdd.setOnClickListener(v -> listener.onAddClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onMoreClick(View view, int position);

        void onEditClick(View view, int position);

        void onDelClick(View view, int position);

        void onAddClick(View view, int position);

        void onChildEditClick(View view, int position, int positionChild);

        void onChildDelClick(View view, int position, int positionChild);
    }
}
