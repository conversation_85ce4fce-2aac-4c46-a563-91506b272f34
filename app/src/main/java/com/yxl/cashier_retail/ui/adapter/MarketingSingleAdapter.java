package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.ActivityListData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:营销活动-单品促销（适配器）
 * Created by jingang on 2024/6/1
 */
public class MarketingSingleAdapter extends BaseAdapter<ActivityListData.PromotionGoodsSingleListBean> {

    public MarketingSingleAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_marketing_single;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvBarcode, tvPrice, tvName0, tvName1, tvName2;
        tvName = holder.getView(R.id.tvItemName);
        tvBarcode = holder.getView(R.id.tvItemBarcode);
        tvPrice = holder.getView(R.id.tvItemPrice);

        tvName0 = holder.getView(R.id.tvItemName0);
        tvName1 = holder.getView(R.id.tvItemName1);
        tvName2 = holder.getView(R.id.tvItemName2);

        tvName.setText(TextUtils.isEmpty(mDataList.get(position).getGoodsName()) ? "-" : mDataList.get(position).getGoodsName());
        tvBarcode.setText(TextUtils.isEmpty(mDataList.get(position).getGoodsBarcode()) ? "(-)" : "(" + mDataList.get(position).getGoodsBarcode() + "）");
        tvPrice.setText(getRstr(R.string.price_old_colon) + DFUtils.getNum2(mDataList.get(position).getSalePrice()));

        if (mDataList.get(position).getNumber1() > 0) {
            tvName0.setText(getRstr(R.string.full) + mDataList.get(position).getNumber1() + getRstr(R.string.piece) + DFUtils.getNum2(mDataList.get(position).getDiscountPercent1()) + getRstr(R.string.discounts));
        } else {
            tvName0.setText("");
        }
        if (mDataList.get(position).getNumber2() > 0) {
            tvName1.setText(getRstr(R.string.full) + mDataList.get(position).getNumber2() + getRstr(R.string.piece) + DFUtils.getNum2(mDataList.get(position).getDiscountPercent2()) + getRstr(R.string.discounts));
        } else {
            tvName1.setText("");
        }
        if (mDataList.get(position).getNumber3() > 0) {
            tvName2.setText(getRstr(R.string.full) + mDataList.get(position).getNumber3() + getRstr(R.string.piece) + DFUtils.getNum2(mDataList.get(position).getDiscountPercent3()) + getRstr(R.string.discounts));
        } else {
            tvName2.setText("");
        }
    }
}
