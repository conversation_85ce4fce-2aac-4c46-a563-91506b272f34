package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 商城-商品详情（实体类）
 */
public class MallGoodsInfoData implements Serializable {
    private CardBean cord;
    private DataBean data;
    private String msg;
    private int status;

    public CardBean getCord() {
        return cord;
    }

    public void setCord(CardBean cord) {
        this.cord = cord;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public static class CardBean {
        private int good_count;//购物车数量

        public int getGood_count() {
            return good_count;
        }

        public void setGood_count(int good_count) {
            this.good_count = good_count;
        }
    }

    public static class DataBean implements Serializable {
        private int goods_id;//商品id
        private String company_name;//供货商名称
        private String goodsunit_name;//单位
        private int stock_count;//库存
        // 商品条码
        private String goods_barcode;
        // 商品名字
        private String goods_name;
        // 金圈币抵扣
        private double gold_deduct;
        // 起订量
        private int start_order;
        // 售价
        private String wholesale_price;
        // 供货商编号
        private String company_code;
        // 商品图片
        private String goods_img;
        // 商城售价
        private double online_price;
        // 月销量
        private int sale_count;
        // 负库存销售 1允许（默认）2不允许
        private int auto_fxiaoshou;
        // 生产日期
        private String production_date;
        // 发货日期
        private String deliverable_date;
        // 第二单位名
        private String sub_unitname;
        // 商品简介
        private String goods_resume;
        // 详情
        private String goods_detail;
        // 换算
        private int proportion_num;
        // 规格id
        private int specs_id;
        // 规格名
        private String spec_name;
        // 配送费方式： 2按订单 1按商品件数
        private int delivery_price_type;
        // 订单起送价格
        private double send_price;
        // 订单免配送费价格
        private double free_delivery_price;
        // 聚宝盆 Y 是（不可用优惠券） N否
        private String cornucopia;
        // 轮播图
        private List<String> fileList = new ArrayList<>();
        // 商品视频介绍
        private String video_url;
        // 净含量
        private String weight;
        // 小类
        private String class_small_name;
        // 大类
        private String class_big_name;
        // 品牌名称
        private String brand_name;
        // 保质期
        private String quality_period;
        // 是否 参与活动（活动无法重复参加） 0 -无 ；1-限购 ；2 捆绑 ； 5.降价活动 -（如：硬核补贴） 6、阶梯价，7、预售
        private int is_activity;
        private Xianggou xianggou;//限购
        private Butie butie;//补贴类活动（如：硬核补贴、双十一）
        private List<Bind> bindList;//2.捆绑
        private List<Coupon> couponList;//优惠券
        private List<Fullgift> fullgift;//满赠
        private List<GoodSpec> goodSpecList;
        //预售时，表示预售价格
        private String activity_price;
        //is_activity 为7时，预售活动开始时间
        private String startTime;
        //is_activity 为7时，预售活动结束时间
        private String endTime;
        //is_activity 为7时，预售活动开始时间戳
        private long startTimeLong;
        //is_activity 为7时，预售活动结束时间戳
        private long endTimeLong;
        //is_activity 为7时，商品配送时间
        private String deliveryTime;
        //预购商品已预购数量
        private int book_num;
        //商户自定义的预售规则
        private String rich_text;
        //筐押金
        private double bucket_deposit;
        //商品阶梯价列表
        private List<StepPriceListBean> stepPriceList;

        public int getGoods_id() {
            return goods_id;
        }

        public void setGoods_id(int goods_id) {
            this.goods_id = goods_id;
        }

        public String getCompany_name() {
            return company_name;
        }

        public void setCompany_name(String company_name) {
            this.company_name = company_name;
        }

        public String getGoodsunit_name() {
            return goodsunit_name;
        }

        public void setGoodsunit_name(String goodsunit_name) {
            this.goodsunit_name = goodsunit_name;
        }

        public int getStock_count() {
            return stock_count;
        }

        public void setStock_count(int stock_count) {
            this.stock_count = stock_count;
        }

        public String getGoods_barcode() {
            return goods_barcode;
        }

        public void setGoods_barcode(String goods_barcode) {
            this.goods_barcode = goods_barcode;
        }

        public String getGoods_name() {
            return goods_name;
        }

        public void setGoods_name(String goods_name) {
            this.goods_name = goods_name;
        }

        public double getGold_deduct() {
            return gold_deduct;
        }

        public void setGold_deduct(double gold_deduct) {
            this.gold_deduct = gold_deduct;
        }

        public int getStart_order() {
            return start_order;
        }

        public void setStart_order(int start_order) {
            this.start_order = start_order;
        }

        public String getWholesale_price() {
            return wholesale_price;
        }

        public void setWholesale_price(String wholesale_price) {
            this.wholesale_price = wholesale_price;
        }

        public String getCompany_code() {
            return company_code;
        }

        public void setCompany_code(String company_code) {
            this.company_code = company_code;
        }

        public String getGoods_img() {
            return goods_img;
        }

        public void setGoods_img(String goods_img) {
            this.goods_img = goods_img;
        }

        public double getOnline_price() {
            return online_price;
        }

        public void setOnline_price(double online_price) {
            this.online_price = online_price;
        }

        public int getSale_count() {
            return sale_count;
        }

        public void setSale_count(int sale_count) {
            this.sale_count = sale_count;
        }

        public int getAuto_fxiaoshou() {
            return auto_fxiaoshou;
        }

        public void setAuto_fxiaoshou(int auto_fxiaoshou) {
            this.auto_fxiaoshou = auto_fxiaoshou;
        }

        public String getProduction_date() {
            return production_date;
        }

        public void setProduction_date(String production_date) {
            this.production_date = production_date;
        }

        public String getDeliverable_date() {
            return deliverable_date;
        }

        public void setDeliverable_date(String deliverable_date) {
            this.deliverable_date = deliverable_date;
        }

        public String getSub_unitname() {
            return sub_unitname;
        }

        public void setSub_unitname(String sub_unitname) {
            this.sub_unitname = sub_unitname;
        }

        public String getGoods_resume() {
            return goods_resume;
        }

        public void setGoods_resume(String goods_resume) {
            this.goods_resume = goods_resume;
        }

        public String getGoods_detail() {
            return goods_detail;
        }

        public void setGoods_detail(String goods_detail) {
            this.goods_detail = goods_detail;
        }

        public int getProportion_num() {
            return proportion_num;
        }

        public void setProportion_num(int proportion_num) {
            this.proportion_num = proportion_num;
        }

        public int getSpecs_id() {
            return specs_id;
        }

        public void setSpecs_id(int specs_id) {
            this.specs_id = specs_id;
        }

        public String getSpec_name() {
            return spec_name;
        }

        public void setSpec_name(String spec_name) {
            this.spec_name = spec_name;
        }

        public int getDelivery_price_type() {
            return delivery_price_type;
        }

        public void setDelivery_price_type(int delivery_price_type) {
            this.delivery_price_type = delivery_price_type;
        }

        public double getSend_price() {
            return send_price;
        }

        public void setSend_price(double send_price) {
            this.send_price = send_price;
        }

        public double getFree_delivery_price() {
            return free_delivery_price;
        }

        public void setFree_delivery_price(double free_delivery_price) {
            this.free_delivery_price = free_delivery_price;
        }

        public String getCornucopia() {
            return cornucopia;
        }

        public void setCornucopia(String cornucopia) {
            this.cornucopia = cornucopia;
        }

        public List<String> getFileList() {
            return fileList;
        }

        public void setFileList(List<String> fileList) {
            this.fileList = fileList;
        }

        public String getVideo_url() {
            return video_url;
        }

        public void setVideo_url(String video_url) {
            this.video_url = video_url;
        }

        public String getWeight() {
            return weight;
        }

        public void setWeight(String weight) {
            this.weight = weight;
        }

        public String getClass_small_name() {
            return class_small_name;
        }

        public void setClass_small_name(String class_small_name) {
            this.class_small_name = class_small_name;
        }

        public String getClass_big_name() {
            return class_big_name;
        }

        public void setClass_big_name(String class_big_name) {
            this.class_big_name = class_big_name;
        }

        public String getBrand_name() {
            return brand_name;
        }

        public void setBrand_name(String brand_name) {
            this.brand_name = brand_name;
        }

        public String getQuality_period() {
            return quality_period;
        }

        public void setQuality_period(String quality_period) {
            this.quality_period = quality_period;
        }

        public int getIs_activity() {
            return is_activity;
        }

        public void setIs_activity(int is_activity) {
            this.is_activity = is_activity;
        }

        public Xianggou getXianggou() {
            return xianggou;
        }

        public void setXianggou(Xianggou xianggou) {
            this.xianggou = xianggou;
        }

        public Butie getButie() {
            return butie;
        }

        public void setButie(Butie butie) {
            this.butie = butie;
        }

        public List<Bind> getBindList() {
            return bindList;
        }

        public void setBindList(List<Bind> bindList) {
            this.bindList = bindList;
        }

        public List<Coupon> getCouponList() {
            return couponList;
        }

        public void setCouponList(List<Coupon> couponList) {
            this.couponList = couponList;
        }

        public List<Fullgift> getFullgift() {
            return fullgift;
        }

        public void setFullgift(List<Fullgift> fullgift) {
            this.fullgift = fullgift;
        }

        public List<GoodSpec> getGoodSpecList() {
            return goodSpecList;
        }

        public void setGoodSpecList(List<GoodSpec> goodSpecList) {
            this.goodSpecList = goodSpecList;
        }

        public String getActivity_price() {
            return activity_price;
        }

        public void setActivity_price(String activity_price) {
            this.activity_price = activity_price;
        }

        public String getStartTime() {
            return startTime;
        }

        public void setStartTime(String startTime) {
            this.startTime = startTime;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }

        public long getStartTimeLong() {
            return startTimeLong;
        }

        public void setStartTimeLong(long startTimeLong) {
            this.startTimeLong = startTimeLong;
        }

        public long getEndTimeLong() {
            return endTimeLong;
        }

        public void setEndTimeLong(long endTimeLong) {
            this.endTimeLong = endTimeLong;
        }

        public String getDeliveryTime() {
            return deliveryTime;
        }

        public void setDeliveryTime(String deliveryTime) {
            this.deliveryTime = deliveryTime;
        }

        public int getBook_num() {
            return book_num;
        }

        public void setBook_num(int book_num) {
            this.book_num = book_num;
        }

        public String getRich_text() {
            return rich_text;
        }

        public void setRich_text(String rich_text) {
            this.rich_text = rich_text;
        }

        public double getBucket_deposit() {
            return bucket_deposit;
        }

        public void setBucket_deposit(double bucket_deposit) {
            this.bucket_deposit = bucket_deposit;
        }

        public List<StepPriceListBean> getStepPriceList() {
            return stepPriceList;
        }

        public void setStepPriceList(List<StepPriceListBean> stepPriceList) {
            this.stepPriceList = stepPriceList;
        }

        /**
         * 限购
         */
        public static class Xianggou implements Serializable {
            // 限购选项（1：每天，2：每周，3：每月，4：每季度，5：每年）
            private int cycle;
            // 限购次数
            private int frequency;
            // 限购数量
            private int quotaNumber;

            public int getCycle() {
                return cycle;
            }

            public void setCycle(int cycle) {
                this.cycle = cycle;
            }

            public int getFrequency() {
                return frequency;
            }

            public void setFrequency(int frequency) {
                this.frequency = frequency;
            }

            public int getQuotaNumber() {
                return quotaNumber;
            }

            public void setQuotaNumber(int quotaNumber) {
                this.quotaNumber = quotaNumber;
            }
        }

        /**
         * 补贴活动（如硬核补贴、双十一等）
         */
        public static class Butie implements Serializable {
            // 1 售罄 2 活动中
            private int isSoldOut;
            // 直降金额
            private String preAmount;
            // 到手价格
            private String promotion_price;

            public int getIsSoldOut() {
                return isSoldOut;
            }

            public void setIsSoldOut(int isSoldOut) {
                this.isSoldOut = isSoldOut;
            }

            public String getPreAmount() {
                return preAmount;
            }

            public void setPreAmount(String preAmount) {
                this.preAmount = preAmount;
            }

            public String getPromotion_price() {
                return promotion_price;
            }

            public void setPromotion_price(String promotion_price) {
                this.promotion_price = promotion_price;
            }
        }

        public static class Bind implements Serializable {
            // 商品id
            private int goods_id;
            // 商品名称
            private String goods_name;
            // 商品图片
            private String goods_img;
            // 金圈币抵扣
            private double gold_deduct;
            // 起订量
            private int start_order;
            // 商城售价
            private double online_price;
            // 配送时间
            private String deliverable_date;
            // 供货商名称
            private String company_name;
            // 单位
            private String goodsunit_name;

            public int getGoods_id() {
                return goods_id;
            }

            public void setGoods_id(int goods_id) {
                this.goods_id = goods_id;
            }

            public String getGoods_name() {
                return goods_name;
            }

            public void setGoods_name(String goods_name) {
                this.goods_name = goods_name;
            }

            public String getGoods_img() {
                return goods_img;
            }

            public void setGoods_img(String goods_img) {
                this.goods_img = goods_img;
            }

            public double getGold_deduct() {
                return gold_deduct;
            }

            public void setGold_deduct(double gold_deduct) {
                this.gold_deduct = gold_deduct;
            }

            public int getStart_order() {
                return start_order;
            }

            public void setStart_order(int start_order) {
                this.start_order = start_order;
            }

            public double getOnline_price() {
                return online_price;
            }

            public void setOnline_price(double online_price) {
                this.online_price = online_price;
            }

            public String getDeliverable_date() {
                return deliverable_date;
            }

            public void setDeliverable_date(String deliverable_date) {
                this.deliverable_date = deliverable_date;
            }

            public String getCompany_name() {
                return company_name;
            }

            public void setCompany_name(String company_name) {
                this.company_name = company_name;
            }

            public String getGoodsunit_name() {
                return goodsunit_name;
            }

            public void setGoodsunit_name(String goodsunit_name) {
                this.goodsunit_name = goodsunit_name;
            }
        }

        /**
         * 优惠券
         */
        public static class Coupon implements Serializable {
            private String company_name;
            private String compay_code;
            private double coupon_amount;//优惠金额
            private int coupon_id;
            private String end_date;
            private String limit_quantity_type;
            private double meet_amount;//满足金额
            private String overdue_status;
            private String record_id;
            private String start_date;
            private int total_distribution;
            private int total_surplus;
            private String usage_status;

            public String getCompany_name() {
                return company_name;
            }

            public void setCompany_name(String company_name) {
                this.company_name = company_name;
            }

            public String getCompay_code() {
                return compay_code;
            }

            public void setCompay_code(String compay_code) {
                this.compay_code = compay_code;
            }

            public double getCoupon_amount() {
                return coupon_amount;
            }

            public void setCoupon_amount(double coupon_amount) {
                this.coupon_amount = coupon_amount;
            }

            public int getCoupon_id() {
                return coupon_id;
            }

            public void setCoupon_id(int coupon_id) {
                this.coupon_id = coupon_id;
            }

            public String getEnd_date() {
                return end_date;
            }

            public void setEnd_date(String end_date) {
                this.end_date = end_date;
            }

            public String getLimit_quantity_type() {
                return limit_quantity_type;
            }

            public void setLimit_quantity_type(String limit_quantity_type) {
                this.limit_quantity_type = limit_quantity_type;
            }

            public double getMeet_amount() {
                return meet_amount;
            }

            public void setMeet_amount(double meet_amount) {
                this.meet_amount = meet_amount;
            }

            public String getOverdue_status() {
                return overdue_status;
            }

            public void setOverdue_status(String overdue_status) {
                this.overdue_status = overdue_status;
            }

            public String getRecord_id() {
                return record_id;
            }

            public void setRecord_id(String record_id) {
                this.record_id = record_id;
            }

            public String getStart_date() {
                return start_date;
            }

            public void setStart_date(String start_date) {
                this.start_date = start_date;
            }

            public int getTotal_distribution() {
                return total_distribution;
            }

            public void setTotal_distribution(int total_distribution) {
                this.total_distribution = total_distribution;
            }

            public int getTotal_surplus() {
                return total_surplus;
            }

            public void setTotal_surplus(int total_surplus) {
                this.total_surplus = total_surplus;
            }

            public String getUsage_status() {
                return usage_status;
            }

            public void setUsage_status(String usage_status) {
                this.usage_status = usage_status;
            }
        }

        /**
         * 满赠
         */
        public static class Fullgift implements Serializable {
            private String compay_code;
            private String end_date;
            private List<GiftGood> couponList;
            private List<GiftGood> goodList;
            private int gift_id;
            private double meet_amount;
            private String start_date;

            public String getCompay_code() {
                return compay_code;
            }

            public void setCompay_code(String compay_code) {
                this.compay_code = compay_code;
            }

            public String getEnd_date() {
                return end_date;
            }

            public void setEnd_date(String end_date) {
                this.end_date = end_date;
            }

            public List<GiftGood> getCouponList() {
                return couponList;
            }

            public void setCouponList(List<GiftGood> couponList) {
                this.couponList = couponList;
            }

            public List<GiftGood> getGoodList() {
                return goodList;
            }

            public void setGoodList(List<GiftGood> goodList) {
                this.goodList = goodList;
            }

            public int getGift_id() {
                return gift_id;
            }

            public void setGift_id(int gift_id) {
                this.gift_id = gift_id;
            }

            public double getMeet_amount() {
                return meet_amount;
            }

            public void setMeet_amount(double meet_amount) {
                this.meet_amount = meet_amount;
            }

            public String getStart_date() {
                return start_date;
            }

            public void setStart_date(String start_date) {
                this.start_date = start_date;
            }

            public static class GiftGood implements Serializable {
                /*商品*/
                private String free_quantity;
                private String goods_barcode;
                private int goods_id;
                private String goods_img;
                private String goods_name;

                /*优惠券*/
                private double coupon_amount;
                private int coupon_id;
                private String coupon_img;
                private double meet_amount;

                public String getFree_quantity() {
                    return free_quantity;
                }

                public void setFree_quantity(String free_quantity) {
                    this.free_quantity = free_quantity;
                }

                public String getGoods_barcode() {
                    return goods_barcode;
                }

                public void setGoods_barcode(String goods_barcode) {
                    this.goods_barcode = goods_barcode;
                }

                public int getGoods_id() {
                    return goods_id;
                }

                public void setGoods_id(int goods_id) {
                    this.goods_id = goods_id;
                }

                public String getGoods_img() {
                    return goods_img;
                }

                public void setGoods_img(String goods_img) {
                    this.goods_img = goods_img;
                }

                public String getGoods_name() {
                    return goods_name;
                }

                public void setGoods_name(String goods_name) {
                    this.goods_name = goods_name;
                }

                public double getCoupon_amount() {
                    return coupon_amount;
                }

                public void setCoupon_amount(double coupon_amount) {
                    this.coupon_amount = coupon_amount;
                }

                public int getCoupon_id() {
                    return coupon_id;
                }

                public void setCoupon_id(int coupon_id) {
                    this.coupon_id = coupon_id;
                }

                public String getCoupon_img() {
                    return coupon_img;
                }

                public void setCoupon_img(String coupon_img) {
                    this.coupon_img = coupon_img;
                }

                public double getMeet_amount() {
                    return meet_amount;
                }

                public void setMeet_amount(double meet_amount) {
                    this.meet_amount = meet_amount;
                }
            }
        }

        public static class GoodSpec implements Serializable {
            private String available_stock_count;
            private String company_code;
            private int compose_specs_id;
            private int dataFlag;
            private String end_time;
            private long goods_barcode;
            private String goods_count;
            private int goods_id;
            private int id;
            private double online_price;
            private double price;
            private double promotion_count;
            private double promotion_price;
            private int promotion_valid;
            private String spec_name;
            private int specs_id;
            private String start_time;

            public String getAvailable_stock_count() {
                return available_stock_count;
            }

            public void setAvailable_stock_count(String available_stock_count) {
                this.available_stock_count = available_stock_count;
            }

            public String getCompany_code() {
                return company_code;
            }

            public void setCompany_code(String company_code) {
                this.company_code = company_code;
            }

            public int getCompose_specs_id() {
                return compose_specs_id;
            }

            public void setCompose_specs_id(int compose_specs_id) {
                this.compose_specs_id = compose_specs_id;
            }

            public int getDataFlag() {
                return dataFlag;
            }

            public void setDataFlag(int dataFlag) {
                this.dataFlag = dataFlag;
            }

            public String getEnd_time() {
                return end_time;
            }

            public void setEnd_time(String end_time) {
                this.end_time = end_time;
            }

            public long getGoods_barcode() {
                return goods_barcode;
            }

            public void setGoods_barcode(long goods_barcode) {
                this.goods_barcode = goods_barcode;
            }

            public String getGoods_count() {
                return goods_count;
            }

            public void setGoods_count(String goods_count) {
                this.goods_count = goods_count;
            }

            public int getGoods_id() {
                return goods_id;
            }

            public void setGoods_id(int goods_id) {
                this.goods_id = goods_id;
            }

            public int getId() {
                return id;
            }

            public void setId(int id) {
                this.id = id;
            }

            public double getOnline_price() {
                return online_price;
            }

            public void setOnline_price(double online_price) {
                this.online_price = online_price;
            }

            public double getPrice() {
                return price;
            }

            public void setPrice(double price) {
                this.price = price;
            }

            public double getPromotion_count() {
                return promotion_count;
            }

            public void setPromotion_count(double promotion_count) {
                this.promotion_count = promotion_count;
            }

            public double getPromotion_price() {
                return promotion_price;
            }

            public void setPromotion_price(double promotion_price) {
                this.promotion_price = promotion_price;
            }

            public int getPromotion_valid() {
                return promotion_valid;
            }

            public void setPromotion_valid(int promotion_valid) {
                this.promotion_valid = promotion_valid;
            }

            public String getSpec_name() {
                return spec_name;
            }

            public void setSpec_name(String spec_name) {
                this.spec_name = spec_name;
            }

            public int getSpecs_id() {
                return specs_id;
            }

            public void setSpecs_id(int specs_id) {
                this.specs_id = specs_id;
            }

            public String getStart_time() {
                return start_time;
            }

            public void setStart_time(String start_time) {
                this.start_time = start_time;
            }
        }

        /**
         * 阶梯价
         */
        public static class StepPriceListBean implements Serializable {
            private double activity_price;//价格
            private int goods_count;//数量

            public double getActivity_price() {
                return activity_price;
            }

            public void setActivity_price(double activity_price) {
                this.activity_price = activity_price;
            }

            public int getGoods_count() {
                return goods_count;
            }

            public void setGoods_count(int goods_count) {
                this.goods_count = goods_count;
            }
        }
    }
}
