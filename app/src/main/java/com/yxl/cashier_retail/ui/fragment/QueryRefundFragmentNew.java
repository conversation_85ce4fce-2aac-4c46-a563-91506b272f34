package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.graphics.Typeface;
import android.view.View;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmQueryRefundNewBinding;
import com.yxl.cashier_retail.ui.adapter.OrderGoodsAdapter;
import com.yxl.cashier_retail.ui.adapter.QueryRefundAdapter;
import com.yxl.cashier_retail.ui.bean.OrderGoodsData;
import com.yxl.cashier_retail.ui.bean.QueryRefundData;
import com.yxl.cashier_retail.ui.bean.RefundInfoData;
import com.yxl.cashier_retail.ui.dialog.DateStartEndDialog;
import com.yxl.cashier_retail.ui.dialog.StaffDialog;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.utils.DateUtils;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:查询-查退款
 * Created by jingang on 2024/5/22
 */
@SuppressLint({"NonConstantResourceId", "NotifyDataSetChanged"})
public class QueryRefundFragmentNew extends BaseFragment<FmQueryRefundNewBinding> implements View.OnClickListener {
    private boolean isOrder;//是否为订单信息
    private String staffName, startDate, endDate, keyWords, saleListUnique;
    private int staffId;

    //退款订单列表
    private QueryRefundAdapter mAdapter;
    private List<QueryRefundData.ListBean> dataList = new ArrayList<>();

    //商品列表
    private OrderGoodsAdapter goodsAdapter;
    private List<OrderGoodsData> goodsList = new ArrayList<>();

    @Override
    protected FmQueryRefundNewBinding getViewBinding() {
        return FmQueryRefundNewBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.linType.setOnClickListener(this);
        mBinding.linStartDate.setOnClickListener(this);
        mBinding.linEndDate.setOnClickListener(this);

        mBinding.tvGoods.setOnClickListener(this);
        mBinding.tvOrder.setOnClickListener(this);
        mBinding.tvPrint.setOnClickListener(this);

        setAdapter();
    }

    @Override
    protected void initData() {
        startDate = DateUtils.getOldDate(-3);
        endDate = DateUtils.getOldDate(0);
        mBinding.tvStartDate.setText(startDate);
        mBinding.tvEndDate.setText(endDate);
        mBinding.vSmartrefreshlayout.smartRefreshLayout.autoRefresh();
    }

    @Override
    public void onClick(View v) {
        if (isQuicklyClick()) {
            return;
        }
        switch (v.getId()) {
            case R.id.linType:
                //操作人
                StaffDialog.showDialog(getActivity(), staffId, staffName, mBinding.ivType, (id, name) -> {
                    staffId = id;
                    staffName = name;
                    mBinding.tvType.setText(staffName);
                    mBinding.vSmartrefreshlayout.smartRefreshLayout.autoRefresh();
                });
                break;
            case R.id.linStartDate:
                //开始日期
                showDialogDate(0, mBinding.ivStartDate);
                break;
            case R.id.linEndDate:
                //结束日期
                showDialogDate(1, mBinding.ivEndDate);
                break;
            case R.id.tvGoods:
                //商品信息
                if (isOrder) {
                    isOrder = false;
                    mBinding.tvGoods.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvGoods.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvGoods.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.linGoods.setVisibility(View.VISIBLE);
                    mBinding.tvOrder.setBackgroundResource(0);
                    mBinding.tvOrder.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvOrder.setTypeface(Typeface.DEFAULT);
                    mBinding.svOrder.setVisibility(View.GONE);
                }
                break;
            case R.id.tvOrder:
                //订单信息
                if (!isOrder) {
                    isOrder = true;
                    mBinding.tvGoods.setBackgroundResource(0);
                    mBinding.tvGoods.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvGoods.setTypeface(Typeface.DEFAULT);
                    mBinding.linGoods.setVisibility(View.GONE);
                    mBinding.tvOrder.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvOrder.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvOrder.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.svOrder.setVisibility(View.VISIBLE);
                }
                break;
            case R.id.tvPrint:
                //打印小票
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //订单列表
        mAdapter = new QueryRefundAdapter(getActivity());
        mBinding.vSmartrefreshlayout.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (!dataList.get(position).isSelect()) {
                for (int i = 0; i < dataList.size(); i++) {
                    if (dataList.get(i).isSelect()) {
                        dataList.get(i).setSelect(false);
                    }
                }
                dataList.get(position).setSelect(true);
                mAdapter.setDataList(dataList);
                saleListUnique = dataList.get(position).getReturnSaleListUnique();
                getRefundInfo();
            }
        });

        mBinding.vSmartrefreshlayout.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getRefundList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getRefundList();
            }
        });

        //商品列表
        goodsAdapter = new OrderGoodsAdapter(getActivity(), 1);
        mBinding.rvGoods.setAdapter(goodsAdapter);
    }

    /**
     * 更新UI
     * @param data
     */
    @SuppressLint("SetTextI18n")
    private void setUI(RefundInfoData data) {
        if (data == null) {
            mBinding.linInfo.setVisibility(View.GONE);
            mBinding.tvNothing.setVisibility(View.VISIBLE);
            return;
        }
        mBinding.linInfo.setVisibility(View.VISIBLE);
        mBinding.tvNothing.setVisibility(View.GONE);

        //订单信息
        mBinding.tvOrderNo.setText(data.getSaleListUnique());
        mBinding.tvOrderMoney.setText(DFUtils.getNum2(data.getSaleListTotal()));
        mBinding.tvRefundMoney.setText(DFUtils.getNum2(data.getRetListTotalMoney()));
        mBinding.tvRefundType.setText(data.getRetMoneyTypeMsg());
        mBinding.tvOperator.setText(String.valueOf(data.getStaffId()));
        mBinding.tvOperateEnd.setText(data.getRetOriginMsg());
        mBinding.tvRefundNo.setText(data.getRetListUnique());
        mBinding.tvRefundTime.setText(data.getRetListDatetime());

        //商品列表
        goodsList.clear();
        if (data.getDetailList() != null) {
            goodsList.addAll(data.getDetailList());
        }
        if (goodsList.size() > 0) {
            mBinding.rvGoods.setVisibility(View.VISIBLE);
            goodsAdapter.setDataList(goodsList);
        } else {
            mBinding.rvGoods.setVisibility(View.GONE);
        }
        mBinding.tvRefund.setText(getRstr(R.string.refund_colon) + DFUtils.getNum2(data.getRetListTotal()));
    }

    /**
     * dialog（选择日期时间）
     */
    private void showDialogDate(int type, View view) {
        DateStartEndDialog.showDialog(getActivity(),
                view,
                startDate,
                endDate,
                type,
                (startDate, endDate) -> {
                    this.startDate = startDate;
                    this.endDate = endDate;
                    mBinding.tvStartDate.setText(startDate);
                    mBinding.tvEndDate.setText(endDate);
                    //请求数据
                    mBinding.vSmartrefreshlayout.smartRefreshLayout.autoRefresh();
                });
    }

    /**
     * 退款列表
     */
    private void getRefundList() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        if (staffId != 0) {
            params.put("staffId", staffId);
        }
        params.put("message", keyWords);
        params.put("startDate", startDate + " 00:00:00");
        params.put("endDate", endDate + " 23:59:59");
        params.put("pageIndex", page);
        params.put("pageSize", Constants.limit);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(getActivity(),
                ZURL.getQueryRefund(),
                params,
                QueryRefundData.class,
                new RequestListener<QueryRefundData>() {
                    @Override
                    public void success(QueryRefundData data) {
                        hideDialog();
                        mBinding.vSmartrefreshlayout.smartRefreshLayout.finishRefresh();
                        mBinding.vSmartrefreshlayout.smartRefreshLayout.finishLoadMore();
                        if (page == 1) {
                            dataList.clear();
                            mAdapter.clear();
                        }
                        if (!data.getList().isEmpty()) {
                            data.getList().get(0).setSelect(true);
                            saleListUnique = data.getList().get(0).getReturnSaleListUnique();
                            getRefundInfo();
                        }
                        mBinding.tvRefundTotal.setText(DFUtils.getNum2(data.getTotalAmount()));
                        dataList.addAll(data.getList());
                        mAdapter.addAll(data.getList());
                        if (dataList.isEmpty()) {
                            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.GONE);
                            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.VISIBLE);
                        } else {
                            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.GONE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.vSmartrefreshlayout.smartRefreshLayout.finishRefresh();
                        mBinding.vSmartrefreshlayout.smartRefreshLayout.finishLoadMore();
                    }
                });
    }

    /**
     * 退款订单详情
     */
    private void getRefundInfo() {
        Map<String, Object> params = new HashMap<>();
        params.put("retListUnique", saleListUnique);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(getActivity(),
                ZURL.getRefundInfo(),
                params,
                RefundInfoData.class,
                new RequestListener<RefundInfoData>() {
                    @Override
                    public void success(RefundInfoData data) {
                        hideDialog();
                        setUI(data);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        setUI(null);
                    }
                });
    }

}
