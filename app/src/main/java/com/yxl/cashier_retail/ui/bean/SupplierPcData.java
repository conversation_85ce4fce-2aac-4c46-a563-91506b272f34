package com.yxl.cashier_retail.ui.bean;

/**
 * Describe: 供货商（实体类）
 * Created by jingang on 2025/6/7
 */
public class SupplierPcData {
    /**
     * shop_unique : 1536215939565
     * supplier_unique : 1749264056070279330
     * company_leagl : 嘿嘿
     * supplier_name : 嘿嘿
     * supplier_kind_id : 195
     * supplier_id : 1561
     * supplier_kind_name : 未分类
     * supplier_address : 山东省临沂市兰山区兰山街道沂蒙路37号书院小区
     * supplier_phone : 666666
     */
    private boolean select;
    private String shop_unique;
    private String supplier_unique;
    private String company_leagl;
    private String supplier_name;
    private int supplier_kind_id;
    private int supplier_id;
    private String supplier_kind_name;
    private String supplier_address;
    private String supplier_phone;

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }

    public String getShop_unique() {
        return shop_unique;
    }

    public void setShop_unique(String shop_unique) {
        this.shop_unique = shop_unique;
    }

    public String getSupplier_unique() {
        return supplier_unique;
    }

    public void setSupplier_unique(String supplier_unique) {
        this.supplier_unique = supplier_unique;
    }

    public String getCompany_leagl() {
        return company_leagl;
    }

    public void setCompany_leagl(String company_leagl) {
        this.company_leagl = company_leagl;
    }

    public String getSupplier_name() {
        return supplier_name;
    }

    public void setSupplier_name(String supplier_name) {
        this.supplier_name = supplier_name;
    }

    public int getSupplier_kind_id() {
        return supplier_kind_id;
    }

    public void setSupplier_kind_id(int supplier_kind_id) {
        this.supplier_kind_id = supplier_kind_id;
    }

    public int getSupplier_id() {
        return supplier_id;
    }

    public void setSupplier_id(int supplier_id) {
        this.supplier_id = supplier_id;
    }

    public String getSupplier_kind_name() {
        return supplier_kind_name;
    }

    public void setSupplier_kind_name(String supplier_kind_name) {
        this.supplier_kind_name = supplier_kind_name;
    }

    public String getSupplier_address() {
        return supplier_address;
    }

    public void setSupplier_address(String supplier_address) {
        this.supplier_address = supplier_address;
    }

    public String getSupplier_phone() {
        return supplier_phone;
    }

    public void setSupplier_phone(String supplier_phone) {
        this.supplier_phone = supplier_phone;
    }
}
