package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.MemberData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:会员列表（适配器）
 * Created by jingang on 2024/5/17
 */
public class MemberAdapter extends BaseAdapter<MemberData> {

    public MemberAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_member;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvUnique, tvMobile, tvName, tvBalance, tvPoints;
        ImageView ivLevel, ivBalance, ivPoints;
        tvUnique = holder.getView(R.id.tvItemUnique);
        tvMobile = holder.getView(R.id.tvItemMobile);
        ivLevel = holder.getView(R.id.ivItemLevel);
        tvName = holder.getView(R.id.tvItemName);
        ivBalance = holder.getView(R.id.ivItemBalance);
        tvBalance = holder.getView(R.id.tvItemBalance);
        ivPoints = holder.getView(R.id.ivItemPoints);
        tvPoints = holder.getView(R.id.tvItemPoints);

        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundResource(R.drawable.shape_green_5);
            tvUnique.setTextColor(mContext.getResources().getColor(R.color.white));
            tvMobile.setTextColor(mContext.getResources().getColor(R.color.white));
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
            ivBalance.setImageResource(R.mipmap.ic_balance002);
            tvBalance.setTextColor(mContext.getResources().getColor(R.color.white));
            ivPoints.setImageResource(R.mipmap.ic_points002);
            tvPoints.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundResource(R.drawable.shape_white_5);
            tvUnique.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
            tvMobile.setTextColor(mContext.getResources().getColor(R.color.black));
            tvName.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
            ivBalance.setImageResource(R.mipmap.ic_balance001);
            tvBalance.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.orange));
            ivPoints.setImageResource(R.mipmap.ic_points001);
            tvPoints.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
        }
        tvUnique.setText(mDataList.get(position).getCusUnique());
        tvMobile.setText(mDataList.get(position).getCusPhone());
        tvName.setText(mDataList.get(position).getCusName());
        tvBalance.setText(DFUtils.getNum2(mDataList.get(position).getTotalBalance()));
        tvPoints.setText(DFUtils.getNum2(mDataList.get(position).getCusPoints()));
        //会员等级 1.铜牌 2白银. 3.黄金 4.钻石
        switch (mDataList.get(position).getCusLevelVal()) {
            case 1:
                ivLevel.setImageResource(R.mipmap.ic_vip0);
                break;
            case 2:
                ivLevel.setImageResource(R.mipmap.ic_vip1);
                break;
            case 3:
                ivLevel.setImageResource(R.mipmap.ic_vip2);
                break;
            case 4:
                ivLevel.setImageResource(R.mipmap.ic_vip3);
                break;
            default:
                ivLevel.setImageResource(0);
                break;
        }
    }
}