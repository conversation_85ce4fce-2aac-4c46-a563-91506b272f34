package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.view.View;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.MyApplication;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityStatisticsBinding;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.dialog.MenuDialog;
import com.yxl.cashier_retail.ui.fragment.StatisticsManageFragment;
import com.yxl.cashier_retail.ui.fragment.StatisticsPaymentFragment;
import com.yxl.cashier_retail.ui.fragment.StatisticsStaffFragment;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.utils.EventBusManager;

/**
 * Describe:菜单-统计
 * Created by jingang on 2024/6/15
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class StatisticsActivity extends BaseActivity<ActivityStatisticsBinding> implements View.OnClickListener {
    public static int day;//0.今日 1.近7日 2.近30日

    private Fragment[] fragments;
    private StatisticsManageFragment manageFragment;
    private StatisticsPaymentFragment paymentFragment;
    private StatisticsStaffFragment staffFragment;
    private int index = 0;//点击的页卡索引
    private int currentTabIndex = 0;//当前的页卡索引

    @Override
    protected ActivityStatisticsBinding getViewBinding() {
        return ActivityStatisticsBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivLogo.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.tvDay0.setOnClickListener(this);
        mBinding.tvDay1.setOnClickListener(this);
        mBinding.tvDay2.setOnClickListener(this);
        mBinding.tvType0.setOnClickListener(this);
        mBinding.tvType1.setOnClickListener(this);
        mBinding.tvType2.setOnClickListener(this);
        setFragment();
    }

    @Override
    protected void initData() {
        setNetwork();
    }

    @Override
    public void getNetwork() {
        setNetwork();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivLogo:
                //菜单
                MenuDialog.showDialog(this, type -> {
                    //0.收银 1.入库 2.查询 3.统计 4.网单 5.会员 6.补货 7.营销 8.交班 9.设置
                    if (type != 3) {
                        switch (type) {
                            case 1:
                                goToActivity(InActivity.class);
                                break;
                            case 2:
                                goToActivity(QueryActivity.class);
                                break;
                            case 4:
                                goToActivity(OrderActivity.class);
                                break;
                            case 5:
                                goToActivity(MemberActivity.class);
                                break;
                            case 6:
                                goToActivity(MallActivity.class);
                                break;
                            case 7:
                                goToActivity(MarketingActivity.class);
                                break;
                            case 8:
                                goToActivity(ShiftActivity.class);
                                break;
                            case 9:
                                goToActivity(SettingActivity.class);
                                break;
                        }
                        finish();
                    }
                });
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.tvDay0:
                //今日
                if (day != 0) {
                    day = 0;
                    mBinding.tvDay0.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvDay0.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvDay1.setBackgroundResource(R.drawable.shape_white_5);
                    mBinding.tvDay1.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvDay2.setBackgroundResource(R.drawable.shape_white_5);
                    mBinding.tvDay2.setTextColor(getResources().getColor(R.color.black));
                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.STATISTICS_LIST));
                }
                break;
            case R.id.tvDay1:
                //近7日
                if (day != 1) {
                    day = 1;
                    mBinding.tvDay1.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvDay1.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvDay0.setBackgroundResource(R.drawable.shape_white_5);
                    mBinding.tvDay0.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvDay2.setBackgroundResource(R.drawable.shape_white_5);
                    mBinding.tvDay2.setTextColor(getResources().getColor(R.color.black));
                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.STATISTICS_LIST));
                }
                break;
            case R.id.tvDay2:
                //近30日
                if (day != 2) {
                    day = 2;
                    mBinding.tvDay2.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvDay2.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvDay1.setBackgroundResource(R.drawable.shape_white_5);
                    mBinding.tvDay1.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvDay0.setBackgroundResource(R.drawable.shape_white_5);
                    mBinding.tvDay0.setTextColor(getResources().getColor(R.color.black));
                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.STATISTICS_LIST));
                }
                break;
            case R.id.tvType0:
                //经营统计
                index = 0;
                fragmentControl();
                break;
            case R.id.tvType1:
                //打款统计
                index = 1;
                fragmentControl();
                break;
            case R.id.tvType2:
                //员工统计
                index = 2;
                fragmentControl();
                break;
        }
    }

    /**
     * 网络监听
     */
    private void setNetwork() {
        if (MyApplication.mNetwork != null) {
            if (MyApplication.mNetwork.isConnect()) {
                switch (MyApplication.mNetwork.getConnectType()) {
                    case 1:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network001);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 2:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network002);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 3:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network003);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    default:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                        mBinding.tvNetwork.setText(getRstr(R.string.no_network));
                        break;
                }
            } else {
                mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                mBinding.tvNetwork.setText(getRstr(R.string.no_network));
            }
        } else {
            mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
            mBinding.tvNetwork.setText(getRstr(R.string.no_network));
        }
    }

    /**
     * 设置fragment
     */
    public void setFragment() {
        day = 0;
        manageFragment = new StatisticsManageFragment();
        paymentFragment = new StatisticsPaymentFragment();
        staffFragment = new StatisticsStaffFragment();
        fragments = new Fragment[]{manageFragment,
                paymentFragment,
                staffFragment};
        setBottomColor();
        getSupportFragmentManager().beginTransaction().add(R.id.fragment_container, fragments[index]).show(fragments[index]).commit();
    }

    /**
     * 控制fragment的变化
     */
    public void fragmentControl() {
        if (currentTabIndex != index) {
            removeBottomColor();
            setBottomColor();

            FragmentTransaction trx = getSupportFragmentManager().beginTransaction();
            trx.hide(fragments[currentTabIndex]);
            if (!fragments[index].isAdded()) {
                trx.add(R.id.fragment_container, fragments[index]);
            }
            trx.show(fragments[index]).commitAllowingStateLoss();
            currentTabIndex = index;
        }
    }

    /**
     * 设置底部栏按钮变色
     */
    private void setBottomColor() {
        switch (index) {
            case 0:
                mBinding.tvType0.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.tvType0.setTextColor(getResources().getColor(R.color.white));
                break;
            case 1:
                mBinding.tvType1.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.tvType1.setTextColor(getResources().getColor(R.color.white));
                break;
            case 2:
                mBinding.tvType2.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.tvType2.setTextColor(getResources().getColor(R.color.white));
                break;
            default:
                break;
        }
    }

    /**
     * 清除底部栏颜色
     */
    private void removeBottomColor() {
        switch (currentTabIndex) {
            case 0:
                mBinding.tvType0.setBackgroundResource(R.drawable.shape_white_5);
                mBinding.tvType0.setTextColor(getResources().getColor(R.color.black));
                break;
            case 1:
                mBinding.tvType1.setBackgroundResource(R.drawable.shape_white_5);
                mBinding.tvType1.setTextColor(getResources().getColor(R.color.black));
                break;
            case 2:
                mBinding.tvType2.setBackgroundResource(R.drawable.shape_white_5);
                mBinding.tvType2.setTextColor(getResources().getColor(R.color.black));
                break;
            default:
                break;
        }
    }

}
