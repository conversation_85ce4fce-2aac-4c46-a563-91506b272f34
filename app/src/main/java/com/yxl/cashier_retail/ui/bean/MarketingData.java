package com.yxl.cashier_retail.ui.bean;

/**
 * Describe:营销活动列表（实体类）
 * Created by jingang on 2024/9/21
 */
public class MarketingData {
    /**
     * promotion_activity_id : 235
     * start_time : 2024-07-04 15:07:49
     * create_time : 2024-07-04 15:07:47
     * end_time : 2024-07-30 00:00:00
     * isProgress : 2
     * type : 1
     * time_status : 3
     * promotion_activity_name : 商品折扣
     * status : 1
     */

    private int promotion_activity_id;
    private String start_time;//开始时间
    private String create_time;//创建时间
    private String end_time;//结束时间
    private int isProgress;//时间状态：0未开始 1进行中 2 过期
    private String type;//活动范围为2时，此地段为空 1：商品折扣；2：商品满赠；3：订单促销；4：单品促销；
    private String time_status;//1.未开始 2.已开始 3.已结束
    private String promotion_activity_name;
    private int status;//1.正常 2.停用

    public int getPromotion_activity_id() {
        return promotion_activity_id;
    }

    public void setPromotion_activity_id(int promotion_activity_id) {
        this.promotion_activity_id = promotion_activity_id;
    }

    public String getStart_time() {
        return start_time;
    }

    public void setStart_time(String start_time) {
        this.start_time = start_time;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getEnd_time() {
        return end_time;
    }

    public void setEnd_time(String end_time) {
        this.end_time = end_time;
    }

    public int getIsProgress() {
        return isProgress;
    }

    public void setIsProgress(int isProgress) {
        this.isProgress = isProgress;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTime_status() {
        return time_status;
    }

    public void setTime_status(String time_status) {
        this.time_status = time_status;
    }

    public String getPromotion_activity_name() {
        return promotion_activity_name;
    }

    public void setPromotion_activity_name(String promotion_activity_name) {
        this.promotion_activity_name = promotion_activity_name;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
