package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmInOutBinding;
import com.yxl.cashier_retail.ui.adapter.GoodsAdapter;
import com.yxl.cashier_retail.ui.adapter.InBatchAdapter;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.ui.bean.GoodsInBatchData;
import com.yxl.cashier_retail.ui.bean.SupplierPcData;
import com.yxl.cashier_retail.ui.dialog.OutBatchDialog;
import com.yxl.cashier_retail.ui.dialog.SupplierEditDialog;
import com.yxl.cashier_retail.ui.popupwindow.SupplierPop;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.litepal.LitePal;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:商品-批量出库记录
 * Created by jingang on 2025/1/3
 */
@SuppressLint("NonConstantResourceId")
public class InOutFragment extends BaseFragment<FmInOutBinding> implements View.OnClickListener {
    private String keyWords,//商品搜索关键字
            supplierUnique;//供货商编号

    //供货商列表
    private List<SupplierPcData> supplierList = new ArrayList<>();

    //已选择商品列表
    private InBatchAdapter goodsAdapter;
    private List<GoodsInBatchData> goodsList = new ArrayList<>();

    //商品列表
    private GoodsAdapter mAdapter;
    private List<GoodsData> dataList = new ArrayList<>();

    @Override
    protected FmInOutBinding getViewBinding() {
        return FmInOutBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.linSupplier.setOnClickListener(this);
        mBinding.tvPrint.setOnClickListener(this);
        mBinding.tvConfirm.setOnClickListener(this);
        mBinding.ivSearchClear.setOnClickListener(this);
        mBinding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                mBinding.ivSearchClear.setVisibility(TextUtils.isEmpty(keyWords) ? View.GONE : View.VISIBLE);
                mBinding.smartRefreshLayout.autoRefresh();
            }
        });
        mBinding.etSearch.setOnEditorActionListener((v, actionId, event) -> {
            hideSoftInput(getActivity());
            return true;
        });
        setAdapter();
    }

    @Override
    protected void initData() {
        mBinding.smartRefreshLayout.autoRefresh();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.linSupplier:
                //选择供货商
                if (supplierList.isEmpty()) {
                    getSupplierList(v);
                } else {
                    showPopSupplier(v);
                }
                break;
            case R.id.tvPrint:
                //打印入库单
                showToast(1, "暂未开发");
                break;
            case R.id.tvConfirm:
                //确认入库
                if (dataList.size() < 1) {
                    showToast(1, getRstr(R.string.select_goods));
                    return;
                }
//                if (TextUtils.isEmpty(supplierUnique)) {
//                    showToast(1, getRstr(R.string.select_supplier));
//                    return;
//                }
                postGoodsStockBatch();
                break;
            case R.id.ivSearchClear:
                //清除搜索输入
                mBinding.etSearch.setText("");
                keyWords = "";
                max = 0;
                getGoodsList();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //已选择商品列表
        goodsAdapter = new InBatchAdapter(getActivity());
        mBinding.rvInBatch.setAdapter(goodsAdapter);
        goodsAdapter.setListener(position -> {
            //删除
            goodsList.remove(position);
            goodsAdapter.remove(position);
            if (goodsList.size() > 0) {
                mBinding.linNothing.setVisibility(View.GONE);
                mBinding.linInfo.setVisibility(View.VISIBLE);
            } else {
                mBinding.linNothing.setVisibility(View.VISIBLE);
                mBinding.linInfo.setVisibility(View.GONE);
            }
            getTotal();
        });

        //商品列表
        mAdapter = new GoodsAdapter(getActivity());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            OutBatchDialog.showDialog(getActivity(), dataList.get(position), (count, price, total, data) -> {
                goodsList.add(0, new GoodsInBatchData(data.getGoods_id(),
                        data.getGoods_barcode(),
                        data.getGoods_name(),
                        price, count, total));
                goodsAdapter.setDataList(goodsList);
                mBinding.linNothing.setVisibility(View.GONE);
                mBinding.linInfo.setVisibility(View.VISIBLE);
                getTotal();
            });
        });
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                getGoodsList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                max = 0;
                getGoodsList();
            }
        });
    }

    /**
     * 计算总价
     */
    private void getTotal() {
        double total = 0;
        for (int i = 0; i < goodsList.size(); i++) {
            total = total + goodsList.get(i).getInTotal();
        }
        mBinding.tvTotal.setText(DFUtils.getNum2(total));
    }

    /**
     * pop 选择供货商
     * @param view
     */
    private void showPopSupplier(View view) {
        SupplierPop.showDialog(getActivity(),
                mBinding.ivSupplier,
                view,
                mBinding.linSupplier.getMeasuredWidth(),
                supplierList,
                supplierUnique,
                new SupplierPop.MyListener() {
                    @Override
                    public void onAddClick() {
                        //添加供货商
                        SupplierEditDialog.showDialog(getActivity(), 0, "", (id, name, type) -> {
                            getSupplierList(view);
                        });
                    }

                    @Override
                    public void onCallBack(SupplierPcData data) {
                        supplierUnique = data.getSupplier_unique();
                        mBinding.tvSupplier.setText(data.getSupplier_name());
                    }
                });
    }

    /**
     * 供货商列表
     */
    private void getSupplierList(View v) {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("supMsg", "");
        params.put("page", 1);
        params.put("limit", 10000);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getSupplierList(),
                params,
                SupplierPcData.class,
                new RequestListListener<SupplierPcData>() {
                    @Override
                    public void onResult(List<SupplierPcData> list) {
                        supplierList.clear();
                        supplierList.addAll(list);
                        showPopSupplier(v);
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 商品列表
     */
    private void getGoodsList() {
        mBinding.smartRefreshLayout.finishRefresh();
        mBinding.smartRefreshLayout.finishLoadMore();
        List<GoodsData> list;
        if (TextUtils.isEmpty(keyWords)) {
            list = LitePal
                    .limit(30)
                    .offset(max)
                    .find(GoodsData.class);
        } else {
            list = LitePal
                    .limit(30)
                    .offset(max)
                    .where("goods_name like ? or goods_barcode like ?", "%" + keyWords + "%", "%" + keyWords + "%")
                    .find(GoodsData.class);
        }
        if (list != null) {
            if (max == 0) {
                dataList.clear();
            }
            dataList.addAll(list);
            max += dataList.size();
        }
        if (!dataList.isEmpty()) {
            mBinding.recyclerView.setVisibility(View.VISIBLE);
            mBinding.linEmpty.setVisibility(View.GONE);
            mAdapter.setDataList(dataList);
        } else {
            mBinding.recyclerView.setVisibility(View.GONE);
            mBinding.linEmpty.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 批量出入库
     */
    private void postGoodsStockBatch() {
        JSONArray array = new JSONArray();
        for (int i = 0; i < goodsList.size(); i++) {
            JSONObject object = new JSONObject();
            try {
                object.put("goodsBarcode", goodsList.get(i).getGoodsBarcode());
                object.put("goodsCount", goodsList.get(i).getInCount());
                object.put("stockPrice", goodsList.get(i).getInPrice());
                array.put(object);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("staffId", getStaffUnique());
        params.put("supplier_unique", supplierUnique);//供货商编号
        params.put("stockType", 2);//1.入库 2.出库
//        params.put("stock_kind", stockKind);//出入库原因
        params.put("stockOrigin", 2);//操作来源：1、手机；2、PC端；3、web网页端；4、小程序
        params.put("list_unique", System.currentTimeMillis() + ((int) (Math.random() * 9000 + 1000)));//提交唯一标识
        params.put("goods_stock_list", array);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(getActivity(),
                ZURL.getGoodsStockBatch(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        for (int i = 0; i < goodsList.size(); i++) {
                            GoodsData data = LitePal.where("goods_barcode = ?", goodsList.get(i).getGoodsBarcode()).findFirst(GoodsData.class);
                            if (data != null) {
                                data.setGoods_count(data.getGoods_count() - goodsList.get(i).getInCount());
                                data.save();
                            }
                        }
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_LIST));
                        goodsList.clear();
                        goodsAdapter.clear();
                        mBinding.linNothing.setVisibility(View.VISIBLE);
                        mBinding.linInfo.setVisibility(View.GONE);
                        getTotal();
                        max = 0;
                        getGoodsList();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }
}
