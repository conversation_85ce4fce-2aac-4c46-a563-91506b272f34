package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.os.CountDownTimer;
import android.text.Editable;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityForgetBinding;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.HashMap;
import java.util.Map;

/**
 * Describe:忘记密码
 * Created by jingang on 2024/5/9
 */
@SuppressLint("NonConstantResourceId")
public class ForgetActivity extends BaseActivity<ActivityForgetBinding> implements View.OnClickListener {
    private String account, code, pwd;
    private boolean isEye;//密码可见

    @Override
    protected ActivityForgetBinding getViewBinding() {
        return ActivityForgetBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.tvBack.setOnClickListener(this);
        mBinding.ivAccountClear.setOnClickListener(this);
        mBinding.ivCodeClear.setOnClickListener(this);
        mBinding.tvCode.setOnClickListener(this);
        mBinding.ivPwdClear.setOnClickListener(this);
        mBinding.ivEye.setOnClickListener(this);
        mBinding.tvConfirm.setOnClickListener(this);
        mBinding.etAccount.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                account = s.toString().trim();
                if (TextUtils.isEmpty(account)) {
                    mBinding.ivAccountClear.setVisibility(View.GONE);
                } else {
                    mBinding.ivAccountClear.setVisibility(View.VISIBLE);
                }
                setTextBg();
            }
        });
        mBinding.etCode.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                code = s.toString().trim();
                if (TextUtils.isEmpty(code)) {
                    mBinding.ivCodeClear.setVisibility(View.GONE);
                } else {
                    mBinding.ivCodeClear.setVisibility(View.VISIBLE);
                }
                setTextBg();
            }
        });
        mBinding.etPwd.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                pwd = s.toString().trim();
                if (TextUtils.isEmpty(pwd)) {
                    mBinding.ivPwdClear.setVisibility(View.GONE);
                } else {
                    mBinding.ivPwdClear.setVisibility(View.VISIBLE);
                }
                setTextBg();
            }
        });
    }

    @Override
    protected void initData() {
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvBack:
                onBackPressed();
                break;
            case R.id.ivAccountClear:
                //清除账号输入
                mBinding.etAccount.setText("");
                break;
            case R.id.ivCodeClear:
                //清除验证码输入
                mBinding.etCode.setText("");
                break;
            case R.id.tvCode:
                //获取验证码
                if (TextUtils.isEmpty(account)) {
                    showToast(1, getRstr(R.string.input_mobile));
                    return;
                }
                getCode();
                break;
            case R.id.ivPwdClear:
                //清除密码输入
                mBinding.etPwd.setText("");
                break;
            case R.id.ivEye:
                isEye = !isEye;
                mBinding.ivEye.setSelected(isEye);
                if (isEye) {
                    mBinding.etPwd.setInputType(InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD);//设置密码可见
                } else {
                    mBinding.etPwd.setInputType(InputType.TYPE_TEXT_VARIATION_PASSWORD | InputType.TYPE_CLASS_TEXT);//设置密码不可见
                }
                mBinding.etPwd.setSelection(mBinding.etPwd.getText().length());
                break;
            case R.id.tvConfirm:
                //确认修改
                if (TextUtils.isEmpty(account)) {
                    showToast(1, getRstr(R.string.input_email_ads));
                    return;
                }
                if (TextUtils.isEmpty(code)) {
                    showToast(1, getRstr(R.string.input_code));
                    return;
                }
                if (TextUtils.isEmpty(pwd)) {
                    showToast(1, getRstr(R.string.input_new_pwd));
                    return;
                }
                postPwdUpdate();
                break;
        }
    }

    //倒计时
    private boolean isRun = false;//倒计时线程是否运行

    /**
     * 验证码倒计时
     */
    public void time() {
        if (!isRun) {
            new CountdownThread(60000, 1000).start();// 构造CountDownTimer对象
            isRun = true;
        }
    }

    /**
     * 倒计时
     */
    class CountdownThread extends CountDownTimer {
        public CountdownThread(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @SuppressLint("SetTextI18n")
        @Override
        public void onTick(long millisUntilFinished) {
            mBinding.tvCode.setText((millisUntilFinished / 1000) + "s");
        }

        @Override
        public void onFinish() {
            //倒计时结束时触发
            mBinding.tvCode.setText(getRstr(R.string.code_gain));
            isRun = false;
        }
    }

    /**
     * 根据账号密码输入改变按钮背景
     */
    private void setTextBg() {
        if (TextUtils.isEmpty(account)) {
            mBinding.tvConfirm.setBackgroundResource(R.drawable.shape_green_tm_5);
            return;
        }
        if (TextUtils.isEmpty(code)) {
            mBinding.tvConfirm.setBackgroundResource(R.drawable.shape_green_tm_5);
            return;
        }
        if (TextUtils.isEmpty(pwd)) {
            mBinding.tvConfirm.setBackgroundResource(R.drawable.shape_green_tm_5);
            return;
        }
        mBinding.tvConfirm.setBackgroundResource(R.drawable.shape_green_5);
    }

    /**
     * 获取验证码
     */
    private void getCode() {
        Map<String, Object> params = new HashMap<>();
        params.put("staffAccount", account);
        params.put("phoneType", 1);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getCode(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        showToast(0, getRstr(R.string.send_success));
                        time();
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 修改密码
     */
    private void postPwdUpdate() {
        Map<String, Object> params = new HashMap<>();
        params.put("staffAccount", account);
        params.put("smsCode", code);
        params.put("newStaffPwd", pwd);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getUpdatePwd(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        showToast(0, s);
                        AppManager.getInstance().finishAllActivityToIgnore(LoginActivity.class);
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                    }
                });
    }

}
