package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;

/**
 * Describe:店铺配送设置（实体类）
 * Created by jingang on 2024/7/2
 */
public class ShopDeliveryData implements Serializable {
    /**
     * shop_unique : 1536215939565
     * distribution_scope : 6.0
     * take_estimate_time : 3
     * is_order_taking : 1
     * delivery_type : 0
     * take_fee : 0.03
     * subsidy_delivery_price : 0.0
     * take_free_price : 48.0
     * shop_take_price : 0.0
     */

    private String shop_unique;
    private double distribution_scope;//配送范围（km）
    private int take_estimate_time;//预计时长（分钟）
    private int is_order_taking;//是否自动接单：0、自动接单；1、不自动接单
    private int delivery_type;//配送方式选择：0、自；1、美团；2、一刻钟配送
    private double take_fee;//起送费，超过这个金额才配送
    private double subsidy_delivery_price;//每单补贴配送费，如果非自配送，店主补贴给用户的费用
    private double take_free_price;//免配送费，订单超过这个金额，配送费店铺承担
    private double shop_take_price;//商家自配送配送费，子配送时，收取客户的配送费

    public String getShop_unique() {
        return shop_unique;
    }

    public void setShop_unique(String shop_unique) {
        this.shop_unique = shop_unique;
    }

    public double getDistribution_scope() {
        return distribution_scope;
    }

    public void setDistribution_scope(double distribution_scope) {
        this.distribution_scope = distribution_scope;
    }

    public int getTake_estimate_time() {
        return take_estimate_time;
    }

    public void setTake_estimate_time(int take_estimate_time) {
        this.take_estimate_time = take_estimate_time;
    }

    public int getIs_order_taking() {
        return is_order_taking;
    }

    public void setIs_order_taking(int is_order_taking) {
        this.is_order_taking = is_order_taking;
    }

    public int getDelivery_type() {
        return delivery_type;
    }

    public void setDelivery_type(int delivery_type) {
        this.delivery_type = delivery_type;
    }

    public double getTake_fee() {
        return take_fee;
    }

    public void setTake_fee(double take_fee) {
        this.take_fee = take_fee;
    }

    public double getSubsidy_delivery_price() {
        return subsidy_delivery_price;
    }

    public void setSubsidy_delivery_price(double subsidy_delivery_price) {
        this.subsidy_delivery_price = subsidy_delivery_price;
    }

    public double getTake_free_price() {
        return take_free_price;
    }

    public void setTake_free_price(double take_free_price) {
        this.take_free_price = take_free_price;
    }

    public double getShop_take_price() {
        return shop_take_price;
    }

    public void setShop_take_price(double shop_take_price) {
        this.shop_take_price = shop_take_price;
    }
}
