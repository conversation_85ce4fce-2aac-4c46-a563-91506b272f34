package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:商品列表（适配器）
 * Created by jingang on 2024/7/10
 */
public class GoodsAdapter extends BaseAdapter<GoodsData> {

    public GoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvBarcode, tvShofar, tvPrice, tvUnit, tvStock, tvHouse;
        ImageView ivScale = holder.getView(R.id.ivItemScale);
        tvName = holder.getView(R.id.tvItemName);
        tvBarcode = holder.getView(R.id.tvItemBarcode);
        tvShofar = holder.getView(R.id.tvItemShofar);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvUnit = holder.getView(R.id.tvItemUnit);
        tvStock = holder.getView(R.id.tvItemStock);
        tvHouse = holder.getView(R.id.tvItemHouse);

        //称重类型 0.标品 1.称重
        if (mDataList.get(position).getGoodsChengType() == 1) {
            ivScale.setVisibility(View.VISIBLE);
            tvShofar.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.orange));
            tvPrice.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.orange));
            tvStock.setBackgroundResource(R.drawable.shape_orange_tm_left_2);
            tvHouse.setBackgroundResource(R.drawable.shape_orange_right_2);
        } else {
            ivScale.setVisibility(View.GONE);
            tvShofar.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvPrice.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvStock.setBackgroundResource(R.drawable.shape_green_tm_left_2);
            tvHouse.setBackgroundResource(R.drawable.shape_green_right_2);
        }
        //是否展示库存
        if (TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_SHOW_STOCK, ""))) {
            tvStock.setVisibility(View.VISIBLE);
            tvHouse.setVisibility(View.VISIBLE);
        } else {
            tvStock.setVisibility(View.GONE);
            tvHouse.setVisibility(View.GONE);
        }

        tvName.setText(mDataList.get(position).getGoods_name());
        tvBarcode.setText(mDataList.get(position).getGoods_barcode());
        tvPrice.setText(DFUtils.getNum2(mDataList.get(position).getGoods_sale_price()));
        tvUnit.setText(TextUtils.isEmpty(mDataList.get(position).getGoods_unit()) ? "" : "/" + mDataList.get(position).getGoods_unit());
        tvStock.setText(DFUtils.getNum4(mDataList.get(position).getGoods_count()));
    }
}
