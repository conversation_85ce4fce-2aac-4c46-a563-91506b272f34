package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;

/**
 * Describe:查询-批次（适配器）
 * Created by jingang on 2024/8/23
 */
public class QueryBatchAdapter extends BaseAdapter<String> {

    public QueryBatchAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_batch_query;
    }

    @Override
    public int getItemCount() {
        return 10;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvNo, tvTime, tvType, tvStatus, tvName, tvOperate, tvCount, tvTotal, tvPlatform, tvRemarks;
        tvNo = holder.getView(R.id.tvItemNo);
        tvTime = holder.getView(R.id.tvItemTime);
        tvType = holder.getView(R.id.tvItemType);
        tvStatus = holder.getView(R.id.tvItemStatus);
        tvOperate = holder.getView(R.id.tvItemOperate);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvPlatform = holder.getView(R.id.tvItemPlatform);
        tvRemarks = holder.getView(R.id.tvItemRemarks);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
        }
    }
}
