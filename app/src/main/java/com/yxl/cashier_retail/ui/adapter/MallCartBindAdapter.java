package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;

/**
 * Describe:商城-购物车-捆绑（适配器）
 * Created by jingang on 2024/6/4
 */
public class MallCartBindAdapter extends BaseAdapter<String> {

    public MallCartBindAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_mall_cart_bind;
    }

    @Override
    public int getItemCount() {
        return 2;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvName, tvCount, tvTotal;
        tvName = holder.getView(R.id.tvItemName);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTotal = holder.getView(R.id.tvItemTotal);
    }
}
