package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogMemberLevelBinding;
import com.yxl.cashier_retail.ui.adapter.MemberLevelDialogAdapter;
import com.yxl.cashier_retail.ui.bean.MemberLevelData;
import com.yxl.cashier_retail.view.NumberKeyBoardView;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:dialog（会员-等级管理）
 * Created by jingang on 2024/7/3
 */
@SuppressLint("NonConstantResourceId")
public class MemberLevelDialog extends BaseDialog<DialogMemberLevelBinding> {
    private int pos;

    private MemberLevelDialogAdapter mAdapter;
    private final List<MemberLevelData> dataList = new ArrayList<>();

    public static void showDialog(Activity activity, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        MemberLevelDialog.listener = listener;
        MemberLevelDialog dialog = new MemberLevelDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public MemberLevelDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(v -> dismiss());
        mBinding.numberKeyBoardView.setMaxLength(Constants.MAX_MONEY_LENGTH);
        mBinding.numberKeyBoardView.setDrop(false);
        mBinding.numberKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var) {
                int points = TextUtils.isEmpty(var) ? 0 : Integer.parseInt(var);
                if (dataList.size() > pos) {
                    dataList.get(pos).setCusLevelPoints(points);
                    mAdapter.notifyItemChanged(pos);
                }
            }

            @Override
            public void onConfirm() {
                List array = new ArrayList();
                for (int i = 0; i < dataList.size(); i++) {
                    Map object = new HashMap();
                    object.put("cusLevelId", dataList.get(i).getCusLevelId());
                    object.put("cusLevelPoints", dataList.get(i).getCusLevelPoints());
                    array.add(object);
                }
                Log.e(tag, "array = " + array);
                postMemberLevel(array);
            }
        });
        setAdapter();
        getMemberLevel();
    }

    @Override
    protected DialogMemberLevelBinding getViewBinding() {
        return DialogMemberLevelBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new MemberLevelDialogAdapter(getContext());
        mBinding.rvDialog.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (!dataList.get(position).isSelect()) {
                for (int i = 0; i < dataList.size(); i++) {
                    if (dataList.get(i).isSelect()) {
                        dataList.get(i).setSelect(false);
                        mAdapter.notifyItemChanged(i);
                    }
                }
                dataList.get(position).setSelect(true);
                mAdapter.notifyItemChanged(position);
                pos = position;
                if (dataList.get(position).getCusLevelPoints() > 0) {
                    mBinding.numberKeyBoardView.setResultStr(String.valueOf(dataList.get(position).getCusLevelPoints()));
                } else {
                    mBinding.numberKeyBoardView.setResultStr("");
                }
            }
        });
    }

    /**
     * 查询店铺会员等级列表
     */
    private void getMemberLevel() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        showDialog();
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getMemberLevel(),
                params,
                MemberLevelData.class,
                new RequestListListener<MemberLevelData>() {
                    @Override
                    public void onResult(List<MemberLevelData> list) {
                        hideDialog();
                        dataList.clear();
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            pos = 0;
                            dataList.get(0).setSelect(true);
                        }
                        mAdapter.setDataList(dataList);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 批量修改会员等级积分
     *
     * @param array
     */
    private void postMemberLevel(List array) {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("cusLevelPointList", array);
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getMemberLevelEdit(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        if (listener != null) {
                            listener.onConfirm();
                            dismiss();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm();
    }
}
