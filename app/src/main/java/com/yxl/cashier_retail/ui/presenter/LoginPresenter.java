package com.yxl.cashier_retail.ui.presenter;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;

import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.ui.contract.LoginContract;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.HashMap;
import java.util.Map;

/**
 * 登录
 */
public class LoginPresenter implements LoginContract.Presenter {
    private Context context;
    private LoginContract.View mView;

    public LoginPresenter(Context context) {
        this.context = context;
    }

    @Override
    public void attachView(@NonNull LoginContract.View view) {
        mView = view;
    }

    @Override
    public void detachView() {
        mView = null;
    }

    @Override
    public void postLogin(String account, String pwd) {
        Map<String, Object> params = new HashMap<>();
        params.put("staff_account", account);
        params.put("staff_pwd", pwd);
        RXHttpUtil.requestByFormPostAsOriginalResponse((LifecycleOwner) context,
                ZURL.getLogin(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        if (mView != null) {
                            mView.successLogin(s);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        if (mView != null) {
                            mView.onError(msg);
                        }
                    }
                });
    }
}
