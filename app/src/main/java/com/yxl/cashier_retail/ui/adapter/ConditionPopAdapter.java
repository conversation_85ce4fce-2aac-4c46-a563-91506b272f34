package com.yxl.cashier_retail.ui.adapter;

import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ui.bean.ConditionData;

import java.util.List;

/**
 * Describe:条件选择-pop（适配器）
 * Created by jingang on 2024/5/20
 */
public class ConditionPopAdapter extends BaseQuickAdapter<ConditionData, BaseViewHolder> {
    public ConditionPopAdapter(@Nullable List<ConditionData> data) {
        super(R.layout.item_condition, data);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder holder, ConditionData data) {
        TextView tvName = holder.getView(R.id.tvItemName);
        tvName.setText(String.valueOf(data.getName()));
        if (data.isSelect()) {
            tvName.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
        } else {
            tvName.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
        }
    }
}
