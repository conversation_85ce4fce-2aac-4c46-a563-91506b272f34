package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmSupplierBinding;
import com.yxl.cashier_retail.ui.activity.SupplierCateActivity;
import com.yxl.cashier_retail.ui.activity.SupplierEditActivity;
import com.yxl.cashier_retail.ui.activity.SupplierInfoActivity;
import com.yxl.cashier_retail.ui.adapter.SupplierAdapter;
import com.yxl.cashier_retail.ui.adapter.SupplierCateHorAdapter;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.SupplierCateData;
import com.yxl.cashier_retail.ui.bean.SupplierData;
import com.yxl.cashier_retail.ui.bean.SupplierListData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:入库-供货商管理
 * Created by jingang on 2024/6/7
 */
@SuppressLint("NonConstantResourceId")
public class SupplierFragment extends BaseFragment<FmSupplierBinding> implements View.OnClickListener {
    private int type;//0.供货商 1.供货商申请
    private String keyWords,
            cateUnique;

    //供货商分类列表
    private SupplierCateHorAdapter cateAdapter;
    private List<SupplierCateData> cateList = new ArrayList<>();

    //供货商列表
    private SupplierAdapter mAdapter;
    private List<SupplierData> dataList = new ArrayList<>();

    @Override
    protected FmSupplierBinding getViewBinding() {
        return FmSupplierBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.tvCate.setOnClickListener(this);
        mBinding.tvAdd.setOnClickListener(this);
        mBinding.tvSupplier.setOnClickListener(this);
        mBinding.tvApply.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected void initData() {
        getCateList();
        getSupplierList();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvCate:
                //供货商分类
                goToActivity(SupplierCateActivity.class);
                break;
            case R.id.tvAdd:
                //添加供货商
                goToActivity(SupplierEditActivity.class);
                break;
            case R.id.tvSupplier:
                //供货商
                if (type != 0) {
                    type = 0;
                    mBinding.tvSupplier.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvSupplier.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvSupplier.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.tvApply.setBackgroundResource(0);
                    mBinding.tvApply.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvApply.setTypeface(Typeface.DEFAULT);
                    mBinding.rvCate.setVisibility(View.VISIBLE);
                    mBinding.vSmartrefreshlayout.smartRefreshLayout.setEnableLoadMore(true);
                    page = 1;
                    getSupplierList();
                }
                break;
            case R.id.tvApply:
                //供货商申请
                if (type != 1) {
                    type = 1;
                    mBinding.tvApply.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvApply.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvApply.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.tvSupplier.setBackgroundResource(0);
                    mBinding.tvSupplier.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvSupplier.setTypeface(Typeface.DEFAULT);
                    mBinding.rvCate.setVisibility(View.GONE);
                    mBinding.vSmartrefreshlayout.smartRefreshLayout.setEnableLoadMore(false);
                    page = 1;
                    getSupplierList();
                }
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.SUPPLIER_CATE_LIST:
                //供货商分类
                getCateList();
                break;
            case Constants.SUPPLIER_LIST:
                //供货商列表
                page = 1;
                getSupplierList();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //分类
        cateAdapter = new SupplierCateHorAdapter(getActivity());
        mBinding.rvCate.setAdapter(cateAdapter);
        cateAdapter.setOnItemClickListener((view, position) -> {
            if (!cateList.get(position).isSelect()) {
                for (int i = 0; i < cateList.size(); i++) {
                    if (cateList.get(i).isSelect()) {
                        cateList.get(i).setSelect(false);
                        cateAdapter.notifyItemChanged(i);
                    }
                }
                cateList.get(position).setSelect(true);
                cateAdapter.notifyItemChanged(position);
                cateUnique = cateList.get(position).getSupplierKindUnique();
            }
        });

        //供货商
        mAdapter = new SupplierAdapter(getActivity());
        mBinding.vSmartrefreshlayout.recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new SupplierAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情
                if (type == 0 && !TextUtils.isEmpty(dataList.get(position).getSupplierUnique())) {
                    startActivity(new Intent(getActivity(), SupplierInfoActivity.class)
                            .putExtra("unique", dataList.get(position).getSupplierUnique())
                    );
                }
            }

            @Override
            public void onEditClick(View view, int position) {
                //编辑
                startActivity(new Intent(getActivity(), SupplierEditActivity.class)
                        .putExtra("data", dataList.get(position))
                );
            }

            @Override
            public void onDelClick(View view, int position) {
                //删除
                postSupplierDel(dataList.get(position).getId(), position);
            }

            @Override
            public void onConfirmClick(View view, int position) {
                //确认通过
                postSupplierConfirm(dataList.get(position).getId(), position);
            }
        });
        mBinding.vSmartrefreshlayout.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getSupplierList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getSupplierList();
            }
        });
    }

    /**
     * 搜索
     *
     * @param keyWords
     */
    public void setKeyWords(String keyWords) {
        if (getActivity() == null) {
            return;
        }
        this.keyWords = keyWords;
        mBinding.vSmartrefreshlayout.smartRefreshLayout.autoRefresh();
    }


    /**
     * 分类列表
     */
    private void getCateList() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        RXHttpUtil.requestByBodyPostAsResponseList(this,
                ZURL.getSupplierCateList(),
                map,
                SupplierCateData.class,
                new RequestListListener<SupplierCateData>() {
                    @Override
                    public void onResult(List<SupplierCateData> list) {
                        hideDialog();
                        cateList.clear();
                        cateList.add(new SupplierCateData(true, "", getRstr(R.string.all)));
                        cateList.addAll(list);
                        cateAdapter.setDataList(cateList);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 供货商列表
     */
    private void getSupplierList() {
        showDialog();
        hideSoftInput(getActivity());
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("queryMeg", keyWords);
        if (type == 0) {
            map.put("pageIndex", page);
            map.put("pageSize", Constants.limit);
            RXHttpUtil.requestByBodyPostAsResponse(getActivity(),
                    ZURL.getSupplierList(),
                    map,
                    SupplierListData.class,
                    new RequestListener<SupplierListData>() {
                        @Override
                        public void success(SupplierListData data) {
                            hideDialog();
                            mBinding.tvDebt.setText(DFUtils.getNum2(data.getTotalDebts()));
                            if (page == 1) {
                                mBinding.vSmartrefreshlayout.smartRefreshLayout.finishRefresh();
                                dataList.clear();
                            } else {
                                mBinding.vSmartrefreshlayout.smartRefreshLayout.finishLoadMore();
                            }
                            dataList.addAll(data.getList());
                            if (dataList.size() > 0) {
                                mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.VISIBLE);
                                mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.GONE);
                                mAdapter.setDataList(dataList);
                            } else {
                                mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.GONE);
                                mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.VISIBLE);
                            }
                        }

                        @Override
                        public void onError(String msg) {
                            hideDialog();
                            showToast(1, msg);
                            mBinding.vSmartrefreshlayout.smartRefreshLayout.finishRefresh();
                            mBinding.vSmartrefreshlayout.smartRefreshLayout.finishLoadMore();
                            if (dataList.size() > 0) {
                                mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.VISIBLE);
                                mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.GONE);
                            } else {
                                mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.GONE);
                                mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.VISIBLE);
                            }
                        }
                    });
        } else {
            RXHttpUtil.requestByBodyPostAsResponseList(getActivity(),
                    ZURL.getSupplierApplyList(),
                    map,
                    SupplierData.class,
                    new RequestListListener<SupplierData>() {
                        @Override
                        public void onResult(List<SupplierData> list) {
                            hideDialog();
                            mBinding.vSmartrefreshlayout.smartRefreshLayout.finishRefresh();
                            dataList.clear();
                            dataList.addAll(list);
                            if (dataList.size() > 0) {
                                mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.VISIBLE);
                                mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.GONE);
                                mAdapter.setDataList(dataList);
                            } else {
                                mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.GONE);
                                mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.VISIBLE);
                            }
                        }

                        @Override
                        public void onError(String msg) {
                            hideDialog();
                            showToast(1, msg);
                            mBinding.vSmartrefreshlayout.smartRefreshLayout.finishRefresh();
                            if (dataList.size() > 0) {
                                mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.VISIBLE);
                                mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.GONE);
                            } else {
                                mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.GONE);
                                mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.VISIBLE);
                            }
                        }
                    });
        }
    }

    /**
     * 供货商删除
     *
     * @param id
     * @param position
     */
    private void postSupplierDel(int id, int position) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("createId", getStaffUnique());
        map.put("createBy", getStaffName());
        map.put("id", id);
        RXHttpUtil.requestByBodyPostAsResponse(getActivity(),
                ZURL.getSupplierDel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        dataList.remove(position);
                        mAdapter.remove(position);
                        if (dataList.size() > 0) {
                            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.GONE);
                        } else {
                            page = 1;
                            getSupplierList();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 供货商绑定（确认通过）
     *
     * @param id
     */
    private void postSupplierConfirm(int id, int position) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("createId", getStaffUnique());
        map.put("createBy", getStaffName());
        map.put("id", id);
        RXHttpUtil.requestByBodyPostAsResponse(getActivity(),
                ZURL.getSupplierBind(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        dataList.remove(position);
                        mAdapter.remove(position);
                        if (dataList.size() > 0) {
                            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.GONE);
                            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

}
