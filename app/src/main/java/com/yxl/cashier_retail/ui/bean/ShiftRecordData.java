package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:交接班记录（实体类）
 * Created by jingang on 2024/8/23
 */
public class ShiftRecordData implements Serializable {
    /**
     * staff_name : 韩天
     * rechargeCount : 0
     * rechargeDetail : [{"recharge_method":1,"name":"现金","sale_list_actually_received":0,"orderNum":0,"accounting":0},{"recharge_method":2,"name":"微信","sale_list_actually_received":0,"orderNum":0,"accounting":0},{"recharge_method":3,"name":"支付宝","sale_list_actually_received":0,"orderNum":0,"accounting":0},{"recharge_method":4,"name":"存零","sale_list_actually_received":0,"orderNum":0,"accounting":0}]
     * login_datetime : 2018-11-02 15:28:31
     * sumMoney : 1892.6
     * rechargeSum : 0
     * start_datetime : 2018-11-02 15:39:51
     * end_datetime : 2018-11-02 22:37:29
     * sign_out_datetime : 2018-11-02 22:40:14
     * orderCount : 122
     * detail : [{"name":"现金","sale_list_actually_received":521.8,"orderNum":36,"accounting":"29.51%"},{"name":"微信","sale_list_actually_received":938.5,"orderNum":61,"accounting":"50.0%"},{"name":"支付宝","sale_list_actually_received":247.8,"orderNum":18,"accounting":"14.75%"},{"name":"储值卡","sale_list_actually_received":0,"orderNum":0,"accounting":"0%"},{"name":"银行卡","sale_list_actually_received":0,"orderNum":0,"accounting":"0%"},{"name":"储值卡充值","sale_list_actually_received":0,"orderNum":0,"accounting":""}]
     * sale_list_cashier : 3488
     */

    private String staff_name;//收银员名称
    private int rechargeCount;//充值计数
    private String login_datetime;//登录时间
    private double sumMoney;//营业额
    private double rechargeSum;//充值金额
    private String start_datetime;//首笔时间
    private String end_datetime;//末笔时间
    private String sign_out_datetime;//交班时间：空.收银中 else.已交班
    private int orderCount;//订单数
    private String sale_list_cashier;//收银员编号
    private boolean check;//是否展开
    private List<DetailBean> rechargeDetail;//充值记录
    private List<DetailBean> detail;//营业记录

    public boolean isCheck() {
        return check;
    }

    public void setCheck(boolean check) {
        this.check = check;
    }

    public String getStaff_name() {
        return staff_name;
    }

    public void setStaff_name(String staff_name) {
        this.staff_name = staff_name;
    }

    public int getRechargeCount() {
        return rechargeCount;
    }

    public void setRechargeCount(int rechargeCount) {
        this.rechargeCount = rechargeCount;
    }

    public String getLogin_datetime() {
        return login_datetime;
    }

    public void setLogin_datetime(String login_datetime) {
        this.login_datetime = login_datetime;
    }

    public double getSumMoney() {
        return sumMoney;
    }

    public void setSumMoney(double sumMoney) {
        this.sumMoney = sumMoney;
    }

    public double getRechargeSum() {
        return rechargeSum;
    }

    public void setRechargeSum(double rechargeSum) {
        this.rechargeSum = rechargeSum;
    }

    public String getStart_datetime() {
        return start_datetime;
    }

    public void setStart_datetime(String start_datetime) {
        this.start_datetime = start_datetime;
    }

    public String getEnd_datetime() {
        return end_datetime;
    }

    public void setEnd_datetime(String end_datetime) {
        this.end_datetime = end_datetime;
    }

    public String getSign_out_datetime() {
        return sign_out_datetime;
    }

    public void setSign_out_datetime(String sign_out_datetime) {
        this.sign_out_datetime = sign_out_datetime;
    }

    public int getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(int orderCount) {
        this.orderCount = orderCount;
    }

    public String getSale_list_cashier() {
        return sale_list_cashier;
    }

    public void setSale_list_cashier(String sale_list_cashier) {
        this.sale_list_cashier = sale_list_cashier;
    }

    public List<DetailBean> getRechargeDetail() {
        return rechargeDetail;
    }

    public void setRechargeDetail(List<DetailBean> rechargeDetail) {
        this.rechargeDetail = rechargeDetail;
    }

    public List<DetailBean> getDetail() {
        return detail;
    }

    public void setDetail(List<DetailBean> detail) {
        this.detail = detail;
    }

    public static class DetailBean {
        /**
         * recharge_method : 1
         * name : 现金
         * sale_list_actually_received : 0
         * orderNum : 0
         * accounting : 0
         */

        private int recharge_method;
        private String name;
        private double sale_list_actually_received;//实际收到的销售清单
        private int orderNum;//数量
        private String accounting;
        private double sale_list_return_money;//退款金额

        public int getRecharge_method() {
            return recharge_method;
        }

        public void setRecharge_method(int recharge_method) {
            this.recharge_method = recharge_method;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public double getSale_list_actually_received() {
            return sale_list_actually_received;
        }

        public void setSale_list_actually_received(double sale_list_actually_received) {
            this.sale_list_actually_received = sale_list_actually_received;
        }

        public int getOrderNum() {
            return orderNum;
        }

        public void setOrderNum(int orderNum) {
            this.orderNum = orderNum;
        }

        public String getAccounting() {
            return accounting;
        }

        public void setAccounting(String accounting) {
            this.accounting = accounting;
        }

        public double getSale_list_return_money() {
            return sale_list_return_money;
        }

        public void setSale_list_return_money(double sale_list_return_money) {
            this.sale_list_return_money = sale_list_return_money;
        }
    }
}
