package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.PurchaseListData;
import com.yxl.cashier_retail.utils.DFUtils;

import java.util.List;

/**
 * Describe:
 * Created by jingang on 2024/9/2
 */
public class SupplierPurchaseAdapter extends BaseAdapter<PurchaseListData> {

    public SupplierPurchaseAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_supplier_purchase;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        TextView tvStatus = holder.getView(R.id.tvItemStatus);
        //1-已提交(待收货)，2-已收货(待付款)，3-待确认，4-已完成，5-异常，6-作废
        switch (mDataList.get(position).getStatus()) {
            case 1:
                tvStatus.setText(getRstr(R.string.order_status9));
                break;
            case 2:
                tvStatus.setText(getRstr(R.string.order_status7));
                break;
            case 3:
                tvStatus.setText(getRstr(R.string.order_status10));
                break;
            case 4:
                tvStatus.setText(getRstr(R.string.order_status6));
                break;
            case 5:
                tvStatus.setText(getRstr(R.string.order_status11));
                break;
            default:
                tvStatus.setText("");
                break;
        }
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName, tvMobile, tvCount, tvTotal, tvStatus;
        tvName = holder.getView(R.id.tvItemName);
        tvMobile = holder.getView(R.id.tvItemMobile);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvStatus = holder.getView(R.id.tvItemStatus);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
        }
        tvName.setText(TextUtils.isEmpty(mDataList.get(position).getSupplierName()) ? "-" : mDataList.get(position).getSupplierName());
        tvMobile.setText(TextUtils.isEmpty(mDataList.get(position).getSupplierPhone()) ? "-" : mDataList.get(position).getSupplierPhone());
        tvCount.setText(String.valueOf(mDataList.get(position).getGoodsCategory()));
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getTotalPrice()));
        //1-已提交(待收货)，2-已收货(待付款)，3-待确认，4-已完成，5-异常，6-作废
        switch (mDataList.get(position).getStatus()) {
            case 1:
                tvStatus.setText(getRstr(R.string.order_status9));
                break;
            case 2:
                tvStatus.setText(getRstr(R.string.order_status7));
                break;
            case 3:
                tvStatus.setText(getRstr(R.string.order_status10));
                break;
            case 4:
                tvStatus.setText(getRstr(R.string.order_status6));
                break;
            case 5:
                tvStatus.setText(getRstr(R.string.order_status11));
                break;
            default:
                tvStatus.setText("");
                break;
        }
    }
}
