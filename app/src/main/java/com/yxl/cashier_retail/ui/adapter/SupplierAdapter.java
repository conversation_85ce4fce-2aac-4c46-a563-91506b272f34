package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.SupplierData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:补货-供货商（适配器）
 * Created by jingang on 2024/6/7
 */
public class SupplierAdapter extends BaseAdapter<SupplierData> {
    private int type;//1.供货商申请

    public void setType(int type) {
        this.type = type;
    }

    public SupplierAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_supplier;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvName, tvMobile, tvTotal, tvDebt, tvCount, tvEdit, tvDel, tvConfirm;
        tvName = holder.getView(R.id.tvItemName);
        tvMobile = holder.getView(R.id.tvItemMobile);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvDebt = holder.getView(R.id.tvItemDebt);
        tvCount = holder.getView(R.id.tvItemCount);
        tvEdit = holder.getView(R.id.tvItemEdit);
        tvDel = holder.getView(R.id.tvItemDel);
        tvConfirm = holder.getView(R.id.tvItemConfirm);

        if (type == 1) {
            tvEdit.setVisibility(View.GONE);
            tvDel.setVisibility(View.GONE);
            tvConfirm.setVisibility(View.VISIBLE);
        } else {
            tvEdit.setVisibility(View.VISIBLE);
            tvConfirm.setVisibility(View.VISIBLE);
            tvConfirm.setVisibility(View.GONE);
        }
        //1.购销 2.自采（本地）
        if (mDataList.get(position).getPurchaseType() == 2) {
            tvName.setText(mDataList.get(position).getSupplierName() + getRstr(R.string.local));
            ivImg.setVisibility(View.GONE);
        } else {
            tvName.setText(mDataList.get(position).getSupplierName());
            ivImg.setVisibility(View.VISIBLE);
        }
        tvMobile.setText(mDataList.get(position).getContacts() + " " + mDataList.get(position).getContactMobile());
        tvDebt.setText(DFUtils.getNum2(mDataList.get(position).getDebts()));
        tvCount.setText(String.valueOf(mDataList.get(position).getOrderCount()));

        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(v, position));
            tvEdit.setOnClickListener(v -> listener.onEditClick(v, position));
            tvDel.setOnClickListener(v -> listener.onDelClick(v, position));
            tvConfirm.setOnClickListener(v -> listener.onConfirmClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onEditClick(View view, int position);

        void onDelClick(View view, int position);

        void onConfirmClick(View view, int position);
    }
}
