package com.yxl.cashier_retail.ui.bean;

import java.util.List;

/**
 * Describe:百货豆列表（实体类）
 * Created by jingang on 2024/8/28
 */
public class BeansListData {
    /**
     * status : 1
     * msg : 查询成功！
     * data : [{"beanCount":985,"saletime":"2024-08-24 14:41:13","saleListUnique":1724481673260,"name":"大葱卷煎饼","state":6},{"beanCount":147,"saletime":"2024-08-24 14:40:17","saleListUnique":1724481617106,"name":"大葱卷煎饼","state":6},{"beanCount":156,"saletime":"2024-08-24 14:39:25","saleListUnique":1724481565217,"name":"大葱卷煎饼","state":6},{"beanCount":1164,"saletime":"2024-08-24 14:32:31","saleListUnique":1724481151589,"name":"大葱卷煎饼","state":6},{"beanCount":1014,"saletime":"2024-08-24 14:29:30","saleListUnique":1724480969948,"name":"大葱卷煎饼","state":6},{"beanCount":10,"saletime":"2024-08-24 11:29:04","saleListUnique":1724470144431,"name":"大葱卷煎饼","state":6},{"beanCount":70,"saletime":"2024-08-13 11:32:25","saleListUnique":1723519945357,"name":"微信用户","state":7},{"beanCount":493,"saletime":"2024-08-13 11:02:26","saleListUnique":1723518146004,"name":"","state":7},{"beanCount":100,"saletime":"2024-08-13 08:04:22","saleListUnique":1723507462950,"name":"微信用户","state":6},{"beanCount":100,"saletime":"2024-08-13 07:56:31","saleListUnique":1723506991627,"name":"微信用户","state":6},{"beanCount":100,"saletime":"2024-08-10 18:01:17","saleListUnique":1723284077293,"name":"微信用户","state":6},{"beanCount":100,"saletime":"2024-08-10 17:55:05","saleListUnique":1723283705127,"name":"微信用户","state":6},{"beanCount":99,"saletime":"2024-08-10 17:49:44","saleListUnique":1723283384389,"name":"微信用户","state":6},{"beanCount":110,"saletime":"2024-08-10 17:40:45","saleListUnique":1723282845187,"name":"微信用户","state":6},{"beanCount":190,"saletime":"2024-08-10 17:37:30","saleListUnique":1723282650573,"name":"微信用户","state":7},{"beanCount":101,"saletime":"2024-08-10 17:33:21","saleListUnique":1723282401821,"name":"微信用户","state":6},{"beanCount":755,"saletime":"2024-08-10 17:27:28","saleListUnique":1723282048582,"name":"","state":6},{"beanCount":91,"saletime":"2024-08-10 17:19:12","saleListUnique":1723281552589,"name":"微信用户","state":6},{"beanCount":192,"saletime":"2024-08-10 17:04:44","saleListUnique":1723280684088,"name":"微信用户","state":6},{"beanCount":80,"saletime":"2024-08-10 17:02:33","saleListUnique":1723280553836,"name":"微信用户","state":6}]
     * count : 41551
     * useHelpUrl : http://file.buyhoo.cc/publicImage/bean/help.html
     */

    private int status;
    private String msg;
    private int count;//剩余百货豆数量
    private String useHelpUrl;//百货豆使用帮助
    private List<DataBean> data;
    private ResDataBean resData;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getUseHelpUrl() {
        return useHelpUrl;
    }

    public void setUseHelpUrl(String useHelpUrl) {
        this.useHelpUrl = useHelpUrl;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public ResDataBean getResData() {
        return resData;
    }

    public void setResData(ResDataBean resData) {
        this.resData = resData;
    }

    public static class DataBean {
        /**
         * beanCount : 985.0
         * saletime : 2024-08-24 14:41:13
         * saleListUnique : 1724481673260
         * name : 大葱卷煎饼
         * state : 6
         */

        private double beanCount;//数量
        private String saletime;//时间
        private String saleListUnique;//订单号
        private String name;//会员名称
        private int state;////订单状态只有出账有：0 待处理 1 到账 4 驳回

        public double getBeanCount() {
            return beanCount;
        }

        public void setBeanCount(double beanCount) {
            this.beanCount = beanCount;
        }

        public String getSaletime() {
            return saletime;
        }

        public void setSaletime(String saletime) {
            this.saletime = saletime;
        }

        public String getSaleListUnique() {
            return saleListUnique;
        }

        public void setSaleListUnique(String saleListUnique) {
            this.saleListUnique = saleListUnique;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getState() {
            return state;
        }

        public void setState(int state) {
            this.state = state;
        }
    }

    public static class ResDataBean {
        /**
         * "platformBeans":478,
         * "beansUse":11293.00,
         * "listTotal":21269.10,
         * "listCount":68
         */

        private double listCount;
        private double listTotal;
        private double platformBeans;
        private double beansUse;

        public double getListCount() {
            return listCount;
        }

        public void setListCount(double listCount) {
            this.listCount = listCount;
        }

        public double getListTotal() {
            return listTotal;
        }

        public void setListTotal(double listTotal) {
            this.listTotal = listTotal;
        }

        public double getPlatformBeans() {
            return platformBeans;
        }

        public void setPlatformBeans(double platformBeans) {
            this.platformBeans = platformBeans;
        }

        public double getBeansUse() {
            return beansUse;
        }

        public void setBeansUse(double beansUse) {
            this.beansUse = beansUse;
        }
    }
}
