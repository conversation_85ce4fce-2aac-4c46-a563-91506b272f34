package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;
import java.util.List;

/**
 * 提交订单信息（实体类）
 */
public class MallOrderSettlementData implements Serializable {
    /**
     * “sum_amt_all”: 0.02, //商品总金额
     * “should_amt_all”: 0.00, //订单总金额
     * “deduct_amt_all”: 0.02, //金圈币总抵扣金额
     * “jqb_count”: 4766.51, //店铺总金圈币数量
     * “jqb_max_count”: 0.02, //本次最多可以抵扣金圈数量
     * “sum_delivery_price”: 0.00, //总配送费
     * “sum_count”: 2 //商品总数量
     */
    private double sum_amt_all; //商品总金额
    private double should_amt_all; //订单总金额
    private double sum_price;
    private double deduct_amt_all; //金圈币总抵扣金额
    private double jqb_count; //店铺总金圈币数量
    private double jqb_max_count; //本次最多可以抵扣金圈数量
    private double sum_delivery_price; //总配送费
    private double loan_amt_all;
    private int sum_count;//商品总数量
    private double admin_coupon;//跨店优惠券金额
    private String adminCouponId;//跨店优惠券id
    private double deduct_gap_price;//差价
    private List<Settlement> settlementList;
    private int special_type;//普通订单；1、预售订单
    private String special_msg;//预售订单返回预售订单的配送时间

    public double getSum_price() {
        return sum_price;
    }

    public void setSum_price(double sum_price) {
        this.sum_price = sum_price;
    }

    public double getAdmin_coupon() {
        return admin_coupon;
    }

    public void setAdmin_coupon(double admin_coupon) {
        this.admin_coupon = admin_coupon;
    }

    public String getAdminCouponId() {
        return adminCouponId;
    }

    public void setAdminCouponId(String adminCouponId) {
        this.adminCouponId = adminCouponId;
    }


    public double getLoan_amt_all() {
        return loan_amt_all;
    }

    public void setLoan_amt_all(double loan_amt_all) {
        this.loan_amt_all = loan_amt_all;
    }


    public int getSum_count() {
        return sum_count;
    }

    public void setSum_count(int sum_count) {
        this.sum_count = sum_count;
    }

    public double getSum_amt_all() {
        return sum_amt_all;
    }

    public void setSum_amt_all(double sum_amt_all) {
        this.sum_amt_all = sum_amt_all;
    }

    public double getShould_amt_all() {
        return should_amt_all;
    }

    public void setShould_amt_all(double should_amt_all) {
        this.should_amt_all = should_amt_all;
    }

    public double getDeduct_amt_all() {
        return deduct_amt_all;
    }

    public void setDeduct_amt_all(double deduct_amt_all) {
        this.deduct_amt_all = deduct_amt_all;
    }

    public double getJqb_count() {
        return jqb_count;
    }

    public void setJqb_count(double jqb_count) {
        this.jqb_count = jqb_count;
    }

    public double getJqb_max_count() {
        return jqb_max_count;
    }

    public void setJqb_max_count(double jqb_max_count) {
        this.jqb_max_count = jqb_max_count;
    }

    public double getSum_delivery_price() {
        return sum_delivery_price;
    }

    public void setSum_delivery_price(double sum_delivery_price) {
        this.sum_delivery_price = sum_delivery_price;
    }

    public double getDeduct_gap_price() {
        return deduct_gap_price;
    }

    public void setDeduct_gap_price(double deduct_gap_price) {
        this.deduct_gap_price = deduct_gap_price;
    }

    public List<Settlement> getSettlementList() {
        return settlementList;
    }

    public void setSettlementList(List<Settlement> settlementList) {
        this.settlementList = settlementList;
    }

    public int getSpecial_type() {
        return special_type;
    }

    public void setSpecial_type(int special_type) {
        this.special_type = special_type;
    }

    public String getSpecial_msg() {
        return special_msg;
    }

    public void setSpecial_msg(String special_msg) {
        this.special_msg = special_msg;
    }

    public class Settlement {
        private double cost_amt;
        private double sum_deduct_amt;
        private double should_amt;
        private double free_delivery_price;
        private double actual_delivery_price;
        private double deduct_amt;
        private double sum_good_count;
        private double sum_amt;
        private double delivery_price;
        private String company_name;
        private String area_dict_num;
        private String company_code;
        private List<GoodList> good_list;
        private String order_remarks;
        private String order_time;
        private String pay_date;
        private String pay_left_time;
        private String order_code;
        private double loan_amt;
        private double notLoanMoney;
        private String main_order_no;
        private List<Fulls> fullgiftList;
        private double coupon_amount;
        private double loan_money;
        private String coupon_id;
        private String record_id;
        private String collect_name;
        private String collect_phone;
        private String collect_address;
        // 订单支付过期时间
        private long surplusTime;
        // 订单状态
        private int order_status;
        //1.预售订单 0.普通订单
        private int special_type;
        // 预售发货时间
        private String special_msg;
        //筐押金
        private double bucket_deposit_total;

        public double getBucket_deposit_total() {
            return bucket_deposit_total;
        }

        public void setBucket_deposit_total(double bucket_deposit_total) {
            this.bucket_deposit_total = bucket_deposit_total;
        }

        public int getSpecial_type() {
            return special_type;
        }

        public void setSpecial_type(int special_type) {
            this.special_type = special_type;
        }

        public String getSpecial_msg() {
            return special_msg;
        }

        public void setSpecial_msg(String special_msg) {
            this.special_msg = special_msg;
        }

        public int getOrder_status() {
            return order_status;
        }

        public void setOrder_status(int order_status) {
            this.order_status = order_status;
        }

        public long getSurplusTime() {
            return surplusTime;
        }

        public void setSurplusTime(long surplusTime) {
            this.surplusTime = surplusTime;
        }

        public String getCollect_name() {
            return collect_name;
        }

        public void setCollect_name(String collect_name) {
            this.collect_name = collect_name;
        }

        public String getCollect_phone() {
            return collect_phone;
        }

        public void setCollect_phone(String collect_phone) {
            this.collect_phone = collect_phone;
        }

        public String getCollect_address() {
            return collect_address;
        }

        public void setCollect_address(String collect_address) {
            this.collect_address = collect_address;
        }

        public String getPay_date() {
            return pay_date;
        }

        public void setPay_date(String pay_date) {
            this.pay_date = pay_date;
        }


        public double getLoan_money() {
            return loan_money;
        }

        public void setLoan_money(double loan_money) {
            this.loan_money = loan_money;
        }


        public List<Fulls> getFullgiftList() {
            return fullgiftList;
        }

        public void setFullgiftList(List<Fulls> fullgiftList) {
            this.fullgiftList = fullgiftList;
        }


        public double getCoupon_amount() {
            return coupon_amount;
        }

        public void setCoupon_amount(double coupon_amount) {
            this.coupon_amount = coupon_amount;
        }

        public String getCoupon_id() {
            return coupon_id;
        }

        public void setCoupon_id(String coupon_id) {
            this.coupon_id = coupon_id;
        }

        public String getRecord_id() {
            return record_id;
        }

        public void setRecord_id(String record_id) {
            this.record_id = record_id;
        }


        public String getMain_order_no() {
            return main_order_no;
        }

        public void setMain_order_no(String main_order_no) {
            this.main_order_no = main_order_no;
        }


        public double getLoan_amt() {
            return loan_amt;
        }

        public void setLoan_amt(double loan_amt) {
            this.loan_amt = loan_amt;
        }

        public double getNotLoanMoney() {
            return notLoanMoney;
        }

        public void setNotLoanMoney(double notLoanMoney) {
            this.notLoanMoney = notLoanMoney;
        }


        public String getOrder_code() {
            return order_code;
        }

        public void setOrder_code(String order_code) {
            this.order_code = order_code;
        }

        public String getPay_left_time() {
            return pay_left_time;
        }

        public void setPay_left_time(String pay_left_time) {
            this.pay_left_time = pay_left_time;
        }

        public String getOrder_time() {
            return order_time;
        }

        public void setOrder_time(String order_time) {
            this.order_time = order_time;
        }

        public String getOrder_remarks() {
            return order_remarks;
        }

        public void setOrder_remarks(String order_remarks) {
            this.order_remarks = order_remarks;
        }

        public double getCost_amt() {
            return cost_amt;
        }

        public void setCost_amt(double cost_amt) {
            this.cost_amt = cost_amt;
        }

        public double getSum_deduct_amt() {
            return sum_deduct_amt;
        }

        public void setSum_deduct_amt(double sum_deduct_amt) {
            this.sum_deduct_amt = sum_deduct_amt;
        }

        public double getShould_amt() {
            return should_amt;
        }

        public void setShould_amt(double should_amt) {
            this.should_amt = should_amt;
        }

        public double getFree_delivery_price() {
            return free_delivery_price;
        }

        public void setFree_delivery_price(double free_delivery_price) {
            this.free_delivery_price = free_delivery_price;
        }

        public double getActual_delivery_price() {
            return actual_delivery_price;
        }

        public void setActual_delivery_price(double actual_delivery_price) {
            this.actual_delivery_price = actual_delivery_price;
        }

        public double getDeduct_amt() {
            return deduct_amt;
        }

        public void setDeduct_amt(double deduct_amt) {
            this.deduct_amt = deduct_amt;
        }

        public double getSum_good_count() {
            return sum_good_count;
        }

        public void setSum_good_count(double sum_good_count) {
            this.sum_good_count = sum_good_count;
        }

        public double getSum_amt() {
            return sum_amt;
        }

        public void setSum_amt(double sum_amt) {
            this.sum_amt = sum_amt;
        }

        public double getDelivery_price() {
            return delivery_price;
        }

        public void setDelivery_price(double delivery_price) {
            this.delivery_price = delivery_price;
        }

        public String getCompany_name() {
            return null == company_name ? "" : company_name;
        }

        public void setCompany_name(String company_name) {
            this.company_name = company_name;
        }

        public String getArea_dict_num() {
            return area_dict_num;
        }

        public void setArea_dict_num(String area_dict_num) {
            this.area_dict_num = area_dict_num;
        }

        public String getCompany_code() {
            return company_code;
        }

        public void setCompany_code(String company_code) {
            this.company_code = company_code;
        }

        public List<GoodList> getGood_list() {
            return good_list;
        }

        public void setGood_list(List<GoodList> good_list) {
            this.good_list = good_list;
        }
    }

    public static class GoodList {
        /**
         * good_count : 1
         * order_no :
         * goods_name :
         * shop_unique :
         * create_time : 1589358902000
         * stock_count :
         * good_id : 1000000418
         * is_order : 1
         * sum_amt : 0.01
         * spec_id : -1
         * online_price : 0.01
         * company_code :
         * id : 593
         * spec_name :
         * is_check : 1
         * goods_img :
         * delivery_price : 0.0
         */

        private int good_count;
        private String order_no;
        private String goods_name;
        private String shop_unique;
        private long create_time;
        private String stock_count;
        private int good_id;
        private int is_order;
        private double sum_amt;
        private int spec_id;
        private double online_price;
        private String company_code;
        private int id;
        private String spec_name;
        private int is_check;
        private String goods_img;
        private double delivery_price;
        private int loan_count;
        private double loan_price;
        private int is_loan_sataus;
        private int start_order;
        private double sub_sum_amt;
        private double sub_loan_amt;
        private double loanCut;
        private double subLoanSum;
        private double subSum;//详情商品小计
        private String cornucpia; // 聚宝盆商品标识 Y是聚宝盆商品

        public double getSubSum() {
            return subSum;
        }

        public void setSubSum(double subSum) {
            this.subSum = subSum;
        }


        public double getLoanCut() {
            return loanCut;
        }

        public void setLoanCut(double loanCut) {
            this.loanCut = loanCut;
        }

        public double getSubLoanSum() {
            return subLoanSum;
        }

        public void setSubLoanSum(double subLoanSum) {
            this.subLoanSum = subLoanSum;
        }

        public int getLoan_count() {
            return loan_count;
        }

        public void setLoan_count(int loan_count) {
            this.loan_count = loan_count;
        }

        public double getLoan_price() {
            return loan_price;
        }

        public void setLoan_price(double loan_price) {
            this.loan_price = loan_price;
        }

        public int getIs_loan_sataus() {
            return is_loan_sataus;
        }

        public void setIs_loan_sataus(int is_loan_sataus) {
            this.is_loan_sataus = is_loan_sataus;
        }

        public int getStart_order() {
            return start_order;
        }

        public void setStart_order(int start_order) {
            this.start_order = start_order;
        }

        public double getSub_sum_amt() {
            return sub_sum_amt;
        }

        public void setSub_sum_amt(double sub_sum_amt) {
            this.sub_sum_amt = sub_sum_amt;
        }

        public double getSub_loan_amt() {
            return sub_loan_amt;
        }

        public void setSub_loan_amt(double sub_loan_amt) {
            this.sub_loan_amt = sub_loan_amt;
        }


        public int getGood_count() {
            return good_count;
        }

        public void setGood_count(int good_count) {
            this.good_count = good_count;
        }

        public String getOrder_no() {
            return order_no;
        }

        public void setOrder_no(String order_no) {
            this.order_no = order_no;
        }

        public String getGoods_name() {
            return goods_name;
        }

        public void setGoods_name(String goods_name) {
            this.goods_name = goods_name;
        }

        public String getShop_unique() {
            return shop_unique;
        }

        public void setShop_unique(String shop_unique) {
            this.shop_unique = shop_unique;
        }

        public long getCreate_time() {
            return create_time;
        }

        public void setCreate_time(long create_time) {
            this.create_time = create_time;
        }

        public String getStock_count() {
            return stock_count;
        }

        public void setStock_count(String stock_count) {
            this.stock_count = stock_count;
        }

        public int getGood_id() {
            return good_id;
        }

        public void setGood_id(int good_id) {
            this.good_id = good_id;
        }

        public int getIs_order() {
            return is_order;
        }

        public void setIs_order(int is_order) {
            this.is_order = is_order;
        }

        public double getSum_amt() {
            return sum_amt;
        }

        public void setSum_amt(double sum_amt) {
            this.sum_amt = sum_amt;
        }

        public int getSpec_id() {
            return spec_id;
        }

        public void setSpec_id(int spec_id) {
            this.spec_id = spec_id;
        }

        public double getOnline_price() {
            return online_price;
        }

        public void setOnline_price(double online_price) {
            this.online_price = online_price;
        }

        public String getCompany_code() {
            return company_code;
        }

        public void setCompany_code(String company_code) {
            this.company_code = company_code;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getSpec_name() {
            return spec_name;
        }

        public void setSpec_name(String spec_name) {
            this.spec_name = spec_name;
        }

        public int getIs_check() {
            return is_check;
        }

        public void setIs_check(int is_check) {
            this.is_check = is_check;
        }

        public String getGoods_img() {
            return goods_img;
        }

        public void setGoods_img(String goods_img) {
            this.goods_img = goods_img;
        }

        public double getDelivery_price() {
            return delivery_price;
        }

        public void setDelivery_price(double delivery_price) {
            this.delivery_price = delivery_price;
        }

        public String getCornucpia() {
            return cornucpia;
        }

        public void setCornucpia(String cornucpia) {
            this.cornucpia = cornucpia;
        }
    }


    public static class Fulls implements Serializable {

        private String endDate;
        private String compayCode;
        private List<MallCartData.FullgiftListBean.GiftCouponBean> giftCoupon;
        private int gift_id;
        private List<MallCartData.FullgiftListBean.GiftGoodsBean> giftGoods;
        private double meetAmount;
        private String startDate;

        public int getGift_id() {
            return gift_id;
        }

        public void setGift_id(int gift_id) {
            this.gift_id = gift_id;
        }


        public String getEndDate() {
            return endDate;
        }

        public void setEndDate(String endDate) {
            this.endDate = endDate;
        }

        public String getCompayCode() {
            return compayCode;
        }

        public void setCompayCode(String compayCode) {
            this.compayCode = compayCode;
        }

        public List<MallCartData.FullgiftListBean.GiftCouponBean> getGiftCoupon() {
            return giftCoupon;
        }

        public void setGiftCoupon(List<MallCartData.FullgiftListBean.GiftCouponBean> giftCoupon) {
            this.giftCoupon = giftCoupon;
        }

        public List<MallCartData.FullgiftListBean.GiftGoodsBean> getGiftGoods() {
            return giftGoods;
        }

        public void setGiftGoods(List<MallCartData.FullgiftListBean.GiftGoodsBean> giftGoods) {
            this.giftGoods = giftGoods;
        }

        public double getMeetAmount() {
            return meetAmount;
        }

        public void setMeetAmount(double meetAmount) {
            this.meetAmount = meetAmount;
        }

        public String getStartDate() {
            return startDate;
        }

        public void setStartDate(String startDate) {
            this.startDate = startDate;
        }


        public class GiftGoods implements Serializable {

            private final String TAG = getClass().getSimpleName();

            private String goods_name;
            private String goods_barcode;
            private int goods_id;
            private String gfree_quantity;
            private String goods_img;

            public String getGoods_name() {
                return goods_name;
            }

            public void setGoods_name(String goods_name) {
                this.goods_name = goods_name;
            }

            public String getGoods_barcode() {
                return goods_barcode;
            }

            public void setGoods_barcode(String goods_barcode) {
                this.goods_barcode = goods_barcode;
            }

            public int getGoods_id() {
                return goods_id;
            }

            public void setGoods_id(int goods_id) {
                this.goods_id = goods_id;
            }

            public String getGfree_quantity() {
                return gfree_quantity;
            }

            public void setGfree_quantity(String gfree_quantity) {
                this.gfree_quantity = gfree_quantity;
            }

            public String getGoods_img() {
                return goods_img;
            }

            public void setGoods_img(String goods_img) {
                this.goods_img = goods_img;
            }


        }

        public static class Coupon implements Serializable {
            private double coupon_amount;
            private String coupon_id;
            private String record_id;
            private String companyCode;

            public Coupon() {
            }

            public String getCompanyCode() {
                return companyCode;
            }

            public void setCompanyCode(String companyCode) {
                this.companyCode = companyCode;
            }


            public double getCoupon_amount() {
                return coupon_amount;
            }

            public void setCoupon_amount(double coupon_amount) {
                this.coupon_amount = coupon_amount;
            }

            public String getCoupon_id() {
                return coupon_id;
            }

            public void setCoupon_id(String coupon_id) {
                this.coupon_id = coupon_id;
            }

            public String getRecord_id() {
                return record_id;
            }

            public void setRecord_id(String record_id) {
                this.record_id = record_id;
            }

        }
    }
}
