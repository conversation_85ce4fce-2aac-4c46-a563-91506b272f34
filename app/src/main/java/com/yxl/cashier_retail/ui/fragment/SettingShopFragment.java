package com.yxl.cashier_retail.ui.fragment;

import android.text.TextUtils;

import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmSettingShopBinding;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.commonlibrary.bean.LoginData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.litepal.LitePal;

import java.util.HashMap;
import java.util.Map;

/**
 * Describe:设置-店铺信息
 * Created by jingang on 2024/5/13
 */
public class SettingShopFragment extends BaseFragment<FmSettingShopBinding> {

    @Override
    protected FmSettingShopBinding getViewBinding() {
        return FmSettingShopBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        //名称
        mBinding.etShopName.setOnEditorActionListener((v, actionId, event) -> {
            if (TextUtils.isEmpty(mBinding.etShopName.getText().toString().trim())) {
                showToast(1, getRstr(R.string.input_shop_name));
            } else {
                postShopInfo(0, mBinding.etShopName.getText().toString().trim());
            }
            return true;
        });
        //位置
        mBinding.etShopAddress.setOnEditorActionListener((v, actionId, event) -> {
            if (TextUtils.isEmpty(mBinding.etShopAddress.getText().toString().trim())) {
                showToast(1, getRstr(R.string.input_shop_address));
            } else {
                postShopInfo(1, mBinding.etShopAddress.getText().toString().trim());
            }
            return true;
        });
    }

    @Override
    protected void initData() {
        if (getLoginData() != null) {
            mBinding.tvShopID.setText(getLoginData().getShopUnique());
            mBinding.etShopName.setText(getLoginData().getShop_name());
            mBinding.etShopAddress.setText(getLoginData().getShop_address_detail());
            mBinding.tvStaffUnique.setText(getLoginData().getCashier_id());
            mBinding.tvMobile.setText(getLoginData().getStaff_phone());
        }
    }

    /**
     * 店铺信息编辑
     *
     * @param type 0.名称 1.位置
     * @param name
     */
    private void postShopInfo(int type, String name) {
        hideSoftInput(getActivity());
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        if (type == 0) {
            params.put("shopName", name);
        } else {
            params.put("shopAddress", name);
        }
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(getActivity(),
                ZURL.getShopInfoEdit(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        LoginData data = LitePal.findFirst(LoginData.class);
                        if (data != null) {
                            if (type == 0) {
                                data.setShop_name(name);
                            } else {
                                data.setShop_address_detail(name);
                            }
                            data.save();
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.SHOP_INFO));
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }
}
