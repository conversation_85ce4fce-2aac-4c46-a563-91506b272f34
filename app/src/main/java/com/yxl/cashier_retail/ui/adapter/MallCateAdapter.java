package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.MallCateData;
import com.yxl.commonlibrary.utils.StringUtils;

/**
 * Describe:商城-分类（适配器）
 * Created by jingang on 2024/6/3
 */
public class MallCateAdapter extends BaseAdapter<MallCateData> {

    public MallCateAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_mall_cate;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvName = holder.getView(R.id.tvItemName);

        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoods_kind_image()))
                .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img).circleCrop())
                .into(ivImg);
        tvName.setText(mDataList.get(position).getGoods_kind_name());
    }
}
