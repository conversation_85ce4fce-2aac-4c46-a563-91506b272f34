package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogUnitBinding;
import com.yxl.cashier_retail.ui.adapter.UnitDialogAdapter;
import com.yxl.cashier_retail.ui.bean.UnitData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:dialog（商品单位）
 * Created by jingang on 2024/7/1
 */
@SuppressLint({"NonConstantResourceId", "StaticFieldLeak"})
public class UnitDialog extends BaseDialog<DialogUnitBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private static String name, keyWords;
    private static View viewIcon;
    private final Animation openAnim, closeAnim;

    private List<UnitData> dataList = new ArrayList<>();
    private UnitDialogAdapter mAdapter;

    public static void showDialog(Activity activity, String name, View viewIcon, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        UnitDialog.listener = listener;
        UnitDialog.name = name;
        UnitDialog.viewIcon = viewIcon;
        keyWords = "";
        UnitDialog dialog = new UnitDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 7);
        dialog.show();
    }

    public UnitDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        openAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_1);
        closeAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_2);
        openAnim.setFillAfter(true);
        closeAnim.setFillAfter(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.ivDialogSearchClear.setOnClickListener(this);
        mBinding.tvDialogAdd.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        mBinding.etDialogSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                mBinding.ivDialogSearchClear.setVisibility(TextUtils.isEmpty(keyWords) ? View.GONE : View.VISIBLE);
            }
        });
        mBinding.etDialogSearch.setOnEditorActionListener((v, actionId, event) -> {
            getUnitList();
            return true;
        });
        setAdapter();
        getUnitList();
    }

    @Override
    protected DialogUnitBinding getViewBinding() {
        return DialogUnitBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
        if (viewIcon != null) {
            viewIcon.startAnimation(openAnim);
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
        if (viewIcon != null) {
            viewIcon.startAnimation(closeAnim);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.ivDialogSearchClear:
                //清除搜索输入
                mBinding.etDialogSearch.setText("");
                keyWords = "";
                getUnitList();
                break;
            case R.id.tvDialogAdd:
                //新增
                UnitEditDialog.showDialog(mActivity, "", "", name -> getUnitList());
                break;
            case R.id.tvDialogConfirm:
                //确认
                if (TextUtils.isEmpty(name)) {
                    showToast(1, getRstr(R.string.select_goods_unit));
                    return;
                }
                if (listener != null) {
                    listener.onConfirm(name);
                    dismiss();
                }
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new UnitDialogAdapter(getContext());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new UnitDialogAdapter.MyListener() {
            @Override
            public void onItemClick(int position) {
                if (!dataList.get(position).isSelect()) {
                    for (int i = 0; i < dataList.size(); i++) {
                        if (dataList.get(i).isSelect()) {
                            dataList.get(i).setSelect(false);
                            mAdapter.notifyItemChanged(i);
                        }
                    }
                    dataList.get(position).setSelect(true);
                    mAdapter.notifyItemChanged(position);
                    name = dataList.get(position).getGoods_unit();
                }
            }

            @Override
            public void onEditClick(int position) {
                UnitEditDialog.showDialog(mActivity,
                        dataList.get(position).getGoods_unit_id(),
                        dataList.get(position).getGoods_unit(),
                        name -> {
                            dataList.get(position).setGoods_unit(name);
                            mAdapter.notifyItemChanged(position);
                        });
            }

            @Override
            public void onDelClick(int position) {
                postGoodsUnitDel(dataList.get(position).getGoods_unit_id(), position);
            }
        });
        mBinding.smartRefreshLayout.setOnRefreshListener(refreshLayout -> getUnitList());
        mBinding.smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 商品单位
     */
    private void getUnitList() {
        hideSoftInput(mActivity);
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("goods_unit", keyWords);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getGoodsUnit(),
                params,
                UnitData.class,
                new RequestListListener<UnitData>() {
                    @Override
                    public void onResult(List<UnitData> list) {
                        hideDialog();
                        mBinding.smartRefreshLayout.finishRefresh();
                        dataList.clear();
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            if (TextUtils.isEmpty(name)) {
                                dataList.get(0).setSelect(true);
                                name = dataList.get(0).getGoods_unit();
                            } else {
                                for (int i = 0; i < dataList.size(); i++) {
                                    if (!TextUtils.isEmpty(dataList.get(i).getGoods_unit())) {
                                        if (dataList.get(i).getGoods_unit().equals(name)) {
                                            dataList.get(i).setSelect(true);
                                        }
                                    }
                                }
                            }
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 单位删除
     *
     * @param id
     * @param position
     */
    private void postGoodsUnitDel(String id, int position) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("goods_unit_id", id);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getGoodsUnitDel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        dataList.remove(position);
                        mAdapter.remove(position);
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(String name);
    }
}
