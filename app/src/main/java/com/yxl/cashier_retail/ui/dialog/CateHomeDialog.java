package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogCateHomeBinding;
import com.yxl.cashier_retail.ui.adapter.CateHomeAdapter;
import com.yxl.cashier_retail.ui.bean.CateChildData;
import com.yxl.cashier_retail.ui.bean.CateData;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.litepal.LitePal;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:dialog（首页分类列表）
 * Created by jingang on 2024/8/21
 */
@SuppressLint("NonConstantResourceId")
public class CateHomeDialog extends BaseDialog<DialogCateHomeBinding> implements View.OnClickListener {
    private static Activity mActivity;

    private CateHomeAdapter mAdapter;
    private List<CateData> dataList = new ArrayList<>();

    public static void showDialog(Activity activity) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        CateHomeDialog dialog = new CateHomeDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 7);
        dialog.show();
    }

    public CateHomeDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        setAdapter();
        getCateList(0);
    }

    @Override
    protected DialogCateHomeBinding getViewBinding() {
        return DialogCateHomeBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //新增一级分类
                showDialogCateEdit(0, "", "", "", 0, "");
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new CateHomeAdapter(getContext());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new CateHomeAdapter.MyListener() {
            @Override
            public void onMoreClick(View view, int position) {
                //展开
                dataList.get(position).setCheck(!dataList.get(position).isCheck());
                mAdapter.notifyItemChanged(position);
            }

            @Override
            public void onEditClick(View view, int position) {
                //一级分类编辑
                int imgId = TextUtils.isEmpty(dataList.get(position).getKindIconId()) ? 0 : Integer.parseInt(dataList.get(position).getKindIconId());
                showDialogCateEdit(1,
                        dataList.get(position).getGroupUnique(),
                        "",
                        dataList.get(position).getGroupName(),
                        imgId,
                        dataList.get(position).getKindIcon());
            }

            @Override
            public void onDelClick(View view, int position) {
                //一级分类删除
                postCateEdit(0, dataList.get(position).getGroupUnique());
            }

            @Override
            public void onAddClick(View view, int position) {
                //二级分类添加
                showDialogCateEdit(2, dataList.get(position).getGroupUnique(), "", "", 0, "");
            }

            @Override
            public void onChildEditClick(View view, int position, int positionChild) {
                //二级分类编辑
                showDialogCateEdit(3,
                        "",
                        dataList.get(position).getKindDetail().get(positionChild).getKindUnique(),
                        dataList.get(position).getKindDetail().get(positionChild).getKindName(),
                        0,
                        "");
            }

            @Override
            public void onChildDelClick(View view, int position, int positionChild) {
                //二级分类删除
                postCateEdit(1, dataList.get(position).getKindDetail().get(positionChild).getKindUnique());
            }
        });
        mBinding.smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            getCateList(0);
        });
        mBinding.smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 分类编辑
     *
     * @param type        0.一级分类新增 1.一级分类编辑 2.二级分类新增 3.二级分类编辑
     * @param unique      一级分类编号
     * @param childUnique 二级分类编号
     * @param name        分类名称
     * @param imgId       分类图标id
     * @param img         分类图标
     */
    private void showDialogCateEdit(int type, String unique, String childUnique, String name, int imgId, String img) {
        CateEditHomeDialog.showDialog(mActivity, type, unique, childUnique, name, imgId, img, () -> getCateList(1));
    }

    /**
     * 分类列表
     *
     * @param type 1.更新本地数据
     */
    private void getCateList(int type) {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        showDialog();
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getCateList(),
                params,
                CateData.class,
                new RequestListListener<CateData>() {
                    @Override
                    public void onResult(List<CateData> list) {
                        hideDialog();
                        mBinding.smartRefreshLayout.finishRefresh();
                        dataList.clear();
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                        //更新本地数据
                        if (type == 1) {
                            CateData data = LitePal.findFirst(CateData.class);
                            if (data != null) {
                                LitePal.deleteAll(CateData.class);
                            }
                            if (LitePal.saveAll(list)) {
                                List<CateChildData> childList = new ArrayList<>();
                                for (int i = 0; i < list.size(); i++) {
                                    childList.addAll(list.get(i).getKindDetail());
                                }
                                if (LitePal.saveAll(childList)) {
                                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.CATE_LIST));
                                }
                            }
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 分类新增、删除、更新
     *
     * @param type 0.删除一级分类 1.删除二级分类
     */
    private void postCateEdit(int type, String unique) {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("kindUnique", unique);
        params.put("validType", 2);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getCateEdit(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        getCateList(1);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }
}
