package com.yxl.cashier_retail.ui.bean;

import java.util.List;

/**
 * Describe:交班查询（实体类）
 * Created by jingang on 2024/8/29
 */
public class ShiftData {
    /**
     * status : 0
     * msg : 交班成功！
     * data : {"staff_name":"益农社服务员","login_id":42860,"shop_unique":1.536215939565E12,"member_card":0,"cusRecharge":"0","sum":0,"receivable":0,"total":0,"login_datetime":"2024-08-22 14:17:54","shopUnique":1536215939565,"staff_id":3586,"sign_out_datetime":"2024-08-22 14:48:36","staffName":"益农社服务员","retListTotal":0,"startTime":1724307474000,"endTime":"2024-08-22 14:48:36","staffId":3586}
     * data1 : [{"payment_total":0,"pay_ment":"平台送豆"},{"payment_total":0,"pay_ment":"供货商送豆"},{"payment_total":0,"pay_ment":"店铺送豆"},{"payment_total":0,"pay_ment":"百货豆抵扣数量"},{"payment_total":0,"pay_ment":"网上收入"},{"payment_total":0,"pay_ment":"平台优惠券抵扣"},{"payment_total":0,"pay_ment":"店内优惠券抵扣"}]
     * address : [{"recharge_method":1,"name":"现金","sale_list_actually_received":0,"orderNum":0},{"recharge_method":2,"name":"微信","sale_list_actually_received":0,"orderNum":0},{"recharge_method":3,"name":"支付宝","sale_list_actually_received":0,"orderNum":0},{"recharge_method":4,"name":"存零","sale_list_actually_received":0,"orderNum":0},{"recharge_method":5,"name":"退款","sale_list_actually_received":0,"orderNum":0},{"recharge_method":6,"name":"免密","sale_list_actually_received":0,"orderNum":0},{"recharge_method":7,"name":"公众号","sale_list_actually_received":0,"orderNum":0}]
     * cus_data : null
     */

    private int status;
    private String msg;
    private DataBean data;
    private Object cus_data;
    private List<Data1Bean> data1;//充值统计
    private List<AddressBean> address;//收银统计

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public Object getCus_data() {
        return cus_data;
    }

    public void setCus_data(Object cus_data) {
        this.cus_data = cus_data;
    }

    public List<Data1Bean> getData1() {
        return data1;
    }

    public void setData1(List<Data1Bean> data1) {
        this.data1 = data1;
    }

    public List<AddressBean> getAddress() {
        return address;
    }

    public void setAddress(List<AddressBean> address) {
        this.address = address;
    }

    public static class DataBean {
        /**
         * staff_name : 益农社服务员
         * login_id : 42860
         * shop_unique : 1.536215939565E12
         * member_card : 0
         * cusRecharge : 0
         * sum : 0
         * receivable : 0.0
         * total : 0.0
         * login_datetime : 2024-08-22 14:17:54
         * shopUnique : 1536215939565
         * staff_id : 3586
         * sign_out_datetime : 2024-08-22 14:48:36
         * staffName : 益农社服务员
         * retListTotal : 0.0
         * startTime : 1724307474000
         * endTime : 2024-08-22 14:48:36
         * staffId : 3586
         */

        private String staff_name;//员工名称
        private int login_id;//登录id
        private double shop_unique;
        private int member_card;
        private String cusRecharge;
        private int sum;//订单数量
        private double receivable;//实收金额
        private double total;//应收总金额
        private String login_datetime;//接班时间
        private long shopUnique;
        private int staff_id;//员工编号
        private String sign_out_datetime;//交班时间
        private String staffName;//员工名称
        private double retListTotal;//退款金额
//        private long startTime;
//        private String endTime;
        private int staffId;//员工编号

        public String getStaff_name() {
            return staff_name;
        }

        public void setStaff_name(String staff_name) {
            this.staff_name = staff_name;
        }

        public int getLogin_id() {
            return login_id;
        }

        public void setLogin_id(int login_id) {
            this.login_id = login_id;
        }

        public double getShop_unique() {
            return shop_unique;
        }

        public void setShop_unique(double shop_unique) {
            this.shop_unique = shop_unique;
        }

        public int getMember_card() {
            return member_card;
        }

        public void setMember_card(int member_card) {
            this.member_card = member_card;
        }

        public String getCusRecharge() {
            return cusRecharge;
        }

        public void setCusRecharge(String cusRecharge) {
            this.cusRecharge = cusRecharge;
        }

        public int getSum() {
            return sum;
        }

        public void setSum(int sum) {
            this.sum = sum;
        }

        public double getReceivable() {
            return receivable;
        }

        public void setReceivable(double receivable) {
            this.receivable = receivable;
        }

        public double getTotal() {
            return total;
        }

        public void setTotal(double total) {
            this.total = total;
        }

        public String getLogin_datetime() {
            return login_datetime;
        }

        public void setLogin_datetime(String login_datetime) {
            this.login_datetime = login_datetime;
        }

        public long getShopUnique() {
            return shopUnique;
        }

        public void setShopUnique(long shopUnique) {
            this.shopUnique = shopUnique;
        }

        public int getStaff_id() {
            return staff_id;
        }

        public void setStaff_id(int staff_id) {
            this.staff_id = staff_id;
        }

        public String getSign_out_datetime() {
            return sign_out_datetime;
        }

        public void setSign_out_datetime(String sign_out_datetime) {
            this.sign_out_datetime = sign_out_datetime;
        }

        public String getStaffName() {
            return staffName;
        }

        public void setStaffName(String staffName) {
            this.staffName = staffName;
        }

        public double getRetListTotal() {
            return retListTotal;
        }

        public void setRetListTotal(double retListTotal) {
            this.retListTotal = retListTotal;
        }

//        public long getStartTime() {
//            return startTime;
//        }
//
//        public void setStartTime(long startTime) {
//            this.startTime = startTime;
//        }
//
//        public String getEndTime() {
//            return endTime;
//        }
//
//        public void setEndTime(String endTime) {
//            this.endTime = endTime;
//        }

        public int getStaffId() {
            return staffId;
        }

        public void setStaffId(int staffId) {
            this.staffId = staffId;
        }
    }

    public static class Data1Bean {
        /**
         * payment_total : 0
         * pay_ment : 平台送豆
         */

        private double payment_total;
        private String pay_ment;

        public double getPayment_total() {
            return payment_total;
        }

        public void setPayment_total(double payment_total) {
            this.payment_total = payment_total;
        }

        public String getPay_ment() {
            return pay_ment;
        }

        public void setPay_ment(String pay_ment) {
            this.pay_ment = pay_ment;
        }
    }

    public static class AddressBean {
        /**
         * recharge_method : 1
         * name : 现金
         * sale_list_actually_received : 0.0
         * orderNum : 0
         */

        private int recharge_method;//1.现金 2.微信 3.支付宝 4.存零 5.退款 6.免密 7.公众号
        private String name;//名称
        private double sale_list_actually_received;//金额
        private int orderNum;//订单数量

        public int getRecharge_method() {
            return recharge_method;
        }

        public void setRecharge_method(int recharge_method) {
            this.recharge_method = recharge_method;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public double getSale_list_actually_received() {
            return sale_list_actually_received;
        }

        public void setSale_list_actually_received(double sale_list_actually_received) {
            this.sale_list_actually_received = sale_list_actually_received;
        }

        public int getOrderNum() {
            return orderNum;
        }

        public void setOrderNum(int orderNum) {
            this.orderNum = orderNum;
        }
    }
}
