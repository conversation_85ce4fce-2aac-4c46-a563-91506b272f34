package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.QueryOrderInfoData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:查询-订单详情-商品列表（适配器）
 * Created by jingang on 2024/8/26
 */
public class QueryOrderGoodsAdapter extends BaseAdapter<QueryOrderInfoData.DataBean.ListDetailBean> {

    public QueryOrderGoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_query_order_order;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        RelativeLayout rel = holder.getView(R.id.relItem);
        ImageView ivScale = holder.getView(R.id.ivItemScale);
        TextView tvName, tvRefund, tvCount, tvPrice;
        tvName = holder.getView(R.id.tvItemName);
        tvRefund = holder.getView(R.id.tvItemRefund);
        tvCount = holder.getView(R.id.tvItemCount);
        tvPrice = holder.getView(R.id.tvItemPrice);

        if (position % 2 == 0) {
            rel.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            rel.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
        }
        ivScale.setVisibility(mDataList.get(position).getGoods_cheng_type() == 1 ? View.VISIBLE : View.GONE);
        tvName.setText(mDataList.get(position).getGoods_name());
        tvRefund.setVisibility(mDataList.get(position).getRetCount() > 0 ? View.VISIBLE : View.GONE);
        tvCount.setText(DFUtils.getNum4(mDataList.get(position).getSale_list_detail_count()));
        tvPrice.setText(DFUtils.getNum2(mDataList.get(position).getSale_list_detail_price()));
    }
}
