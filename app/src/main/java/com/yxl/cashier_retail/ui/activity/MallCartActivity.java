package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.view.View;


import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityMallCartBinding;
import com.yxl.cashier_retail.ui.adapter.MallCartAdapter;
import com.yxl.cashier_retail.ui.bean.MallCartData;
import com.yxl.cashier_retail.ui.bean.MallOrderSettlementData;
import com.yxl.cashier_retail.ui.dialog.IAlertDialog;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:商城-购物车
 * Created by jingang on 2024/6/4
 */
@SuppressLint("NonConstantResourceId")
public class MallCartActivity extends BaseActivity<ActivityMallCartBinding> implements View.OnClickListener {

    private double count,
            total,
            realTotal;
    private String ids;

    private MallCartAdapter mAdapter;
    private List<MallCartData> dataList = new ArrayList<>();

    @Override
    protected ActivityMallCartBinding getViewBinding() {
        return ActivityMallCartBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivBack.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.tvBuy.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected void initData() {
        getCartList();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.tvBuy:
                //立即购买
                goToActivity(MallOrderSettlementActivity.class);
//                if (TextUtils.isEmpty(ids)) {
//                    showToast(1, "没有选择商品");
//                    return;
//                }
//                postOrder();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new MallCartAdapter(this);
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new MallCartAdapter.MyListener() {
            @Override
            public void onSelectClick(View view, int position) {
                //选择供货商
                boolean isCheck = dataList.get(position).isSelect();
                dataList.get(position).setSelect(!isCheck);
                for (int i = 0; i < dataList.get(position).getGood_list().size(); i++) {
                    dataList.get(position).getGood_list().get(i).setSelect(!isCheck);
                }
                mAdapter.notifyItemChanged(position, dataList.get(position));
                getTotal();
            }

            @Override
            public void onShippedClick(View view, int position) {
                //去凑单
            }

            @Override
            public void onChildSelectClick(View view, int position, int positionChild) {
                //选择商品
                dataList.get(position).getGood_list().get(positionChild).setSelect(!dataList.get(position).getGood_list().get(positionChild).isSelect());
                boolean isCheck = false;
                for (int i = 0; i < dataList.get(position).getGood_list().size(); i++) {
                    if (!dataList.get(position).getGood_list().get(i).isSelect()) {
                        isCheck = false;
                        break;
                    } else {
                        isCheck = true;
                    }
                }
                dataList.get(position).setSelect(isCheck);
                mAdapter.notifyItemChanged(position, dataList.get(position));
                getTotal();
            }

            @Override
            public void onChildSubClick(View view, int position, int positionChild) {
                //减
                int count = dataList.get(position).getGood_list().get(positionChild).getGood_count();
                if (count > dataList.get(position).getGood_list().get(positionChild).getStart_order()) {
                    //减少（需请求接口）
                    count--;
                    postCart(dataList.get(position).getGood_list().get(positionChild).getId(), count, position, positionChild);
                } else {
//                    showMessage("该宝贝不能再减了！");
                    IAlertDialog.showDialog(mContext,
                            "确认删除？",
                            "确认",
                            (dialog, which) -> {
                                postCarDel(String.valueOf(dataList.get(position).getGood_list().get(positionChild).getId()), position);
                            });
                }
            }

            @Override
            public void onChildAddClick(View view, int position, int positionChild) {
                //加
                MallCartData.GoodListBean data = dataList.get(position).getGood_list().get(positionChild);
                int count = data.getGood_count(),
                        kucun;
                if (data.getAuto_fxiaoshou() != 1) {
                    if (TextUtils.isEmpty(data.getAvailable_stock_count())) {
                        kucun = 0;
                    } else {
                        kucun = Integer.parseInt(data.getAvailable_stock_count());
                    }
                    if (count < kucun) {
                        //添加（需请求接口）
                        count++;
                        postCart(data.getId(), count, position, positionChild);
                    } else {
                        showToast(1, "购物车数量不能高于库存数量");
                    }
                } else {
                    //添加（需请求接口）
                    count++;
                    postCart(data.getId(), count, position, positionChild);
                }
            }
        });
        mBinding.smartRefreshLayout.setOnRefreshListener(refreshLayout -> getCartList());
        mBinding.smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 合计
     */
    @SuppressLint("SetTextI18n")
    private void getTotal() {
        //合计
        total = 0;
        realTotal = 0;
        count = 0;
        ids = "";
        double loanPrice;
        for (int i = 0; i < dataList.size(); i++) {
            List<MallCartData.GoodListBean> childMapList = dataList.get(i).getGood_list();
            for (int j = 0; j < childMapList.size(); j++) {
                MallCartData.GoodListBean goodsBean = childMapList.get(j);
                int loanCount = goodsBean.getLoan_count();
                int count = goodsBean.getGood_count() - loanCount;
                double price = goodsBean.getOnline_price();
                loanPrice = goodsBean.getLoan_price();
                if (goodsBean.getPromotion_price() != -1) {
                    if (count <= goodsBean.getPromotion_count()) {
                        price = goodsBean.getPromotion_price();
                        loanPrice = (goodsBean.getPromotion_price() + goodsBean.getLoan_cut());
                    }
                }
                double discountPrice = price * count;
                double creditMoney = loanPrice * loanCount;
                if (goodsBean.isSelect()) {
                    this.count++;   //单品多数量只记1
                    realTotal += discountPrice;
                    realTotal += creditMoney;
                    if (TextUtils.isEmpty(ids)) {
                        ids = String.valueOf(goodsBean.getId());
                    } else {
                        ids = ids + "," + goodsBean.getId();
                    }
                }
            }
        }
        mBinding.tvCount.setText("货品共：" + dataList.size() + "类，" + count + "件");
        mBinding.tvTotalReal.setText(getRstr(R.string.money) + DFUtils.getNum2(realTotal));
    }

    /**
     * 购物车列表
     */
    private void getCartList() {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("area_dict_num", getAreaDictNum());
        showDialog();
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getMallCartList(),
                params,
                MallCartData.class,
                new RequestListListener<MallCartData>() {
                    @Override
                    public void onResult(List<MallCartData> list) {
                        hideDialog();
                        mBinding.smartRefreshLayout.finishRefresh();
                        dataList.clear();
                        dataList.addAll(list);
                        for (int i = 0; i < dataList.size(); i++) {
                            dataList.get(i).setSelect(true);
                            for (int j = 0; j < dataList.get(i).getGood_list().size(); j++) {
                                dataList.get(i).getGood_list().get(j).setSelect(true);
                            }
                        }
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                        getTotal();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        mBinding.smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 购物车数量加减
     *
     * @param id
     * @param good_count
     */
    private void postCart(int id, int good_count, int position, int position1) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", id);
        map.put("good_count", good_count);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getMallCartUpdate(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        dataList.get(position).getGood_list().get(position1).setGood_count(good_count);
                        mAdapter.notifyItemChanged(position, dataList.get(position));
                        getTotal();
                    }
                });
    }

    /**
     * 购物车-批量删除
     *
     * @param id
     */
    private void postCarDel(String id, int position) {
        Map<String, Object> map = new HashMap<>();
        map.put("ids", id);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getMallCartDel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        showToast(0, "删除成功");
                        if (dataList.get(position).isSelect()) {
                            dataList.remove(position);
                            mAdapter.remove(position);
                        } else {
                            for (int i = 0; i < dataList.get(position).getGood_list().size(); i++) {
                                if (dataList.get(position).getGood_list().get(i).isSelect()) {
                                    dataList.get(position).getGood_list().remove(i);
                                }
                            }
                            mAdapter.notifyItemChanged(position, dataList.get(position));
                        }
                        getTotal();
                    }
                });
    }

    /**
     * 购物车提交订单
     */
    private void postOrder() {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("area_dict_num", getAreaDictNum());
        params.put("ids", ids);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getMallCartSettlement(),
                params,
                MallOrderSettlementData.class,
                new RequestListener<MallOrderSettlementData>() {
                    @Override
                    public void success(MallOrderSettlementData data) {
                        hideDialog();
                        showToast(0, "提交成功");
                        dataList.clear();
                        mAdapter.clear();
                        mBinding.recyclerView.setVisibility(View.GONE);
                        mBinding.linEmpty.setVisibility(View.VISIBLE);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

}
