package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;

/**
 * Describe:商城-商品列表（实体类）
 * Created by jingang on 2024/6/3
 */
public class MallGoodsData implements Serializable {
    /**
     * goods_name : 科迪纯牛奶
     * gold_deduct : 0.0
     * stock_count : 1
     * goods_id : 1000000644
     * start_order : 10
     * is_binding : 0
     * goodsunit_name : 箱
     * goods_barcode : 6904422906305
     * online_price : 52.0
     * company_name : 盛大商贸
     * wholesale_price : 52.0
     * is_activity : 0
     * company_code : GS371308495
     * activity_price :
     * auto_fxiaoshou : 1
     * goods_img : http://file.buyhoo.cc//image/suppliers/GS371306833/4a51b898-f21c-441f-952c-6d405af6d7b3.gif
     */

    private String goods_name;
    private double gold_deduct;//金圈币抵扣金额
    private int stock_count;//库存
    private int goods_id;
    private int start_order;//起订量
    private int is_binding;//0.未绑定 1.已绑定
    private String goodsunit_name;//单位
    private String goods_barcode;//条码
    private double online_price;//网购单价
    private String company_name;//供货商名称
    private double wholesale_price;//批发售价
    private int is_activity;
    private String company_code;//供货商编号
    private String activity_price;
    private int auto_fxiaoshou;//允许负库存销售 1.允许 2.不允许
    private String goods_img;//商品图片

    public String getGoods_name() {
        return goods_name;
    }

    public void setGoods_name(String goods_name) {
        this.goods_name = goods_name;
    }

    public double getGold_deduct() {
        return gold_deduct;
    }

    public void setGold_deduct(double gold_deduct) {
        this.gold_deduct = gold_deduct;
    }

    public int getStock_count() {
        return stock_count;
    }

    public void setStock_count(int stock_count) {
        this.stock_count = stock_count;
    }

    public int getGoods_id() {
        return goods_id;
    }

    public void setGoods_id(int goods_id) {
        this.goods_id = goods_id;
    }

    public int getStart_order() {
        return start_order;
    }

    public void setStart_order(int start_order) {
        this.start_order = start_order;
    }

    public int getIs_binding() {
        return is_binding;
    }

    public void setIs_binding(int is_binding) {
        this.is_binding = is_binding;
    }

    public String getGoodsunit_name() {
        return goodsunit_name;
    }

    public void setGoodsunit_name(String goodsunit_name) {
        this.goodsunit_name = goodsunit_name;
    }

    public String getGoods_barcode() {
        return goods_barcode;
    }

    public void setGoods_barcode(String goods_barcode) {
        this.goods_barcode = goods_barcode;
    }

    public double getOnline_price() {
        return online_price;
    }

    public void setOnline_price(double online_price) {
        this.online_price = online_price;
    }

    public String getCompany_name() {
        return company_name;
    }

    public void setCompany_name(String company_name) {
        this.company_name = company_name;
    }

    public double getWholesale_price() {
        return wholesale_price;
    }

    public void setWholesale_price(double wholesale_price) {
        this.wholesale_price = wholesale_price;
    }

    public int getIs_activity() {
        return is_activity;
    }

    public void setIs_activity(int is_activity) {
        this.is_activity = is_activity;
    }

    public String getCompany_code() {
        return company_code;
    }

    public void setCompany_code(String company_code) {
        this.company_code = company_code;
    }

    public String getActivity_price() {
        return activity_price;
    }

    public void setActivity_price(String activity_price) {
        this.activity_price = activity_price;
    }

    public int getAuto_fxiaoshou() {
        return auto_fxiaoshou;
    }

    public void setAuto_fxiaoshou(int auto_fxiaoshou) {
        this.auto_fxiaoshou = auto_fxiaoshou;
    }

    public String getGoods_img() {
        return goods_img;
    }

    public void setGoods_img(String goods_img) {
        this.goods_img = goods_img;
    }
}
