package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogRiderBinding;
import com.yxl.cashier_retail.ui.adapter.RiderAdapter;
import com.yxl.cashier_retail.ui.bean.RiderData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:dialog（骑手列表）
 * Created by jingang on 2024/6/18
 */
@SuppressLint("NonConstantResourceId")
public class RiderDialog extends BaseDialog<DialogRiderBinding> {

    private static int id;
    private List<RiderData> dataList = new ArrayList<>();
    private RiderAdapter mAdapter;

    public static void showDialog(Activity activity, int id, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        RiderDialog.listener = listener;
        RiderDialog.id = id;
        RiderDialog dialog = new RiderDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 7);
        dialog.show();
    }

    public RiderDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(v -> dismiss());
        setAdapter();
        getRiderList();
    }

    @Override
    protected DialogRiderBinding getViewBinding() {
        return DialogRiderBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new RiderAdapter(getContext());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (listener != null) {
                listener.onConfirm(dataList.get(position));
                dismiss();
            }
        });
        mBinding.smartRefreshLayout.setOnRefreshListener(refreshLayout -> getRiderList());
        mBinding.smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 店铺查询骑手列表
     */
    public void getRiderList() {
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("page", page);
        params.put("limit", 10000);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getRiderList(),
                params,
                RiderData.class,
                new RequestListListener<RiderData>() {
                    @Override
                    public void onResult(List<RiderData> list) {
                        hideDialog();
                        mBinding.smartRefreshLayout.finishRefresh();
                        for (int i = 0; i < list.size(); i++) {
                            if (list.get(i).getId() == id) {
                                list.get(i).setSelect(true);
                            }
                        }
                        dataList.clear();
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(RiderData data);
    }
}
