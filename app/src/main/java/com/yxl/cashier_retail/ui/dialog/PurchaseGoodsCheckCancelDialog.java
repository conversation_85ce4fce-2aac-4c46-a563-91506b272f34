package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogPurchaseGoodsCheckCancelBinding;
import com.yxl.cashier_retail.ui.bean.PurchaseInfoData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.StringUtils;

/**
 * Describe:dialog（购销单详情-商品撤销核对）
 * Created by jingang on 2024/6/14
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class PurchaseGoodsCheckCancelDialog extends BaseDialog<DialogPurchaseGoodsCheckCancelBinding> implements View.OnClickListener {
    private static PurchaseInfoData.GoodsListBean data;

    public static void showDialog(Activity activity, PurchaseInfoData.GoodsListBean data, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        PurchaseGoodsCheckCancelDialog.listener = listener;
        PurchaseGoodsCheckCancelDialog.data = data;
        PurchaseGoodsCheckCancelDialog dialog = new PurchaseGoodsCheckCancelDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 10 * 7, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public PurchaseGoodsCheckCancelDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.tvDialogCancel.setOnClickListener(this);
        setUI();
    }

    @Override
    protected DialogPurchaseGoodsCheckCancelBinding getViewBinding() {
        return DialogPurchaseGoodsCheckCancelBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogCancel:
                //撤销核对
                if (listener != null) {
                    listener.onConfirm();
                    dismiss();
                }
                break;
        }
    }

    /**
     * 更新UI
     */
    private void setUI() {
        if (data == null) {
            return;
        }
        Glide.with(getContext())
                .load(StringUtils.handledImgUrl(data.getGoodsPicturepath()))
                .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img))
                .into(mBinding.ivDialogImg);
        mBinding.tvDialogName.setText(data.getGoodsName());
        //采购单位
        if (TextUtils.isEmpty(data.getRestockUnit())) {
            mBinding.tvDialogCountPurchase.setText(getRstr(R.string.ordered_count_colon) + DFUtils.getNum4(data.getPurchaseGoodsCount()));
        } else {
            mBinding.tvDialogCountPurchase.setText(getRstr(R.string.ordered_count_colon) + DFUtils.getNum4(data.getPurchaseGoodsCount()) + data.getRestockUnit());
        }
        //配送单位
        if (TextUtils.isEmpty(data.getBillUnit())) {
            mBinding.tvDialogCountDelivery.setText(DFUtils.getNum4(data.getTradeGoodsCount()));
        } else {
            mBinding.tvDialogCountDelivery.setText(DFUtils.getNum4(data.getTradeGoodsCount()) + data.getBillUnit());
        }
        mBinding.tvDialogInPrice.setText(DFUtils.getNum2(data.getPurchasePrice()));
        mBinding.tvDialogTotal.setText(DFUtils.getNum2(data.getTotalPrice()));
        mBinding.tvDialogSuggest.setText(DFUtils.getNum2(data.getSalePrice()));
        mBinding.tvDialogCount.setText(DFUtils.getNum4(data.getPurchaseGoodsCount()));
        //是否缺货 0.正常 1.缺货 2.超出
        switch (data.getNumberStatus()) {
            case 1:
                mBinding.linDialogStatus.setVisibility(View.VISIBLE);
                mBinding.tvDialogStatus.setText(getRstr(R.string.goods_short_stock));
                break;
            case 2:
                mBinding.linDialogStatus.setVisibility(View.VISIBLE);
                mBinding.tvDialogStatus.setText(getRstr(R.string.goods_many_stock));
                break;
            default:
                mBinding.linDialogStatus.setVisibility(View.GONE);
                break;
        }
        mBinding.tvDialogSalePrice.setText(getRstr(R.string.price_sale_colon) + DFUtils.getNum2(data.getRetailPriceNow()));
        mBinding.tvDialogOnlinePrice.setText(getRstr(R.string.price_online_colon) + DFUtils.getNum2(data.getNetPriceNow()));
        mBinding.tvDialogMemberPrice.setText(getRstr(R.string.price_member_colon) + DFUtils.getNum2(data.getMemberPriceNow()));
        mBinding.tvDialogSalePriceInput.setText(DFUtils.getNum2(data.getRetailPrice()));
        mBinding.tvDialogOnlinePriceInput.setText(DFUtils.getNum2(data.getNetPrice()));
        mBinding.tvDialogMemberPriceInput.setText(DFUtils.getNum2(data.getMemberPrice()));
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm();
    }
}
