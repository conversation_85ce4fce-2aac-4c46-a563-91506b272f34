package com.yxl.cashier_retail.ui.bean;

/**
 * Describe:会员等级列表（实体类）
 * Created by jingang on 2024/8/6
 */
public class MemberLevelData {
    /**
     * cusLevelPoints : 599
     * cusLevelName : 铜牌会员
     * cusLevelVal : 1
     * cusLevelDiscount : 0.95
     * cusLevelId : 1549
     * cusLevelStatus : 1
     */
    private boolean select;
    private int cusLevelPoints;//等级积分
    private String cusLevelName;//等级名称
    private int cusLevelVal;//会员等级分级
    private double cusLevelDiscount;//等级折扣
    private int cusLevelId;
    private int cusLevelStatus;//会员等级是否升级0: 不升级 1:升级

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }

    public int getCusLevelPoints() {
        return cusLevelPoints;
    }

    public void setCusLevelPoints(int cusLevelPoints) {
        this.cusLevelPoints = cusLevelPoints;
    }

    public String getCusLevelName() {
        return cusLevelName;
    }

    public void setCusLevelName(String cusLevelName) {
        this.cusLevelName = cusLevelName;
    }

    public int getCusLevelVal() {
        return cusLevelVal;
    }

    public void setCusLevelVal(int cusLevelVal) {
        this.cusLevelVal = cusLevelVal;
    }

    public double getCusLevelDiscount() {
        return cusLevelDiscount;
    }

    public void setCusLevelDiscount(double cusLevelDiscount) {
        this.cusLevelDiscount = cusLevelDiscount;
    }

    public int getCusLevelId() {
        return cusLevelId;
    }

    public void setCusLevelId(int cusLevelId) {
        this.cusLevelId = cusLevelId;
    }

    public int getCusLevelStatus() {
        return cusLevelStatus;
    }

    public void setCusLevelStatus(int cusLevelStatus) {
        this.cusLevelStatus = cusLevelStatus;
    }
}
