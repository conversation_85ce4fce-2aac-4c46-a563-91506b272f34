package com.yxl.cashier_retail.ui.bean;

/**
 * Describe:统计-销售额走势（实体类）
 * Created by jingang on 2024/8/30
 */
public class StatisticsSalesData {
    /**
     * saleTotal : 289.08
     * datelist : 1724342400000
     * listCount : 26
     */

    private double saleTotal;//销售额
    private long datelist;//日期
    private int listCount;//订单量

    public double getSaleTotal() {
        return saleTotal;
    }

    public void setSaleTotal(double saleTotal) {
        this.saleTotal = saleTotal;
    }

    public long getDatelist() {
        return datelist;
    }

    public void setDatelist(long datelist) {
        this.datelist = datelist;
    }

    public int getListCount() {
        return listCount;
    }

    public void setListCount(int listCount) {
        this.listCount = listCount;
    }
}
