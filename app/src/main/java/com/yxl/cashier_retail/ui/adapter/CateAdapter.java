package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.CateData;

/**
 * Describe:商品一级分类（适配器）
 * Created by jingang on 2024/6/5
 */
public class CateAdapter extends BaseAdapter<CateData> {

    public CateAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_cate;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvName = holder.getView(R.id.tvItemName);
        RecyclerView recyclerView = holder.getView(R.id.rvItem);

        tvName.setText(mDataList.get(position).getGroupName());
        ivImg.setSelected(mDataList.get(position).isCheck());
        if (mDataList.get(position).isShow()) {
            if (mDataList.get(position).getKindDetail() != null) {
                recyclerView.setVisibility(View.VISIBLE);
                CateChildAdapter adapter = new CateChildAdapter(mContext);
                recyclerView.setAdapter(adapter);
                adapter.setDataList(mDataList.get(position).getKindDetail());
                adapter.setOnItemClickListener((view, position1) -> {
                    if (listener != null) {
                        listener.onItemItemClick(view, position, position1);
                    }
                });
            } else {
                recyclerView.setVisibility(View.GONE);
            }
        } else {
            recyclerView.setVisibility(View.GONE);
        }
        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onItemItemClick(View view, int position, int positionChild);
    }
}
