package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmBeansBinding;
import com.yxl.cashier_retail.ui.adapter.BeansAdapter;
import com.yxl.cashier_retail.ui.bean.BeansListData;
import com.yxl.cashier_retail.ui.dialog.DateStartEndDialog;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:网单-百货豆
 * Created by jingang on 2024/6/20
 */
@SuppressLint("NonConstantResourceId")
public class BeansFragment extends BaseFragment<FmBeansBinding> implements View.OnClickListener {
    private int type = 1;//1.交易记录 2.提现记录
    private String startDate, endDate, rule;
    private BeansAdapter mAdapter;
    private List<BeansListData.DataBean> dataList = new ArrayList<>();

    @Override
    protected FmBeansBinding getViewBinding() {
        return FmBeansBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.tvRule.setOnClickListener(this);
        mBinding.linTab0.setOnClickListener(this);
        mBinding.linTab1.setOnClickListener(this);
        mBinding.linStartDate.setOnClickListener(this);
        mBinding.linEndDate.setOnClickListener(this);
        mBinding.tvReset.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected void initData() {
        mBinding.smartRefreshLayout.autoRefresh();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvRule:
                //规则设置
                showToast(1, "暂未开发");
                break;
            case R.id.linTab0:
                //交易记录
                if (type != 1) {
                    type = 1;
                    mBinding.tvTab0.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    mBinding.tvTab0.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.vTab0.setVisibility(View.VISIBLE);
                    mBinding.tvTab1.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvTab1.setTypeface(Typeface.DEFAULT);
                    mBinding.vTab1.setVisibility(View.INVISIBLE);
                    page = 1;
                    getBeansList();
                }
                break;
            case R.id.linTab1:
                //提现记录
                if (type != 2) {
                    type = 2;
                    mBinding.tvTab1.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    mBinding.tvTab1.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.vTab1.setVisibility(View.VISIBLE);
                    mBinding.tvTab0.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvTab0.setTypeface(Typeface.DEFAULT);
                    mBinding.vTab0.setVisibility(View.INVISIBLE);
                    page = 1;
                    getBeansList();
                }
                break;
            case R.id.linStartDate:
                //开始日期
                showDialogDate(0, mBinding.ivStartDate);
                break;
            case R.id.linEndDate:
                //结束日期
                showDialogDate(1, mBinding.ivEndDate);
                break;
            case R.id.tvReset:
                //重置
                startDate = "";
                endDate = "";
                mBinding.tvStartDate.setText("");
                mBinding.tvEndDate.setText("");
                mBinding.smartRefreshLayout.autoRefresh();
                break;
        }
    }

    /**
     * dialog（选择日期时间）
     */
    private void showDialogDate(int type, View view) {
        DateStartEndDialog.showDialog(getActivity(),
                view,
                startDate,
                endDate,
                type,
                (startDate, endDate) -> {
                    this.startDate = startDate;
                    this.endDate = endDate;
                    mBinding.tvStartDate.setText(startDate);
                    mBinding.tvEndDate.setText(endDate);
                    mBinding.smartRefreshLayout.autoRefresh();
                });
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new BeansAdapter(getActivity());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {

        });
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getBeansList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getBeansList();
            }
        });
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(BeansListData data) {
        rule = data.getUseHelpUrl();
        mBinding.tvCount.setText(String.valueOf(data.getCount()));
        if (data.getResData() != null) {
            mBinding.tvListCount.setText(DFUtils.getNum4(data.getResData().getListCount()));
            mBinding.tvListTotal.setText(DFUtils.getNum4(data.getResData().getListTotal()));
            mBinding.tvPlatformCount.setText(DFUtils.getNum4(data.getResData().getPlatformBeans()));
            mBinding.tvUseCount.setText(DFUtils.getNum4(data.getResData().getBeansUse()));
        }
        if (page == 1) {
            dataList.clear();
        }
        if (data.getData() != null) {
            dataList.addAll(data.getData());
        }
        if (dataList.size() > 0) {
            mBinding.recyclerView.setVisibility(View.VISIBLE);
            mBinding.linEmpty.setVisibility(View.GONE);
            mAdapter.setDataList(dataList);
        } else {
            mBinding.recyclerView.setVisibility(View.GONE);
            mBinding.linEmpty.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 百货豆首页
     */
    private void getBeansList() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("beanType", type);//1.入账 2.出账
        if (!TextUtils.isEmpty(startDate)) {
            params.put("startTime", startDate + " 00:00");
        }
        if (!TextUtils.isEmpty(endDate)) {
            params.put("endTime", endDate + " 23:59");
        }
        params.put("page", page);
        params.put("pageSize", Constants.limit);
        RXHttpUtil.requestByFormPostAsOriginalResponse(getActivity(),
                ZURL.getBeansList(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                        BeansListData data = new Gson().fromJson(s, BeansListData.class);
                        if (data == null) {
                            return;
                        }
                        if (data.getStatus() != Constants.SUCCESS_CODE) {
                            showToast(1, data.getMsg());
                            return;
                        }
                        setUI(data);
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                    }
                });
    }
}
