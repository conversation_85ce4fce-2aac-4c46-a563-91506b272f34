package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.MallGoodsInfoData;
import com.yxl.commonlibrary.utils.StringUtils;

/**
 * Describe:商城-商品详情-捆绑商品（适配器）
 * Created by jingang on 2024/6/4
 */
public class MallGoodsInfoBindAdapter extends BaseAdapter<MallGoodsInfoData.DataBean.Bind> {

    public MallGoodsInfoBindAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_mall_goods_info_gift;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvCount = holder.getView(R.id.tvItemCount);

        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoods_img()))
                .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img))
                .into(ivImg);
        tvCount.setText("x" + mDataList.get(position).getStart_order());
    }
}
