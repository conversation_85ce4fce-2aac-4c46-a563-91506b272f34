package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.MyApplication;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityGoodsBinding;
import com.yxl.cashier_retail.ui.adapter.CatePcAdapter;
import com.yxl.cashier_retail.ui.adapter.GoodsManageAdapter;
import com.yxl.cashier_retail.ui.bean.CatePcData;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.ui.bean.GoodsInfoData;
import com.yxl.cashier_retail.ui.bean.GoodsListData;
import com.yxl.cashier_retail.ui.bean.SupplierPcData;
import com.yxl.cashier_retail.ui.dialog.CatePcDialog;
import com.yxl.cashier_retail.ui.dialog.GoodsAddQuickDialog;
import com.yxl.cashier_retail.ui.dialog.IAlertDialog;
import com.yxl.cashier_retail.ui.dialog.InDialog;
import com.yxl.cashier_retail.ui.dialog.MenuDialog;
import com.yxl.cashier_retail.ui.dialog.OutDialog;
import com.yxl.cashier_retail.ui.dialog.SupplierEditDialog;
import com.yxl.cashier_retail.ui.dialog.UnitDialog;
import com.yxl.cashier_retail.ui.popupwindow.SupplierPop;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.utils.DecimalDigitsInputFilter;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;
import org.json.JSONArray;
import org.litepal.LitePal;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:商品管理
 * Created by jingang on 2024/5/24
 */
@SuppressLint({"NonConstantResourceId", "NotifyDataSetChanged"})
public class GoodsActivity extends BaseActivity<ActivityGoodsBinding> implements View.OnClickListener {
    private String keyWords,
            goodsKindUnique,
            supplierUnique, unit,
            barcode,
            name,
            parentUnique;//虚拟分类编号
    private int parentId,//虚拟分类id
            posCate,//虚拟分类列表下标
            pos,//商品列表下标
            goodsId,
            chengType,//计价类型 0.计件 1.称重
            goodsLife;//保质期
    private double stock,//库存
            salePrice,//零售单价
            onlinePrice,//网购单价
            memberPrice;//会员单价

    //商品分类
    private CatePcAdapter cateAdapter;
    private List<CatePcData> cateList = new ArrayList<>();

    //商品列表
    private GoodsManageAdapter mAdapter;
    private List<GoodsData> dataList = new ArrayList<>();
    private GoodsListData data0;

    //供货商列表
    private List<SupplierPcData> supplierList = new ArrayList<>();

    @Override
    protected ActivityGoodsBinding getViewBinding() {
        return ActivityGoodsBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivLogo.setOnClickListener(this);
        mBinding.ivSearchClear.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);

        mBinding.tvPrint.setOnClickListener(this);
        mBinding.ivChengType0.setOnClickListener(this);
        mBinding.ivChengType1.setOnClickListener(this);
        mBinding.linSupplier.setOnClickListener(this);
        mBinding.linUnit.setOnClickListener(this);
        mBinding.tvIn.setOnClickListener(this);
        mBinding.tvOut.setOnClickListener(this);
        mBinding.tvDel.setOnClickListener(this);
        mBinding.tvMore.setOnClickListener(this);
        mBinding.tvConfirm.setOnClickListener(this);

        mBinding.tvCate.setOnClickListener(this);
        mBinding.tvAddToCate.setOnClickListener(this);
        mBinding.tvAdd.setOnClickListener(this);
        mBinding.tvAddQuick.setOnClickListener(this);
        mBinding.tvExit.setOnClickListener(this);
        mBinding.etSalePrice.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        mBinding.etOnlinePrice.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        mBinding.etMemberPrice.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        mBinding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                if (TextUtils.isEmpty(keyWords)) {
                    mBinding.ivSearchClear.setVisibility(View.GONE);
                } else {
                    mBinding.ivSearchClear.setVisibility(View.VISIBLE);
                }
            }
        });
        mBinding.etSearch.setOnEditorActionListener((v, actionId, event) -> {
            max = 0;
            getGoodsList();
            return true;
        });
        setAdapter();
    }

    @Override
    protected void initData() {
        setNetwork();
        getCateList();
    }

    @Override
    public void getNetwork() {
        setNetwork();
    }

    @Override
    public void onClick(View v) {
        if (isQuicklyClick()) {
            return;
        }
        switch (v.getId()) {
            case R.id.ivLogo:
                //菜单
                MenuDialog.showDialog(this, type -> {
                    //0.收银 1.入库 2.查询 3.统计 4.网单 5.会员 6.补货 7.营销 8.交班 9.设置
                    switch (type) {
                        case 1:
                            goToActivity(InActivity.class);
                            break;
                        case 2:
                            goToActivity(QueryActivity.class);
                            break;
                        case 3:
                            goToActivity(StatisticsActivity.class);
                            break;
                        case 4:
                            goToActivity(OrderActivity.class);
                            break;
                        case 5:
                            goToActivity(MemberActivity.class);
                            break;
                        case 6:
                            goToActivity(MallActivity.class);
                            break;
                        case 7:
                            goToActivity(MarketingActivity.class);
                            break;
                        case 8:
                            goToActivity(ShiftActivity.class);
                            break;
                        case 9:
                            goToActivity(SettingActivity.class);
                            break;
                    }
                    finish();
                });
                break;
            case R.id.ivSearchClear:
                //清空搜索输入
                keyWords = "";
                mBinding.etSearch.setText("");
                max = 0;
                getGoodsList();
                break;
            case R.id.tvCashier:
                //收银台
            case R.id.tvExit:
                //退出商品管理
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.tvPrint:
                //打印价签
                break;
            case R.id.ivChengType0:
                //计价类型：计件
                if (chengType != 0) {
                    chengType = 0;
                    mBinding.ivChengType0.setSelected(true);
                    mBinding.ivChengType1.setSelected(false);
                }
                break;
            case R.id.ivChengType1:
                //计价类型：称重
                if (chengType != 1) {
                    chengType = 1;
                    mBinding.ivChengType0.setSelected(false);
                    mBinding.ivChengType1.setSelected(true);
                }
                break;
            case R.id.linSupplier:
                //选择供货商
                if (supplierList.isEmpty()) {
                    getSupplierList(v);
                } else {
                    showPopSupplier(v);
                }
                break;
            case R.id.linUnit:
                //选择单位
                UnitDialog.showDialog(this, mBinding.tvUnit.getText().toString().trim(), mBinding.ivUnit, name -> {
                    unit = name;
                    mBinding.tvUnit.setText(name);
                });
                break;
            case R.id.tvIn:
                //入库
                if (data0 != null) {
                    InDialog.showDialog(this, goodsLife, data0, (barcode, count) -> {
                        stock = stock + count;
                        mBinding.tvStock.setText(DFUtils.getNum4(stock));
                        for (int i = 0; i < dataList.size(); i++) {
                            if (!TextUtils.isEmpty(dataList.get(i).getGoods_barcode()) && !TextUtils.isEmpty(barcode)) {
                                if (barcode.equals(dataList.get(i).getGoods_barcode())) {
                                    dataList.get(i).setGoods_count(dataList.get(i).getGoods_count() + count);
                                    mAdapter.notifyItemChanged(i);
                                    getGoodsInfo(barcode);
                                }
                            }
                        }
                    });
                }
                break;
            case R.id.tvOut:
                //出库
                if (data0 != null) {
                    OutDialog.showDialog(this, data0, (barcode, count) -> {
                        stock = stock + count;
                        mBinding.tvStock.setText(DFUtils.getNum4(stock));
                        for (int i = 0; i < dataList.size(); i++) {
                            if (!TextUtils.isEmpty(dataList.get(i).getGoods_barcode()) && !TextUtils.isEmpty(barcode)) {
                                if (barcode.equals(dataList.get(i).getGoods_barcode())) {
                                    dataList.get(i).setGoods_count(dataList.get(i).getGoods_count() + count);
                                    mAdapter.notifyItemChanged(i);
                                    getGoodsInfo(barcode);
                                }
                            }
                        }
                    });
                }
                break;
//            case R.id.tvClear:
//                //清空
//                mBinding.etName.setText("");
//                salePrice = 0;
//                mBinding.etSalePrice.setText("");
//                onlinePrice = 0;
//                mBinding.etOnlinePrice.setText("");
//                memberPrice = 0;
//                mBinding.etMemberPrice.setText("");
//                supplierUnique = "";
//                mBinding.tvSupplier.setText("");
//                unit = "";
//                mBinding.tvUnit.setText("");
//                break;
            case R.id.tvDel:
                //删除
                postGoodsDel();
                break;
            case R.id.tvMore:
                //更多商品信息
                startActivity(new Intent(this, GoodsEditActivity.class)
                        .putExtra("barcode", barcode)
                );
                break;
            case R.id.tvConfirm:
                //保存
                postGoodsEdit();
                break;
            case R.id.tvCate:
                //编辑首页虚拟分类
                CatePcDialog.showDialog(this, cateList);
                break;
            case R.id.tvAddToCate:
                //添加商品到此类
                goToActivity(CateAddGoodsActivity.class);
                break;
            case R.id.tvAdd:
                //添加商品
                goToActivity(GoodsEditActivity.class);
                break;
            case R.id.tvAddQuick:
                //快速新增商品
                GoodsAddQuickDialog.showDialog(this, getRstr(R.string.goods_add_quick), "");
                break;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.CATE_LIST:
                //分类列表
                page = 1;
                max = 0;
                getCateList();
                break;
            case Constants.GOODS_LIST:
                if (!TextUtils.isEmpty(barcode)) {
                    getGoodsInfo(barcode);
                }
                break;
        }
    }

    /**
     * 网络监听
     */
    private void setNetwork() {
        if (MyApplication.mNetwork != null) {
            if (MyApplication.mNetwork.isConnect()) {
                switch (MyApplication.mNetwork.getConnectType()) {
                    case 1:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network001);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 2:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network002);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 3:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network003);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    default:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                        mBinding.tvNetwork.setText(getRstr(R.string.no_network));
                        break;
                }
            } else {
                mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                mBinding.tvNetwork.setText(getRstr(R.string.no_network));
            }
        } else {
            mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
            mBinding.tvNetwork.setText(getRstr(R.string.no_network));
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //商品分类
        cateAdapter = new CatePcAdapter(this);
        mBinding.rvCate.setAdapter(cateAdapter);
        cateAdapter.setOnItemClickListener((view, position) -> {
            if (!cateList.get(position).isCheck()) {
                for (int i = 0; i < cateList.size(); i++) {
                    if (cateList.get(i).isCheck()) {
                        cateList.get(i).setCheck(false);
                    }
                }
                posCate = position;
                cateList.get(position).setCheck(true);
                cateAdapter.setDataList(cateList);
                setUI(null);
                parentId = cateList.get(position).getGoodsKindInventedId();
                parentUnique = cateList.get(position).getGoods_kind_unique();
                max = 0;
                getGoodsList();
            }
        });

        //商品列表
        mAdapter = new GoodsManageAdapter(this);
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new GoodsManageAdapter.MyListener() {
            @Override
            public void onItemClick(int position) {
                //详情
                if (!dataList.get(position).isSelect()) {
                    for (int i = 0; i < dataList.size(); i++) {
                        if (dataList.get(i).isSelect()) {
                            dataList.get(i).setSelect(false);
                            mAdapter.notifyItemChanged(i);
                        }
                    }
                    dataList.get(position).setSelect(true);
                    mAdapter.notifyItemChanged(position);
                    pos = position;
                    getGoodsInfo(dataList.get(position).getGoods_barcode());
                } else {
                    dataList.get(position).setSelect(false);
                    mAdapter.notifyItemChanged(position);
                    setUI(null);
                }
            }

            @Override
            public void onDelClick(int position) {
                //删除
                pos = position;
                IAlertDialog.showDialog(GoodsActivity.this,
                        getRstr(R.string.confirm_del_goods_in_cate),
                        getRstr(R.string.confirm),
                        (dialog, which) -> {
                            postCatePcGoodsEdit(dataList.get(position).getGoods_barcode());
                        });
            }
        });
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                getGoodsList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                max = 0;
                getGoodsList();
            }
        });
    }

    /**
     * 更新UI-商品详情
     *
     * @param data
     */
    private void setUI(GoodsInfoData data) {
        if (data == null) {
            mBinding.linNothing.setVisibility(View.VISIBLE);
            mBinding.linInfo.setVisibility(View.GONE);
            barcode = "";
            return;
        }
        mBinding.linNothing.setVisibility(View.GONE);
        mBinding.linInfo.setVisibility(View.VISIBLE);
        chengType = data.getGoodsChengType();
        stock = data.getGoodsCount();
        if (chengType == 0) {
            mBinding.ivChengType0.setSelected(true);
            mBinding.ivChengType1.setSelected(false);
        } else {
            mBinding.ivChengType0.setSelected(false);
            mBinding.ivChengType1.setSelected(true);
        }
        supplierUnique = data.getSupplierUnique();
        mBinding.tvSupplier.setText(data.getSupplierName());
        mBinding.tvStock.setText(DFUtils.getNum4(data.getGoodsCount()));
        goodsLife = data.getGoodsLife();
        goodsKindUnique = data.getKindUnique();
        //基础包装
        if (data.getListDetail().size() > 0) {
            data0 = data.getListDetail().get(0);
            goodsId = data0.getGoodsId();
            barcode = data0.getGoodsBarcode();
            name = data0.getGoodsName();
            salePrice = data0.getGoodsSalePrice();
            onlinePrice = data0.getGoodsWebSalePrice();
            memberPrice = data0.getGoodsCusPrice();
            unit = data0.getGoodsUnit();
            mBinding.tvBarcode.setText(barcode);
            mBinding.etName.setText(name);
            mBinding.etSalePrice.setText(DFUtils.getNum2(salePrice));
            mBinding.etOnlinePrice.setText(DFUtils.getNum2(onlinePrice));
            mBinding.etMemberPrice.setText(DFUtils.getNum2(memberPrice));
            mBinding.tvAveragePrice.setText(DFUtils.getNum2(data0.getGoodsInPrice()));
            mBinding.tvUnit.setText(unit);
        }
    }

    /**
     * 选择供货商
     * @param view
     */
    private void showPopSupplier(View view) {
        SupplierPop.showDialog(this,
                mBinding.ivSupplier,
                view,
                mBinding.linSupplier.getMeasuredWidth(),
                supplierList,
                supplierUnique,
                new SupplierPop.MyListener() {
                    @Override
                    public void onAddClick() {
                        //添加供货商
                        SupplierEditDialog.showDialog(GoodsActivity.this, 0, "", (id, name, type) -> {
                            getSupplierList(view);
                        });
                    }

                    @Override
                    public void onCallBack(SupplierPcData data) {
                        supplierUnique = data.getSupplier_unique();
                        mBinding.tvSupplier.setText(data.getSupplier_name());
                    }
                });
    }

    /**
     * 供货商列表
     */
    private void getSupplierList(View v) {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("supMsg", "");
        params.put("page", 1);
        params.put("limit", 10000);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getSupplierList(),
                params,
                SupplierPcData.class,
                new RequestListListener<SupplierPcData>() {
                    @Override
                    public void onResult(List<SupplierPcData> list) {
                        supplierList.clear();
                        supplierList.addAll(list);
                        showPopSupplier(v);
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 获取商品分类数据
     */
    private void getCateList() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getCatePcList(),
                map,
                CatePcData.class,
                new RequestListListener<CatePcData>() {
                    @Override
                    public void onResult(List<CatePcData> list) {
                        if (LitePal.findFirst(CatePcData.class) != null) {
                            LitePal.deleteAll(CatePcData.class);
                        }
                        LitePal.saveAll(list);
                        cateList.clear();
//                        if (list.size() > 12) {
//                            for (int i = 0; i < 12; i++) {
//                                cateList.add(list.get(i));
//                            }
//                        } else {
//                            cateList.addAll(list);
//                        }
//                        if (list.size() > 11) {
//                            for (int i = 0; i < 11; i++) {
//                                cateList.add(list.get(i));
//                            }
//                        } else {
//                            cateList.addAll(list);
//                        }
                        cateList.addAll(list);
                        cateList.add(new CatePcData("", getRstr(R.string.all)));

                        if (cateList.size() > posCate) {
                            cateList.get(posCate).setCheck(true);
                            cateAdapter.setDataList(cateList);
                            parentId = cateList.get(posCate).getGoodsKindInventedId();
                            parentUnique = cateList.get(posCate).getGoods_kind_unique();
                            max = 0;
                            getGoodsList();
                        }
                    }
                });
    }

    /**
     * 获取商品列表数据
     */
    private void getGoodsList() {
        hideSoftInput(this);
        mBinding.smartRefreshLayout.finishRefresh();
        mBinding.smartRefreshLayout.finishLoadMore();
        List<GoodsData> list;
        if (TextUtils.isEmpty(parentUnique)) {
            if (TextUtils.isEmpty(keyWords)) {
                list = LitePal
                        .limit(Constants.limit)
                        .offset(max)
                        .find(GoodsData.class);
            } else {
                list = LitePal
                        .limit(Constants.limit)
                        .offset(max)
                        .where("goods_name like ? or goods_barcode like ?", "%" + keyWords + "%", "%" + keyWords + "%")
                        .find(GoodsData.class);
            }
        } else {
            if (TextUtils.isEmpty(keyWords)) {
                list = LitePal
                        .limit(Constants.limit)
                        .offset(max)
                        .where("virtual_kind_unique = ?", parentUnique)
                        .find(GoodsData.class);
            } else {
                list = LitePal
                        .limit(Constants.limit)
                        .offset(max)
                        .where("virtual_kind_unique = ? and goods_name like ? or goods_barcode like ?", parentUnique, "%" + keyWords + "%", "%" + keyWords + "%")
                        .find(GoodsData.class);
            }
        }
        if (list != null) {
            if (max == 0) {
                dataList.clear();
            }
            dataList.addAll(list);
            max += dataList.size();
        }
        mAdapter.setDataList(dataList);
        mAdapter.setVirtual_kind_unique(parentUnique);
        if (dataList.isEmpty()) {
            mBinding.recyclerView.setVisibility(View.GONE);
            mBinding.linEmpty.setVisibility(View.VISIBLE);
        } else {
            mBinding.recyclerView.setVisibility(View.VISIBLE);
            mBinding.linEmpty.setVisibility(View.GONE);
        }
    }

    /**
     * 商品详情
     *
     * @param barcode
     */
    private void getGoodsInfo(String barcode) {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("goodsBarcode", barcode);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse((LifecycleOwner) mContext,
                ZURL.getGoodsInfo(),
                params,
                GoodsInfoData.class,
                new RequestListener<GoodsInfoData>() {
                    @Override
                    public void success(GoodsInfoData data) {
                        hideDialog();
                        setUI(data);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.linNothing.setVisibility(View.VISIBLE);
                        mBinding.linInfo.setVisibility(View.GONE);
                    }
                });
    }

    /**
     * 商品编辑
     */
    private void postGoodsEdit() {
        Map<String, Object> params = new HashMap<>();
        List array = new ArrayList();
        /*基础包装*/
        name = mBinding.etName.getText().toString().trim();
        if (TextUtils.isEmpty(name)) {
            showToast(1, getRstr(R.string.input_goods_name));
            return;
        }
        salePrice = TextUtils.isEmpty(mBinding.etSalePrice.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etSalePrice.getText().toString().trim());
        if (salePrice <= 0) {
            showToast(1, getRstr(R.string.input_price_sale));
            return;
        }
        memberPrice = TextUtils.isEmpty(mBinding.etMemberPrice.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etMemberPrice.getText().toString().trim());
        onlinePrice = TextUtils.isEmpty(mBinding.etOnlinePrice.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etOnlinePrice.getText().toString().trim());
        Map object0 = new HashMap();
        object0.put("goodsId", goodsId);
        object0.put("goodsBarcode", barcode);
        object0.put("goodsName", name);
        object0.put("goodsSalePrice", salePrice);
        object0.put("goodsWebSalePrice", onlinePrice);
        object0.put("goodsUnit", unit);
        object0.put("goodsCusPrice", memberPrice);
        object0.put("goodsContain", 1);
        array.add(object0);
        showDialog();
        params.put("shopUnique", getShopUnique());
        params.put("goodsKindUnique", goodsKindUnique);//二级分类编号
        params.put("supplierUnique", supplierUnique);//供货商编号
        params.put("goodsChengType", chengType);//计价类型 0.计件 1.称重
        params.put("foreignKey", barcode);//包装外键（最小规格的商品条码）
        params.put("goodsMessage", array);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getGoodsEdit(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        GoodsData data = LitePal.where("goods_barcode = ?", barcode).findFirst(GoodsData.class);
                        if (data != null) {
                            data.setGoods_name(name);
                            data.setGoodsChengType(chengType);
                            data.setDefault_supplier_unique(supplierUnique);
                            data.setGoods_sale_price(salePrice);
                            data.setGoods_web_sale_price(onlinePrice);
                            data.setGoods_cus_price(DFUtils.getNum4(memberPrice));
                            data.setGoods_unit(unit);
                            data.save();
                        }
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_LIST));
                        //1.更新列表
                        if (dataList.size() > pos) {
                            dataList.get(pos).setGoods_name(name);
                            dataList.get(pos).setGoodsChengType(chengType);
                            dataList.get(pos).setGoods_sale_price(salePrice);
                            dataList.get(pos).setGoods_unit(unit);
                            mAdapter.notifyItemChanged(pos);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 删除商品
     */
    public void postGoodsDel() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("goodsBarcode", barcode);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getGoodsDel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        setUI(null);
                        GoodsData data = LitePal.where("goods_barcode = ?", barcode).findFirst(GoodsData.class);
                        if (data != null) {
                            data.delete();
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_LIST));
                        }
                        if (dataList.size() > pos) {
                            dataList.remove(pos);
                            mAdapter.remove(pos);
                            if (dataList.size() < 1) {
                                max = 0;
                                getGoodsList();
                            }
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 删除或增加虚拟分类下的商品信息
     */
    public void postCatePcGoodsEdit(String barcode) {
        JSONArray array = new JSONArray();
        array.put(barcode);
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("goodsKindUnique", parentUnique);
        map.put("goodskindInventedId", parentId);
        map.put("operateType", 0);//0.删除 1.增加
        map.put("goodsList", array.toString());
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getCatePcGoodsEdit(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        GoodsData data = LitePal.where("goods_barcode = ?", barcode).findFirst(GoodsData.class);
                        if (data != null) {
                            data.setVirtual_kind_unique("");
                            data.save();
                        }
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_LIST));
                        if (dataList.size() > pos) {
                            dataList.remove(pos);
                            mAdapter.remove(pos);
                            if (dataList.isEmpty()) {
                                max = 0;
                                getGoodsList();
                            }
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }
}
