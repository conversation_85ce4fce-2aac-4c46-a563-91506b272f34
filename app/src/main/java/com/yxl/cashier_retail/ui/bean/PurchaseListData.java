package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:购销单列表（实体类）
 * Created by jingang on 2023/9/13
 */
public class PurchaseListData implements Serializable {
    /**
     * 供货商详情
     * id : 1
     * billNo : B202309091735581220000
     * supplierUnique : d60054eac842485aa3fa31a4dcfc57f3
     * supplierName : 影响力
     * supplierPhone : 18167733244
     * status : 1
     * totalPrice : 121.5
     * goodsCategory : 2
     * goodsList : [{"goodsBarcode":"8960757619486","goodsPicturepath":"https://auth.ikeda10.xyz/ClothingShoesandJewelry","purchaseGoodsCount":9},{"goodsBarcode":"23413252","goodsPicturepath":"https://www.kwokok7.cn/Food","purchaseGoodsCount":6}]
     */

    private boolean select;
    private int id;
    private String billNo;
    private String supplierUnique;
    private String supplierName;
    private String supplierPhone;
    private int status;//1-已提交(待收货)，2-已收货(待付款)，3-待确认，4-已完成，5-异常，6-作废
    private double totalPrice;
    private int goodsCategory;//商品种类
    private String createTime;
    private String remark;//备注
    private List<ChildGoodsData> goodsList;

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierPhone() {
        return supplierPhone;
    }

    public void setSupplierPhone(String supplierPhone) {
        this.supplierPhone = supplierPhone;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public double getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(double totalPrice) {
        this.totalPrice = totalPrice;
    }

    public int getGoodsCategory() {
        return goodsCategory;
    }

    public void setGoodsCategory(int goodsCategory) {
        this.goodsCategory = goodsCategory;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<ChildGoodsData> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<ChildGoodsData> goodsList) {
        this.goodsList = goodsList;
    }
}
