package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogRiderEditBinding;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Describe:dialog（骑手新增、编辑、删除）
 * Created by jingang on 2024/6/20
 */
@SuppressLint("NonConstantResourceId")
public class RiderEditDialog extends BaseDialog<DialogRiderEditBinding> implements View.OnClickListener {
    private static int id;
    private static String name, mobile;

    public static void showDialog(Activity activity, int id, String name, String mobile, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        RiderEditDialog.listener = listener;
        RiderEditDialog.id = id;
        RiderEditDialog.name = name;
        RiderEditDialog.mobile = mobile;
        RiderEditDialog dialog = new RiderEditDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public RiderEditDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        mBinding.tvDialogDel.setOnClickListener(this);
        if (id == 0) {
            mBinding.tvDialogTitle.setText(getRstr(R.string.rider_add));
            mBinding.tvDialogDel.setVisibility(View.GONE);
        } else {
            mBinding.tvDialogTitle.setText(getRstr(R.string.rider_edit));
            mBinding.etDialogName.setText(name);
            mBinding.etDialogMobile.setText(mobile);
            mBinding.tvDialogDel.setVisibility(View.VISIBLE);
        }
    }

    @Override
    protected DialogRiderEditBinding getViewBinding() {
        return DialogRiderEditBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //保存
                if (TextUtils.isEmpty(mBinding.etDialogName.getText().toString().trim())) {
                    showToast(1, getRstr(R.string.input_rider_name));
                    return;
                }
                if (TextUtils.isEmpty(mBinding.etDialogMobile.getText().toString().trim())) {
                    showToast(1, getRstr(R.string.input_rider_mobile));
                    return;
                }
                if (mBinding.etDialogMobile.getText().toString().trim().length() < 11) {
                    showToast(1, getRstr(R.string.input_rider_mobile_right));
                    return;
                }
                if (id == 0) {
                    postRider(0);
                } else {
                    postRider(2);
                }
                break;
            case R.id.tvDialogDel:
                //删除
                postRider(1);
                break;
        }
    }

    /**
     * 新增、编辑、删除骑手
     *
     * @param type 0.新增 1.删除 2.编辑
     */
    private void postRider(int type) {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("courier_name", mBinding.etDialogName.getText().toString().trim());
        params.put("courier_phone", mBinding.etDialogMobile.getText().toString().trim());
        if (id != 0) {
            params.put("id", id);
        }
        if (type != 0) {
            params.put("delete_courier", type);
        }
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getRiderEdit(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        if (listener != null) {
                            listener.onConfirm(id, name, mobile, type);
                            dismiss();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(int id, String name, String mobile, int type);
    }
}
