package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:退款订单详情（实体类）
 * Created by jingang on 2024/5/23
 */
public class RefundInfoData implements Serializable {
    /**
     * id : null
     * saleListUnique : 1716023028536
     * shopUnique : 1536215939565
     * retListDatetime : 2024-05-18 17:05:44
     * retListTotal : 12.0
     * retListCount : 6.0
     * retListState : 2
     * retListStateMsg : 已退款
     * retListHandlestate : 3
     * retListHandlestateMsg : 已退款
     * retListRemarks :
     * staffId : 3586
     * macId : E43A6E2BB03A
     * retOrigin : 3
     * retOriginMsg : 小程序
     * retMoneyType : null
     * retMoneyTypeMsg : null
     * retListTotalMoney : null
     * retBackDatetime : 2024-05-18 17:05:50
     * retListUnique : 17160231449231000
     * retListBean : 0
     * retListReason : 买错商品了
     * retPayMsg : 本次已选商品退款总计：12.0元，储值卡退款：9.05元；百货豆退款：2.95元；
     * cusUnique : 1706146929715
     * saleListHandlestate : 6
     * retListDelfee : 0.0
     * saleListPhone : 18315761966
     * saleListAddress :
     * saleListTotal : 16.0
     * saleListDelfee : 0.0
     * detailList : [{"retListDetailId":1298,"saleListUnique":"1716023028536","goodsBarcode":"6944312688143","goodsName":"测试6944312688143","retListDetailCount":6,"retListDetailPrice":2,"handleWay":1,"retListOriginPrice":2,"retListUnique":"17160231449231000","rsaleListDetailId":null,"imagePath":"upload/no_goodsB.jpg","goodsStandard":"","goodsUnit":"","saleListDetailCount":8}]
     * payDetailList : [{"retPayDetailId":1775,"saleListUnique":"1716023028536","retListUnique":"17160231449231000","payType":5,"payTypeMsg":"储值卡","payMoney":9.05,"serviceType":1,"serviceTypeMsg":"线下","mchId":null},{"retPayDetailId":1776,"saleListUnique":"1716023028536","retListUnique":"17160231449231000","payType":8,"payTypeMsg":"百货豆","payMoney":2.95,"serviceType":1,"serviceTypeMsg":"线下","mchId":null}]
     */

    private String saleListUnique;//原订单编号
    private String retListDatetime;//申请时间
    private double retListTotal;//申退总金额
    private double retListCount;//申退商品总数量
    private int retListHandlestate;//退款状态
    private String retListHandlestateMsg;//退款状态名称
    private String retListRemarks;
    private int staffId;
    private String macId;
    private int retOrigin;
    private String retOriginMsg;
    private double retListTotalMoney;
    private String retBackDatetime;
    private String retListUnique;
    private int retListBean;
    private String retListReason;
    private String retPayMsg;
    private String cusUnique;
    private int saleListHandlestate;
    private double retListDelfee;
    private String saleListPhone;
    private String saleListAddress;
    private double saleListTotal;
    private double saleListDelfee;
    private String retMoneyTypeMsg;//退款方式
    private List<OrderGoodsData> detailList;
    private List<PayDetailListBean> payDetailList;

    public String getSaleListUnique() {
        return saleListUnique;
    }

    public void setSaleListUnique(String saleListUnique) {
        this.saleListUnique = saleListUnique;
    }

    public String getRetListDatetime() {
        return retListDatetime;
    }

    public void setRetListDatetime(String retListDatetime) {
        this.retListDatetime = retListDatetime;
    }

    public double getRetListTotal() {
        return retListTotal;
    }

    public void setRetListTotal(double retListTotal) {
        this.retListTotal = retListTotal;
    }

    public double getRetListCount() {
        return retListCount;
    }

    public void setRetListCount(double retListCount) {
        this.retListCount = retListCount;
    }

    public int getRetListHandlestate() {
        return retListHandlestate;
    }

    public void setRetListHandlestate(int retListHandlestate) {
        this.retListHandlestate = retListHandlestate;
    }

    public String getRetListHandlestateMsg() {
        return retListHandlestateMsg;
    }

    public void setRetListHandlestateMsg(String retListHandlestateMsg) {
        this.retListHandlestateMsg = retListHandlestateMsg;
    }

    public String getRetListRemarks() {
        return retListRemarks;
    }

    public void setRetListRemarks(String retListRemarks) {
        this.retListRemarks = retListRemarks;
    }

    public int getStaffId() {
        return staffId;
    }

    public void setStaffId(int staffId) {
        this.staffId = staffId;
    }

    public String getMacId() {
        return macId;
    }

    public void setMacId(String macId) {
        this.macId = macId;
    }

    public int getRetOrigin() {
        return retOrigin;
    }

    public void setRetOrigin(int retOrigin) {
        this.retOrigin = retOrigin;
    }

    public String getRetOriginMsg() {
        return retOriginMsg;
    }

    public void setRetOriginMsg(String retOriginMsg) {
        this.retOriginMsg = retOriginMsg;
    }

    public double getRetListTotalMoney() {
        return retListTotalMoney;
    }

    public void setRetListTotalMoney(double retListTotalMoney) {
        this.retListTotalMoney = retListTotalMoney;
    }

    public String getRetBackDatetime() {
        return retBackDatetime;
    }

    public void setRetBackDatetime(String retBackDatetime) {
        this.retBackDatetime = retBackDatetime;
    }

    public String getRetListUnique() {
        return retListUnique;
    }

    public void setRetListUnique(String retListUnique) {
        this.retListUnique = retListUnique;
    }

    public int getRetListBean() {
        return retListBean;
    }

    public void setRetListBean(int retListBean) {
        this.retListBean = retListBean;
    }

    public String getRetListReason() {
        return retListReason;
    }

    public void setRetListReason(String retListReason) {
        this.retListReason = retListReason;
    }

    public String getRetPayMsg() {
        return retPayMsg;
    }

    public void setRetPayMsg(String retPayMsg) {
        this.retPayMsg = retPayMsg;
    }

    public String getCusUnique() {
        return cusUnique;
    }

    public void setCusUnique(String cusUnique) {
        this.cusUnique = cusUnique;
    }

    public int getSaleListHandlestate() {
        return saleListHandlestate;
    }

    public void setSaleListHandlestate(int saleListHandlestate) {
        this.saleListHandlestate = saleListHandlestate;
    }

    public double getRetListDelfee() {
        return retListDelfee;
    }

    public void setRetListDelfee(double retListDelfee) {
        this.retListDelfee = retListDelfee;
    }

    public String getSaleListPhone() {
        return saleListPhone;
    }

    public void setSaleListPhone(String saleListPhone) {
        this.saleListPhone = saleListPhone;
    }

    public String getSaleListAddress() {
        return saleListAddress;
    }

    public void setSaleListAddress(String saleListAddress) {
        this.saleListAddress = saleListAddress;
    }

    public double getSaleListTotal() {
        return saleListTotal;
    }

    public void setSaleListTotal(double saleListTotal) {
        this.saleListTotal = saleListTotal;
    }

    public double getSaleListDelfee() {
        return saleListDelfee;
    }

    public void setSaleListDelfee(double saleListDelfee) {
        this.saleListDelfee = saleListDelfee;
    }

    public String getRetMoneyTypeMsg() {
        return retMoneyTypeMsg;
    }

    public void setRetMoneyTypeMsg(String retMoneyTypeMsg) {
        this.retMoneyTypeMsg = retMoneyTypeMsg;
    }

    public List<OrderGoodsData> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<OrderGoodsData> detailList) {
        this.detailList = detailList;
    }

    public List<PayDetailListBean> getPayDetailList() {
        return payDetailList;
    }

    public void setPayDetailList(List<PayDetailListBean> payDetailList) {
        this.payDetailList = payDetailList;
    }

    public static class PayDetailListBean {
        /**
         * retPayDetailId : 1775
         * saleListUnique : 1716023028536
         * retListUnique : 17160231449231000
         * payType : 5
         * payTypeMsg : 储值卡
         * payMoney : 9.05
         * serviceType : 1
         * serviceTypeMsg : 线下
         * mchId : null
         */

        private int retPayDetailId;
        private String saleListUnique;
        private String retListUnique;
        private int payType;
        private String payTypeMsg;
        private double payMoney;
        private int serviceType;
        private String serviceTypeMsg;
        private Object mchId;

        public int getRetPayDetailId() {
            return retPayDetailId;
        }

        public void setRetPayDetailId(int retPayDetailId) {
            this.retPayDetailId = retPayDetailId;
        }

        public String getSaleListUnique() {
            return saleListUnique;
        }

        public void setSaleListUnique(String saleListUnique) {
            this.saleListUnique = saleListUnique;
        }

        public String getRetListUnique() {
            return retListUnique;
        }

        public void setRetListUnique(String retListUnique) {
            this.retListUnique = retListUnique;
        }

        public int getPayType() {
            return payType;
        }

        public void setPayType(int payType) {
            this.payType = payType;
        }

        public String getPayTypeMsg() {
            return payTypeMsg;
        }

        public void setPayTypeMsg(String payTypeMsg) {
            this.payTypeMsg = payTypeMsg;
        }

        public double getPayMoney() {
            return payMoney;
        }

        public void setPayMoney(double payMoney) {
            this.payMoney = payMoney;
        }

        public int getServiceType() {
            return serviceType;
        }

        public void setServiceType(int serviceType) {
            this.serviceType = serviceType;
        }

        public String getServiceTypeMsg() {
            return serviceTypeMsg;
        }

        public void setServiceTypeMsg(String serviceTypeMsg) {
            this.serviceTypeMsg = serviceTypeMsg;
        }

        public Object getMchId() {
            return mchId;
        }

        public void setMchId(Object mchId) {
            this.mchId = mchId;
        }
    }
}
