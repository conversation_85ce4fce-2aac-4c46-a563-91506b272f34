package com.yxl.cashier_retail.ui.bean;

import org.litepal.crud.LitePalSupport;

import java.io.Serializable;

/**
 * Describe:商品列表（实体类）
 * Created by jingang on 2024/5/23
 */
public class GoodsListData extends LitePalSupport implements Serializable {
    /**
     * supplierUnique : 2469853835650
     * stockType : 2
     * goodsId : 1540507673
     * saleCount : 0
     * goodsPicturePath : http://document.buyhoo.cc/cpfr/8f07d86f912541929bd64b939347fc07.jpg
     * purCount : 0
     * pcShelfState : 1
     * goodsCount : 99.0
     * goodsSalePrice : 7.0
     * goodsInPrice : 1.32
     * goodsUnit : 支
     * godsStandard : 白雪
     * goodsName : 白雪直液式走珠笔1
     * goodsBarcode : 6920024533978
     * shelfState : 2
     */
    private boolean select;//是否选择
    private boolean showDel;//是否显示删除
    private int cartNum;//数量
    private String supplierUnique;
    private int stockType;//1、缺货；2、充足
    private int goodsId;
    private int saleCount;//月销量
    private String goodsPicturePath;
    private double purCount;
    private int pcShelfState;//收银机上下架状态 1.上架 2.下架
    private double goodsCount;//库存
    private double goodsSalePrice;//售价
    private double goodsInPrice;//进价
    private String goodsUnit;
    private String godsStandard;
    private String goodsName;
    private String goodsBarcode;
    private int shelfState;//小程序上下架状态 1.上架 2.下架
    private double goodsSalePriceUndoc;//未建档商品售价

    /*编辑后的单价(只在收银页主单使用)*/
    private double newPrice;
    private boolean editPrice;//是否编辑单价
    private boolean editCount;//是否编辑数量
    private boolean editTotal;//是否编辑总价

    /**
     * 商品详情
     * goodsId : 1542549482
     * goodsBarcode : 1626677469134
     * goodsName : 在
     * goodsStandard :
     * goodsUnit :
     * goodsInPrice : 0.03
     * goodsSalePrice : 0.0
     * goodsWebSalePrice : 0.0
     * foreign_key : 1626677469134
     * containCount : 1
     * goodsContainCount : null
     * goodsPromotion : 1.0
     * goodsDiscount : 1.0
     * goodsPicturepath : /image/suppliers/GS371306159/a2f04d5a-0650-4d87-a360-750c917e39b6.jpg
     * goodsCusPrice : 0.0
     * shelfState : 2
     * countPresent : 0.0
     * pgoodsName : null
     * pgoodsCount : null
     * pgoodsUnit : null
     * stock_warning_status : 0
     * out_stock_waring_count : 0
     * unsalable_count : 0
     * tableType : null
     */
    private String goodsStandard;//规格
    private double goodsWebSalePrice;//网购价
    private double containCount;//包含最小单位商品数量
    private double goodsPromotion;//商品促销状态：1、促销；2、不促销
    private double goodsDiscount;//折扣
    private String goodsPicturepath;//图片
    private double goodsCusPrice;//会员价
    private double countPresent;//购满数量，为0.0时没有赠品
    private int stock_warning_status;//0:没有库存预警 1:有库存预警
    private double out_stock_waring_count;//库存预警下限
    private double unsalable_count;//库存预警上限
    private boolean check;
    private double stockPrice;//上次出入库单价
    private String minSaleCount;//起订量
    private double goodStockPrice;//最近入库价

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }

    public boolean isShowDel() {
        return showDel;
    }

    public void setShowDel(boolean showDel) {
        this.showDel = showDel;
    }

    public int getCartNum() {
        return cartNum;
    }

    public void setCartNum(int cartNum) {
        this.cartNum = cartNum;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public int getStockType() {
        return stockType;
    }

    public void setStockType(int stockType) {
        this.stockType = stockType;
    }

    public int getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(int goodsId) {
        this.goodsId = goodsId;
    }

    public int getSaleCount() {
        return saleCount;
    }

    public void setSaleCount(int saleCount) {
        this.saleCount = saleCount;
    }

    public String getGoodsPicturePath() {
        return goodsPicturePath;
    }

    public void setGoodsPicturePath(String goodsPicturePath) {
        this.goodsPicturePath = goodsPicturePath;
    }

    public double getPurCount() {
        return purCount;
    }

    public void setPurCount(double purCount) {
        this.purCount = purCount;
    }

    public int getPcShelfState() {
        return pcShelfState;
    }

    public void setPcShelfState(int pcShelfState) {
        this.pcShelfState = pcShelfState;
    }

    public double getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(double goodsCount) {
        this.goodsCount = goodsCount;
    }

    public double getGoodsSalePrice() {
        return goodsSalePrice;
    }

    public void setGoodsSalePrice(double goodsSalePrice) {
        this.goodsSalePrice = goodsSalePrice;
    }

    public double getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(double goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getGodsStandard() {
        return godsStandard;
    }

    public void setGodsStandard(String godsStandard) {
        this.godsStandard = godsStandard;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public int getShelfState() {
        return shelfState;
    }

    public void setShelfState(int shelfState) {
        this.shelfState = shelfState;
    }

    public double getGoodsSalePriceUndoc() {
        return goodsSalePriceUndoc;
    }

    public void setGoodsSalePriceUndoc(double goodsSalePriceUndoc) {
        this.goodsSalePriceUndoc = goodsSalePriceUndoc;
    }

    public double getNewPrice() {
        return newPrice;
    }

    public void setNewPrice(double newPrice) {
        this.newPrice = newPrice;
    }

    public boolean isEditPrice() {
        return editPrice;
    }

    public void setEditPrice(boolean editPrice) {
        this.editPrice = editPrice;
    }

    public boolean isEditCount() {
        return editCount;
    }

    public void setEditCount(boolean editCount) {
        this.editCount = editCount;
    }

    public boolean isEditTotal() {
        return editTotal;
    }

    public void setEditTotal(boolean editTotal) {
        this.editTotal = editTotal;
    }

    public String getGoodsStandard() {
        return goodsStandard;
    }

    public void setGoodsStandard(String goodsStandard) {
        this.goodsStandard = goodsStandard;
    }

    public double getGoodsWebSalePrice() {
        return goodsWebSalePrice;
    }

    public void setGoodsWebSalePrice(double goodsWebSalePrice) {
        this.goodsWebSalePrice = goodsWebSalePrice;
    }

    public double getContainCount() {
        return containCount;
    }

    public void setContainCount(double containCount) {
        this.containCount = containCount;
    }

    public double getGoodsPromotion() {
        return goodsPromotion;
    }

    public void setGoodsPromotion(double goodsPromotion) {
        this.goodsPromotion = goodsPromotion;
    }

    public double getGoodsDiscount() {
        return goodsDiscount;
    }

    public void setGoodsDiscount(double goodsDiscount) {
        this.goodsDiscount = goodsDiscount;
    }

    public String getGoodsPicturepath() {
        return goodsPicturepath;
    }

    public void setGoodsPicturepath(String goodsPicturepath) {
        this.goodsPicturepath = goodsPicturepath;
    }

    public double getGoodsCusPrice() {
        return goodsCusPrice;
    }

    public void setGoodsCusPrice(double goodsCusPrice) {
        this.goodsCusPrice = goodsCusPrice;
    }

    public double getCountPresent() {
        return countPresent;
    }

    public void setCountPresent(double countPresent) {
        this.countPresent = countPresent;
    }

    public int getStock_warning_status() {
        return stock_warning_status;
    }

    public void setStock_warning_status(int stock_warning_status) {
        this.stock_warning_status = stock_warning_status;
    }

    public double getOut_stock_waring_count() {
        return out_stock_waring_count;
    }

    public void setOut_stock_waring_count(double out_stock_waring_count) {
        this.out_stock_waring_count = out_stock_waring_count;
    }

    public double getUnsalable_count() {
        return unsalable_count;
    }

    public void setUnsalable_count(double unsalable_count) {
        this.unsalable_count = unsalable_count;
    }

    public boolean isCheck() {
        return check;
    }

    public void setCheck(boolean check) {
        this.check = check;
    }

    public double getStockPrice() {
        return stockPrice;
    }

    public void setStockPrice(double stockPrice) {
        this.stockPrice = stockPrice;
    }

    public String getMinSaleCount() {
        return minSaleCount;
    }

    public void setMinSaleCount(String minSaleCount) {
        this.minSaleCount = minSaleCount;
    }

    public double getGoodStockPrice() {
        return goodStockPrice;
    }

    public void setGoodStockPrice(double goodStockPrice) {
        this.goodStockPrice = goodStockPrice;
    }

}
