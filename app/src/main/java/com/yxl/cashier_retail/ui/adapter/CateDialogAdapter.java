package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.CateData;

/**
 * Describe:一级分类-弹窗（适配器）
 * Created by jingang on 2024/7/1
 */
public class CateDialogAdapter extends BaseAdapter<CateData> {

    public CateDialogAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_cate_dialog;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName = holder.getView(R.id.tvItemName);
        tvName.setText(mDataList.get(position).getGroupName());
        if (mDataList.get(position).isCheck()) {
            tvName.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
        } else {
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
        }
    }
}
