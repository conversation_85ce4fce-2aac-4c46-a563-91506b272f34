package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.MallMarketingData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.utils.StringUtils;

/**
 * Describe:商城-硬核补贴（适配器）
 * Created by jingang on 2024/6/3
 */
public class MallSubsidyAdapter extends BaseAdapter<MallMarketingData.ActivityBean.ActivityListBean> {

    public MallSubsidyAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_mall_subsidy;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvName, tvPrice;
        tvName = holder.getView(R.id.tvItemName);
        tvPrice = holder.getView(R.id.tvItemPrice);

        MallMarketingData.ActivityBean.ActivityListBean data = mDataList.get(position % mDataList.size());
        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(data.getGoods_img()))
                .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img))
                .into(ivImg);
        tvName.setText(data.getGoods_name());
        tvPrice.setText("¥" + DFUtils.getNum2(data.getPrice()));
    }
}
