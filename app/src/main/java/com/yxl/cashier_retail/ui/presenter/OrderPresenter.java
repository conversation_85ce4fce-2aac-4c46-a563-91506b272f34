package com.yxl.cashier_retail.ui.presenter;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;

import com.google.gson.Gson;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.ui.bean.QueryOrderInfoData;
import com.yxl.cashier_retail.ui.bean.OrderListData;
import com.yxl.cashier_retail.ui.contract.OrderContract;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.HashMap;
import java.util.Map;

/**
 * Describe:订单列表
 * Created by jingang on 2024/5/20
 */
public class OrderPresenter implements OrderContract.Presenter {
    private Context context;
    private OrderContract.View mView;

    public OrderPresenter(Context context) {
        this.context = context;
    }

    @Override
    public void attachView(@NonNull OrderContract.View view) {
        this.mView = view;
    }

    @Override
    public void detachView() {
        this.mView = null;
    }

    @Override
    public void getOrderList(String shopUnique, String staff_id, String startTime, String endTime, String keyWords, int sale_list_payment, int page, int limit) {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", shopUnique);
        params.put("staff_id", staff_id);
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        params.put("msg", keyWords);
        params.put("source_type", 1);
        params.put("sale_list_payment", sale_list_payment);
        RXHttpUtil.requestByFormPostAsOriginalResponse((LifecycleOwner) context,
                ZURL.getOrderList(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        OrderListData data = new Gson().fromJson(s, OrderListData.class);
                        if (mView == null) {
                            return;
                        }
                        if (data == null) {
                            return;
                        }
                        if (data.getStatus() != 0) {
                            mView.onListError(data.getMsg());
                            return;
                        }
                        mView.onListSuccess(data);
                    }

                    @Override
                    public void onError(String msg) {
                        if (mView != null) {
                            mView.onListError(msg);
                        }
                    }
                });
    }

    @Override
    public void getOrderInfo(String shop_unique, String sale_list_unique) {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", shop_unique);
        params.put("sale_list_unique", sale_list_unique);
        RXHttpUtil.requestByFormPostAsOriginalResponse((LifecycleOwner) context,
                ZURL.getQueryOrderInfo(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        QueryOrderInfoData data = new Gson().fromJson(s, QueryOrderInfoData.class);
                        if (mView == null) {
                            return;
                        }
                        if (data == null) {
                            return;
                        }
                        if (data.getStatus() != 0 || data.getData() == null) {
                            mView.onInfoError(data.getMsg());
                            return;
                        }
                        mView.onInfoSuccess(data.getData());
                    }

                    @Override
                    public void onError(String msg) {
                        if (mView != null) {
                            mView.onInfoError(msg);
                        }
                    }
                });
    }
}
