package com.yxl.cashier_retail.ui.bean;

/**
 * Describe:Bus实体类
 * Created by jingang on 2024/6/6
 */
public class EventData {
    private String msg;
    private String types;
    private int num;
    private byte[] bytes;//串口数据
    private String xPrinterStr;//小票打印机msg

    public EventData(String msg) {
        this.msg = msg;
    }

    public EventData(String types, String msg) {
        this.msg = msg;
        this.types = types;
    }

    public EventData(int num, String msg) {
        this.num = num;
        this.msg = msg;
    }

    /**
     * 串口
     *
     * @param bytes 数据
     * @param msg   标记
     */
    public EventData(byte[] bytes, String msg) {
        this.bytes = bytes;
        this.msg = msg;
    }

    /**
     * XPrinter小票打印机
     *
     * @param msg
     * @param num      1.连接成功 2.连接失败 3.发送失败 4.连接中断 5.USB设备已连上 6.USB设备已断开
     * @param connInfo
     * @param message
     */
    public EventData(String msg, int num, String connInfo, String message) {
        this.msg = msg;
        this.num = num;
        this.types = connInfo;
        this.xPrinterStr = message;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getTypes() {
        return types;
    }

    public void setTypes(String types) {
        this.types = types;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public byte[] getBytes() {
        return bytes;
    }

    public void setBytes(byte[] bytes) {
        this.bytes = bytes;
    }

    public String getxPrinterStr() {
        return xPrinterStr;
    }

    public void setxPrinterStr(String xPrinterStr) {
        this.xPrinterStr = xPrinterStr;
    }
}
