package com.yxl.cashier_retail.ui.bean;

/**
 * Describe: 统计-info(实体类)
 * Created by jingang on 2025/2/24
 */
public class StatisticsInfoData {
    /**
     * goodsTypeNum : 0
     * supplementNum : 0
     * warningNum : 0
     * todayReturnTotal : null
     */

    private double rechargeAmount;//充值金额
    private double storedAmount;//储值金额
    private int goodsTypeNum;//商品总种类
    private int supplementNum;//需补货商品
    private int warningNum;//过期预警
    private double todayReturnTotal;//退款金额

    public double getRechargeAmount() {
        return rechargeAmount;
    }

    public void setRechargeAmount(double rechargeAmount) {
        this.rechargeAmount = rechargeAmount;
    }

    public double getStoredAmount() {
        return storedAmount;
    }

    public void setStoredAmount(double storedAmount) {
        this.storedAmount = storedAmount;
    }

    public int getGoodsTypeNum() {
        return goodsTypeNum;
    }

    public void setGoodsTypeNum(int goodsTypeNum) {
        this.goodsTypeNum = goodsTypeNum;
    }

    public int getSupplementNum() {
        return supplementNum;
    }

    public void setSupplementNum(int supplementNum) {
        this.supplementNum = supplementNum;
    }

    public int getWarningNum() {
        return warningNum;
    }

    public void setWarningNum(int warningNum) {
        this.warningNum = warningNum;
    }

    public double getTodayReturnTotal() {
        return todayReturnTotal;
    }

    public void setTodayReturnTotal(double todayReturnTotal) {
        this.todayReturnTotal = todayReturnTotal;
    }
}
