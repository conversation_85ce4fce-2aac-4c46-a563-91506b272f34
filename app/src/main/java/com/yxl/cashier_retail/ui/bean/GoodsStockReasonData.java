package com.yxl.cashier_retail.ui.bean;

import java.util.List;

/**
 * Describe:商品出入库原因
 * Created by jingang on 2024/7/1
 */
public class GoodsStockReasonData {
    /**
     * list : [{"dictLabel":"采购","dictSort":1},{"dictLabel":"退货","dictSort":2},{"dictLabel":"其他","dictSort":3}]
     * dictType : sys_inbound_reason
     */

    private String dictType;
    private List<ListBean> list;

    public String getDictType() {
        return dictType;
    }

    public void setDictType(String dictType) {
        this.dictType = dictType;
    }

    public List<ListBean> getList() {
        return list;
    }

    public void setList(List<ListBean> list) {
        this.list = list;
    }

    public static class ListBean {
        /**
         * dictLabel : 采购
         * dictSort : 1
         */

        private boolean select;
        private String dictLabel;
        private int dictSort;

        public boolean isSelect() {
            return select;
        }

        public void setSelect(boolean select) {
            this.select = select;
        }

        public String getDictLabel() {
            return dictLabel;
        }

        public void setDictLabel(String dictLabel) {
            this.dictLabel = dictLabel;
        }

        public int getDictSort() {
            return dictSort;
        }

        public void setDictSort(int dictSort) {
            this.dictSort = dictSort;
        }

    }
}
