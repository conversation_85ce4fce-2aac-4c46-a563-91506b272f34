package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.QueryStockRecordData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:查询-出入库记录（适配器）
 * Created by jingang on 2025/1/6
 */
public class QueryChuRuAdapter extends BaseAdapter<QueryStockRecordData.DataBean> {
    public QueryChuRuAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_query_churu;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvBarcode, tvType, tvTime, tvCountBefore, tvCount, tvCountAfter, tvSource, tvWay;
        LinearLayout lin = holder.getView(R.id.linItem);
        tvName = holder.getView(R.id.tvItemName);
        tvBarcode = holder.getView(R.id.tvItemBarcode);
        tvType = holder.getView(R.id.tvItemType);
        tvTime = holder.getView(R.id.tvItemTime);
        tvCountBefore = holder.getView(R.id.tvItemCountBefore);
        tvCount = holder.getView(R.id.tvItemCount);
        tvCountAfter = holder.getView(R.id.tvItemCountAfter);
        tvSource = holder.getView(R.id.tvItemSource);
        tvWay = holder.getView(R.id.tvItemWay);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
        }

        tvName.setText(mDataList.get(position).getGoodsName());
        tvBarcode.setText(mDataList.get(position).getGoodsBarcode());
        tvType.setText(mDataList.get(position).getStockType());
        tvTime.setText(mDataList.get(position).getStockTime());
        tvCountBefore.setText(DFUtils.getNum4(mDataList.get(position).getOriginalCount()));
        tvCount.setText(DFUtils.getNum4(mDataList.get(position).getGoodsCount()));
        tvCountAfter.setText(DFUtils.getNum4(mDataList.get(position).getStockCount()));
        tvSource.setText(mDataList.get(position).getStockOrigin());
        tvWay.setText(mDataList.get(position).getStockSource());
    }
}
