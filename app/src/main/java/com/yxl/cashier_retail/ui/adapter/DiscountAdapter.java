package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.DiscountData;
import com.yxl.cashier_retail.utils.DFUtils;

import java.util.List;

/**
 * Describe:折扣（适配器）
 * Created by jingang on 2024/5/31
 */
public class DiscountAdapter extends BaseAdapter<DiscountData> {
    private int type;//0.选择 1.编辑

    public DiscountAdapter(Context context, int type) {
        super(context);
        this.type = type;
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_discount;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName, tvHint;
        tvName = holder.getView(R.id.tvItemName);
        tvHint = holder.getView(R.id.tvItemHint);
        ImageView ivCursor = holder.getView(R.id.ivItemCursor);
        if (type == 0) {
            tvName.setVisibility(View.VISIBLE);
            tvHint.setVisibility(View.GONE);
            ivCursor.setVisibility(View.GONE);
            tvName.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.red));
            if (mDataList.get(position).isSelect()) {
                lin.setBackgroundResource(R.drawable.shape_red_tm_5);
            } else {
                lin.setBackgroundResource(R.drawable.shape_red_kuang_5);
            }
        } else {
            if (mDataList.get(position).getDiscount() > 0) {
                tvName.setVisibility(View.VISIBLE);
                tvHint.setVisibility(View.GONE);
            } else {
                tvName.setVisibility(View.GONE);
                tvHint.setVisibility(View.VISIBLE);
            }
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
            if (mDataList.get(position).isSelect()) {
                lin.setBackgroundResource(R.drawable.shape_green_kuang_5);
                ivCursor.setVisibility(View.VISIBLE);
            } else {
                lin.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                ivCursor.setVisibility(View.GONE);
            }
        }
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName, tvHint;
        tvName = holder.getView(R.id.tvItemName);
        tvHint = holder.getView(R.id.tvItemHint);
        ImageView ivCursor = holder.getView(R.id.ivItemCursor);

        ((AnimationDrawable) ivCursor.getDrawable()).start();
        if (type == 0) {
            tvName.setText(DFUtils.getNum4(mDataList.get(position).getDiscount()) + getRstr(R.string.discounts));
            tvName.setVisibility(View.VISIBLE);
            tvHint.setVisibility(View.GONE);
            ivCursor.setVisibility(View.GONE);
            tvName.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.red));
            if (mDataList.get(position).isSelect()) {
                lin.setBackgroundResource(R.drawable.shape_red_tm_5);
            } else {
                lin.setBackgroundResource(R.drawable.shape_red_kuang_5);
            }
        } else {
            tvName.setText(DFUtils.getNum4(mDataList.get(position).getDiscount()));
            if (mDataList.get(position).getDiscount() > 0) {
                tvName.setVisibility(View.VISIBLE);
                tvHint.setVisibility(View.GONE);
            } else {
                tvName.setVisibility(View.GONE);
                tvHint.setVisibility(View.VISIBLE);
            }
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
            if (mDataList.get(position).isSelect()) {
                lin.setBackgroundResource(R.drawable.shape_green_kuang_5);
                ivCursor.setVisibility(View.VISIBLE);
            } else {
                lin.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                ivCursor.setVisibility(View.GONE);
            }
        }
    }
}
