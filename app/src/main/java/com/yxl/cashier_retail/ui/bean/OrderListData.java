package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:
 * Created by jingang on 2024/5/21
 */
public class OrderListData implements Serializable {

    /**
     * msg : 查询成功！
     * address : [{"payMoney":19.84,"retListTotal":0,"pay_method":1,"sale_list_state":3},{"payMoney":4,"retListTotal":0,"pay_method":2,"sale_list_state":3},{"payMoney":78.25,"retListTotal":0,"pay_method":5,"sale_list_state":3},{"payMoney":0.1,"retListTotal":0.13,"pay_method":13,"sale_list_state":3},{"payMoney":1000,"pay_method":"-1"},{"payMoney":65,"pay_method":"-2"},{"payMoney":1087.69,"pay_method":"-3"},{"beansUse":0,"count":0,"purSum":0,"pay_method":"-4"}]
     * data : [{"sale_list_datetime":"2024-05-05 08:19:41","sale_list_address":"","sale_list_unique_str":"1714868381339","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714868381339,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"金圈平台","sale_list_remarks":"","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"0.02","saleListPayment":13,"saleListPur":"34.00","returnType":1},{"sale_list_datetime":"2024-05-05 09:03:01","sale_list_address":"","sale_list_unique_str":"1714870979702686","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714870979702686,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"支付宝","sale_list_remarks":"2","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"1.00","saleListPayment":2,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 09:15:09","sale_list_address":"","sale_list_unique_str":"1714871683679386","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714871683679386,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 09:20:13","sale_list_address":"","sale_list_unique_str":"1714870999269086","refundState":"3","refundCount":0.03,"sale_list_totalCount":"1","sale_list_unique":1714870999269086,"saleListCode":3,"refundMoney":0.03,"sale_list_state":"已付款","payMent":"免密支付","sale_list_remarks":"9","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"1.00","saleListPayment":9,"saleListPur":"0.85","returnType":2},{"sale_list_datetime":"2024-05-05 10:15:34","sale_list_address":"","sale_list_unique_str":"1714875310516686","refundState":"3","refundCount":0.02,"sale_list_totalCount":"1","sale_list_unique":1714875310516686,"saleListCode":3,"refundMoney":0.02,"sale_list_state":"已付款","payMent":"免密支付","sale_list_remarks":"9","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"1.00","saleListPayment":9,"saleListPur":"0.85","returnType":2},{"sale_list_datetime":"2024-05-05 11:12:23","sale_list_address":"","sale_list_unique_str":"1714878444319686","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714878444319686,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 11:13:38","sale_list_address":"","sale_list_unique_str":"1714878771072586","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714878771072586,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"6.00","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 11:13:51","sale_list_address":"","sale_list_unique_str":"1714878827617886","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714878827617886,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 11:14:05","sale_list_address":"","sale_list_unique_str":"1714878843049386","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714878843049386,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 11:16:25","sale_list_address":"","sale_list_unique_str":"1714878872631686","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714878872631686,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 11:19:42","sale_list_address":"","sale_list_unique_str":"1714879150002586","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714879150002586,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 11:19:54","sale_list_address":"","sale_list_unique_str":"1714879191808486","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714879191808486,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 11:20:13","sale_list_address":"","sale_list_unique_str":"1714879208750086","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714879208750086,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 11:33:42","sale_list_address":"","sale_list_unique_str":"1714880008621086","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714880008621086,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"1.90","saleListPayment":5,"saleListPur":"1.70","returnType":1},{"sale_list_datetime":"2024-05-05 11:33:57","sale_list_address":"","sale_list_unique_str":"1714880027870586","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714880027870586,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 11:55:33","sale_list_address":"","sale_list_unique_str":"1714881270157186","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714881270157186,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 11:56:14","sale_list_address":"","sale_list_unique_str":"1714881365085686","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714881365085686,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 14:28:13","sale_list_address":"","sale_list_unique_str":"1714890482540586","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714890482540586,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 14:28:49","sale_list_address":"","sale_list_unique_str":"1714890509444586","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714890509444586,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 14:37:56","sale_list_address":"","sale_list_unique_str":"1714890947643586","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714890947643586,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 14:43:14","sale_list_address":"","sale_list_unique_str":"1714891380841486","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714891380841486,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 14:44:45","sale_list_address":"","sale_list_unique_str":"1714891470971086","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714891470971086,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 14:47:54","sale_list_address":"","sale_list_unique_str":"1714891664891286","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714891664891286,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 14:49:54","sale_list_address":"","sale_list_unique_str":"1714891783827186","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714891783827186,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 14:50:41","sale_list_address":"","sale_list_unique_str":"1714891831738386","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714891831738386,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 14:51:00","sale_list_address":"","sale_list_unique_str":"1714891849370386","refundState":"-1","refundCount":0,"sale_list_totalCount":"2","sale_list_unique":1714891849370386,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"2.85","saleListPayment":5,"saleListPur":"2.55","returnType":1},{"sale_list_datetime":"2024-05-05 14:51:21","sale_list_address":"","sale_list_unique_str":"1714891866186886","refundState":"-1","refundCount":0,"sale_list_totalCount":"2","sale_list_unique":1714891866186886,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"2.85","saleListPayment":5,"saleListPur":"2.55","returnType":1},{"sale_list_datetime":"2024-05-05 14:52:04","sale_list_address":"","sale_list_unique_str":"1714891905074486","refundState":"-1","refundCount":0,"sale_list_totalCount":"2","sale_list_unique":1714891905074486,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"5.70","saleListPayment":5,"saleListPur":"5.10","returnType":1},{"sale_list_datetime":"2024-05-05 14:52:23","sale_list_address":"","sale_list_unique_str":"1714891932763586","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714891932763586,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 14:52:36","sale_list_address":"","sale_list_unique_str":"1714891951138586","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714891951138586,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"1.90","saleListPayment":5,"saleListPur":"1.70","returnType":1},{"sale_list_datetime":"2024-05-05 14:53:47","sale_list_address":"","sale_list_unique_str":"1714891988356586","refundState":"3","refundCount":0.02,"sale_list_totalCount":"1","sale_list_unique":1714891988356586,"saleListCode":3,"refundMoney":0.02,"sale_list_state":"已付款","payMent":"免密支付","sale_list_remarks":"9","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"0.95","saleListPayment":9,"saleListPur":"0.85","returnType":2},{"sale_list_datetime":"2024-05-05 14:56:09","sale_list_address":"","sale_list_unique_str":"1714892149361186","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714892149361186,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"免密支付","sale_list_remarks":"9","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"0.95","saleListPayment":9,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 15:02:05","sale_list_address":"","sale_list_unique_str":"1714892468298786","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714892468298786,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"免密支付","sale_list_remarks":"9","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"0.95","saleListPayment":9,"saleListPur":"7.65","returnType":1},{"sale_list_datetime":"2024-05-05 15:03:12","sale_list_address":"","sale_list_unique_str":"1714892533505886","refundState":"-1","refundCount":0,"sale_list_totalCount":"2","sale_list_unique":1714892533505886,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"免密支付","sale_list_remarks":"9","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"0.95","saleListPayment":9,"saleListPur":"7.65","returnType":1},{"sale_list_datetime":"2024-05-05 15:16:04","sale_list_address":"","sale_list_unique_str":"1714892983290086","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714892983290086,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 15:17:42","sale_list_address":"","sale_list_unique_str":"1714893370747686","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714893370747686,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 15:18:13","sale_list_address":"","sale_list_unique_str":"1714893488315486","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714893488315486,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"1.90","saleListPayment":5,"saleListPur":"1.70","returnType":1},{"sale_list_datetime":"2024-05-05 15:21:24","sale_list_address":"","sale_list_unique_str":"1714893612201386","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714893612201386,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 15:21:45","sale_list_address":"","sale_list_unique_str":"1714893699818886","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714893699818886,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 15:23:58","sale_list_address":"","sale_list_unique_str":"1714893832987286","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714893832987286,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 15:27:00","sale_list_address":"","sale_list_unique_str":"1714893869441086","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714893869441086,"saleListCode":4,"refundMoney":0,"sale_list_state":"赊账","payMent":"现金","sale_list_remarks":"1","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"1000.00","saleListPayment":1,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 15:36:05","sale_list_address":"","sale_list_unique_str":"1714894558232486","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714894558232486,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 15:39:29","sale_list_address":"","sale_list_unique_str":"1714894757559686","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714894757559686,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 15:53:01","sale_list_address":"","sale_list_unique_str":"1714895577703486","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714895577703486,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"现金","sale_list_remarks":"1","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"1.00","saleListPayment":1,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 16:04:06","sale_list_address":"","sale_list_unique_str":"1714896244101886","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714896244101886,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"现金","sale_list_remarks":"1","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"1.00","saleListPayment":1,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 16:48:06","sale_list_address":"","sale_list_unique_str":"1714898873003786","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714898873003786,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 17:02:05","sale_list_address":"","sale_list_unique_str":"1714899725100","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714899725100,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"混合支付","sale_list_remarks":"","sale_list_name":"我上去就是一刀","sale_list_phone":"13054908225","cus_name":"","sale_list_total":"0.02","saleListPayment":8,"saleListPur":"0.02","returnType":1},{"sale_list_datetime":"2024-05-05 17:03:02","sale_list_address":"","sale_list_unique_str":"1714899776987986","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714899776987986,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 17:06:00","sale_list_address":"","sale_list_unique_str":"1714899947722786","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714899947722786,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 17:08:05","sale_list_address":"","sale_list_unique_str":"1714900074010986","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714900074010986,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 17:09:06","sale_list_address":"","sale_list_unique_str":"1714900140387386","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714900140387386,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 17:19:21","sale_list_address":"","sale_list_unique_str":"1714900756097186","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714900756097186,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 17:23:35","sale_list_address":"","sale_list_unique_str":"1714900998204186","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714900998204186,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 17:24:21","sale_list_address":"","sale_list_unique_str":"1714901056905786","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714901056905786,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 17:27:45","sale_list_address":"","sale_list_unique_str":"1714901254705586","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714901254705586,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 17:29:54","sale_list_address":"","sale_list_unique_str":"1714901360529186","refundState":"-1","refundCount":0,"sale_list_totalCount":"2","sale_list_unique":1714901360529186,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"免密支付","sale_list_remarks":"9","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"9.00","saleListPayment":9,"saleListPur":"7.65","returnType":1},{"sale_list_datetime":"2024-05-05 17:31:01","sale_list_address":"","sale_list_unique_str":"1714901428356086","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714901428356086,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"1","sale_list_phone":"15065358459","cus_name":"1","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 17:31:10","sale_list_address":"","sale_list_unique_str":"1714901466225386","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714901466225386,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"1","sale_list_phone":"15065358459","cus_name":"1","sale_list_total":"3.80","saleListPayment":5,"saleListPur":"3.40","returnType":1},{"sale_list_datetime":"2024-05-05 17:31:59","sale_list_address":"","sale_list_unique_str":"1714901481664586","refundState":"3","refundCount":0.02,"sale_list_totalCount":"1","sale_list_unique":1714901481664586,"saleListCode":3,"refundMoney":0.02,"sale_list_state":"已付款","payMent":"免密支付","sale_list_remarks":"9","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"1.00","saleListPayment":9,"saleListPur":"0.85","returnType":2},{"sale_list_datetime":"2024-05-05 17:33:14","sale_list_address":"","sale_list_unique_str":"1714901565681386","refundState":"3","refundCount":0.02,"sale_list_totalCount":"1","sale_list_unique":1714901565681386,"saleListCode":3,"refundMoney":0.02,"sale_list_state":"已付款","payMent":"免密支付","sale_list_remarks":"9","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"1.00","saleListPayment":9,"saleListPur":"0.85","returnType":2},{"sale_list_datetime":"2024-05-05 17:33:34","sale_list_address":"","sale_list_unique_str":"1714901603169386","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714901603169386,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 17:39:13","sale_list_address":"","sale_list_unique_str":"1714901944936686","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714901944936686,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"储值卡","sale_list_remarks":"5","sale_list_name":"测试会员0010","sale_list_phone":"10010","cus_name":"测试会员0010","sale_list_total":"0.95","saleListPayment":5,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 17:42:11","sale_list_address":"","sale_list_unique_str":"1714902097064086","refundState":"3","refundCount":0.02,"sale_list_totalCount":"1","sale_list_unique":1714902097064086,"saleListCode":3,"refundMoney":0.02,"sale_list_state":"已付款","payMent":"免密支付","sale_list_remarks":"9","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"1.00","saleListPayment":9,"saleListPur":"0.85","returnType":2},{"sale_list_datetime":"2024-05-05 17:50:10","sale_list_address":"","sale_list_unique_str":"1714902606288286","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714902606288286,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"支付宝","sale_list_remarks":"2","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"1.00","saleListPayment":2,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 17:52:33","sale_list_address":"","sale_list_unique_str":"1714902710848286","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714902710848286,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"支付宝","sale_list_remarks":"2","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"1.00","saleListPayment":2,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 17:56:05","sale_list_address":"","sale_list_unique_str":"1714902962288086","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714902962288086,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"支付宝","sale_list_remarks":"2","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"1.00","saleListPayment":2,"saleListPur":"0.85","returnType":1},{"sale_list_datetime":"2024-05-05 18:00:12","sale_list_address":"","sale_list_unique_str":"1714903205862986","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1714903205862986,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"现金","sale_list_remarks":"1","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"1.00","saleListPayment":1,"saleListPur":"0.85","returnType":1}]
     * data1 : [{"rechargeCode":1,"rechargeMethod":"现金","rechargeMoney":64}]
     * status : 0
     */

    private String msg;
    private int status;
    private List<AddressBean> address;
    private List<DataBean> data;
    private List<Data1Bean> data1;

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public List<AddressBean> getAddress() {
        return address;
    }

    public void setAddress(List<AddressBean> address) {
        this.address = address;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public List<Data1Bean> getData1() {
        return data1;
    }

    public void setData1(List<Data1Bean> data1) {
        this.data1 = data1;
    }

    public static class AddressBean {
        /**
         * payMoney : 19.84
         * retListTotal : 0.0
         * pay_method : 1
         * sale_list_state : 3
         * beansUse : 0.0
         * count : 0
         * purSum : 0.0
         */

        private double payMoney;
        private double retListTotal;
        private int pay_method;
        private int sale_list_state;
        private double beansUse;
        private double count;
        private double purSum;

        public double getPayMoney() {
            return payMoney;
        }

        public void setPayMoney(double payMoney) {
            this.payMoney = payMoney;
        }

        public double getRetListTotal() {
            return retListTotal;
        }

        public void setRetListTotal(double retListTotal) {
            this.retListTotal = retListTotal;
        }

        public int getPay_method() {
            return pay_method;
        }

        public void setPay_method(int pay_method) {
            this.pay_method = pay_method;
        }

        public int getSale_list_state() {
            return sale_list_state;
        }

        public void setSale_list_state(int sale_list_state) {
            this.sale_list_state = sale_list_state;
        }

        public double getBeansUse() {
            return beansUse;
        }

        public void setBeansUse(double beansUse) {
            this.beansUse = beansUse;
        }

        public double getCount() {
            return count;
        }

        public void setCount(double count) {
            this.count = count;
        }

        public double getPurSum() {
            return purSum;
        }

        public void setPurSum(double purSum) {
            this.purSum = purSum;
        }
    }

    public static class DataBean {
        /**
         * sale_list_datetime : 2024-05-05 08:19:41
         * sale_list_address :
         * sale_list_unique_str : 1714868381339
         * refundState : -1
         * refundCount : 0.0
         * sale_list_totalCount : 1
         * sale_list_unique : 1714868381339
         * saleListCode : 3
         * refundMoney : 0.0
         * sale_list_state : 已付款
         * payMent : 金圈平台
         * sale_list_remarks :
         * sale_list_name : 本地订单
         * sale_list_phone :
         * cus_name :
         * sale_list_total : 0.02
         * saleListPayment : 13
         * saleListPur : 34.00
         * returnType : 1
         */
        private boolean select;
        private String sale_list_datetime;
        private String sale_list_address;
        private String sale_list_unique_str;
        private String refundState;
        private double refundCount;
        private String sale_list_totalCount;
        private String sale_list_unique;
        private int saleListCode;
        private double refundMoney;
        private String sale_list_state;
        private String payMent;
        private String sale_list_remarks;
        private String sale_list_name;
        private String sale_list_phone;
        private String cus_name;
        private String sale_list_total;
        private int saleListPayment;
        private String saleListPur;
        private int returnType;

        public boolean isSelect() {
            return select;
        }

        public void setSelect(boolean select) {
            this.select = select;
        }

        public String getSale_list_datetime() {
            return sale_list_datetime;
        }

        public void setSale_list_datetime(String sale_list_datetime) {
            this.sale_list_datetime = sale_list_datetime;
        }

        public String getSale_list_address() {
            return sale_list_address;
        }

        public void setSale_list_address(String sale_list_address) {
            this.sale_list_address = sale_list_address;
        }

        public String getSale_list_unique_str() {
            return sale_list_unique_str;
        }

        public void setSale_list_unique_str(String sale_list_unique_str) {
            this.sale_list_unique_str = sale_list_unique_str;
        }

        public String getRefundState() {
            return refundState;
        }

        public void setRefundState(String refundState) {
            this.refundState = refundState;
        }

        public double getRefundCount() {
            return refundCount;
        }

        public void setRefundCount(double refundCount) {
            this.refundCount = refundCount;
        }

        public String getSale_list_totalCount() {
            return sale_list_totalCount;
        }

        public void setSale_list_totalCount(String sale_list_totalCount) {
            this.sale_list_totalCount = sale_list_totalCount;
        }

        public String getSale_list_unique() {
            return sale_list_unique;
        }

        public void setSale_list_unique(String sale_list_unique) {
            this.sale_list_unique = sale_list_unique;
        }

        public int getSaleListCode() {
            return saleListCode;
        }

        public void setSaleListCode(int saleListCode) {
            this.saleListCode = saleListCode;
        }

        public double getRefundMoney() {
            return refundMoney;
        }

        public void setRefundMoney(double refundMoney) {
            this.refundMoney = refundMoney;
        }

        public String getSale_list_state() {
            return sale_list_state;
        }

        public void setSale_list_state(String sale_list_state) {
            this.sale_list_state = sale_list_state;
        }

        public String getPayMent() {
            return payMent;
        }

        public void setPayMent(String payMent) {
            this.payMent = payMent;
        }

        public String getSale_list_remarks() {
            return sale_list_remarks;
        }

        public void setSale_list_remarks(String sale_list_remarks) {
            this.sale_list_remarks = sale_list_remarks;
        }

        public String getSale_list_name() {
            return sale_list_name;
        }

        public void setSale_list_name(String sale_list_name) {
            this.sale_list_name = sale_list_name;
        }

        public String getSale_list_phone() {
            return sale_list_phone;
        }

        public void setSale_list_phone(String sale_list_phone) {
            this.sale_list_phone = sale_list_phone;
        }

        public String getCus_name() {
            return cus_name;
        }

        public void setCus_name(String cus_name) {
            this.cus_name = cus_name;
        }

        public String getSale_list_total() {
            return sale_list_total;
        }

        public void setSale_list_total(String sale_list_total) {
            this.sale_list_total = sale_list_total;
        }

        public int getSaleListPayment() {
            return saleListPayment;
        }

        public void setSaleListPayment(int saleListPayment) {
            this.saleListPayment = saleListPayment;
        }

        public String getSaleListPur() {
            return saleListPur;
        }

        public void setSaleListPur(String saleListPur) {
            this.saleListPur = saleListPur;
        }

        public int getReturnType() {
            return returnType;
        }

        public void setReturnType(int returnType) {
            this.returnType = returnType;
        }
    }

    public static class Data1Bean {
        /**
         * rechargeCode : 1
         * rechargeMethod : 现金
         * rechargeMoney : 64.0
         */

        private int rechargeCode;
        private String rechargeMethod;
        private double rechargeMoney;

        public int getRechargeCode() {
            return rechargeCode;
        }

        public void setRechargeCode(int rechargeCode) {
            this.rechargeCode = rechargeCode;
        }

        public String getRechargeMethod() {
            return rechargeMethod;
        }

        public void setRechargeMethod(String rechargeMethod) {
            this.rechargeMethod = rechargeMethod;
        }

        public double getRechargeMoney() {
            return rechargeMoney;
        }

        public void setRechargeMoney(double rechargeMoney) {
            this.rechargeMoney = rechargeMoney;
        }
    }
}
