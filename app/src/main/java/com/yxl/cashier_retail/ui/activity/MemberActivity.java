package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.graphics.Color;
import android.graphics.Typeface;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.SPUtils;
import com.github.mikephil.charting.animation.Easing;
import com.github.mikephil.charting.components.Legend;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.data.PieData;
import com.github.mikephil.charting.data.PieDataSet;
import com.github.mikephil.charting.data.PieEntry;
import com.github.mikephil.charting.formatter.IndexAxisValueFormatter;
import com.github.mikephil.charting.utils.ColorTemplate;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.MyApplication;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityMemberBinding;
import com.yxl.cashier_retail.ui.adapter.MemberAdapter;
import com.yxl.cashier_retail.ui.bean.ConditionData;
import com.yxl.cashier_retail.ui.bean.MemberData;
import com.yxl.cashier_retail.ui.bean.MemberLevelData;
import com.yxl.cashier_retail.ui.bean.MemberStatisticsData;
import com.yxl.cashier_retail.ui.dialog.MemberAddDialog;
import com.yxl.cashier_retail.ui.dialog.MemberLevelDialog;
import com.yxl.cashier_retail.ui.dialog.MemberPointsDialog;
import com.yxl.cashier_retail.ui.dialog.MemberPointsRuleDialog;
import com.yxl.cashier_retail.ui.dialog.MemberRechargeDialog;
import com.yxl.cashier_retail.ui.dialog.MemberRecordDialog;
import com.yxl.cashier_retail.ui.dialog.MemberRefundDialog;
import com.yxl.cashier_retail.ui.dialog.MemberRefundTipsDialog;
import com.yxl.cashier_retail.ui.dialog.MenuDialog;
import com.yxl.cashier_retail.ui.popupwindow.ConditionPop;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:会员管理
 * Created by jingang on 2024/5/16
 */
@SuppressLint({"NonConstantResourceId", "NotifyDataSetChanged", "SetTextI18n"})
public class MemberActivity extends BaseActivity<ActivityMemberBinding> implements View.OnClickListener {
    private int index,//0.全部 3.赊欠 2.普通 1.储值
            currentIndex;
    private String keyWords,//搜索关键字
            unique,//会员编号
            mobile,//手机
            name,//名称
            remarks;//备注
    private int cusId,//会员id
            pos,//列表下标
            levelId,//等级id
            sex;//性别：0.保密 1.男 2.女
    private double balance,//余额
            points,//积分
            debtLimit;//欠款限额

    //会员列表
    private MemberAdapter mAdapter;
    private List<MemberData> dataList = new ArrayList<>();
    private MemberData memberData;

    //会员性别
    private List<ConditionData> conditionList = new ArrayList<>();

    //会员等级
    private List<MemberLevelData> levelList = new ArrayList<>();

    @Override
    protected ActivityMemberBinding getViewBinding() {
        return ActivityMemberBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        getPointsRule();
        mBinding.ivLogo.setOnClickListener(this);
        mBinding.ivSearchClear.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.tvType0.setOnClickListener(this);
        mBinding.tvType1.setOnClickListener(this);
        mBinding.tvType2.setOnClickListener(this);
        mBinding.tvType3.setOnClickListener(this);

        mBinding.ivMobileClear.setOnClickListener(this);
        mBinding.ivNameClear.setOnClickListener(this);
        mBinding.tvRecord.setOnClickListener(this);
        mBinding.tvRefund.setOnClickListener(this);
        mBinding.tvRecharge.setOnClickListener(this);
        mBinding.tvExchange.setOnClickListener(this);
        mBinding.tvPointsAdd.setOnClickListener(this);
        mBinding.tvPointsSub.setOnClickListener(this);
        mBinding.linSex.setOnClickListener(this);
        mBinding.ivDebtLimitClear.setOnClickListener(this);
        mBinding.ivRemarksClear.setOnClickListener(this);
        mBinding.linConfirm.setOnClickListener(this);

        mBinding.relAdd.setOnClickListener(this);
        mBinding.relLevel.setOnClickListener(this);
        mBinding.relPoints.setOnClickListener(this);
        mBinding.etSearch.setOnEditorActionListener((v, actionId, event) -> {
            page = 1;
            getMemberList();
            return true;
        });
        mBinding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                mBinding.ivSearchClear.setVisibility(TextUtils.isEmpty(keyWords) ? View.GONE : View.VISIBLE);
            }
        });
        setChartType();
        setChartStored();
        setChartPoints();
        setChartLevel();
        setChartTime();
        setAdapter();
    }

    @Override
    protected void initData() {
        setNetwork();
        conditionList.clear();
        conditionList.add(new ConditionData(0, getRstr(R.string.sex0), false));
        conditionList.add(new ConditionData(1, getRstr(R.string.sex1), false));
        conditionList.add(new ConditionData(2, getRstr(R.string.sex2), false));
        getMemberStatistics();
        getMemberToShopTime();
        getMemberList();
        getMemberLevel();
    }

    @Override
    public void getNetwork() {
        setNetwork();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivLogo:
                //菜单
                MenuDialog.showDialog(this, type -> {
                    //0.收银 1.入库 2.查询 3.统计 4.网单 5.会员 6.补货 7.营销 8.交班 9.设置
                    if (type != 5) {
                        switch (type) {
                            case 1:
                                goToActivity(InActivity.class);
                                break;
                            case 2:
                                goToActivity(QueryActivity.class);
                                break;
                            case 3:
                                goToActivity(StatisticsActivity.class);
                                break;
                            case 4:
                                goToActivity(OrderActivity.class);
                                break;
                            case 6:
                                goToActivity(MallActivity.class);
                                break;
                            case 7:
                                goToActivity(MarketingActivity.class);
                                break;
                            case 8:
                                goToActivity(ShiftActivity.class);
                                break;
                            case 9:
                                goToActivity(SettingActivity.class);
                                break;
                        }
                        finish();
                    }
                });
                break;
            case R.id.ivSearchClear:
                //清除搜素输入
                mBinding.etSearch.setText("");
                keyWords = "";
                page = 1;
                getMemberList();
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.tvType0:
                //全部
                index = 0;
                fragmentControl();
                break;
            case R.id.tvType1:
                //赊欠会员
                index = 3;
                fragmentControl();
                break;
            case R.id.tvType2:
                //储值会员
                index = 1;
                fragmentControl();
                break;
            case R.id.tvType3:
                //普通会员
                index = 2;
                fragmentControl();
                break;
            case R.id.ivMobileClear:
                //清除会员手机输入
                mBinding.etMobile.setText("");
                showSoftInput(mBinding.etMobile);
                break;
            case R.id.ivNameClear:
                //清除会员名称输入0
                mBinding.etName.setText("");
                showSoftInput(mBinding.etName);
                break;
            case R.id.tvRecord:
                //消费记录
                MemberRecordDialog.showDialog(this, cusId);
                break;
            case R.id.tvRefund:
                //退费
                MemberRefundTipsDialog.showDialog(this, () -> {
                    MemberRefundDialog.showDialog(this, unique, balance, money -> {
                        balance = balance - money;
                        mBinding.tvBalance.setText(DFUtils.getNum2(balance));
                        if (dataList.size() > pos) {
                            dataList.get(pos).setTotalBalance(balance);
                            mAdapter.notifyItemChanged(pos);
                        }
                    });
                });
                break;
            case R.id.tvRecharge:
                //充值
                MemberRechargeDialog.showDialog(this, unique, 0, money -> {
                    balance = balance + money;
                    mBinding.tvBalance.setText(DFUtils.getNum2(balance));
                    if (dataList.size() > pos) {
                        dataList.get(pos).setTotalBalance(balance);
                        mAdapter.notifyItemChanged(pos);
                    }
                });
                break;
            case R.id.tvExchange:
                //兑换
                showDialogPoints(0);
                break;
            case R.id.tvPointsAdd:
                //增加积分
                showDialogPoints(1);
                break;
            case R.id.tvPointsSub:
                //减少积分
                showDialogPoints(2);
                break;
            case R.id.linSex:
                //选择性别
                ConditionPop.showDialog(mContext,
                        mBinding.ivSex,
                        v,
                        mBinding.linSex.getMeasuredWidth(),
                        conditionList,
                        sex,
                        data -> {
                            sex = data.getId();
                            mBinding.tvSex.setText(data.getName());
                        });
                break;
            case R.id.ivDebtLimitClear:
                //清除欠款限额输入
                mBinding.etDebtLimit.setText("");
                showSoftInput(mBinding.etDebtLimit);
                break;
            case R.id.ivRemarksClear:
                //请输入备注输入
                mBinding.etRemarks.setText("");
                showSoftInput(mBinding.etRemarks);
                break;
            case R.id.linConfirm:
                //保存
                mobile = mBinding.etMobile.getText().toString().trim();
                name = mBinding.etName.getText().toString().trim();
                remarks = mBinding.etRemarks.getText().toString().trim();
                debtLimit = TextUtils.isEmpty(mBinding.etDebtLimit.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etDebtLimit.getText().toString().trim());
                if (TextUtils.isEmpty(mobile)) {
                    showToast(1, getRstr(R.string.input_member_mobile));
                    return;
                }
                if (TextUtils.isEmpty(name)) {
                    showToast(1, getRstr(R.string.input_member_name));
                    return;
                }
                postMemberEdit();
                break;
            case R.id.relAdd:
                //新增会员
                MemberAddDialog.showDialog(this, () -> {
                    page = 1;
                    getMemberList();
                });
                break;
            case R.id.relLevel:
                //等级管理
                MemberLevelDialog.showDialog(this, this::getMemberLevel);
                break;
            case R.id.relPoints:
                //积分规则
                MemberPointsRuleDialog.showDialog(this, this::getPointsRule);
                break;
        }
    }

    /**
     * 网络监听
     */
    private void setNetwork() {
        if (MyApplication.mNetwork != null) {
            if (MyApplication.mNetwork.isConnect()) {
                switch (MyApplication.mNetwork.getConnectType()) {
                    case 1:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network001);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 2:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network002);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 3:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network003);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    default:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                        mBinding.tvNetwork.setText(getRstr(R.string.no_network));
                        break;
                }
            } else {
                mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                mBinding.tvNetwork.setText(getRstr(R.string.no_network));
            }
        } else {
            mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
            mBinding.tvNetwork.setText(getRstr(R.string.no_network));
        }
    }

    /**
     * 控制fragment的变化
     */
    public void fragmentControl() {
        if (currentIndex != index) {
            removeBottomColor();
            setBottomColor();
            currentIndex = index;
            page = 1;
            getMemberList();
        }
    }

    /**
     * 设置底部栏按钮变色
     */
    private void setBottomColor() {
        switch (index) {
            case 0:
                mBinding.tvType0.setBackgroundResource(R.drawable.shape_green_left_5);
                mBinding.tvType0.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvType0.setTypeface(Typeface.DEFAULT_BOLD);
                break;
            case 3:
                mBinding.tvType1.setBackgroundColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                mBinding.tvType1.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvType1.setTypeface(Typeface.DEFAULT_BOLD);
                break;
            case 1:
                mBinding.tvType2.setBackgroundColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                mBinding.tvType2.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvType2.setTypeface(Typeface.DEFAULT_BOLD);
                break;
            case 2:
                mBinding.tvType3.setBackgroundResource(R.drawable.shape_green_right_5);
                mBinding.tvType3.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvType3.setTypeface(Typeface.DEFAULT_BOLD);
                break;
            default:
                break;
        }
    }

    /**
     * 清除底部栏颜色
     */
    private void removeBottomColor() {
        switch (currentIndex) {
            case 0:
                mBinding.tvType0.setBackgroundResource(R.drawable.shape_white_left_5);
                mBinding.tvType0.setTextColor(getResources().getColor(R.color.black));
                mBinding.tvType0.setTypeface(Typeface.DEFAULT);
                break;
            case 3:
                mBinding.tvType1.setBackgroundColor(getResources().getColor(R.color.white));
                mBinding.tvType1.setTextColor(getResources().getColor(R.color.black));
                mBinding.tvType1.setTypeface(Typeface.DEFAULT);
                break;
            case 1:
                mBinding.tvType2.setBackgroundColor(getResources().getColor(R.color.white));
                mBinding.tvType2.setTextColor(getResources().getColor(R.color.black));
                mBinding.tvType2.setTypeface(Typeface.DEFAULT);
                break;
            case 2:
                mBinding.tvType3.setBackgroundResource(R.drawable.shape_white_right_5);
                mBinding.tvType3.setTextColor(getResources().getColor(R.color.black));
                mBinding.tvType3.setTypeface(Typeface.DEFAULT);
                break;
            default:
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new MemberAdapter(this);
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (dataList.get(position).isSelect()) {
                dataList.get(position).setSelect(false);
                mAdapter.notifyItemChanged(position);
                memberData = null;
            } else {
                for (int i = 0; i < dataList.size(); i++) {
                    if (dataList.get(i).isSelect()) {
                        dataList.get(i).setSelect(false);
                        mAdapter.notifyItemChanged(i);
                    }
                }
                dataList.get(position).setSelect(true);
                mAdapter.notifyItemChanged(position);
                pos = position;
                memberData = dataList.get(position);
            }
            setUIInfo();
        });
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getMemberList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getMemberList();
            }
        });
    }

    /**
     * 更新UI（会员详情）
     */
    private void setUIInfo() {
        if (memberData == null) {
            mBinding.tvTitle.setText(getRstr(R.string.member_manage));
            mBinding.linStatistic.setVisibility(View.VISIBLE);
            mBinding.linInfo.setVisibility(View.GONE);
            return;
        }
        mBinding.tvTitle.setText(getRstr(R.string.member_info));
        mBinding.linStatistic.setVisibility(View.GONE);
        mBinding.linInfo.setVisibility(View.VISIBLE);

        cusId = memberData.getCusId();
        unique = memberData.getCusUnique();
        mobile = memberData.getCusPhone();
        name = memberData.getCusName();
        levelId = memberData.getCusLevelId();
        sex = memberData.getCusSex();
        remarks = TextUtils.isEmpty(memberData.getCusRemark()) ? "" : memberData.getCusRemark();
        balance = memberData.getTotalBalance();
        points = memberData.getCusPoints();
        debtLimit = memberData.getCreditLimit();

        mBinding.tvUnique.setText(getRstr(R.string.no_colon) + memberData.getCusUnique());
        mBinding.etMobile.setText(mobile);
        mBinding.etName.setText(name);
        mBinding.tvBalance.setText(DFUtils.getNum2(balance));
        mBinding.tvPoints.setText(DFUtils.getNum2(points));
        mBinding.tvLevel.setText(TextUtils.isEmpty(memberData.getCusLevelName()) ? "" : memberData.getCusLevelName());
        switch (sex) {
            case 1:
                mBinding.tvSex.setText(getRstr(R.string.sex1));
                break;
            case 2:
                mBinding.tvSex.setText(getRstr(R.string.sex2));
                break;
            default:
                mBinding.tvSex.setText(getRstr(R.string.sex0));
                break;
        }
        mBinding.etDebtLimit.setText(DFUtils.getNum2(debtLimit));
        mBinding.etRemarks.setText(remarks);
    }

    /**
     * dialog（积分变动）
     *
     * @param type 0.兑换 1.增加 2.减少
     */
    private void showDialogPoints(int type) {
        MemberPointsDialog.showDialog(this, type, cusId, points, points -> {
            this.points = this.points + points;
            mBinding.tvPoints.setText(DFUtils.getNum2(this.points));
            if (dataList.size() > pos) {
                dataList.get(pos).setCusPoints(dataList.get(pos).getCusPoints() + points);
                mAdapter.notifyItemChanged(pos);
            }
        });
    }

    /**
     * 更新UI（店内会员分析）
     */
    private void setUIStatistics(MemberStatisticsData data) {
        //会员总数、储值金额、赊欠金额
        mBinding.tvTotalCount.setText(String.valueOf(data.getCustomerNumber()));
        mBinding.tvTotalStored.setText(DFUtils.getNum2(data.getStoredMoney()));
        mBinding.tvTotalCredit.setText(DFUtils.getNum2(data.getCreditMoney()));

        //会员类型占比
        if (data.getCustomerType() != null) {
            try {
                JSONObject object = new JSONObject(data.getCustomerType());
                List<PieEntry> list = new ArrayList<>();
                list.add(new PieEntry(object.getInt("赊欠会员"), getRstr(R.string.member_credit)));
                list.add(new PieEntry(object.getInt("储值会员"), getRstr(R.string.member_stored)));
                list.add(new PieEntry(object.getInt("普通会员"), getRstr(R.string.member_common)));

                PieDataSet pieDataSet = new PieDataSet(list, "");
                //为每个分组设置颜色，如果不为每个都设置颜色最后饼图只会有一个颜色，这样无法有效看出占比
                pieDataSet.setColors(getResources().getColor(R.color.color_statistics1),
                        getResources().getColor(R.color.color_statistics3),
                        getResources().getColor(R.color.color_statistics0));
                pieDataSet.setSliceSpace(0f); //设置饼块与饼块之间的距离
                pieDataSet.setDrawValues(false);//是否绘制饼图上的文字
                PieData pieData = new PieData(pieDataSet);
                mBinding.chartType.setData(pieData);
                mBinding.chartType.invalidate();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        //会员类型占比
//        if (data.getCustomerType() != null) {
//            try {
//                List<PieEntry> list = new ArrayList<>();
//                for (int i = 0; i < data.getCustomerType().size(); i++) {
//                    list.add(new PieEntry(data.getCustomerType().get(i).getValue(), data.getCustomerType().get(i).getKey()));
//                }
//                PieDataSet pieDataSet = new PieDataSet(list, "");
//                //为每个分组设置颜色，如果不为每个都设置颜色最后饼图只会有一个颜色，这样无法有效看出占比
//                pieDataSet.setColors(getResources().getColor(R.color.color_statistics1),
//                        getResources().getColor(R.color.color_statistics3),
//                        getResources().getColor(R.color.color_statistics0));
//                pieDataSet.setSliceSpace(0f); //设置饼块与饼块之间的距离
//                pieDataSet.setDrawValues(false);//是否绘制饼图上的文字
//                PieData pieData = new PieData(pieDataSet);
//                mBinding.chartType.setData(pieData);
//                mBinding.chartType.invalidate();
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }

        //储值金额占比
        if (data.getCusBalance() != null) {
            try {
                JSONObject object = new JSONObject(data.getCusBalance());
                List<BarEntry> list = new ArrayList<>();
                list.add(new BarEntry(0, object.getInt("0-50")));
                list.add(new BarEntry(1, object.getInt("50-100")));
                list.add(new BarEntry(2, object.getInt("100-200")));
                list.add(new BarEntry(3, object.getInt("200-500")));
                list.add(new BarEntry(4, object.getInt("500以上")));

                BarDataSet barDataSet = new BarDataSet(list, "");
                BarData barData = new BarData(barDataSet);
                barData.setDrawValues(true);
                barData.setValueTextSize(6f);
                barData.setValueTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
                mBinding.chartStored.setData(barData);
                mBinding.chartStored.getXAxis().setLabelCount(list.size());
                mBinding.chartStored.invalidate();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        //储值金额占比
//        if (data.getCusBalance() != null) {
//            try {
//                List<BarEntry> list = new ArrayList<>();
//                for (int i = 0; i < data.getCusBalance().size(); i++) {
//                    list.add(new BarEntry(i, data.getCusBalance().get(i).getValue()));
//                }
//                BarDataSet barDataSet = new BarDataSet(list, "");
//                BarData barData = new BarData(barDataSet);
//                barData.setDrawValues(true);
//                barData.setValueTextSize(6f);
//                barData.setValueTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
//                mBinding.chartStored.setData(barData);
//                mBinding.chartStored.getXAxis().setLabelCount(list.size());
//                mBinding.chartStored.invalidate();
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }

        //会员积分占比
        if (data.getCusPoint() != null) {
            try {
                JSONObject object = new JSONObject(data.getCusPoint());
                List<PieEntry> list = new ArrayList<>();
                list.add(new PieEntry(object.getInt("500以上"), "500以上"));
                list.add(new PieEntry(object.getInt("200-500"), "200-500"));
                list.add(new PieEntry(object.getInt("100-200"), "100-200"));
                list.add(new PieEntry(object.getInt("0-100"), "0-100"));
                PieDataSet pieDataSet = new PieDataSet(list, "");
                //为每个分组设置颜色，如果不为每个都设置颜色最后饼图只会有一个颜色，这样无法有效看出占比
                pieDataSet.setColors(getResources().getColor(R.color.color_statistics1),
                        getResources().getColor(R.color.color_statistics3),
                        getResources().getColor(R.color.color_statistics0),
                        getResources().getColor(R.color.color_statistics2));
                pieDataSet.setSliceSpace(0f); //设置饼块与饼块之间的距离
                pieDataSet.setDrawValues(false);//是否绘制饼图上的文字
                PieData pieData = new PieData(pieDataSet);
                mBinding.chartPoints.setData(pieData);
                mBinding.chartPoints.invalidate();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        //会员积分占比
//        if (data.getCusPoint() != null) {
//            try {
//                List<PieEntry> list = new ArrayList<>();
//                for (int i = 0; i < data.getCusPoint().size(); i++) {
//                    list.add(new PieEntry(data.getCusPoint().get(i).getValue(), data.getCusPoint().get(i).getKey()));
//                }
//                PieDataSet pieDataSet = new PieDataSet(list, "");
//                //为每个分组设置颜色，如果不为每个都设置颜色最后饼图只会有一个颜色，这样无法有效看出占比
//                pieDataSet.setColors(getResources().getColor(R.color.color_statistics1),
//                        getResources().getColor(R.color.color_statistics3),
//                        getResources().getColor(R.color.color_statistics0),
//                        getResources().getColor(R.color.color_statistics2));
//                pieDataSet.setSliceSpace(0f); //设置饼块与饼块之间的距离
//                pieDataSet.setDrawValues(false);//是否绘制饼图上的文字
//                PieData pieData = new PieData(pieDataSet);
//                mBinding.chartPoints.setData(pieData);
//                mBinding.chartPoints.invalidate();
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }

        //会员等级占比
        if (data.getCustomerLevel() != null) {
            try {
                JSONObject object = new JSONObject(data.getCustomerLevel());
                List<PieEntry> list = new ArrayList<>();
                list.add(new PieEntry(object.getInt("钻石会员"), "钻石会员"));
                list.add(new PieEntry(object.getInt("黄金会员"), "黄金会员"));
                list.add(new PieEntry(object.getInt("白银会员"), "白银会员"));
                list.add(new PieEntry(object.getInt("铜牌会员"), "铜牌会员"));

                PieDataSet dataSet = new PieDataSet(list, "");
                dataSet.setColors(getResources().getColor(R.color.color_statistics2),
                        getResources().getColor(R.color.color_statistics0),
                        getResources().getColor(R.color.color_statistics3),
                        getResources().getColor(R.color.color_statistics1));
                dataSet.setSliceSpace(0f);
                dataSet.setSelectionShift(3f);
                dataSet.setColors(ColorTemplate.MATERIAL_COLORS);

                PieData pieData = new PieData(dataSet);
                pieData.setDrawValues(false);//不显示数值
                mBinding.chartLevel.setData(pieData);
                mBinding.chartLevel.invalidate();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
//        //会员等级占比
//        if (data.getCustomerLevel() != null) {
//            try {
//                List<PieEntry> list = new ArrayList<>();
//                for (int i = 0; i < data.getCustomerLevel().size(); i++) {
//                    list.add(new PieEntry(data.getCustomerLevel().get(i).getValue(), data.getCustomerLevel().get(i).getKey()));
//                }
//                PieDataSet dataSet = new PieDataSet(list, "");
//                dataSet.setColors(getResources().getColor(R.color.color_statistics2),
//                        getResources().getColor(R.color.color_statistics0),
//                        getResources().getColor(R.color.color_statistics3),
//                        getResources().getColor(R.color.color_statistics1));
//                dataSet.setSliceSpace(0f);
//                dataSet.setSelectionShift(3f);
//                dataSet.setColors(ColorTemplate.MATERIAL_COLORS);
//
//                PieData pieData = new PieData(dataSet);
//                pieData.setDrawValues(false);//不显示数值
//                mBinding.chartLevel.setData(pieData);
//                mBinding.chartLevel.invalidate();
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
    }

    /**
     * 会员类型占比（饼状图）
     */
    private void setChartType() {
        mBinding.chartType.setDrawHoleEnabled(false);//设置图形是否绘制空洞
        mBinding.chartType.getDescription().setEnabled(false);//是否显示标签
        mBinding.chartType.setHoleRadius(DensityUtils.dip2px(this, 84));//半径
        mBinding.chartType.setDrawEntryLabels(false);//饼图上的文字

        //获取图例的实例
        Legend legend = mBinding.chartType.getLegend();
        //图例文字的大小
        legend.setTextSize(6f);
        //图例文字的颜色
        legend.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        //图例的大小
        legend.setFormSize(6f);
        //图例的模式
        legend.setForm(Legend.LegendForm.CIRCLE);
        //图例的位置
        //图例水平方向为靠右
        legend.setHorizontalAlignment(Legend.LegendHorizontalAlignment.RIGHT);
        //图例垂直方向居中
        legend.setVerticalAlignment(Legend.LegendVerticalAlignment.CENTER);
        //图例分布模式(这里是垂直分布，默认的为水平分布)
        legend.setOrientation(Legend.LegendOrientation.VERTICAL);
    }

    /**
     * 储值金额占比（柱状图）
     */
    private void setChartStored() {
        // 准备数据
        List<String> xLabels = new ArrayList<>();
        xLabels.add("0-50");
        xLabels.add("50-100");
        xLabels.add("100-200");
        xLabels.add("200-500");
        xLabels.add("500" + getRstr(R.string.over));

        mBinding.chartStored.getDescription().setEnabled(false);
        mBinding.chartStored.getLegend().setEnabled(false);
        mBinding.chartStored.setFitBars(true);

        XAxis xAxis = mBinding.chartStored.getXAxis();
        xAxis.setDrawGridLines(false);//是否绘制网格线
        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);//位置
        xAxis.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        xAxis.setAxisLineColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        xAxis.setGridColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        xAxis.setTextSize(6f);
        xAxis.setValueFormatter(new IndexAxisValueFormatter(xLabels));

        //是否y轴绘制网格线
        mBinding.chartStored.getAxisLeft().setDrawGridLines(false);
        mBinding.chartStored.getAxisLeft().setEnabled(false);

        YAxis yAxis = mBinding.chartStored.getAxisRight();
        yAxis.setDrawGridLines(false);
        yAxis.setEnabled(true);
        yAxis.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        yAxis.setTextSize(6f);
        yAxis.setAxisMinimum(0f);//设置Y轴最小值
        mBinding.chartStored.getAxisLeft().setAxisMinimum(0f);
    }

    /**
     * 会员积分占比（饼状图-圆环）
     */
    private void setChartPoints() {
        mBinding.chartPoints.getDescription().setEnabled(false);//是否显示标签
        mBinding.chartPoints.setHoleRadius(DensityUtils.dip2px(this, 84));//半径
        mBinding.chartPoints.setDrawEntryLabels(false);//饼图上的文字

        //获取图例的实例
        Legend legend = mBinding.chartPoints.getLegend();
        //图例文字的大小
        legend.setTextSize(6f);
        //图例文字的颜色
        legend.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        //图例的大小
        legend.setFormSize(6f);
        //图例的模式
        legend.setForm(Legend.LegendForm.CIRCLE);
        //图例的位置
        //图例水平方向为靠右
        legend.setHorizontalAlignment(Legend.LegendHorizontalAlignment.RIGHT);
        //图例垂直方向居中
        legend.setVerticalAlignment(Legend.LegendVerticalAlignment.CENTER);
        //图例分布模式(这里是垂直分布，默认的为水平分布)
        legend.setOrientation(Legend.LegendOrientation.VERTICAL);

        //设置图形是否绘制空洞
        mBinding.chartPoints.setDrawHoleEnabled(true);
        //设置空心圆半径的大小
        mBinding.chartPoints.setHoleRadius(50f);
        //设置空心圆颜色
        mBinding.chartPoints.setHoleColor(Color.TRANSPARENT);
        //设置透明圈的透明度
        mBinding.chartPoints.setTransparentCircleAlpha(0);
    }

    /**
     * 会员等级占比（半圆饼状图）
     */
    private void setChartLevel() {
        mBinding.chartLevel.setUsePercentValues(true);
        mBinding.chartLevel.getDescription().setEnabled(false);

        mBinding.chartLevel.setDrawHoleEnabled(true);//true设置绘图孔启用
        mBinding.chartLevel.setHoleColor(Color.TRANSPARENT);//设置绘图孔颜色

        mBinding.chartLevel.setHoleRadius(30f);//中间圆的半径
        mBinding.chartLevel.setTransparentCircleRadius(0f);//设置半透明圆环的半径, 0为透明

        mBinding.chartLevel.setDrawCenterText(false);//中心是否允许画文字

        mBinding.chartLevel.setRotationEnabled(false);//整个视图是否旋转
        mBinding.chartLevel.setHighlightPerTapEnabled(true);//true点击各个板块会向上突起一点

        mBinding.chartLevel.setMaxAngle(180f); // 显示一半
        mBinding.chartLevel.setRotationAngle(180f);

        mBinding.chartLevel.animateY(1400, Easing.EaseInOutQuad);//设置进来动画。1400是动画执行的时间
//        mBinding.chartLevel.getLegend().setEnabled(false);
        mBinding.chartLevel.setDrawEntryLabels(false);//饼图上的文字

        //获取图例的实例
        Legend legend = mBinding.chartLevel.getLegend();
        //图例文字的大小
        legend.setTextSize(6f);
        //图例文字的颜色
        legend.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        //图例的大小
        legend.setFormSize(6f);
        //图例的模式
        legend.setForm(Legend.LegendForm.CIRCLE);
        //图例水平方向
        legend.setHorizontalAlignment(Legend.LegendHorizontalAlignment.CENTER);
        //图例垂直方向
        legend.setVerticalAlignment(Legend.LegendVerticalAlignment.TOP);
        //图例分布模式(这里是垂直分布，默认的为水平分布)
        legend.setOrientation(Legend.LegendOrientation.HORIZONTAL);
    }

    /**
     * 会员到店时时段分布（折线图）
     */
    private void setChartTime() {
        //设置x轴的位置
        XAxis xAxis = mBinding.chartTime.getXAxis();
        xAxis.setDrawGridLines(false);//是否绘制网格线
        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);//位置
        xAxis.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        xAxis.setTextSize(6f);

        //设置Y轴
        YAxis yAxis = mBinding.chartTime.getAxisLeft();
        yAxis.setAxisMinimum(0f);//设置Y轴最小值
        yAxis.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        yAxis.setTextSize(6f);
        yAxis.setDrawGridLines(false); //是否y轴绘制网格线

        //是否绘制右侧轴线
        mBinding.chartTime.getAxisRight().setDrawGridLines(false);
        mBinding.chartTime.getAxisRight().setEnabled(false);

        mBinding.chartTime.getDescription().setEnabled(false);
        mBinding.chartTime.getLegend().setEnabled(false);
    }

    /**
     * 会员统计
     */
    private void getMemberStatistics() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getMemberStatistics(),
                params,
                MemberStatisticsData.class,
                new RequestListener<MemberStatisticsData>() {
                    @Override
                    public void success(MemberStatisticsData data) {
                        setUIStatistics(data);
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 会员到店时段占比
     */
    private void getMemberToShopTime() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getMemberToShopTime(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        Log.e(tag, "会员到店时段占比 = " + s);
                        if (TextUtils.isEmpty(s)) {
                            return;
                        }
                        try {
                            JSONObject object = new JSONObject(s);
                            List<Entry> list = new ArrayList<>();
                            list.add(new Entry(0, object.getInt("0")));
                            list.add(new Entry(1, object.getInt("1")));
                            list.add(new Entry(2, object.getInt("2")));
                            list.add(new Entry(3, object.getInt("3")));
                            list.add(new Entry(4, object.getInt("4")));
                            list.add(new Entry(5, object.getInt("5")));
                            list.add(new Entry(6, object.getInt("6")));
                            list.add(new Entry(7, object.getInt("7")));
                            list.add(new Entry(8, object.getInt("8")));
                            list.add(new Entry(9, object.getInt("9")));
                            list.add(new Entry(10, object.getInt("10")));
                            list.add(new Entry(11, object.getInt("11")));
                            list.add(new Entry(12, object.getInt("12")));
                            list.add(new Entry(13, object.getInt("13")));
                            list.add(new Entry(14, object.getInt("14")));
                            list.add(new Entry(15, object.getInt("15")));
                            list.add(new Entry(16, object.getInt("16")));
                            list.add(new Entry(17, object.getInt("17")));
                            list.add(new Entry(18, object.getInt("18")));
                            list.add(new Entry(19, object.getInt("19")));
                            list.add(new Entry(20, object.getInt("20")));
                            list.add(new Entry(21, object.getInt("21")));
                            list.add(new Entry(22, object.getInt("22")));
                            list.add(new Entry(23, object.getInt("23")));
                            LineDataSet lineDataSet = new LineDataSet(list, "大小");
                            lineDataSet.setColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));//折线颜色
                            lineDataSet.setLineWidth(0.5f);//折现宽度
                            lineDataSet.setDrawCircles(false);//隐藏圆点
                            lineDataSet.setDrawFilled(true);// 启用填充
                            lineDataSet.setFillColor(getResources().getColor(com.yxl.commonlibrary.R.color.green_20));  // 设置填充颜色
                            LineData lineData = new LineData(lineDataSet);
                            lineData.setDrawValues(false);//隐藏数值
                            mBinding.chartTime.setData(lineData);
                            mBinding.chartTime.getXAxis().setLabelCount(list.size());//标签数量
                            mBinding.chartTime.invalidate();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 会员列表
     */
    private void getMemberList() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("searchParam", keyWords);//搜索关键字（会员卡号/手机号/姓名）
//        if (index != 0) {
//            params.put("cusType", index);
//        }
        params.put("cusType", index);
        params.put("pageNum", page);
        params.put("pageSize", Constants.limit);
        showDialog();
        hideSoftInput(this);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getMemberList(),
                params,
                MemberData.class,
                new RequestListListener<MemberData>() {
                    @Override
                    public void onResult(List<MemberData> list) {
                        hideDialog();
                        if (page == 1) {
                            mBinding.smartRefreshLayout.finishRefresh();
                            mBinding.linStatistic.setVisibility(View.VISIBLE);
                            mBinding.linInfo.setVisibility(View.GONE);
                            dataList.clear();
                        } else {
                            mBinding.smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 会员信息编辑
     */
    private void postMemberEdit() {
        Map<String, Object> params = new HashMap<>();
        params.put("cusId", cusId);//会员id
        params.put("cusName", name);
        params.put("cusPhone", mobile);
        params.put("cusSex", sex);//1.男 2.女
        params.put("cusLevelId", levelId);//会员等级id
        params.put("creditLimit", debtLimit);//欠款限额
        params.put("cusRemark", remarks);
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getMemberEdit(),
                params, String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        mBinding.tvTitle.setText(getRstr(R.string.member_manage));
                        mBinding.linStatistic.setVisibility(View.VISIBLE);
                        mBinding.linInfo.setVisibility(View.GONE);
                        if (dataList.size() > pos) {
                            dataList.get(pos).setSelect(false);
                            dataList.get(pos).setCusPhone(mobile);
                            dataList.get(pos).setCusName(name);
                            dataList.get(pos).setCusSex(sex);
                            dataList.get(pos).setCusLevelId(levelId);
                            dataList.get(pos).setCreditLimit(debtLimit);
                            dataList.get(pos).setCusRemark(remarks);
                            mAdapter.notifyItemChanged(pos);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 会员等级列表
     */
    private void getMemberLevel() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getMemberLevel(),
                params,
                MemberLevelData.class,
                new RequestListListener<MemberLevelData>() {
                    @Override
                    public void onResult(List<MemberLevelData> list) {
                        levelList.clear();
                        levelList.addAll(list);
                        int points = 0;
                        for (int i = 0; i < levelList.size(); i++) {
                            points = points + levelList.get(i).getCusLevelPoints();
                        }
                        mBinding.tvLevelSetting.setVisibility(points > 0 ? View.INVISIBLE : View.VISIBLE);
                    }
                });
    }

    /**
     * 积分规则是否设置
     */
    private void getPointsRule() {
        boolean isEnable = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_POINTS, ""));
        mBinding.tvPointsSetting.setVisibility(isEnable ? View.INVISIBLE : View.VISIBLE);
    }

}
