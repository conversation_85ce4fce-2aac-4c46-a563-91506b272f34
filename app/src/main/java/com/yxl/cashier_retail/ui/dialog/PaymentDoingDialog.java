package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.Gravity;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.google.gson.Gson;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogPaymentDoingBinding;
import com.yxl.cashier_retail.ui.bean.CashierStatusData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.base.BaseData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

/**
 * Describe:dialog（收款中）
 * Created by jingang on 2024/09/07
 */
@SuppressLint("SetTextI18n")
public class PaymentDoingDialog extends BaseDialog<DialogPaymentDoingBinding> {
    private static Activity mActivity;
    private static int type;//0.扫码收款 1.组合收款（含有金圈收款）
    private static double total;//收款金额
    private static String saleListUnique;

    public static void showDialog(Activity activity, int type, double total, String saleListUnique, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        PaymentDoingDialog.listener = listener;
        PaymentDoingDialog.type = type;
        PaymentDoingDialog.total = total;
        PaymentDoingDialog.saleListUnique = saleListUnique;
        PaymentDoingDialog dialog = new PaymentDoingDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 10 * 3, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 8);
        dialog.show();
    }

    public PaymentDoingDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(false);
        mBinding.tvDialogConfirm.setOnClickListener(v -> {
            postPaymentCancel();
        });
        Glide.with(mActivity)
                .asGif()
                .load(R.drawable.cashiering)
                .into(mBinding.ivDialogStatus);
        mBinding.tvDialogTotal.setText(getRstr(R.string.money) + DFUtils.getNum2(total));
        switch (type) {
            case 0:
                getPaymentStatus();
                break;
            case 1:
                getPaymentAloneStatus();
                break;
        }
    }

    @Override
    protected DialogPaymentDoingBinding getViewBinding() {
        return DialogPaymentDoingBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void dismiss() {
        super.dismiss();
        cancelTimer();
    }

    private Timer timer;
    private TimerTask timerTask;

    //倒计时开始
    private void startTimer() {
        cancelTimer();
        if (timer == null) {
            timer = new Timer();
        }
        timerTask = new TimerTask() {
            @Override
            public void run() {
                switch (type) {
                    case 0:
                        getPaymentStatus();
                        break;
                    case 1:
                        getPaymentAloneStatus();
                        break;
                }
            }
        };
        timer.schedule(timerTask, 2000);
    }

    //倒计时结束
    private void cancelTimer() {
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
        if (timerTask != null) {
            timerTask.cancel();
            timerTask = null;
        }
    }

//    /**
//     * 扫码支付结果查询
//     */
//    private void getPaymentStatus() {
//        Map<String, Object> map = new HashMap<>();
//        map.put("shopUnique", getShopUnique());
//        map.put("out_trade_no", saleListUnique);
//        map.put("pay_type", 13);
//        RXHttpUtil.requestByFormPostAsResponse(this,
//                ZURL.getPaymentStatus(),
//                map,
//                CashierStatusDataOld.class,
//                new RequestListener<CashierStatusDataOld>() {
//                    @Override
//                    public void success(CashierStatusDataOld data) {
//                        if (TextUtils.isEmpty(data.getTrade_state())) {
//                            if (listener != null) {
//                                listener.onPaymentFail("");
//                                dismiss();
//                            }
//                            return;
//                        }
//                        //支付状态、SUCCESS:代表成功；DOING或USERPAYING:代表支付中;FAIL或ERROR代表失败；其他按失败处理
//                        switch (data.getTrade_state()) {
//                            case "SUCCESS":
//                                if (listener != null) {
//                                    listener.onPaymentSuccess();
//                                    dismiss();
//                                }
//                                break;
//                            case "DOING":
//                            case "USERPAYING":
//                                new Handler(Looper.getMainLooper()).postDelayed(() -> getPaymentStatus(), 2000);
//                                break;
//                            default:
//                                if (listener != null) {
//                                    listener.onPaymentFail("");
//                                    dismiss();
//                                }
//                                break;
//                        }
//                    }
//
//                    @Override
//                    public void onError(String msg) {
//                        if (listener != null) {
//                            listener.onPaymentFail(msg);
//                            dismiss();
//                        }
//                    }
//                });
//    }

    /**
     * 扫码支付结果查询
     */
    private void getPaymentStatus() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("out_trade_no", saleListUnique);
        map.put("pay_type", 13);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getPaymentStatus(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        CashierStatusData data = new Gson().fromJson(s, CashierStatusData.class);
                        if (data == null) {
                            if (listener != null) {
                                listener.onPaymentFail(getRstr(R.string.collection_fail));
                                dismiss();
                            }
                            return;
                        }
                        if (data.getStatus() != Constants.SUCCESS_CODE) {
                            if (listener != null) {
                                listener.onPaymentFail(data.getMsg());
                                dismiss();
                            }
                            return;
                        }
                        if (data.getData() == null) {
                            if (listener != null) {
                                listener.onPaymentFail(data.getMsg());
                                dismiss();
                            }
                            return;
                        }
                        double salePoints = 0, cusPoints = 0;
                        if (data.getCusData() != null) {
                            salePoints = TextUtils.isEmpty(data.getCusData().getSale_points()) ? 0 : Double.parseDouble(data.getCusData().getSale_points());
                            cusPoints = data.getCusData().getCus_points();
                        }
                        //支付状态、SUCCESS:代表成功；DOING或USERPAYING:代表支付中;FAIL或ERROR代表失败；其他按失败处理
                        switch (data.getData().getTrade_state()) {
                            case "SUCCESS":
                                if (listener != null) {
                                    listener.onPaymentSuccess(salePoints, cusPoints);
                                    dismiss();
                                }
                                break;
                            case "DOING":
                            case "USERPAYING":
//                                new Handler(Looper.getMainLooper()).postDelayed(() -> getPaymentStatus(), 2000);
                                startTimer();
                                break;
                            default:
                                if (listener != null) {
                                    listener.onPaymentFail("");
                                    dismiss();
                                }
                                break;
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        if (listener != null) {
                            listener.onPaymentFail(msg);
                            dismiss();
                        }
                    }
                });
    }

    /**
     * 查询组合免密订单状态
     */
    // {"status":1,"msg":"支付成功","totals":0,"perPageNum":0,"data":"20240906180132094","goodsData":null,"cusData":null,"resultCode":0,"sale_list_unique":null}
    //{"status":0,"msg":"订单支付中","totals":0,"perPageNum":0,"data":{},"goodsData":null,"cusData":null,"resultCode":0,"sale_list_unique":null}
    private void getPaymentAloneStatus() {
        Map<String, Object> params = new HashMap<>();
        params.put("sale_list_unique", saleListUnique);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getPaymentScanAloneStatus(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        if (listener == null) {
                            return;
                        }
                        if (data == null) {
                            listener.onPaymentFail(getRstr(R.string.collection_fail));
                            dismiss();
                            return;
                        }
                        if (data.getStatus() == 1) {
                            listener.onPaymentSuccess(0, 0);
                            dismiss();
                        } else if (data.getStatus() == 0) {
//                            new Handler(Looper.getMainLooper()).postDelayed(() -> getPaymentAloneStatus(), 2000);
                            startTimer();
                        } else {
                            listener.onPaymentFail(data.getMsg());
                            dismiss();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        if (listener != null) {
                            listener.onPaymentFail(msg);
                            dismiss();
                        }
                    }
                });
    }

    /**
     * 取消收款
     */
    private void postPaymentCancel() {
        Map<String, Object> params = new HashMap<>();
        params.put("saleListUnique", saleListUnique);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getPaymentCancel(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, getRstr(R.string.collection_cancel));
                        dismiss();
//                        if (listener != null) {
//                            listener.onPaymentFail(getRstr(R.string.collection_cancel));
//                            dismiss();
//                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        /**
         * 收款成功
         */
        void onPaymentSuccess(double salePoints, double cusPoints);

        /**
         * 收款失败
         *
         * @param msg
         */
        void onPaymentFail(String msg);
    }
}
