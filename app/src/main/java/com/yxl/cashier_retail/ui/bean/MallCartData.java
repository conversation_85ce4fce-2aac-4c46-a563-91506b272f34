package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:商城-购物车（实体类）
 * Created by jingang on 2024/6/4
 */
public class MallCartData implements Serializable {
    /**
     * company_delivery_price : 0
     * cost_amt : 75.0
     * sum_deduct_amt : 0.0
     * free_delivery_price : 20.00
     * workTime : 00:00-23:59
     * delivery_price_type : 1
     * smgId : 0
     * good_list : [{"good_count":5,"order_no":"1","deliveryTime":"","start_order":5,"good_id":1000000777,"is_order":0,"cycle":"","is_loan_sataus":2,"smgId":0,"frequency":"","quotaNumber":"","sum_amt":75,"spec_id":307,"loan_price":15,"company_code":"GS112379213","isActivity":0,"id":17073,"auto_fxiaoshou":1,"loan_cut":0,"goods_name":"绒毛樱桃挂件","available_stock_count":"19","shop_unique":"1536215939565","stock_count":"19","binding_code":"","loan_count":0,"promotion_price":-1,"online_price":15,"promotion_count":0,"spec_name":"粉色","is_check":1,"cornucopia":"N","goods_img":"http://file.buyhoo.cc//image/suppliers/GS112379213/6e7b82e9-b016-4938-8905-4480612eb483.jpg","delivery_price":0}]
     * sum_good_count : 5
     * sum_amt : 75.0
     * company_name : 山东yf软件有限公司 00:00-23:59
     * area_dict_num : 371302
     * send_price : 1.00
     * fullgiftList : [{"end_date":"2023-04-27 23:59:59","compay_code":"GS112379213","giftCoupon":[{"coupon_amount":1,"coupon_img":"../static/img/coupon.jpg","coupon_id":220,"meet_amount":2,"cfree_quantity":"1"}],"gift_id":64,"giftGoods":[{"goods_name":"雪碧听装330ml","goods_barcode":"512019776186","spec_id":-1,"goods_id":1000000779,"gfree_quantity":"1","spec_name":"无","auto_fxiaoshou":1,"goodsunit_name":"瓶","goods_img":"http://file.buyhoo.cc//image/suppliers/GS112379213/f940189f-e2bb-4917-8011-0689728002d8.jpg"}],"meet_amount":1.01,"start_date":"2023-04-07 00:00:00"}]
     * company_code : GS112379213
     * shopType : 1
     */
    private boolean select;
    private double company_delivery_price;//配送费
    private double cost_amt;
    private double sum_deduct_amt;
    private String free_delivery_price;
    private String workTime;
    private int delivery_price_type;
    private int smgId;
    private int sum_good_count;
    private double sum_amt;
    private String company_name;
    private String area_dict_num;
    private String send_price;
    private String company_code;
    private int shopType;
    private List<GoodListBean> good_list;
    private List<FullgiftListBean> fullgiftList;

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }

    public double getCompany_delivery_price() {
        return company_delivery_price;
    }

    public void setCompany_delivery_price(double company_delivery_price) {
        this.company_delivery_price = company_delivery_price;
    }

    public double getCost_amt() {
        return cost_amt;
    }

    public void setCost_amt(double cost_amt) {
        this.cost_amt = cost_amt;
    }

    public double getSum_deduct_amt() {
        return sum_deduct_amt;
    }

    public void setSum_deduct_amt(double sum_deduct_amt) {
        this.sum_deduct_amt = sum_deduct_amt;
    }

    public String getFree_delivery_price() {
        return free_delivery_price;
    }

    public void setFree_delivery_price(String free_delivery_price) {
        this.free_delivery_price = free_delivery_price;
    }

    public String getWorkTime() {
        return workTime;
    }

    public void setWorkTime(String workTime) {
        this.workTime = workTime;
    }

    public int getDelivery_price_type() {
        return delivery_price_type;
    }

    public void setDelivery_price_type(int delivery_price_type) {
        this.delivery_price_type = delivery_price_type;
    }

    public int getSmgId() {
        return smgId;
    }

    public void setSmgId(int smgId) {
        this.smgId = smgId;
    }

    public int getSum_good_count() {
        return sum_good_count;
    }

    public void setSum_good_count(int sum_good_count) {
        this.sum_good_count = sum_good_count;
    }

    public double getSum_amt() {
        return sum_amt;
    }

    public void setSum_amt(double sum_amt) {
        this.sum_amt = sum_amt;
    }

    public String getCompany_name() {
        return company_name;
    }

    public void setCompany_name(String company_name) {
        this.company_name = company_name;
    }

    public String getArea_dict_num() {
        return area_dict_num;
    }

    public void setArea_dict_num(String area_dict_num) {
        this.area_dict_num = area_dict_num;
    }

    public String getSend_price() {
        return send_price;
    }

    public void setSend_price(String send_price) {
        this.send_price = send_price;
    }

    public String getCompany_code() {
        return company_code;
    }

    public void setCompany_code(String company_code) {
        this.company_code = company_code;
    }

    public int getShopType() {
        return shopType;
    }

    public void setShopType(int shopType) {
        this.shopType = shopType;
    }

    public List<GoodListBean> getGood_list() {
        return good_list;
    }

    public void setGood_list(List<GoodListBean> good_list) {
        this.good_list = good_list;
    }

    public List<FullgiftListBean> getFullgiftList() {
        return fullgiftList;
    }

    public void setFullgiftList(List<FullgiftListBean> fullgiftList) {
        this.fullgiftList = fullgiftList;
    }

    public static class GoodListBean {
        /**
         * good_count : 5
         * order_no : 1
         * deliveryTime :
         * start_order : 5
         * good_id : 1000000777
         * is_order : 0
         * cycle :
         * is_loan_sataus : 2
         * smgId : 0
         * frequency :
         * quotaNumber :
         * sum_amt : 75.0
         * spec_id : 307
         * loan_price : 15.0
         * company_code : GS112379213
         * isActivity : 0
         * id : 17073
         * auto_fxiaoshou : 1
         * loan_cut : 0.0
         * goods_name : 绒毛樱桃挂件
         * available_stock_count : 19
         * shop_unique : 1536215939565
         * stock_count : 19
         * binding_code :
         * loan_count : 0
         * promotion_price : -1
         * online_price : 15.0
         * promotion_count : 0.0
         * spec_name : 粉色
         * is_check : 1
         * cornucopia : N
         * goods_img : http://file.buyhoo.cc//image/suppliers/GS112379213/6e7b82e9-b016-4938-8905-4480612eb483.jpg
         * delivery_price : 0.0
         */
        private boolean select;
        private int good_count;
        private String order_no;
        private String deliveryTime;
        private int start_order;
        private int good_id;
        private int is_order;
        private String cycle;
        private int is_loan_sataus;
        private int smgId;
        private String frequency;
        private String quotaNumber;
        private double sum_amt;
        private int spec_id;
        private double loan_price;
        private String company_code;
        private int isActivity;
        private int id;
        private int auto_fxiaoshou;
        private double loan_cut;
        private String goods_name;
        private String available_stock_count;
        private String shop_unique;
        private String stock_count;
        private String binding_code;
        private int loan_count;
        private double promotion_price;
        private double online_price;
        private double promotion_count;
        private String spec_name;
        private int is_check;
        private String cornucopia;
        private String goods_img;
        private double delivery_price;

        public boolean isSelect() {
            return select;
        }

        public void setSelect(boolean select) {
            this.select = select;
        }

        public int getGood_count() {
            return good_count;
        }

        public void setGood_count(int good_count) {
            this.good_count = good_count;
        }

        public String getOrder_no() {
            return order_no;
        }

        public void setOrder_no(String order_no) {
            this.order_no = order_no;
        }

        public String getDeliveryTime() {
            return deliveryTime;
        }

        public void setDeliveryTime(String deliveryTime) {
            this.deliveryTime = deliveryTime;
        }

        public int getStart_order() {
            return start_order;
        }

        public void setStart_order(int start_order) {
            this.start_order = start_order;
        }

        public int getGood_id() {
            return good_id;
        }

        public void setGood_id(int good_id) {
            this.good_id = good_id;
        }

        public int getIs_order() {
            return is_order;
        }

        public void setIs_order(int is_order) {
            this.is_order = is_order;
        }

        public String getCycle() {
            return cycle;
        }

        public void setCycle(String cycle) {
            this.cycle = cycle;
        }

        public int getIs_loan_sataus() {
            return is_loan_sataus;
        }

        public void setIs_loan_sataus(int is_loan_sataus) {
            this.is_loan_sataus = is_loan_sataus;
        }

        public int getSmgId() {
            return smgId;
        }

        public void setSmgId(int smgId) {
            this.smgId = smgId;
        }

        public String getFrequency() {
            return frequency;
        }

        public void setFrequency(String frequency) {
            this.frequency = frequency;
        }

        public String getQuotaNumber() {
            return quotaNumber;
        }

        public void setQuotaNumber(String quotaNumber) {
            this.quotaNumber = quotaNumber;
        }

        public double getSum_amt() {
            return sum_amt;
        }

        public void setSum_amt(double sum_amt) {
            this.sum_amt = sum_amt;
        }

        public int getSpec_id() {
            return spec_id;
        }

        public void setSpec_id(int spec_id) {
            this.spec_id = spec_id;
        }

        public double getLoan_price() {
            return loan_price;
        }

        public void setLoan_price(double loan_price) {
            this.loan_price = loan_price;
        }

        public String getCompany_code() {
            return company_code;
        }

        public void setCompany_code(String company_code) {
            this.company_code = company_code;
        }

        public int getIsActivity() {
            return isActivity;
        }

        public void setIsActivity(int isActivity) {
            this.isActivity = isActivity;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getAuto_fxiaoshou() {
            return auto_fxiaoshou;
        }

        public void setAuto_fxiaoshou(int auto_fxiaoshou) {
            this.auto_fxiaoshou = auto_fxiaoshou;
        }

        public double getLoan_cut() {
            return loan_cut;
        }

        public void setLoan_cut(double loan_cut) {
            this.loan_cut = loan_cut;
        }

        public String getGoods_name() {
            return goods_name;
        }

        public void setGoods_name(String goods_name) {
            this.goods_name = goods_name;
        }

        public String getAvailable_stock_count() {
            return available_stock_count;
        }

        public void setAvailable_stock_count(String available_stock_count) {
            this.available_stock_count = available_stock_count;
        }

        public String getShop_unique() {
            return shop_unique;
        }

        public void setShop_unique(String shop_unique) {
            this.shop_unique = shop_unique;
        }

        public String getStock_count() {
            return stock_count;
        }

        public void setStock_count(String stock_count) {
            this.stock_count = stock_count;
        }

        public String getBinding_code() {
            return binding_code;
        }

        public void setBinding_code(String binding_code) {
            this.binding_code = binding_code;
        }

        public int getLoan_count() {
            return loan_count;
        }

        public void setLoan_count(int loan_count) {
            this.loan_count = loan_count;
        }

        public double getPromotion_price() {
            return promotion_price;
        }

        public void setPromotion_price(double promotion_price) {
            this.promotion_price = promotion_price;
        }

        public double getOnline_price() {
            return online_price;
        }

        public void setOnline_price(double online_price) {
            this.online_price = online_price;
        }

        public double getPromotion_count() {
            return promotion_count;
        }

        public void setPromotion_count(double promotion_count) {
            this.promotion_count = promotion_count;
        }

        public String getSpec_name() {
            return spec_name;
        }

        public void setSpec_name(String spec_name) {
            this.spec_name = spec_name;
        }

        public int getIs_check() {
            return is_check;
        }

        public void setIs_check(int is_check) {
            this.is_check = is_check;
        }

        public String getCornucopia() {
            return cornucopia;
        }

        public void setCornucopia(String cornucopia) {
            this.cornucopia = cornucopia;
        }

        public String getGoods_img() {
            return goods_img;
        }

        public void setGoods_img(String goods_img) {
            this.goods_img = goods_img;
        }

        public double getDelivery_price() {
            return delivery_price;
        }

        public void setDelivery_price(double delivery_price) {
            this.delivery_price = delivery_price;
        }
    }

    public static class FullgiftListBean {
        /**
         * end_date : 2023-04-27 23:59:59
         * compay_code : GS112379213
         * giftCoupon : [{"coupon_amount":1,"coupon_img":"../static/img/coupon.jpg","coupon_id":220,"meet_amount":2,"cfree_quantity":"1"}]
         * gift_id : 64
         * giftGoods : [{"goods_name":"雪碧听装330ml","goods_barcode":"512019776186","spec_id":-1,"goods_id":1000000779,"gfree_quantity":"1","spec_name":"无","auto_fxiaoshou":1,"goodsunit_name":"瓶","goods_img":"http://file.buyhoo.cc//image/suppliers/GS112379213/f940189f-e2bb-4917-8011-0689728002d8.jpg"}]
         * meet_amount : 1.01
         * start_date : 2023-04-07 00:00:00
         */

        private String end_date;
        private String compay_code;
        private int gift_id;
        private double meet_amount;
        private String start_date;
        private List<GiftCouponBean> giftCoupon;
        private List<GiftGoodsBean> giftGoods;

        public String getEnd_date() {
            return end_date;
        }

        public void setEnd_date(String end_date) {
            this.end_date = end_date;
        }

        public String getCompay_code() {
            return compay_code;
        }

        public void setCompay_code(String compay_code) {
            this.compay_code = compay_code;
        }

        public int getGift_id() {
            return gift_id;
        }

        public void setGift_id(int gift_id) {
            this.gift_id = gift_id;
        }

        public double getMeet_amount() {
            return meet_amount;
        }

        public void setMeet_amount(double meet_amount) {
            this.meet_amount = meet_amount;
        }

        public String getStart_date() {
            return start_date;
        }

        public void setStart_date(String start_date) {
            this.start_date = start_date;
        }

        public List<GiftCouponBean> getGiftCoupon() {
            return giftCoupon;
        }

        public void setGiftCoupon(List<GiftCouponBean> giftCoupon) {
            this.giftCoupon = giftCoupon;
        }

        public List<GiftGoodsBean> getGiftGoods() {
            return giftGoods;
        }

        public void setGiftGoods(List<GiftGoodsBean> giftGoods) {
            this.giftGoods = giftGoods;
        }

        public static class GiftCouponBean {
            /**
             * coupon_amount : 1.0
             * coupon_img : ../static/img/coupon.jpg
             * coupon_id : 220
             * meet_amount : 2.0
             * cfree_quantity : 1
             */

            private double coupon_amount;
            private String coupon_img;
            private int coupon_id;
            private double meet_amount;
            private String cfree_quantity;

            public double getCoupon_amount() {
                return coupon_amount;
            }

            public void setCoupon_amount(double coupon_amount) {
                this.coupon_amount = coupon_amount;
            }

            public String getCoupon_img() {
                return coupon_img;
            }

            public void setCoupon_img(String coupon_img) {
                this.coupon_img = coupon_img;
            }

            public int getCoupon_id() {
                return coupon_id;
            }

            public void setCoupon_id(int coupon_id) {
                this.coupon_id = coupon_id;
            }

            public double getMeet_amount() {
                return meet_amount;
            }

            public void setMeet_amount(double meet_amount) {
                this.meet_amount = meet_amount;
            }

            public String getCfree_quantity() {
                return cfree_quantity;
            }

            public void setCfree_quantity(String cfree_quantity) {
                this.cfree_quantity = cfree_quantity;
            }
        }

        public static class GiftGoodsBean {
            /**
             * goods_name : 雪碧听装330ml
             * goods_barcode : 512019776186
             * spec_id : -1
             * goods_id : 1000000779
             * gfree_quantity : 1
             * spec_name : 无
             * auto_fxiaoshou : 1
             * goodsunit_name : 瓶
             * goods_img : http://file.buyhoo.cc//image/suppliers/GS112379213/f940189f-e2bb-4917-8011-0689728002d8.jpg
             */

            private String goods_name;
            private String goods_barcode;
            private int spec_id;
            private int goods_id;
            private String gfree_quantity;
            private String spec_name;
            private int auto_fxiaoshou;
            private String goodsunit_name;
            private String goods_img;

            public String getGoods_name() {
                return goods_name;
            }

            public void setGoods_name(String goods_name) {
                this.goods_name = goods_name;
            }

            public String getGoods_barcode() {
                return goods_barcode;
            }

            public void setGoods_barcode(String goods_barcode) {
                this.goods_barcode = goods_barcode;
            }

            public int getSpec_id() {
                return spec_id;
            }

            public void setSpec_id(int spec_id) {
                this.spec_id = spec_id;
            }

            public int getGoods_id() {
                return goods_id;
            }

            public void setGoods_id(int goods_id) {
                this.goods_id = goods_id;
            }

            public String getGfree_quantity() {
                return gfree_quantity;
            }

            public void setGfree_quantity(String gfree_quantity) {
                this.gfree_quantity = gfree_quantity;
            }

            public String getSpec_name() {
                return spec_name;
            }

            public void setSpec_name(String spec_name) {
                this.spec_name = spec_name;
            }

            public int getAuto_fxiaoshou() {
                return auto_fxiaoshou;
            }

            public void setAuto_fxiaoshou(int auto_fxiaoshou) {
                this.auto_fxiaoshou = auto_fxiaoshou;
            }

            public String getGoodsunit_name() {
                return goodsunit_name;
            }

            public void setGoodsunit_name(String goodsunit_name) {
                this.goodsunit_name = goodsunit_name;
            }

            public String getGoods_img() {
                return goods_img;
            }

            public void setGoods_img(String goods_img) {
                this.goods_img = goods_img;
            }
        }
    }
}
