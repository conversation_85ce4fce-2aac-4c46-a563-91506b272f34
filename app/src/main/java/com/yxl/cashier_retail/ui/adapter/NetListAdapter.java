package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.NetListData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:网单-网单订单（适配器）
 * Created by jingang on 2024/6/17
 */
public class NetListAdapter extends BaseAdapter<NetListData.DataBean> {

    public NetListAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_net_list;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvTime, tvName, tvNo, tvPayStatus, tvMobile, tvAddress, tvTotal, tvStatus, tvWeight;
        tvTime = holder.getView(R.id.tvItemTime);
        tvName = holder.getView(R.id.tvItemName);
        tvNo = holder.getView(R.id.tvItemNo);
        tvPayStatus = holder.getView(R.id.tvItemPayStatus);
        tvMobile = holder.getView(R.id.tvItemMobile);
        tvAddress = holder.getView(R.id.tvItemAddress);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvStatus = holder.getView(R.id.tvItemStatus);
        tvWeight = holder.getView(R.id.tvItemWeight);

        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvTime.setTextColor(mContext.getResources().getColor(R.color.white));
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
            tvNo.setTextColor(mContext.getResources().getColor(R.color.white));
            tvPayStatus.setTextColor(mContext.getResources().getColor(R.color.white));
            tvMobile.setTextColor(mContext.getResources().getColor(R.color.white));
            tvAddress.setTextColor(mContext.getResources().getColor(R.color.white));
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.white));
            tvStatus.setTextColor(mContext.getResources().getColor(R.color.white));
            tvWeight.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            if (position % 2 == 0) {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
            } else {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
            }
            tvTime.setTextColor(mContext.getResources().getColor(R.color.black));
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
            tvNo.setTextColor(mContext.getResources().getColor(R.color.black));
            tvPayStatus.setTextColor(mContext.getResources().getColor(R.color.black));
            tvMobile.setTextColor(mContext.getResources().getColor(R.color.black));
            tvAddress.setTextColor(mContext.getResources().getColor(R.color.black));
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.black));
            tvStatus.setTextColor(mContext.getResources().getColor(R.color.black));
            tvWeight.setTextColor(mContext.getResources().getColor(R.color.black));
        }

        tvTime.setText(mDataList.get(position).getSale_list_datetime());
        tvName.setText(mDataList.get(position).getSale_list_name());
        tvNo.setText(mDataList.get(position).getSale_list_unique_str());
        tvPayStatus.setText(mDataList.get(position).getSale_list_state());
        tvMobile.setText(mDataList.get(position).getSale_list_phone());
        tvAddress.setText(mDataList.get(position).getSale_list_address());
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getSale_list_total()));
        tvWeight.setText(DFUtils.getNum2(mDataList.get(position).getGoods_weight()));
        String status;
        switch (mDataList.get(position).getSale_list_handlestate()) {
            case 2:
                status = getRstr(R.string.order_status0);
                break;
            case 3:
                status = getRstr(R.string.order_status3);
                break;
            case 9:
                status = getRstr(R.string.order_status4);
                break;
            case 4:
                status = getRstr(R.string.order_status6);
                break;
            case 10:
                status = getRstr(R.string.order_status2);
                break;
            case 7:
                status = getRstr(R.string.order_status10);
                break;
            case 6:
                status = getRstr(R.string.order_status5);
                break;
            default:
                status = "";
                break;
        }
        tvStatus.setText(status);
    }
}
