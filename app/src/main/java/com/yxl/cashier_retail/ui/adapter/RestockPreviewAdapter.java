package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.RestockPreviewData;
import com.yxl.cashier_retail.utils.DFUtils;

import java.util.List;

/**
 * Describe:自采补货-补货计划预览（适配器）
 * Created by jingang on 2024/6/5
 */
public class RestockPreviewAdapter extends BaseAdapter<RestockPreviewData> {

    public RestockPreviewAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_restock_preview;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        LinearLayout lin = holder.getView(R.id.linItem);
        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundResource(R.drawable.shape_green_kuang_white_5);
        } else {
            lin.setBackgroundResource(R.drawable.shape_white_5);
        }
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName, tvCount, tvTotal;
        tvName = holder.getView(R.id.tvItemName);
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        RecyclerView recyclerView = holder.getView(R.id.rvItem);
        tvCount = holder.getView(R.id.tvItemCount);
        EditText etRemarks = holder.getView(R.id.etItemRemarks);
        tvTotal = holder.getView(R.id.tvItemTotal);

        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundResource(R.drawable.shape_green_kuang_white_5);
        } else {
            lin.setBackgroundResource(R.drawable.shape_white_5);
        }

        //1.购销 2.自采（本地）
        if (mDataList.get(position).getPurchaseType() == 2) {
            tvName.setText(mDataList.get(position).getSupplierName() + getRstr(R.string.local));
            ivImg.setVisibility(View.GONE);
        } else {
            tvName.setText(mDataList.get(position).getSupplierName());
            ivImg.setVisibility(View.VISIBLE);
        }
        tvCount.setText(getRstr(R.string.total) + mDataList.get(position).getGoodsCounts() + getRstr(R.string.classX));
        if (TextUtils.isEmpty(mDataList.get(position).getRemark())) {
            etRemarks.setText("");
        } else {
            etRemarks.setText(mDataList.get(position).getRemark());
        }
        tvTotal.setText(getRstr(R.string.money) + DFUtils.getNum2(mDataList.get(position).getGoodsTotal()));
        TextWatcher watcher = new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String str = s.toString().trim();
                mDataList.get(position).setRemark(str);
                if (listener != null) {
                    listener.onRemarks(str, position);
                }
            }
        };
        etRemarks.setOnFocusChangeListener((v, hasFocus) -> {
            EditText mV = (EditText) v;
            if (hasFocus) {
                mV.addTextChangedListener(watcher);
                mV.setSelection(mV.getText().toString().trim().length());
            } else {
                mV.removeTextChangedListener(watcher);
            }
        });

        if (mDataList.get(position).getGoodsList() != null) {
            if (mDataList.get(position).getGoodsList().size() > 0) {
                recyclerView.setVisibility(View.VISIBLE);
                ChildGoodsAdapter adapter = new ChildGoodsAdapter(mContext);
                recyclerView.setAdapter(adapter);
                adapter.setDataList(mDataList.get(position).getGoodsList());
            } else {
                recyclerView.setVisibility(View.GONE);
            }
        } else {
            recyclerView.setVisibility(View.GONE);
        }
        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onRemarks(String remarks, int position);
    }
}
