package com.yxl.cashier_retail.ui.bean;

import org.litepal.crud.LitePalSupport;

/**
 * Describe: 商品信息（实体类）
 * Created by jingang on 2025/6/9
 */
public class GoodsData extends LitePalSupport {
    private boolean select;//是否选择
    private boolean showDel;//是否显示删除
    private double cartNum;//购物车数量
    private double newPrice;//编辑后的单价（只在收银页主单使用）
    private boolean editPrice;//是否编辑单价
    private boolean editCount;//是否编辑数量
    private boolean editTotal;//是否编辑总价
    private String virtual_kind_unique;//虚拟分类编号

    /**
     * beanTop : 0
     * foreign_key : 454554
     * goods_kind_parunique : 90000
     * goods_count : -528.0
     * goods_sale_price : 21.0
     * goods_standard : 0*4
     * goods_cus_price : 1
     * update_time : 2025-06-07 08:36:27
     * goods_barcode : 000454554
     * goods_points : 0
     * goods_web_sale_price : 21.0
     * goods_discount : 1.0
     * countBeans : 0
     * goodsChengType : 0
     * sameType : 1
     * shelfState : 2
     * goods_name : 特仑苏250ml*12盒2
     * shop_unique : 1536215939565
     * pc_shelf_state : 1
     * goods_sold : 548.0
     * goods_in_price : 4.44
     * goods_id : 1543305262
     * goodStockPrice : 4.44
     * goods_picturepath : /image/1536215939565/00045455442.jpg
     * goods_life : 0
     * goods_unit : 的
     * goods_address :
     * goods_alias : TLS250ML*12H2
     * goods_contain : 1.0
     * wholesalePriceFlg : 0
     * goods_remarks :
     * goods_promotion : 1
     * goods_hits : 0
     * goods_kind_unique : 90001
     * default_supplier_unique : 2469853835650
     * beanTimes : 0
     * goods_brand : 0
     * giveCount : 0
     * wholesaleCount : 100.0
     * wholesalePrice : 15.0
     */

    private String foreign_key;//基础条码
    private String goods_kind_parunique;//一级分类编号
    private double goods_count;//库存
    private double goods_sale_price;//零售单价
    private String goods_standard;//商品规格
    private String goods_cus_price;//会员单价
    private String update_time;//更新时间
    private String goods_barcode;//商品条码
    private double goods_web_sale_price;//线上单价
    private int goodsChengType;////称重类型 0.标品 1.称重
    private int shelfState;//线上上架状态：1、已上架；2、已下架
    private String goods_name;
    private String shop_unique;
    private int pc_shelf_state;//收银机上架状态：1.已上架 2.已下架
    private double goods_in_price;//采购单价
    private String goods_id;
    private double goodStockPrice;//最近入库价
    private String goods_picturepath;
    private int goods_life;//保质期（天）
    private String goods_unit;
    private String goods_address;
    private String goods_alias;
    private double goods_contain;//最小单位数量
    private String goods_remarks;
    private String goods_kind_unique;//二级分类编号
    private String default_supplier_unique;//供货商编号
    private String goods_brand;//商品品牌

    public GoodsData() {
    }

    public GoodsData(String goods_barcode, String goods_id, String goods_kind_unique, String goods_name, double newPrice, double cartNum) {
        this.goods_barcode = goods_barcode;
        this.goods_id = goods_id;
        this.goods_kind_unique = goods_kind_unique;
        this.goods_name = goods_name;
        this.newPrice = newPrice;
        this.cartNum = cartNum;
    }

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }

    public boolean isShowDel() {
        return showDel;
    }

    public void setShowDel(boolean showDel) {
        this.showDel = showDel;
    }

    public double getCartNum() {
        return cartNum;
    }

    public void setCartNum(double cartNum) {
        this.cartNum = cartNum;
    }

    public double getNewPrice() {
        return newPrice;
    }

    public void setNewPrice(double newPrice) {
        this.newPrice = newPrice;
    }

    public boolean isEditPrice() {
        return editPrice;
    }

    public void setEditPrice(boolean editPrice) {
        this.editPrice = editPrice;
    }

    public boolean isEditCount() {
        return editCount;
    }

    public void setEditCount(boolean editCount) {
        this.editCount = editCount;
    }

    public boolean isEditTotal() {
        return editTotal;
    }

    public void setEditTotal(boolean editTotal) {
        this.editTotal = editTotal;
    }

    public String getVirtual_kind_unique() {
        return virtual_kind_unique;
    }

    public void setVirtual_kind_unique(String virtual_kind_unique) {
        this.virtual_kind_unique = virtual_kind_unique;
    }

    public String getForeign_key() {
        return foreign_key;
    }

    public void setForeign_key(String foreign_key) {
        this.foreign_key = foreign_key;
    }

    public String getGoods_kind_parunique() {
        return goods_kind_parunique;
    }

    public void setGoods_kind_parunique(String goods_kind_parunique) {
        this.goods_kind_parunique = goods_kind_parunique;
    }

    public double getGoods_count() {
        return goods_count;
    }

    public void setGoods_count(double goods_count) {
        this.goods_count = goods_count;
    }

    public double getGoods_sale_price() {
        return goods_sale_price;
    }

    public void setGoods_sale_price(double goods_sale_price) {
        this.goods_sale_price = goods_sale_price;
    }

    public String getGoods_standard() {
        return goods_standard;
    }

    public void setGoods_standard(String goods_standard) {
        this.goods_standard = goods_standard;
    }

    public String getGoods_cus_price() {
        return goods_cus_price;
    }

    public void setGoods_cus_price(String goods_cus_price) {
        this.goods_cus_price = goods_cus_price;
    }

    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    public String getGoods_barcode() {
        return goods_barcode;
    }

    public void setGoods_barcode(String goods_barcode) {
        this.goods_barcode = goods_barcode;
    }

    public double getGoods_web_sale_price() {
        return goods_web_sale_price;
    }

    public void setGoods_web_sale_price(double goods_web_sale_price) {
        this.goods_web_sale_price = goods_web_sale_price;
    }


    public int getGoodsChengType() {
        return goodsChengType;
    }

    public void setGoodsChengType(int goodsChengType) {
        this.goodsChengType = goodsChengType;
    }

    public int getShelfState() {
        return shelfState;
    }

    public void setShelfState(int shelfState) {
        this.shelfState = shelfState;
    }

    public String getGoods_name() {
        return goods_name;
    }

    public void setGoods_name(String goods_name) {
        this.goods_name = goods_name;
    }

    public String getShop_unique() {
        return shop_unique;
    }

    public void setShop_unique(String shop_unique) {
        this.shop_unique = shop_unique;
    }

    public int getPc_shelf_state() {
        return pc_shelf_state;
    }

    public void setPc_shelf_state(int pc_shelf_state) {
        this.pc_shelf_state = pc_shelf_state;
    }

    public double getGoods_in_price() {
        return goods_in_price;
    }

    public void setGoods_in_price(double goods_in_price) {
        this.goods_in_price = goods_in_price;
    }

    public String getGoods_id() {
        return goods_id;
    }

    public void setGoods_id(String goods_id) {
        this.goods_id = goods_id;
    }

    public double getGoodStockPrice() {
        return goodStockPrice;
    }

    public void setGoodStockPrice(double goodStockPrice) {
        this.goodStockPrice = goodStockPrice;
    }

    public String getGoods_picturepath() {
        return goods_picturepath;
    }

    public void setGoods_picturepath(String goods_picturepath) {
        this.goods_picturepath = goods_picturepath;
    }

    public int getGoods_life() {
        return goods_life;
    }

    public void setGoods_life(int goods_life) {
        this.goods_life = goods_life;
    }

    public String getGoods_unit() {
        return goods_unit;
    }

    public void setGoods_unit(String goods_unit) {
        this.goods_unit = goods_unit;
    }

    public String getGoods_address() {
        return goods_address;
    }

    public void setGoods_address(String goods_address) {
        this.goods_address = goods_address;
    }

    public String getGoods_alias() {
        return goods_alias;
    }

    public void setGoods_alias(String goods_alias) {
        this.goods_alias = goods_alias;
    }

    public double getGoods_contain() {
        return goods_contain;
    }

    public void setGoods_contain(double goods_contain) {
        this.goods_contain = goods_contain;
    }

    public String getGoods_remarks() {
        return goods_remarks;
    }

    public void setGoods_remarks(String goods_remarks) {
        this.goods_remarks = goods_remarks;
    }

    public String getGoods_kind_unique() {
        return goods_kind_unique;
    }

    public void setGoods_kind_unique(String goods_kind_unique) {
        this.goods_kind_unique = goods_kind_unique;
    }

    public String getDefault_supplier_unique() {
        return default_supplier_unique;
    }

    public void setDefault_supplier_unique(String default_supplier_unique) {
        this.default_supplier_unique = default_supplier_unique;
    }

    public String getGoods_brand() {
        return goods_brand;
    }

    public void setGoods_brand(String goods_brand) {
        this.goods_brand = goods_brand;
    }
}
