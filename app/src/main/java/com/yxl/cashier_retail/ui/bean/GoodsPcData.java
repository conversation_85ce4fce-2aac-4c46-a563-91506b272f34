package com.yxl.cashier_retail.ui.bean;

import org.litepal.crud.LitePalSupport;

/**
 * Describe: 虚拟分类下的商品列表（实体类）
 * Created by jingang on 2025/3/31
 */
public class GoodsPcData extends LitePalSupport {
    /**
     * 收银页商品列表
     * beanTop : 0
     * foreign_key : 1008510086
     * goods_kind_parunique : 21059596
     * goods_count : 3.0
     * goods_sale_price : 0.02
     * goods_standard :
     * goods_cus_price : 0.02
     * update_time : 2024-03-08 14:35:56
     * goods_barcode : 1008510086
     * goods_points : 0
     * goods_web_sale_price : 0.02
     * goods_discount : 1.0
     * countBeans : 0
     * goodsChengType : 0
     * sameType : 1
     * shelfState : 2
     * goods_name : 唐三
     * shop_unique : 1536215939565
     * pc_shelf_state : 1
     * goods_sold : 0.0
     * goods_in_price : 0.01
     * goods_id : 1543399375
     * goods_picturepath : /image/1536215939565/100851008691.jpg
     * goods_life : 2099-12-12
     * goods_unit :
     * goods_address :
     * goods_alias : TS
     * goods_contain : 1
     * goods_remarks :
     * goods_promotion : 1
     * goods_hits : 0
     * goods_kind_unique : 9347089
     * default_supplier_unique :
     * beanTimes : 0
     * goods_brand :
     * giveCount : 0
     */
    private String cateUnique;//虚拟分类编号
    private int goods_kind_parunique;
    private boolean select;//是否选择
    private boolean showDel;//是否显示删除
    private double cartNum;//购物车数量
    private double newPrice;//编辑后的单价（只在收银页主单使用）
    private boolean editPrice;//是否编辑单价
    private boolean editCount;//是否编辑数量
    private boolean editTotal;//是否编辑总价

    private double goods_count;//数量(库存)
    private double goods_in_price;//进价
    private double goods_sale_price;//售价
    private String goods_cus_price;//会员价 为什么是string
    private double goods_web_sale_price;//网单价
    private String goods_barcode;//条码 -1.无码称重 -2.无码商品
    private int goodsChengType;//称重类型 0.标品 1.称重
    private String goods_name;//名称
    private String goods_id;//id
    private String goods_picturepath;//图片
    private String goods_unit;//单位
    private String goods_kind_unique;//父类编号
    private String goods_brand;

    public GoodsPcData() {
    }

    public GoodsPcData(String goods_barcode, String goods_id, String cateUnique, String goods_name, double newPrice, double cartNum) {
        this.goods_barcode = goods_barcode;
        this.goods_id = goods_id;
        this.cateUnique = cateUnique;
        this.goods_name = goods_name;
        this.newPrice = newPrice;
        this.cartNum = cartNum;
    }

    public String getCateUnique() {
        return cateUnique;
    }

    public void setCateUnique(String cateUnique) {
        this.cateUnique = cateUnique;
    }

    public int getGoods_kind_parunique() {
        return goods_kind_parunique;
    }

    public void setGoods_kind_parunique(int goods_kind_parunique) {
        this.goods_kind_parunique = goods_kind_parunique;
    }

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }

    public boolean isShowDel() {
        return showDel;
    }

    public void setShowDel(boolean showDel) {
        this.showDel = showDel;
    }

    public double getCartNum() {
        return cartNum;
    }

    public void setCartNum(double cartNum) {
        this.cartNum = cartNum;
    }

    public double getNewPrice() {
        return newPrice;
    }

    public void setNewPrice(double newPrice) {
        this.newPrice = newPrice;
    }

    public boolean isEditPrice() {
        return editPrice;
    }

    public void setEditPrice(boolean editPrice) {
        this.editPrice = editPrice;
    }

    public boolean isEditCount() {
        return editCount;
    }

    public void setEditCount(boolean editCount) {
        this.editCount = editCount;
    }

    public boolean isEditTotal() {
        return editTotal;
    }

    public void setEditTotal(boolean editTotal) {
        this.editTotal = editTotal;
    }

    public double getGoods_count() {
        return goods_count;
    }

    public void setGoods_count(double goods_count) {
        this.goods_count = goods_count;
    }

    public double getGoods_sale_price() {
        return goods_sale_price;
    }

    public void setGoods_sale_price(double goods_sale_price) {
        this.goods_sale_price = goods_sale_price;
    }

    public String getGoods_barcode() {
        return goods_barcode;
    }

    public void setGoods_barcode(String goods_barcode) {
        this.goods_barcode = goods_barcode;
    }

    public int getGoodsChengType() {
        return goodsChengType;
    }

    public void setGoodsChengType(int goodsChengType) {
        this.goodsChengType = goodsChengType;
    }

    public String getGoods_name() {
        return goods_name;
    }

    public void setGoods_name(String goods_name) {
        this.goods_name = goods_name;
    }

    public double getGoods_in_price() {
        return goods_in_price;
    }

    public void setGoods_in_price(double goods_in_price) {
        this.goods_in_price = goods_in_price;
    }

    public String getGoods_id() {
        return goods_id;
    }

    public void setGoods_id(String goods_id) {
        this.goods_id = goods_id;
    }

    public String getGoods_picturepath() {
        return goods_picturepath;
    }

    public void setGoods_picturepath(String goods_picturepath) {
        this.goods_picturepath = goods_picturepath;
    }

    public String getGoods_unit() {
        return goods_unit;
    }

    public void setGoods_unit(String goods_unit) {
        this.goods_unit = goods_unit;
    }

    public String getGoods_kind_unique() {
        return goods_kind_unique;
    }

    public void setGoods_kind_unique(String goods_kind_unique) {
        this.goods_kind_unique = goods_kind_unique;
    }

    public String getGoods_brand() {
        return goods_brand;
    }

    public void setGoods_brand(String goods_brand) {
        this.goods_brand = goods_brand;
    }

    public String getGoods_cus_price() {
        return goods_cus_price;
    }

    public void setGoods_cus_price(String goods_cus_price) {
        this.goods_cus_price = goods_cus_price;
    }

    public double getGoods_web_sale_price() {
        return goods_web_sale_price;
    }

    public void setGoods_web_sale_price(double goods_web_sale_price) {
        this.goods_web_sale_price = goods_web_sale_price;
    }
}
