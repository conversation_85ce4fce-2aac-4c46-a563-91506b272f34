package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogMemberAddBinding;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.utils.DateUtils;
import com.yxl.commonlibrary.base.BaseData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Describe:dialog（新增会员）
 * Created by jingang on 2024/5/11
 */
@SuppressLint("NonConstantResourceId")
public class MemberAddDialog extends BaseDialog<DialogMemberAddBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private String unique, mobile, name, pwd, remarks;
    private double balance,//账户余额
            debtLimit;//欠款限额
    private int type,//会员类型 0.会员卡 1.储值卡 2.会员储值卡
            sex = 1;//0.保密 1.男 2.女

    public static void showDialog(Activity activity, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        MemberAddDialog.listener = listener;
        MemberAddDialog dialog = new MemberAddDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public MemberAddDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(false);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.tvDialogType0.setOnClickListener(this);
        mBinding.tvDialogType1.setOnClickListener(this);
        mBinding.tvDialogType2.setOnClickListener(this);
        mBinding.tvDialogSex0.setOnClickListener(this);
        mBinding.tvDialogSex1.setOnClickListener(this);
        mBinding.tvDialogSex2.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
    }

    @Override
    protected DialogMemberAddBinding getViewBinding() {
        return DialogMemberAddBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogType0:
                //会员卡
                if (type != 0) {
                    type = 0;
                    mBinding.tvDialogType0.setBackgroundResource(R.drawable.shape_green_tm_5);
                    mBinding.tvDialogType0.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    mBinding.tvDialogType1.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.tvDialogType1.setTextColor(getContext().getResources().getColor(R.color.black));
                    mBinding.tvDialogType2.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.tvDialogType2.setTextColor(getContext().getResources().getColor(R.color.black));
                }
                break;
            case R.id.tvDialogType1:
                //储值卡
                if (type != 1) {
                    type = 1;
                    mBinding.tvDialogType1.setBackgroundResource(R.drawable.shape_green_tm_5);
                    mBinding.tvDialogType1.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    mBinding.tvDialogType0.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.tvDialogType0.setTextColor(getContext().getResources().getColor(R.color.black));
                    mBinding.tvDialogType2.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.tvDialogType2.setTextColor(getContext().getResources().getColor(R.color.black));
                }
                break;
            case R.id.tvDialogType2:
                //会员储值卡
                if (type != 2) {
                    type = 2;
                    mBinding.tvDialogType2.setBackgroundResource(R.drawable.shape_green_tm_5);
                    mBinding.tvDialogType2.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    mBinding.tvDialogType1.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.tvDialogType1.setTextColor(getContext().getResources().getColor(R.color.black));
                    mBinding.tvDialogType0.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.tvDialogType0.setTextColor(getContext().getResources().getColor(R.color.black));
                }
                break;
            case R.id.tvDialogSex0:
                //性别：保密
                if (sex != 0) {
                    sex = 0;
                    mBinding.tvDialogSex0.setBackgroundResource(R.drawable.shape_green_tm_5);
                    mBinding.tvDialogSex0.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    mBinding.tvDialogSex1.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.tvDialogSex1.setTextColor(getContext().getResources().getColor(R.color.black));
                    mBinding.tvDialogSex2.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.tvDialogSex2.setTextColor(getContext().getResources().getColor(R.color.black));
                }
                break;
            case R.id.tvDialogSex1:
                //性别：男性
                if (sex != 1) {
                    sex = 1;
                    mBinding.tvDialogSex1.setBackgroundResource(R.drawable.shape_green_tm_5);
                    mBinding.tvDialogSex1.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    mBinding.tvDialogSex0.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.tvDialogSex0.setTextColor(getContext().getResources().getColor(R.color.black));
                    mBinding.tvDialogSex2.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.tvDialogSex2.setTextColor(getContext().getResources().getColor(R.color.black));
                }
                break;
            case R.id.tvDialogSex2:
                //性别：女性
                if (sex != 2) {
                    sex = 2;
                    mBinding.tvDialogSex2.setBackgroundResource(R.drawable.shape_green_tm_5);
                    mBinding.tvDialogSex2.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    mBinding.tvDialogSex1.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.tvDialogSex1.setTextColor(getContext().getResources().getColor(R.color.black));
                    mBinding.tvDialogSex0.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.tvDialogSex0.setTextColor(getContext().getResources().getColor(R.color.black));
                }
                break;
            case R.id.tvDialogConfirm:
                //保存
                unique = mBinding.etDialogNo.getText().toString().trim();
                mobile = mBinding.etDialogMobile.getText().toString().trim();
                name = mBinding.etDialogName.getText().toString().trim();
                balance = TextUtils.isEmpty(mBinding.etDialogBalance.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etDialogBalance.getText().toString().trim());
                debtLimit = TextUtils.isEmpty(mBinding.etDialogLimit.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etDialogLimit.getText().toString().trim());
                pwd = mBinding.etDialogPwd.getText().toString().trim();
                remarks = mBinding.etDialogRemarks.getText().toString().trim();
                if (TextUtils.isEmpty(unique)) {
                    showToast(1, getRstr(R.string.input_member_no));
                    return;
                }
                if (TextUtils.isEmpty(mobile)) {
                    showToast(1, getRstr(R.string.input_member_mobile));
                    return;
                }
                if (TextUtils.isEmpty(name)) {
                    showToast(1, getRstr(R.string.input_member_name));
                    return;
                }
                if (TextUtils.isEmpty(pwd)) {
                    showToast(1, getRstr(R.string.input_account_pwd));
                    return;
                }
                postMemberAdd();
                break;
        }
    }

    /**
     * 会员新增
     */
    private void postMemberAdd() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("cusUnique", unique);
        params.put("cusName", name);
        params.put("cusPhone", mobile);
        params.put("cusPoints", 0);
        params.put("cusRegeditDate", DateUtils.getCurrentDate(DateUtils.PATTERN_DAY));
        switch (type) {
            case 1:
                params.put("cusType", "储");
                break;
            case 2:
                params.put("cusType", "会,储");
                break;
            default:
                params.put("cusType", "会");
                break;
        }
        params.put("cusBalance", balance);
        params.put("cusPassword", pwd);
        params.put("cus_remark", remarks);

        showDialog();
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getMemberAdd(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        if (data.getStatus() == 0) {
                            showToast(0, data.getMsg());
                            //此处判断balance大于0去充值
                            if (balance > 0) {
                                MemberRechargeDialog.showDialog(mActivity, unique, balance, money -> {
                                });
                            }
                            dismiss();
                        } else {
                            showToast(1, data.getMsg());
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm();
    }
}
