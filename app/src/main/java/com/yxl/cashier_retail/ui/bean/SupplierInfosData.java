package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;

/**
 * Describe:供货商详情-业务信息（实体类）
 * Created by jingang on 2024/6/8
 */
public class SupplierInfosData implements Serializable {
    /**
     * supplierUnique : d60054eac842485aa3fa31a4dcfc57f3
     * supplierName : 供应商甲
     * goodsTypeCount : 0
     * purchaseAmount : 121.5
     * settledAmount : 100
     * outstandingAmount : 0
     */

    private String supplierUnique;
    private String supplierName;
    private int goodsTypeCount;//所供商品数量
    private double purchaseAmount;//采购金额
    private double settledAmount;//已结金额
    private double outstandingAmount;//待结金额
    private int billCount;//订单数量

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public int getGoodsTypeCount() {
        return goodsTypeCount;
    }

    public void setGoodsTypeCount(int goodsTypeCount) {
        this.goodsTypeCount = goodsTypeCount;
    }

    public double getPurchaseAmount() {
        return purchaseAmount;
    }

    public void setPurchaseAmount(double purchaseAmount) {
        this.purchaseAmount = purchaseAmount;
    }

    public double getSettledAmount() {
        return settledAmount;
    }

    public void setSettledAmount(double settledAmount) {
        this.settledAmount = settledAmount;
    }

    public double getOutstandingAmount() {
        return outstandingAmount;
    }

    public void setOutstandingAmount(double outstandingAmount) {
        this.outstandingAmount = outstandingAmount;
    }

    public int getBillCount() {
        return billCount;
    }

    public void setBillCount(int billCount) {
        this.billCount = billCount;
    }
}
