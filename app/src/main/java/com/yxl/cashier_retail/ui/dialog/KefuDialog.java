package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.Gravity;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogKefuBinding;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:dialog（联系客服）
 * Created by jingang on 2024/5/8
 */
@SuppressLint("NonConstantResourceId")
public class KefuDialog extends BaseDialog<DialogKefuBinding> {
    private static Activity mActivity;

    public static void showDialog(Activity activity, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        KefuDialog.listener = listener;
        KefuDialog dialog = new KefuDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, DensityUtils.getScreenHeight(dialog.getContext()) / 5 * 3);
        dialog.show();
    }

    public KefuDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
    }

    @Override
    protected DialogKefuBinding getViewBinding() {
        return DialogKefuBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    private static MyListener listener;

    public interface MyListener {
        void onLanguageClick(String language, String area);
    }
}
