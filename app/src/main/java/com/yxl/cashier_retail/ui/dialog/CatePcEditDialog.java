package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogCatePcEditBinding;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:dialog（虚拟分类编辑）
 * Created by jingang on 2024/12/21
 */
@SuppressLint("NonConstantResourceId")
public class CatePcEditDialog extends BaseDialog<DialogCatePcEditBinding> implements View.OnClickListener {

    private static int id;
    private static String name, unique;

    public static void showDialog(Activity activity, int id, String name, String unique, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        CatePcEditDialog.id = id;
        CatePcEditDialog.name = name;
        CatePcEditDialog.unique = unique;
        CatePcEditDialog.listener = listener;
        CatePcEditDialog dialog = new CatePcEditDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 10 * 7, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public CatePcEditDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.tvDialogDel.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        if (id == 0) {
            //新增
            mBinding.tvDialogTitle.setText(getRstr(R.string.cate_home_pc_add));
            mBinding.etDialog.setText("");
            mBinding.tvDialogDel.setVisibility(View.GONE);
        } else {
            //编辑
            mBinding.tvDialogTitle.setText(getRstr(R.string.cate_home_pc_edit));
            mBinding.etDialog.setText(name);
            mBinding.tvDialogDel.setVisibility(View.VISIBLE);
        }
    }

    @Override
    protected DialogCatePcEditBinding getViewBinding() {
        return DialogCatePcEditBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogDel:
                //删除
                if (listener != null) {
                    listener.onConfirm(2, id, name, unique);
                    dismiss();
                }
                break;
            case R.id.tvDialogConfirm:
                //保存
                name = mBinding.etDialog.getText().toString().trim();
                if (TextUtils.isEmpty(name)) {
                    showToast(1, getRstr(R.string.input_cate_name));
                    return;
                }
                if (listener != null) {
                    if (id == 0) {
                        listener.onConfirm(0, id, name, unique);
                    } else {
                        listener.onConfirm(1, id, name, unique);
                    }
                    dismiss();
                }
                break;
        }
    }

    private static MyListener listener;

    public interface MyListener {
        /**
         * @param type   0.新增 1.编辑 2.删除
         * @param id
         * @param name
         * @param unique
         */
        void onConfirm(int type, int id, String name, String unique);
    }

}
