package com.yxl.cashier_retail.ui.contract;

import com.yxl.cashier_retail.ui.bean.QueryOrderInfoData;
import com.yxl.cashier_retail.ui.bean.OrderListData;
import com.yxl.commonlibrary.base.BasePresenter;
import com.yxl.commonlibrary.base.BaseView;

/**
 * Describe:订单
 * Created by jingang on 2024/5/20
 */
public class OrderContract {
    public interface View extends BaseView {
        void onListSuccess(OrderListData data);

        void onListError(String msg);

        void onInfoSuccess(QueryOrderInfoData.DataBean data);

        void onInfoError(String msg);
    }

    public interface Presenter extends BasePresenter<View> {

        /**
         * 订单列表
         *
         * @param staff_id          员工编号
         * @param startTime         开始日期
         * @param endTime           结束日期
         * @param keyWords          搜索关键字
         * @param sale_list_payment 付款方式： 1、现金支付，2-支付宝，3-微信，4、银行卡，5-储值卡 ，8-混合支付,10、积分兑换，100 金圈平台 ，全部传空
         */
        void getOrderList(String shopUnique, String staff_id, String startTime, String endTime, String keyWords, int sale_list_payment, int page, int limit);

        /**
         * 订单详情
         *
         * @param shop_unique
         * @param sale_list_unique 订单编号
         */
        void getOrderInfo(String shop_unique, String sale_list_unique);
    }
}
