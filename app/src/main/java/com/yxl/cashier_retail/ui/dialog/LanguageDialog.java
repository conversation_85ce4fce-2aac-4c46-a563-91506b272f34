package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogLanguageBinding;

/**
 * Describe:dialog（语言切换）
 * Created by jingang on 2023/6/17
 */
@SuppressLint("NonConstantResourceId")
public class LanguageDialog extends BaseDialog<DialogLanguageBinding> {
    private static Activity mActivity;
    private String language;
    private int type;//0.中文 1.英文 2.泰文 3.韩文 4.日文

    public static void showDialog(Activity activity, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        LanguageDialog.listener = listener;
        LanguageDialog dialog = new LanguageDialog(activity);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public LanguageDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        language = SPUtils.getInstance().getString(Constants.LANGUAGE,"");
//        if (TextUtils.isEmpty(language)) {
//            type = 0;
//            clearSelect();
//            ivLanguage0.setSelected(true);
//        } else {
//            switch (language) {
//                case "en":
//                    type = 1;
//                    clearSelect();
//                    ivLanguage1.setSelected(true);
//                    break;
//                case "th":
//                    type = 2;
//                    clearSelect();
//                    ivLanguage2.setSelected(true);
//                    break;
//                case "ko":
//                    type = 3;
//                    clearSelect();
//                    ivLanguage3.setSelected(true);
//                    break;
//                case "ja":
//                    type = 4;
//                    clearSelect();
//                    ivLanguage4.setSelected(true);
//                    break;
//                default:
//                    type = 0;
//                    clearSelect();
//                    ivLanguage0.setSelected(true);
//                    break;
//            }
//        }
    }

    @Override
    protected DialogLanguageBinding getViewBinding() {
        return DialogLanguageBinding.inflate(getLayoutInflater());
    }


//    @OnClick({R.id.ivDialogClose,
//            R.id.relDialogLanguage0, R.id.relDialogLanguage1, R.id.relDialogLanguage2, R.id.relDialogLanguage3, R.id.relDialogLanguage4,
//            R.id.tvDialogConfirm})
//    public void onViewClicked(View view) {
//        switch (view.getId()) {
//            case R.id.ivDialogClose:
//                dismiss();
//                break;
//            case R.id.relDialogLanguage0:
//                //中文
//                if (type != 0) {
//                    type = 0;
//                    clearSelect();
//                    ivLanguage0.setSelected(true);
//                }
//                break;
//            case R.id.relDialogLanguage1:
//                //英文
//                if (type != 1) {
//                    type = 1;
//                    clearSelect();
//                    ivLanguage1.setSelected(true);
//                }
//                break;
//            case R.id.relDialogLanguage2:
//                //泰文
//                if (type != 2) {
//                    type = 2;
//                    clearSelect();
//                    ivLanguage2.setSelected(true);
//                }
//                break;
//            case R.id.relDialogLanguage3:
//                //韩文
//                if (type != 3) {
//                    type = 3;
//                    clearSelect();
//                    ivLanguage3.setSelected(true);
//                }
//                break;
//            case R.id.relDialogLanguage4:
//                //日文
//                if (type != 4) {
//                    type = 4;
//                    clearSelect();
//                    ivLanguage4.setSelected(true);
//                }
//                break;
//            case R.id.tvDialogConfirm:
//                //确认
//                String language, area;
//                switch (type) {
//                    case 1:
//                        language = "en";
//                        area = "US";
//                        break;
//                    case 2:
//                        language = "th";
//                        area = "TH";
//                        break;
//                    case 3:
//                        language = "ko";
//                        area = "KR";
//                        break;
//                    case 4:
//                        language = "ja";
//                        area = "JP";
//                        break;
//                    default:
//                        language = "zh";
//                        area = "ZH";
//                        break;
//                }
//                if (listener != null) {
//                    MultiLanguageUtils.changeLanguage(mActivity, language, area);
//                    listener.onLanguageClick(language, area);
//                    dismiss();
//                }
//                break;
//        }
//    }

    /**
     * 清除选择
     */
    private void clearSelect() {
//        ivLanguage0.setSelected(false);
//        ivLanguage1.setSelected(false);
//        ivLanguage2.setSelected(false);
//        ivLanguage3.setSelected(false);
//        ivLanguage4.setSelected(false);
    }

    private static MyListener listener;


    public interface MyListener {
        void onLanguageClick(String language, String area);
    }
}
