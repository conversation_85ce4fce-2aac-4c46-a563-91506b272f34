package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogCameraBinding;

/**
 * Describe:dialog（拍照、相册选择图片）
 * Created by jingang on 2023/5/30
 */
@SuppressLint("NonConstantResourceId")
public class CameraDialog extends BaseDialog<DialogCameraBinding> implements View.OnClickListener {

    public static void showDialog(Context context, MyListener listener) {
        CameraDialog.listener = listener;
        CameraDialog dialog = new CameraDialog(context);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public CameraDialog(Context context) {
        super(context, R.style.dialog_bottom);
        setCancelable(true);
        mBinding.tvDialogCamera.setOnClickListener(this);
        mBinding.tvDialogAlbum.setOnClickListener(this);
        mBinding.tvDialogCancel.setOnClickListener(this);
    }

    @Override
    protected DialogCameraBinding getViewBinding() {
        return DialogCameraBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvDialogCamera:
                //拍照
                if (listener != null) {
                    listener.onClick(0);
                    dismiss();
                }
                break;
            case R.id.tvDialogAlbum:
                //从手机相册选择
                if (listener != null) {
                    listener.onClick(1);
                    dismiss();
                }
                break;
            case R.id.tvDialogCancel:
                //取消
                dismiss();
                break;
        }
    }

    private static MyListener listener;

    public interface MyListener {
        /**
         * @param type 0.拍照 1.从手机相册选择
         */
        void onClick(int type);
    }
}
