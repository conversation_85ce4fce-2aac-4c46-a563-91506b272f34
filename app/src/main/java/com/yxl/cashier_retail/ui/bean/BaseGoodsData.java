package com.yxl.cashier_retail.ui.bean;

import java.util.List;

/**
 * Describe: 全部商品（实体类）
 * Created by jingang on 2025/6/12
 */
public class BaseGoodsData {
    /**
     * status : 0 为什么要返回0！！！
     * msg : 查询成功！
     * data : [{"beanTop":0,"foreign_key":1111111,"goods_kind_parunique":98000,"goods_count":0,"goods_sale_price":24,"goods_standard":"0*4","goods_cus_price":"1.0","update_time":"2024-07-09 09:16:13","goods_barcode":"000454554","goods_points":0,"goods_web_sale_price":24,"goods_discount":1,"countBeans":0,"goodsChengType":0,"sameType":1,"shelfState":2,"goods_name":"特仑苏250ml*12盒","shop_unique":1536215939565,"pc_shelf_state":1,"goods_sold":0,"goods_in_price":4.04,"goods_id":1543305262,"goodStockPrice":4.04,"goods_picturepath":"","goods_life":0,"goods_unit":"","goods_address":"","goods_alias":"TLS250ML12H","goods_contain":4,"goods_remarks":"","goods_promotion":"1","goods_hits":0,"goods_kind_unique":"98001","default_supplier_unique":"","beanTimes":0,"goods_brand":"0","giveCount":0},{"beanTop":0,"foreign_key":1111111,"goods_kind_parunique":98000,"goods_count":0,"goods_sale_price":12,"goods_standard":"0*2","goods_cus_price":"15.0","update_time":"2024-07-09 09:16:13","goods_barcode":"01111111","goods_points":0,"goods_web_sale_price":12,"goods_discount":1,"countBeans":0,"goodsChengType":0,"sameType":1,"shelfState":2,"goods_name":"豆角","shop_unique":1536215939565,"pc_shelf_state":1,"goods_sold":0,"goods_in_price":2.02,"goods_id":1543305261,"goodStockPrice":2.02,"goods_picturepath":"","goods_life":0,"goods_unit":"","goods_address":"","goods_alias":"DJ","goods_contain":2,"goods_remarks":"","goods_promotion":"1","goods_hits":0,"goods_kind_unique":"98001","default_supplier_unique":"","beanTimes":0,"goods_brand":"0","giveCount":0},{"beanTop":0,"foreign_key":1536215939565,"goods_kind_parunique":98000,"goods_count":44,"goods_sale_price":13.98,"goods_standard":"","goods_cus_price":"0.02","update_time":"2024-07-03 21:10:22","goods_barcode":"10085","goods_points":0,"goods_web_sale_price":13.98,"goods_discount":1,"countBeans":0,"goodsChengType":0,"sameType":1,"shelfState":1,"goods_name":"黄豆片","shop_unique":1536215939565,"pc_shelf_state":1,"goods_sold":298.42,"goods_in_price":0.01,"goods_id":1543246048,"goodStockPrice":0.01,"goods_picturepath":"/image/1536215939565/1008569.png","goods_life":0,"goods_unit":"包","goods_address":"","goods_alias":"HDP","goods_contain":1,"goods_remarks":"","goods_promotion":"1","goods_hits":0,"goods_kind_unique":"98001","default_supplier_unique":"","beanTimes":0,"goods_brand":"","giveCount":0}]
     */

    private int status;
    private String msg;
    private List<GoodsData> data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<GoodsData> getData() {
        return data;
    }

    public void setData(List<GoodsData> data) {
        this.data = data;
    }

}
