package com.yxl.cashier_retail.ui.contract;


import com.yxl.commonlibrary.base.BasePresenter;
import com.yxl.commonlibrary.base.BaseView;

/**
 * 登录
 */
public class LoginContract {
    public interface View extends BaseView {
        /**
         * 登录成功
         *
         * @param str
         */
        void successLogin(String str);
    }

    public interface Presenter extends BasePresenter<View> {
        /**
         * 登录
         *
         * @param account
         * @param pwd
         */
        void postLogin(String account, String pwd);
    }
}
