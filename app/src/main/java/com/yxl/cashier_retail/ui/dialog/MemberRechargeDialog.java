package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogMemberRechargeBinding;
import com.yxl.cashier_retail.ui.adapter.MemberRechargeConfigAdapter;
import com.yxl.cashier_retail.ui.bean.MemberRechargeConfigData;
import com.yxl.cashier_retail.ui.bean.SaleListUniqueData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.view.NumberKeyBoardView;
import com.yxl.commonlibrary.base.BaseData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:dialog（会员充值）
 * Created by jingang on 2024/7/3
 */
@SuppressLint("NonConstantResourceId")
public class MemberRechargeDialog extends BaseDialog<DialogMemberRechargeBinding> implements View.OnClickListener {
    private int type = 1,//0.金圈 1.现金 2.微信 3.支付宝 4.存零
            currentIndex = 1;
    private static String unique;//会员编号
    private static double money,//充值金额
            gift;//赠送金额

    private List<MemberRechargeConfigData> configList = new ArrayList<>();
    private MemberRechargeConfigAdapter configAdapter;

    public static void showDialog(Activity activity, String unique, double money, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        MemberRechargeDialog.listener = listener;
        MemberRechargeDialog.unique = unique;
        MemberRechargeDialog.money = money;
        MemberRechargeDialog dialog = new MemberRechargeDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public MemberRechargeDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        ((AnimationDrawable) mBinding.ivDialogCursor.getDrawable()).start();
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.linDialogJinQ.setOnClickListener(this);
        mBinding.linDialogCash.setOnClickListener(this);
        mBinding.linDialogWechat.setOnClickListener(this);
        mBinding.linDialogAlipay.setOnClickListener(this);
        if (money > 0) {
            mBinding.tvDialogMoney.setText(DFUtils.getNum2(money));
            mBinding.tvDialogMoney.setVisibility(View.VISIBLE);
            mBinding.tvDialogHints.setVisibility(View.GONE);
        }
        mBinding.etDialogScan.requestFocus();
        //获取焦点不弹出软键盘
        mBinding.etDialogScan.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                InputMethodManager imm = (InputMethodManager) v.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                if (imm != null) {
                    imm.hideSoftInputFromWindow(v.getWindowToken(), 0);
                }
            }
        });
        mBinding.etDialogScan.setScanResultListener(result -> {
            Log.e(tag, "扫码结果 = " + result);
            if (money <= 0) {
                showToast(1, getRstr(R.string.input_recharge_money));
                return;
            }
            getSaleListUnique(result);
        });

        mBinding.numberKeyBoardView.setMaxLength(Constants.MAX_MONEY_LENGTH);
        mBinding.numberKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var) {
                mBinding.tvDialogMoney.setText(var);
                if (TextUtils.isEmpty(var)) {
                    mBinding.tvDialogMoney.setVisibility(View.GONE);
                    mBinding.tvDialogHints.setVisibility(View.VISIBLE);
                    money = 0;
                } else {
                    mBinding.tvDialogMoney.setVisibility(View.VISIBLE);
                    mBinding.tvDialogHints.setVisibility(View.GONE);
                    money = Double.parseDouble(var);
                }
            }

            @Override
            public void onConfirm() {
                if (money <= 0) {
                    showToast(1, getRstr(R.string.input_recharge_money));
                    return;
                }
                if (type == 0) {
                    return;
                }
                gift = findUnitPriceByQuantity(configList, money);
                postRecharge();
            }
        });
        setAdapter();
        getRechargeConfig();
    }

    @Override
    protected DialogMemberRechargeBinding getViewBinding() {
        return DialogMemberRechargeBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.linDialogJinQ:
                //金圈收款
                type = 0;
                fragmentControl();
                break;
            case R.id.linDialogCash:
                //现金
                type = 1;
                fragmentControl();
                break;
            case R.id.linDialogWechat:
                //微信
                type = 2;
                fragmentControl();
                break;
            case R.id.linDialogAlipay:
                //支付宝
                type = 3;
                fragmentControl();
                break;
        }
    }

    /**
     * 控制fragment的变化
     */
    public void fragmentControl() {
        if (currentIndex != type) {
            removeBottomColor();
            setBottomColor();
            currentIndex = type;
        }
    }

    /**
     * 设置底部栏按钮变色
     */
    private void setBottomColor() {
        switch (type) {
            case 0:
                mBinding.linDialogJinQ.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivDialogJinQ.setImageResource(R.mipmap.ic_recharge01);
                mBinding.tvDialogJinQ.setTextColor(getContext().getResources().getColor(R.color.white));
                mBinding.tvDialogJinQHint.setTextColor(getContext().getResources().getColor(R.color.white));
                break;
            case 1:
                mBinding.linDialogCash.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivDialogCash.setImageResource(R.mipmap.ic_recharge11);
                mBinding.tvDialogCash.setTextColor(getContext().getResources().getColor(R.color.white));
                mBinding.tvDialogCashHint.setTextColor(getContext().getResources().getColor(R.color.white));
                break;
            case 2:
                mBinding.linDialogWechat.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivDialogWechat.setImageResource(R.mipmap.ic_recharge21);
                mBinding.tvDialogWechat.setTextColor(getContext().getResources().getColor(R.color.white));
                mBinding.tvDialogWechatHint.setTextColor(getContext().getResources().getColor(R.color.white));
                break;
            case 3:
                mBinding.linDialogAlipay.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivDialogAlipay.setImageResource(R.mipmap.ic_recharge31);
                mBinding.tvDialogAlipay.setTextColor(getContext().getResources().getColor(R.color.white));
                mBinding.tvDialogAlipayHint.setTextColor(getContext().getResources().getColor(R.color.white));
                break;
            default:
                break;
        }
    }

    /**
     * 清除底部栏颜色
     */
    private void removeBottomColor() {
        switch (currentIndex) {
            case 0:
                mBinding.linDialogJinQ.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogJinQ.setImageResource(R.mipmap.ic_recharge00);
                mBinding.tvDialogJinQ.setTextColor(getContext().getResources().getColor(R.color.black));
                mBinding.tvDialogJinQHint.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
                break;
            case 1:
                mBinding.linDialogCash.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCash.setImageResource(R.mipmap.ic_recharge10);
                mBinding.tvDialogCash.setTextColor(getContext().getResources().getColor(R.color.black));
                mBinding.tvDialogCashHint.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
                break;
            case 2:
                mBinding.linDialogWechat.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogWechat.setImageResource(R.mipmap.ic_recharge20);
                mBinding.tvDialogWechat.setTextColor(getContext().getResources().getColor(R.color.black));
                mBinding.tvDialogWechatHint.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
                break;
            case 3:
                mBinding.linDialogAlipay.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogAlipay.setImageResource(R.mipmap.ic_recharge30);
                mBinding.tvDialogAlipay.setTextColor(getContext().getResources().getColor(R.color.black));
                mBinding.tvDialogAlipayHint.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
                break;
            default:
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        configAdapter = new MemberRechargeConfigAdapter(getContext());
        mBinding.rvDialogConfig.setAdapter(configAdapter);
        configAdapter.setOnItemClickListener((view, position) -> {
            mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(configList.get(position).getRecharge_money()));
        });
    }

    /**
     * 遍历list，返回满足条件的第一个单价（即数量大于或等于指定数量的最低单价
     * @param priceTiers
     * @param quantity
     * @return
     */
    @SuppressLint("NewApi")
    public double findUnitPriceByQuantity(List<MemberRechargeConfigData> priceTiers, double quantity) {
        // 首先筛选出所有数量小于或等于给定数量的阶梯
        List<MemberRechargeConfigData> validTiers = new ArrayList<>();
        for (MemberRechargeConfigData tier : priceTiers) {
            if (quantity >= tier.getRecharge_money()) {
                validTiers.add(tier);
            }
        }

        // 如果没有找到匹配项，则返回默认值或抛出异常
        if (validTiers.isEmpty()) {
            return 0; // 或者抛出异常，表示未找到合适的价格
        }

        // 找到符合条件的最大数量对应的单价
        MemberRechargeConfigData selectedTier = null;
        selectedTier = Collections.max(validTiers, Comparator.comparingDouble(MemberRechargeConfigData::getRecharge_money));
        return selectedTier.getGive_money();
    }

    /**
     * 查询充值金额配置
     */
    private void getRechargeConfig() {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("type", 2);//是否过期 1:过期 2:未过期
        params.put("page", 1);
        params.put("limit", 10000);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getMemberRechargeConfig(),
                params,
                MemberRechargeConfigData.class,
                new RequestListListener<MemberRechargeConfigData>() {
                    @Override
                    public void onResult(List<MemberRechargeConfigData> list) {
                        configList.clear();
                        for (int i = 0; i < list.size(); i++) {
                            if (list.get(i).isFlag()) {
                                configList.add(list.get(i));
                            }
                        }
                        configAdapter.setDataList(configList);
                        if (configList.isEmpty()) {
                            mBinding.rvDialogConfig.setVisibility(View.GONE);
                        } else {
                            mBinding.rvDialogConfig.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        configList.clear();
                        configAdapter.clear();
                        mBinding.rvDialogConfig.setVisibility(View.GONE);
                    }
                });
    }

    /**
     * 充值
     */
    private void postRecharge() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("cusUnique", unique);
        params.put("money", money);
        params.put("type", 1);//1.充值 2.提现
        params.put("saleListCashier", getStaffUnique());
        params.put("recharge_method", type);//充值方式 1.现金 2.微信 3.支付宝 4.存零
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getMemberRecharge(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, getRstr(R.string.recharge_success));
                        if (listener != null) {
                            listener.onConfirm(money + gift);
                            dismiss();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 创建订单编号
     */
    private void getSaleListUnique(String code) {
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getSaleListUniqueCreate(),
                new HashMap<>(),
                SaleListUniqueData.class,
                new RequestListener<SaleListUniqueData>() {
                    @Override
                    public void success(SaleListUniqueData data) {
                        postRechargeScan(code, data.getSale_list_unique());
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 扫码充值
     */
    private void postRechargeScan(String code, String saleListUnique) {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("cusUnique", unique);
        params.put("money", money);
        params.put("type", 1);//1.充值 2.提现
        params.put("saleListCashier", getStaffUnique());
        params.put("recharge_method", 6);//充值方法：6、合利宝（目前只支持合利宝）
        params.put("auth_code", code);
        params.put("sale_list_unique", saleListUnique);
        showDialog();
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getMemberRecharge(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        if (data == null) {
                            hideDialog();
                            return;
                        }
                        //状态字：0-失败；1-成功；2、用户支付中
                        switch (data.getStatus()) {
                            case 0:
                                hideDialog();
                                showToast(1, data.getMsg());
                                break;
                            case 1:
                                hideDialog();
                                showToast(0, data.getMsg());
                                if (listener != null) {
                                    listener.onConfirm(money);
                                    dismiss();
                                }
                                break;
                            default:
                                getRechargeStatus(saleListUnique, money);
                                break;
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 查询充值订单状态
     * {"status":2,"msg":"支付中","totals":0,"perPageNum":0,"data":"20241011113422751","goodsData":null,"cusData":null,"resultCode":0,"sale_list_unique":null}
     *
     * @param saleListUnique
     */
    private void getRechargeStatus(String saleListUnique, double money) {
        Map<String, Object> params = new HashMap<>();
        params.put("sale_list_unique", saleListUnique);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getMemberRechargeStatus(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        if (data == null) {
                            hideDialog();
                            return;
                        }
                        //0.失败 1.成功 2.支付中
                        switch (data.getStatus()) {
                            case 1:
                                hideDialog();
                                showToast(0, data.getMsg());
                                if (listener != null) {
                                    listener.onConfirm(money);
                                    dismiss();
                                }
                                break;
                            case 2:
                                new Handler(Looper.getMainLooper()).postDelayed(() -> getRechargeStatus(saleListUnique, money), 2000);
                                break;
                            default:
                                hideDialog();
                                showToast(1, data.getMsg());
                                break;
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(double money);
    }
}
