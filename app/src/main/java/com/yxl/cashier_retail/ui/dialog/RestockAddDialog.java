package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogKefuBinding;
import com.yxl.cashier_retail.databinding.DialogRestockAddBinding;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:dialog（创建补货计划）
 * Created by jingang on 2024/6/5
 */
@SuppressLint("NonConstantResourceId")
public class RestockAddDialog extends BaseDialog<DialogRestockAddBinding> implements View.OnClickListener {

    public static void showDialog(Activity activity, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        RestockAddDialog.listener = listener;
        RestockAddDialog dialog = new RestockAddDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public RestockAddDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.ivDialogClear.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        mBinding.etDialogName.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (TextUtils.isEmpty(s.toString().trim())) {
                    mBinding.ivDialogClear.setVisibility(View.GONE);
                } else {
                    mBinding.ivDialogClear.setVisibility(View.VISIBLE);
                }
            }
        });
    }

    @Override
    protected DialogRestockAddBinding getViewBinding() {
        return DialogRestockAddBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.ivDialogClear:
                //清除输入
                mBinding.etDialogName.setText("");
                break;
            case R.id.tvDialogConfirm:
                //确认创建
                if (TextUtils.isEmpty(mBinding.etDialogName.getText().toString().trim())) {
                    showToast(1, getRstr(R.string.input_restock_name));
                    return;
                }
                if (listener != null) {
                    listener.onConfirm(mBinding.etDialogName.getText().toString().trim());
                    dismiss();
                }
                break;
        }
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(String name);
    }
}
