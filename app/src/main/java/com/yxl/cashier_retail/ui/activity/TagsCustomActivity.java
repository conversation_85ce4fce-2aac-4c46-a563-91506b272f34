package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.view.View;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityTagsCustomBinding;
import com.yxl.commonlibrary.AppManager;

/**
 * Describe:设置-配件设置-价签设置-自定义价签模版
 * Created by jingang on 2024/5/16
 */
@SuppressLint("NonConstantResourceId")
public class TagsCustomActivity extends BaseActivity<ActivityTagsCustomBinding> implements View.OnClickListener {

    @Override
    protected ActivityTagsCustomBinding getViewBinding() {
        return ActivityTagsCustomBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivLogo.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
    }

    @Override
    protected void initData() {

    }

    @Override
    public void onClick(View v) {
        if (isQuicklyClick()) {
            return;
        }
        switch (v.getId()) {
            case R.id.ivLogo:
                //菜单
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
        }
    }
}
