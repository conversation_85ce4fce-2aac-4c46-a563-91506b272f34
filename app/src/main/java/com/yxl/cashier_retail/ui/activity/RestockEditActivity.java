package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityRestockEditBinding;
import com.yxl.cashier_retail.ui.adapter.CateAdapter;
import com.yxl.cashier_retail.ui.adapter.RestockChildAdapter;
import com.yxl.cashier_retail.ui.adapter.RestockGoodsAdapter;
import com.yxl.cashier_retail.ui.bean.CateData;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.GoodsInfoData;
import com.yxl.cashier_retail.ui.bean.GoodsListData;
import com.yxl.cashier_retail.ui.bean.RestockData;
import com.yxl.cashier_retail.ui.dialog.RestockGoodsSelectDialog;
import com.yxl.cashier_retail.ui.dialog.SupplierDialog;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:自采补货-补货计划详情
 * Created by jingang on 2024/6/5
 */
@SuppressLint("NonConstantResourceId")
public class RestockEditActivity extends BaseActivity<ActivityRestockEditBinding> implements View.OnClickListener {
    private int id,
            pageRestock = 1,
            order,//1或null.默认排序 2.销量 3.价格 4.库存
            orderType;//1.升序 2.降序
    private String supplierUnique,//供货商编号
            keyWords,//搜索关键字
            cateUnique,//一级分类编号
            cateChildUnique;//二级分类编号

    //补货计划已添加商品列表
    private RestockChildAdapter mAdapter;
    private List<RestockData.GoodsListBean> dataList = new ArrayList<>();

    //商品分类
    private CateAdapter cateAdapter;
    private List<CateData> cateList = new ArrayList<>();

    //商品列表
    private RestockGoodsAdapter goodsAdapter;
    private List<GoodsListData> goodsList = new ArrayList<>();

    @Override
    protected ActivityRestockEditBinding getViewBinding() {
        return ActivityRestockEditBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivBack.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.tvCancel.setOnClickListener(this);
        mBinding.tvPreview.setOnClickListener(this);

        mBinding.linSupplier.setOnClickListener(this);
        mBinding.ivSearchClear.setOnClickListener(this);
        mBinding.linSort.setOnClickListener(this);
        mBinding.linStock.setOnClickListener(this);
        mBinding.linPrice.setOnClickListener(this);
        mBinding.linSale.setOnClickListener(this);
        mBinding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                if (TextUtils.isEmpty(keyWords)) {
                    mBinding.ivSearchClear.setVisibility(View.GONE);
                } else {
                    mBinding.ivSearchClear.setVisibility(View.VISIBLE);
                }
            }
        });
        mBinding.etSearch.setOnEditorActionListener((v, actionId, event) -> {
            page = 1;
            order = 0;
            clearTextBg();
            getGoodsList();
            return true;
        });
        setAdapter();
    }

    @Override
    protected void initData() {
        id = getIntent().getIntExtra("id", 0);
        Log.e(tag, "id = " + id);
        getRestockInfo();
        getCate();
        getGoodsList();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.tvCancel:
                //取消补货
                postRestockStatus(3);
                break;
            case R.id.tvPreview:
                //预览
                if (dataList.size() < 1) {
                    showToast(1, getRstr(R.string.place_add_goods));
                    return;
                }
                startActivityForResult(new Intent(this, RestockPreviewActivity.class)
                                .putExtra("id", id)
                        , Constants.RESTOCK
                );
                break;
            case R.id.linSupplier:
                //选择供货商
                SupplierDialog.showDialog(this, mBinding.ivSupplier, 1, data -> {
                    supplierUnique = data.getSupplierUnique();
                    mBinding.tvSupplier.setText(data.getSupplierName());
                    page = 1;
                    order = 0;
                    clearTextBg();
                    getGoodsList();
                });
//                WebSupplierDialog.showDialog(this, mBinding.ivSupplier, data -> {
//                    supplierUnique = data.getSupplier_unique();
//                    mBinding.tvSupplier.setText(data.getSupplier_name());
//                    page = 1;
//                    order = 0;
//                    clearTextBg();
//                    getGoodsList();
//                });
                break;
            case R.id.ivSearchClear:
                //清除搜索输入
                mBinding.etSearch.setText("");
                keyWords = "";
                page = 1;
                order = 0;
                clearTextBg();
                getGoodsList();
                break;
            case R.id.linSort:
                //排序
                if (order != 1) {
                    order = 1;
                    clearTextBg();
                    mBinding.tvSort.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    orderType = 2;
                    mBinding.ivSort.setImageResource(R.mipmap.ic_arrow005);
                } else {
                    if (orderType == 1) {
                        orderType = 2;
                        mBinding.ivSort.setImageResource(R.mipmap.ic_arrow005);
                    } else {
                        orderType = 1;
                        mBinding.ivSort.setImageResource(R.mipmap.ic_arrow004);
                    }
                }
                page = 1;
                getGoodsList();
                break;
            case R.id.linStock:
                //库存
                if (order != 4) {
                    order = 4;
                    clearTextBg();
                    mBinding.tvStock.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    orderType = 2;
                    mBinding.ivStock.setImageResource(R.mipmap.ic_arrow005);
                } else {
                    if (orderType == 1) {
                        orderType = 2;
                        mBinding.ivStock.setImageResource(R.mipmap.ic_arrow005);
                    } else {
                        orderType = 1;
                        mBinding.ivStock.setImageResource(R.mipmap.ic_arrow004);
                    }
                }
                page = 1;
                getGoodsList();
                break;
            case R.id.linPrice:
                //价格
                if (order != 3) {
                    order = 3;
                    clearTextBg();
                    mBinding.tvPrice.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    orderType = 1;
                    mBinding.ivPrice.setImageResource(R.mipmap.ic_arrow004);
                } else {
                    if (orderType == 1) {
                        orderType = 2;
                        mBinding.ivPrice.setImageResource(R.mipmap.ic_arrow005);
                    } else {
                        orderType = 1;
                        mBinding.ivPrice.setImageResource(R.mipmap.ic_arrow004);
                    }
                }
                page = 1;
                getGoodsList();
                break;
            case R.id.linSale:
                //销量
                if (order != 2) {
                    order = 2;
                    clearTextBg();
                    mBinding.tvSale.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    orderType = 2;
                    mBinding.ivSale.setImageResource(R.mipmap.ic_arrow005);
                } else {
                    if (orderType == 1) {
                        orderType = 2;
                        mBinding.ivSale.setImageResource(R.mipmap.ic_arrow005);
                    } else {
                        orderType = 1;
                        mBinding.ivSale.setImageResource(R.mipmap.ic_arrow004);
                    }
                }
                page = 1;
                getGoodsList();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //补货计划已添加商品列表
        mAdapter = new RestockChildAdapter(this);
        mBinding.rvRestock.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            postRestockGoodsEdit(goodsList.get(position).getGoodsId(), position);
        });
        mBinding.srlRestock.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                pageRestock++;
                getRestockInfo();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                pageRestock = 1;
                getRestockInfo();
            }
        });

        //商品分类
        cateAdapter = new CateAdapter(this);
        mBinding.rvCate.setAdapter(cateAdapter);
        cateAdapter.setListener(new CateAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //展示
                if (!cateList.get(position).isShow()) {
                    for (int i = 0; i < cateList.size(); i++) {
                        cateList.get(i).setShow(false);
                    }
                    cateList.get(position).setShow(true);
                } else {
                    cateList.get(position).setShow(false);
                }
                for (int i = 0; i < cateList.size(); i++) {
                    cateList.get(i).setCheck(false);
                    if (cateList.get(i).getKindDetail() != null) {
                        for (int j = 0; j < cateList.get(i).getKindDetail().size(); j++) {
                            cateList.get(i).getKindDetail().get(j).setCheck(false);
                        }
                    }
                }
                cateList.get(position).setCheck(true);
                cateAdapter.setDataList(cateList);
                cateUnique = cateList.get(position).getGroupUnique();
                cateChildUnique = "-1";//全部
                mBinding.tvCount.setText(cateList.get(position).getGroupName());
                page = 1;
                order = 0;
                clearTextBg();
                getGoodsList();
            }

            @Override
            public void onItemItemClick(View view, int position, int positionChild) {
                if (!cateList.get(position).getKindDetail().get(positionChild).isCheck()) {
                    for (int i = 0; i < cateList.get(position).getKindDetail().size(); i++) {
                        cateList.get(position).getKindDetail().get(i).setCheck(false);
                    }
                    cateList.get(position).getKindDetail().get(positionChild).setCheck(true);
                    cateAdapter.setDataList(cateList);
                    cateChildUnique = cateList.get(position).getKindDetail().get(positionChild).getKindUnique();
                    mBinding.tvCount.setText(cateList.get(position).getKindDetail().get(positionChild).getKindName());
                    page = 1;
                    order = 0;
                    clearTextBg();
                    getGoodsList();
                }
            }
        });

        //商品列表
        goodsAdapter = new RestockGoodsAdapter(this);
        mBinding.recyclerView.setAdapter(goodsAdapter);
        goodsAdapter.setOnItemClickListener((view, position) -> {
            if (isQuicklyClick()) {
                return;
            }
            getGoodsInfo(goodsList.get(position).getGoodsBarcode());
        });
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getGoodsList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getGoodsList();
            }
        });

    }

    /**
     * 清除按钮选中效果
     */
    private void clearTextBg() {
        mBinding.tvSort.setTextColor(getResources().getColor(R.color.black));
        mBinding.ivSort.setImageResource(R.mipmap.ic_arrow003);
        mBinding.ivPrice.setImageResource(R.mipmap.ic_arrow003);
        mBinding.tvStock.setTextColor(getResources().getColor(R.color.black));
        mBinding.ivStock.setImageResource(R.mipmap.ic_arrow003);
        mBinding.tvSort.setTextColor(getResources().getColor(R.color.black));
        mBinding.ivSort.setImageResource(R.mipmap.ic_arrow003);
        mBinding.tvSale.setTextColor(getResources().getColor(R.color.black));
        mBinding.ivSale.setImageResource(R.mipmap.ic_arrow003);
    }

    /**
     * 补货计划详情
     */
    private void getRestockInfo() {
        if (id == 0) {
            showToast(1, getRstr(R.string.restock_id_wrong));
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("restockPlanId", id);
        params.put("pageIndex", pageRestock);
        params.put("pageSize", Constants.limit);
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponseList(this,
                ZURL.getRestockInfo(),
                params,
                RestockData.GoodsListBean.class,
                new RequestListListener<RestockData.GoodsListBean>() {
                    @Override
                    public void onResult(List<RestockData.GoodsListBean> list) {
                        hideDialog();
                        mBinding.srlRestock.finishRefresh();
                        mBinding.srlRestock.finishLoadMore();
                        if (pageRestock == 1) {
                            dataList.clear();
                        }
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            mBinding.rvRestock.setVisibility(View.VISIBLE);
                            mBinding.linEmptyRestock.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.rvRestock.setVisibility(View.GONE);
                            mBinding.linEmptyRestock.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.srlRestock.finishRefresh();
                        mBinding.srlRestock.finishLoadMore();
                        if (dataList.size() > 0) {
                            mBinding.rvRestock.setVisibility(View.VISIBLE);
                            mBinding.linEmptyRestock.setVisibility(View.GONE);
                        } else {
                            mBinding.rvRestock.setVisibility(View.GONE);
                            mBinding.linEmptyRestock.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 获取分类
     */
    private void getCate() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getCateList(),
                params,
                CateData.class,
                new RequestListListener<CateData>() {
                    @Override
                    public void onResult(List<CateData> list) {
                        cateList.clear();
                        cateList.add(new CateData(getRstr(R.string.goods_all), "", -2));
                        cateList.addAll(list);
                        cateAdapter.setDataList(cateList);
                    }
                });
    }

    /**
     * 商品列表
     */
    private void getGoodsList() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("goodsMessage", keyWords);
        params.put("orderType", 1);
        if (order != 0) {
            params.put("order", order);
            if (orderType != 0) {
                params.put("orderType", orderType);
            }
        }
        if (!TextUtils.isEmpty(cateUnique)) {
            params.put("groupUnique", cateUnique);
            if (!TextUtils.isEmpty(cateChildUnique)) {
                params.put("kindUnique", cateChildUnique);
            }
        }
        params.put("pageIndex", page);
        params.put("pageSize", Constants.limit);
        showDialog();
        hideSoftInput(this);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getGoodsList(),
                params,
                GoodsListData.class,
                new RequestListListener<GoodsListData>() {
                    @Override
                    public void onResult(List<GoodsListData> list) {
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                        hideDialog();
                        if (page == 1) {
                            goodsList.clear();
                        }
                        goodsList.addAll(list);
                        if (goodsList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            goodsAdapter.setDataList(goodsList);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                        showToast(1, msg);
                        hideDialog();
                        if (goodsList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 商品详情
     *
     * @param barcode
     */
    private void getGoodsInfo(String barcode) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("goodsBarcode", barcode);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getGoodsInfo(),
                map,
                GoodsInfoData.class,
                new RequestListener<GoodsInfoData>() {
                    @Override
                    public void success(GoodsInfoData data) {
                        hideDialog();
                        RestockGoodsSelectDialog.showDialog(RestockEditActivity.this, id, data, barcode, () -> {
                            pageRestock = 1;
                            getRestockInfo();
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.RESTOCK_LIST));
                        });
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 修改补货计划状态
     *
     * @param status 1.未生成 2.已生成 3.已取消
     */
    private void postRestockStatus(int status) {
        if (id == 0) {
            showToast(1, getRstr(R.string.restock_id_wrong));
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("restockPlanId", id);
        params.put("planStatus", status);
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getRestockUpdate(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.RESTOCK_LIST));
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 补货计划商品编辑
     *
     * @param goodsId
     * @param position
     */
    private void postRestockGoodsEdit(int goodsId, int position) {
        if (id == 0) {
            showToast(1, getRstr(R.string.restock_id_wrong));
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("shopRestockplanId", id);
        map.put("shopRestockplanGoodsId", goodsId);
        map.put("delFlag", 2);
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getRestockGoodsEdit(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        goodsList.remove(position);
                        goodsAdapter.remove(position);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.RESTOCK_LIST));
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null && requestCode == Constants.RESTOCK) {
            finish();
        }
    }
}
