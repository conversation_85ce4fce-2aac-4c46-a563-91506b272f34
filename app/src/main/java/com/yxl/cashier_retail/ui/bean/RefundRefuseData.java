package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;

/**
 * Describe:退款-拒绝话术（实体类）
 * Created by jingang on 2024/6/19
 */
public class RefundRefuseData implements Serializable {
    private int id;
    private String msg;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
