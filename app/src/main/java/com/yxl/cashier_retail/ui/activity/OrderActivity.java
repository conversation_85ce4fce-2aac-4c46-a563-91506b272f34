package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.yxl.cashier_retail.MyApplication;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityOrderBinding;
import com.yxl.cashier_retail.ui.dialog.MenuDialog;
import com.yxl.cashier_retail.ui.fragment.BeansFragment;
import com.yxl.cashier_retail.ui.fragment.DeliveryFragment;
import com.yxl.cashier_retail.ui.fragment.NetListFragment;
import com.yxl.cashier_retail.ui.fragment.RefundFragment;
import com.yxl.cashier_retail.ui.fragment.RiderFragment;
import com.yxl.commonlibrary.AppManager;

/**
 * Describe:菜单-网单
 * Created by jingang on 2024/6/15
 */
@SuppressLint("NonConstantResourceId")
public class OrderActivity extends BaseActivity<ActivityOrderBinding> implements View.OnClickListener {

    //    private OrderFragment orderFragment;
    private NetListFragment netListFragment;
    private RefundFragment refundFragment;
    private RiderFragment riderFragment;
    private BeansFragment beansFragment;
    private DeliveryFragment deliveryFragment;
    private Fragment[] fragments;
    private int index = 0,//点击的页卡索引
            currentTabIndex = 0;//当前的页卡索引
    //搜索关键字
    private String keyWordsOrder,//订单
            keyWordsRider;//骑手

    @Override
    protected ActivityOrderBinding getViewBinding() {
        return ActivityOrderBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivLogo.setOnClickListener(this);
        mBinding.ivSearchClear.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.relOrder.setOnClickListener(this);
        mBinding.relRefund.setOnClickListener(this);
        mBinding.relRider.setOnClickListener(this);
        mBinding.relBeans.setOnClickListener(this);
        mBinding.relDelivery.setOnClickListener(this);
        mBinding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                mBinding.ivSearchClear.setVisibility(TextUtils.isEmpty(s.toString().trim()) ? View.GONE : View.VISIBLE);
            }
        });
        mBinding.etSearch.setOnEditorActionListener((v, actionId, event) -> {
            getKeyWords();
            return true;
        });
        setFragment();
    }

    @Override
    protected void initData() {
        setNetwork();
    }

    @Override
    public void getNetwork() {
        setNetwork();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivLogo:
                //菜单
                MenuDialog.showDialog(this, type -> {
                    //0.收银 1.入库 2.查询 3.统计 4.网单 5.会员 6.补货 7.营销 8.交班 9.设置
                    if (type != 4) {
                        switch (type) {
                            case 1:
                                goToActivity(InActivity.class);
                                break;
                            case 2:
                                goToActivity(QueryActivity.class);
                                break;
                            case 3:
                                goToActivity(StatisticsActivity.class);
                                break;
                            case 5:
                                goToActivity(MemberActivity.class);
                                break;
                            case 6:
                                goToActivity(MallActivity.class);
                                break;
                            case 7:
                                goToActivity(MarketingActivity.class);
                                break;
                            case 8:
                                goToActivity(ShiftActivity.class);
                                break;
                            case 9:
                                goToActivity(SettingActivity.class);
                                break;
                        }
                        finish();
                    }
                });
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.ivSearchClear:
                //清除搜索输入
                mBinding.etSearch.setText("");
                getKeyWords();
                break;
            case R.id.relOrder:
                //网单
                index = 0;
                fragmentControl();
                break;
            case R.id.relRefund:
                //退款
                index = 1;
                fragmentControl();
                break;
            case R.id.relRider:
                //骑手管理
                index = 2;
                fragmentControl();
                break;
            case R.id.relBeans:
                //百货豆
                index = 3;
                fragmentControl();
                break;
            case R.id.relDelivery:
                //配送设置
                index = 4;
                fragmentControl();
                break;
        }
    }

    /**
     * 网络监听
     */
    private void setNetwork() {
        if (MyApplication.mNetwork != null) {
            if (MyApplication.mNetwork.isConnect()) {
                switch (MyApplication.mNetwork.getConnectType()) {
                    case 1:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network001);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 2:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network002);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 3:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network003);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    default:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                        mBinding.tvNetwork.setText(getRstr(R.string.no_network));
                        break;
                }
            } else {
                mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                mBinding.tvNetwork.setText(getRstr(R.string.no_network));
            }
        } else {
            mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
            mBinding.tvNetwork.setText(getRstr(R.string.no_network));
        }
    }

    /**
     * 搜索
     */
    private void getKeyWords() {
        hideSoftInput(this);
        //0.订单 1.退单 2.骑手
        switch (index) {
            case 0:
                keyWordsOrder = mBinding.etSearch.getText().toString().trim();
//                if (netListFragment != null) {
//                    NetListFragment.setKeyWords(keyWordsOrder);
//                }
                break;
            case 2:
                keyWordsRider = mBinding.etSearch.getText().toString().trim();
                if (riderFragment != null) {
                    riderFragment.setKeyWords(keyWordsRider);
                }
                break;
        }
    }

    /**
     * 设置fragment
     */
    public void setFragment() {
//        orderFragment = new OrderFragment();
        netListFragment = new NetListFragment();
        refundFragment = new RefundFragment();
        riderFragment = new RiderFragment();
        beansFragment = new BeansFragment();
        deliveryFragment = new DeliveryFragment();
        fragments = new Fragment[]{netListFragment,
                refundFragment,
                riderFragment,
                beansFragment,
                deliveryFragment};
        setBottomColor();

        getSupportFragmentManager().beginTransaction().add(R.id.fragment_container, fragments[index]).show(fragments[index]).commit();
    }

    /**
     * 控制fragment的变化
     */
    public void fragmentControl() {
        if (currentTabIndex != index) {
            removeBottomColor();
            setBottomColor();

            FragmentTransaction trx = getSupportFragmentManager().beginTransaction();
            trx.hide(fragments[currentTabIndex]);
            if (!fragments[index].isAdded()) {
                trx.add(R.id.fragment_container, fragments[index]);
            }
            trx.show(fragments[index]).commitAllowingStateLoss();
            currentTabIndex = index;
        }
    }

    /**
     * 设置底部栏按钮变色
     */
    private void setBottomColor() {
        switch (index) {
            case 0:
                mBinding.relOrder.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivOrder.setImageResource(R.mipmap.ic_order_tab001);
                mBinding.tvOrder.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.netlist_order));
//                mBinding.linSearch.setVisibility(View.VISIBLE);
//                mBinding.etSearch.setText(keyWordsOrder);
//                mBinding.etSearch.setSelection(mBinding.etSearch.getText().toString().trim().length());
                mBinding.linSearch.setVisibility(View.GONE);
                break;
            case 1:
                mBinding.relRefund.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivRefund.setImageResource(R.mipmap.ic_order_tab011);
                mBinding.tvRefund.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.netlist_refund));
                mBinding.linSearch.setVisibility(View.GONE);
                break;
            case 2:
                mBinding.relRider.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivRider.setImageResource(R.mipmap.ic_order_tab021);
                mBinding.tvRider.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.rider_manage));
                mBinding.linSearch.setVisibility(View.VISIBLE);
                mBinding.etSearch.setText(keyWordsRider);
                mBinding.etSearch.setSelection(mBinding.etSearch.getText().toString().trim().length());
                break;
            case 3:
                mBinding.relBeans.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivBeans.setImageResource(R.mipmap.ic_order_tab031);
                mBinding.tvBeans.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.beans));
                mBinding.linSearch.setVisibility(View.GONE);
                break;
            case 4:
                mBinding.relDelivery.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivDelivery.setImageResource(R.mipmap.ic_order_tab041);
                mBinding.tvDelivery.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.delivery_setting));
                mBinding.linSearch.setVisibility(View.GONE);
                break;
            default:
                break;
        }
    }

    /**
     * 清除底部栏颜色
     */
    private void removeBottomColor() {
        switch (currentTabIndex) {
            case 0:
                mBinding.relOrder.setBackgroundResource(0);
                mBinding.ivOrder.setImageResource(R.mipmap.ic_order_tab002);
                mBinding.tvOrder.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 1:
                mBinding.relRefund.setBackgroundResource(0);
                mBinding.ivRefund.setImageResource(R.mipmap.ic_order_tab012);
                mBinding.tvRefund.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 2:
                mBinding.relRider.setBackgroundResource(0);
                mBinding.ivRider.setImageResource(R.mipmap.ic_order_tab022);
                mBinding.tvRider.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 3:
                mBinding.relBeans.setBackgroundResource(0);
                mBinding.ivBeans.setImageResource(R.mipmap.ic_order_tab032);
                mBinding.tvBeans.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 4:
                mBinding.relDelivery.setBackgroundResource(0);
                mBinding.ivDelivery.setImageResource(R.mipmap.ic_order_tab042);
                mBinding.tvDelivery.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            default:
                break;
        }
    }
}
