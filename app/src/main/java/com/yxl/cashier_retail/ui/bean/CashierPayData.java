package com.yxl.cashier_retail.ui.bean;

/**
 * Describe: 普通线下订单结算(现金、微信、支付宝、银行卡、储值卡)-实体类
 * Created by jingang on 2025/6/7
 */
public class CashierPayData {
    /**
     * status : 1
     * msg : null
     * totals : 0
     * perPageNum : 0
     * data : 20250607135555644
     * goodsData : null
     * cusData : {"cus_level_name":"钻石会员","cusName":"哈哈哈","cusRegeditDate":"2024-07-03","cusLevelId":1552,"cusBirthday":"2001-01-01","cus_balance":8.42,"cusUnique":"17865069350","cusPhone":"17865069350","cusBalance":8.42,"cusId":444279,"shopUnique":1536215939565,"cusLevelDiscount":0.95,"totalPoints":184.96,"sale_points":1.9,"cusPoints":184.96}
     * resultCode : 0
     * sale_list_unique : 20250607135555644
     */

    private int status;
    private String msg;
    private int totals;
    private int perPageNum;
    private Object data;
    private Object goodsData;
    private CusDataBean cusData;
    private int resultCode;
    private String sale_list_unique;//订单编号

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getTotals() {
        return totals;
    }

    public void setTotals(int totals) {
        this.totals = totals;
    }

    public int getPerPageNum() {
        return perPageNum;
    }

    public void setPerPageNum(int perPageNum) {
        this.perPageNum = perPageNum;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public Object getGoodsData() {
        return goodsData;
    }

    public void setGoodsData(Object goodsData) {
        this.goodsData = goodsData;
    }

    public CusDataBean getCusData() {
        return cusData;
    }

    public void setCusData(CusDataBean cusData) {
        this.cusData = cusData;
    }

    public int getResultCode() {
        return resultCode;
    }

    public void setResultCode(int resultCode) {
        this.resultCode = resultCode;
    }

    public String getSale_list_unique() {
        return sale_list_unique;
    }

    public void setSale_list_unique(String sale_list_unique) {
        this.sale_list_unique = sale_list_unique;
    }

    public static class CusDataBean {
        /**
         * cus_level_name : 钻石会员
         * cusName : 哈哈哈
         * cusRegeditDate : 2024-07-03
         * cusLevelId : 1552
         * cusBirthday : 2001-01-01
         * cus_balance : 8.42
         * cusUnique : 17865069350
         * cusPhone : 17865069350
         * cusBalance : 8.42
         * cusId : 444279
         * shopUnique : 1536215939565
         * cusLevelDiscount : 0.95
         * totalPoints : 184.96
         * sale_points : 1.9
         * cusPoints : 184.96
         */

        private String cus_level_name;
        private String cusName;
        private String cusRegeditDate;
        private int cusLevelId;
        private String cusBirthday;
        private double cus_balance;
        private String cusUnique;
        private String cusPhone;
        private double cusBalance;
        private int cusId;
        private long shopUnique;
        private double cusLevelDiscount;
        private double totalPoints;//总积分
        private double sale_points;//获得积分
        private double cusPoints;//剩余积分

        public String getCus_level_name() {
            return cus_level_name;
        }

        public void setCus_level_name(String cus_level_name) {
            this.cus_level_name = cus_level_name;
        }

        public String getCusName() {
            return cusName;
        }

        public void setCusName(String cusName) {
            this.cusName = cusName;
        }

        public String getCusRegeditDate() {
            return cusRegeditDate;
        }

        public void setCusRegeditDate(String cusRegeditDate) {
            this.cusRegeditDate = cusRegeditDate;
        }

        public int getCusLevelId() {
            return cusLevelId;
        }

        public void setCusLevelId(int cusLevelId) {
            this.cusLevelId = cusLevelId;
        }

        public String getCusBirthday() {
            return cusBirthday;
        }

        public void setCusBirthday(String cusBirthday) {
            this.cusBirthday = cusBirthday;
        }

        public double getCus_balance() {
            return cus_balance;
        }

        public void setCus_balance(double cus_balance) {
            this.cus_balance = cus_balance;
        }

        public String getCusUnique() {
            return cusUnique;
        }

        public void setCusUnique(String cusUnique) {
            this.cusUnique = cusUnique;
        }

        public String getCusPhone() {
            return cusPhone;
        }

        public void setCusPhone(String cusPhone) {
            this.cusPhone = cusPhone;
        }

        public double getCusBalance() {
            return cusBalance;
        }

        public void setCusBalance(double cusBalance) {
            this.cusBalance = cusBalance;
        }

        public int getCusId() {
            return cusId;
        }

        public void setCusId(int cusId) {
            this.cusId = cusId;
        }

        public long getShopUnique() {
            return shopUnique;
        }

        public void setShopUnique(long shopUnique) {
            this.shopUnique = shopUnique;
        }

        public double getCusLevelDiscount() {
            return cusLevelDiscount;
        }

        public void setCusLevelDiscount(double cusLevelDiscount) {
            this.cusLevelDiscount = cusLevelDiscount;
        }

        public double getTotalPoints() {
            return totalPoints;
        }

        public void setTotalPoints(double totalPoints) {
            this.totalPoints = totalPoints;
        }

        public double getSale_points() {
            return sale_points;
        }

        public void setSale_points(double sale_points) {
            this.sale_points = sale_points;
        }

        public double getCusPoints() {
            return cusPoints;
        }

        public void setCusPoints(double cusPoints) {
            this.cusPoints = cusPoints;
        }
    }
}
