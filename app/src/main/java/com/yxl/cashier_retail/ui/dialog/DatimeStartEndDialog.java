package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogDatimeStartEndBinding;
import com.yxl.cashier_retail.view.pickerview.entiy.DatimeEntity;

import java.text.SimpleDateFormat;
import java.util.Date;

import butterknife.ButterKnife;

/**
 * Describe:日期时间(开始结束)选择弹窗
 * Created by jingang on 2024/5/21
 */
@SuppressLint({"NonConstantResourceId", "SimpleDateFormat"})
public class DatimeStartEndDialog extends BaseDialog<DialogDatimeStartEndBinding> implements View.OnClickListener {
    private static int type;//0.开始 1.结束
    private static String startDate, endDate;

    @Override
    protected DialogDatimeStartEndBinding getViewBinding() {
        return DialogDatimeStartEndBinding.inflate(getLayoutInflater());
    }

    public static void showDialog(Context mContext, String startTime, String endTime, int type, MyListener listener) {
        DatimeStartEndDialog.startDate = startTime;
        DatimeStartEndDialog.endDate = endTime;
        DatimeStartEndDialog.type = type;
        DatimeStartEndDialog dialog = new DatimeStartEndDialog(mContext);
        dialog.setRange(startTime, endTime);
        dialog.setListener(listener);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        dialog.show();
    }

    public DatimeStartEndDialog(Context context) {
        super(context, R.style.dialog_bottom);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        setContentView(R.layout.dialog_datime_start_end);
        ButterKnife.bind(this);

        mBinding.tvDialogDateStart.setText(startDate);
        mBinding.tvDialogDateEnd.setText(endDate);
        if (type == 0) {
            mBinding.tvDialogDateStart.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
            mBinding.vDialogDateStart.setBackgroundColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
            mBinding.tvDialogDateEnd.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
            mBinding.vDialogDateEnd.setBackgroundColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        } else {
            mBinding.tvDialogDateStart.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
            mBinding.vDialogDateStart.setBackgroundColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
            mBinding.tvDialogDateEnd.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
            mBinding.vDialogDateEnd.setBackgroundColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
        }
        mBinding.wheelLayout.setDefaultValue(DatimeEntity.now()); //当前日期
        mBinding.wheelLayout.setSelectedTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));//选中字体颜色
        mBinding.wheelLayout.setSelectedTextSize(14 * getContext().getResources().getDisplayMetrics().scaledDensity);//选中字体大小
        mBinding.wheelLayout.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));//字体颜色
        mBinding.wheelLayout.setTextSize(12 * getContext().getResources().getDisplayMetrics().scaledDensity);//字体大小
        mBinding.wheelLayout.setIndicatorColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.transparent)); //横线颜色
        mBinding.wheelLayout.setOnDatimeSelectedListener((year, month, day, hour, minute, second) -> {
            String month_str, day_str, hour_str, minute_str;
            if (month < 10) {
                month_str = "0" + month;
            } else {
                month_str = String.valueOf(month);
            }
            if (day < 10) {
                day_str = "0" + day;
            } else {
                day_str = String.valueOf(day);
            }
            if (hour < 10) {
                hour_str = "0" + hour;
            } else {
                hour_str = String.valueOf(hour);
            }
            if (minute < 10) {
                minute_str = "0" + minute;
            } else {
                minute_str = String.valueOf(minute);
            }
            if (type == 0) {
                startDate = year + "-" + month_str + "-" + day_str + " " + hour_str + ":" + minute_str;
                mBinding.tvDialogDateStart.setText(startDate);
            } else {
                endDate = year + "-" + month_str + "-" + day_str + " " + hour_str + ":" + minute_str;
                mBinding.tvDialogDateEnd.setText(endDate);
            }
        });
        mBinding.tvDialogCancel.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        mBinding.linDialogDateStart.setOnClickListener(this);
        mBinding.linDialogDateEnd.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvDialogCancel:
                //取消
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //确认
                if (TextUtils.isEmpty(startDate)) {
                    showToast(1,getRstr(R.string.select_date_start));
                    return;
                }
                if (TextUtils.isEmpty(endDate)) {
                    showToast(1,getRstr(R.string.select_date_end));
                    return;
                }
                if (listener != null) {
                    listener.onClick(startDate, endDate);
                }
                dismiss();
                break;
            case R.id.linDialogDateStart:
                //选择开始时间
                if (type != 0) {
                    type = 0;
                    mBinding.tvDialogDateStart.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    mBinding.vDialogDateStart.setBackgroundColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    mBinding.tvDialogDateEnd.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
                    mBinding.vDialogDateEnd.setBackgroundColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
                    setRange(startDate, endDate);
                }
                break;
            case R.id.linDialogDateEnd:
                //选择结束时间
                if (type != 1) {
                    type = 1;
                    mBinding.tvDialogDateStart.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
                    mBinding.vDialogDateStart.setBackgroundColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
                    mBinding.tvDialogDateEnd.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    mBinding.vDialogDateEnd.setBackgroundColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    setRange(startDate, "");
                }
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.BOTTOM);
        getWindow().setWindowAnimations(R.style.dialog_anim);
    }

    /**
     * 设置日期时间范围
     * <p>
     * 0.查询：开始 1.查询：结束 2.活动：开始 3.活动：结束
     *
     * @param startTime
     * @param endTime
     */
    private void setRange(String startTime, String endTime) {
        DatimeEntity datimeStart = new DatimeEntity(),
                datimeEnd = new DatimeEntity(),
                datimeNow = new DatimeEntity();

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        Date dStart, dEnd, dNow;

        /*****************开始日期时间*****************/
        try {
            dStart = dateFormat.parse(startTime);
            if (dStart != null) {
                if (type == 1) {
                    datimeStart.setYear(dStart.getYear() + 1900);
                    datimeStart.setHour(0);
                } else {
                    datimeStart.setYear(dStart.getYear() + 1890);
                    datimeStart.setHour(dStart.getHours());
                }
                if (dStart.getMonth() == 0) {
                    datimeStart.setMonth(1);
                } else {
                    datimeStart.setMonth(dStart.getMonth() + 1);
                }
                datimeStart.setDay(dStart.getDate());
            } else {
                datimeStart = DatimeEntity.yearOnFuture(-10);
            }
        } catch (Exception e) {
            e.printStackTrace();
            datimeStart = DatimeEntity.yearOnFuture(-10);
        }

        /*****************结束日期时间*****************/
        try {
            dEnd = dateFormat.parse(endTime);
            if (dEnd != null) {
                datimeEnd.setYear(dEnd.getYear() + 1900);
                if (dEnd.getMonth() == 0) {
                    datimeEnd.setMonth(1);
                } else {
                    datimeEnd.setMonth(dEnd.getMonth() + 1);
                }
                datimeEnd.setDay(dEnd.getDate());
                datimeEnd.setHour(dEnd.getHours());
                datimeEnd.setMinute(dEnd.getMinutes());
                datimeEnd.setSecond(59);
            } else {
                datimeEnd = DatimeEntity.now();
            }
        } catch (Exception e) {
            e.printStackTrace();
            datimeEnd = DatimeEntity.now();
        }

        /*****************现在日期时间*****************/
        try {
            if (type == 0) {
                dNow = dateFormat.parse(startTime);
            } else {
                dNow = dateFormat.parse(endTime);
            }
            if (dNow != null) {
                datimeNow.setYear(dNow.getYear() + 1900);
                if (dNow.getMonth() == 0) {
                    datimeNow.setMonth(1);
                } else {
                    datimeNow.setMonth(dNow.getMonth() + 1);
                }
                datimeNow.setDay(dNow.getDate());
                datimeNow.setHour(dNow.getHours());
                datimeNow.setMinute(dNow.getMinutes());
            } else {
                datimeNow = DatimeEntity.now();
            }
        } catch (Exception e) {
            e.printStackTrace();
            datimeNow = DatimeEntity.now();
        }
        mBinding.wheelLayout.setRange(datimeStart, datimeEnd, datimeNow); //日期范围
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onClick(String startDate, String endDate);
    }
}
