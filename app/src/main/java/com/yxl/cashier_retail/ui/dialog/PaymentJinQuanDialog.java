package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.util.Log;
import android.view.Gravity;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogPaymentJinquanBinding;
import com.yxl.cashier_retail.ui.bean.SaleListUniqueData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Describe:dialog（金圈收款）
 * Created by jingang on 2024/09/06
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class PaymentJinQuanDialog extends BaseDialog<DialogPaymentJinquanBinding> {
    private static Activity mActivity;
    private static int type;//0.金圈收款 1.组合收款(包含金圈收款)
    private static double total;
    private static String memberUnique;//会员编号

    public static void showDialog(Activity activity, int type, double total, String memberUnique, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        PaymentJinQuanDialog.listener = listener;
        PaymentJinQuanDialog.type = type;
        PaymentJinQuanDialog.total = total;
        PaymentJinQuanDialog.memberUnique = memberUnique;
        PaymentJinQuanDialog dialog = new PaymentJinQuanDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 10 * 3, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 8);
        dialog.show();
    }

    public PaymentJinQuanDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(false);
        mBinding.etDialogScan.requestFocus();
        //获取焦点不弹出软键盘
        mBinding.etDialogScan.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                InputMethodManager imm = (InputMethodManager) v.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                if (imm != null) {
                    imm.hideSoftInputFromWindow(v.getWindowToken(), 0);
                }
            }
        });
        mBinding.etDialogScan.setScanResultListener(result -> {
            Log.e(tag, "扫码结果 = " + result);
            if (type == 0) {
                if (listener != null) {
                    listener.onConfirm(type, total, result);
                    dismiss();
                }
            } else {
                getSaleListUnique(result);
            }
        });
        Glide.with(mActivity)
                .asGif()
                .load(R.drawable.cashiering002)
                .into(mBinding.ivDialogImg);
        mBinding.tvDialogTotal.setText(getRstr(R.string.money) + DFUtils.getNum2(total));
        //取消收款
        mBinding.tvDialogCancel.setOnClickListener(v -> {
            dismiss();
        });
    }

    @Override
    protected DialogPaymentJinquanBinding getViewBinding() {
        return DialogPaymentJinquanBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    /**
     * 创建订单编号
     */
    private void getSaleListUnique(String scanCode) {
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getSaleListUniqueCreate(),
                new HashMap<>(),
                SaleListUniqueData.class,
                new RequestListener<SaleListUniqueData>() {
                    @Override
                    public void success(SaleListUniqueData data) {
                        postPaymentScan(data.getSale_list_unique(), scanCode);
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 收银机单独扫码支付
     *
     * @param saleListUnique
     * @param scanCode
     */
    //结算成功：{"status":1,"msg":"20240906174254083:结算成功！","totals":0,"perPageNum":0,"data":20240906174254083,"goodsData":3,"cusData":null,"resultCode":0,"sale_list_unique":null}
    private void postPaymentScan(String saleListUnique, String scanCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("sale_list_unique", saleListUnique);
        params.put("cus_unique", memberUnique);
        params.put("money", total);
        params.put("auth_code", scanCode);
        showDialog();
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getPaymentScanAlone(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        PaymentDoingDialog.showDialog(mActivity, 1, total, saleListUnique, new PaymentDoingDialog.MyListener() {
                            @Override
                            public void onPaymentSuccess(double salePoints, double cusPoints) {
                                if (listener != null) {
                                    listener.onConfirm(type, total, saleListUnique);
                                    dismiss();
                                }
                            }

                            @Override
                            public void onPaymentFail(String msg) {
                                showToast(1, msg);
                            }
                        });
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        /**
         * @param type           0.金圈收款 1.组合收款(包含金圈收款)
         * @param money          金额
         * @param saleListUnique 订单编号
         */
        void onConfirm(int type, double money, String saleListUnique);
    }
}
