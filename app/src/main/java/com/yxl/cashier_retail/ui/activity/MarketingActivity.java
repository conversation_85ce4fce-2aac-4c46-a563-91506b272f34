package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.view.View;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.MyApplication;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityMarketingBinding;
import com.yxl.cashier_retail.ui.adapter.MarketingAdapter;
import com.yxl.cashier_retail.ui.bean.ActivityListData;
import com.yxl.cashier_retail.ui.bean.ConditionData;
import com.yxl.cashier_retail.ui.bean.MarketingData;
import com.yxl.cashier_retail.ui.dialog.DateStartEndDialog;
import com.yxl.cashier_retail.ui.dialog.MenuDialog;
import com.yxl.cashier_retail.ui.popupwindow.ConditionPop;
import com.yxl.cashier_retail.utils.DateUtils;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:菜单-营销
 * Created by jingang on 2024/6/1
 */
@SuppressLint("NonConstantResourceId")
public class MarketingActivity extends BaseActivity<ActivityMarketingBinding> implements View.OnClickListener {
    private String startDate, endDate;

    //类型
    private List<ConditionData> conditionList = new ArrayList<>();
    private int type;//活动类型:1、商品折扣；2、商品满赠；3、订单促销；4、单品促销

    private List<ActivityListData> dataList = new ArrayList<>();
    private MarketingAdapter mAdapter;

    @Override
    protected ActivityMarketingBinding getViewBinding() {
        return ActivityMarketingBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivLogo.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.linType.setOnClickListener(this);
        mBinding.linStartDate.setOnClickListener(this);
        mBinding.linEndDate.setOnClickListener(this);
        startDate = DateUtils.getOldDate(-300);
        endDate = DateUtils.getOldDate(0);
        mBinding.tvStartDate.setText(startDate);
        mBinding.tvEndDate.setText(endDate);
        setAdapter();
    }

    @Override
    protected void initData() {
        setNetwork();
        conditionList.clear();
        conditionList.add(new ConditionData(0, getRstr(R.string.cate_all), false));
        conditionList.add(new ConditionData(1, getRstr(R.string.goods_discount), false));
        conditionList.add(new ConditionData(2, getRstr(R.string.goods_gift), false));
        conditionList.add(new ConditionData(3, getRstr(R.string.order_promotion), false));
        conditionList.add(new ConditionData(4, getRstr(R.string.goods_promotion), false));
        getActivityList();
    }

    @Override
    public void getNetwork() {
        setNetwork();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivLogo:
                //菜单
                MenuDialog.showDialog(this, type -> {
                    //0.收银 1.入库 2.查询 3.统计 4.网单 5.会员 6.补货 7.营销 8.交班 9.设置
                    if (type != 7) {
                        switch (type) {
                            case 1:
                                goToActivity(InActivity.class);
                                break;
                            case 2:
                                goToActivity(QueryActivity.class);
                                break;
                            case 3:
                                goToActivity(StatisticsActivity.class);
                                break;
                            case 4:
                                goToActivity(OrderActivity.class);
                                break;
                            case 5:
                                goToActivity(MemberActivity.class);
                                break;
                            case 6:
                                goToActivity(MallActivity.class);
                                break;
                            case 8:
                                goToActivity(ShiftActivity.class);
                                break;
                            case 9:
                                goToActivity(SettingActivity.class);
                                break;
                        }
                        finish();
                    }
                });
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.linType:
                //选择类型
                ConditionPop.showDialog(mContext,
                        mBinding.ivType,
                        v,
                        mBinding.linType.getMeasuredWidth(),
                        conditionList,
                        type,
                        data -> {
                            type = data.getId();
                            mBinding.tvType.setText(data.getName());
                            page = 1;
                            getActivityList();
                        });
                break;
            case R.id.linStartDate:
                //开始日期
                showDialogDate(0, mBinding.ivStartDate);
                break;
            case R.id.linEndDate:
                //结束日期
                showDialogDate(1, mBinding.ivEndDate);
                break;
        }
    }

    /**
     * 网络监听
     */
    private void setNetwork() {
        if (MyApplication.mNetwork != null) {
            if (MyApplication.mNetwork.isConnect()) {
                switch (MyApplication.mNetwork.getConnectType()) {
                    case 1:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network001);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 2:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network002);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 3:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network003);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    default:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                        mBinding.tvNetwork.setText(getRstr(R.string.no_network));
                        break;
                }
            } else {
                mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                mBinding.tvNetwork.setText(getRstr(R.string.no_network));
            }
        } else {
            mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
            mBinding.tvNetwork.setText(getRstr(R.string.no_network));
        }
    }

    /**
     * dialog（选择日期时间）
     */
    private void showDialogDate(int type, View view) {
        DateStartEndDialog.showDialog(this,
                view,
                startDate,
                endDate,
                type,
                (startDate, endDate) -> {
                    this.startDate = startDate;
                    this.endDate = endDate;
                    mBinding.tvStartDate.setText(startDate);
                    mBinding.tvEndDate.setText(endDate);
                    //请求数据
                    page = 1;
                    getActivityList();
                });
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new MarketingAdapter(this);
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {

        });
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getActivityList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getActivityList();
            }
        });
    }

    /**
     * 更新UI
     *
     * @param list
     */
    private void setUI(List<ActivityListData> list) {
        if (list == null) {
            mBinding.recyclerView.setVisibility(View.GONE);
            mBinding.linEmpty.setVisibility(View.VISIBLE);
            return;
        }
        if (page == 1) {
            dataList.clear();
        }
        dataList.addAll(list);
        if (dataList.size() > 0) {
            mBinding.recyclerView.setVisibility(View.VISIBLE);
            mBinding.linEmpty.setVisibility(View.GONE);
            mAdapter.setDataList(dataList);
        } else {
            mBinding.recyclerView.setVisibility(View.GONE);
            mBinding.linEmpty.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 店铺商品促销列表
     */
    private void getActivityList() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("startTime", startDate);
        params.put("endTime", endDate);
        if (type != 0) {
            params.put("type", type);//活动类型:1、商品折扣；2、商品满赠；3、订单促销；4、单品促销
        }
        params.put("page", page);
        params.put("pageSize", Constants.limit);
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponseList(this,
                ZURL.getPromotionActivityList(),
                params,
                ActivityListData.class,
                new RequestListListener<ActivityListData>() {
                    @Override
                    public void onResult(List<ActivityListData> list) {
                        hideDialog();
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                        setUI(list);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                        setUI(null);
                    }
                });
    }
}
