package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogPurchaseVoucherBinding;
import com.yxl.cashier_retail.ui.activity.ImgBigActivity;
import com.yxl.cashier_retail.ui.adapter.ImgAdapter;
import com.yxl.cashier_retail.ui.bean.PurchaseVoucherData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Describe:dialog（购销单详情-查看单据凭证）
 * Created by jingang on 2024/6/14
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class PurchaseVoucherDialog extends BaseDialog<DialogPurchaseVoucherBinding> implements View.OnClickListener {
    private static PurchaseVoucherData data;
    private ImgAdapter mAdapter;
    private List<String> dataList = new ArrayList<>();

    public static void showDialog(Activity activity, PurchaseVoucherData data) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        PurchaseVoucherDialog.data = data;
        PurchaseVoucherDialog dialog = new PurchaseVoucherDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public PurchaseVoucherDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        setAdapter();
        setUI();
    }

    @Override
    protected DialogPurchaseVoucherBinding getViewBinding() {
        return DialogPurchaseVoucherBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mBinding.rvDialog.setLayoutManager(new GridLayoutManager(getContext(), 3));
        mAdapter = new ImgAdapter(getContext());
        mBinding.rvDialog.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            getContext().startActivity(new Intent(getContext(), ImgBigActivity.class)
                    .putExtra("img", (Serializable) dataList)
                    .putExtra("index", position)
            );
        });
    }

    /**
     * 更新UI
     */
    private void setUI() {
        if (data == null) {
            return;
        }
        mBinding.tvDialogMoney.setText("¥" + DFUtils.getNum2(data.getPaymentMoney()));
        if (TextUtils.isEmpty(data.getPaymentRemark())) {
            mBinding.tvDialogRemarks.setText(getRstr(R.string.nothing));
        } else {
            mBinding.tvDialogRemarks.setText(data.getPaymentRemark());
        }

        dataList.clear();
        dataList.addAll(data.getImageUrlList());
        mAdapter.setDataList(dataList);
    }
}
