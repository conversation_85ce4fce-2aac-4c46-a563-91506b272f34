package com.yxl.cashier_retail.ui.bean;

import java.util.List;

/**
 * Describe:供货商详情-未还款购销单列表（实体类）
 * Created by jingang on 2024/9/14
 */
public class SupplierPurchaseData {
    /**
     * outstandingCount : 5
     * billList : [{"supplierUnique":"41","supplierName":"测试001","supplierPhone":"13888888881","id":72,"status":2,"goodsCategory":1,"totalPrice":20,"outstandingAmount":20,"goodsList":[{"goodsBarcode":"54879","goodsName":"杯子","goodsPicturepath":"upload/no_goodsB.jpg","purchaseGoodsCount":5}]},{"supplierUnique":"41","supplierName":"测试001","supplierPhone":"13888888881","id":83,"status":2,"goodsCategory":1,"totalPrice":20,"outstandingAmount":20,"goodsList":[{"goodsBarcode":"54879","goodsName":"杯子","goodsPicturepath":"upload/no_goodsB.jpg","purchaseGoodsCount":5}]},{"supplierUnique":"41","supplierName":"测试001","supplierPhone":"13888888881","id":84,"status":2,"goodsCategory":1,"totalPrice":30,"outstandingAmount":30,"goodsList":[{"goodsBarcode":"54879","goodsName":"杯子","goodsPicturepath":"upload/no_goodsB.jpg","purchaseGoodsCount":3}]},{"supplierUnique":"41","supplierName":"测试001","supplierPhone":"13888888881","id":85,"status":2,"goodsCategory":1,"totalPrice":8.4,"outstandingAmount":8.4,"goodsList":[{"goodsBarcode":"54879","goodsName":"杯子","goodsPicturepath":"upload/no_goodsB.jpg","purchaseGoodsCount":2}]},{"supplierUnique":"41","supplierName":"测试001","supplierPhone":"13888888881","id":86,"status":2,"goodsCategory":1,"totalPrice":30,"outstandingAmount":30,"goodsList":[{"goodsBarcode":"54879","goodsName":"杯子","goodsPicturepath":"upload/no_goodsB.jpg","purchaseGoodsCount":3}]}]
     * purchaseAmounts : 108.4
     */

    private int outstandingCount;//数量
    private double purchaseAmounts;//总欠款
    private List<PurchaseListData> billList;

    public int getOutstandingCount() {
        return outstandingCount;
    }

    public void setOutstandingCount(int outstandingCount) {
        this.outstandingCount = outstandingCount;
    }

    public double getPurchaseAmounts() {
        return purchaseAmounts;
    }

    public void setPurchaseAmounts(double purchaseAmounts) {
        this.purchaseAmounts = purchaseAmounts;
    }

    public List<PurchaseListData> getBillList() {
        return billList;
    }

    public void setBillList(List<PurchaseListData> billList) {
        this.billList = billList;
    }
}
