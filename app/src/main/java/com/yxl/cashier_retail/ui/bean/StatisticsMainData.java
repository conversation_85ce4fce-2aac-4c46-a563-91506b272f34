package com.yxl.cashier_retail.ui.bean;

/**
 * Describe:统计-收银界面新版-横轴界面（实体类）-啥呀
 * Created by jingang on 2024/8/29
 */
public class StatisticsMainData {
    /**
     * date : 2024-08-29
     * netListCount : 0
     * saleTotal : 0.0
     * averPrice : 0.0
     * saleTotalRatio : 0
     * listCountRatio : 0
     * grossProfitRatio : 0
     * listCount : 0
     * averPriceRatio : 0
     * cusSum : 3754
     * grossProfit : 0.0
     * netListCountRatio : 0
     */

    private String date;//当前日期
    private double saleTotal;//今日营业额
    private double saleTotalRatio;//营业额对比昨日增幅
    private int netListCount;//网单量
    private double netListCountRatio;//网单量对比昨日增幅
    private int listCount;//订单量
    private double listCountRatio;//订单量对比昨日增幅
    private double averPrice;//平均客单价
    private double averPriceRatio;//平均客单价对比昨日增幅
    private double grossProfit;//毛利润
    private double grossProfitRatio;//毛利润对比昨日增幅
    private int cusSum;//会员总数量

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public double getSaleTotal() {
        return saleTotal;
    }

    public void setSaleTotal(double saleTotal) {
        this.saleTotal = saleTotal;
    }

    public double getSaleTotalRatio() {
        return saleTotalRatio;
    }

    public void setSaleTotalRatio(double saleTotalRatio) {
        this.saleTotalRatio = saleTotalRatio;
    }

    public int getNetListCount() {
        return netListCount;
    }

    public void setNetListCount(int netListCount) {
        this.netListCount = netListCount;
    }

    public double getNetListCountRatio() {
        return netListCountRatio;
    }

    public void setNetListCountRatio(double netListCountRatio) {
        this.netListCountRatio = netListCountRatio;
    }

    public int getListCount() {
        return listCount;
    }

    public void setListCount(int listCount) {
        this.listCount = listCount;
    }

    public double getListCountRatio() {
        return listCountRatio;
    }

    public void setListCountRatio(double listCountRatio) {
        this.listCountRatio = listCountRatio;
    }

    public double getAverPrice() {
        return averPrice;
    }

    public void setAverPrice(double averPrice) {
        this.averPrice = averPrice;
    }

    public double getAverPriceRatio() {
        return averPriceRatio;
    }

    public void setAverPriceRatio(double averPriceRatio) {
        this.averPriceRatio = averPriceRatio;
    }

    public double getGrossProfit() {
        return grossProfit;
    }

    public void setGrossProfit(double grossProfit) {
        this.grossProfit = grossProfit;
    }

    public double getGrossProfitRatio() {
        return grossProfitRatio;
    }

    public void setGrossProfitRatio(double grossProfitRatio) {
        this.grossProfitRatio = grossProfitRatio;
    }

    public int getCusSum() {
        return cusSum;
    }

    public void setCusSum(int cusSum) {
        this.cusSum = cusSum;
    }
}
