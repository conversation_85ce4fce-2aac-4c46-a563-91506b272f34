package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.MemberRecordData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:会员消费记录（适配器）
 * Created by jingang on 2024/8/7
 */
public class MemberRecordDialogAdapter extends BaseAdapter<MemberRecordData> {

    public MemberRecordDialogAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_member_record_dialog;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvNo, tvTotal, tvType, tvTime;
        tvNo = holder.getView(R.id.tvItemNo);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvType = holder.getView(R.id.tvItemType);
        tvTime = holder.getView(R.id.tvItemTime);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
        }
        tvNo.setText(TextUtils.isEmpty(mDataList.get(position).getSaleListUnique()) ? "-" : mDataList.get(position).getSaleListUnique());
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getRecMoney()));
        tvType.setText(TextUtils.isEmpty(mDataList.get(position).getConsumptionType()) ? "-" : mDataList.get(position).getConsumptionType());
        tvTime.setText(TextUtils.isEmpty(mDataList.get(position).getRecDate()) ? "-" : mDataList.get(position).getRecDate());
    }
}
