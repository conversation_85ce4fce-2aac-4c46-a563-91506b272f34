package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.ShiftRecordData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:查询-交接班记录-充值统计（适配器）
 * Created by jingang on 2024/8/23
 */
public class ShiftInfoRechargeAdapter extends BaseAdapter<ShiftRecordData.DetailBean> {

    public ShiftInfoRechargeAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_shift_info_recharge;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvType, tvTotal, tvCount;
        tvType = holder.getView(R.id.tvItemType);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvCount = holder.getView(R.id.tvItemCount);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
        }
        tvType.setText(mDataList.get(position).getName());
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getSale_list_actually_received()));
        tvCount.setText(String.valueOf(mDataList.get(position).getOrderNum()));
    }
}
