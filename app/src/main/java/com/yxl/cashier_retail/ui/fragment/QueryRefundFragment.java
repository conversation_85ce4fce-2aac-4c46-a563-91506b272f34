package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.graphics.Typeface;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmQueryRefundBinding;
import com.yxl.cashier_retail.ui.adapter.OrderGoodsAdapter;
import com.yxl.cashier_retail.ui.adapter.RefundAdapter;
import com.yxl.cashier_retail.ui.bean.ConditionData;
import com.yxl.cashier_retail.ui.bean.OrderGoodsData;
import com.yxl.cashier_retail.ui.bean.RefundInfoData;
import com.yxl.cashier_retail.ui.bean.RefundData;
import com.yxl.cashier_retail.ui.contract.RefundContact;
import com.yxl.cashier_retail.ui.dialog.DateStartEndDialog;
import com.yxl.cashier_retail.ui.popupwindow.ConditionPop;
import com.yxl.cashier_retail.ui.presenter.RefundPresenter;
import com.yxl.cashier_retail.utils.DateUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Describe:查询-查退款
 * Created by jingang on 2024/5/22
 */
@SuppressLint({"NonConstantResourceId", "NotifyDataSetChanged"})
public class QueryRefundFragment extends BaseFragment<FmQueryRefundBinding> implements View.OnClickListener,
        RefundContact.View {
    private RefundPresenter mPresenter;
    private boolean isOrder;//是否为订单信息
    private String startDate, endDate, keyWords;

    //操作人
    private List<ConditionData> conditionList = new ArrayList<>();
    private int conditionId;

    //退款订单列表
    private RefundAdapter mAdapter;
    private List<RefundData> dataList = new ArrayList<>();

    //商品列表
    private OrderGoodsAdapter goodsAdapter;
    private List<OrderGoodsData> goodsList = new ArrayList<>();

    @Override
    protected FmQueryRefundBinding getViewBinding() {
        return FmQueryRefundBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.linType.setOnClickListener(this);
        mBinding.linStartDate.setOnClickListener(this);
        mBinding.linEndDate.setOnClickListener(this);
        mBinding.ivSearchClear.setOnClickListener(this);

        mBinding.tvGoods.setOnClickListener(this);
        mBinding.tvOrder.setOnClickListener(this);
        mBinding.tvPrint.setOnClickListener(this);

        mBinding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                if (TextUtils.isEmpty(keyWords)) {
                    mBinding.ivSearchClear.setVisibility(View.GONE);
                } else {
                    mBinding.ivSearchClear.setVisibility(View.VISIBLE);
                }
            }
        });
        mBinding.etSearch.setOnEditorActionListener((v, actionId, event) -> {
            page = 1;
            return true;
        });
        setAdapter();
    }

    @Override
    protected void initData() {
        mPresenter = new RefundPresenter(mContext);
        mPresenter.attachView(this);

        conditionList.clear();
        conditionList.add(new ConditionData(0, "收款方式", false));
        conditionList.add(new ConditionData(1, "微信", false));
        conditionList.add(new ConditionData(2, "刷卡", false));
        conditionList.add(new ConditionData(3, "扫我二维码", false));

        startDate = DateUtils.getOldDate(-3);
        endDate = DateUtils.getOldDate(0);
        mBinding.tvStartDate.setText(startDate);
        mBinding.tvEndDate.setText(endDate);

        mPresenter.getRefundList(getShopUnique(), startDate, endDate, page, Constants.limit);
    }

    @Override
    public void onClick(View v) {
        if (isQuicklyClick()) {
            return;
        }
        switch (v.getId()) {
            case R.id.linType:
                //操作人
                ConditionPop.showDialog(mContext,
                        mBinding.ivType,
                        v,
                        mBinding.linType.getMeasuredWidth(),
                        conditionList,
                        conditionId,
                        data -> {
                            conditionId = data.getId();
                            mBinding.tvType.setText(data.getName());
                        });
                break;
            case R.id.linStartDate:
                //开始日期
                showDialogDate(0, mBinding.ivStartDate);
                break;
            case R.id.linEndDate:
                //结束日期
                showDialogDate(1, mBinding.ivEndDate);
                break;
            case R.id.ivSearchClear:
                //清除搜索输入
                mBinding.etSearch.setText("");
                showSoftInput(mBinding.etSearch);
                break;
            case R.id.tvGoods:
                //商品信息
                if (isOrder) {
                    isOrder = false;
                    mBinding.tvGoods.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvGoods.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvGoods.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.linGoods.setVisibility(View.VISIBLE);
                    mBinding.tvOrder.setBackgroundResource(0);
                    mBinding.tvOrder.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvOrder.setTypeface(Typeface.DEFAULT);
                    mBinding.svOrder.setVisibility(View.GONE);
                }
                break;
            case R.id.tvOrder:
                //订单信息
                if (!isOrder) {
                    isOrder = true;
                    mBinding.tvGoods.setBackgroundResource(0);
                    mBinding.tvGoods.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvGoods.setTypeface(Typeface.DEFAULT);
                    mBinding.linGoods.setVisibility(View.GONE);
                    mBinding.tvOrder.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvOrder.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvOrder.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.svOrder.setVisibility(View.VISIBLE);
                }
                break;
            case R.id.tvPrint:
                //打印小票
                break;
        }
    }

    @Override
    public void onListSuccess(List<RefundData> list) {
        //退款订单列表成功
        mBinding.vSmartrefreshlayout.smartRefreshLayout.finishRefresh();
        mBinding.vSmartrefreshlayout.smartRefreshLayout.finishLoadMore();
        if (page == 1) {
            dataList.clear();
            dataList.addAll(list);
            if (dataList.size() > 0) {
                dataList.get(0).setSelect(true);
                mPresenter.getRefundInfo(dataList.get(0).getRetListUnique());
            }
        } else {
            dataList.addAll(list);
        }
        if (dataList.size() > 0) {
            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.VISIBLE);
            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.GONE);
            mAdapter.setDataList(dataList);
        } else {
            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.GONE);
            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onListError(String msg) {
        //退款订单列表失败
        mBinding.vSmartrefreshlayout.smartRefreshLayout.finishRefresh();
        mBinding.vSmartrefreshlayout.smartRefreshLayout.finishLoadMore();
        if (dataList.size() > 0) {
            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.VISIBLE);
            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.GONE);
        } else {
            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.GONE);
            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onInfoSuccess(RefundInfoData data) {
        //退款订单详情成功
        mBinding.linInfo.setVisibility(View.VISIBLE);
        mBinding.tvNothing.setVisibility(View.GONE);

        //商品列表
        goodsList.clear();
        if (data.getDetailList() != null) {
            goodsList.addAll(data.getDetailList());
        }
        if (goodsList.size() > 0) {
            mBinding.rvGoods.setVisibility(View.VISIBLE);
            goodsAdapter.setDataList(goodsList);
        } else {
            mBinding.rvGoods.setVisibility(View.GONE);
        }
    }

    @Override
    public void onInfoError(String msg) {
        //退款订单详情失败
        mBinding.linInfo.setVisibility(View.GONE);
        mBinding.tvNothing.setVisibility(View.VISIBLE);
    }

    @Override
    public void onError(String msg) {
        showToast(1, msg);
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //订单列表
        mAdapter = new RefundAdapter(getActivity());
        mBinding.vSmartrefreshlayout.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (!dataList.get(position).isSelect()) {
                for (int i = 0; i < dataList.size(); i++) {
                    if (dataList.get(i).isSelect()) {
                        dataList.get(i).setSelect(false);
                    }
                }
                dataList.get(position).setSelect(true);
                mAdapter.setDataList(dataList);
                mPresenter.getRefundInfo(dataList.get(position).getRetListUnique());
            }
        });
        mBinding.vSmartrefreshlayout.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                mPresenter.getRefundList(getShopUnique(), startDate, endDate, page, Constants.limit);
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                mPresenter.getRefundList(getShopUnique(), startDate, endDate, page, Constants.limit);
            }
        });

        //商品列表
        goodsAdapter = new OrderGoodsAdapter(getActivity(),1);
        mBinding.rvGoods.setAdapter(goodsAdapter);
    }

    /**
     * dialog（选择日期时间）
     */
    private void showDialogDate(int type, View view) {
        DateStartEndDialog.showDialog(getActivity(),
                view,
                startDate,
                endDate,
                type,
                (startDate, endDate) -> {
                    this.startDate = startDate;
                    this.endDate = endDate;
                    mBinding.tvStartDate.setText(startDate);
                    mBinding.tvEndDate.setText(endDate);
                    //请求数据
                    page = 1;
                });
    }

}
