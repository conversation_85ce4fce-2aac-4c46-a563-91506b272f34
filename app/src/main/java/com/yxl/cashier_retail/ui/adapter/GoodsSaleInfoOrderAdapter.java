package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.GoodsSaleInfoData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:查询-商品销售统计详情-订单（适配器）
 * Created by jingang on 2024/8/24
 */
public class GoodsSaleInfoOrderAdapter extends BaseAdapter<GoodsSaleInfoData.DataBean> {

    public GoodsSaleInfoOrderAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_shift_info_order;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvTime, tvNo, tvTotal;
        tvTime = holder.getView(R.id.tvItemTime);
        tvNo = holder.getView(R.id.tvItemNo);
        tvTotal = holder.getView(R.id.tvItemTotal);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
        }
        tvTime.setText(TextUtils.isEmpty(mDataList.get(position).getSaleListDatetime()) ? "-" : mDataList.get(position).getSaleListDatetime());
        tvNo.setText(mDataList.get(position).getSaleListUnique());
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getSaleSum()));
    }
}
