package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.MallGoodsInfoData;
import com.yxl.commonlibrary.utils.StringUtils;

/**
 * Describe:商城-商品详情-满赠（适配器）
 * Created by jingang on 2024/6/3
 */
public class MallGoodsInfoGiftAdapter extends BaseAdapter<MallGoodsInfoData.DataBean.Fullgift.GiftGood> {

    public MallGoodsInfoGiftAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_mall_goods_info_gift;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvCount = holder.getView(R.id.tvItemCount);

        String img;
        if (TextUtils.isEmpty(mDataList.get(position).getGoods_img())) {
            img = mDataList.get(position).getCoupon_img();
        } else {
            img = mDataList.get(position).getGoods_img();
        }

        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(img))
                .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img))
                .into(ivImg);
        tvCount.setText("x" + mDataList.get(position).getFree_quantity());
    }
}
