package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.view.View;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmRiderBinding;
import com.yxl.cashier_retail.ui.adapter.RiderAdapter;
import com.yxl.cashier_retail.ui.bean.RiderData;
import com.yxl.cashier_retail.ui.dialog.RiderEditDialog;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:网单-骑手管理
 * Created by jingang on 2024/6/20
 */
@SuppressLint("NonConstantResourceId")
public class RiderFragment extends BaseFragment<FmRiderBinding> implements View.OnClickListener {
    private String keyWords;
    private List<RiderData> dataList = new ArrayList<>();
    private RiderAdapter mAdapter;

    /**
     * 搜索
     *
     * @param keyWords
     */
    public void setKeyWords(String keyWords) {
        this.keyWords = keyWords;
        page = 1;
        getRiderList();
    }

    @Override
    protected FmRiderBinding getViewBinding() {
        return FmRiderBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.tvAdd.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected void initData() {
        getRiderList();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvAdd:
                //新增骑手
                RiderEditDialog.showDialog(getActivity(), 0, "", "", (id, name, mobile, type) -> getRiderList());
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new RiderAdapter(getContext());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            RiderEditDialog.showDialog(getActivity(),
                    dataList.get(position).getId(),
                    dataList.get(position).getCourier_name(),
                    dataList.get(position).getCourier_phone(),
                    (id, name, mobile, type) -> {
                        //0.新增 1.删除 2.编辑
                        switch (type) {
                            case 1:
                                dataList.remove(position);
                                mAdapter.remove(position);
                                if (dataList.size() < 1) {
                                    page = 1;
                                    getRiderList();
                                }
                                break;
                            case 2:
                                dataList.get(position).setCourier_name(name);
                                dataList.get(position).setCourier_phone(mobile);
                                mAdapter.notifyItemChanged(position);
                                break;
                        }
                    });
        });
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getRiderList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getRiderList();
            }
        });
    }

    /**
     * 店铺查询骑手列表
     */
    public void getRiderList() {
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("msessage", keyWords);
        params.put("page", page);
        params.put("limit", Constants.limit);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getRiderList(),
                params,
                RiderData.class,
                new RequestListListener<RiderData>() {
                    @Override
                    public void onResult(List<RiderData> list) {
                        hideDialog();
                        if (page == 1) {
                            mBinding.smartRefreshLayout.finishRefresh();
                            dataList.clear();
                        } else {
                            mBinding.smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        if (page == 1) {
                            mBinding.smartRefreshLayout.finishRefresh();
                        } else {
                            mBinding.smartRefreshLayout.finishLoadMore();
                        }
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

}
