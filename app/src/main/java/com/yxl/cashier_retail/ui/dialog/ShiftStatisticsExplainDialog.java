package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.Gravity;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogShiftStatisticsExplainBinding;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:dialog（交班-统计字段说明）
 * Created by jingang on 2024/7/4
 */
@SuppressLint("NonConstantResourceId")
public class ShiftStatisticsExplainDialog extends BaseDialog<DialogShiftStatisticsExplainBinding> {

    public static void showDialog(Activity activity) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        ShiftStatisticsExplainDialog dialog = new ShiftStatisticsExplainDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public ShiftStatisticsExplainDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.tvDialogConfirm.setOnClickListener(v -> dismiss());
    }

    @Override
    protected DialogShiftStatisticsExplainBinding getViewBinding() {
        return DialogShiftStatisticsExplainBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }
}
