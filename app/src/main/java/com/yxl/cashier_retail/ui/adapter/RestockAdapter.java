package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.RestockData;

import java.util.List;

/**
 * Describe:自采补货（适配器）
 * Created by jingang on 2024/6/4
 */
public class RestockAdapter extends BaseAdapter<RestockData> {

    public RestockAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_restock;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName, tvStatus, tvTime;
        tvName = holder.getView(R.id.tvItemName);
        tvStatus = holder.getView(R.id.tvItemStatus);
        tvTime = holder.getView(R.id.tvItemTime);

        //补货计划状态:1待生成2已生成3已取消
        switch (mDataList.get(position).getStatus()) {
            case 1:
                tvStatus.setText(getRstr(R.string.generate_no));
                tvStatus.setBackgroundResource(R.drawable.shape_orange_5);
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.white));
                break;
            case 2:
                tvStatus.setText(getRstr(R.string.generated));
                if (mDataList.get(position).isSelect()) {
                    tvStatus.setBackgroundResource(R.drawable.shape_white_5);
                    tvStatus.setTextColor(mContext.getResources().getColor(R.color.black));
                } else {
                    tvStatus.setBackgroundResource(R.drawable.shape_green_5);
                    tvStatus.setTextColor(mContext.getResources().getColor(R.color.white));
                }
                break;
            default:
                tvStatus.setText(getRstr(R.string.canceled));
                tvStatus.setBackgroundResource(R.drawable.shape_f2_5);
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
        }
        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundResource(R.drawable.shape_green_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
            tvTime.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundResource(R.drawable.shape_white_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
            tvTime.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
        }
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName, tvStatus, tvTime;
        tvName = holder.getView(R.id.tvItemName);
        tvStatus = holder.getView(R.id.tvItemStatus);
        tvTime = holder.getView(R.id.tvItemTime);

        tvName.setText(mDataList.get(position).getShopRestockplanName());
        tvTime.setText(getRstr(R.string.time_create_colon) + mDataList.get(position).getCreateTime());
        //补货计划状态:1待生成2已生成3已取消
        switch (mDataList.get(position).getStatus()) {
            case 1:
                tvStatus.setText(getRstr(R.string.generate_no));
                tvStatus.setBackgroundResource(R.drawable.shape_orange_5);
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.white));
                break;
            case 2:
                tvStatus.setText(getRstr(R.string.generated));
                if (mDataList.get(position).isSelect()) {
                    tvStatus.setBackgroundResource(R.drawable.shape_white_5);
                    tvStatus.setTextColor(mContext.getResources().getColor(R.color.black));
                } else {
                    tvStatus.setBackgroundResource(R.drawable.shape_green_5);
                    tvStatus.setTextColor(mContext.getResources().getColor(R.color.white));
                }
                break;
            default:
                tvStatus.setText(getRstr(R.string.canceled));
                tvStatus.setBackgroundResource(R.drawable.shape_f2_5);
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
        }
        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundResource(R.drawable.shape_green_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
            tvTime.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundResource(R.drawable.shape_white_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
            tvTime.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
        }
    }
}
