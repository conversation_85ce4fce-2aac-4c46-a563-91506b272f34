package com.yxl.cashier_retail.ui.bean;

import org.litepal.crud.LitePalSupport;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:分类（实体类）
 * Created by jingang on 2024/5/27
 */
public class CateData extends LitePalSupport implements Serializable {
    /**
     * groupName : 冻肉22
     * groupUnique : 496037084
     * kindDetail : []
     * sort : -2
     */

    private boolean check;
    private boolean show;
    private String groupName;
    private String groupUnique;
    private int sort;
    private List<CateChildData> kindDetail;
    private int editType;//1.不可编辑 2.可编辑
    private String kindIconId;//图片id
    private String kindIcon;//图片
    private int cartNum;//购物车数量

    public CateData() {
    }

    public CateData(String groupName, String groupUnique, int sort) {
        this.groupName = groupName;
        this.groupUnique = groupUnique;
        this.sort = sort;
    }

    public boolean isCheck() {
        return check;
    }

    public void setCheck(boolean check) {
        this.check = check;
    }

    public boolean isShow() {
        return show;
    }

    public void setShow(boolean show) {
        this.show = show;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getGroupUnique() {
        return groupUnique;
    }

    public void setGroupUnique(String groupUnique) {
        this.groupUnique = groupUnique;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public List<CateChildData> getKindDetail() {
        return kindDetail;
    }

    public void setKindDetail(List<CateChildData> kindDetail) {
        this.kindDetail = kindDetail;
    }

    public int getEditType() {
        return editType;
    }

    public void setEditType(int editType) {
        this.editType = editType;
    }

    public String getKindIconId() {
        return kindIconId;
    }

    public void setKindIconId(String kindIconId) {
        this.kindIconId = kindIconId;
    }

    public String getKindIcon() {
        return kindIcon;
    }

    public void setKindIcon(String kindIcon) {
        this.kindIcon = kindIcon;
    }

    public int getCartNum() {
        return cartNum;
    }

    public void setCartNum(int cartNum) {
        this.cartNum = cartNum;
    }
}
