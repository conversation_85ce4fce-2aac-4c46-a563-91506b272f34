package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.CateChildData;

/**
 * Describe:商品二级分类（适配器）
 * Created by jingang on 2024/6/5
 */
public class CateChildAdapter extends BaseAdapter<CateChildData> {

    public CateChildAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_cate_child;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName = holder.getView(R.id.tvItemName);

        tvName.setText(mDataList.get(position).getKindName());
        if (mDataList.get(position).isCheck()) {
            tvName.setBackgroundResource(R.mipmap.ic_cate_bg001);
        } else {
            tvName.setBackgroundColor(mContext.getResources().getColor(R.color.black));
        }
    }
}
