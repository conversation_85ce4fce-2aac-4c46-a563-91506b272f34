package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;

/**
 * Describe:商城-提交订单-商品（适配器）
 * Created by jingang on 2024/6/4
 */
public class MallOrderSettlementGoodsAdapter extends BaseAdapter<String> {

    public MallOrderSettlementGoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_mall_order_settlement_goods;
    }

    @Override
    public int getItemCount() {
        return 2;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvName, tvStartOrder, tvPrice, tvCount, tvTotal, tvGift;
        tvName = holder.getView(R.id.tvItemName);
        tvStartOrder = holder.getView(R.id.tvItemStartOrder);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTotal = holder.getView(R.id.tvItemTotal);
        LinearLayout linBind, linGift;
        RecyclerView rvBind, rvGift;
        linBind = holder.getView(R.id.linItemBind);
        rvBind = holder.getView(R.id.rvItemBind);
        linGift = holder.getView(R.id.linItemGift);
        tvGift = holder.getView(R.id.tvItemGift);
        rvGift = holder.getView(R.id.rvItemGift);

        //捆绑
        MallCartBindAdapter bindAdapter = new MallCartBindAdapter(mContext);
        rvBind.setAdapter(bindAdapter);

        //满赠
        MallCartGiftAdapter giftAdapter = new MallCartGiftAdapter(mContext);
        rvGift.setAdapter(giftAdapter);
    }
}
