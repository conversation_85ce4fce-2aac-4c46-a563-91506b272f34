package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.NetworkUtils;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogAccountBinding;
import com.yxl.cashier_retail.ui.bean.OrderOfflineData;
import com.yxl.cashier_retail.ui.bean.SaleListUniqueData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import org.litepal.LitePal;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:dialog（当前登录账号）
 * Created by jingang on 2024/6/28
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n", "MissingPermission"})
public class AccountDialog extends BaseDialog<DialogAccountBinding> implements View.OnClickListener {
    private final List<OrderOfflineData> offlineList = new ArrayList<>();

    public static void showDialog(Activity activity, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        AccountDialog.listener = listener;
        AccountDialog dialog = new AccountDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 3, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public AccountDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.tvDialogPwd.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        setUI();
    }

    @Override
    protected DialogAccountBinding getViewBinding() {
        return DialogAccountBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogPwd:
                //修改密码
                if (listener != null) {
                    listener.onPwdClick();
                    dismiss();
                }
                break;
            case R.id.tvDialogConfirm:
                //交班
                if (isQuicklyClick()) {
                    return;
                }
                if (offlineList.size() > 0) {
                    showDialog();
                    getSaleListUnique();
                } else {
                    if (listener != null) {
                        listener.onShiftClick();
                        dismiss();
                    }
                }
                break;
        }
    }

    /**
     * 更新UI
     */
    private void setUI() {
        if (getLoginData() == null) {
            return;
        }
        if (TextUtils.isEmpty(getLoginData().getStaffName())) {
            mBinding.tvDialogName.setText(getRstr(R.string.staff_name_colon) + "-");
        } else {
            mBinding.tvDialogName.setText(getRstr(R.string.staff_name_colon) + getLoginData().getStaffName());
        }
        if (TextUtils.isEmpty(getLoginData().getStaff_phone())) {
            mBinding.tvDialogMobile.setText(getRstr(R.string.contact_mobile_colon) + "-");
            mBinding.tvDialogAccount.setText(getRstr(R.string.login_account_colon) + "-");
        } else {
            mBinding.tvDialogMobile.setText(getRstr(R.string.contact_mobile_colon) + getLoginData().getStaff_phone());
            mBinding.tvDialogAccount.setText(getRstr(R.string.login_account_colon) + getLoginData().getStaff_phone());
        }
        if (TextUtils.isEmpty(getLoginData().getShop_name())) {
            mBinding.tvDialogShopName.setText(getRstr(R.string.belong_shop_colon) + "-");
        } else {
            mBinding.tvDialogShopName.setText(getRstr(R.string.belong_shop_colon) + getLoginData().getShop_name());
        }
        if (TextUtils.isEmpty(getLoginData().getShopUnique())) {
            mBinding.tvDialogShopUnique.setText(getRstr(R.string.shop_no_colon) + "-");
        } else {
            mBinding.tvDialogShopUnique.setText(getRstr(R.string.shop_no_colon) + getLoginData().getShopUnique());
        }
        if (getLoginData().getStaff_position() == 3) {
            mBinding.tvDialogPosition.setText(getRstr(R.string.staff_position_colon) + getRstr(R.string.shopowner));
        } else {
            mBinding.tvDialogPosition.setText(getRstr(R.string.staff_position_colon) + getRstr(R.string.cashier_staff));
        }
        getOrderList();
    }

    /**
     * 获取离线订单列表
     */
    private void getOrderList() {
        List<OrderOfflineData> list = LitePal.findAll(OrderOfflineData.class);
        offlineList.clear();
        if (list != null) {
            offlineList.addAll(list);
        }
        Collections.reverse(offlineList);
    }

    /**
     * 创建订单编号
     */
    private void getSaleListUnique() {
        if (!NetworkUtils.isConnected()) {
            hideDialog();
            showToast(1, getRstr(R.string.no_network_connect));
            return;
        }
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getSaleListUniqueCreate(),
                new HashMap<>(),
                SaleListUniqueData.class,
                new RequestListener<SaleListUniqueData>() {
                    @Override
                    public void success(SaleListUniqueData data) {
                        postPayment(data.getSale_list_unique());
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 普通线下订单结算
     *
     * @param saleListUnique
     */
    private void postPayment(String saleListUnique) {
        if (!NetworkUtils.isConnected()) {
            hideDialog();
            showToast(1, getRstr(R.string.no_network_connect));
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("saleListState", offlineList.get(0).getSaleListState());//付款状态 3.已付款 4.赊账
        params.put("saleListUnique", saleListUnique);//订单编号(接口创建)
        params.put("goodsPurprice", offlineList.get(0).getGoodsPurprice());//商品进价 “,”隔开
        params.put("saleListDetailPrice", offlineList.get(0).getSaleListDetailPrice());//商品售价 “,”隔开
        params.put("goods_old_price", offlineList.get(0).getGoods_old_price());//商品原价 “,”隔开
        params.put("saleListDetailCount", offlineList.get(0).getSaleListDetailCount());//商品数量 “,”隔开
        params.put("goodsName", offlineList.get(0).getGoodsName());//商品名称 “,”隔开
        params.put("goodsBarcode", offlineList.get(0).getGoodsBarcode());//商品条码 “,”隔开
        params.put("goodsId", offlineList.get(0).getGoodsId());//商品id ","隔开
        params.put("saleListTotal", offlineList.get(0).getSaleListTotal());//订单总金额
        params.put("saleListCashier", getStaffUnique());//员工id
//        params.put("saleListRemarks", );//订单备注
//        params.put("machine_num", );//机器编号
        params.put("saleListActuallyReceived", offlineList.get(0).getSaleListActuallyReceived());
        params.put("sale_list_payment", offlineList.get(0).getSale_list_payment());//支付方式 1.现金 2.支付宝 3.微信 4.银行卡 5.储值卡 8.混合支付 9.免密支付
//        params.put("machineTime", );//失败时上传当前时间
        params.put("type", offlineList.get(0).getType());//固定值2
//        params.put("wholesale_phone", );//批发客户的手机号
        params.put("saleListPayDetail", offlineList.get(0).getSaleListPayDetail());//支付详情
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getPayment(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        OrderOfflineData data = offlineList.get(0);
                        data.delete();
                        offlineList.remove(0);
                        if (offlineList.size() > 0) {
                            getSaleListUnique();
                        } else {
                            hideDialog();
                            if (listener != null) {
                                listener.onShiftClick();
                                dismiss();
                            }
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onPwdClick();

        void onShiftClick();
    }
}
