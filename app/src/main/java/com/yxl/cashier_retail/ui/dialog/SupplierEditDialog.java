package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogSupplierEditBinding;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Describe:dialog（供货商新增、编辑）
 * Created by jingang on 2025/6/7
 */
@SuppressLint("NonConstantResourceId")
public class SupplierEditDialog extends BaseDialog<DialogSupplierEditBinding> implements View.OnClickListener {
    private static int id;
    private static String name;

    public static void showDialog(Activity activity, int id, String name, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        SupplierEditDialog.listener = listener;
        SupplierEditDialog.id = id;
        SupplierEditDialog.name = name;
        SupplierEditDialog dialog = new SupplierEditDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public SupplierEditDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        mBinding.tvDialogDel.setOnClickListener(this);
        if (id == 0) {
            mBinding.tvDialogTitle.setText(getRstr(R.string.supplier_add));
            mBinding.tvDialogDel.setVisibility(View.GONE);
        } else {
            mBinding.tvDialogTitle.setText(getRstr(R.string.supplier_edit));
            mBinding.etDialogName.setText(name);
            mBinding.tvDialogDel.setVisibility(View.VISIBLE);
        }
    }

    @Override
    protected DialogSupplierEditBinding getViewBinding() {
        return DialogSupplierEditBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //保存
                name = mBinding.etDialogName.getText().toString().trim();
                if (TextUtils.isEmpty(name)) {
                    showToast(1, getRstr(R.string.input_supplier_name));
                    return;
                }
                postSupplierEdit();
                break;
            case R.id.tvDialogDel:
                //删除
                break;
        }
    }

    /**
     * 新增供货商
     */
    private void postSupplierEdit() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("supplierName", name);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getSupplierAdd(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        if (listener != null) {
                            listener.onConfirm(id, name, 0);
                            dismiss();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(int id, String name, int type);
    }
}
