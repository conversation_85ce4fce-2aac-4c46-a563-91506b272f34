package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.Paint;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogDiscountBinding;
import com.yxl.cashier_retail.ui.adapter.DiscountAdapter;
import com.yxl.cashier_retail.ui.bean.DiscountData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.view.NumberKeyBoardView;
import com.yxl.commonlibrary.utils.DensityUtils;

import org.litepal.LitePal;

import java.util.ArrayList;
import java.util.List;

/**
 * Describe:dialog（整单折扣）
 * Created by jingang on 2024/5/29
 */
@SuppressLint("NonConstantResourceId")
public class DiscountDialog extends BaseDialog<DialogDiscountBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private boolean isDiscount;//当前焦点是否为折扣
    private int zeroType;// 0.不抹零 1.抹零到1元 2.抹零到5角 3.抹零到1角
    private static double total,//合计
            discountMoney,//优惠金额
            money;//优惠后金额

    private List<DiscountData> discountList = new ArrayList<>();
    private DiscountAdapter discountAdapter;

    public static void showDialog(Activity activity, double total, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        DiscountDialog.listener = listener;
        DiscountDialog.total = total;
        DiscountDialog dialog = new DiscountDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 7);
        dialog.show();
    }

    public DiscountDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        setUI();
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.tvDialogEdit.setOnClickListener(this);
        mBinding.linDialogTotal.setOnClickListener(this);
        mBinding.tvDialogZero0.setOnClickListener(this);
        mBinding.tvDialogZero1.setOnClickListener(this);
        mBinding.linDialogCustom.setOnClickListener(this);
        mBinding.numberKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var) {
                zeroType = 0;
                setUIZero();
                //清空折扣
                for (int i = 0; i < discountList.size(); i++) {
                    if (discountList.get(i).isSelect()) {
                        discountList.get(i).setSelect(false);
                    }
                }
                if (discountAdapter != null) {
                    discountAdapter.setDataList(discountList);
                }
                if (isDiscount) {
                    mBinding.tvDialogCustom.setText(var);
                    if (TextUtils.isEmpty(var)) {
                        mBinding.tvDialogCustom.setVisibility(View.GONE);
                        mBinding.tvDialogCustomHint.setVisibility(View.VISIBLE);
                        money = total;
                    } else {
                        mBinding.tvDialogCustom.setVisibility(View.VISIBLE);
                        mBinding.tvDialogCustomHint.setVisibility(View.GONE);
                        double discount = Double.parseDouble(var);
                        if (discount > 0) {
                            money = total * discount / 10;
                        } else {
                            money = total;
                        }
                    }
                    discountMoney = total - money;
                    mBinding.tvDialogMoney.setText(DFUtils.getNum4(money));
                } else {
                    mBinding.tvDialogMoney.setText(var);
                    if (TextUtils.isEmpty(var)) {
                        money = 0;
                    } else {
                        money = Double.parseDouble(var);
                    }
                    discountMoney = total - money;
                    mBinding.tvDialogCustom.setText("");
                    mBinding.tvDialogCustom.setVisibility(View.GONE);
                    mBinding.tvDialogCustomHint.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onConfirm() {
                if (listener != null) {
                    listener.onConfirm(money, discountMoney);
                    dismiss();
                }
            }
        });
    }

    @Override
    protected DialogDiscountBinding getViewBinding() {
        return DialogDiscountBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogEdit:
                //编辑
                DiscountEditDialog.showDialog(mActivity, () -> {
                    setUI();
                });
                break;
            case R.id.linDialogTotal:
                //总价
                if (isDiscount) {
                    isDiscount = false;
                    mBinding.linDialogTotal.setBackgroundResource(R.drawable.shape_green_kuang_5);
                    mBinding.ivDialogCursor.setVisibility(View.VISIBLE);
                    mBinding.linDialogCustom.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursorCustom.setVisibility(View.GONE);
                    mBinding.numberKeyBoardView.setResultStr("");
                }
                break;
            case R.id.tvDialogZero0:
                //抹零到5角
                if (zeroType == 2) {
                    zeroType = 0;
                } else {
                    zeroType = 2;
                }
                setUIZero();
                money = DFUtils.getZero(total, zeroType);
                discountMoney = total - money;
                mBinding.tvDialogMoney.setText(DFUtils.getNum4(money));
                for (int i = 0; i < discountList.size(); i++) {
                    if (discountList.get(i).isSelect()) {
                        discountList.get(i).setSelect(false);
                    }
                }
                if (discountAdapter != null) {
                    discountAdapter.setDataList(discountList);
                }
                mBinding.tvDialogCustom.setText("");
                mBinding.tvDialogCustom.setVisibility(View.GONE);
                mBinding.tvDialogCustomHint.setVisibility(View.VISIBLE);
                break;
            case R.id.tvDialogZero1:
                //抹零到1元
                if (zeroType == 1) {
                    zeroType = 0;
                } else {
                    zeroType = 1;
                }
                setUIZero();
                money = DFUtils.getZero(total, zeroType);
                discountMoney = total - money;
                mBinding.tvDialogMoney.setText(DFUtils.getNum4(money));
                for (int i = 0; i < discountList.size(); i++) {
                    if (discountList.get(i).isSelect()) {
                        discountList.get(i).setSelect(false);
                    }
                }
                if (discountAdapter != null) {
                    discountAdapter.setDataList(discountList);
                }
                mBinding.tvDialogCustom.setText("");
                mBinding.tvDialogCustom.setVisibility(View.GONE);
                mBinding.tvDialogCustomHint.setVisibility(View.VISIBLE);
                break;
            case R.id.linDialogCustom:
                //自定义
                if (!isDiscount) {
                    isDiscount = true;
                    mBinding.linDialogTotal.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursor.setVisibility(View.GONE);
                    mBinding.linDialogCustom.setBackgroundResource(R.drawable.shape_green_kuang_5);
                    mBinding.ivDialogCursorCustom.setVisibility(View.VISIBLE);
                    mBinding.numberKeyBoardView.setResultStr("");
                }
                break;
        }
    }

    /**
     * 更新UI
     */
    private void setUI() {
        zeroType = getZero();
        setUIZero();
        ((AnimationDrawable) mBinding.ivDialogCursor.getDrawable()).start();
        ((AnimationDrawable) mBinding.ivDialogCursorCustom.getDrawable()).start();
        mBinding.tvDialogTotal.setText(DFUtils.getNum2(total));
        mBinding.tvDialogTotal.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG);

        money = DFUtils.getZero(total, zeroType);//应付金额
        discountMoney = total - money;//优惠金额
        mBinding.tvDialogMoney.setText(DFUtils.getNum4(money));

        discountList.clear();
        discountList.addAll(LitePal.findAll(DiscountData.class));
        discountAdapter = new DiscountAdapter(getContext(), 0);
        mBinding.rvDialogDiscount.setAdapter(discountAdapter);
        discountAdapter.setDataList(discountList);
        discountAdapter.setOnItemClickListener((view, position) -> {
            if (discountList.get(position).isSelect()) {
                discountList.get(position).setSelect(false);
            } else {
                for (int i = 0; i < discountList.size(); i++) {
                    if (discountList.get(i).isSelect()) {
                        discountList.get(i).setSelect(false);
                    }
                }
                discountList.get(position).setSelect(true);
            }
            discountAdapter.setDataList(discountList);
            if (discountList.get(position).isSelect()) {
                money = total * discountList.get(position).getDiscount() / 10;
            } else {
                money = total;
            }
            mBinding.tvDialogMoney.setText(DFUtils.getNum4(money));
            discountMoney = total - money;
            zeroType = 0;
            setUIZero();
        });
    }

    /**
     * 更新UI-抹零
     */
    private void setUIZero() {
        //抹零 0.不抹零 1.抹零到1元 2.抹零到5角 3.抹零到1角
        switch (zeroType) {
            case 1:
                mBinding.tvDialogZero0.setBackgroundResource(R.drawable.shape_red_tm_5);
                mBinding.tvDialogZero0.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.red));
                mBinding.tvDialogZero1.setBackgroundResource(R.drawable.shape_red_5);
                mBinding.tvDialogZero1.setTextColor(getContext().getResources().getColor(R.color.white));
                break;
            case 2:
                mBinding.tvDialogZero1.setBackgroundResource(R.drawable.shape_red_tm_5);
                mBinding.tvDialogZero1.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.red));
                mBinding.tvDialogZero0.setBackgroundResource(R.drawable.shape_red_5);
                mBinding.tvDialogZero0.setTextColor(getContext().getResources().getColor(R.color.white));
                break;
            default:
                mBinding.tvDialogZero0.setBackgroundResource(R.drawable.shape_red_tm_5);
                mBinding.tvDialogZero0.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.red));
                mBinding.tvDialogZero1.setBackgroundResource(R.drawable.shape_red_tm_5);
                mBinding.tvDialogZero1.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.red));
                break;
        }
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(double money, double discount);
    }
}
