package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogCateBinding;
import com.yxl.cashier_retail.ui.adapter.CateChildDialogAdapter;
import com.yxl.cashier_retail.ui.adapter.CateDialogAdapter;
import com.yxl.cashier_retail.ui.bean.CateChildData;
import com.yxl.cashier_retail.ui.bean.CateData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:dialog（商品分类）
 * Created by jingang on 2024/7/1
 */
@SuppressLint("NonConstantResourceId")
public class CateDialog extends BaseDialog<DialogCateBinding> implements View.OnClickListener {
    private static String cateUnique, cateName, cateChildUnique, cateChildName;
    private static View viewIcon;
    private static int type;//1.作为筛选条件，手动添加“全部分类”
    private final Animation openAnim, closeAnim;

    //一级分类
    private List<CateData> cateList = new ArrayList<>();
    private CateDialogAdapter cateAdapter;

    //二级分类
    private List<CateChildData> cateChildList = new ArrayList<>();
    private CateChildDialogAdapter cateChildAdapter;

    public static void showDialog(Activity activity, String cateUnique, String cateChildUnique, View viewIcon, int type, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        CateDialog.listener = listener;
        CateDialog.cateUnique = cateUnique;
        CateDialog.cateChildUnique = cateChildUnique;
        CateDialog.viewIcon = viewIcon;
        CateDialog.type = type;
        CateDialog dialog = new CateDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 7);
        dialog.show();
    }

    public CateDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        openAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_1);
        closeAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_2);
        openAnim.setFillAfter(true);
        closeAnim.setFillAfter(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        setAdapter();
        getCate();
    }

    @Override
    protected DialogCateBinding getViewBinding() {
        return DialogCateBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
        if (viewIcon != null) {
            viewIcon.startAnimation(openAnim);
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
        if (viewIcon != null) {
            viewIcon.startAnimation(closeAnim);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //确认
//                if (TextUtils.isEmpty(cateUnique)) {
//                    showToast(1, getRstr(R.string.select_goods_cate_big));
//                    return;
//                }
//                if (TextUtils.isEmpty(cateChildUnique)) {
//                    showToast(1, getRstr(R.string.select_goods_cate_small));
//                    return;
//                }
                if (listener != null) {
                    listener.onConfirm(cateUnique, cateName, cateChildUnique, cateChildName);
                    dismiss();
                }
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //一级分类
        cateAdapter = new CateDialogAdapter(getContext());
        mBinding.rvDialogCate.setAdapter(cateAdapter);
        cateAdapter.setOnItemClickListener((view, position) -> {
            if (!cateList.get(position).isCheck()) {
                for (int i = 0; i < cateList.size(); i++) {
                    cateList.get(i).setCheck(false);
                }
                cateList.get(position).setCheck(true);
                cateUnique = cateList.get(position).getGroupUnique();
                cateName = cateList.get(position).getGroupName();
                cateAdapter.setDataList(cateList);
                //二级分类
                cateChildList.clear();
                if (cateList.get(position).getKindDetail() != null) {
                    cateChildList.addAll(cateList.get(position).getKindDetail());
                }
                if (cateChildList.size() > 0) {
                    cateChildList.get(0).setCheck(true);
                    cateChildUnique = cateChildList.get(0).getKindUnique();
                    cateChildName = cateChildList.get(0).getKindName();
                } else {
                    cateChildUnique = "";
                    cateChildName = "";
                }
                cateChildAdapter.setDataList(cateChildList);
            }
        });
        //二级分类
        cateChildAdapter = new CateChildDialogAdapter(getContext());
        mBinding.rvDialogCateChild.setAdapter(cateChildAdapter);
        cateChildAdapter.setOnItemClickListener((view, position) -> {
            if (!cateChildList.get(position).isCheck()) {
                for (int i = 0; i < cateChildList.size(); i++) {
                    cateChildList.get(i).setCheck(false);
                }
                cateChildList.get(position).setCheck(true);
                cateChildUnique = cateChildList.get(position).getKindUnique();
                cateChildName = cateChildList.get(position).getKindName();
                cateChildAdapter.setDataList(cateChildList);
            }
        });
    }

    /**
     * 自定义商品分类查询
     */
    private void getCate() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getCateList(),
                map,
                CateData.class,
                new RequestListListener<CateData>() {
                    @Override
                    public void onResult(List<CateData> list) {
                        hideDialog();
                        //一级分类
                        cateList.clear();
                        cateList.addAll(list);
                        if (type == 1) {
                            cateList.add(0, new CateData(getRstr(R.string.cate_all), "", 0));
                        }
                        if (TextUtils.isEmpty(cateUnique)) {
                            if (cateList.size() > 0) {
                                cateList.get(0).setCheck(true);
                                cateUnique = cateList.get(0).getGroupUnique();
                                cateName = cateList.get(0).getGroupName();
                                //二级分类
                                cateChildList.clear();
                                if (cateList.get(0).getKindDetail() != null) {
                                    cateChildList.addAll(cateList.get(0).getKindDetail());
                                }
                            }
                        } else {
                            for (int i = 0; i < cateList.size(); i++) {
                                if (cateList.get(i).getGroupUnique().equals(cateUnique)) {
                                    cateList.get(i).setCheck(true);
                                    cateName = cateList.get(i).getGroupName();
                                    //二级分类
                                    cateChildList.clear();
                                    if (cateList.get(i).getKindDetail() != null) {
                                        cateChildList.addAll(cateList.get(i).getKindDetail());
                                    }
                                }
                            }
                        }
                        cateAdapter.setDataList(cateList);
                        //二级分类
                        if (TextUtils.isEmpty(cateChildUnique)) {
                            if (cateChildList.size() > 0) {
                                cateChildList.get(0).setCheck(true);
                                cateChildUnique = cateChildList.get(0).getKindUnique();
                                cateChildName = cateChildList.get(0).getKindName();
                            } else {
                                cateChildUnique = "";
                                cateChildName = "";
                            }
                        } else {
                            for (int i = 0; i < cateChildList.size(); i++) {
                                if (cateChildList.get(i).getKindUnique().equals(cateChildUnique)) {
                                    cateChildList.get(i).setCheck(true);
                                    cateChildName = cateChildList.get(i).getKindName();
                                }
                            }
                        }
                        cateChildAdapter.setDataList(cateChildList);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(String unique, String name, String childUnique, String childName);
    }
}
