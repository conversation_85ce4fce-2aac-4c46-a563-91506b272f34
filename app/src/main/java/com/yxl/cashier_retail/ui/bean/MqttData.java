package com.yxl.cashier_retail.ui.bean;

import java.util.List;

/**
 * Describe:MQTT消息（实体类）
 * Created by jingang on 2025/01/10
 */
public class MqttData {
    /**
     * errcode : 0
     * msg : 商品更新成功
     * data : [{"goods_name":"黄豆片","shop_unique":1536215939565,"supplierUnique":"41","pc_shelf_state":"1","foreign_key":10085,"goods_in_price":7.5,"goodStockPrice":7.5,"goods_id":1543246048,"goods_count":-321.93,"goods_sale_price":13.98,"goods_standard":"","goods_cus_price":0.02,"shelf_state":2,"update_time":"25-01-10 09:38:39","goods_barcode":"10085","goods_unit":"盒","goods_alias":"HDP","goods_contain":1,"goods_web_sale_price":0.02,"goodsChengType":1,"goods_kind_unique":"98001","goods_brand":""}]
     * ctrl : msg_goods_update
     * count : 0
     * ID : 98ea2a61870137ed
     * status : 200
     */

    private int errcode;
    private String msg;
    private String ctrl;
    private int count;
    private String ID;
    private int status;
    private List<DataBean> data;

    public int getErrcode() {
        return errcode;
    }

    public void setErrcode(int errcode) {
        this.errcode = errcode;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getCtrl() {
        return ctrl;
    }

    public void setCtrl(String ctrl) {
        this.ctrl = ctrl;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getID() {
        return ID;
    }

    public void setID(String ID) {
        this.ID = ID;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * 新增、编辑
         * goods_name : 黄豆片
         * shop_unique : 1536215939565
         * supplierUnique : 41
         * pc_shelf_state : 1
         * foreign_key : 10085
         * goods_in_price : 7.5
         * goodStockPrice : 7.5
         * goods_id : 1543246048
         * goods_count : -321.93
         * goods_sale_price : 13.98
         * goods_standard :
         * goods_cus_price : 0.02
         * shelf_state : 2
         * update_time : 25-01-10 09:38:39
         * goods_barcode : 10085
         * goods_unit : 盒
         * goods_alias : HDP
         * goods_contain : 1
         * goods_web_sale_price : 0.02
         * goodsChengType : 1
         * goods_kind_unique : 98001
         * goods_brand :
         */

        private String goods_name;
        private String shop_unique;
        private String supplierUnique;
        private int pc_shelf_state;//1.上架 2.下架
        private String foreign_key;
        private double goods_in_price;
        private double goodStockPrice;
        private String goods_id;
        private double goods_count;
        private double goods_sale_price;
        private String goods_standard;
        private double goods_cus_price;
        private int shelf_state;
        private String update_time;
        private String goods_barcode;
        private String goods_unit;
        private String goods_alias;
        private int goods_contain;
        private double goods_web_sale_price;
        private int goodsChengType;
        private String goods_kind_unique;
        private String goods_brand;

        /**
         * 删除
         * "shopUnique":"1536215939565","goodsBarcode":"6920024542482"
         */
        private String shopUnique;
        private String goodsBarcode;

        public String getGoods_name() {
            return goods_name;
        }

        public void setGoods_name(String goods_name) {
            this.goods_name = goods_name;
        }

        public String getShop_unique() {
            return shop_unique;
        }

        public void setShop_unique(String shop_unique) {
            this.shop_unique = shop_unique;
        }

        public String getSupplierUnique() {
            return supplierUnique;
        }

        public void setSupplierUnique(String supplierUnique) {
            this.supplierUnique = supplierUnique;
        }

        public int getPc_shelf_state() {
            return pc_shelf_state;
        }

        public void setPc_shelf_state(int pc_shelf_state) {
            this.pc_shelf_state = pc_shelf_state;
        }

        public String getForeign_key() {
            return foreign_key;
        }

        public void setForeign_key(String foreign_key) {
            this.foreign_key = foreign_key;
        }

        public double getGoods_in_price() {
            return goods_in_price;
        }

        public void setGoods_in_price(double goods_in_price) {
            this.goods_in_price = goods_in_price;
        }

        public double getGoodStockPrice() {
            return goodStockPrice;
        }

        public void setGoodStockPrice(double goodStockPrice) {
            this.goodStockPrice = goodStockPrice;
        }

        public String getGoods_id() {
            return goods_id;
        }

        public void setGoods_id(String goods_id) {
            this.goods_id = goods_id;
        }

        public double getGoods_count() {
            return goods_count;
        }

        public void setGoods_count(double goods_count) {
            this.goods_count = goods_count;
        }

        public double getGoods_sale_price() {
            return goods_sale_price;
        }

        public void setGoods_sale_price(double goods_sale_price) {
            this.goods_sale_price = goods_sale_price;
        }

        public String getGoods_standard() {
            return goods_standard;
        }

        public void setGoods_standard(String goods_standard) {
            this.goods_standard = goods_standard;
        }

        public double getGoods_cus_price() {
            return goods_cus_price;
        }

        public void setGoods_cus_price(double goods_cus_price) {
            this.goods_cus_price = goods_cus_price;
        }

        public int getShelf_state() {
            return shelf_state;
        }

        public void setShelf_state(int shelf_state) {
            this.shelf_state = shelf_state;
        }

        public String getUpdate_time() {
            return update_time;
        }

        public void setUpdate_time(String update_time) {
            this.update_time = update_time;
        }

        public String getGoods_barcode() {
            return goods_barcode;
        }

        public void setGoods_barcode(String goods_barcode) {
            this.goods_barcode = goods_barcode;
        }

        public String getGoods_unit() {
            return goods_unit;
        }

        public void setGoods_unit(String goods_unit) {
            this.goods_unit = goods_unit;
        }

        public String getGoods_alias() {
            return goods_alias;
        }

        public void setGoods_alias(String goods_alias) {
            this.goods_alias = goods_alias;
        }

        public int getGoods_contain() {
            return goods_contain;
        }

        public void setGoods_contain(int goods_contain) {
            this.goods_contain = goods_contain;
        }

        public double getGoods_web_sale_price() {
            return goods_web_sale_price;
        }

        public void setGoods_web_sale_price(double goods_web_sale_price) {
            this.goods_web_sale_price = goods_web_sale_price;
        }

        public int getGoodsChengType() {
            return goodsChengType;
        }

        public void setGoodsChengType(int goodsChengType) {
            this.goodsChengType = goodsChengType;
        }

        public String getGoods_kind_unique() {
            return goods_kind_unique;
        }

        public void setGoods_kind_unique(String goods_kind_unique) {
            this.goods_kind_unique = goods_kind_unique;
        }

        public String getGoods_brand() {
            return goods_brand;
        }

        public void setGoods_brand(String goods_brand) {
            this.goods_brand = goods_brand;
        }

        public String getShopUnique() {
            return shopUnique;
        }

        public void setShopUnique(String shopUnique) {
            this.shopUnique = shopUnique;
        }

        public String getGoodsBarcode() {
            return goodsBarcode;
        }

        public void setGoodsBarcode(String goodsBarcode) {
            this.goodsBarcode = goodsBarcode;
        }
    }
}
