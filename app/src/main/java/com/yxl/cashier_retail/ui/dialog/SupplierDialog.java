package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogSupplierBinding;
import com.yxl.cashier_retail.ui.adapter.SupplierDialogAdapter;
import com.yxl.cashier_retail.ui.bean.SupplierData;
import com.yxl.cashier_retail.ui.bean.SupplierListData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:dialog（选择供货商）
 * Created by jingang on 2024/6/5
 */
@SuppressLint("NonConstantResourceId")
public class SupplierDialog extends BaseDialog<DialogSupplierBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private static View viewIcon;
    private static int type;//1.作为筛选条件，手动添加“全部供货商”
    private String keyWords;
    private final Animation openAnim, closeAnim;

    private SupplierDialogAdapter mAdapter;
    private List<SupplierData> dataList = new ArrayList<>();

    public static void showDialog(Activity activity, View viewIcon, int type, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        SupplierDialog.listener = listener;
        SupplierDialog.viewIcon = viewIcon;
        SupplierDialog.type = type;
        SupplierDialog dialog = new SupplierDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 7);
        dialog.show();
    }

    public SupplierDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        openAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_1);
        closeAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_2);
        openAnim.setFillAfter(true);
        closeAnim.setFillAfter(true);

        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.ivDialogClear.setOnClickListener(this);
        mBinding.etDialog.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                if (TextUtils.isEmpty(keyWords)) {
                    mBinding.ivDialogClear.setVisibility(View.GONE);
                } else {
                    mBinding.ivDialogClear.setVisibility(View.VISIBLE);
                }
            }
        });
        mBinding.etDialog.setOnEditorActionListener((v, actionId, event) -> {
            page = 1;
            getSupplierList();
            return true;
        });
        setAdapter();
        getSupplierList();
    }

    @Override
    protected DialogSupplierBinding getViewBinding() {
        return DialogSupplierBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
        if (viewIcon != null) {
            viewIcon.startAnimation(openAnim);
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
        if (viewIcon != null) {
            viewIcon.startAnimation(closeAnim);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.ivDialogClear:
                //清除搜索输入
                mBinding.etDialog.setText("");
                keyWords = "";
                page = 1;
                getSupplierList();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new SupplierDialogAdapter(getContext());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (listener != null) {
                listener.onConfirm(dataList.get(position));
                dismiss();
            }
        });
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getSupplierList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getSupplierList();
            }
        });
    }


    /**
     * 供货商列表
     */
    private void getSupplierList() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("queryMeg", keyWords);
        params.put("pageIndex", page);
        params.put("pageSize", Constants.limit);
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getSupplierList(),
                params,
                SupplierListData.class,
                new RequestListener<SupplierListData>() {
                    @Override
                    public void success(SupplierListData data) {
                        hideDialog();
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                        if (page == 1) {
                            dataList.clear();
                            dataList.addAll(data.getList());
                            if (type == 1) {
                                dataList.add(0, new SupplierData(getRstr(R.string.supplier_all), "", 0));
                            }
                        } else {
                            dataList.addAll(data.getList());
                        }
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(SupplierData data);
    }
}
