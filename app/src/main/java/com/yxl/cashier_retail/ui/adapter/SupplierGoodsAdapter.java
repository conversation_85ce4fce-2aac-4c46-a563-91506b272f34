package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.GoodsListData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.utils.StringUtils;

/**
 * Describe:供货商管理-所供商品（适配器）
 * Created by jingang on 2024/6/8
 */
public class SupplierGoodsAdapter extends BaseAdapter<GoodsListData> {
    private int type;//0.未建档 1.已建档

    public void setType(int type) {
        this.type = type;
    }

    public SupplierGoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_supplier_goods;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivScale, ivImg, ivCashier, ivApplet;
        TextView tvName, tvBarcode, tvPrice, tvUnit, tvStock, tvCount, tvSuggest, tvConfirm;
        LinearLayout linPrice, linSales, linConfirm;
        ivScale = holder.getView(R.id.ivItemScale);
        ivImg = holder.getView(R.id.ivItemImg);
        tvName = holder.getView(R.id.tvItemName);
        tvBarcode = holder.getView(R.id.tvItemBarcode);
        linPrice = holder.getView(R.id.linItemPrice);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvUnit = holder.getView(R.id.tvItemUnit);
        tvStock = holder.getView(R.id.tvItemStock);
        linSales = holder.getView(R.id.linItemSales);
        tvCount = holder.getView(R.id.tvItemCount);
        ivCashier = holder.getView(R.id.ivItemCashier);
        ivApplet = holder.getView(R.id.ivItemApplet);
        linConfirm = holder.getView(R.id.linItemConfirm);
        tvSuggest = holder.getView(R.id.tvItemSuggest);
        tvConfirm = holder.getView(R.id.tvItemConfirm);

        if (type == 1) {
            linPrice.setVisibility(View.VISIBLE);
            linSales.setVisibility(View.VISIBLE);
            linConfirm.setVisibility(View.GONE);
            tvPrice.setText(DFUtils.getNum2(mDataList.get(position).getGoodsInPrice()));
            if (TextUtils.isEmpty(mDataList.get(position).getGoodsUnit())) {
                tvUnit.setText("");
            } else {
                tvUnit.setText("/" + mDataList.get(position).getGoodsUnit());
            }
            tvStock.setText(DFUtils.getNum4(mDataList.get(position).getGoodsCount()));
            tvCount.setText(getRstr(R.string.sales_month)+ DFUtils.getNum4(mDataList.get(position).getSaleCount()));
            ivCashier.setSelected(mDataList.get(position).getPcShelfState() == 1);
            ivApplet.setSelected(mDataList.get(position).getShelfState() == 1);
        } else {
            linPrice.setVisibility(View.GONE);
            linSales.setVisibility(View.GONE);
            linConfirm.setVisibility(View.VISIBLE);
            if (TextUtils.isEmpty(mDataList.get(position).getGoodsUnit())) {
                tvSuggest.setText(getRstr(R.string.price_suggest_colon) + DFUtils.getNum2(mDataList.get(position).getGoodsSalePriceUndoc()));
            }
        }

        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoodsPicturePath()))
                .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img))
                .into(ivImg);
        tvName.setText(mDataList.get(position).getGoodsName());
        tvBarcode.setText(mDataList.get(position).getGoodsBarcode());

        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(v, position));
            tvConfirm.setOnClickListener(v -> listener.onConfirmClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onConfirmClick(View view, int position);
    }
}
