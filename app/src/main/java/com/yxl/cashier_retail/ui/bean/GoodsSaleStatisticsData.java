package com.yxl.cashier_retail.ui.bean;

import java.util.List;

/**
 * Describe:商品销售统计（实体类）-写出花来
 * Created by jingang on 2024/8/24
 */
public class GoodsSaleStatisticsData {
    /**
     * status : 1
     * msg : Запрос удался！
     * data : [{"saleCount":0,"saleSum":0,"giftCount":0,"purSum":0,"goodsName":"大白兔奶糖","goodsBarcode":"6665555","grossProfit":-3.64},{"saleCount":0,"saleSum":0,"giftCount":0,"purSum":0,"goodsName":"小白兔奶糖","goodsBarcode":"6665557","grossProfit":-88.89}]
     * data1 : [{"saleCount":0,"saleSum":0,"purSum":0,"grossProfit":0}]
     * address : 1
     * cus_data : null
     */

    private int status;
    private String msg;
    private int address;
    private Object cus_data;
    private List<DataBean> data;
    private List<Data1Bean> data1;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getAddress() {
        return address;
    }

    public void setAddress(int address) {
        this.address = address;
    }

    public Object getCus_data() {
        return cus_data;
    }

    public void setCus_data(Object cus_data) {
        this.cus_data = cus_data;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public List<Data1Bean> getData1() {
        return data1;
    }

    public void setData1(List<Data1Bean> data1) {
        this.data1 = data1;
    }

    public static class DataBean {
        /**
         * saleCount : 0.0
         * saleSum : 0.0
         * giftCount : 0.0
         * purSum : 0.0
         * goodsName : 大白兔奶糖
         * goodsBarcode : 6665555
         * grossProfit : -3.64
         */
        private boolean select;
        private double saleCount;//销售数量
        private double saleSum;//销售总额
        private double giftCount;//赠送数量
        private double purSum;//成本
        private String goodsName;
        private String goodsBarcode;
        private double grossProfit;//利润率

        public boolean isSelect() {
            return select;
        }

        public void setSelect(boolean select) {
            this.select = select;
        }

        public double getSaleCount() {
            return saleCount;
        }

        public void setSaleCount(double saleCount) {
            this.saleCount = saleCount;
        }

        public double getSaleSum() {
            return saleSum;
        }

        public void setSaleSum(double saleSum) {
            this.saleSum = saleSum;
        }

        public double getGiftCount() {
            return giftCount;
        }

        public void setGiftCount(double giftCount) {
            this.giftCount = giftCount;
        }

        public double getPurSum() {
            return purSum;
        }

        public void setPurSum(double purSum) {
            this.purSum = purSum;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public String getGoodsBarcode() {
            return goodsBarcode;
        }

        public void setGoodsBarcode(String goodsBarcode) {
            this.goodsBarcode = goodsBarcode;
        }

        public double getGrossProfit() {
            return grossProfit;
        }

        public void setGrossProfit(double grossProfit) {
            this.grossProfit = grossProfit;
        }
    }

    public static class Data1Bean {
        /**
         * saleCount : 0.0
         * saleSum : 0.0
         * purSum : 0.0
         * grossProfit : 0.0
         */

        private double saleCount;//销售总数量
        private double saleSum;//销售总金额
        private double purSum;//销售总成本
        private double grossProfit;//销售总利润

        public double getSaleCount() {
            return saleCount;
        }

        public void setSaleCount(double saleCount) {
            this.saleCount = saleCount;
        }

        public double getSaleSum() {
            return saleSum;
        }

        public void setSaleSum(double saleSum) {
            this.saleSum = saleSum;
        }

        public double getPurSum() {
            return purSum;
        }

        public void setPurSum(double purSum) {
            this.purSum = purSum;
        }

        public double getGrossProfit() {
            return grossProfit;
        }

        public void setGrossProfit(double grossProfit) {
            this.grossProfit = grossProfit;
        }
    }
}
