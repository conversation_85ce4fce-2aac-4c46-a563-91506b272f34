package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.MemberLevelData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:会员等级-弹窗（适配器）
 * Created by jingang on 2024/8/6
 */
public class MemberLevelDialogAdapter extends BaseAdapter<MemberLevelData> {

    public MemberLevelDialogAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_member_level_dialog;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvPoints, tvHint;
        tvName = holder.getView(R.id.tvItemName);
        LinearLayout lin = holder.getView(R.id.linItem);
        tvPoints = holder.getView(R.id.tvItemPoints);
        ImageView ivCursor = holder.getView(R.id.ivItemCursor);
        tvHint = holder.getView(R.id.tvItemHint);

        ((AnimationDrawable) ivCursor.getDrawable()).start();
        tvName.setText(mDataList.get(position).getCusLevelName());
        if (mDataList.get(position).getCusLevelPoints() > 0) {
            tvPoints.setVisibility(View.VISIBLE);
            tvHint.setVisibility(View.GONE);
            tvPoints.setText(String.valueOf(mDataList.get(position).getCusLevelPoints()));
        } else {
            tvPoints.setVisibility(View.GONE);
            tvHint.setVisibility(View.VISIBLE);
            tvPoints.setText("");
        }
        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundResource(R.drawable.shape_green_kuang_5);
            ivCursor.setVisibility(View.VISIBLE);
        } else {
            lin.setBackgroundResource(R.drawable.shape_d8_kuang_5);
            ivCursor.setVisibility(View.GONE);
        }
    }
}
