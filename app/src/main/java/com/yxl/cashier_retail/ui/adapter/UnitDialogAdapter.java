package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.UnitData;

/**
 * Describe:商品单位-弹窗（适配器）
 * Created by jingang on 2024/7/1
 */
public class UnitDialogAdapter extends BaseAdapter<UnitData> {

    public UnitDialogAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_unit_dialog;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivEdit, ivDel;
        TextView tvName = holder.getView(R.id.tvItemName);
        ivEdit = holder.getView(R.id.ivItemEdit);
        ivDel = holder.getView(R.id.ivItemDel);
        tvName.setText(mDataList.get(position).getGoods_unit());
        if (mDataList.get(position).isSelect()) {
            tvName.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
        } else {
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
        }

        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(position));
            ivEdit.setOnClickListener(v -> listener.onEditClick(position));
            ivDel.setOnClickListener(v -> listener.onDelClick(position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(int position);

        void onEditClick(int position);

        void onDelClick(int position);
    }
}
