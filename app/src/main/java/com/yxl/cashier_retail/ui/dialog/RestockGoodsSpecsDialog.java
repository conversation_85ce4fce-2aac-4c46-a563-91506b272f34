package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogRestockGoodsSpecsBinding;
import com.yxl.cashier_retail.ui.adapter.GoodsSpecsDialogAdapter;
import com.yxl.cashier_retail.ui.bean.GoodsInfoData;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:dialog（商品规格）
 * Created by jingang on 2024/6/6
 */
@SuppressLint("NonConstantResourceId")
public class RestockGoodsSpecsDialog extends BaseDialog<DialogRestockGoodsSpecsBinding> implements View.OnClickListener {
    private static GoodsInfoData data;

    private GoodsSpecsDialogAdapter mAdapter;

    public static void showDialog(Activity activity, GoodsInfoData data, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        RestockGoodsSpecsDialog.listener = listener;
        RestockGoodsSpecsDialog.data = data;
        RestockGoodsSpecsDialog dialog = new RestockGoodsSpecsDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 7);
        dialog.show();
    }

    public RestockGoodsSpecsDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected DialogRestockGoodsSpecsBinding getViewBinding() {
        return DialogRestockGoodsSpecsBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //确认
                if (listener != null) {
                    listener.onConfirm(data);
                    dismiss();
                }
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new GoodsSpecsDialogAdapter(getContext());
        mBinding.rvDialog.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (!data.getListDetail().get(position).isCheck()) {
                for (int i = 0; i < data.getListDetail().size(); i++) {
                    if (data.getListDetail().get(i).isCheck()) {
                        data.getListDetail().get(i).setCheck(false);
                        mAdapter.notifyItemChanged(i);
                    }
                }
                data.getListDetail().get(position).setCheck(true);
                mAdapter.notifyItemChanged(position);
            }
        });
        if (data == null) {
            return;
        }
        if (data.getListDetail() == null) {
            return;
        }
        mAdapter.setDataList(data.getListDetail());
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(GoodsInfoData data);
    }
}
