package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogOrderRefundBinding;
import com.yxl.cashier_retail.ui.bean.QueryOrderInfoData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.view.NumberKeyBoardView;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.SystemUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Describe:dialog（订单退款）
 * Created by jingang on 2024/6/20
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class OrderRefundDialog extends BaseDialog<DialogOrderRefundBinding> implements View.OnClickListener {
    private static QueryOrderInfoData.DataBean data;
    private static int saleListPayment;//支付方式
    private static String goodsMsg;
    private static double total;
    private boolean isPrint;//是否退款打印小票
    private int type;//13.金圈平台 1.现金 2.支付宝 3.微信 5.储值卡

    public static void showDialog(Activity activity, QueryOrderInfoData.DataBean data, int saleListPayment, String goodsMsg, double total, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        OrderRefundDialog.listener = listener;
        OrderRefundDialog.data = data;
        OrderRefundDialog.saleListPayment = saleListPayment;
        OrderRefundDialog.goodsMsg = goodsMsg;
        OrderRefundDialog.total = total;
        OrderRefundDialog dialog = new OrderRefundDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public OrderRefundDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        ((AnimationDrawable) mBinding.ivDialogCursor.getDrawable()).start();
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.ivDialogPrint.setOnClickListener(this);
        mBinding.tvDialogJinQ.setOnClickListener(this);
        mBinding.tvDialogStore.setOnClickListener(this);
        mBinding.tvDialogCash.setOnClickListener(this);
        mBinding.tvDialogAlipay.setOnClickListener(this);
        mBinding.tvDialogWechat.setOnClickListener(this);
        mBinding.numberKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var) {
                mBinding.tvDialogRefund.setText(var);
                if (TextUtils.isEmpty(var)) {
                    mBinding.tvDialogRefund.setVisibility(View.GONE);
                    mBinding.tvDialogRefundHint.setVisibility(View.VISIBLE);
                    total = 0;
                } else {
                    mBinding.tvDialogRefund.setVisibility(View.VISIBLE);
                    mBinding.tvDialogRefundHint.setVisibility(View.GONE);
                    total = Double.parseDouble(var);
                }
            }

            @Override
            public void onConfirm() {
                if (total <= 0) {
                    showToast(1, getRstr(R.string.input_refund_total));
                    return;
                }
                if (type == 0) {
                    showToast(1, getRstr(R.string.select_refund_type));
                    return;
                }
                postRefund();
            }
        });
        setUI();
    }

    @Override
    protected DialogOrderRefundBinding getViewBinding() {
        return DialogOrderRefundBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.ivDialogPrint:
                //退款打印小票
                isPrint = !isPrint;
                mBinding.ivDialogPrint.setSelected(isPrint);
                break;
            case R.id.tvDialogJinQ:
                //金圈平台
                if (type != 13) {
                    type = 13;
                    clearTextBg();
                    mBinding.tvDialogJinQ.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvDialogJinQ.setTextColor(getContext().getResources().getColor(R.color.white));
                    mBinding.tvDialogTips.setText(getRstr(R.string.refund_jinquan_plat_tips));
                }
                break;
            case R.id.tvDialogStore:
                //储值卡
                if (type != 5) {
                    type = 5;
                    clearTextBg();
                    mBinding.tvDialogStore.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvDialogStore.setTextColor(getContext().getResources().getColor(R.color.white));
                    mBinding.tvDialogTips.setText(getRstr(R.string.refund_stored_tips));
                }
                break;
            case R.id.tvDialogCash:
                //现金
                if (type != 1) {
                    type = 1;
                    clearTextBg();
                    mBinding.tvDialogCash.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvDialogCash.setTextColor(getContext().getResources().getColor(R.color.white));
                    mBinding.tvDialogTips.setText(getRstr(R.string.refund_cash_tips));
                }
                break;
            case R.id.tvDialogAlipay:
                //支付宝
                if (type != 2) {
                    type = 2;
                    clearTextBg();
                    mBinding.tvDialogAlipay.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvDialogAlipay.setTextColor(getContext().getResources().getColor(R.color.white));
                    mBinding.tvDialogTips.setText(getRstr(R.string.refund_alipay_tips));
                }
                break;
            case R.id.tvDialogWechat:
                //微信
                if (type != 3) {
                    type = 3;
                    clearTextBg();
                    mBinding.tvDialogWechat.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvDialogWechat.setTextColor(getContext().getResources().getColor(R.color.white));
                    mBinding.tvDialogTips.setText(getRstr(R.string.refund_wechat_tips));
                }
                break;
        }
    }

    /**
     * 更新UI
     */
    private void setUI() {
        mBinding.tvDialogTotal.setText(getRstr(R.string.order_total_colon) + DFUtils.getNum2(total));
        mBinding.tvDialogRefund.setText(DFUtils.getNum4(total));
        mBinding.tvDialogRefund.setVisibility(View.VISIBLE);
        mBinding.tvDialogRefundHint.setVisibility(View.GONE);
        if (data != null) {
            mBinding.tvDialogPayment.setText(getRstr(R.string.collection_type_colon) + data.getSale_list_payment());
        }
        //1.现金 2.支付宝 3.微信 4.银行卡 5.储值卡 8.混合支付 10.积分兑换 13.金圈平台
//        switch (saleListPayment) {
//            case 13:
//                type = 13;
//                clearTextBg();
//                mBinding.tvDialogJinQ.setBackgroundResource(R.drawable.shape_green_5);
//                mBinding.tvDialogJinQ.setTextColor(getContext().getResources().getColor(R.color.white));
//                mBinding.tvDialogTips.setText(getRstr(R.string.refund_jinquan_plat_tips));
//                break;
//            case 5:
//                type = 5;
//                clearTextBg();
//                mBinding.tvDialogStore.setBackgroundResource(R.drawable.shape_green_5);
//                mBinding.tvDialogStore.setTextColor(getContext().getResources().getColor(R.color.white));
//                mBinding.tvDialogTips.setText(getRstr(R.string.refund_stored_tips));
//                break;
//            case 2:
//                type = 2;
//                clearTextBg();
//                mBinding.tvDialogAlipay.setBackgroundResource(R.drawable.shape_green_5);
//                mBinding.tvDialogAlipay.setTextColor(getContext().getResources().getColor(R.color.white));
//                mBinding.tvDialogTips.setText(getRstr(R.string.refund_alipay_tips));
//                break;
//            case 3:
//                type = 3;
//                clearTextBg();
//                mBinding.tvDialogWechat.setBackgroundResource(R.drawable.shape_green_5);
//                mBinding.tvDialogWechat.setTextColor(getContext().getResources().getColor(R.color.white));
//                mBinding.tvDialogTips.setText(getRstr(R.string.refund_wechat_tips));
//                break;
//            default:
//                type = 1;
//                clearTextBg();
//                mBinding.tvDialogCash.setBackgroundResource(R.drawable.shape_green_5);
//                mBinding.tvDialogCash.setTextColor(getContext().getResources().getColor(R.color.white));
//                mBinding.tvDialogTips.setText(getRstr(R.string.refund_cash_tips));
//                break;
//        }
        if (saleListPayment == 13) {
            mBinding.tvDialogJinQ.setVisibility(View.VISIBLE);
            type = 13;
            clearTextBg();
            mBinding.tvDialogJinQ.setBackgroundResource(R.drawable.shape_green_5);
            mBinding.tvDialogJinQ.setTextColor(getContext().getResources().getColor(R.color.white));
            mBinding.tvDialogTips.setText(getRstr(R.string.refund_jinquan_plat_tips));
        } else {
            mBinding.tvDialogJinQ.setVisibility(View.GONE);
            type = 1;
            clearTextBg();
            mBinding.tvDialogCash.setBackgroundResource(R.drawable.shape_green_5);
            mBinding.tvDialogCash.setTextColor(getContext().getResources().getColor(R.color.white));
            mBinding.tvDialogTips.setText(getRstr(R.string.refund_cash_tips));
        }
    }

    /**
     * 清除按钮选中样式
     */
    private void clearTextBg() {
        mBinding.tvDialogJinQ.setBackgroundResource(R.drawable.shape_d8_kuang_f0_5);
        mBinding.tvDialogJinQ.setTextColor(getContext().getResources().getColor(R.color.black));
        mBinding.tvDialogStore.setBackgroundResource(R.drawable.shape_d8_kuang_f0_5);
        mBinding.tvDialogStore.setTextColor(getContext().getResources().getColor(R.color.black));
        mBinding.tvDialogCash.setBackgroundResource(R.drawable.shape_d8_kuang_f0_5);
        mBinding.tvDialogCash.setTextColor(getContext().getResources().getColor(R.color.black));
        mBinding.tvDialogAlipay.setBackgroundResource(R.drawable.shape_d8_kuang_f0_5);
        mBinding.tvDialogAlipay.setTextColor(getContext().getResources().getColor(R.color.black));
        mBinding.tvDialogWechat.setBackgroundResource(R.drawable.shape_d8_kuang_f0_5);
        mBinding.tvDialogWechat.setTextColor(getContext().getResources().getColor(R.color.black));
    }

    /**
     * 订单退款
     */
    private void postRefund() {
        if (data == null) {
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("staffId", getStaffUnique());
        params.put("retOrigin", 4);
        params.put("saleListUnique", data.getSale_list_unique());
//        params.put("macId", SystemUtils.getDeviceId(getContext()));
        params.put("macId", SPUtils.getInstance().getString(Constants.DEVICE_ID, ""));
        //订单退款状态 1.未退款 2.已退款
        //退款处理状态 1:未处理；2：已受理；3：受理完毕
        if (type == 1 || type == 2 || type == 3) {
            params.put("retListState", 2);
            params.put("retListHandlestate", 3);
        } else {
            params.put("retListState", 1);
            params.put("retListHandlestate", 1);
        }
//        params.put("retListRemarks", remarks);
        params.put("retListTotalMoney", total);//退款金额
        params.put("retMoneyType", type);//退款方式 1.现金 2.支付宝 3.微信 4.银行卡 5.储值卡 13.免密支付
        params.put("goodsMessage", goodsMsg);//退款商品信息 30531471:3:2.01;220002:2:2.03;220003:1:0.93
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getRefundRecordAdd(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        if (listener != null) {
                            listener.onConfirm();
                            dismiss();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm();
    }
}
