package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.google.android.material.tabs.TabLayout;
import com.google.gson.Gson;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmOrderBinding;
import com.yxl.cashier_retail.ui.adapter.OrderGoodsAdapter;
import com.yxl.cashier_retail.ui.adapter.SimpleFragmentPagerAdapter;
import com.yxl.cashier_retail.ui.bean.ConditionData;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.OrderCountData;
import com.yxl.cashier_retail.ui.bean.OrderCountDataOld;
import com.yxl.cashier_retail.ui.bean.OrderGoodsData;
import com.yxl.cashier_retail.ui.bean.OrderInfoData;
import com.yxl.cashier_retail.ui.dialog.DateStartEndDialog;
import com.yxl.cashier_retail.ui.dialog.RiderDialog;
import com.yxl.cashier_retail.ui.popupwindow.ConditionPop;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.utils.DateUtils;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:网单-网单订单
 * Created by jingang on 2024/6/17
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class OrderFragment extends BaseFragment<FmOrderBinding> implements View.OnClickListener {
    public static String keyWords = "",//搜索关键字
            startDate, endDate;
    public static int conditionId;//网单类型id

    //网单类型
    private List<ConditionData> conditionList = new ArrayList<>();

    private SimpleFragmentPagerAdapter pagerAdapter;
    private List<String> titleList = new ArrayList<>();
    private List<Fragment> fragmentList = new ArrayList<>();
    private List<Integer> countList = new ArrayList<>();

    //订单详情
    private String saleListUnique;//订单编号
    private int shippingMethod,//配送方式1:送货上门 2:自提
            deliveryType = -1,//送货方式，0：自配送 2:一刻钟配送
            orderStatus;//订单状态 1.无效订单 2.新订单（待发货） 3.已发货（代收货）4.已完成（已收货）5.已取消 6.待评论 7.待骑手配送 10.配送异常
    private double goodTotalCount;//订单商品总数量
    private List<OrderGoodsData> goodsList = new ArrayList<>();
    private OrderGoodsAdapter goodsAdapter;

    /**
     * 搜索
     *
     * @param keyWords
     */
    public static void setKeyWords(String keyWords) {
        OrderFragment.keyWords = keyWords;
        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.ORDER_LIST));
    }

    @Override
    protected FmOrderBinding getViewBinding() {
        return FmOrderBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.linType.setOnClickListener(this);
        mBinding.linStartDate.setOnClickListener(this);
        mBinding.linEndDate.setOnClickListener(this);
        //订单详情
        mBinding.tvOrderConfirm.setOnClickListener(this);
        mBinding.tvOrderGet.setOnClickListener(this);
        mBinding.tvOrderDeliveryCancel.setOnClickListener(this);
        mBinding.tvOrderCancel.setOnClickListener(this);
        mBinding.tvOrderPick.setOnClickListener(this);
        mBinding.tvOrderAgain.setOnClickListener(this);

        startDate = DateUtils.getOldDate(0);
        endDate = startDate;
        mBinding.tvStartDate.setText(startDate);
        mBinding.tvEndDate.setText(endDate);
        setFragment();
        setAdapter();
    }

    @Override
    protected void initData() {
        conditionList.clear();
        conditionList.add(new ConditionData(0, getRstr(R.string.order_type), true));
        conditionList.add(new ConditionData(1, getRstr(R.string.order_type0), false));
        conditionList.add(new ConditionData(2, getRstr(R.string.order_type1), false));
        conditionList.add(new ConditionData(3, getRstr(R.string.order_type2), false));
        getOrderCount();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.linType:
                //网单类型
                ConditionPop.showDialog(mContext,
                        mBinding.ivType,
                        v,
                        mBinding.linType.getMeasuredWidth(),
                        conditionList,
                        conditionId,
                        data -> {
                            conditionId = data.getId();
                            mBinding.tvType.setText(data.getName());
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.ORDER_LIST));
                        });
                break;
            case R.id.linStartDate:
                //开始日期
                showDialogDate(0, mBinding.ivStartDate);
                break;
            case R.id.linEndDate:
                //结束日期
                showDialogDate(1, mBinding.ivEndDate);
                break;
            case R.id.tvOrderConfirm:
                //接单
            case R.id.tvOrderAgain:
                //重新接单
                if (shippingMethod == 1) {
                    switch (deliveryType) {
                        case 0:
                            //自配送
                            RiderDialog.showDialog(getActivity(), 0, data -> {
                                postOrderCreate("0", String.valueOf(data.getId()), data.getCourier_name(), data.getCourier_phone());
                            });
                            break;
                        case 2:
                            //一刻钟配送
                            postOrderCreate(String.valueOf(goodTotalCount), "", "", "");
                            break;
                        default:
                            showToast(1, "该订单不支持接单");
                            break;
                    }
                }
                break;
            case R.id.tvOrderGet:
                //确认收货
            case R.id.tvOrderPick:
                //确认取货
                postOrderFinish();
                break;
            case R.id.tvOrderDeliveryCancel:
                //取消配送
                postOrderDeliveryCancel();
                break;
            case R.id.tvOrderCancel:
                //取消订单
                postOrderCancel();
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.ORDER_COUNT:
                //订单数量
                getOrderCount();
                break;
            case Constants.ORDER_INFO:
                //订单详情
                saleListUnique = event.getTypes();
                if (TextUtils.isEmpty(saleListUnique)) {
                    mBinding.linInfo.setVisibility(View.GONE);
                    mBinding.tvNothing.setVisibility(View.VISIBLE);
                } else {
                    getOrderInfo();
                }
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        goodsAdapter = new OrderGoodsAdapter(getActivity(), 0);
        mBinding.rvGoods.setAdapter(goodsAdapter);
    }

    /**
     * dialog（选择日期时间）
     */
    private void showDialogDate(int type, View view) {
        DateStartEndDialog.showDialog(getActivity(), view, startDate, endDate, type, (startDate, endDate) -> {
            OrderFragment.startDate = startDate;
            OrderFragment.endDate = endDate;
            mBinding.tvStartDate.setText(startDate);
            mBinding.tvEndDate.setText(endDate);
            getOrderCount();
            //请求数据
            EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.ORDER_LIST));
        });
    }

    /**
     * 设置viewpage
     */
    private void setFragment() {
        keyWords = "";
        startDate = "";
        endDate = "";
        conditionId = 0;
        titleList.clear();
        titleList.add(getRstr(R.string.all));
        titleList.add(getRstr(R.string.order_status0));
        titleList.add(getRstr(R.string.order_status1));
        titleList.add(getRstr(R.string.order_status2));
        titleList.add(getRstr(R.string.order_status3));
        titleList.add(getRstr(R.string.order_status4));
        titleList.add(getRstr(R.string.order_status5));
        titleList.add(getRstr(R.string.order_status6));

        countList.clear();
        for (int i = 0; i < 8; i++) {
            countList.add(0);
        }
        fragmentList.clear();
        for (int i = 0; i < titleList.size(); i++) {
            switch (i) {
                case 0:
                    fragmentList.add(new OrderChildFragment(-1));
                    break;
                case 1:
                    fragmentList.add(new OrderChildFragment(2));
                    break;
                case 2:
                    fragmentList.add(new OrderChildFragment(7));
                    break;
                case 3:
                    fragmentList.add(new OrderChildFragment(10));
                    break;
                case 4:
                    fragmentList.add(new OrderChildFragment(3));
                    break;
                case 5:
                    fragmentList.add(new OrderChildFragment(9));
                    break;
                case 6:
                    fragmentList.add(new OrderChildFragment(6));
                    break;
                case 7:
                    fragmentList.add(new OrderChildFragment(4));
                    break;
            }
        }

        pagerAdapter = new SimpleFragmentPagerAdapter(getActivity(), getChildFragmentManager(), fragmentList, titleList, countList);
        mBinding.viewPager.setAdapter(pagerAdapter);
        mBinding.viewPager.setCurrentItem(0);
        mBinding.viewPager.setOffscreenPageLimit(titleList.size());
        mBinding.tabLayout.setupWithViewPager(mBinding.viewPager);
        mBinding.tabLayout.setSelectedTabIndicatorHeight(0);
        mBinding.tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                updateTabTextView(tab, true);
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                updateTabTextView(tab, false);
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });
        setUpTabBadge();

    }

    /**
     * 设置tabLayout上的标题的角标
     */
    private void setUpTabBadge() {
        for (int i = 0; i < fragmentList.size(); i++) {
            TabLayout.Tab tab = mBinding.tabLayout.getTabAt(i);
            View customView = tab.getCustomView();
            if (customView != null) {
                ViewParent parent = customView.getParent();
                ViewGroup viewGroup = (ViewGroup) parent;
                if (parent != null) {
                    try {
                        viewGroup.removeView(customView);
                    } catch (Exception ignored) {
                    }
                }
            }
            // 更新CustomView
            tab.setCustomView(pagerAdapter.getTabItemView(i));
        }

        // 需加上以下代码,不然会出现更新Tab角标后,选中的Tab字体颜色不是选中状态的颜色
        TabLayout.Tab tabAt = mBinding.tabLayout.getTabAt(mBinding.tabLayout.getSelectedTabPosition());
        updateTabTextView(tabAt, true);
    }

    /**
     * 设置tabLayout标题样式和横杠
     */
    private void updateTabTextView(TabLayout.Tab tab, Boolean isSelect) {
        if (tab == null) {
            return;
        }
        if (tab.getCustomView() == null) {
            return;
        }
        TextView tabSelect = tab.getCustomView().findViewById(R.id.tvItemName);
        View line = tab.getCustomView().findViewById(R.id.vItem);
        if (isSelect) {
            //选中加粗
            tabSelect.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            tabSelect.setText(tab.getText());
            line.setVisibility(View.VISIBLE);
        } else {
            tabSelect.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            tabSelect.setText(tab.getText());
            line.setVisibility(View.GONE);
        }
    }

    /**
     * 更新UI-订单详情
     *
     * @param data
     */
    private void setUIInfo(OrderInfoData data) {
        if (data == null) {
            mBinding.linInfo.setVisibility(View.GONE);
            mBinding.tvNothing.setVisibility(View.VISIBLE);
            return;
        }
        mBinding.linInfo.setVisibility(View.VISIBLE);
        mBinding.tvNothing.setVisibility(View.GONE);
        orderStatus = data.getHandleStateCode();
        shippingMethod = data.getShipping_method();
        deliveryType = data.getDelivery_type();
        goodTotalCount = data.getTotalCount();

        //0-已删除-1无效订单-2待发货-3待收货-4已完成-5已取消-6待评论-7配送单待确认 8-待付款9-待自提 10-配送异常 11-已核单未发货'
        switch (orderStatus) {
            case 2:
                mBinding.tvOrderConfirm.setVisibility(View.VISIBLE);
                mBinding.tvOrderGet.setVisibility(View.GONE);
                mBinding.tvOrderDeliveryCancel.setVisibility(View.GONE);
                mBinding.tvOrderCancel.setVisibility(View.GONE);
                mBinding.tvOrderPick.setVisibility(View.GONE);
                mBinding.tvOrderAgain.setVisibility(View.GONE);
                break;
            case 3:
                mBinding.tvOrderConfirm.setVisibility(View.GONE);
                mBinding.tvOrderGet.setVisibility(View.VISIBLE);
                mBinding.tvOrderDeliveryCancel.setVisibility(View.GONE);
                mBinding.tvOrderCancel.setVisibility(View.GONE);
                mBinding.tvOrderPick.setVisibility(View.GONE);
                mBinding.tvOrderAgain.setVisibility(View.GONE);
                break;
            case 7:
                mBinding.tvOrderConfirm.setVisibility(View.GONE);
                mBinding.tvOrderGet.setVisibility(View.GONE);
                mBinding.tvOrderDeliveryCancel.setVisibility(View.VISIBLE);
                mBinding.tvOrderCancel.setVisibility(View.GONE);
                mBinding.tvOrderPick.setVisibility(View.GONE);
                mBinding.tvOrderAgain.setVisibility(View.GONE);
                break;
            case 8:
                mBinding.tvOrderConfirm.setVisibility(View.GONE);
                mBinding.tvOrderGet.setVisibility(View.GONE);
                mBinding.tvOrderDeliveryCancel.setVisibility(View.GONE);
                mBinding.tvOrderCancel.setVisibility(View.VISIBLE);
                mBinding.tvOrderPick.setVisibility(View.GONE);
                mBinding.tvOrderAgain.setVisibility(View.GONE);
                break;
            case 9:
                mBinding.tvOrderConfirm.setVisibility(View.GONE);
                mBinding.tvOrderGet.setVisibility(View.GONE);
                mBinding.tvOrderDeliveryCancel.setVisibility(View.GONE);
                mBinding.tvOrderCancel.setVisibility(View.GONE);
                mBinding.tvOrderPick.setVisibility(View.VISIBLE);
                mBinding.tvOrderAgain.setVisibility(View.GONE);
                break;
            case 10:
                mBinding.tvOrderConfirm.setVisibility(View.GONE);
                mBinding.tvOrderGet.setVisibility(View.GONE);
                mBinding.tvOrderDeliveryCancel.setVisibility(View.GONE);
                mBinding.tvOrderCancel.setVisibility(View.GONE);
                mBinding.tvOrderPick.setVisibility(View.GONE);
                mBinding.tvOrderAgain.setVisibility(View.VISIBLE);
                break;
            default:
                mBinding.tvOrderConfirm.setVisibility(View.GONE);
                mBinding.tvOrderGet.setVisibility(View.GONE);
                mBinding.tvOrderDeliveryCancel.setVisibility(View.GONE);
                mBinding.tvOrderCancel.setVisibility(View.GONE);
                mBinding.tvOrderPick.setVisibility(View.GONE);
                mBinding.tvOrderAgain.setVisibility(View.GONE);
                break;
        }

        if (TextUtils.isEmpty(data.getSaleListName())) {
            if (TextUtils.isEmpty(data.getSaleListPhone())) {
                mBinding.tvName.setText("-");
            } else {
                mBinding.tvName.setText(data.getSaleListPhone());
            }
        } else {
            if (TextUtils.isEmpty(data.getSaleListPhone())) {
                mBinding.tvName.setText(data.getSaleListName());
            } else {
                mBinding.tvName.setText(data.getSaleListName() + "-" + data.getSaleListPhone());
            }
        }
        if (TextUtils.isEmpty(data.getSaleListAddress())) {
            mBinding.tvAds.setText("-");
        } else {
            mBinding.tvAds.setText(data.getSaleListAddress());
        }
        if (data.getShipping_method() == 1) {
            mBinding.tvDeliveryType.setText(getRstr(R.string.order_type2));
        } else {
            mBinding.tvDeliveryType.setText(getRstr(R.string.order_type1));
        }

        //订单信息
        mBinding.tvOrderTotal.setText(DFUtils.getNum2(data.getSaleListTotal() + data.getPeisong_money()));
        mBinding.tvOrderDiscount.setText("-" + DFUtils.getNum2(data.getCoupon_amount()));
        mBinding.tvOrderBeans.setText("-" + DFUtils.getNum2(data.getBeans_money()));
        mBinding.tvOrderReceive.setText(DFUtils.getNum2(data.getActuallyReceived()));
        mBinding.tvOrderNo.setText(data.getSaleListUnique());
        if (TextUtils.isEmpty(data.getPayTime())) {
            mBinding.tvOrderPayTime.setText("-");
        } else {
            mBinding.tvOrderPayTime.setText(data.getPayTime());
        }
        if (TextUtils.isEmpty(data.getSaleListPayment())) {
            mBinding.tvOrderPayType.setText("-");
        } else {
            mBinding.tvOrderPayType.setText(data.getSaleListPayment());
        }

        //商品信息
        goodsList.clear();
        if (data.getListDetail() != null) {
            goodsList.addAll(data.getListDetail());
        }
        goodsAdapter.setDataList(goodsList);
    }

    /**
     * 获取每种状态下的订单数量
     */
    private void getOrderCount() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("saleListMessage", keyWords);
        RXHttpUtil.requestByFormPostAsResponseList(getActivity(), ZURL.getOrderCount(), params, OrderCountDataOld.class, new RequestListListener<OrderCountDataOld>() {
            @Override
            public void onResult(List<OrderCountDataOld> dataList) {
                countList.clear();
                for (int i = 0; i < 8; i++) {
                    countList.add(0);
                }
                for (OrderCountDataOld data : dataList) {
                    switch (data.getHandleState()) {
                        case -1:
                            countList.set(0, data.getCount());
                            break;
                        case 2:
                            countList.set(1, data.getCount());
                            break;
                        case 7:
                            countList.set(2, data.getCount());
                            break;
                        case 10:
                            countList.set(3, data.getCount());
                            break;
                        case 3:
                            countList.set(4, data.getCount());
                            break;
                        case 9:
                            countList.set(5, data.getCount());
                            break;
                        case 6:
                            countList.set(6, data.getCount());
                            break;
                        case 4:
                            countList.set(7, data.getCount());
                            break;
                    }
                }
                setUpTabBadge();
            }
        });
    }

    /**
     * 销售订单详情
     */
    private void getOrderInfo() {
        Map<String, Object> map = new HashMap<>();
        map.put("saleListUnique", saleListUnique);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this, ZURL.getOrderInfo(), map, OrderInfoData.class, new RequestListener<OrderInfoData>() {
            @Override
            public void success(OrderInfoData data) {
                hideDialog();
                setUIInfo(data);
            }

            @Override
            public void onError(String msg) {
                hideDialog();
                showToast(1, msg);
                mBinding.linInfo.setVisibility(View.GONE);
                mBinding.tvNothing.setVisibility(View.VISIBLE);
            }
        });
    }

    /**
     * 创建自配送
     */
    private void postOrderCreate(String count, String id, String name, String phone) {
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("sale_list_unique", saleListUnique);
        params.put("delivery_type", deliveryType);
        params.put("goods_weight", count);
        params.put("shop_courier_id", id);
        params.put("courier_name", name);
        params.put("courier_phone", phone);
        params.put("sale_list_cashier", getStaffUnique());
        params.put("return_price", 0.00);
        params.put("goodsList", "");
        RXHttpUtil.requestByBodyPostAsResponse(this, ZURL.getOrderCreate(), params, String.class, new RequestListener<String>() {
            @Override
            public void success(String s) {
                hideDialog();
                showToast(0, getRstr(R.string.operate_success));
                //刷新订单数量、列表、详情
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.ORDER_COUNT));
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(7, Constants.ORDER_LIST_UPDATE));
                getOrderInfo();
            }

            @Override
            public void onError(String msg) {
                hideDialog();
                showToast(1, msg);
            }
        });
    }

    /**
     * 订单完成
     */
    private void postOrderFinish() {
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("sale_list_unique", saleListUnique);
        params.put("sale_list_cashier", getStaffUnique());
        RXHttpUtil.requestByFormPostAsResponse(this, ZURL.getOrderConfirm(), params, String.class, new RequestListener<String>() {
            @Override
            public void success(String s) {
                hideDialog();
                showToast(0, getRstr(R.string.operate_success));
                //刷新订单数量、列表、详情
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.ORDER_COUNT));
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(6, Constants.ORDER_LIST_UPDATE));
                getOrderInfo();
            }

            @Override
            public void onError(String msg) {
                hideDialog();
                showToast(1, msg);
            }
        });
    }

    /**
     * 取消配送
     */
    public void postOrderDeliveryCancel() {
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("orderNum", saleListUnique);
        RXHttpUtil.requestByFormPostAsResponse(this, ZURL.getOrderDeliveryCancel(), params, String.class, new RequestListener<String>() {
            @Override
            public void success(String s) {
                hideDialog();
                showToast(0, getRstr(R.string.operate_success));
                //刷新订单数量、列表、详情
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.ORDER_COUNT));
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(2, Constants.ORDER_LIST_UPDATE));
                getOrderInfo();
            }

            @Override
            public void onError(String msg) {
                hideDialog();
                showToast(1, msg);
            }
        });
    }

    /**
     * 订单取消
     */
    private void postOrderCancel() {
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("sale_list_unique", saleListUnique);
        RXHttpUtil.requestByFormPostAsResponse(this, ZURL.getOrderCancel(), params, String.class, new RequestListener<String>() {
            @Override
            public void success(String s) {
                hideDialog();
                showToast(0, getRstr(R.string.operate_success));
                //刷新订单数量、列表、详情
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.ORDER_COUNT));
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(5, Constants.ORDER_LIST_UPDATE));
                getOrderInfo();
            }

            @Override
            public void onError(String msg) {
                hideDialog();
                showToast(1, msg);
            }
        });
    }

}
