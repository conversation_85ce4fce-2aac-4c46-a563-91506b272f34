package com.yxl.cashier_retail.ui.fragment;

import android.view.View;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmSupplierGoodsBinding;
import com.yxl.cashier_retail.ui.adapter.SupplierGoodsAdapter;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.GoodsListData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:供货商管理-详情-所供商品
 * Created by jingang on 2024/6/8
 */
public class SupplierGoodsFragment extends BaseFragment<FmSupplierGoodsBinding> {
    private String supplierUnique;
    //所供商品
    private SupplierGoodsAdapter goodsAdapter,//已建档
            goodsAdapter1;//未建档
    private List<GoodsListData> goodsList = new ArrayList<>(),//已建档
            goodsList1 = new ArrayList<>();//未建档

    public SupplierGoodsFragment(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    @Override
    protected FmSupplierGoodsBinding getViewBinding() {
        return FmSupplierGoodsBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        setAdapter();
    }

    @Override
    protected void initData() {
        getGoodsList();
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //已建档商品
        goodsAdapter = new SupplierGoodsAdapter(getActivity());
        mBinding.rvGoods0.setAdapter(goodsAdapter);
        goodsAdapter.setType(1);
        goodsAdapter.setOnItemClickListener((view, position) -> {
            //详情
        });

        //未建档商品
        goodsAdapter1 = new SupplierGoodsAdapter(getActivity());
        mBinding.rvGoods1.setAdapter(goodsAdapter1);
        goodsAdapter1.setListener(new SupplierGoodsAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情
            }

            @Override
            public void onConfirmClick(View view, int position) {
                //建档
                postSupplierConfirm(goodsList1.get(position).getGoodsId(), position);
            }
        });

        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getGoodsList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getGoodsList();
            }
        });
    }


    /**
     * 所供商品(已建档)
     */
    private void getGoodsList() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("supplierUnique", supplierUnique);
        map.put("recordStatus", 1);//0.未建档 1.已建档
        map.put("pageIndex", page);
        map.put("pageSize", Constants.limit);
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponseList(this,
                ZURL.getSupplierInfoGoods(),
                map,
                GoodsListData.class,
                new RequestListListener<GoodsListData>() {
                    @Override
                    public void onResult(List<GoodsListData> list) {
                        getGoodsList1();
                        if (page == 1) {
                            goodsList.clear();
                        }
                        goodsList.addAll(list);
                        if (goodsList.size() > 0) {
                            mBinding.rvGoods0.setVisibility(View.VISIBLE);
                            mBinding.linEmptyGoods0.setVisibility(View.GONE);
                            goodsAdapter.setDataList(goodsList);
                        } else {
                            mBinding.rvGoods0.setVisibility(View.GONE);
                            mBinding.linEmptyGoods0.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        getGoodsList1();
                        if (goodsList.size() > 0) {
                            mBinding.rvGoods0.setVisibility(View.VISIBLE);
                            mBinding.linEmptyGoods0.setVisibility(View.GONE);
                        } else {
                            mBinding.rvGoods0.setVisibility(View.GONE);
                            mBinding.linEmptyGoods0.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 所供商品(未建档)
     */
    private void getGoodsList1() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("supplierUnique", supplierUnique);
        map.put("recordStatus", 0);//0.未建档 1.已建档
        map.put("pageIndex", page);
        map.put("pageSize", Constants.limit);
        RXHttpUtil.requestByBodyPostAsResponseList(this,
                ZURL.getSupplierInfoGoods(),
                map,
                GoodsListData.class,
                new RequestListListener<GoodsListData>() {
                    @Override
                    public void onResult(List<GoodsListData> list) {
                        hideDialog();
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                        if (page == 1) {
                            goodsList1.clear();
                        }
                        goodsList1.addAll(list);
                        if (goodsList1.size() > 0) {
                            mBinding.rvGoods1.setVisibility(View.VISIBLE);
                            mBinding.linEmptyGoods1.setVisibility(View.GONE);
                            goodsAdapter1.setDataList(goodsList1);
                        } else {
                            mBinding.rvGoods1.setVisibility(View.GONE);
                            mBinding.linEmptyGoods1.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                        if (goodsList1.size() > 0) {
                            mBinding.rvGoods1.setVisibility(View.VISIBLE);
                            mBinding.linEmptyGoods1.setVisibility(View.GONE);
                        } else {
                            mBinding.rvGoods1.setVisibility(View.GONE);
                            mBinding.linEmptyGoods1.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 供货商绑定（）确认通过
     *
     * @param id
     */
    private void postSupplierConfirm(int id, int position) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("createId", getStaffUnique());
        map.put("createBy", getStaffName());
        map.put("id", id);
        RXHttpUtil.requestByBodyPostAsResponse(getActivity(),
                ZURL.getSupplierBind(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        goodsList1.remove(position);
                        goodsAdapter1.remove(position);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.SUPPLIER_LIST));
                        if (goodsList1.size() > 0) {
                            mBinding.rvGoods1.setVisibility(View.VISIBLE);
                            mBinding.linEmptyGoods1.setVisibility(View.GONE);
                        } else {
                            mBinding.rvGoods1.setVisibility(View.GONE);
                            mBinding.linEmptyGoods1.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }
}
