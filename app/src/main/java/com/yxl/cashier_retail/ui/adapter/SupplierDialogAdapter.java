package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.SupplierData;

/**
 * Describe:供货商列表-弹窗选择（适配器）
 * Created by jingang on 2024/6/5
 */
public class SupplierDialogAdapter extends BaseAdapter<SupplierData> {

    public SupplierDialogAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_supplier_dialog;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName = holder.getView(R.id.tvItemName);
        ImageView ivImg = holder.getView(R.id.ivItemImg);

        //1.购销 2.自采（本地）
        if (mDataList.get(position).getPurchaseType() == 2) {
            tvName.setText(mDataList.get(position).getSupplierName() + "(" + getRstr(R.string.local) + ")");
            ivImg.setVisibility(View.GONE);
        } else {
            tvName.setText(mDataList.get(position).getSupplierName());
            ivImg.setVisibility(View.VISIBLE);
        }
//        if (mDataList.get(position).getPurchaseType() == 2) {
//            tvName.setText(mDataList.get(position).getSupplier_name() + "("+getRstr(R.string.local)+")");
//            ivImg.setVisibility(View.GONE);
//        } else {
//            tvName.setText(mDataList.get(position).getSupplier_name());
//            ivImg.setVisibility(View.VISIBLE);
//        }
    }
}
