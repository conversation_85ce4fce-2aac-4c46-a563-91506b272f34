package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.view.View;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityMallPaymentBinding;
import com.yxl.commonlibrary.AppManager;

/**
 * Describe:商城-付款
 * Created by jingang on 2024/6/4
 */
@SuppressLint("NonConstantResourceId")
public class MallPaymentActivity extends BaseActivity<ActivityMallPaymentBinding> implements View.OnClickListener {

    @Override
    protected ActivityMallPaymentBinding getViewBinding() {
        return ActivityMallPaymentBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivBack.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.tvOrderInfo.setOnClickListener(this);
    }

    @Override
    protected void initData() {

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.tvOrderInfo:
                //查看订单详情
                break;
        }
    }
}
