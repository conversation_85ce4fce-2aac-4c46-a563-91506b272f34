package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogCartEditBinding;
import com.yxl.cashier_retail.ui.adapter.DiscountAdapter;
import com.yxl.cashier_retail.ui.bean.DiscountData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.view.NumberKeyBoardView;
import com.yxl.commonlibrary.utils.DensityUtils;

import org.litepal.LitePal;

import java.util.ArrayList;
import java.util.List;

/**
 * Describe:dialog（单个商品改价、折扣）
 * Created by jingang on 2024/12/30
 */
@SuppressLint("NonConstantResourceId")
public class CartEditDialog extends BaseDialog<DialogCartEditBinding> implements View.OnClickListener {
    private static int type;//0.单价 1.数量 2.总价
    private boolean isDiscount;//当前焦点是否为折扣
    private static double total,//合计
            money;//优惠后金额
    private static int chengType;//称重类型 0.标品 1.称重

    private List<DiscountData> discountList = new ArrayList<>();
    private DiscountAdapter discountAdapter;

    public static void showDialog(Activity activity, int type, double total, int chengType, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        CartEditDialog.listener = listener;
        CartEditDialog.type = type;
        CartEditDialog.total = total;
        CartEditDialog.chengType = chengType;
        CartEditDialog dialog = new CartEditDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.dip2px(activity, 330), ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public CartEditDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(false);
        setUI();
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.linDialogPrice.setOnClickListener(this);
        mBinding.linDialogCustom.setOnClickListener(this);
        mBinding.numberKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var) {
                setTextBg();
                //清空折扣
                for (int i = 0; i < discountList.size(); i++) {
                    if (discountList.get(i).isSelect()) {
                        discountList.get(i).setSelect(false);
                    }
                }
                if (discountAdapter != null) {
                    discountAdapter.setDataList(discountList);
                }
                if (isDiscount) {
                    mBinding.tvDialogCustom.setText(var);
                    if (TextUtils.isEmpty(var)) {
                        mBinding.tvDialogCustom.setVisibility(View.GONE);
                        mBinding.tvDialogCustomHint.setVisibility(View.VISIBLE);
                        money = total;
                    } else {
                        mBinding.tvDialogCustom.setVisibility(View.VISIBLE);
                        mBinding.tvDialogCustomHint.setVisibility(View.GONE);
                        double discount = Double.parseDouble(var);
                        if (discount > 0) {
                            money = total * discount / 10;
                        } else {
                            money = total;
                        }
                    }
                    mBinding.tvDialogPrice.setVisibility(View.VISIBLE);
                    mBinding.tvDialogPriceHint.setVisibility(View.GONE);
                    mBinding.tvDialogPrice.setText(DFUtils.getNum4(money));
                } else {
                    mBinding.tvDialogPrice.setText(var);
                    if (TextUtils.isEmpty(var)) {
                        mBinding.tvDialogPrice.setVisibility(View.GONE);
                        mBinding.tvDialogPriceHint.setVisibility(View.VISIBLE);
                        money = 0;
                    } else {
                        mBinding.tvDialogPrice.setVisibility(View.VISIBLE);
                        mBinding.tvDialogPriceHint.setVisibility(View.GONE);
                        money = Double.parseDouble(var);
                    }
                    mBinding.tvDialogCustom.setText("");
                    mBinding.tvDialogCustom.setVisibility(View.GONE);
                    mBinding.tvDialogCustomHint.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onConfirm() {
                if (listener != null) {
                    listener.onValueClick(money);
                    dismiss();
                }
            }
        });
    }

    @Override
    protected DialogCartEditBinding getViewBinding() {
        return DialogCartEditBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                if (listener != null) {
                    listener.onCancelClick();
                    dismiss();
                }
                break;
            case R.id.linDialogPrice:
                //总价
                if (isDiscount) {
                    isDiscount = false;
                    mBinding.linDialogPrice.setBackgroundResource(R.drawable.shape_green_kuang_5);
                    mBinding.ivDialogCursor.setVisibility(View.VISIBLE);
                    mBinding.linDialogCustom.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursorCustom.setVisibility(View.GONE);
                    mBinding.numberKeyBoardView.setResultStr("");
                }
                break;
            case R.id.linDialogCustom:
                //自定义
                if (!isDiscount) {
                    isDiscount = true;
                    mBinding.linDialogPrice.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursor.setVisibility(View.GONE);
                    mBinding.linDialogCustom.setBackgroundResource(R.drawable.shape_green_kuang_5);
                    mBinding.ivDialogCursorCustom.setVisibility(View.VISIBLE);
                    mBinding.numberKeyBoardView.setResultStr("");
                }
                break;
        }
    }

    /**
     * 更新UI
     */
    private void setUI() {
        ((AnimationDrawable) mBinding.ivDialogCursor.getDrawable()).start();
        ((AnimationDrawable) mBinding.ivDialogCursorCustom.getDrawable()).start();
        money = total;
        switch (type) {
            case 1:
                mBinding.tvDialogTitle.setText(getRstr(R.string.modify_count));
                mBinding.tvDialogPriceHint.setHint(getRstr(R.string.input_count));
                mBinding.tvDialogPrice.setText(DFUtils.getNum4(total));
                mBinding.linDialogDiscount.setVisibility(View.GONE);
                mBinding.numberKeyBoardView.setDrop(chengType == 1);
                break;
            case 2:
                mBinding.tvDialogTitle.setText(getRstr(R.string.modify_subtotal));
                mBinding.tvDialogPriceHint.setHint(getRstr(R.string.input_subtotal));
                mBinding.tvDialogPrice.setText(DFUtils.getNum2(total));
                mBinding.linDialogDiscount.setVisibility(View.VISIBLE);
                mBinding.numberKeyBoardView.setDrop(true);
                break;
            default:
                mBinding.tvDialogTitle.setText(getRstr(R.string.modify_price));
                mBinding.tvDialogPriceHint.setHint(getRstr(R.string.input_price));
                mBinding.tvDialogPrice.setText(DFUtils.getNum2(total));
                mBinding.linDialogDiscount.setVisibility(View.VISIBLE);
                mBinding.numberKeyBoardView.setDrop(true);
                break;
        }

        discountList.clear();
        discountList.addAll(LitePal.findAll(DiscountData.class));
        discountAdapter = new DiscountAdapter(getContext(), 0);
        mBinding.rvDialogDiscount.setAdapter(discountAdapter);
        discountAdapter.setDataList(discountList);
        discountAdapter.setOnItemClickListener((view, position) -> {
            if (discountList.get(position).isSelect()) {
                discountList.get(position).setSelect(false);
            } else {
                for (int i = 0; i < discountList.size(); i++) {
                    if (discountList.get(i).isSelect()) {
                        discountList.get(i).setSelect(false);
                    }
                }
                discountList.get(position).setSelect(true);
            }
            discountAdapter.setDataList(discountList);
            if (discountList.get(position).isSelect()) {
                money = total * discountList.get(position).getDiscount() / 10;
            } else {
                money = total;
            }
            mBinding.tvDialogPrice.setText(DFUtils.getNum4(money));
            setTextBg();
        });
    }

    /**
     * 设置金额 无背景、黑色字体、显示光标
     */
    private void setTextBg() {
        mBinding.tvDialogPrice.setBackgroundResource(0);
        mBinding.tvDialogPrice.setTextColor(getContext().getResources().getColor(R.color.black));
    }

    private static MyListener listener;

    public interface MyListener {
        void onValueClick(double value);

        void onCancelClick();
    }
}
