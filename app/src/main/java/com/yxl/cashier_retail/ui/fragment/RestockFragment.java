package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Typeface;
import android.view.View;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmRestockBinding;
import com.yxl.cashier_retail.ui.activity.RestockEditActivity;
import com.yxl.cashier_retail.ui.activity.RestockPreviewActivity;
import com.yxl.cashier_retail.ui.adapter.RestockAdapter;
import com.yxl.cashier_retail.ui.adapter.RestockChildAdapter;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.RestockData;
import com.yxl.cashier_retail.ui.dialog.RestockAddDialog;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.base.BaseData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:入库-自采补货
 * Created by jingang on 2024/6/4
 */
@SuppressLint("NonConstantResourceId")
public class RestockFragment extends BaseFragment<FmRestockBinding> implements View.OnClickListener {
    private int status = 1,//1.待生成 2.已生成 3.已取消
            id,//补货计划id
            position;//补货计划列表下标
    private RestockData data;

    //补货计划列表
    private RestockAdapter mAdapter;
    private List<RestockData> dataList = new ArrayList<>();

    //商品列表
    private RestockChildAdapter goodsAdapter;
    private List<RestockData.GoodsListBean> goodsList = new ArrayList<>();

    @Override
    protected FmRestockBinding getViewBinding() {
        return FmRestockBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.tvType0.setOnClickListener(this);
        mBinding.tvType1.setOnClickListener(this);
        mBinding.tvType2.setOnClickListener(this);
        mBinding.tvAdd.setOnClickListener(this);
        mBinding.tvDel.setOnClickListener(this);
        mBinding.tvCancel.setOnClickListener(this);
        mBinding.tvInfo.setOnClickListener(this);
        mBinding.tvPreview.setOnClickListener(this);
        mBinding.tvAgain.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected void initData() {
        getRestockList();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvType0:
                //待生成
                if (status != 1) {
                    status = 1;
                    mBinding.tvType0.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvType0.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvType0.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.tvType1.setBackgroundResource(0);
                    mBinding.tvType1.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType1.setTypeface(Typeface.DEFAULT);
                    mBinding.tvType2.setBackgroundResource(0);
                    mBinding.tvType2.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType2.setTypeface(Typeface.DEFAULT);
                    page = 1;
                    getRestockList();
                }
                break;
            case R.id.tvType1:
                //已生成
                if (status != 2) {
                    status = 2;
                    mBinding.tvType1.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvType1.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvType1.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.tvType0.setBackgroundResource(0);
                    mBinding.tvType0.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType0.setTypeface(Typeface.DEFAULT);
                    mBinding.tvType2.setBackgroundResource(0);
                    mBinding.tvType2.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType2.setTypeface(Typeface.DEFAULT);
                    page = 1;
                    getRestockList();
                }
                break;
            case R.id.tvType2:
                //已取消
                if (status != 3) {
                    status = 3;
                    mBinding.tvType2.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvType2.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvType2.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.tvType1.setBackgroundResource(0);
                    mBinding.tvType1.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType1.setTypeface(Typeface.DEFAULT);
                    mBinding.tvType0.setBackgroundResource(0);
                    mBinding.tvType0.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType0.setTypeface(Typeface.DEFAULT);
                    page = 1;
                    getRestockList();
                }
                break;
            case R.id.tvAdd:
                //创建补货计划
                RestockAddDialog.showDialog(getActivity(), this::postRestockAdd);
                break;
            case R.id.tvDel:
                //删除
                postRestockDel();
                break;
            case R.id.tvCancel:
                //取消补货
                postRestockStatus(3);
                break;
            case R.id.tvInfo:
                //添加补货
                startActivity(new Intent(getActivity(), RestockEditActivity.class)
                        .putExtra("id", id)
                );
                break;
            case R.id.tvPreview:
                //预览
                if (dataList.get(position).getGoodsList().size() < 1) {
                    showToast(1, getRstr(R.string.place_add_goods));
                    return;
                }
                startActivity(new Intent(getActivity(), RestockPreviewActivity.class)
                        .putExtra("id", id)
                );
                break;
            case R.id.tvAgain:
                //再次补货
                postRestockAgain();
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.RESTOCK_LIST:
                //刷新列表
                page = 1;
                getRestockList();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //补货计划列表
        mAdapter = new RestockAdapter(getActivity());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (!dataList.get(position).isSelect()) {
                for (int i = 0; i < dataList.size(); i++) {
                    if (dataList.get(i).isSelect()) {
                        dataList.get(i).setSelect(false);
                        mAdapter.notifyItemChanged(i);
                    }
                }
                dataList.get(position).setSelect(true);
                mAdapter.notifyItemChanged(position);
                this.position = position;
                id = dataList.get(position).getShopRestockplanId();
                setUI(dataList.get(position));
            }
        });
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getRestockList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getRestockList();
            }
        });

        //商品列表
        goodsAdapter = new RestockChildAdapter(getActivity());
        mBinding.rvGoods.setAdapter(goodsAdapter);
        goodsAdapter.setOnItemClickListener((view, position) -> {
            postRestockGoodsEdit(goodsList.get(position).getShopRestockplanGoodsId(), position);
        });
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(RestockData data) {
        this.data = data;
        goodsList.clear();
        goodsList.addAll(data.getGoodsList());
        goodsAdapter.setStatus(status);
        goodsAdapter.setDataList(goodsList);
        //1待生成2已生成3已取消
        switch (data.getStatus()) {
            case 1:
                mBinding.ivDel.setVisibility(View.INVISIBLE);
                mBinding.tvDel.setVisibility(View.GONE);
                mBinding.tvCancel.setVisibility(View.VISIBLE);
                mBinding.tvInfo.setVisibility(View.VISIBLE);
                mBinding.tvPreview.setVisibility(View.VISIBLE);
                mBinding.tvAgain.setVisibility(View.GONE);
                break;
            case 2:
                mBinding.ivDel.setVisibility(View.GONE);
                mBinding.tvDel.setVisibility(View.GONE);
                mBinding.tvCancel.setVisibility(View.GONE);
                mBinding.tvInfo.setVisibility(View.GONE);
                mBinding.tvPreview.setVisibility(View.GONE);
                mBinding.tvAgain.setVisibility(View.VISIBLE);
                break;
            default:
                mBinding.ivDel.setVisibility(View.GONE);
                mBinding.tvDel.setVisibility(View.VISIBLE);
                mBinding.tvCancel.setVisibility(View.GONE);
                mBinding.tvInfo.setVisibility(View.GONE);
                mBinding.tvPreview.setVisibility(View.GONE);
                mBinding.tvAgain.setVisibility(View.VISIBLE);
                break;
        }
    }

    /**
     * 补货计划列表
     */
    private void getRestockList() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("planStatus", status);
        params.put("pageIndex", page);
        params.put("pageSize", Constants.limit);
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponseList(getActivity(),
                ZURL.getRestockPlanList(),
                params,
                RestockData.class,
                new RequestListListener<RestockData>() {
                    @Override
                    public void onResult(List<RestockData> list) {
                        hideDialog();
                        if (page == 1) {
                            mBinding.smartRefreshLayout.finishRefresh();
                            dataList.clear();
                            dataList.addAll(list);
                            if (dataList.size() > 0) {
                                position = 0;
                                id = dataList.get(0).getShopRestockplanId();
                                dataList.get(0).setSelect(true);
                                setUI(dataList.get(0));
                            }
                        } else {
                            mBinding.smartRefreshLayout.finishLoadMore();
                            dataList.addAll(list);
                        }
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }

                    }
                });

    }

    /**
     * 补货计划创建
     */
    private void postRestockAdd(String name) {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("loginUser", getStaffUnique());
        params.put("restockPlanName", name);
        showDialog();
        RXHttpUtil.requestByBodyPostAsOriginalResponse(getActivity(),
                ZURL.getRestockAdd(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        if (data.getStatus() == Constants.SUCCESS_CODE && data.getData() != null) {
                            showToast(0, data.getMsg());
                            if (status == 1) {
                                page = 1;
                                getRestockList();
                            }
                            int id;
                            if (data.getData() != null) {
                                id = Integer.parseInt(DFUtils.getNum4((Double) data.getData()));
                            } else {
                                id = 0;
                            }
                            startActivity(new Intent(getActivity(), RestockEditActivity.class)
                                    .putExtra("id", id)
                            );
                        } else {
                            showToast(1, data.getMsg());
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 补货计划删除
     */
    private void postRestockDel() {
        if (id == 0) {
            showToast(1, getRstr(R.string.restock_id_wrong));
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("shopRestockplanId", id);
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponse(getActivity(),
                ZURL.getRestockDel(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        dataList.remove(position);
                        mAdapter.remove(position);
                        if (dataList.size() > 0) {
                            dataList.get(0).setSelect(true);
                            mAdapter.notifyItemChanged(0);
                            setUI(dataList.get(0));
                        } else {
                            page = 1;
                            getRestockList();
                        }
                    }
                });
    }

    /**
     * 修改补货计划状态
     *
     * @param status 1.未生成 2.已生成 3.已取消
     */
    private void postRestockStatus(int status) {
        if (id == 0) {
            showToast(1, getRstr(R.string.restock_id_wrong));
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("restockPlanId", id);
        params.put("planStatus", status);
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponse(getActivity(),
                ZURL.getRestockUpdate(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        dataList.remove(position);
                        mAdapter.remove(position);
                        if (dataList.size() > 0) {
                            dataList.get(0).setSelect(true);
                            mAdapter.notifyItemChanged(0);
                            setUI(dataList.get(0));
                        } else {
                            page = 1;
                            getRestockList();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 再次补货
     */
    private void postRestockAgain() {
        if (id == 0) {
            showToast(1, getRstr(R.string.restock_id_wrong));
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("shopRestockplanId", id);
        RXHttpUtil.requestByBodyPostAsOriginalResponse(getActivity(),
                ZURL.getRestockAgain(),
                map,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        if (data.getStatus() == Constants.SUCCESS_CODE && data.getData() != null) {
                            showToast(0, data.getMsg());
                            page = 1;
                            getRestockList();
                            int id;
                            if (data.getData() != null) {
                                id = Integer.parseInt(DFUtils.getNum4((Double) data.getData()));
                            } else {
                                id = 0;
                            }
                            startActivity(new Intent(getActivity(), RestockEditActivity.class)
                                    .putExtra("id", id)
                            );
                        } else {
                            showToast(1, data.getMsg());
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 补货计划商品编辑
     *
     * @param goodsId
     * @param position
     */
    private void postRestockGoodsEdit(int goodsId, int position) {
        if (id == 0) {
            showToast(1, getRstr(R.string.restock_id_wrong));
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("shopRestockplanId", id);
        map.put("shopRestockplanGoodsId", goodsId);
        map.put("delFlag", 2);
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getRestockGoodsEdit(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        goodsList.remove(position);
                        goodsAdapter.remove(position);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });

    }

}
