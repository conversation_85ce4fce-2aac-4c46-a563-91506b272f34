package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmSupplierPaymentBinding;
import com.yxl.cashier_retail.ui.adapter.SupplierPaymentAdapter;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.SupplierPaymentData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:供货商管理-详情-结款记录
 * Created by jingang on 2024/6/8
 */
@SuppressLint("NonConstantResourceId")
public class SupplierPaymentFragment extends BaseFragment<FmSupplierPaymentBinding> {
    private String supplierUnique;//供货商编号

    private SupplierPaymentAdapter mAdapter;
    private List<SupplierPaymentData> dataList = new ArrayList<>();

    public SupplierPaymentFragment(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    @Override
    protected FmSupplierPaymentBinding getViewBinding() {
        return FmSupplierPaymentBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        setAdapter();
    }

    @Override
    protected void initData() {
        getPaymentList();
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new SupplierPaymentAdapter(getActivity());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            EventBusManager.getInstance().getGlobaEventBus().post(new EventData(String.valueOf(dataList.get(position).getPaymentId()), Constants.SETTLEMENT_INFO));
        });
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getPaymentList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getPaymentList();
            }
        });
    }

    /**
     * 付款记录
     */
    private void getPaymentList() {
        if (TextUtils.isEmpty(supplierUnique)) {
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("supplierUnique", supplierUnique);
        map.put("pageIndex", page);
        map.put("pageSize", Constants.limit);
        RXHttpUtil.requestByBodyPostAsResponseList(getActivity(),
                ZURL.getSupplierInfoPayment(),
                map,
                SupplierPaymentData.class,
                new RequestListListener<SupplierPaymentData>() {
                    @Override
                    public void onResult(List<SupplierPaymentData> list) {
                        hideDialog();
                        if (page == 1) {
                            mBinding.smartRefreshLayout.finishRefresh();
                            dataList.clear();
                        } else {
                            mBinding.smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }
}
