package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogDateStartEndBinding;
import com.yxl.cashier_retail.view.pickerview.entiy.DateEntity;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Describe:日期(开始结束)选择弹窗
 * Created by jingang on 2024/5/21
 */
@SuppressLint({"NonConstantResourceId", "SimpleDateFormat"})
public class DateStartEndDialog extends BaseDialog<DialogDateStartEndBinding> implements View.OnClickListener {
    private static View viewIcon;
    private static int type;//0.开始 1.结束
    private static String startDate, endDate;

    private final Animation openAnim, closeAnim;

    @Override
    protected DialogDateStartEndBinding getViewBinding() {
        return DialogDateStartEndBinding.inflate(getLayoutInflater());
    }

    public static void showDialog(Context mContext, View viewIcon, String startTime, String endTime, int type, MyListener listener) {
        DateStartEndDialog.viewIcon = viewIcon;
        DateStartEndDialog.startDate = startTime;
        DateStartEndDialog.endDate = endTime;
        DateStartEndDialog.type = type;
        DateStartEndDialog dialog = new DateStartEndDialog(mContext);
        dialog.setRange(startTime, endTime);
        dialog.setListener(listener);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public DateStartEndDialog(Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);

        openAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_1);
        closeAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_2);
        openAnim.setFillAfter(true);
        closeAnim.setFillAfter(true);

        mBinding.tvDialogCancel.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        mBinding.linDialogDateStart.setOnClickListener(this);
        mBinding.linDialogDateEnd.setOnClickListener(this);

        mBinding.tvDialogDateStart.setText(startDate);
        mBinding.tvDialogDateEnd.setText(endDate);
        if (type == 0) {
            mBinding.tvDialogDateStart.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
            mBinding.vDialogDateStart.setBackgroundColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
            mBinding.tvDialogDateEnd.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
            mBinding.vDialogDateEnd.setBackgroundColor(getContext().getResources().getColor(R.color.color_line));
        } else {
            mBinding.tvDialogDateStart.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
            mBinding.vDialogDateStart.setBackgroundColor(getContext().getResources().getColor(R.color.color_line));
            mBinding.tvDialogDateEnd.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
            mBinding.vDialogDateEnd.setBackgroundColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
        }
        mBinding.wheelLayout.setDefaultValue(DateEntity.today()); //当前日期
        mBinding.wheelLayout.setSelectedTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));//选中字体颜色
        mBinding.wheelLayout.setSelectedTextSize(14 * getContext().getResources().getDisplayMetrics().scaledDensity);//选中字体大小
        mBinding.wheelLayout.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));//字体颜色
        mBinding.wheelLayout.setTextSize(12 * getContext().getResources().getDisplayMetrics().scaledDensity);//字体大小
        mBinding.wheelLayout.setIndicatorColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.transparent)); //横线颜色
        mBinding.wheelLayout.setOnDateSelectedListener((year, month, day) -> {
            Log.e(tag, "year = " + year + "-" + month + "-" + day);
            String month_str, day_str;
            if (month < 10) {
                month_str = "0" + month;
            } else {
                month_str = String.valueOf(month);
            }
            if (day < 10) {
                day_str = "0" + day;
            } else {
                day_str = String.valueOf(day);
            }
            if (type == 0) {
                startDate = year + "-" + month_str + "-" + day_str;
                mBinding.tvDialogDateStart.setText(startDate);
            } else {
                endDate = year + "-" + month_str + "-" + day_str;
                mBinding.tvDialogDateEnd.setText(endDate);
            }
        });
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvDialogCancel:
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //确认
                if (TextUtils.isEmpty(startDate)) {
                    showToast(1, getRstr(R.string.select_date_start));
                    return;
                }
                if (TextUtils.isEmpty(endDate)) {
                    showToast(1, getRstr(R.string.select_date_end));
                    return;
                }
                if (listener != null) {
                    listener.onClick(startDate, endDate);
                }
                dismiss();
                break;
            case R.id.linDialogDateStart:
                //选择开始时间
                if (type != 0) {
                    type = 0;
                    mBinding.tvDialogDateStart.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    mBinding.vDialogDateStart.setBackgroundColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    mBinding.tvDialogDateEnd.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
                    mBinding.vDialogDateEnd.setBackgroundColor(getContext().getResources().getColor(R.color.color_line));
                    setRange(startDate, endDate);
                }
                break;
            case R.id.linDialogDateEnd:
                //选择结束时间
                if (type != 1) {
                    type = 1;
                    mBinding.tvDialogDateStart.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
                    mBinding.vDialogDateStart.setBackgroundColor(getContext().getResources().getColor(R.color.color_line));
                    mBinding.tvDialogDateEnd.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    mBinding.vDialogDateEnd.setBackgroundColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    setRange(startDate, "");
                }
                break;
        }
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
        if (viewIcon != null) {
            viewIcon.startAnimation(openAnim);
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
        if (viewIcon != null) {
            viewIcon.startAnimation(closeAnim);
        }
    }

    /**
     * 设置日期时间范围
     * <p>
     * 0.查询：开始 1.查询：结束 2.活动：开始 3.活动：结束
     *
     * @param startTime
     * @param endTime
     */
    private void setRange(String startTime, String endTime) {
        DateEntity entityStart, entityEnd, entityNow;

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date dateStart, dateEnd, dateNow;

        /*****************开始日期时间*****************/
        try {
            dateStart = dateFormat.parse(startTime);
            if (dateStart != null) {
                entityStart = new DateEntity();
                if (type == 1) {
                    entityStart.setYear(dateStart.getYear() + 1900);
                } else {
                    entityStart.setYear(dateStart.getYear() + 1890);
                }
                if (dateStart.getMonth() == 0) {
                    entityStart.setMonth(1);
                } else {
                    entityStart.setMonth(dateStart.getMonth() + 1);
                }
                entityStart.setDay(dateStart.getDate());
            } else {
                entityStart = DateEntity.yearOnFuture(-10);
            }
        } catch (Exception e) {
            e.printStackTrace();
            entityStart = DateEntity.yearOnFuture(-10);
        }

        /*****************结束日期时间*****************/
        try {
            dateEnd = dateFormat.parse(endTime);
            if (dateEnd != null) {
                entityEnd = new DateEntity();
                entityEnd.setYear(dateEnd.getYear() + 1900);
                if (dateEnd.getMonth() == 0) {
                    entityEnd.setMonth(1);
                } else {
                    entityEnd.setMonth(dateEnd.getMonth() + 1);
                }
                entityEnd.setDay(dateEnd.getDate());
            } else {
                entityEnd = DateEntity.today();
            }
        } catch (Exception e) {
            e.printStackTrace();
            entityEnd = DateEntity.today();
        }

        /*****************现在日期时间*****************/
        try {
            if (type == 0) {
                dateNow = dateFormat.parse(startTime);
            } else {
                dateNow = dateFormat.parse(endTime);
            }
            if (dateNow != null) {
                entityNow = new DateEntity();
                entityNow.setYear(dateNow.getYear() + 1900);
                if (dateNow.getMonth() == 0) {
                    entityNow.setMonth(1);
                } else {
                    entityNow.setMonth(dateNow.getMonth() + 1);
                }
                entityNow.setDay(dateNow.getDate());
            } else {
                entityNow = DateEntity.today();
            }
        } catch (Exception e) {
            e.printStackTrace();
            entityNow = DateEntity.today();
        }
        mBinding.wheelLayout.setRange(entityStart, entityEnd, entityNow); //日期范围
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onClick(String startDate, String endDate);
    }
}
