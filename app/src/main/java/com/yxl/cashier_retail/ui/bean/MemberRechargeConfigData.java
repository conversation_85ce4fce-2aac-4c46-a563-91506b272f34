package com.yxl.cashier_retail.ui.bean;

/**
 * Describe: 会员充值配置（实体类）
 * Created by jingang on 2025/6/6
 */
public class MemberRechargeConfigData {
    /**
     * start_time : 2025-05-10 00:00:00
     * shop_unique : 1536215939565
     * flag : true
     * create_time : 2025-05-10 15:00:41
     * recharge_money : 1.0
     * end_time : 2028-05-10 00:00:00
     * id : 78
     * give_money : 0.5
     */

    private String start_time;//开始时间
    private String shop_unique;
    private boolean flag;//是否启用（false 否 true是）
    private String create_time;//创建时间
    private double recharge_money;//充值金额
    private String end_time;//结束时间
    private int id;
    private double give_money;//赠送金额

    public String getStart_time() {
        return start_time;
    }

    public void setStart_time(String start_time) {
        this.start_time = start_time;
    }

    public String getShop_unique() {
        return shop_unique;
    }

    public void setShop_unique(String shop_unique) {
        this.shop_unique = shop_unique;
    }

    public boolean isFlag() {
        return flag;
    }

    public void setFlag(boolean flag) {
        this.flag = flag;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public double getRecharge_money() {
        return recharge_money;
    }

    public void setRecharge_money(double recharge_money) {
        this.recharge_money = recharge_money;
    }

    public String getEnd_time() {
        return end_time;
    }

    public void setEnd_time(String end_time) {
        this.end_time = end_time;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public double getGive_money() {
        return give_money;
    }

    public void setGive_money(double give_money) {
        this.give_money = give_money;
    }
}
