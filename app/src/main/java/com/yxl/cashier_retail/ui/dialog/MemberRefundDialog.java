package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogMemberRefundBinding;
import com.yxl.cashier_retail.view.NumberKeyBoardView;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Describe:dialog（会员退费）
 * Created by jingang on 2024/7/3
 */
@SuppressLint("NonConstantResourceId")
public class MemberRefundDialog extends BaseDialog<DialogMemberRefundBinding> implements View.OnClickListener {
    private int type = 1,//1.现金 2.微信 3.支付宝 4.存零
            currentIndex = 1;
    private static String unique;//会员编号
    private static double balance;//会员余额
    private double money;//退费金额

    public static void showDialog(Activity activity, String unique, double balance, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        MemberRefundDialog.listener = listener;
        MemberRefundDialog.unique = unique;
        MemberRefundDialog.balance = balance;
        MemberRefundDialog dialog = new MemberRefundDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public MemberRefundDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        ((AnimationDrawable) mBinding.ivDialogCursor.getDrawable()).start();
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.linDialogCash.setOnClickListener(this);
        mBinding.linDialogWechat.setOnClickListener(this);
        mBinding.linDialogAlipay.setOnClickListener(this);
        mBinding.numberKeyBoardView.setMaxLength(Constants.MAX_MONEY_LENGTH);
        mBinding.numberKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var) {
                mBinding.tvDialogMoney.setText(var);
                if (TextUtils.isEmpty(var)) {
                    mBinding.tvDialogMoney.setVisibility(View.GONE);
                    mBinding.tvDialogHints.setVisibility(View.VISIBLE);
                    money = 0;
                } else {
                    mBinding.tvDialogMoney.setVisibility(View.VISIBLE);
                    mBinding.tvDialogHints.setVisibility(View.GONE);
                    money = Double.parseDouble(var);
                }
            }

            @Override
            public void onConfirm() {
                if (money <= 0) {
                    showToast(1, getRstr(R.string.input_refund_fee_money));
                    return;
                }
                if (money > balance) {
                    showToast(1, getRstr(R.string.member_refund_tips1));
                    return;
                }
                postRefund();
            }
        });
    }

    @Override
    protected DialogMemberRefundBinding getViewBinding() {
        return DialogMemberRefundBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.linDialogCash:
                //现金
                type = 1;
                fragmentControl();
                break;
            case R.id.linDialogWechat:
                //微信
                type = 2;
                fragmentControl();
                break;
            case R.id.linDialogAlipay:
                //支付宝
                type = 3;
                fragmentControl();
                break;
        }
    }

    /**
     * 控制fragment的变化
     */
    public void fragmentControl() {
        if (currentIndex != type) {
            removeBottomColor();
            setBottomColor();
            currentIndex = type;
        }
    }

    /**
     * 设置底部栏按钮变色
     */
    private void setBottomColor() {
        switch (type) {
            case 1:
                mBinding.linDialogCash.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivDialogCash.setImageResource(R.mipmap.ic_recharge11);
                mBinding.tvDialogCash.setTextColor(getContext().getResources().getColor(R.color.white));
                mBinding.tvDialogCashHint.setTextColor(getContext().getResources().getColor(R.color.white));
                break;
            case 2:
                mBinding.linDialogWechat.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivDialogWechat.setImageResource(R.mipmap.ic_recharge21);
                mBinding.tvDialogWechat.setTextColor(getContext().getResources().getColor(R.color.white));
                mBinding.tvDialogWechatHint.setTextColor(getContext().getResources().getColor(R.color.white));
                break;
            case 3:
                mBinding.linDialogAlipay.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivDialogAlipay.setImageResource(R.mipmap.ic_recharge31);
                mBinding.tvDialogAlipay.setTextColor(getContext().getResources().getColor(R.color.white));
                mBinding.tvDialogAlipayHint.setTextColor(getContext().getResources().getColor(R.color.white));
                break;
            default:
                break;
        }
    }

    /**
     * 清除底部栏颜色
     */
    private void removeBottomColor() {
        switch (currentIndex) {
            case 1:
                mBinding.linDialogCash.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCash.setImageResource(R.mipmap.ic_recharge10);
                mBinding.tvDialogCash.setTextColor(getContext().getResources().getColor(R.color.black));
                mBinding.tvDialogCashHint.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
                break;
            case 2:
                mBinding.linDialogWechat.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogWechat.setImageResource(R.mipmap.ic_recharge20);
                mBinding.tvDialogWechat.setTextColor(getContext().getResources().getColor(R.color.black));
                mBinding.tvDialogWechatHint.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
                break;
            case 3:
                mBinding.linDialogAlipay.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogAlipay.setImageResource(R.mipmap.ic_recharge30);
                mBinding.tvDialogAlipay.setTextColor(getContext().getResources().getColor(R.color.black));
                mBinding.tvDialogAlipayHint.setTextColor(getContext().getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
                break;
            default:
                break;
        }
    }

    /**
     * 退款
     */
    private void postRefund() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("cusUnique", unique);
        params.put("money", money);
        params.put("type", 2);//1.充值 2.提现
        params.put("saleListCashier", getStaffUnique());
        params.put("recharge_method", type);//充值方式 1.现金 2.微信 3.支付宝 4.存零
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getMemberRecharge(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, getRstr(R.string.refund_success));
                        if (listener != null) {
                            listener.onConfirm(money);
                            dismiss();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(double money);
    }
}
