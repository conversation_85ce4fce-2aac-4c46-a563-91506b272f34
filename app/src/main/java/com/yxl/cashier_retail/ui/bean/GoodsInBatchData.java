package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;

/**
 * Describe:商品批量入库（实体类）
 * Created by jingang on 2024/9/12
 */
public class GoodsInBatchData implements Serializable {
    private String goodsId;
    private String goodsBarcode;
    private String goodsName;
    private double inPrice;//入库单价
    private double inCount;//入库数量
    private double inTotal;//入库总价
    private double salePrice;//零售单价
    private double onlinePrice;//网购单价
    private double memberPrice;//会员单价

    public GoodsInBatchData() {
    }

    public GoodsInBatchData(String goodsId, String goodsBarcode, String goodsName, double inPrice, double inCount, double inTotal) {
        this.goodsId = goodsId;
        this.goodsBarcode = goodsBarcode;
        this.goodsName = goodsName;
        this.inPrice = inPrice;
        this.inCount = inCount;
        this.inTotal = inTotal;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public double getInPrice() {
        return inPrice;
    }

    public void setInPrice(double inPrice) {
        this.inPrice = inPrice;
    }

    public double getInCount() {
        return inCount;
    }

    public void setInCount(double inCount) {
        this.inCount = inCount;
    }

    public double getInTotal() {
        return inTotal;
    }

    public void setInTotal(double inTotal) {
        this.inTotal = inTotal;
    }

    public double getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(double salePrice) {
        this.salePrice = salePrice;
    }

    public double getOnlinePrice() {
        return onlinePrice;
    }

    public void setOnlinePrice(double onlinePrice) {
        this.onlinePrice = onlinePrice;
    }

    public double getMemberPrice() {
        return memberPrice;
    }

    public void setMemberPrice(double memberPrice) {
        this.memberPrice = memberPrice;
    }
}
