package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;

/**
 * Describe:员工列表（实体类）
 * Created by jingang on 2024/8/23
 */
public class StaffData implements Serializable {
    /**
     * powerPrice : 2
     * powerAdd : 1
     * powerCount : 2
     * powerSupplier : 2
     * powerPur : 2
     * powerKind : 2
     * staffName : 张三
     * powerChange : 2
     * powerInPrice : 2
     * powerName : 1
     * staffId : 4
     * powerDelete : 1
     */
    private boolean select;
    private int powerPrice;//修改商品价格权限
    private int powerAdd;//添加新商品
    private int powerCount;//修改商品库存量
    private int powerSupplier;//修改供应商权限
    private int powerPur;//采购权限
    private int powerKind;//修改商品分类
    private String staffName;//员工名称
    private int powerChange;//抹零权限
    private int powerInPrice;//修改商品进价
    private int powerName;//修改商品名称
    private int staffId;//员工id
    private int powerDelete;//删除权限

    public StaffData() {
    }

    public StaffData(String staffName, int staffId) {
        this.staffName = staffName;
        this.staffId = staffId;
    }

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }

    public int getPowerPrice() {
        return powerPrice;
    }

    public void setPowerPrice(int powerPrice) {
        this.powerPrice = powerPrice;
    }

    public int getPowerAdd() {
        return powerAdd;
    }

    public void setPowerAdd(int powerAdd) {
        this.powerAdd = powerAdd;
    }

    public int getPowerCount() {
        return powerCount;
    }

    public void setPowerCount(int powerCount) {
        this.powerCount = powerCount;
    }

    public int getPowerSupplier() {
        return powerSupplier;
    }

    public void setPowerSupplier(int powerSupplier) {
        this.powerSupplier = powerSupplier;
    }

    public int getPowerPur() {
        return powerPur;
    }

    public void setPowerPur(int powerPur) {
        this.powerPur = powerPur;
    }

    public int getPowerKind() {
        return powerKind;
    }

    public void setPowerKind(int powerKind) {
        this.powerKind = powerKind;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public int getPowerChange() {
        return powerChange;
    }

    public void setPowerChange(int powerChange) {
        this.powerChange = powerChange;
    }

    public int getPowerInPrice() {
        return powerInPrice;
    }

    public void setPowerInPrice(int powerInPrice) {
        this.powerInPrice = powerInPrice;
    }

    public int getPowerName() {
        return powerName;
    }

    public void setPowerName(int powerName) {
        this.powerName = powerName;
    }

    public int getStaffId() {
        return staffId;
    }

    public void setStaffId(int staffId) {
        this.staffId = staffId;
    }

    public int getPowerDelete() {
        return powerDelete;
    }

    public void setPowerDelete(int powerDelete) {
        this.powerDelete = powerDelete;
    }
}
