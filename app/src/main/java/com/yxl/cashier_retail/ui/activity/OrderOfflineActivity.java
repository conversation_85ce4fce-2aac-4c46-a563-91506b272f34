package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.view.View;

import com.blankj.utilcode.util.NetworkUtils;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityOrderOfflineBinding;
import com.yxl.cashier_retail.ui.adapter.OrderOfflineAdapter;
import com.yxl.cashier_retail.ui.bean.OrderOfflineData;
import com.yxl.cashier_retail.ui.bean.SaleListUniqueData;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import org.litepal.LitePal;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:交接班-离线订单
 * Created by jingang on 2024/9/18
 */
@SuppressLint({"NonConstantResourceId", "MissingPermission"})
public class OrderOfflineActivity extends BaseActivity<ActivityOrderOfflineBinding> implements View.OnClickListener {
    private OrderOfflineAdapter mAdapter;
    private List<OrderOfflineData> dataList = new ArrayList<>();

    @Override
    protected ActivityOrderOfflineBinding getViewBinding() {
        return ActivityOrderOfflineBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivBack.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.tvConfirm.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected void initData() {
        getOrderList();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.tvConfirm:
                //一键提交
                if (isQuicklyClick()) {
                    return;
                }
                if (dataList.size() < 1) {
                    showToast(1, getRstr(R.string.no_order_offline));
                    return;
                }
                showDialog();
                getSaleListUnique();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new OrderOfflineAdapter(this);
        mBinding.recyclerView.setAdapter(mAdapter);
        mBinding.smartRefreshLayout.setOnRefreshListener(refreshLayout -> getOrderList());
        mBinding.smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 获取离线订单列表
     */
    private void getOrderList() {
        mBinding.smartRefreshLayout.finishRefresh();
        List<OrderOfflineData> list = LitePal.findAll(OrderOfflineData.class);
        dataList.clear();
        if (list != null) {
            dataList.addAll(list);
        }
        Collections.reverse(dataList);
        if (dataList.size() > 0) {
            mBinding.recyclerView.setVisibility(View.VISIBLE);
            mBinding.linEmpty.setVisibility(View.GONE);
            mAdapter.setDataList(dataList);
        } else {
            mBinding.recyclerView.setVisibility(View.GONE);
            mBinding.linEmpty.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 创建订单编号
     */
    private void getSaleListUnique() {
        if (!NetworkUtils.isConnected()) {
            hideDialog();
            showToast(1, getRstr(R.string.no_network_connect));
            return;
        }
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getSaleListUniqueCreate(),
                new HashMap<>(),
                SaleListUniqueData.class,
                new RequestListener<SaleListUniqueData>() {
                    @Override
                    public void success(SaleListUniqueData data) {
                        postPayment(data.getSale_list_unique());
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 普通线下订单结算
     *
     * @param saleListUnique
     */
    private void postPayment(String saleListUnique) {
        if (!NetworkUtils.isConnected()) {
            hideDialog();
            showToast(1, getRstr(R.string.no_network_connect));
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("saleListState", dataList.get(0).getSaleListState());//付款状态 3.已付款 4.赊账
        params.put("saleListUnique", saleListUnique);//订单编号(接口创建)
        params.put("goodsPurprice", dataList.get(0).getGoodsPurprice());//商品进价 “,”隔开
        params.put("saleListDetailPrice", dataList.get(0).getSaleListDetailPrice());//商品售价 “,”隔开
        params.put("goods_old_price", dataList.get(0).getGoods_old_price());//商品原价 “,”隔开
        params.put("saleListDetailCount", dataList.get(0).getSaleListDetailCount());//商品数量 “,”隔开
        params.put("goodsName", dataList.get(0).getGoodsName());//商品名称 “,”隔开
        params.put("goodsBarcode", dataList.get(0).getGoodsBarcode());//商品条码 “,”隔开
        params.put("goodsId", dataList.get(0).getGoodsId());//商品id ","隔开
        params.put("saleListTotal", dataList.get(0).getSaleListTotal());//订单总金额
        params.put("saleListCashier", getStaffUnique());//员工id
//        params.put("saleListRemarks", );//订单备注
//        params.put("machine_num", );//机器编号
        params.put("saleListActuallyReceived", dataList.get(0).getSaleListActuallyReceived());
        params.put("sale_list_payment", dataList.get(0).getSale_list_payment());//支付方式 1.现金 2.支付宝 3.微信 4.银行卡 5.储值卡 8.混合支付 9.免密支付
//        params.put("machineTime", );//失败时上传当前时间
        params.put("type", dataList.get(0).getType());//固定值2
//        params.put("wholesale_phone", );//批发客户的手机号
        params.put("saleListPayDetail", dataList.get(0).getSaleListPayDetail());//支付详情
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getPayment(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        OrderOfflineData data = dataList.get(0);
                        data.delete();
                        dataList.remove(0);
                        mAdapter.remove(0);
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            getSaleListUnique();
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                            hideDialog();
                            showToast(0, getRstr(R.string.submit_success));
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }
}
