package com.yxl.cashier_retail.ui.bean;

/**
 * Describe:统计-销售品类占比（实体类）
 * Created by jingang on 2024/8/30
 */
public class StatisticsClassData {
    /**
     * saleTotal : 119.0
     * kindName : 自营商品
     */

    private double saleTotal;//营业额
    private String kindName;//商品分类名称

    public double getSaleTotal() {
        return saleTotal;
    }

    public void setSaleTotal(double saleTotal) {
        this.saleTotal = saleTotal;
    }

    public String getKindName() {
        return kindName;
    }

    public void setKindName(String kindName) {
        this.kindName = kindName;
    }
}
