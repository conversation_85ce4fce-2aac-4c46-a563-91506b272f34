package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogCateEditHomeBinding;
import com.yxl.cashier_retail.databinding.DialogKefuBinding;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Describe:dialog（首页分类编辑、新增）
 * Created by jingang on 2024/8/21
 */
@SuppressLint("NonConstantResourceId")
public class CateEditHomeDialog extends BaseDialog<DialogCateEditHomeBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private static int type,//0.一级分类新增 1.一级分类编辑 2.二级分类新增 3.二级分类编辑
            imgId;
    private static String unique, childUnique, name, img;

    public static void showDialog(Activity activity, int type, String unique, String childUnique, String name, int imgId, String img, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        CateEditHomeDialog.type = type;
        CateEditHomeDialog.unique = unique;
        CateEditHomeDialog.childUnique = childUnique;
        CateEditHomeDialog.name = name;
        CateEditHomeDialog.imgId = imgId;
        CateEditHomeDialog.img = img;
        CateEditHomeDialog.listener = listener;
        CateEditHomeDialog dialog = new CateEditHomeDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 7);
        dialog.show();
    }

    public CateEditHomeDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.ivDialogImg.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        switch (type) {
            case 1:
                mBinding.tvDialogTitle.setText(getRstr(R.string.cate_edit));
                mBinding.etDialogName.setText(name);
                mBinding.linDialogImg.setVisibility(View.VISIBLE);
                Glide.with(mActivity)
                        .load(StringUtils.handledImgUrl(img))
                        .apply(new RequestOptions().error(R.mipmap.ic_camera001))
                        .into(mBinding.ivDialogImg);
                break;
            case 2:
                mBinding.tvDialogTitle.setText(getRstr(R.string.cate_child_add));
                mBinding.linDialogImg.setVisibility(View.GONE);
                break;
            case 3:
                mBinding.tvDialogTitle.setText(getRstr(R.string.cate_child_edit));
                mBinding.etDialogName.setText(name);
                mBinding.linDialogImg.setVisibility(View.GONE);
                break;
            default:
                mBinding.tvDialogTitle.setText(getRstr(R.string.cate_add));
                mBinding.linDialogImg.setVisibility(View.VISIBLE);
                break;
        }
    }

    @Override
    protected DialogCateEditHomeBinding getViewBinding() {
        return DialogCateEditHomeBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.ivDialogImg:
                //选择照片
                CateImgDialog.showDialog(mActivity, imgId, (id, img) -> {
                    imgId = id;
                    CateEditHomeDialog.img = img;
                    Glide.with(mActivity)
                            .load(StringUtils.handledImgUrl(img))
                            .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img))
                            .into(mBinding.ivDialogImg);
                });
                break;
            case R.id.tvDialogConfirm:
                //保存
                name = mBinding.etDialogName.getText().toString().trim();
                if (TextUtils.isEmpty(name)) {
                    showToast(1, getRstr(R.string.input_cate_name));
                    return;
                }
                postCateEdit();
                break;
        }
    }

    /**
     * 分类新增、删除、更新
     */
    private void postCateEdit() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        //0.一级分类新增 1.一级分类编辑 2.二级分类新增 3.二级分类编辑
        switch (type) {
            case 0:
                params.put("goodsKindParunique", 0);
                params.put("goodsKindName", name);
                params.put("kindIconId", imgId);
                break;
            case 1:
                params.put("kindUnique", unique);
                params.put("goodsKindName", name);
                params.put("kindIconId", imgId);
                break;
            case 2:
                params.put("goodsKindParunique", unique);
                params.put("goodsKindName", name);
                break;
            case 3:
                params.put("kindUnique", childUnique);
                params.put("goodsKindName", name);
                break;
        }
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getCateEdit(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        if (listener != null) {
                            listener.onConfirm();
                            dismiss();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm();
    }
}
