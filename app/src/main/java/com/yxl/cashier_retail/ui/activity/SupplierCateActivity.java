package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.RecyclerView;

import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivitySupplierCateBinding;
import com.yxl.cashier_retail.ui.adapter.SupplierCateAdapter;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.SupplierCateData;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:供货商管理-分类管理
 * Created by jingang on 2024/6/7
 */
@SuppressLint("NonConstantResourceId")
public class SupplierCateActivity extends BaseActivity<ActivitySupplierCateBinding> implements View.OnClickListener {
    private int selectType,//1.选择
            type,//0.新增 1.编辑
            pos;//列表下标
    private boolean isFirst;

    private SupplierCateAdapter mAdapter;
    private List<SupplierCateData> dataList = new ArrayList<>();

    @Override
    protected ActivitySupplierCateBinding getViewBinding() {
        return ActivitySupplierCateBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivBack.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.tvAdd.setOnClickListener(this);
        mBinding.tvConfirm.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected void initData() {
        selectType = getIntent().getIntExtra("type", 0);
        getCateList();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.tvAdd:
                //新增
                mBinding.linAdd.setVisibility(View.GONE);
                mBinding.linEdit.setVisibility(View.VISIBLE);
                type = 0;
                break;
            case R.id.tvConfirm:
                //保存
                if (TextUtils.isEmpty(mBinding.etName.getText().toString().trim())) {
                    showToast(1, getRstr(R.string.input_supplier_cate_name));
                    return;
                }
                if (type == 1) {
                    postCateEdit(dataList.get(pos).getSupplierKindUnique(), mBinding.etName.getText().toString().trim(), 1, pos);
                } else {
                    postCateAdd(mBinding.etName.getText().toString().trim());
                }
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new SupplierCateAdapter(this);
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new SupplierCateAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情
                if (selectType == 1) {
                    setResult(Constants.SELECT, new Intent()
                            .putExtra("unique", dataList.get(position).getSupplierKindUnique())
                            .putExtra("name", dataList.get(position).getSupplierKindName())
                    );
                    finish();
                }
            }

            @Override
            public void onEditClick(View view, int position) {
                //编辑
                mBinding.linAdd.setVisibility(View.GONE);
                mBinding.linEdit.setVisibility(View.VISIBLE);
                mBinding.etName.setText(dataList.get(position).getSupplierKindName());
                type = 1;
                pos = position;
            }

            @Override
            public void onDelClick(View view, int position) {
                //删除
                postCateEdit(dataList.get(position).getSupplierKindUnique(), "", 2, position);
            }
        });
        mBinding.smartRefreshLayout.setOnRefreshListener(refreshLayout -> getCateList());
        mBinding.smartRefreshLayout.setEnableLoadMore(false);
    }

    @SuppressLint("NotifyDataSetChanged")
    private void setItemTouchHelper() {
        //添加拖拽事件
        if (isFirst) {
            return;
        }
        isFirst = true;
        new ItemTouchHelper(new ItemTouchHelper.Callback() {
            @Override
            public int getMovementFlags(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
                return makeMovementFlags(ItemTouchHelper.UP | ItemTouchHelper.DOWN | ItemTouchHelper.LEFT | ItemTouchHelper.RIGHT, 0);
            }

            @Override
            public boolean onMove(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder, @NonNull RecyclerView.ViewHolder target) {
                recyclerView.getParent().requestDisallowInterceptTouchEvent(true);
                //得到当拖拽的viewHolder的Position
                int fromPosition = viewHolder.getAdapterPosition();
                //拿到当前拖拽到的item的viewHolder
                int toPosition = target.getAdapterPosition();
                if (fromPosition < toPosition) {
                    for (int i = fromPosition; i < toPosition; i++) {
                        Collections.swap(dataList, i, i + 1);
                    }
                } else {
                    for (int i = fromPosition; i > toPosition; i--) {
                        Collections.swap(dataList, i, i - 1);
                    }
                }
                mAdapter.notifyItemMoved(fromPosition, toPosition);
                return true;
            }

            @Override
            public void onSwiped(@NonNull RecyclerView.ViewHolder viewHolder, int direction) {

            }

            @Override
            public boolean isLongPressDragEnabled() {
                return true;
            }

            @Override
            public void clearView(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
                super.clearView(recyclerView, viewHolder);
                JSONArray array = new JSONArray();
                for (int i = 0; i < dataList.size(); i++) {
                    JSONObject object = new JSONObject();
                    try {
                        object.put("supplierKindUnique", dataList.get(i).getSupplierKindUnique());
                        array.put(object);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
                Log.e(tag, "array = " + array);
                postCateSort(array.toString());
            }
        }).attachToRecyclerView(mBinding.recyclerView);
    }

    /**
     * 分类列表
     */
    private void getCateList() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        RXHttpUtil.requestByBodyPostAsResponseList(this,
                ZURL.getSupplierCateList(),
                map,
                SupplierCateData.class,
                new RequestListListener<SupplierCateData>() {
                    @Override
                    public void onResult(List<SupplierCateData> list) {
                        hideDialog();
                        mBinding.smartRefreshLayout.finishRefresh();
                        dataList.clear();
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                            setItemTouchHelper();
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 分类新增
     *
     * @param name
     */
    private void postCateAdd(String name) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("createId", getStaffUnique());
        map.put("createBy", getStaffName());
        map.put("supKindName", name);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getSupplierCateAdd(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.SUPPLIER_CATE_LIST));
                        getCateList();
                        mBinding.linAdd.setVisibility(View.VISIBLE);
                        mBinding.linEdit.setVisibility(View.GONE);
                        mBinding.etName.setText("");
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 分类编辑或删除
     *
     * @param unique
     * @param name
     * @param type   1.编辑 2.删除
     */
    private void postCateEdit(String unique, String name, int type, int position) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("createId", getStaffUnique());
        map.put("createBy", getStaffName());
        map.put("supKindUnique", unique);
        if (!TextUtils.isEmpty(name)) {
            map.put("supKindName", name);
        }
        map.put("modifyType", type);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getSupplierCateEdit(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.SUPPLIER_CATE_LIST));
                        if (type == 1) {
                            dataList.get(position).setSupplierKindName(name);
                            mAdapter.notifyItemChanged(position, dataList.get(position));
                        } else {
                            dataList.remove(position);
                            mAdapter.remove(position);
                            if (dataList.size() > 0) {
                                mBinding.recyclerView.setVisibility(View.VISIBLE);
                                mBinding.linEmpty.setVisibility(View.GONE);
                            } else {
                                mBinding.recyclerView.setVisibility(View.GONE);
                                mBinding.linEmpty.setVisibility(View.VISIBLE);
                            }
                        }
                        mBinding.linAdd.setVisibility(View.VISIBLE);
                        mBinding.linEdit.setVisibility(View.GONE);
                        mBinding.etName.setText("");
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 排序
     *
     * @param array
     */
    private void postCateSort(String array) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("supKindSortList", array);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getSupplierCateSort(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.SUPPLIER_CATE_LIST));
                        getCateList();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        getCateList();
                    }
                });
    }

}
