package com.yxl.cashier_retail.ui.presenter;

import android.content.Context;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.ui.contract.MainContract;

/**
 * 主函数（收银）
 */
public class MainPresenter implements MainContract.Presenter {
    private Context context;
    private MainContract.View mView;

    public MainPresenter(Context context) {
        this.context = context;
    }

    @Override
    public void attachView(@NonNull MainContract.View view) {
        mView = view;
    }

    @Override
    public void detachView() {
        mView = null;
    }

}
