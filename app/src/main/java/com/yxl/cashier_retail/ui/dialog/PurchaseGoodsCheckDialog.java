package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogPurchaseGoodsCheckBinding;
import com.yxl.cashier_retail.ui.bean.PurchaseInfoData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.view.NumberKeyBoardView;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.StringUtils;

/**
 * Describe:dialog（购销单详情-商品核对）
 * Created by jingang on 2024/6/12
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class PurchaseGoodsCheckDialog extends BaseDialog<DialogPurchaseGoodsCheckBinding> implements View.OnClickListener {
    private static PurchaseInfoData.GoodsListBean data;
    private int type;//0.收货数量 1.零售价 2.网单价 3.会员价
    private double count,//实际入库数量
            inPrice,//进价
            suggestPrice,//建议售价
            salePrice,//零售价
            webPrice,//网单价
            cusPrice;//会员价

    public static void showDialog(Activity activity, PurchaseInfoData.GoodsListBean data, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        PurchaseGoodsCheckDialog.listener = listener;
        PurchaseGoodsCheckDialog.data = data;
        PurchaseGoodsCheckDialog dialog = new PurchaseGoodsCheckDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 10 * 7, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public PurchaseGoodsCheckDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        ((AnimationDrawable) mBinding.ivDialogCursorCount.getDrawable()).start();
        ((AnimationDrawable) mBinding.ivDialogCursorSalePrice.getDrawable()).start();
        ((AnimationDrawable) mBinding.ivDialogCursorOnlinePrice.getDrawable()).start();
        ((AnimationDrawable) mBinding.ivDialogCursorMemberPrice.getDrawable()).start();
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.ivDialogSub.setOnClickListener(this);
        mBinding.linDialogCount.setOnClickListener(this);
        mBinding.ivDialogAdd.setOnClickListener(this);
        mBinding.tvDialogSyn.setOnClickListener(this);
        mBinding.linDialogSalePrice.setOnClickListener(this);
        mBinding.linDialogOnlinePrice.setOnClickListener(this);
        mBinding.linDialogMemberPrice.setOnClickListener(this);
        mBinding.numberKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var) {
                //0.收货数量 1.零售价 2.网单价 3.会员价
                switch (type) {
                    case 1:
                        mBinding.tvDialogSalePriceInput.setText(var);
                        if (TextUtils.isEmpty(var)) {
                            mBinding.tvDialogSalePriceInput.setVisibility(View.GONE);
                            mBinding.tvDialogSalePriceHint.setVisibility(View.VISIBLE);
                            salePrice = 0;
                        } else {
                            mBinding.tvDialogSalePriceInput.setVisibility(View.VISIBLE);
                            mBinding.tvDialogSalePriceHint.setVisibility(View.GONE);
                            salePrice = Double.parseDouble(var);
                        }
                        break;
                    case 2:
                        mBinding.tvDialogOnlinePriceInput.setText(var);
                        if (TextUtils.isEmpty(var)) {
                            mBinding.tvDialogOnlinePriceInput.setVisibility(View.GONE);
                            mBinding.tvDialogOnlinePriceHint.setVisibility(View.VISIBLE);
                            webPrice = 0;
                        } else {
                            mBinding.tvDialogOnlinePriceInput.setVisibility(View.VISIBLE);
                            mBinding.tvDialogOnlinePriceHint.setVisibility(View.GONE);
                            webPrice = Double.parseDouble(var);
                        }
                        break;
                    case 3:
                        mBinding.tvDialogMemberPriceInput.setText(var);
                        if (TextUtils.isEmpty(var)) {
                            mBinding.tvDialogMemberPriceInput.setVisibility(View.GONE);
                            mBinding.tvDialogMemberPriceHint.setVisibility(View.VISIBLE);
                            cusPrice = 0;
                        } else {
                            mBinding.tvDialogMemberPriceInput.setVisibility(View.VISIBLE);
                            mBinding.tvDialogMemberPriceHint.setVisibility(View.GONE);
                            cusPrice = Double.parseDouble(var);
                        }
                        break;
                    default:
                        mBinding.tvDialogCount.setText(var);
                        if (TextUtils.isEmpty(var)) {
                            mBinding.tvDialogCount.setVisibility(View.GONE);
                            mBinding.tvDialogCountHint.setVisibility(View.VISIBLE);
                            count = 0;
                        } else {
                            mBinding.tvDialogCount.setVisibility(View.VISIBLE);
                            mBinding.tvDialogCountHint.setVisibility(View.GONE);
                            count = Double.parseDouble(var);
                        }
                        break;
                }
            }

            @Override
            public void onConfirm() {
                if (salePrice == 0) {
                    showToast(1, getRstr(R.string.input_price_sale));
                    return;
                }
                if (webPrice == 0) {
                    showToast(1, getRstr(R.string.input_price_online));
                    return;
                }
                if (cusPrice == 0) {
                    showToast(1, getRstr(R.string.input_price_member));
                    return;
                }
                if (listener != null) {
                    listener.onConfirm(count, inPrice, salePrice, webPrice, cusPrice);
                    dismiss();
                }
            }
        });
        setUI();
    }

    @Override
    protected DialogPurchaseGoodsCheckBinding getViewBinding() {
        return DialogPurchaseGoodsCheckBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.ivDialogSub:
                //减
                if (count <= 1) {
                    return;
                }
                mBinding.tvDialogCount.setText(DFUtils.getNum4(count - 1));
                break;
            case R.id.linDialogCount:
                //收货数量
                if (type != 0) {
                    type = 0;
                    mBinding.linDialogCount.setBackgroundResource(R.drawable.shape_green_kuang_5);
                    mBinding.ivDialogCursorCount.setVisibility(View.VISIBLE);
                    mBinding.linDialogSalePrice.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursorSalePrice.setVisibility(View.GONE);
                    mBinding.linDialogOnlinePrice.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursorOnlinePrice.setVisibility(View.GONE);
                    mBinding.linDialogMemberPrice.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursorMemberPrice.setVisibility(View.GONE);
                    if (count > 0) {
                        mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(count));
                    } else {
                        mBinding.numberKeyBoardView.setResultStr("");
                    }
                }
                break;
            case R.id.ivDialogAdd:
                //加
                mBinding.tvDialogCount.setText(DFUtils.getNum4(count + 1));
                break;
            case R.id.tvDialogSyn:
                //三价同步建议价
                mBinding.tvDialogSalePrice.setText(DFUtils.getNum4(suggestPrice));
                mBinding.tvDialogOnlinePrice.setText(DFUtils.getNum4(suggestPrice));
                mBinding.tvDialogMemberPrice.setText(DFUtils.getNum4(suggestPrice));
                break;
            case R.id.linDialogSalePrice:
                //零售价
                if (type != 1) {
                    type = 1;
                    mBinding.linDialogCount.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursorCount.setVisibility(View.GONE);
                    mBinding.linDialogSalePrice.setBackgroundResource(R.drawable.shape_green_kuang_5);
                    mBinding.ivDialogCursorSalePrice.setVisibility(View.VISIBLE);
                    mBinding.linDialogOnlinePrice.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursorOnlinePrice.setVisibility(View.GONE);
                    mBinding.linDialogMemberPrice.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursorMemberPrice.setVisibility(View.GONE);
                    if (salePrice > 0) {
                        mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(salePrice));
                    } else {
                        mBinding.numberKeyBoardView.setResultStr("");
                    }
                }
                break;
            case R.id.linDialogOnlinePrice:
                //网单价
                if (type != 2) {
                    type = 2;
                    mBinding.linDialogCount.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursorCount.setVisibility(View.GONE);
                    mBinding.linDialogSalePrice.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursorSalePrice.setVisibility(View.GONE);
                    mBinding.linDialogOnlinePrice.setBackgroundResource(R.drawable.shape_green_kuang_5);
                    mBinding.ivDialogCursorOnlinePrice.setVisibility(View.VISIBLE);
                    mBinding.linDialogMemberPrice.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursorMemberPrice.setVisibility(View.GONE);
                    if (webPrice > 0) {
                        mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(webPrice));
                    } else {
                        mBinding.numberKeyBoardView.setResultStr("");
                    }
                }
                break;
            case R.id.linDialogMemberPrice:
                //会员价
                if (type != 3) {
                    type = 3;
                    mBinding.linDialogCount.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursorCount.setVisibility(View.GONE);
                    mBinding.linDialogSalePrice.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursorSalePrice.setVisibility(View.GONE);
                    mBinding.linDialogOnlinePrice.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursorOnlinePrice.setVisibility(View.GONE);
                    mBinding.linDialogMemberPrice.setBackgroundResource(R.drawable.shape_green_kuang_5);
                    mBinding.ivDialogCursorMemberPrice.setVisibility(View.VISIBLE);
                    if (cusPrice > 0) {
                        mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(cusPrice));
                    } else {
                        mBinding.numberKeyBoardView.setResultStr("");
                    }
                }
                break;
        }
    }

    /**
     * 更新UI
     */
    private void setUI() {
        if (data == null) {
            return;
        }
        count = data.getPurchaseGoodsCount();
        inPrice = data.getPurchasePrice();
        suggestPrice = data.getSalePrice();
        Glide.with(getContext())
                .load(StringUtils.handledImgUrl(data.getGoodsPicturepath()))
                .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img))
                .into(mBinding.ivDialogImg);
        mBinding.tvDialogName.setText(data.getGoodsName());
        //采购单位
        if (TextUtils.isEmpty(data.getRestockUnit())) {
            mBinding.tvDialogCountPurchase.setText(getRstr(R.string.ordered_count_colon) + DFUtils.getNum4(data.getPurchaseGoodsCount()));
        } else {
            mBinding.tvDialogCountPurchase.setText(getRstr(R.string.ordered_count_colon) + DFUtils.getNum4(data.getPurchaseGoodsCount()) + data.getRestockUnit());
        }
        //配送单位
        if (TextUtils.isEmpty(data.getBillUnit())) {
            mBinding.tvDialogCountDelivery.setText(DFUtils.getNum4(data.getTradeGoodsCount()));
        } else {
            mBinding.tvDialogCountDelivery.setText(DFUtils.getNum4(data.getTradeGoodsCount()) + data.getBillUnit());
        }
        mBinding.tvDialogInPrice.setText(DFUtils.getNum2(inPrice));
        mBinding.tvDialogTotal.setText(DFUtils.getNum2(data.getTotalPrice()));
        mBinding.tvDialogSuggest.setText(DFUtils.getNum2(suggestPrice));
        if (count > 0) {
            mBinding.tvDialogCount.setVisibility(View.VISIBLE);
            mBinding.tvDialogCountHint.setVisibility(View.GONE);
            mBinding.tvDialogCount.setText(DFUtils.getNum4(count));
        } else {
            mBinding.tvDialogCount.setVisibility(View.GONE);
            mBinding.tvDialogCountHint.setVisibility(View.VISIBLE);
        }
        mBinding.tvDialogUnit.setText(data.getRestockUnit());
        //是否缺货 0.正常 1.缺货 2.超出
        switch (data.getNumberStatus()) {
            case 1:
                mBinding.linDialogStatus.setVisibility(View.VISIBLE);
                mBinding.tvDialogStatus.setText(getRstr(R.string.goods_short_stock));
                break;
            case 2:
                mBinding.linDialogStatus.setVisibility(View.VISIBLE);
                mBinding.tvDialogStatus.setText(getRstr(R.string.goods_many_stock));
                break;
            default:
                mBinding.linDialogStatus.setVisibility(View.GONE);
                break;
        }
        mBinding.tvDialogSalePrice.setText(getRstr(R.string.price_sale_colon) + DFUtils.getNum2(data.getRetailPriceNow()));
        mBinding.tvDialogOnlinePrice.setText(getRstr(R.string.price_online_colon) + DFUtils.getNum2(data.getNetPriceNow()));
        mBinding.tvDialogMemberPrice.setText(getRstr(R.string.price_member_colon) + DFUtils.getNum2(data.getMemberPriceNow()));
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(double count, double inPrice, double salePrice, double webPrice, double cusPrice);
    }
}
