package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.recyclerview.widget.ItemTouchHelper;

import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmSettingCashierBinding;
import com.yxl.cashier_retail.ui.adapter.PaymentAdapter;
import com.yxl.cashier_retail.ui.adapter.PaymentItemTouchHelper;
import com.yxl.cashier_retail.ui.adapter.PaymentSwitchAdapter;
import com.yxl.cashier_retail.ui.bean.PaymentData;
import com.yxl.cashier_retail.utils.DFUtils;

import org.litepal.LitePal;

import java.util.ArrayList;
import java.util.List;

/**
 * Describe:设置-收银设置
 * Created by jingang on 2024/5/15
 */
@SuppressLint({"NonConstantResourceId", "NotifyDataSetChanged"})
public class SettingCashierFragment extends BaseFragment<FmSettingCashierBinding> implements View.OnClickListener {
    private int type,//0.利润设置 1.支付设置
            ratioPoints;//积分比例（-1~100，默认1）
    private double ratioNoBarcode,//无码商品利润比（利润/售价）（0.00~1.00，默认0.15）
            ratioBarcode;//普通商品利润比(0.00~1.00,默认0.25)

    //支付方式
    private PaymentAdapter mAdapter;
    private List<PaymentData> dataList = new ArrayList<>(),
            selectList = new ArrayList<>();
    private PaymentSwitchAdapter switchAdapter;

    @Override
    protected FmSettingCashierBinding getViewBinding() {
        return FmSettingCashierBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.tvProfit.setOnClickListener(this);
        mBinding.tvPay.setOnClickListener(this);
        mBinding.ivSubNoBarcode.setOnClickListener(this);
        mBinding.ivAddNoBarcode.setOnClickListener(this);
        mBinding.ivSubBarcode.setOnClickListener(this);
        mBinding.ivAddBarcode.setOnClickListener(this);
        mBinding.ivSubPoints.setOnClickListener(this);
        mBinding.ivAddPoints.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected void initData() {
        setUI();
        initPaymentData();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvProfit:
                //利润设置
                if (type != 0) {
                    type = 0;
                    mBinding.tvProfit.setBackgroundResource(R.drawable.shape_white_topleft_5);
                    mBinding.tvProfit.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.tvProfit.setTextColor(getResources().getColor(R.color.black));
                    mBinding.linProfit.setVisibility(View.VISIBLE);
                    mBinding.tvPay.setBackgroundResource(0);
                    mBinding.tvPay.setTypeface(Typeface.DEFAULT);
                    mBinding.tvPay.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
                    mBinding.nslPay.setVisibility(View.GONE);
                }
                break;
            case R.id.tvPay:
                //支付设置
                if (type != 1) {
                    type = 1;
                    mBinding.tvProfit.setBackgroundResource(0);
                    mBinding.tvProfit.setTypeface(Typeface.DEFAULT);
                    mBinding.tvProfit.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
                    mBinding.linProfit.setVisibility(View.GONE);
                    mBinding.tvPay.setBackgroundColor(getResources().getColor(R.color.white));
                    mBinding.tvPay.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.tvPay.setTextColor(getResources().getColor(R.color.black));
                    mBinding.nslPay.setVisibility(View.VISIBLE);
                }
                break;
            case R.id.ivSubNoBarcode:
                //无码商品利润比-减少
                if (ratioNoBarcode > 0.01) {
                    ratioNoBarcode = ratioNoBarcode - 0.01;
                    SPUtils.getInstance().put(Constants.RATIO_NO_BARCODE, String.valueOf(ratioNoBarcode));
                    mBinding.tvCountNoBarcode.setText(DFUtils.getNum2(ratioNoBarcode));
                }
                break;
            case R.id.ivAddNoBarcode:
                //无码商品利润比-增加
                if (ratioNoBarcode < 1.00) {
                    ratioNoBarcode = ratioNoBarcode + 0.01;
                    SPUtils.getInstance().put(Constants.RATIO_NO_BARCODE, String.valueOf(ratioNoBarcode));
                    mBinding.tvCountNoBarcode.setText(DFUtils.getNum2(ratioNoBarcode));
                }
                break;
            case R.id.ivSubBarcode:
                //普通商品利润比-减少
                if (ratioBarcode > 0.01) {
                    ratioBarcode = ratioBarcode - 0.01;
                    SPUtils.getInstance().put(Constants.RATIO_BARCODE, String.valueOf(ratioBarcode));
                    mBinding.tvCountBarcode.setText(DFUtils.getNum2(ratioBarcode));
                }
                break;
            case R.id.ivAddBarcode:
                //普通商品利润比-增加
                if (ratioBarcode < 1.00) {
                    ratioBarcode = ratioBarcode + 0.01;
                    SPUtils.getInstance().put(Constants.RATIO_BARCODE, String.valueOf(ratioBarcode));
                    mBinding.tvCountBarcode.setText(DFUtils.getNum2(ratioBarcode));
                }
                break;
            case R.id.ivSubPoints:
                //积分比例-减少
                if (ratioPoints > -1) {
                    ratioPoints--;
                    SPUtils.getInstance().put(Constants.POINTS_RATIO, ratioPoints);
                    mBinding.tvCountPoints.setText(String.valueOf(ratioPoints));
                }
                break;
            case R.id.ivAddPoints:
                //积分比例-增加
                if (ratioPoints < 100) {
                    ratioPoints++;
                    SPUtils.getInstance().put(Constants.POINTS_RATIO, ratioPoints);
                    mBinding.tvCountPoints.setText(String.valueOf(ratioPoints));
                }
                break;
        }
    }

    /**
     * 更新UI
     */
    private void setUI() {
        String str0 = SPUtils.getInstance().getString(Constants.RATIO_NO_BARCODE, "0.15"),
                str1 = SPUtils.getInstance().getString(Constants.RATIO_BARCODE, "0.25");
        ratioNoBarcode = TextUtils.isEmpty(str0) ? 0.15 : Double.parseDouble(str0);
        mBinding.tvCountNoBarcode.setText(DFUtils.getNum2(ratioNoBarcode));
        ratioBarcode = TextUtils.isEmpty(str1) ? 0.25 : Double.parseDouble(str1);
        mBinding.tvCountBarcode.setText(DFUtils.getNum2(ratioBarcode));
        ratioPoints = SPUtils.getInstance().getInt(Constants.POINTS_RATIO, 1);
        mBinding.tvCountPoints.setText(String.valueOf(ratioPoints));
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //支付方式
        mAdapter = new PaymentAdapter(getActivity());
        mBinding.rvPaymentHor.setAdapter(mAdapter);
        new ItemTouchHelper(new PaymentItemTouchHelper<>(getActivity(), selectList, mAdapter, list -> {
            if (list == null) {
                return;
            }
            //生成数据
            List<PaymentData> paymentList = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                paymentList.add(new PaymentData(list.get(i).getPaymentId(), list.get(i).isSelect()));
            }
            if (LitePal.findFirst(PaymentData.class) != null) {
                LitePal.deleteAll(PaymentData.class);
            }
            LitePal.saveAll(paymentList);
            dataList.clear();
            dataList.addAll(paymentList);
            switchAdapter.setDataList(dataList);
        })).attachToRecyclerView(mBinding.rvPaymentHor);

        //支付方式开关
        switchAdapter = new PaymentSwitchAdapter(getActivity());
        mBinding.rvPaymentVer.setAdapter(switchAdapter);
        switchAdapter.setOnItemClickListener((view, position) -> {
            if (dataList.get(position).isSelect()) {
                dataList.get(position).setSelect(false);
                switchAdapter.notifyItemChanged(position);
                for (PaymentData data : dataList) {
                    if (dataList.get(position).getPaymentId() == data.getPaymentId()) {
                        data.setSelect(false);
                        data.save();
                    }
                }
                for (int i = 0; i < selectList.size(); i++) {
                    if (selectList.get(i).getPaymentId() == dataList.get(position).getPaymentId()) {
                        selectList.remove(i);
                        mAdapter.remove(i);
                    }
                }
            } else {
                if (selectList.size() > 6) {
                    showToast(1, getRstr(R.string.shortcut_display_length));
                    return;
                }
                dataList.get(position).setSelect(true);
                switchAdapter.notifyItemChanged(position);
                for (PaymentData data : dataList) {
                    if (dataList.get(position).getPaymentId() == data.getPaymentId()) {
                        data.setSelect(true);
                        data.save();
                    }
                }
                selectList.add(dataList.get(position));
                mAdapter.setDataList(selectList);
            }
        });
    }

    /**
     * 获取支付方式数据
     */
    private void initPaymentData() {
        dataList.clear();
        dataList.addAll(LitePal.findAll(PaymentData.class));
        switchAdapter.setDataList(dataList);

        selectList.clear();
        for (int i = 0; i < dataList.size(); i++) {
            if (dataList.get(i).isSelect()) {
                selectList.add(dataList.get(i));
            }
        }
        mAdapter.setDataList(selectList);
    }
}
