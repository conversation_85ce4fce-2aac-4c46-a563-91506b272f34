package com.yxl.cashier_retail.ui.popupwindow;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.PopupWindow;

import androidx.recyclerview.widget.RecyclerView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.ui.adapter.ConditionPopAdapter;
import com.yxl.cashier_retail.ui.adapter.SupplierPopAdapter;
import com.yxl.cashier_retail.ui.bean.ConditionData;
import com.yxl.cashier_retail.ui.bean.SupplierPcData;

import java.util.List;

/**
 * Describe:popupWindow（选择供货商）
 * Created by jingang on 2025/06/07
 */
@SuppressLint({"StaticFieldLeak", "NonConstantResourceId"})
public class SupplierPop extends PopupWindow {
    private static View viewIcon;
    private static List<SupplierPcData> list;
    private static String unique;
    private RecyclerView recyclerView;

    private SupplierPopAdapter mAdapter;

    private final Animation openAnim, closeAnim;

    public static void showDialog(Context context, View viewIcon, View viewShow, int width, List<SupplierPcData> list, String unique, MyListener listener) {
        SupplierPop.viewIcon = viewIcon;
        SupplierPop.list = list;
        SupplierPop.unique = unique;
        SupplierPop popupWindow = new SupplierPop(context);
        popupWindow.setListener(listener);
        popupWindow.setWidth(width);
        popupWindow.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
        //new ColorDrawable(0)即为透明背景
        popupWindow.setBackgroundDrawable(new ColorDrawable(0));
        // 设置动画效果
//        popupWindow.setAnimationStyle(R.style.dialog_anim);
        //设置可以获取焦点
        popupWindow.setFocusable(true);//设置为true isShowing才会有值
        //设置可以触摸弹出框以外的区域
        popupWindow.setOutsideTouchable(true);
        //放在具体控件下方
        popupWindow.showAsDropDown(viewShow);
    }

    public SupplierPop(Context context) {
        super(context);
        View view = View.inflate(context, R.layout.pop_supplier, null);
        setContentView(view);
        recyclerView = view.findViewById(R.id.rvPop);
        view.findViewById(R.id.tvPopAdd).setOnClickListener(v -> {
            if (listener != null) {
                listener.onAddClick();
                dismiss();
            }
        });

        openAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_1);
        closeAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_2);
        openAnim.setFillAfter(true);
        closeAnim.setFillAfter(true);
        setAdapter();
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        for (int i = 0; i < list.size(); i++) {
            if (!TextUtils.isEmpty(unique)) {
                if (list.get(i).getSupplier_unique().equals(unique)) {
                    list.get(i).setSelect(true);
                } else {
                    if (list.get(i).isSelect()) {
                        list.get(i).setSelect(false);
                    }
                }
            }
        }
        mAdapter = new SupplierPopAdapter(getContentView().getContext());
        recyclerView.setAdapter(mAdapter);
        mAdapter.setDataList(list);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (listener != null) {
                listener.onCallBack(list.get(position));
                dismiss();
            }
        });
    }

    @Override
    public void showAsDropDown(View anchor) {
        super.showAsDropDown(anchor);
        viewIcon.startAnimation(openAnim);
    }

    @Override
    public void dismiss() {
        super.dismiss();
        viewIcon.startAnimation(closeAnim);
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onAddClick();

        void onCallBack(SupplierPcData data);
    }
}
