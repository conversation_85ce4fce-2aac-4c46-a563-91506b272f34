package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.view.View;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.yxl.cashier_retail.MyApplication;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivitySettingBinding;
import com.yxl.cashier_retail.ui.dialog.MenuDialog;
import com.yxl.cashier_retail.ui.fragment.SettingCashierFragment;
import com.yxl.cashier_retail.ui.fragment.SettingOnlineFragment;
import com.yxl.cashier_retail.ui.fragment.SettingPartsFragment;
import com.yxl.cashier_retail.ui.fragment.SettingShopFragment;
import com.yxl.cashier_retail.ui.fragment.SettingSystemFragment;

/**
 * Describe:设置
 * Created by jingang on 2024/5/13
 */
@SuppressLint("NonConstantResourceId")
public class SettingActivity extends BaseActivity<ActivitySettingBinding> implements View.OnClickListener {

    private Fragment[] fragments;
    private SettingShopFragment shopFragment;
    private SettingCashierFragment cashierFragment;
    private SettingPartsFragment partsFragment;
    private SettingOnlineFragment onlineFragment;
    private SettingSystemFragment systemFragment;

    private int index = 0;//点击的页卡索引
    private int currentTabIndex = 0;//当前的页卡索引

    @Override
    protected ActivitySettingBinding getViewBinding() {
        return ActivitySettingBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivLogo.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.linShop.setOnClickListener(this);
        mBinding.linCash.setOnClickListener(this);
        mBinding.linParts.setOnClickListener(this);
        mBinding.linOnline.setOnClickListener(this);
        mBinding.linSystem.setOnClickListener(this);
        setFragment();
    }

    @Override
    protected void initData() {
        setNetwork();
    }

    @Override
    public void getNetwork() {
        setNetwork();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivLogo:
                //菜单
                MenuDialog.showDialog(this, type -> {
                    //0.收银 1.入库 2.查询 3.统计 4.网单 5.会员 6.补货 7.营销 8.交班 9.设置
                    if (type != 9) {
                        switch (type) {
                            case 1:
                                goToActivity(InActivity.class);
                                break;
                            case 2:
                                goToActivity(QueryActivity.class);
                                break;
                            case 3:
                                goToActivity(StatisticsActivity.class);
                                break;
                            case 4:
                                goToActivity(OrderActivity.class);
                                break;
                            case 5:
                                goToActivity(MemberActivity.class);
                                break;
                            case 6:
                                goToActivity(MallActivity.class);
                                break;
                            case 7:
                                goToActivity(MarketingActivity.class);
                                break;
                            case 8:
                                goToActivity(ShiftActivity.class);
                                break;
                        }
                        finish();
                    }
                });
                break;
            case R.id.tvCashier:
                //收银台
                onBackPressed();
                break;
            case R.id.linShop:
                //店铺信息
                index = 0;
                fragmentControl();
                break;
            case R.id.linCash:
                //收银设置
                index = 1;
                fragmentControl();
                break;
            case R.id.linParts:
                //配件设置
                index = 2;
                fragmentControl();
                break;
            case R.id.linOnline:
                //线上设置
                index = 3;
                fragmentControl();
                break;
            case R.id.linSystem:
                //系统设置
                index = 4;
                fragmentControl();
                break;
        }
    }

    /**
     * 网络监听
     */
    private void setNetwork() {
        if (MyApplication.mNetwork != null) {
            if (MyApplication.mNetwork.isConnect()) {
                switch (MyApplication.mNetwork.getConnectType()) {
                    case 1:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network001);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 2:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network002);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 3:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network003);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    default:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                        mBinding.tvNetwork.setText(getRstr(R.string.no_network));
                        break;
                }
            } else {
                mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                mBinding.tvNetwork.setText(getRstr(R.string.no_network));
            }
        } else {
            mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
            mBinding.tvNetwork.setText(getRstr(R.string.no_network));
        }
    }

    /**
     * 设置fragment
     */
    public void setFragment() {
        shopFragment = new SettingShopFragment();
        cashierFragment = new SettingCashierFragment();
        partsFragment = new SettingPartsFragment();
        onlineFragment = new SettingOnlineFragment();
        systemFragment = new SettingSystemFragment();
        fragments = new Fragment[]{shopFragment,
                cashierFragment,
                partsFragment,
                onlineFragment,
                systemFragment};
        setBottomColor();

        getSupportFragmentManager().beginTransaction().add(R.id.fragment_container, fragments[index]).show(fragments[index]).commit();
    }

    /**
     * 控制fragment的变化
     */
    public void fragmentControl() {
        if (currentTabIndex != index) {
            removeBottomColor();
            setBottomColor();

            FragmentTransaction trx = getSupportFragmentManager().beginTransaction();
            trx.hide(fragments[currentTabIndex]);
            if (!fragments[index].isAdded()) {
                trx.add(R.id.fragment_container, fragments[index]);
            }
            trx.show(fragments[index]).commitAllowingStateLoss();
            currentTabIndex = index;
        }
    }

    /**
     * 设置底部栏按钮变色
     */
    private void setBottomColor() {
        switch (index) {
            case 0:
                mBinding.linShop.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivShop.setImageResource(R.mipmap.ic_setting_shop001);
                mBinding.tvShop.setTextColor(getResources().getColor(R.color.white));
                break;
            case 1:
                mBinding.linCash.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivCash.setImageResource(R.mipmap.ic_setting_cashier001);
                mBinding.tvCash.setTextColor(getResources().getColor(R.color.white));
                break;
            case 2:
                mBinding.linParts.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivParts.setImageResource(R.mipmap.ic_setting_parts001);
                mBinding.tvParts.setTextColor(getResources().getColor(R.color.white));
                break;
            case 3:
                mBinding.linOnline.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivOnline.setImageResource(R.mipmap.ic_setting_online001);
                mBinding.tvOnline.setTextColor(getResources().getColor(R.color.white));
                break;
            case 4:
                mBinding.linSystem.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivSystem.setImageResource(R.mipmap.ic_setting_system001);
                mBinding.tvSystem.setTextColor(getResources().getColor(R.color.white));
                break;
            default:
                break;
        }
    }

    /**
     * 清除底部栏颜色
     */
    private void removeBottomColor() {
        switch (currentTabIndex) {
            case 0:
                mBinding.linShop.setBackgroundResource(0);
                mBinding.ivShop.setImageResource(R.mipmap.ic_setting_shop002);
                mBinding.tvShop.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 1:
                mBinding.linCash.setBackgroundResource(0);
                mBinding.ivCash.setImageResource(R.mipmap.ic_setting_cashier002);
                mBinding.tvCash.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 2:
                mBinding.linParts.setBackgroundResource(0);
                mBinding.ivParts.setImageResource(R.mipmap.ic_setting_parts002);
                mBinding.tvParts.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 3:
                mBinding.linOnline.setBackgroundResource(0);
                mBinding.ivOnline.setImageResource(R.mipmap.ic_setting_online002);
                mBinding.tvOnline.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 4:
                mBinding.linSystem.setBackgroundResource(0);
                mBinding.ivSystem.setImageResource(R.mipmap.ic_setting_system002);
                mBinding.tvSystem.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            default:
                break;
        }
    }

}
