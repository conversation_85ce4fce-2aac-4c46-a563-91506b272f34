package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.GoodsSaleStatisticsData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:查商品-商品列表（适配器）
 * Created by jingang on 2024/5/23
 */
public class GoodsQueryAdapter extends BaseAdapter<GoodsSaleStatisticsData.DataBean> {

    public GoodsQueryAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_query;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName, tvBarcode, tvCount, tvGift, tvTotal, tvPurchase, tvProfit;
        tvName = holder.getView(R.id.tvItemName);
        tvBarcode = holder.getView(R.id.tvItemBarcode);
        tvCount = holder.getView(R.id.tvItemCount);
        tvGift = holder.getView(R.id.tvItemGift);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvPurchase = holder.getView(R.id.tvItemPurchase);
        tvProfit = holder.getView(R.id.tvItemProfit);

        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
            tvBarcode.setTextColor(mContext.getResources().getColor(R.color.white));
            tvCount.setTextColor(mContext.getResources().getColor(R.color.white));
            tvGift.setTextColor(mContext.getResources().getColor(R.color.white));
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.white));
            tvPurchase.setTextColor(mContext.getResources().getColor(R.color.white));
            tvProfit.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            if (position % 2 == 0) {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
            } else {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
            }
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
            tvBarcode.setTextColor(mContext.getResources().getColor(R.color.black));
            tvCount.setTextColor(mContext.getResources().getColor(R.color.black));
            tvGift.setTextColor(mContext.getResources().getColor(R.color.black));
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.black));
            tvPurchase.setTextColor(mContext.getResources().getColor(R.color.black));
            tvProfit.setTextColor(mContext.getResources().getColor(R.color.black));
        }
        tvName.setText(TextUtils.isEmpty(mDataList.get(position).getGoodsName()) ? "-" : mDataList.get(position).getGoodsName());
        tvBarcode.setText(TextUtils.isEmpty(mDataList.get(position).getGoodsBarcode()) ? "-" : mDataList.get(position).getGoodsBarcode());
        tvCount.setText(DFUtils.getNum4(mDataList.get(position).getSaleCount()));
        tvGift.setText(DFUtils.getNum4(mDataList.get(position).getGiftCount()));
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getSaleSum()));
        tvPurchase.setText(DFUtils.getNum2(mDataList.get(position).getPurSum()));
        tvProfit.setText(DFUtils.getNum2(mDataList.get(position).getGrossProfit()) + "%");
    }
}