package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.ActivityListData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:营销-商品满赠（适配器）
 * Created by jingang on 2024/10/12
 */
public class MarketingGiftAdapter extends BaseAdapter<ActivityListData.PromotionGoodsGiftListBean> {

    public MarketingGiftAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_marketing_gift;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvBarcode, tvPrice, tvName0, tvPrice0, tvName1, tvPrice1, tvName2, tvPrice2;
        tvName = holder.getView(R.id.tvItemName);
        tvBarcode = holder.getView(R.id.tvItemBarcode);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvName0 = holder.getView(R.id.tvItemName0);
        tvPrice0 = holder.getView(R.id.tvItemPrice0);
        tvName1 = holder.getView(R.id.tvItemName1);
        tvPrice1 = holder.getView(R.id.tvItemPrice1);
        tvName2 = holder.getView(R.id.tvItemName2);
        tvPrice2 = holder.getView(R.id.tvItemPrice2);

        tvName.setText(TextUtils.isEmpty(mDataList.get(position).getGoodsName()) ? "-" : mDataList.get(position).getGoodsName());
        tvBarcode.setText(TextUtils.isEmpty(mDataList.get(position).getGoodsBarcode()) ? "(-)" : "(" + mDataList.get(position).getGoodsBarcode() + "）");
        tvPrice.setText(getRstr(R.string.price_old_colon) + DFUtils.getNum2(mDataList.get(position).getSalePrice()));

        if (mDataList.get(position).getMeetCount1() > 0) {
            tvName0.setText(getRstr(R.string.full) + mDataList.get(position).getMeetCount1() + getRstr(R.string.piece_gift) + mDataList.get(position).getGiftCount1() + getRstr(R.string.piece));
            tvPrice0.setText(TextUtils.isEmpty(mDataList.get(position).getGoodsNameGift1()) ? "-" : mDataList.get(position).getGoodsNameGift1());
        } else {
            tvName0.setText("");
            tvPrice0.setText("");
        }
        if (mDataList.get(position).getMeetCount2() > 0) {
            tvName1.setText(getRstr(R.string.full) + mDataList.get(position).getMeetCount2() + getRstr(R.string.piece_gift) + mDataList.get(position).getGiftCount2() + getRstr(R.string.piece));
            tvPrice1.setText(TextUtils.isEmpty(mDataList.get(position).getGoodsNameGift2()) ? "-" : mDataList.get(position).getGoodsNameGift2());
        } else {
            tvName1.setText("");
            tvPrice1.setText("");
        }
        if (mDataList.get(position).getMeetCount3() > 0) {
            tvName2.setText(getRstr(R.string.full) + mDataList.get(position).getMeetCount3() + getRstr(R.string.piece_gift) + mDataList.get(position).getGiftCount3() + getRstr(R.string.piece));
            tvPrice2.setText(TextUtils.isEmpty(mDataList.get(position).getGoodsNameGift3()) ? "-" : mDataList.get(position).getGoodsNameGift3());
        } else {
            tvName2.setText("");
            tvPrice2.setText("");
        }
    }
}
