package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.ActivityListData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:营销-订单促销（适配器）
 * Created by jingang on 2024/10/12
 */
public class MarketingOrderAdapter extends BaseAdapter<ActivityListData.PromotionOrderMarkdownListBean> {

    public MarketingOrderAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_marketing_order;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName0, tvPrice0, tvName1, tvPrice1, tvName2, tvPrice2;
        tvName0 = holder.getView(R.id.tvItemName0);
        tvPrice0 = holder.getView(R.id.tvItemPrice0);
        tvName1 = holder.getView(R.id.tvItemName1);
        tvPrice1 = holder.getView(R.id.tvItemPrice1);
        tvName2 = holder.getView(R.id.tvItemName1);
        tvPrice2 = holder.getView(R.id.tvItemPrice1);

        if (mDataList.get(position).getMeetPrice1() > 0) {
            tvName0.setText(getRstr(R.string.full) + DFUtils.getNum2(mDataList.get(position).getMeetPrice1())
                    + getRstr(R.string.yuan_discount) + DFUtils.getNum2(mDataList.get(position).getDiscountPrice1())
                    + getRstr(R.string.yuan_gift) + mDataList.get(position).getGiftCount1() + getRstr(R.string.piece));
            tvPrice0.setText(TextUtils.isEmpty(mDataList.get(position).getGiftGoodsName1()) ? "-" : mDataList.get(position).getGiftGoodsName1());
        } else {
            tvName0.setText("");
            tvPrice0.setText("");
        }
        if (mDataList.get(position).getMeetPrice2() > 0) {
            tvName1.setText(getRstr(R.string.full) + DFUtils.getNum2(mDataList.get(position).getMeetPrice2())
                    + getRstr(R.string.yuan_discount) + DFUtils.getNum2(mDataList.get(position).getDiscountPrice2())
                    + getRstr(R.string.yuan_gift) + mDataList.get(position).getGiftCount2() + getRstr(R.string.piece));
            tvPrice1.setText(TextUtils.isEmpty(mDataList.get(position).getGiftGoodsName2()) ? "-" : mDataList.get(position).getGiftGoodsName2());
        } else {
            tvName1.setText("");
            tvPrice1.setText("");
        }
        if (mDataList.get(position).getMeetPrice3() > 0) {
            tvName2.setText(getRstr(R.string.full) + DFUtils.getNum2(mDataList.get(position).getMeetPrice3())
                    + getRstr(R.string.yuan_discount) + DFUtils.getNum2(mDataList.get(position).getDiscountPrice3())
                    + getRstr(R.string.yuan_gift) + mDataList.get(position).getGiftCount3() + getRstr(R.string.piece));
            tvPrice2.setText(TextUtils.isEmpty(mDataList.get(position).getGiftGoodsName3()) ? "-" : mDataList.get(position).getGiftGoodsName3());
        } else {
            tvName2.setText("");
            tvPrice2.setText("");
        }
    }
}
