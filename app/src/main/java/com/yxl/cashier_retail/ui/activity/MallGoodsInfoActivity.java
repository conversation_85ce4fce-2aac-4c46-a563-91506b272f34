package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.os.AsyncTask;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.google.gson.Gson;
import com.shuyu.gsyvideoplayer.GSYVideoManager;
import com.shuyu.gsyvideoplayer.listener.GSYSampleCallBack;
import com.shuyu.gsyvideoplayer.utils.Debuger;
import com.shuyu.gsyvideoplayer.utils.GSYVideoHelper;
import com.youth.banner.adapter.BannerImageAdapter;
import com.youth.banner.holder.BannerImageHolder;
import com.youth.banner.listener.OnPageChangeListener;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityMallGoodsInfoBinding;
import com.yxl.cashier_retail.ui.adapter.MallGoodsInfoBindAdapter;
import com.yxl.cashier_retail.ui.adapter.MallGoodsInfoCouponsAdapter;
import com.yxl.cashier_retail.ui.adapter.MallGoodsInfoGiftAdapter;
import com.yxl.cashier_retail.ui.adapter.MallGoodsInfoImgData;
import com.yxl.cashier_retail.ui.bean.MallGoodsInfoData;
import com.yxl.cashier_retail.ui.bean.MallOrderSettlementData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.view.MyVideoPlayer;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:商城-商品详情
 * Created by jingang on 2024/6/3
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class MallGoodsInfoActivity extends BaseActivity<ActivityMallGoodsInfoBinding> implements View.OnClickListener {
    private int goodsId,//商品id
            cartCount,//购物车数量
            startOrder,//起订量
            count,//数量
            stockCount,//库存
            isFu, // 允许负库存销售 1.允许
            is_activity;//是否 参与活动（活动无法重复参加） 0 -无 ；1-限购 ；2 捆绑 ； 5.降价活动 -（如：硬核补贴） 6、阶梯价，7、预售
    private double price;//当前价格

    //视频播放器
    private GSYVideoHelper smallVideoHelper;
    private MyVideoPlayer mPlayer;
    private GSYVideoHelper.GSYVideoHelperBuilder gsySmallVideoHelperBuilder;
    private List<MallGoodsInfoImgData> imgList = new ArrayList();

    //优惠券
    private MallGoodsInfoCouponsAdapter couponsAdapter;
    private List<MallGoodsInfoData.DataBean.Coupon> couponsList = new ArrayList<>();

    //满赠
    private MallGoodsInfoGiftAdapter giftAdapter;
    private List<MallGoodsInfoData.DataBean.Fullgift.GiftGood> giftList = new ArrayList<>();

    //捆绑
    private MallGoodsInfoBindAdapter bindAdapter;
    private List<MallGoodsInfoData.DataBean.Bind> bindList = new ArrayList<>();

    @Override
    protected ActivityMallGoodsInfoBinding getViewBinding() {
        return ActivityMallGoodsInfoBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivBack.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.tvCredit.setOnClickListener(this);
        mBinding.tvOrder.setOnClickListener(this);
        mBinding.relCart.setOnClickListener(this);
        mBinding.tvCoupons.setOnClickListener(this);
        mBinding.ivSub.setOnClickListener(this);
        mBinding.ivAdd.setOnClickListener(this);
        mBinding.tvBuy.setOnClickListener(this);
        mBinding.tvCart.setOnClickListener(this);
        initVideoPlayer();
        setAdapter();
    }

    @Override
    protected void initData() {
        goodsId = getIntent().getIntExtra("goodsId", 0);
        getGoodsInfo();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.tvCredit:
                //赊销
                break;
            case R.id.tvOrder:
                //订单
                break;
            case R.id.relCart:
                //购物车
                goToActivity(MallCartActivity.class);
                break;
            case R.id.tvCoupons:
                //优惠券
                break;
            case R.id.ivSub:
                //减
                if (count == startOrder) {
                    showToast(1, "数量不可低于起订量");
                    return;
                }
                count--;
                mBinding.tvCount.setText(String.valueOf(count));
                break;
            case R.id.ivAdd:
                //加
                if (is_activity == 7) {
                    //预售
                    count++;
                    mBinding.tvCount.setText(String.valueOf(count));
                    return;
                }
                if (isFu != 1) {
                    //负库存销售
                    if (count == stockCount) {
                        showToast(1, "数量不能高于库存数量");
                        return;
                    }
                }
                count++;
                mBinding.tvCount.setText(String.valueOf(count));
                break;
            case R.id.tvBuy:
                //立即购买
                break;
            case R.id.tvCart:
                //加入购物车
                break;
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        mBinding.webView.onPause();
        //暂停WebView在后台的JS活动
        mBinding.webView.pauseTimers();
    }

    @Override
    protected void onResume() {
        super.onResume();
        mBinding.webView.onResume();
        mBinding.webView.resumeTimers();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mBinding.webView.destroy();
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //优惠券
        couponsAdapter = new MallGoodsInfoCouponsAdapter(this);
        mBinding.rvCoupons.setAdapter(couponsAdapter);

        //满赠
        giftAdapter = new MallGoodsInfoGiftAdapter(this);
        mBinding.rvGift.setAdapter(giftAdapter);
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(MallGoodsInfoData.DataBean data) {
        is_activity = data.getIs_activity();
        startOrder = data.getStart_order();
        stockCount = data.getStock_count();
        isFu = data.getAuto_fxiaoshou();

        //图片
        imgList.clear();
        int index = 0;
        if (null != data.getVideo_url() && !data.getVideo_url().isEmpty()) {
            imgList.add(new MallGoodsInfoImgData(StringUtils.handledImgUrl(data.getGoods_img()), index));
            index++;
            ImageView imageView = new ImageView(this);
            imageView.setVisibility(View.INVISIBLE);
            mBinding.flVideo.setVisibility(View.VISIBLE);
            smallVideoHelper.addVideoPlayer(0, imageView, tag, mBinding.flVideo, mBinding.ivPlay);
            mBinding.ivPlay.setOnClickListener(v -> {
                new Handler().postDelayed(() -> {
                    smallVideoHelper.addVideoPlayer(0, imageView, tag, mBinding.flVideo, mBinding.ivPlay);
                }, 100);
                smallVideoHelper.setPlayPositionAndTag(0, tag);
                gsySmallVideoHelperBuilder.setUrl(StringUtils.handledImgUrl(data.getVideo_url()));
                smallVideoHelper.startPlay();
                //必须在startPlay之后设置才能生效
                //smallVideoHelper.getGsyVideoPlayer().getTitleTextView().setVisibility(View.VISIBLE);
            });
        }
        for (String url : data.getFileList()) {
            imgList.add(new MallGoodsInfoImgData(StringUtils.handledImgUrl(url), index));
            index++;
        }
        mBinding.tvImgCount.setText(String.format("1/%s", imgList.size()));
        mBinding.tvImgCount.setVisibility(imgList.size() > 1 ? View.VISIBLE : View.GONE);
        mBinding.banner.setAdapter(new BannerImageAdapter<MallGoodsInfoImgData>(imgList) {
                    @Override
                    public void onBindView(BannerImageHolder holder, MallGoodsInfoImgData model, int position, int size) {
                        Glide.with(mContext)
                                .load(model.getUrl())
                                .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img))
                                .into(holder.imageView);
                    }
                })
                .isAutoLoop(false)
                .addBannerLifecycleObserver(this)//添加生命周期观察者
                .addOnPageChangeListener(new OnPageChangeListener() {
                    @Override
                    public void onPageScrolled(int i, float v, int i1) {

                    }

                    @Override
                    public void onPageSelected(int i) {
                        if (null != data.getVideo_url() && !data.getVideo_url().isEmpty()) {
                            mBinding.ivPlay.setVisibility(i == 0 ? View.VISIBLE : View.GONE);
                        } else {
                            mBinding.ivPlay.setVisibility(View.GONE);
                        }
                        mBinding.tvImgCount.setText(String.format("%d/%s", i + 1, imgList.size()));
                    }

                    @Override
                    public void onPageScrollStateChanged(int i) {

                    }
                });

        if (cartCount > 0) {
            count = cartCount;
            mBinding.tvCartCount.setVisibility(View.VISIBLE);
            mBinding.tvCartCount.setText(String.valueOf(cartCount));
        } else {
            count = startOrder;
            mBinding.tvCartCount.setVisibility(View.GONE);
        }
        //价格
        if (data.getOnline_price() > 0) {
            if (data.getOnline_price() < data.getGold_deduct()) {
                price = 0;
            } else {
                price = data.getOnline_price() - data.getGold_deduct();
            }
        } else {
            price = 0;
        }
        mBinding.tvCount.setText(String.valueOf(count));
        mBinding.tvName.setText(data.getGoods_name());
        mBinding.tvSaleCount.setText(String.valueOf(data.getSale_count()));
        mBinding.tvStartOrder.setText(String.valueOf(data.getStart_order()));
        mBinding.tvStock.setText(String.valueOf(data.getStock_count()));
        mBinding.tvStartDate.setText(data.getProduction_date());
        mBinding.tvPrice.setText(DFUtils.getNum2(price));
        if (data.getGold_deduct() > 0) {
            mBinding.tvPriceDeduct.setVisibility(View.VISIBLE);
            mBinding.tvPriceBefore.setVisibility(View.VISIBLE);
            mBinding.tvPriceDeduct.setText("金圈币抵扣" + DFUtils.getNum2(data.getGold_deduct()));
            mBinding.tvPriceBefore.setText("抵扣前" + getRstr(R.string.money) + DFUtils.getNum2(data.getOnline_price()));
        } else {
            mBinding.tvPriceDeduct.setVisibility(View.GONE);
            mBinding.tvPriceBefore.setVisibility(View.GONE);
        }
        if (TextUtils.isEmpty(data.getSpec_name())) {
            mBinding.tvSpecs.setText("-");
        } else {
            mBinding.tvSpecs.setText(data.getSpec_name());
        }
        if (data.getXianggou() != null) {
            mBinding.tvQuotaTips.setVisibility(View.VISIBLE);
            //限购选项（1：每天，2：每周，3：每月，4：每季度，5：每年）
            String cycle;
            switch (data.getXianggou().getCycle()) {
                case 2:
                    cycle = "每周";
                    break;
                case 3:
                    cycle = "每月";
                    break;
                case 4:
                    cycle = "每季度";
                    break;
                case 5:
                    cycle = "每年";
                    break;
                default:
                    cycle = "每天";
                    break;
            }
            mBinding.tvQuotaTips.setText(cycle + "仅限购" + data.getXianggou().getFrequency() + "次，每次限购" + data.getXianggou().getQuotaNumber() + data.getGoodsunit_name());
        } else {
            mBinding.tvQuotaTips.setVisibility(View.GONE);
        }

        //规格参数
        mBinding.tvSpecsPrice.setText(DFUtils.getNum2(data.getOnline_price()) + "元");
        if (TextUtils.isEmpty(data.getSpec_name())) {
            mBinding.tvSpecsSpecs.setText("-");
            mBinding.tvSpecsSpecs1.setText("-");
        } else {
            mBinding.tvSpecsSpecs.setText(data.getSpec_name());
            mBinding.tvSpecsSpecs1.setText(data.getSpec_name());
        }
        if (TextUtils.isEmpty(data.getGoodsunit_name())) {
            mBinding.tvSpecsUnit.setText("-");
        } else {
            mBinding.tvSpecsUnit.setText(data.getGoodsunit_name());
        }
        if (TextUtils.isEmpty(data.getWeight())) {
            mBinding.tvSpecsWeight.setText("-");
        } else {
            mBinding.tvSpecsWeight.setText(data.getWeight() + "kg");
        }
        if (TextUtils.isEmpty(data.getProduction_date())) {
            mBinding.tvSpecsStartDate.setText("-");
        } else {
            mBinding.tvSpecsStartDate.setText(data.getProduction_date());
        }
        if (TextUtils.isEmpty(data.getQuality_period())) {
            mBinding.tvSpecsEndDate.setText("-");
        } else {
            mBinding.tvSpecsEndDate.setText(data.getQuality_period() + "天");
        }
        if (TextUtils.isEmpty(data.getBrand_name())) {
            mBinding.tvSpecsBrand.setText("-");
        } else {
            mBinding.tvSpecsBrand.setText(data.getBrand_name());
        }
        if (TextUtils.isEmpty(data.getClass_big_name())) {
            mBinding.tvSpecsCate.setText("-");
        } else {
            mBinding.tvSpecsCate.setText(data.getClass_big_name());
        }
        if (TextUtils.isEmpty(data.getClass_small_name())) {
            mBinding.tvSpecsCateChild.setText("-");
        } else {
            mBinding.tvSpecsCateChild.setText(data.getClass_small_name());
        }

        //商品简介
        if (TextUtils.isEmpty(data.getGoods_resume())) {
            mBinding.tvIntro.setVisibility(View.GONE);
        } else {
            mBinding.tvIntro.setVisibility(View.VISIBLE);
            mBinding.tvIntro.setText(data.getGoods_resume());
        }

        //webView
        if (!TextUtils.isEmpty(data.getGoods_detail()) && !"<p><br></p>".

                equals(data.getGoods_detail())) {
            mBinding.webView.setVisibility(View.VISIBLE);
            mBinding.linEmpty.setVisibility(View.GONE);
            new WebViewTask().execute();
            mBinding.webView.setWebViewClient(new WebViewClient() {
                @Override
                public boolean shouldOverrideUrlLoading(WebView view, String url) {
                    view.loadUrl(url);
                    return true;
                }
            });
            mBinding.webView.loadDataWithBaseURL(null, data.getGoods_detail(), "text/html", "UTF-8", null);
        } else {
            mBinding.webView.setVisibility(View.GONE);
            mBinding.linEmpty.setVisibility(View.VISIBLE);
        }
//        //是否 参与活动（活动无法重复参加） 0 -无 ；1-限购 ；2 捆绑 ； 5.降价活动 -（如：硬核补贴） 6、阶梯价，7、预售
//        switch (is_activity) {
//            case 1:
//                //限购
//                mBinding.linBind.setVisibility(View.GONE);
//                if (data.getXianggou() != null) {
//                    mBinding.linQuota.setVisibility(View.VISIBLE);
//                    //限购选项（1：每天，2：每周，3：每月，4：每季度，5：每年）
//                    String cycle;
//                    switch (data.getXianggou().getCycle()) {
//                        case 2:
//                            cycle = "每周";
//                            break;
//                        case 3:
//                            cycle = "每月";
//                            break;
//                        case 4:
//                            cycle = "每季度";
//                            break;
//                        case 5:
//                            cycle = "每年";
//                            break;
//                        default:
//                            cycle = "每天";
//                            break;
//                    }
//                    mBinding.tvQuota.setText(cycle + "仅限购" + data.getXianggou().getFrequency() + "次，每次限购" + data.getXianggou().getQuotaNumber() + data.getGoodsunit_name());
//                } else {
//                    mBinding.linQuota.setVisibility(View.GONE);
//                }
//                break;
//            case 2:
//                //捆绑
//                mBinding.linQuota.setVisibility(View.GONE);
//                if (data.getBindList() == null) {
//                    mBinding.linBind.setVisibility(View.GONE);
//                } else {
//                    bindList.clear();
//                    bindList.addAll(data.getBindList());
//                    if (bindList.size() > 0) {
//                        mBinding.linBind.setVisibility(View.VISIBLE);
//                        bindAdapter.setDataList(bindList);
//                    } else {
//                        mBinding.linBind.setVisibility(View.GONE);
//                    }
//                }
//                break;
//            case 5:
//                //降价活动
//                mBinding.linQuota.setVisibility(View.GONE);
//                mBinding.linBind.setVisibility(View.GONE);
//                break;
//            case 6:
//                //阶梯价
//                mBinding.linQuota.setVisibility(View.GONE);
//                mBinding.linBind.setVisibility(View.GONE);
//                break;
//            case 7:
//                //预售
//                mBinding.linQuota.setVisibility(View.GONE);
//                mBinding.linBind.setVisibility(View.GONE);
//                break;
//            default:
//                //无
//                mBinding.linQuota.setVisibility(View.GONE);
//                mBinding.linBind.setVisibility(View.GONE);
//                break;
//        }
//        //优惠券
//        couponsList.clear();
//        if (data.getCouponList() != null) {
//            couponsList.addAll(data.getCouponList());
//        }
//        couponsAdapter.setDataList(couponsList);
//
//        //满赠
//        giftList.clear();
//        if (data.getFullgift() != null) {
//            if (data.getFullgift().size() > 0) {
//                mBinding.linCoupons.setVisibility(View.VISIBLE);
//            } else {
//                mBinding.linCoupons.setVisibility(View.GONE);
//            }
//            for (int i = 0; i < data.getFullgift().size(); i++) {
//                if (data.getFullgift().get(i).getGoodList() != null) {
//                    giftList.addAll(data.getFullgift().get(i).getGoodList());
//                }
//                if (data.getFullgift().get(i).getCouponList() != null) {
//                    giftList.addAll(data.getFullgift().get(i).getCouponList());
//                }
//            }
//        } else {
//            mBinding.linCoupons.setVisibility(View.GONE);
//        }
//        if (giftList.size() > 0) {
//            mBinding.linGift.setVisibility(View.VISIBLE);
//            giftAdapter.setDataList(giftList);
//        } else {
//            mBinding.linGift.setVisibility(View.GONE);
//        }
    }

    /**
     * 商品详情
     */
    private void getGoodsInfo() {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("goods_id", goodsId);
        showDialog();
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getMallGoodsInfo(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        MallGoodsInfoData data = new Gson().fromJson(s, MallGoodsInfoData.class);
                        if (data == null) {
                            showToast(1, "商品下架或不配送到此区域");
                            new Handler().postDelayed(() -> finish(), 1500);
                            return;
                        }
                        if (data.getCord() != null) {
                            cartCount = data.getCord().getGood_count();
                        }
                        if (data.getData() == null) {
                            showToast(1, "商品下架或不配送到此区域");
                            new Handler().postDelayed(() -> finish(), 1500);
                            return;
                        }
                        if (data.getData().getGoods_id() == 0) {
                            showToast(1, "商品下架或不配送到此区域");
                            new Handler().postDelayed(() -> finish(), 1500);
                            return;
                        }
                        setUI(data.getData());
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        finish();
                    }
                });
    }

    /**
     * 立即购买
     */
    private void postOrder() {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("area_dict_num", getAreaDictNum());
        params.put("good_id", "");
        params.put("good_count", count);
        params.put("deduct_amt", "");
        params.put("company_code", "");
        params.put("price", price);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getMallCartSettlement(),
                params,
                MallOrderSettlementData.class,
                new RequestListener<MallOrderSettlementData>() {
                    @Override
                    public void success(MallOrderSettlementData data) {
                        hideDialog();
                        showToast(0, "提交成功");
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 适配视频播放器
     */
    private void initVideoPlayer() {
        mPlayer = new MyVideoPlayer(this);
        mPlayer.setMyVideoAllCallBack(() -> {
            smallVideoHelper.releaseVideoPlayer();
            GSYVideoManager.releaseAllVideos();
            mBinding.tvImgCount.setText(String.format("%d/%s", 2, imgList.size()));
        });
        //创建小窗口帮助类
        smallVideoHelper = new GSYVideoHelper(this, mPlayer);
        smallVideoHelper.getGsyVideoPlayer().setDialogProgressBar(null);
        //配置
        gsySmallVideoHelperBuilder = new GSYVideoHelper.GSYVideoHelperBuilder();
        gsySmallVideoHelperBuilder
                .setHideStatusBar(true)
                .setNeedLockFull(true)
                .setCacheWithPlay(true)
                .setIsTouchWiget(true)
                .setShowFullAnimation(false)
                .setCacheWithPlay(true)
                .setDismissControlTime(2000)
                .setRotateViewAuto(false)
                .setLockLand(true)
                .setVideoAllCallBack(new GSYSampleCallBack() {
                    @Override
                    public void onPrepared(String url, Object... objects) {
                        super.onPrepared(url, objects);
                        Debuger.printfLog("Duration " + smallVideoHelper.getGsyVideoPlayer().getDuration() + " CurrentPosition " + smallVideoHelper.getGsyVideoPlayer().getCurrentPositionWhenPlaying());
                    }

                    @Override
                    public void onQuitSmallWidget(String url, Object... objects) {
                        super.onQuitSmallWidget(url, objects);
                        //大于0说明有播放,//对应的播放列表TAG
//                        if (smallVideoHelper.getPlayPosition() >= 0 ) {
//                            //不可视的是时候
//                            if (mScrollY >1000) {
//                                //释放掉视频
//                                smallVideoHelper.releaseVideoPlayer();
//                            }
//                        }
                    }

                    @Override
                    public void onTouchScreenSeekPosition(String url, Object... objects) {
//                        smallVideoHelper.releaseVideoPlayer();
//                        GSYVideoManager.releaseAllVideos();
//                        imageCountTV.setText(String.format("%d/%s", 2, mGoodsImgs.size()));
//                        super.onTouchScreenSeekPosition(url, objects);
                    }

                    @Override
                    public void onComplete(String url, Object... objects) {
                        super.onComplete(url, objects);
                        smallVideoHelper.releaseVideoPlayer();
                        GSYVideoManager.releaseAllVideos();
                        mBinding.banner.getViewPager2().setCurrentItem(2);
//                        banner.setCurrentItem(banner.getCurrentItem()+1,false);
                        mBinding.tvImgCount.setText(String.format("%d/%s", 2, imgList.size()));
                    }

                    @Override
                    public void onAutoComplete(String url, Object... objects) {
                        super.onAutoComplete(url, objects);
                        smallVideoHelper.releaseVideoPlayer();
                        GSYVideoManager.releaseAllVideos();
                        mBinding.banner.getViewPager2().setCurrentItem(2);
                        mBinding.tvImgCount.setText(String.format("%d/%s", 2, imgList.size()));
                    }
                });
        smallVideoHelper.getGsyVideoPlayer().setShowDragProgressTextOnSeekBar(false);
        smallVideoHelper.setGsyVideoOptionBuilder(gsySmallVideoHelperBuilder);
    }

    private class WebViewTask extends AsyncTask<Void, Void, Boolean> {

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
        }

        protected Boolean doInBackground(Void... param) {
            return false;
        }

        @Override
        protected void onPostExecute(Boolean result) {
            WebSettings webSettings = mBinding.webView.getSettings();
            webSettings.setDefaultTextEncodingName("UTF-8");
            webSettings.setJavaScriptEnabled(true);
            webSettings.setBuiltInZoomControls(true);
            webSettings.setDisplayZoomControls(false);//隐藏webview缩放比例
            webSettings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NORMAL);
            webSettings.setUseWideViewPort(true);//适配手机
            webSettings.setLoadWithOverviewMode(true);//适配手机
            //    shoppingImgDetils.setWebViewClient(new MyWebViewClient());
            webSettings.setUseWideViewPort(true);// 可任意比例缩放
            webSettings.setLoadWithOverviewMode(true);// setUseWideViewPort方法设置webview推荐使用的窗口。setLoadWithOverviewMode方法是设置webview加载的页面的模式。

            webSettings.setSavePassword(true);
            webSettings.setSaveFormData(true);// 保存表单数据
            webSettings.setJavaScriptEnabled(true);

            webSettings.setDomStorageEnabled(true);
            webSettings.setSupportMultipleWindows(true);// 新加
            webSettings.setTextZoom(230);

            //这行很关键
            mBinding.webView.setWebChromeClient(new WebChromeClient());
        }
    }
}
