package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogMemberBinding;
import com.yxl.cashier_retail.ui.activity.MemberActivity;
import com.yxl.cashier_retail.ui.adapter.MemberDialogAdapter;
import com.yxl.cashier_retail.ui.bean.MemberData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.view.NumberKeyBoardView;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

/**
 * Describe:dialog（会员搜索）
 * Created by jingang on 2024/5/10
 */
@SuppressLint({"NonConstantResourceId", "NotifyDataSetChanged", "SetTextI18n", "StaticFieldLeak"})
public class MemberDialog extends BaseDialog<DialogMemberBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private MemberDialogAdapter mAdapter;
    private final List<MemberData> dataList = new ArrayList<>();
    private MemberData data;
    private int type;//0.新增会员、会员管理 1.未搜索到会员信息 2.会员列表 3.会员详情

    public static void showDialog(Activity activity, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        MemberDialog.listener = listener;
        MemberDialog dialog = new MemberDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 7);
        dialog.show();
    }

    public MemberDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        ((AnimationDrawable) mBinding.ivDialogCursor.getDrawable()).start();
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.ivDialogSearchClear.setOnClickListener(this);
        mBinding.tvDialogAdd.setOnClickListener(this);
        mBinding.tvDialogManage.setOnClickListener(this);
        mBinding.tvDialogAdd1.setOnClickListener(this);
        mBinding.numberKeyBoardView.setDrop(false);
        mBinding.numberKeyBoardView.setMaxLength(Constants.MAX_MOBILE_LENGTH);
        mBinding.numberKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var) {
                cancelTimer();
                mBinding.tvDialogSearch.setText(var);
                if (TextUtils.isEmpty(var)) {
                    mBinding.tvDialogSearch.setVisibility(View.GONE);
                    mBinding.tvDialogHint.setVisibility(View.VISIBLE);
                    mBinding.ivDialogSearchClear.setVisibility(View.GONE);
                    data = null;
                    type = 0;
                    setUI();
                } else {
                    mBinding.tvDialogSearch.setVisibility(View.VISIBLE);
                    mBinding.tvDialogHint.setVisibility(View.GONE);
                    mBinding.ivDialogSearchClear.setVisibility(View.VISIBLE);
//                    if (var.length() > 3) {
//                        startTimer();
//                    }
                    startTimer();
                }
            }

            @Override
            public void onConfirm() {
                if (data == null) {
                    showToast(1, getRstr(R.string.place_select_member));
                    return;
                }
                if (listener != null) {
                    listener.onClick(data);
                    dismiss();
                }
            }
        });
        setAdapter();
    }

    @Override
    protected DialogMemberBinding getViewBinding() {
        return DialogMemberBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void dismiss() {
        super.dismiss();
        cancelTimer();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.ivDialogSearchClear:
                //清除搜索输入
                dataList.clear();
                mAdapter.notifyDataSetChanged();
                mBinding.numberKeyBoardView.setResultStr("");
                break;
            case R.id.tvDialogAdd:
            case R.id.tvDialogAdd1:
                //新增会员
                if (isQuicklyClick()) {
                    return;
                }
                MemberAddDialog.showDialog(mActivity, () -> {
                });
                break;
            case R.id.tvDialogManage:
                //管理会员
                if (isQuicklyClick()) {
                    return;
                }
                mActivity.startActivity(new Intent(mActivity, MemberActivity.class));
                dismiss();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new MemberDialogAdapter(getContext());
        mBinding.rvDialog.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            data = dataList.get(position);
            if (listener != null) {
                listener.onClick(data);
                dismiss();
            }
        });
    }

    /**
     * 更新UI
     */
    private void setUI() {
        //0.新增会员、会员管理 1.未搜索到会员信息 2.会员列表 3.会员详情
        switch (type) {
            case 1:
                mBinding.linDialogAdd.setVisibility(View.GONE);
                mBinding.linDialogEmpty.setVisibility(View.VISIBLE);
                mBinding.rvDialog.setVisibility(View.GONE);
                mBinding.linDialogMember.setVisibility(View.GONE);
                break;
            case 2:
                mBinding.linDialogAdd.setVisibility(View.GONE);
                mBinding.linDialogEmpty.setVisibility(View.GONE);
                mBinding.rvDialog.setVisibility(View.VISIBLE);
                mBinding.linDialogMember.setVisibility(View.GONE);
                break;
            case 3:
                mBinding.linDialogAdd.setVisibility(View.GONE);
                mBinding.linDialogEmpty.setVisibility(View.GONE);
                mBinding.rvDialog.setVisibility(View.GONE);
                mBinding.linDialogMember.setVisibility(View.VISIBLE);
                break;
            default:
                mBinding.linDialogAdd.setVisibility(View.VISIBLE);
                mBinding.linDialogEmpty.setVisibility(View.GONE);
                mBinding.rvDialog.setVisibility(View.GONE);
                mBinding.linDialogMember.setVisibility(View.GONE);
                break;
        }
        setUIInfo();
    }

    /**
     * 更新UI-会员详情
     */
    private void setUIInfo() {
        if (data == null) {
            return;
        }
//        Glide.with(getContext())
//                .load(StringUtils.getStarMobile(data.getCusHeadPath()))
//                .apply(new RequestOptions().error(R.mipmap.ic_head003))
//                .into(mBinding.ivDialogMemberHead);
        mBinding.tvDialogMemberMobile.setText(data.getCusPhone());
        mBinding.tvDialogMemberName.setText(data.getCusName());
        mBinding.tvDialogMemberBalance.setText(getRstr(R.string.money) + DFUtils.getNum2(data.getTotalBalance()));
        mBinding.tvDialogMemberPoints.setText(DFUtils.getNum2(data.getCusPoints()));
        switch (data.getCusLevelVal()) {
            case 1:
                mBinding.ivDialogMemberLevel.setImageResource(R.mipmap.ic_vip0);
                break;
            case 2:
                mBinding.ivDialogMemberLevel.setImageResource(R.mipmap.ic_vip1);
                break;
            case 3:
                mBinding.ivDialogMemberLevel.setImageResource(R.mipmap.ic_vip2);
                break;
            case 4:
                mBinding.ivDialogMemberLevel.setImageResource(R.mipmap.ic_vip3);
                break;
            default:
                mBinding.ivDialogMemberLevel.setImageResource(0);
                break;
        }
    }

    private Timer timer;
    private TimerTask timerTask;

    //倒计时开始
    private void startTimer() {
        cancelTimer();
        if (timer == null) {
            timer = new Timer();
        }
        timerTask = new TimerTask() {
            @Override
            public void run() {
                getMemberList();
            }
        };
        timer.schedule(timerTask, Constants.SEARCH_DELAY_MILLIS);
    }

    //倒计时结束
    private void cancelTimer() {
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
        if (timerTask != null) {
            timerTask.cancel();
            timerTask = null;
        }
    }

    /**
     * 会员列表
     */
    private void getMemberList() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("searchParam", mBinding.tvDialogSearch.getText().toString().trim());//搜索关键字（会员卡号/手机号/姓名）
        params.put("pageNum", page);
        params.put("pageSize", 10000);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getMemberList(),
                params,
                MemberData.class,
                new RequestListListener<MemberData>() {
                    @Override
                    public void onResult(List<MemberData> list) {
                        mBinding.linDialogAdd.setVisibility(View.GONE);
                        mBinding.linDialogMember.setVisibility(View.GONE);
                        dataList.clear();
                        dataList.addAll(list);
                        mAdapter.setDataList(dataList);
                        if (dataList.isEmpty()) {
                            type = 1;
                            data = null;
                        } else {
                            data = dataList.get(0);
                            if (dataList.size() == 1) {
                                type = 3;
                            } else {
                                type = 2;
                            }
                        }
                        setUI();
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                        data = null;
                        type = 1;
                        setUI();
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onClick(MemberData data);
    }
}
