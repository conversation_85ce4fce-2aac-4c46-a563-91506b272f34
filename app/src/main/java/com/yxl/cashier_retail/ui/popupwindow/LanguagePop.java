package com.yxl.cashier_retail.ui.popupwindow;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.PopupWindow;

import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.utils.MultiLanguageUtils;

/**
 * Describe:popupWindow（资源）
 * Created by jingang on 2024/04/27
 */
@SuppressLint({"StaticFieldLeak", "NonConstantResourceId"})
public class LanguagePop extends PopupWindow implements View.OnClickListener {
    private static LanguagePop popupWindow;
    private static Context mContext;
    private static View viewIcon;

    private final Animation openAnim, closeAnim;
    private String language, area;

    public LanguagePop(Context context) {
        super(context);
        View view = View.inflate(context, R.layout.pop_language, null);
        setContentView(view);
        view.findViewById(R.id.tvPopLanguage0).setOnClickListener(this);
        view.findViewById(R.id.tvPopLanguage1).setOnClickListener(this);
        view.findViewById(R.id.tvPopLanguage2).setOnClickListener(this);
        view.findViewById(R.id.tvPopLanguage3).setOnClickListener(this);
        view.findViewById(R.id.tvPopLanguage4).setOnClickListener(this);
        view.findViewById(R.id.tvPopLanguage5).setOnClickListener(this);
        view.findViewById(R.id.tvPopLanguage6).setOnClickListener(this);

        openAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_1);
        closeAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_2);
        openAnim.setFillAfter(true);
        closeAnim.setFillAfter(true);
        language = SPUtils.getInstance().getString(Constants.LANGUAGE, "zh");
    }

    public static void showDialog(Context context, View viewIcon, View viewShow, int width, MyListener listener) {
        LanguagePop.mContext = context;
        LanguagePop.viewIcon = viewIcon;

        popupWindow = new LanguagePop(context);
        popupWindow.setListener(listener);
        popupWindow.setWidth(width);
        popupWindow.setHeight(ViewGroup.LayoutParams.WRAP_CONTENT);
        //new ColorDrawable(0)即为透明背景
        popupWindow.setBackgroundDrawable(new ColorDrawable(0));
        // 设置动画效果
//        popupWindow.setAnimationStyle(R.style.dialog_anim);
        //设置可以获取焦点
        popupWindow.setFocusable(true);//设置为true isShowing才会有值
        //设置可以触摸弹出框以外的区域
        popupWindow.setOutsideTouchable(true);
        //放在具体控件下方
        popupWindow.showAsDropDown(viewShow);
    }

    @Override
    public void showAsDropDown(View anchor) {
        super.showAsDropDown(anchor);
        viewIcon.startAnimation(openAnim);
    }

    @Override
    public void dismiss() {
        super.dismiss();
        viewIcon.startAnimation(closeAnim);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvPopLanguage0:
                //中文
                if (!language.equals("zh")) {
                    language = "zh";
                    area = "ZH";
                    setLanguage();
                }
                break;
            case R.id.tvPopLanguage1:
                //英文
                if (!language.equals("en")) {
                    language = "en";
                    area = "US";
                    setLanguage();
                }
                break;
            case R.id.tvPopLanguage2:
                //泰文
                if (!language.equals("th")) {
                    language = "th";
                    area = "TH";
                    setLanguage();
                }
                break;
            case R.id.tvPopLanguage3:
                //俄文
                if (!language.equals("ru")) {
                    language = "ru";
                    area = "RU";
                    setLanguage();
                }
                break;
            case R.id.tvPopLanguage4:
                //马来文
                if (!language.equals("ms")) {
                    language = "ms";
                    area = "MY";
                    setLanguage();
                }
                break;
            case R.id.tvPopLanguage5:
                //哈萨克文
                if (!language.equals("kk")) {
                    language = "kk";
                    area = "KZ";
                    setLanguage();
                }
                break;
            case R.id.tvPopLanguage6:
                //越南文
                if (!language.equals("vi")) {
                    language = "vi";
                    area = "VN";
                    setLanguage();
                }
                break;
        }
    }

    /**
     * 设置语言
     */
    private void setLanguage() {
        if (listener != null) {
            MultiLanguageUtils.changeLanguage(mContext, language, area);
            listener.onClick();
            dismiss();
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }


    public interface MyListener {
        void onClick();
    }
}
