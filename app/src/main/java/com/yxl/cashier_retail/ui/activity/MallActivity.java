package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.view.View;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.yxl.cashier_retail.MyApplication;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityMallBinding;
import com.yxl.cashier_retail.ui.dialog.MenuDialog;
import com.yxl.cashier_retail.ui.fragment.MallFragment;
import com.yxl.cashier_retail.ui.fragment.RestockFragment;
import com.yxl.cashier_retail.ui.fragment.SupplierFragment;
import com.yxl.commonlibrary.AppManager;

/**
 * Describe:菜单-补货
 * Created by jingang on 2024/6/3
 */
@SuppressLint("NonConstantResourceId")
public class MallActivity extends BaseActivity<ActivityMallBinding> implements View.OnClickListener {
    private Fragment[] fragments;
    private MallFragment mallFragment;
    private RestockFragment replenishmentFragment;
    private SupplierFragment supplierFragment;
    private int index = 1;//点击的页卡索引
    private int currentTabIndex = 1;//当前的页卡索引

    @Override
    protected ActivityMallBinding getViewBinding() {
        return ActivityMallBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivLogo.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.linMall.setOnClickListener(this);
        mBinding.linReplenishment.setOnClickListener(this);
        mBinding.linSupplier.setOnClickListener(this);
        setFragment();
    }

    @Override
    protected void initData() {
        setNetwork();
    }

    @Override
    public void getNetwork() {
        setNetwork();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivLogo:
                //菜单
                MenuDialog.showDialog(this, type -> {
                    //0.收银 1.入库 2.查询 3.统计 4.网单 5.会员 6.补货 7.营销 8.交班 9.设置
                    if (type != 6) {
                        switch (type) {
                            case 1:
                                goToActivity(InActivity.class);
                                break;
                            case 2:
                                goToActivity(QueryActivity.class);
                                break;
                            case 3:
                                goToActivity(StatisticsActivity.class);
                                break;
                            case 4:
                                goToActivity(OrderActivity.class);
                                break;
                            case 5:
                                goToActivity(MemberActivity.class);
                                break;
                            case 7:
                                goToActivity(MarketingActivity.class);
                                break;
                            case 8:
                                goToActivity(ShiftActivity.class);
                                break;
                            case 9:
                                goToActivity(SettingActivity.class);
                                break;
                        }
                        finish();
                    }
                });
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.linMall:
                //商城
                index = 0;
                fragmentControl();
                break;
            case R.id.linReplenishment:
                //自采补货
                index = 1;
                fragmentControl();
                break;
            case R.id.linSupplier:
                //供货商管理
                index = 2;
                fragmentControl();
                break;
        }
    }

    /**
     * 网络监听
     */
    private void setNetwork() {
        if (MyApplication.mNetwork != null) {
            if (MyApplication.mNetwork.isConnect()) {
                switch (MyApplication.mNetwork.getConnectType()) {
                    case 1:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network001);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 2:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network002);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 3:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network003);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    default:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                        mBinding.tvNetwork.setText(getRstr(R.string.no_network));
                        break;
                }
            } else {
                mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                mBinding.tvNetwork.setText(getRstr(R.string.no_network));
            }
        } else {
            mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
            mBinding.tvNetwork.setText(getRstr(R.string.no_network));
        }
    }

    /**
     * 设置fragment
     */
    public void setFragment() {
        mallFragment = new MallFragment();
        replenishmentFragment = new RestockFragment();
        supplierFragment = new SupplierFragment();
        fragments = new Fragment[]{mallFragment,
                replenishmentFragment,
                supplierFragment};
        setBottomColor();

        getSupportFragmentManager().beginTransaction().add(R.id.fragment_container, fragments[index]).show(fragments[index]).commit();
    }

    /**
     * 控制fragment的变化
     */
    public void fragmentControl() {
        if (currentTabIndex != index) {
            removeBottomColor();
            setBottomColor();

            FragmentTransaction trx = getSupportFragmentManager().beginTransaction();
            trx.hide(fragments[currentTabIndex]);
            if (!fragments[index].isAdded()) {
                trx.add(R.id.fragment_container, fragments[index]);
            }
            trx.show(fragments[index]).commitAllowingStateLoss();
            currentTabIndex = index;
        }
    }

    /**
     * 设置底部栏按钮变色
     */
    private void setBottomColor() {
        switch (index) {
            case 0:
                mBinding.linMall.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivMall.setImageResource(R.mipmap.ic_mall_tab001);
                mBinding.tvMall.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.mall_jinquan));
                break;
            case 1:
                mBinding.linReplenishment.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivReplenishment.setImageResource(R.mipmap.ic_mall_replenishment_tab001);
                mBinding.tvReplenishment.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.restock_self));
                break;
            case 2:
                mBinding.linSupplier.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivSupplier.setImageResource(R.mipmap.ic_mall_supplier_tab001);
                mBinding.tvSupplier.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.supplier_manage));
                break;
            default:
                break;
        }
    }

    /**
     * 清除底部栏颜色
     */
    private void removeBottomColor() {
        switch (currentTabIndex) {
            case 0:
                mBinding.linMall.setBackgroundResource(0);
                mBinding.ivMall.setImageResource(R.mipmap.ic_mall_tab002);
                mBinding.tvMall.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 1:
                mBinding.linReplenishment.setBackgroundResource(0);
                mBinding.ivReplenishment.setImageResource(R.mipmap.ic_mall_replenishment_tab002);
                mBinding.tvReplenishment.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 2:
                mBinding.linSupplier.setBackgroundResource(0);
                mBinding.ivSupplier.setImageResource(R.mipmap.ic_mall_supplier_tab002);
                mBinding.tvSupplier.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            default:
                break;
        }
    }
}
