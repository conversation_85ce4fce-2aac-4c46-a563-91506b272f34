package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.MallGoodsInfoData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:商城-商品详情-优惠券（适配器）
 * Created by jingang on 2024/6/3
 */
public class MallGoodsInfoCouponsAdapter extends BaseAdapter<MallGoodsInfoData.DataBean.Coupon> {

    public MallGoodsInfoCouponsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_mall_goods_info_coupons;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName = holder.getView(R.id.tvItemName);
        tvName.setText("满" + DFUtils.getNum4(mDataList.get(position).getMeet_amount()) + "减" + DFUtils.getNum4(mDataList.get(position).getCoupon_amount()));
    }
}
