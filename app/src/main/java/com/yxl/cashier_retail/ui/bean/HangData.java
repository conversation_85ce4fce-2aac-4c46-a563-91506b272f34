package com.yxl.cashier_retail.ui.bean;

import org.litepal.crud.LitePalSupport;

import java.io.Serializable;

/**
 * Describe:挂单（实体类）
 * Created by jingang on 2024/9/10
 */
public class HangData extends LitePalSupport implements Serializable {
    private boolean select;
    private int type;//0.全部 1.散客 2.会员
    private int id;
    private String timestamp;//取当前时间戳
    private String memberName;//会员名称
    private String jsonMember;//会员详情json
    private double total;//商品总金额
    private int count;//商品总种类
    private String jsonGoods;//商品详情json

    public HangData() {
    }

    public HangData(int type,String timestamp, String memberName, String jsonMember, double total, int count, String jsonGoods) {
        this.type = type;
        this.timestamp = timestamp;
        this.memberName = memberName;
        this.jsonMember = jsonMember;
        this.total = total;
        this.count = count;
        this.jsonGoods = jsonGoods;
    }

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getMemberName() {
        return memberName;
    }

    public void setMemberName(String memberName) {
        this.memberName = memberName;
    }

    public String getJsonMember() {
        return jsonMember;
    }

    public void setJsonMember(String jsonMember) {
        this.jsonMember = jsonMember;
    }

    public double getTotal() {
        return total;
    }

    public void setTotal(double total) {
        this.total = total;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getJsonGoods() {
        return jsonGoods;
    }

    public void setJsonGoods(String jsonGoods) {
        this.jsonGoods = jsonGoods;
    }
}
