package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;

/**
 * Describe:商品单位（实体类）
 * Created by jingang on 2024/7/1
 */
public class UnitData implements Serializable {
    /**
     * goods_unit_id : 单位ID
     * shop_unique : 店铺编号
     * goods_unit : 商品单位
     */
    private boolean select;
    private String goods_unit_id;
    private String shop_unique;
    private String goods_unit;

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }

    public String getGoods_unit_id() {
        return goods_unit_id;
    }

    public void setGoods_unit_id(String goods_unit_id) {
        this.goods_unit_id = goods_unit_id;
    }

    public String getShop_unique() {
        return shop_unique;
    }

    public void setShop_unique(String shop_unique) {
        this.shop_unique = shop_unique;
    }

    public String getGoods_unit() {
        return goods_unit;
    }

    public void setGoods_unit(String goods_unit) {
        this.goods_unit = goods_unit;
    }
}
