package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.SupplierData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:供货商管理-详情-替换供货商（适配器）
 * Created by jingang on 2024/6/12
 */
public class SupplierReplaceAdapter extends BaseAdapter<SupplierData> {

    public SupplierReplaceAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_supplier_replace;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvName, tvMobile, tvTotal, tvDebt, tvCount, tvConfirm;
        tvName = holder.getView(R.id.tvItemName);
        tvMobile = holder.getView(R.id.tvItemMobile);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvDebt = holder.getView(R.id.tvItemDebt);
        tvCount = holder.getView(R.id.tvItemCount);
        tvConfirm = holder.getView(R.id.tvItemConfirm);

        //1.购销 2.自采（本地）
        if (mDataList.get(position).getPurchaseType() == 2) {
            tvName.setText(mDataList.get(position).getSupplierName() + getRstr(R.string.local));
            ivImg.setVisibility(View.GONE);
        } else {
            tvName.setText(mDataList.get(position).getSupplierName());
            ivImg.setVisibility(View.VISIBLE);
        }
        tvMobile.setText(mDataList.get(position).getContacts() + " " + mDataList.get(position).getContactMobile());
        tvDebt.setText(DFUtils.getNum2(mDataList.get(position).getDebts()));
        tvCount.setText(String.valueOf(mDataList.get(position).getOrderCount()));
        if (onItemClickListener != null) {
            tvConfirm.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
    }

}
