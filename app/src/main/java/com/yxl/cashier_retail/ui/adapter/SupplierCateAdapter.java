package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.SupplierCateData;

import java.util.List;

/**
 * Describe:供货商分类（适配器）
 * Created by jingang on 2024/6/7
 */
public class SupplierCateAdapter extends BaseAdapter<SupplierCateData> {

    public SupplierCateAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_supplier_cate;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName = holder.getView(R.id.tvItemName);
        tvName.setText(mDataList.get(position).getSupplierKindName());
        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundResource(R.drawable.shape_green_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundResource(R.drawable.shape_white_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
        }
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName = holder.getView(R.id.tvItemName);
        ImageView ivEdit, ivDel;
        ivEdit = holder.getView(R.id.ivItemEdit);
        ivDel = holder.getView(R.id.ivItemDel);

        tvName.setText(mDataList.get(position).getSupplierKindName());
        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundResource(R.drawable.shape_green_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundResource(R.drawable.shape_white_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
        }
        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(v, position));
            ivEdit.setOnClickListener(v -> listener.onEditClick(v, position));
            ivDel.setOnClickListener(v -> listener.onDelClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onEditClick(View view, int position);

        void onDelClick(View view, int position);
    }
}
