package com.yxl.cashier_retail.ui.adapter;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ui.bean.ScaleData;

import java.util.List;

/**
 * Describe:设置-配件设置-称重设置—称列表（适配器）
 * Created by jingang on 2024/5/16
 */
public class ScaleAdapter extends BaseQuickAdapter<ScaleData, BaseViewHolder> {

    public ScaleAdapter(@Nullable List<ScaleData> data) {
        super(R.layout.item_scale, data);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder holder, ScaleData data) {
        TextView tvName, tvStatus;
        ImageView ivDel, ivSwitch;
        LinearLayout linIp, linStatus;
        tvName = holder.getView(R.id.tvItemName);
        ivDel = holder.getView(R.id.ivItemDel);
        ivSwitch = holder.getView(R.id.ivItemSwitch);
        linIp = holder.getView(R.id.linItemIp);
        EditText etIp = holder.getView(R.id.etItemIp);
        linStatus = holder.getView(R.id.linItemStatus);
        View vStatus = holder.getView(R.id.vItemStatus);
        tvStatus = holder.getView(R.id.tvItemStatus);

        ivSwitch.setSelected(data.isSelect());
        if (data.isSelect()) {
            linIp.setVisibility(View.VISIBLE);
            linStatus.setVisibility(View.VISIBLE);
        } else {
            linIp.setVisibility(View.GONE);
            linStatus.setVisibility(View.GONE);
        }

        TextWatcher watcher = new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
//                String str = s.toString().trim();
//                if (TextUtils.isEmpty(str)) {
//                    return;
//                }
//
//                double count = Double.parseDouble(str);
//                if (count > goodsCount) {
//                    count = goodsCount;
//                    etCount.setText(DFUtils.getNum4(count));
//                }
//                mDataList.get(position).setCount(count);
//                if (listener != null) {
//                    listener.onCount(count, position);
//                }
            }
        };
        etIp.setOnFocusChangeListener((v, hasFocus) -> {
            EditText mV = (EditText) v;
            if (hasFocus) {
                mV.addTextChangedListener(watcher);
            } else {
                mV.removeTextChangedListener(watcher);
            }
        });
        //java.lang.IllegalStateException: focus search returned a view that wasn't able to take focus!
        etIp.setOnEditorActionListener((v, actionId, event) -> {
            //可以根据需求获取下一个焦点还是上一个
            View nextView = v.focusSearch(View.FOCUS_DOWN);
            if (nextView != null) {
                nextView.requestFocus(View.FOCUS_DOWN);
            }
            //这里一定要返回true
            return true;
        });

        addChildClickViewIds(R.id.ivItemDel, R.id.ivItemSwitch);
    }
}
