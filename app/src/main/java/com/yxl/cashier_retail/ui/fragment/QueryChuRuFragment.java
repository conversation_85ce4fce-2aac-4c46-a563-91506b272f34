package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.view.View;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmQueryChuruBinding;
import com.yxl.cashier_retail.ui.adapter.QueryChuRuAdapter;
import com.yxl.cashier_retail.ui.bean.ConditionData;
import com.yxl.cashier_retail.ui.bean.QueryStockRecordData;
import com.yxl.cashier_retail.ui.dialog.DateStartEndDialog;
import com.yxl.cashier_retail.ui.popupwindow.ConditionPop;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.utils.DateUtils;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:查询-查出入库
 * Created by jingang on 2025/1/6
 */
@SuppressLint("NonConstantResourceId")
public class QueryChuRuFragment extends BaseFragment<FmQueryChuruBinding> implements View.OnClickListener {
    private String keyWords, startDate, endDate;

    //筛选条件（出入库类型）
    private List<ConditionData> conditionList = new ArrayList<>();
    private int conditionId;//0.全部类型 1.入库 1.出库

    private QueryChuRuAdapter mAdapter;
    private List<QueryStockRecordData.DataBean> dataList = new ArrayList<>();

    /**
     * 搜索
     *
     * @param keyWords
     */
    public void setKeyWords(String keyWords) {
        this.keyWords = keyWords;
        mBinding.smartRefreshLayout.autoRefresh();
    }

    @Override
    protected FmQueryChuruBinding getViewBinding() {
        return FmQueryChuruBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.linType.setOnClickListener(this);
        mBinding.linStartDate.setOnClickListener(this);
        mBinding.linEndDate.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected void initData() {
        conditionList.clear();
        conditionList.add(new ConditionData(0, getRstr(R.string.type_all), true));
        conditionList.add(new ConditionData(1, getRstr(R.string.in), false));
        conditionList.add(new ConditionData(2, getRstr(R.string.out), false));

        startDate = DateUtils.getOldDate(0);
        endDate = DateUtils.getOldDate(0);
        mBinding.tvStartDate.setText(startDate);
        mBinding.tvEndDate.setText(endDate);
        mBinding.smartRefreshLayout.autoRefresh();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.linType:
                //类型
                ConditionPop.showDialog(mContext, mBinding.ivType, v, mBinding.linType.getMeasuredWidth(), conditionList, conditionId, data -> {
                    conditionId = data.getId();
                    mBinding.tvType.setText(data.getName());
                    mBinding.smartRefreshLayout.autoRefresh();
                });
                break;
            case R.id.linStartDate:
                //开始日期
                showDialogDate(0, mBinding.ivStartDate);
                break;
            case R.id.linEndDate:
                //结束日期
                showDialogDate(1, mBinding.ivEndDate);
                break;
        }
    }

    /**
     * dialog（选择日期时间）
     */
    private void showDialogDate(int type, View view) {
        DateStartEndDialog.showDialog(getActivity(), view, startDate, endDate, type, (startDate, endDate) -> {
            this.startDate = startDate;
            this.endDate = endDate;
            mBinding.tvStartDate.setText(startDate);
            mBinding.tvEndDate.setText(endDate);
            mBinding.smartRefreshLayout.autoRefresh();
        });
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new QueryChuRuAdapter(getActivity());
        mBinding.recyclerView.setAdapter(mAdapter);
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getStockRecord();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getStockRecord();
            }
        });
    }

    /**
     * 出入库记录
     */
    private void getStockRecord() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("startTime", startDate + " 00:00");
        params.put("endTime", endDate + " 23:59");
        params.put("goodsMessage", keyWords);
        if (conditionId != 0) {
            params.put("stockType", conditionId);
        }
        params.put("pageNum", page);
        params.put("pageSize", Constants.limit);
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getStockRecord(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                        QueryStockRecordData data = new Gson().fromJson(s, QueryStockRecordData.class);
                        if (data == null) {
                            return;
                        }
                        if (data.getStatus() != Constants.SUCCESS_CODE) {
                            showToast(1, data.getMsg());
                            if (page == 1) {
                                dataList.clear();
                                mAdapter.clear();
                                mBinding.recyclerView.setVisibility(View.GONE);
                                mBinding.linEmpty.setVisibility(View.VISIBLE);
                            }
                        } else {
                            if (data.getObject() != null) {
                                mBinding.tvInTotal.setText(DFUtils.getNum2(data.getObject().getEntryTotal()));
                                mBinding.tvOutTotal.setText(DFUtils.getNum2(data.getObject().getOutTotal()));
                            }
                            if (data.getData() != null) {
                                if (page == 1) {
                                    dataList.clear();
                                }
                                dataList.addAll(data.getData());
                                if (dataList.size() > 0) {
                                    mBinding.recyclerView.setVisibility(View.VISIBLE);
                                    mBinding.linEmpty.setVisibility(View.GONE);
                                    mAdapter.setDataList(dataList);
                                } else {
                                    mBinding.recyclerView.setVisibility(View.GONE);
                                    mBinding.linEmpty.setVisibility(View.VISIBLE);
                                }
                            }
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                        if (page == 1) {
                            mBinding.smartRefreshLayout.finishRefresh();
                        } else {
                            mBinding.smartRefreshLayout.finishLoadMore();
                        }
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

}
