package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.GoodsInBatchData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:批量入库（适配器）
 * Created by jingang on 2024/5/28
 */
public class InBatchAdapter extends BaseAdapter<GoodsInBatchData> {

    public InBatchAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_in_batch;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName, tvBarcode, tvPrice, tvCount, tvTotal;
        tvName = holder.getView(R.id.tvItemName);
        tvBarcode = holder.getView(R.id.tvItemBarcode);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTotal = holder.getView(R.id.tvItemTotal);
        ImageView ivDel = holder.getView(R.id.ivItemDel);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
        }
        tvName.setText(mDataList.get(position).getGoodsName());
        tvBarcode.setText(mDataList.get(position).getGoodsBarcode());
        tvPrice.setText(DFUtils.getNum2(mDataList.get(position).getInPrice()));
        tvCount.setText(DFUtils.getNum2(mDataList.get(position).getInCount()));
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getInTotal()));
        if (listener != null) {
            ivDel.setOnClickListener(v -> listener.onDelClick(position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onDelClick(int position);
    }
}
