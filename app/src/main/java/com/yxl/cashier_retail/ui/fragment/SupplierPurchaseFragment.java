package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmSupplierPurchaseBinding;
import com.yxl.cashier_retail.ui.adapter.SupplierPurchaseAdapter;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.PurchaseListData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:供货商管理-详情-购销订单
 * Created by jingang on 2024/6/8
 */
@SuppressLint("NonConstantResourceId")
public class SupplierPurchaseFragment extends BaseFragment<FmSupplierPurchaseBinding> implements View.OnClickListener {
    private String supplierUnique;//供货商编号
    private int type;//0.全部 1.待入库 2.待结款 3.已完成

    private SupplierPurchaseAdapter mAdapter;
    private List<PurchaseListData> dataList = new ArrayList<>();

    public SupplierPurchaseFragment(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    @Override
    protected FmSupplierPurchaseBinding getViewBinding() {
        return FmSupplierPurchaseBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.tvType0.setOnClickListener(this);
        mBinding.tvType1.setOnClickListener(this);
        mBinding.tvType2.setOnClickListener(this);
        mBinding.tvType3.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected void initData() {
        getOrderList();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvType0:
                //全部
                if (type != 0) {
                    type = 0;
                    mBinding.tvType0.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvType0.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvType0.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.tvType1.setBackgroundResource(0);
                    mBinding.tvType1.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType1.setTypeface(Typeface.DEFAULT);
                    mBinding.tvType2.setBackgroundResource(0);
                    mBinding.tvType2.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType2.setTypeface(Typeface.DEFAULT);
                    mBinding.tvType3.setBackgroundResource(0);
                    mBinding.tvType3.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType3.setTypeface(Typeface.DEFAULT);
                    page = 1;
                    getOrderList();
                }
                break;
            case R.id.tvType1:
                //待入库
                if (type != 1) {
                    type = 1;
                    mBinding.tvType1.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvType1.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvType1.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.tvType0.setBackgroundResource(0);
                    mBinding.tvType0.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType0.setTypeface(Typeface.DEFAULT);
                    mBinding.tvType2.setBackgroundResource(0);
                    mBinding.tvType2.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType2.setTypeface(Typeface.DEFAULT);
                    mBinding.tvType3.setBackgroundResource(0);
                    mBinding.tvType3.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType3.setTypeface(Typeface.DEFAULT);
                    page = 1;
                    getOrderList();
                }
                break;
            case R.id.tvType2:
                //待结款
                if (type != 2) {
                    type = 2;
                    mBinding.tvType2.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvType2.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvType2.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.tvType1.setBackgroundResource(0);
                    mBinding.tvType1.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType1.setTypeface(Typeface.DEFAULT);
                    mBinding.tvType0.setBackgroundResource(0);
                    mBinding.tvType0.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType0.setTypeface(Typeface.DEFAULT);
                    mBinding.tvType3.setBackgroundResource(0);
                    mBinding.tvType3.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType3.setTypeface(Typeface.DEFAULT);
                    page = 1;
                    getOrderList();
                }
                break;
            case R.id.tvType3:
                //已完成
                if (type != 3) {
                    type = 3;
                    mBinding.tvType3.setBackgroundResource(R.drawable.shape_green_5);
                    mBinding.tvType3.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvType3.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.tvType1.setBackgroundResource(0);
                    mBinding.tvType1.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType1.setTypeface(Typeface.DEFAULT);
                    mBinding.tvType2.setBackgroundResource(0);
                    mBinding.tvType2.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType2.setTypeface(Typeface.DEFAULT);
                    mBinding.tvType0.setBackgroundResource(0);
                    mBinding.tvType0.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType0.setTypeface(Typeface.DEFAULT);
                    page = 1;
                    getOrderList();
                }
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new SupplierPurchaseAdapter(getActivity());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            EventBusManager.getInstance().getGlobaEventBus().post(new EventData(String.valueOf(dataList.get(position).getId()), Constants.PURCHASE_INFO));
        });

        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getOrderList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getOrderList();
            }
        });
    }

    /**
     * 购销单列表
     */
    private void getOrderList() {
        if (TextUtils.isEmpty(supplierUnique)) {
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("supplierUnique", supplierUnique);
        map.put("pageIndex", page);
        map.put("pageSize", Constants.limit);
        RXHttpUtil.requestByBodyPostAsResponseList(getActivity(),
                ZURL.getSupplierInfoGouXList(),
                map,
                PurchaseListData.class,
                new RequestListListener<PurchaseListData>() {
                    @Override
                    public void onResult(List<PurchaseListData> list) {
                        hideDialog();
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                        if (page == 1) {
                            dataList.clear();
                        }
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }
}
