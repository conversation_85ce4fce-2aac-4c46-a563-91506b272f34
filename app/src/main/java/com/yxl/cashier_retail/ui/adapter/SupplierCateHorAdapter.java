package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.SupplierCateData;

import java.util.List;

/**
 * Describe:供货商分类列表-横向（适配器）
 * Created by jingang on 2024/6/7
 */
public class SupplierCateHorAdapter extends BaseAdapter<SupplierCateData> {

    public SupplierCateHorAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_supplier_cate_hor;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        TextView tvName = holder.getView(R.id.tvItemName);
        if (mDataList.get(position).isSelect()) {
            tvName.setBackgroundResource(R.drawable.shape_green_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            tvName.setBackgroundResource(R.drawable.shape_white_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
        }
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName = holder.getView(R.id.tvItemName);
        tvName.setText(mDataList.get(position).getSupplierKindName());
        if (mDataList.get(position).isSelect()) {
            tvName.setBackgroundResource(R.drawable.shape_green_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            tvName.setBackgroundResource(R.drawable.shape_white_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
        }
    }
}
