package com.yxl.cashier_retail.ui.bean;

/**
 * Describe: 统计网单每个订单状态数量（实体类）
 * Created by jingang on 2025/6/6
 */
public class OrderCountData {
    /**
     * status : 0
     * msg : null
     * data : {"evaluateCount":113,"successCount":3,"receivedCount":0,"confirmCount":4,"sendCount":7,"totalCount":143,"pickupCount":16,"errorCount":0}
     * data1 : null
     * address : null
     * cus_data : null
     */

    private int status;
    private Object msg;
    private DataBean data;
    private Object data1;
    private Object address;
    private Object cus_data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Object getMsg() {
        return msg;
    }

    public void setMsg(Object msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public Object getData1() {
        return data1;
    }

    public void setData1(Object data1) {
        this.data1 = data1;
    }

    public Object getAddress() {
        return address;
    }

    public void setAddress(Object address) {
        this.address = address;
    }

    public Object getCus_data() {
        return cus_data;
    }

    public void setCus_data(Object cus_data) {
        this.cus_data = cus_data;
    }

    public static class DataBean {
        /**
         * evaluateCount : 113
         * successCount : 3
         * receivedCount : 0
         * confirmCount : 4
         * sendCount : 7
         * totalCount : 143
         * pickupCount : 16
         * errorCount : 0
         */

        private int evaluateCount;//待评价
        private int successCount;//已完成
        private int receivedCount;//待收货
        private int confirmCount;//待确认
        private int sendCount;//代发货
        private int totalCount;//全部
        private int pickupCount;//待自提
        private int errorCount;//配送异常

        /**
         * {"status":0,"msg":null,"data":{
         * "auditCount":0,
         * "refundCount":0,
         * "refuseCount":0,
         * "retTotalCount":0},
         * "data1":null,"address":null,"cus_data":null}
         * @return
         */

        private int auditCount;//待审核
        private int refundCount;//已退款
        private int refuseCount;//已拒绝
        private int retTotalCount;//全部

        public int getEvaluateCount() {
            return evaluateCount;
        }

        public void setEvaluateCount(int evaluateCount) {
            this.evaluateCount = evaluateCount;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public void setSuccessCount(int successCount) {
            this.successCount = successCount;
        }

        public int getReceivedCount() {
            return receivedCount;
        }

        public void setReceivedCount(int receivedCount) {
            this.receivedCount = receivedCount;
        }

        public int getConfirmCount() {
            return confirmCount;
        }

        public void setConfirmCount(int confirmCount) {
            this.confirmCount = confirmCount;
        }

        public int getSendCount() {
            return sendCount;
        }

        public void setSendCount(int sendCount) {
            this.sendCount = sendCount;
        }

        public int getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(int totalCount) {
            this.totalCount = totalCount;
        }

        public int getPickupCount() {
            return pickupCount;
        }

        public void setPickupCount(int pickupCount) {
            this.pickupCount = pickupCount;
        }

        public int getErrorCount() {
            return errorCount;
        }

        public void setErrorCount(int errorCount) {
            this.errorCount = errorCount;
        }

        public int getAuditCount() {
            return auditCount;
        }

        public void setAuditCount(int auditCount) {
            this.auditCount = auditCount;
        }

        public int getRefundCount() {
            return refundCount;
        }

        public void setRefundCount(int refundCount) {
            this.refundCount = refundCount;
        }

        public int getRefuseCount() {
            return refuseCount;
        }

        public void setRefuseCount(int refuseCount) {
            this.refuseCount = refuseCount;
        }

        public int getRetTotalCount() {
            return retTotalCount;
        }

        public void setRetTotalCount(int retTotalCount) {
            this.retTotalCount = retTotalCount;
        }
    }
}
