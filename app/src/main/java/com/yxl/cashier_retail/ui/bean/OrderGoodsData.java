package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;

/**
 * Describe:订单内商品列表（实体类）
 * Created by jingang on 2024/5/21
 */
public class OrderGoodsData implements Serializable {
    /**
     * 查询订单详情
     * sale_list_detail_id : 2262147
     * sale_list_unique : null
     * goods_barcode : 512019776186
     * goods_name : 雪碧听装330ml
     * goods_picturepath : /image/suppliers/GS112379213/f940189f-e2bb-4917-8011-0689728002d8.jpg
     * sale_list_detail_count : 1.0
     * sale_list_detail_price : 6.2
     * goods_purprice : 3.0
     * goods_standard :
     * sub_total : 6.2
     * retCount : 0.0
     * retPrice : 0.0
     * saleListExpressId : -1
     */
    private boolean select;
    private String sale_list_detail_id;
    private String sale_list_unique;
    private String goods_barcode;
    private String goods_name;
    private String goods_picturepath;
    private double sale_list_detail_count;//数量
    private double sale_list_detail_price;//单价
    private double goods_purprice;
    private String goods_standard;
    private double sub_total;//小计
    private double retCount;//退货数量
    private double retPrice;//退货价格
    private String saleListExpressId;

    /**
     * 销售订单详情
     * saleListDetailId : 8287
     * goodsBarcode : 6909493102299
     * goodsName : 冰冻西瓜
     * goodsPicturepath :
     * saleListDetailCount : 1.0
     * saleListDetailPrice : 0.03
     * subTotal : 0.03
     * goods_unit : null
     * goods_purprice : 20.0
     */

    private int saleListDetailId;
    private String goodsBarcode;
    private String goodsName;
    private String goodsPicturepath;
    private double saleListDetailCount;//重量
    private double saleListDetailPrice;//单价
    private double subTotal;//小计
    private String goods_unit;
    //    private double goods_purprice;
    private String goodsChengType;//散装
    private String goodsType;//自营

    /**
     * 退款订单详情
     * "retListDetailId":1473,
     * "saleListUnique":"1718183282917",
     * "goodsBarcode":"6972261601309",
     * "goodsName":"柔恣抽纸",
     * "retListDetailCount":2.0,
     * "retListDetailPrice":6.0,
     * "handleWay":1,
     * "retListOriginPrice":6.0,
     * "retListUnique":"17183275004931000",
     * "rsaleListDetailId":null,
     * "imagePath":"/image/1536215939565/697226160130931.jpg",
     * "goodsStandard":"",
     * "goodsUnit":"",
     * "saleListDetailCount":2.0
     */
    private int retListDetailId;
    private String saleListUnique;
    //    private String goodsBarcode;
//    private String goodsName;
    private double retListDetailCount;//退款商品数量
    private double retListDetailPrice;//退款商品单价
    private String handleWay;
    private double retListOriginPrice;//商品销售单价
    private String retListUnique;
    private String imagePath;
    private String goodsStandard;
    private String goodsUnit;
//    private double saleListDetailCount;//商品销售数量

    /*订单退款*/
    private double refundCount;//退货数量

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }

    public String getSale_list_detail_id() {
        return sale_list_detail_id;
    }

    public void setSale_list_detail_id(String sale_list_detail_id) {
        this.sale_list_detail_id = sale_list_detail_id;
    }

    public String getSale_list_unique() {
        return sale_list_unique;
    }

    public void setSale_list_unique(String sale_list_unique) {
        this.sale_list_unique = sale_list_unique;
    }

    public String getGoods_barcode() {
        return goods_barcode;
    }

    public void setGoods_barcode(String goods_barcode) {
        this.goods_barcode = goods_barcode;
    }

    public String getGoods_name() {
        return goods_name;
    }

    public void setGoods_name(String goods_name) {
        this.goods_name = goods_name;
    }

    public String getGoods_picturepath() {
        return goods_picturepath;
    }

    public void setGoods_picturepath(String goods_picturepath) {
        this.goods_picturepath = goods_picturepath;
    }

    public double getSale_list_detail_count() {
        return sale_list_detail_count;
    }

    public void setSale_list_detail_count(double sale_list_detail_count) {
        this.sale_list_detail_count = sale_list_detail_count;
    }

    public double getSale_list_detail_price() {
        return sale_list_detail_price;
    }

    public void setSale_list_detail_price(double sale_list_detail_price) {
        this.sale_list_detail_price = sale_list_detail_price;
    }

    public double getGoods_purprice() {
        return goods_purprice;
    }

    public void setGoods_purprice(double goods_purprice) {
        this.goods_purprice = goods_purprice;
    }

    public String getGoods_standard() {
        return goods_standard;
    }

    public void setGoods_standard(String goods_standard) {
        this.goods_standard = goods_standard;
    }

    public double getSub_total() {
        return sub_total;
    }

    public void setSub_total(double sub_total) {
        this.sub_total = sub_total;
    }

    public double getRetCount() {
        return retCount;
    }

    public void setRetCount(double retCount) {
        this.retCount = retCount;
    }

    public double getRetPrice() {
        return retPrice;
    }

    public void setRetPrice(double retPrice) {
        this.retPrice = retPrice;
    }

    public String getSaleListExpressId() {
        return saleListExpressId;
    }

    public void setSaleListExpressId(String saleListExpressId) {
        this.saleListExpressId = saleListExpressId;
    }

    public int getSaleListDetailId() {
        return saleListDetailId;
    }

    public void setSaleListDetailId(int saleListDetailId) {
        this.saleListDetailId = saleListDetailId;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsPicturepath() {
        return goodsPicturepath;
    }

    public void setGoodsPicturepath(String goodsPicturepath) {
        this.goodsPicturepath = goodsPicturepath;
    }

    public double getSaleListDetailCount() {
        return saleListDetailCount;
    }

    public void setSaleListDetailCount(double saleListDetailCount) {
        this.saleListDetailCount = saleListDetailCount;
    }

    public double getSaleListDetailPrice() {
        return saleListDetailPrice;
    }

    public void setSaleListDetailPrice(double saleListDetailPrice) {
        this.saleListDetailPrice = saleListDetailPrice;
    }

    public double getSubTotal() {
        return subTotal;
    }

    public void setSubTotal(double subTotal) {
        this.subTotal = subTotal;
    }

    public String getGoods_unit() {
        return goods_unit;
    }

    public void setGoods_unit(String goods_unit) {
        this.goods_unit = goods_unit;
    }

    public String getGoodsChengType() {
        return goodsChengType;
    }

    public void setGoodsChengType(String goodsChengType) {
        this.goodsChengType = goodsChengType;
    }

    public String getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(String goodsType) {
        this.goodsType = goodsType;
    }

    public int getRetListDetailId() {
        return retListDetailId;
    }

    public void setRetListDetailId(int retListDetailId) {
        this.retListDetailId = retListDetailId;
    }

    public String getSaleListUnique() {
        return saleListUnique;
    }

    public void setSaleListUnique(String saleListUnique) {
        this.saleListUnique = saleListUnique;
    }

    public double getRetListDetailCount() {
        return retListDetailCount;
    }

    public void setRetListDetailCount(double retListDetailCount) {
        this.retListDetailCount = retListDetailCount;
    }

    public double getRetListDetailPrice() {
        return retListDetailPrice;
    }

    public void setRetListDetailPrice(double retListDetailPrice) {
        this.retListDetailPrice = retListDetailPrice;
    }

    public String getHandleWay() {
        return handleWay;
    }

    public void setHandleWay(String handleWay) {
        this.handleWay = handleWay;
    }

    public double getRetListOriginPrice() {
        return retListOriginPrice;
    }

    public void setRetListOriginPrice(double retListOriginPrice) {
        this.retListOriginPrice = retListOriginPrice;
    }

    public String getRetListUnique() {
        return retListUnique;
    }

    public void setRetListUnique(String retListUnique) {
        this.retListUnique = retListUnique;
    }

    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    public String getGoodsStandard() {
        return goodsStandard;
    }

    public void setGoodsStandard(String goodsStandard) {
        this.goodsStandard = goodsStandard;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public double getRefundCount() {
        return refundCount;
    }

    public void setRefundCount(double refundCount) {
        this.refundCount = refundCount;
    }
}
