package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogScaleAddBinding;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:dialog（新增条码秤）
 * Created by jingang on 2024/5/16
 */
@SuppressLint("NonConstantResourceId")
public class ScaleAddDialog extends BaseDialog<DialogScaleAddBinding> implements View.OnClickListener {

    public static void showDialog(Activity activity, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        ScaleAddDialog.listener = listener;
        ScaleAddDialog dialog = new ScaleAddDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public ScaleAddDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.ivDialogClear.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        mBinding.etDialogName.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (TextUtils.isEmpty(s.toString().trim())) {
                    mBinding.ivDialogClear.setVisibility(View.GONE);
                } else {
                    mBinding.ivDialogClear.setVisibility(View.VISIBLE);
                }
            }
        });
    }

    @Override
    protected DialogScaleAddBinding getViewBinding() {
        return DialogScaleAddBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        if (isQuicklyClick()) {
            return;
        }
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.ivDialogClear:
                //清除输入
                mBinding.etDialogName.setText("");
                break;
            case R.id.tvDialogConfirm:
                //确认添加
                if (TextUtils.isEmpty(mBinding.etDialogName.getText().toString().trim())) {
                    showToast(1, getRstr(R.string.input_scale_name));
                    return;
                }
                if (listener != null) {
                    listener.onCallBack(mBinding.etDialogName.getText().toString().trim());
                    dismiss();
                }
                break;
        }
    }

    private static MyListener listener;

    public interface MyListener {
        void onCallBack(String name);
    }
}
