package com.yxl.cashier_retail.ui.fragment;

import android.util.Log;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.base.LazyBaseFragment;
import com.yxl.cashier_retail.databinding.LayoutSmartrefreshlayoutBinding;
import com.yxl.cashier_retail.ui.adapter.StatisticsPaymentAdapter;

/**
 * Describe:统计-打款统计
 * Created by jingang on 2024/6/17
 */
public class StatisticsPaymentFragment extends LazyBaseFragment<LayoutSmartrefreshlayoutBinding> {

    private StatisticsPaymentAdapter mAdapter;

    @Override
    protected LayoutSmartrefreshlayoutBinding getViewBinding() {
        return LayoutSmartrefreshlayoutBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        setAdapter();
    }

    @Override
    protected void initData() {

    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        Log.e("111111", "StatisticsPaymentFragment = " + isVisibleToUser);
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new StatisticsPaymentAdapter(getActivity());
        mBinding.recyclerView.setAdapter(mAdapter);
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                mBinding.smartRefreshLayout.finishLoadMore();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                mBinding.smartRefreshLayout.finishRefresh();
            }
        });
    }
}
