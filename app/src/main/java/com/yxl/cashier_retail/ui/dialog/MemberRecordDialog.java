package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogMemberRecordBinding;
import com.yxl.cashier_retail.ui.adapter.MemberRecordDialogAdapter;
import com.yxl.cashier_retail.ui.bean.MemberRecordData;
import com.yxl.cashier_retail.utils.DateUtils;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:dialog（会员消费记录）
 * Created by jingang on 2024/8/7
 */
@SuppressLint({"NonConstantResourceId", "StaticFieldLeak"})
public class MemberRecordDialog extends BaseDialog<DialogMemberRecordBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private static int cusId;
    private String startDate, endDate;

    private MemberRecordDialogAdapter mAdapter;
    private final List<MemberRecordData> dataList = new ArrayList<>();

    public static void showDialog(Activity activity, int cusId) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        MemberRecordDialog.cusId = cusId;
        MemberRecordDialog dialog = new MemberRecordDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 3 * 2, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 7);
        dialog.show();
    }

    public MemberRecordDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);

        startDate = DateUtils.getOldDate(0);
        endDate = startDate;
        mBinding.tvDialogStartDate.setText(startDate);
        mBinding.tvDialogEndDate.setText(endDate);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.linDialogStartDate.setOnClickListener(this);
        mBinding.linDialogEndDate.setOnClickListener(this);
        setAdapter();
        getMemberRecord();
    }

    @Override
    protected DialogMemberRecordBinding getViewBinding() {
        return DialogMemberRecordBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }


    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.linDialogStartDate:
                //开始日期
                showDialogDate(0, mBinding.ivDialogStartDate);
                break;
            case R.id.linDialogEndDate:
                //结束日期
                showDialogDate(1, mBinding.ivDialogEndDate);
                break;
        }
    }

    /**
     * dialog（选择日期时间）
     */
    private void showDialogDate(int type, View view) {
        DateStartEndDialog.showDialog(mActivity,
                view,
                startDate,
                endDate,
                type,
                (startDate, endDate) -> {
                    this.startDate = startDate;
                    this.endDate = endDate;
                    mBinding.tvDialogStartDate.setText(startDate);
                    mBinding.tvDialogEndDate.setText(endDate);
                    //请求数据
                    page = 1;
                    getMemberRecord();
                });
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new MemberRecordDialogAdapter(getContext());
        mBinding.vSmartrefreshlayout.recyclerView.setAdapter(mAdapter);
        mBinding.vSmartrefreshlayout.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getMemberRecord();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page++;
                getMemberRecord();
            }
        });
    }

    /**
     * 会员消费记录
     */
    private void getMemberRecord() {
        Map<String, Object> params = new HashMap<>();
        params.put("cusId", cusId);//
        params.put("cusType", 3);//1.充值 2.提现 3.消费 4.积分清零
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("pageNum", page);
        params.put("pageSize", Constants.limit);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getMemberRecord(),
                params,
                MemberRecordData.class,
                new RequestListListener<MemberRecordData>() {
                    @Override
                    public void onResult(List<MemberRecordData> list) {
                        hideDialog();
                        mBinding.vSmartrefreshlayout.smartRefreshLayout.finishRefresh();
                        mBinding.vSmartrefreshlayout.smartRefreshLayout.finishLoadMore();
                        if (page == 1) {
                            dataList.clear();
                        }
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.GONE);
                            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.vSmartrefreshlayout.smartRefreshLayout.finishRefresh();
                        mBinding.vSmartrefreshlayout.smartRefreshLayout.finishLoadMore();
                        if (dataList.size() > 0) {
                            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.GONE);
                            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }
}
