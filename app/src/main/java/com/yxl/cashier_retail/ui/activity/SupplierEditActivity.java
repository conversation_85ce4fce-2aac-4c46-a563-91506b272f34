package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;

import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivitySupplierEditBinding;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.SupplierData;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import java.util.HashMap;
import java.util.Map;

/**
 * Describe:供货商管理-新增/编辑
 * Created by jingang on 2024/6/7
 */
@SuppressLint("NonConstantResourceId")
public class SupplierEditActivity extends BaseActivity<ActivitySupplierEditBinding> implements View.OnClickListener {
    private SupplierData data;
    private String id, name, contacts, contactMobile, address, cateUnique;
    private int isEnable = 2;

    @Override
    protected ActivitySupplierEditBinding getViewBinding() {
        return ActivitySupplierEditBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivBack.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.tvCate.setOnClickListener(this);
        mBinding.ivEnable.setOnClickListener(this);
        mBinding.tvConfirm.setOnClickListener(this);
    }

    @Override
    protected void initData() {
        data = (SupplierData) getIntent().getSerializableExtra("data");
        if (data == null) {
            mBinding.tvTitle.setText(getRstr(R.string.supplier_add));
        } else {
            mBinding.tvTitle.setText(getRstr(R.string.supplier_edit));
            setUI();
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.tvCate:
                //选择分类
                startActivityForResult(new Intent(this, SupplierCateActivity.class)
                                .putExtra("type", 1)
                        , Constants.SELECT);
                break;
            case R.id.ivEnable:
                //启用状态
                if (isEnable == 1) {
                    isEnable = 2;
                    mBinding.ivEnable.setSelected(false);
                } else {
                    isEnable = 1;
                    mBinding.ivEnable.setSelected(true);
                }
                break;
            case R.id.tvConfirm:
                //保存
                name = mBinding.etName.getText().toString().trim();
                contacts = mBinding.etContact.getText().toString().trim();
                contactMobile = mBinding.etMobile.getText().toString().trim();
                address = mBinding.etAddress.getText().toString().trim();
                if (TextUtils.isEmpty(name)) {
                    showToast(1, getRstr(R.string.input_supplier_name));
                    return;
                }
                if (TextUtils.isEmpty(contacts)) {
                    showToast(1, getRstr(R.string.input_contacts));
                    return;
                }
                if (TextUtils.isEmpty(contactMobile)) {
                    showToast(1, getRstr(R.string.input_contact_moblie));
                    return;
                }
                if (TextUtils.isEmpty(cateUnique)) {
                    showToast(1, getRstr(R.string.select_cate));
                    return;
                }
                postSupplierEdit();
                break;
        }
    }

    /**
     * 更新UI
     */
    private void setUI() {
        id = String.valueOf(data.getId());
        mBinding.etName.setText(data.getSupplierName());
        mBinding.etContact.setText(data.getContacts());
        mBinding.etMobile.setText(data.getContactMobile());
        mBinding.etAddress.setText(data.getAddress());
        cateUnique = data.getSupplierKindUnique();
        mBinding.tvCate.setText(data.getSupplierKindName());
        isEnable = data.getEnableStatus();
        mBinding.ivEnable.setSelected(isEnable == 1);
    }

    /**
     * 供货商新增、编辑
     */
    private void postSupplierEdit() {
        showDialog();
        String url;
        Map<String, Object> map = new HashMap<>();
        if (TextUtils.isEmpty(id)) {
            url = ZURL.getSupplierAdd();
        } else {
            url = ZURL.getSupplierEdit();
            map.put("id", id);
        }
        map.put("shopUnique", getShopUnique());
        map.put("createId", getStaffUnique());
        map.put("createBy", getStaffUnique());
        map.put("supplierName", name);
        map.put("contacts", contacts);
        map.put("contactMobile", contactMobile);
        map.put("address", address);
        map.put("supplierKindUnique", cateUnique);
        map.put("enableStatus", isEnable);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                url,
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.SUPPLIER_LIST));
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null && requestCode == Constants.SELECT) {
            cateUnique = data.getStringExtra("unique");
            mBinding.tvCate.setText(data.getStringExtra("name"));
        }
    }
}
