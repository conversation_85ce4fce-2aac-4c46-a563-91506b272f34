package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogPaymentMemberBinding;
import com.yxl.cashier_retail.ui.bean.MemberData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.view.NumberKeyBoardView;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:dialog（储值卡付款）
 * Created by jingang on 2024/6/1
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n", "NotifyDataSetChanged"})
public class PaymentMemberDialog extends BaseDialog<DialogPaymentMemberBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private static MemberData data;//会员信息
    private static double total;//应收金额
    private int type;//0.金圈收款 1.现金
    private double storeMoney,//储值卡金额
            jinqMoney,//金圈收款金额
            cashMoney;//现金金额

    public static void showDialog(Activity activity, MemberData data, double total, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        PaymentMemberDialog.listener = listener;
        PaymentMemberDialog.data = data;
        PaymentMemberDialog.total = total;
        PaymentMemberDialog dialog = new PaymentMemberDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public PaymentMemberDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        ((AnimationDrawable) mBinding.ivDialogCursorJinQ.getDrawable()).start();
        ((AnimationDrawable) mBinding.ivDialogCursorCash.getDrawable()).start();
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.linDialogMemberBalance.setOnClickListener(this);
        mBinding.linDialogJinQ.setOnClickListener(this);
        mBinding.linDialogCash.setOnClickListener(this);
        mBinding.numberKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var) {
                if (type == 0) {
                    //金圈收款
                    mBinding.tvDialogJinQ.setText(var);
                    if (TextUtils.isEmpty(var)) {
                        mBinding.tvDialogJinQ.setVisibility(View.GONE);
                        mBinding.tvDialogJinQHint.setVisibility(View.VISIBLE);
                        jinqMoney = 0;
                    } else {
                        mBinding.tvDialogJinQ.setVisibility(View.VISIBLE);
                        mBinding.tvDialogJinQHint.setVisibility(View.GONE);
                        double money = Double.parseDouble(var);
                        if (money > DFUtils.getDouble(total - storeMoney)) {
                            jinqMoney = DFUtils.getDouble(total - storeMoney);
                            mBinding.tvDialogJinQ.setText(DFUtils.getNum4(jinqMoney));
                            mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(jinqMoney));
                        } else {
                            jinqMoney = money;
                        }
                    }
                    //计算现金收款金额
                    cashMoney = DFUtils.getDouble(total - storeMoney - jinqMoney);
                    mBinding.tvDialogCash.setText(DFUtils.getNum2(cashMoney));
                    if (cashMoney > 0) {
                        mBinding.tvDialogCash.setVisibility(View.VISIBLE);
                        mBinding.tvDialogCashHint.setVisibility(View.GONE);
                    } else {
                        mBinding.tvDialogCash.setVisibility(View.GONE);
                        mBinding.tvDialogCashHint.setVisibility(View.VISIBLE);
                    }
                } else {
                    //现金收款
                    mBinding.tvDialogCash.setText(var);
                    if (TextUtils.isEmpty(var)) {
                        mBinding.tvDialogCash.setVisibility(View.GONE);
                        mBinding.tvDialogCashHint.setVisibility(View.VISIBLE);
                        cashMoney = 0;
                    } else {
                        mBinding.tvDialogCash.setVisibility(View.VISIBLE);
                        mBinding.tvDialogCashHint.setVisibility(View.GONE);
                        double money = Double.parseDouble(var);
                        if (money > (DFUtils.getDouble(total - storeMoney))) {
                            cashMoney = DFUtils.getDouble(total - storeMoney);
                            mBinding.tvDialogCash.setText(DFUtils.getNum4(cashMoney));
                            mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(cashMoney));
                        } else {
                            cashMoney = money;
                        }
                    }
                    //计算金圈收款金额
                    jinqMoney = DFUtils.getDouble(total - storeMoney - cashMoney);
                    mBinding.tvDialogJinQ.setVisibility(View.VISIBLE);
                    mBinding.tvDialogJinQHint.setVisibility(View.GONE);
                    mBinding.tvDialogJinQ.setText(DFUtils.getNum2(jinqMoney));
                    if (jinqMoney > 0) {
                        mBinding.tvDialogJinQ.setVisibility(View.VISIBLE);
                        mBinding.tvDialogJinQHint.setVisibility(View.GONE);
                    } else {
                        mBinding.tvDialogJinQ.setVisibility(View.GONE);
                        mBinding.tvDialogJinQHint.setVisibility(View.VISIBLE);
                    }
                }
            }

            @Override
            public void onConfirm() {
                Log.e(tag, "total = " + total + " store = " + storeMoney + " jinq = " + jinqMoney + " cash = " + cashMoney);
                if (listener == null) {
                    return;
                }
                if (total == 0 || total == storeMoney) {
                    //储值卡收款
                    listener.onPaymentStore(total);
                    dismiss();
                    return;
                }
                if (total == jinqMoney) {
                    //金圈收款-跳转
                    showDialogPaymentJinq(0);
                    return;
                }
                if (total == cashMoney) {
                    //现金收款
                    listener.onPaymentCash(total);
                    dismiss();
                    return;
                }
                if (jinqMoney > 0) {
                    //组合收款-含有金圈收款-跳转
                    showDialogPaymentJinq(1);
                    return;
                }
                //组合收款-不含金圈收款
                listener.onPaymentCombination(total, storeMoney, 0, cashMoney);
                dismiss();
            }
        });
        mBinding.tvDialogTotal.setText(getRstr(R.string.money) + DFUtils.getNum2(total));
        setUI();
    }

    @Override
    protected DialogPaymentMemberBinding getViewBinding() {
        return DialogPaymentMemberBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.linDialogMemberBalance:
                //充值
                MemberRechargeDialog.showDialog(mActivity, data.getCusUnique(), 0, money -> {
                    mBinding.tvDialogMemberBalance.setText(getRstr(R.string.money) + DFUtils.getNum2(data.getTotalBalance() + money));
                    if (listener != null) {
                        listener.onMemberRecharge(money);
                    }
                });
                break;
            case R.id.linDialogJinQ:
                //金圈收银
                if (type != 0) {
                    type = 0;
                    mBinding.linDialogJinQ.setBackgroundResource(R.drawable.shape_green_kuang);
                    mBinding.linDialogCash.setBackgroundResource(R.drawable.shape_d8_kuang);
                    mBinding.ivDialogCursorJinQ.setVisibility(View.VISIBLE);
                    mBinding.ivDialogCursorCash.setVisibility(View.GONE);
                    mBinding.numberKeyBoardView.setResultStr("");
                }
                break;
            case R.id.linDialogCash:
                //现金
                if (type != 1) {
                    type = 1;
                    mBinding.linDialogJinQ.setBackgroundResource(R.drawable.shape_d8_kuang);
                    mBinding.linDialogCash.setBackgroundResource(R.drawable.shape_green_kuang_right_5);
                    mBinding.ivDialogCursorJinQ.setVisibility(View.GONE);
                    mBinding.ivDialogCursorCash.setVisibility(View.VISIBLE);
                    mBinding.numberKeyBoardView.setResultStr("");
                }
                break;
        }
    }

    /**
     * 更新UI
     */
    private void setUI() {
        if (data == null) {
            return;
        }
        mBinding.ivDialogHead.setImageResource(data.getCusSex() == 2 ? R.mipmap.ic_head004 : R.mipmap.ic_head003);
        mBinding.tvDialogMemberMobile.setText(data.getCusPhone());
        mBinding.tvDialogMemberName.setText(data.getCusName());
        mBinding.tvDialogMemberBalance.setText(getRstr(R.string.money) + DFUtils.getNum2(data.getTotalBalance()));
        mBinding.tvDialogMemberPoints.setText(DFUtils.getNum2(data.getCusPoints()));

        double balance = data.getTotalBalance();
        storeMoney = Math.min(balance, total);
        mBinding.tvDialogStore.setText(DFUtils.getNum2(storeMoney));
        if (storeMoney > total) {
            //金圈收款金额不可编辑
            mBinding.linDialogJinQ.setClickable(false);
            mBinding.linDialogJinQ.setEnabled(false);
            mBinding.linDialogJinQ.setBackgroundResource(R.drawable.shape_d8_kuang_f0);
            mBinding.ivDialogCursorJinQ.setVisibility(View.GONE);
            //现金收款金额不可编辑
            mBinding.linDialogCash.setClickable(false);
            mBinding.linDialogCash.setEnabled(false);
            mBinding.linDialogCash.setBackgroundResource(R.drawable.shape_d8_kuang_right_f0_5);
        }

        jinqMoney = DFUtils.getDouble(total - storeMoney);//计算金圈收款金额
        mBinding.tvDialogJinQ.setText(DFUtils.getNum2(jinqMoney));
        if (jinqMoney > 0) {
            mBinding.tvDialogJinQ.setVisibility(View.VISIBLE);
            mBinding.tvDialogJinQHint.setVisibility(View.GONE);
        } else {
            mBinding.tvDialogJinQ.setVisibility(View.GONE);
            mBinding.tvDialogJinQHint.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 金圈收款
     *
     * @param type /0.金圈收款 1.组合收款(包含金圈收款)
     */
    private void showDialogPaymentJinq(int type) {
        PaymentJinQuanDialog.showDialog(mActivity, type, jinqMoney, data.getCusUnique(), (type1, money, saleListUnique) -> {
            if (listener == null) {
                return;
            }
            if (type1 == 0) {
                listener.onPaymentJinq(total, saleListUnique);
                dismiss();
            } else {
                jinqMoney = money;
                listener.onPaymentCombination(total, storeMoney, jinqMoney, cashMoney);
                dismiss();
            }
        });
    }

    private static MyListener listener;

    public interface MyListener {
        /**
         * 储值卡收款
         *
         * @param total
         */
        void onPaymentStore(double total);

        /**
         * 金圈收款
         *
         * @param total
         * @param saleListUnique
         */
        void onPaymentJinq(double total, String saleListUnique);

        /**
         * 现金收款
         *
         * @param total
         */
        void onPaymentCash(double total);

        /**
         * 组合收款
         *
         * @param total      总金额
         * @param storeTotal 储值卡金额
         * @param jinqTotal  金圈金额
         * @param cashTotal  现金金额
         */
        void onPaymentCombination(double total, double storeTotal, double jinqTotal, double cashTotal);

        /**
         * 会员充值
         *
         * @param money
         */
        void onMemberRecharge(double money);
    }
}
