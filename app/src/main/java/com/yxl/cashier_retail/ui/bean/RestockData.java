package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:补货计划（实体类）
 * Created by jingang on 2024/6/5
 */
public class RestockData implements Serializable {
    /**
     * shopRestockplanId : 6
     * shopUnique : 1536215939565
     * shopRestockplanName : 20230901110629
     * status : 1
     * createUser : 111
     * createTime : 1693537626000
     * goodsCount : 1
     * goodsList : [{"shopRestockplanGoodsId":7,"goodsPicturepath":"/spider/30b4a950-c72a-425c-85f9-cb6abdfc775c.jpg","goodsName":"猫王抽纸","goodsCount":40,"goodsTotal":"285.59999999999997","goodsBarcode":"6923083028089"}]
     */
    private boolean select;
    private int shopRestockplanId;
    private long shopUnique;
    private String shopRestockplanName;
    private int status;//补货计划状态:1待生成2已生成3已取消
    private String createUser;
    private String createTime;
    private int goodsCount;
    private List<GoodsListBean> goodsList;

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }

    public int getShopRestockplanId() {
        return shopRestockplanId;
    }

    public void setShopRestockplanId(int shopRestockplanId) {
        this.shopRestockplanId = shopRestockplanId;
    }

    public long getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(long shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getShopRestockplanName() {
        return shopRestockplanName;
    }

    public void setShopRestockplanName(String shopRestockplanName) {
        this.shopRestockplanName = shopRestockplanName;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public int getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(int goodsCount) {
        this.goodsCount = goodsCount;
    }

    public List<GoodsListBean> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<GoodsListBean> goodsList) {
        this.goodsList = goodsList;
    }

    public static class GoodsListBean {
        /**
         * shopRestockplanGoodsId : 7
         * goodsPicturepath : /spider/30b4a950-c72a-425c-85f9-cb6abdfc775c.jpg
         * goodsName : 猫王抽纸
         * goodsCount : 40
         * goodsTotal : 285.59999999999997
         * goodsBarcode : 6923083028089
         */

        private int shopRestockplanGoodsId;
        private String goodsPicturepath;
        private String goodsName;
        private double goodsCount;
        private double goodsTotal;
        private String goodsBarcode;
        private String goodsUnit;
        private double goodsInPrice;//单价

        public int getShopRestockplanGoodsId() {
            return shopRestockplanGoodsId;
        }

        public void setShopRestockplanGoodsId(int shopRestockplanGoodsId) {
            this.shopRestockplanGoodsId = shopRestockplanGoodsId;
        }

        public String getGoodsPicturepath() {
            return goodsPicturepath;
        }

        public void setGoodsPicturepath(String goodsPicturepath) {
            this.goodsPicturepath = goodsPicturepath;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public double getGoodsCount() {
            return goodsCount;
        }

        public void setGoodsCount(double goodsCount) {
            this.goodsCount = goodsCount;
        }

        public double getGoodsTotal() {
            return goodsTotal;
        }

        public void setGoodsTotal(double goodsTotal) {
            this.goodsTotal = goodsTotal;
        }

        public String getGoodsBarcode() {
            return goodsBarcode;
        }

        public void setGoodsBarcode(String goodsBarcode) {
            this.goodsBarcode = goodsBarcode;
        }

        public String getGoodsUnit() {
            return goodsUnit;
        }

        public void setGoodsUnit(String goodsUnit) {
            this.goodsUnit = goodsUnit;
        }

        public double getGoodsInPrice() {
            return goodsInPrice;
        }

        public void setGoodsInPrice(double goodsInPrice) {
            this.goodsInPrice = goodsInPrice;
        }
    }
}
