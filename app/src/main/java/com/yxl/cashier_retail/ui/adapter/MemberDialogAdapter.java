package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.MemberData;

/**
 * Describe:搜索会员弹窗-会员列表（适配器）
 * Created by jingang on 2024/5/11
 */
public class MemberDialogAdapter extends BaseAdapter<MemberData> {

    public MemberDialogAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_member_dialog;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvNo, tvName, tvMobile;
        tvNo = holder.getView(R.id.tvItemNo);
        tvName = holder.getView(R.id.tvItemName);
        tvMobile = holder.getView(R.id.tvItemMobile);

        tvNo.setText(mDataList.get(position).getCusUnique());
        tvName.setText(mDataList.get(position).getCusName());
        tvMobile.setText(mDataList.get(position).getCusPhone());
    }
}