package com.yxl.cashier_retail.ui.contract;


import com.yxl.commonlibrary.base.BasePresenter;
import com.yxl.commonlibrary.base.BaseView;

/**
 * 忘记密码
 */
public class ForgetContract {
    public interface View extends BaseView {
        /**
         * 获取验证码成功
         */
        void successCode(String msg);

        /**
         * 忘记密码修改成功
         */
        void successForget(String msg);
    }

    public interface Presenter extends BasePresenter<View> {
        /**
         * 获取验证码
         *
         * @param account
         */
        void getCode(String account);

        /**
         * 修改密码
         *
         * @param account
         * @param code
         * @param pwd
         */
        void postPwdUpdate(String account, String code, String pwd);
    }
}
