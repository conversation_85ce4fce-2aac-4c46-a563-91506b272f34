package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogPaymentSuccessBinding;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:dialog（收款成功）
 * Created by jingang on 2024/09/07
 */
@SuppressLint("SetTextI18n")
public class PaymentSuccessDialog extends BaseDialog<DialogPaymentSuccessBinding> {
    private static double total;//收款金额

    public static void showDialog(Activity activity, double total, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        PaymentSuccessDialog.listener = listener;
        PaymentSuccessDialog.total = total;
        PaymentSuccessDialog dialog = new PaymentSuccessDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 10 * 3, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 8);
        dialog.show();
    }

    public PaymentSuccessDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.tvDialogConfirm.setOnClickListener(v -> {
            if (listener != null) {
                listener.onConfirm();
                dismiss();
            }
        });
        mBinding.tvDialogTotal.setText(getRstr(R.string.money) + DFUtils.getNum2(total));
        if (TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_PRINT, ""))) {
            mBinding.tvDialogConfirm.setVisibility(View.GONE);
        } else {
            mBinding.tvDialogConfirm.setVisibility(View.VISIBLE);
        }
        new Handler().postDelayed(this::dismiss, 5000);
    }

    @Override
    protected DialogPaymentSuccessBinding getViewBinding() {
        return DialogPaymentSuccessBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm();
    }
}
