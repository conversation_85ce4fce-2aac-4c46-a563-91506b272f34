package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:商品详情（实体类）
 * Created by jingang on 2024/5/23
 */
public class GoodsInfoData implements Serializable {
    /**
     * goodsId : 1542549482
     * goodsCount : 1.0
     * baseBarcode : 1626677469134
     * goodsPicturepath : /image/suppliers/GS371306159/a2f04d5a-0650-4d87-a360-750c917e39b6.jpg
     * groupsName : 烟酒饮料
     * groupsUnique : 1100000
     * kindName : 烟草
     * kindUnique : 1100001
     * goodsUnit :
     * goodsBrand :
     * goodsAddress :
     * goodsRemarks :
     * goodsSold : 0.0
     * supplierUnique :
     * supplierName :
     * goodsPrice : 0.0
     * supGoodsBarcode :
     * listDetail : [{"goodsId":1542549482,"goodsBarcode":"1626677469134","goodsName":"在","goodsStandard":"","goodsUnit":"","goodsInPrice":0.03,"goodsSalePrice":0,"goodsWebSalePrice":0,"foreign_key":"1626677469134","containCount":1,"goodsContainCount":null,"goodsPromotion":1,"goodsDiscount":1,"goodsPicturepath":"/image/suppliers/GS371306159/a2f04d5a-0650-4d87-a360-750c917e39b6.jpg","goodsCusPrice":0,"shelfState":2,"countPresent":0,"pgoodsName":null,"pgoodsCount":null,"pgoodsUnit":null,"stock_warning_status":0,"out_stock_waring_count":0,"unsalable_count":0,"tableType":null}]
     * foreignKey : 1626677469134
     * tableType : 0
     * stockTableType : null
     * goodsChengType : 1
     */

    private int goodsId;
    private double goodsCount;//库存
    private String baseBarcode;//条码
    private String goodsPicturepath;//图片
    private String groupsName;//一级分类名称
    private String groupsUnique;//一级分类编号
    private String kindName;//二级分类名称
    private String kindUnique;//二级分类编号
    private String goodsUnit;//单位
    private String goodsBrand;//品牌
    private String goodsAddress;//产地
    private String goodsRemarks;//备注
    private double goodsSold;//销量
    private String supplierUnique;//供货商编号
    private String supplierName;//供货商名称
    private double goodsPrice;//售价
    private String supGoodsBarcode;//默认采购商品编号
    private String foreignKey;//商品包装外键
    private int tableType;//1.云库商品 2.本店商品
    private int goodsChengType;//计价类型 0.按件 1.称重
    private String goodsProd;//生产日期
    private int goodsLife;//保质期（天）
    private List<GoodsListData> listDetail;

    public int getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(int goodsId) {
        this.goodsId = goodsId;
    }

    public double getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(double goodsCount) {
        this.goodsCount = goodsCount;
    }

    public String getBaseBarcode() {
        return baseBarcode;
    }

    public void setBaseBarcode(String baseBarcode) {
        this.baseBarcode = baseBarcode;
    }

    public String getGoodsPicturepath() {
        return goodsPicturepath;
    }

    public void setGoodsPicturepath(String goodsPicturepath) {
        this.goodsPicturepath = goodsPicturepath;
    }

    public String getGroupsName() {
        return groupsName;
    }

    public void setGroupsName(String groupsName) {
        this.groupsName = groupsName;
    }

    public String getGroupsUnique() {
        return groupsUnique;
    }

    public void setGroupsUnique(String groupsUnique) {
        this.groupsUnique = groupsUnique;
    }

    public String getKindName() {
        return kindName;
    }

    public void setKindName(String kindName) {
        this.kindName = kindName;
    }

    public String getKindUnique() {
        return kindUnique;
    }

    public void setKindUnique(String kindUnique) {
        this.kindUnique = kindUnique;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public String getGoodsBrand() {
        return goodsBrand;
    }

    public void setGoodsBrand(String goodsBrand) {
        this.goodsBrand = goodsBrand;
    }

    public String getGoodsAddress() {
        return goodsAddress;
    }

    public void setGoodsAddress(String goodsAddress) {
        this.goodsAddress = goodsAddress;
    }

    public String getGoodsRemarks() {
        return goodsRemarks;
    }

    public void setGoodsRemarks(String goodsRemarks) {
        this.goodsRemarks = goodsRemarks;
    }

    public double getGoodsSold() {
        return goodsSold;
    }

    public void setGoodsSold(double goodsSold) {
        this.goodsSold = goodsSold;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public double getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(double goodsPrice) {
        this.goodsPrice = goodsPrice;
    }

    public String getSupGoodsBarcode() {
        return supGoodsBarcode;
    }

    public void setSupGoodsBarcode(String supGoodsBarcode) {
        this.supGoodsBarcode = supGoodsBarcode;
    }

    public String getForeignKey() {
        return foreignKey;
    }

    public void setForeignKey(String foreignKey) {
        this.foreignKey = foreignKey;
    }

    public int getGoodsChengType() {
        return goodsChengType;
    }

    public void setGoodsChengType(int goodsChengType) {
        this.goodsChengType = goodsChengType;
    }

    public int getTableType() {
        return tableType;
    }

    public void setTableType(int tableType) {
        this.tableType = tableType;
    }

    public String getGoodsProd() {
        return goodsProd;
    }

    public void setGoodsProd(String goodsProd) {
        this.goodsProd = goodsProd;
    }

    public int getGoodsLife() {
        return goodsLife;
    }

    public void setGoodsLife(int goodsLife) {
        this.goodsLife = goodsLife;
    }

    public List<GoodsListData> getListDetail() {
        return listDetail;
    }

    public void setListDetail(List<GoodsListData> listDetail) {
        this.listDetail = listDetail;
    }
}
