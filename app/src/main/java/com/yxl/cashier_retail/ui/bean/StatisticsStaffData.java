package com.yxl.cashier_retail.ui.bean;

/**
 * Describe: 统计-员工统计（实体类）
 * Created by jingang on 2025/2/21
 */
public class StatisticsStaffData {
    /**
     * staffId : 14612
     * staffName : 东方购物商家
     * saleListActuallyReceived : 1.0
     * secretFree : 0
     * cash : 1.0
     * turnover : 1.0
     * discountAmount : 0.0
     * returnAmount : 0
     * saleListCount : 1
     * returnSaleListCount : 0
     */

    private int staffId;
    private String staffName;
    private double saleListActuallyReceived;//实际营收
    private int secretFree;//免密实收
    private double cash;//现金
    private double turnover;//营业额
    private double discountAmount;//优惠金额
    private int returnAmount;//退款金额
    private int saleListCount;//订单数量
    private int returnSaleListCount;//退款数量

    public int getStaffId() {
        return staffId;
    }

    public void setStaffId(int staffId) {
        this.staffId = staffId;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public double getSaleListActuallyReceived() {
        return saleListActuallyReceived;
    }

    public void setSaleListActuallyReceived(double saleListActuallyReceived) {
        this.saleListActuallyReceived = saleListActuallyReceived;
    }

    public int getSecretFree() {
        return secretFree;
    }

    public void setSecretFree(int secretFree) {
        this.secretFree = secretFree;
    }

    public double getCash() {
        return cash;
    }

    public void setCash(double cash) {
        this.cash = cash;
    }

    public double getTurnover() {
        return turnover;
    }

    public void setTurnover(double turnover) {
        this.turnover = turnover;
    }

    public double getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(double discountAmount) {
        this.discountAmount = discountAmount;
    }

    public int getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(int returnAmount) {
        this.returnAmount = returnAmount;
    }

    public int getSaleListCount() {
        return saleListCount;
    }

    public void setSaleListCount(int saleListCount) {
        this.saleListCount = saleListCount;
    }

    public int getReturnSaleListCount() {
        return returnSaleListCount;
    }

    public void setReturnSaleListCount(int returnSaleListCount) {
        this.returnSaleListCount = returnSaleListCount;
    }
}
