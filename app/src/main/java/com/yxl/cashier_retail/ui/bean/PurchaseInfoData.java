package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:购销单详情（实体类）
 * Created by jingang on 2023/9/13
 */
public class PurchaseInfoData implements Serializable {
    /**
     * id : 2
     * status : 1
     * orderInfo : {"billNo":"B202309091735581220000","supplierUnique":"d60054eac842485aa3fa31a4dcfc57f3","supplierName":"影响力","supplierPhone":"18167733244","supplierAddress":"重庆四平市花溪区","contacts":"张三"}
     * batchInfo : {"goodsCategory":2,"purchaseCost":null,"amountPayable":121.5,"settlePref":0,"settledAmount":0,"outstandingAmount":121.5}
     * paymentInfo : {"paymentId":2,"voucherPicturepath":["111","112"],"paymentMoney":4,"remark":"1111"}
     * goodsList : [{"goodsBarcode":"2221","goodsPicturepath":"https://www.kwokok7.cn/Food","purchaseGoodsCount":null,"detailId":3,"goodsName":"Raspberry mini","tradeGoodsCount":6,"purchasePrice":1.5,"salePrice":2,"totalPrice":9,"billUnit":"把","restockUnit":"把","goodsStatus":0,"numberStatus":null,"retailPriceNow":5,"netPriceNow":2,"memberPriceNow":2,"retailPrice":2,"netPrice":2,"memberPrice":2,"receiptsReceivedCount":null},{"goodsBarcode":"8960757619486","goodsPicturepath":"https://auth.ikeda10.xyz/ClothingShoesandJewelry","purchaseGoodsCount":null,"detailId":4,"goodsName":"Raspberry elite","tradeGoodsCount":9,"purchasePrice":12.5,"salePrice":0,"totalPrice":112.5,"billUnit":"瓶","restockUnit":null,"goodsStatus":0,"numberStatus":0,"retailPriceNow":null,"netPriceNow":null,"memberPriceNow":null,"retailPrice":null,"netPrice":null,"memberPrice":null,"receiptsReceivedCount":0}]
     */

    private int id;
    private int status;
    private String remark;
    private OrderInfoBean orderInfo;
    private BatchInfoBean batchInfo;
    private PaymentInfoBean paymentInfo;
    private List<String> voucherPicturepath;
    private List<GoodsListBean> goodsList;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public OrderInfoBean getOrderInfo() {
        return orderInfo;
    }

    public void setOrderInfo(OrderInfoBean orderInfo) {
        this.orderInfo = orderInfo;
    }

    public BatchInfoBean getBatchInfo() {
        return batchInfo;
    }

    public void setBatchInfo(BatchInfoBean batchInfo) {
        this.batchInfo = batchInfo;
    }

    public PaymentInfoBean getPaymentInfo() {
        return paymentInfo;
    }

    public void setPaymentInfo(PaymentInfoBean paymentInfo) {
        this.paymentInfo = paymentInfo;
    }

    public List<String> getVoucherPicturepath() {
        return voucherPicturepath;
    }

    public void setVoucherPicturepath(List<String> voucherPicturepath) {
        this.voucherPicturepath = voucherPicturepath;
    }

    public List<GoodsListBean> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<GoodsListBean> goodsList) {
        this.goodsList = goodsList;
    }

    public static class OrderInfoBean {
        /**
         * billNo : B202309091735581220000
         * supplierUnique : d60054eac842485aa3fa31a4dcfc57f3
         * supplierName : 影响力
         * supplierPhone : 18167733244
         * supplierAddress : 重庆四平市花溪区
         * contacts : 张三
         */

        private String billNo;
        private String supplierUnique;
        private String supplierName;
        private String supplierPhone;
        private String supplierAddress;
        private String contacts;
        private String createTime;

        public String getBillNo() {
            return billNo;
        }

        public void setBillNo(String billNo) {
            this.billNo = billNo;
        }

        public String getSupplierUnique() {
            return supplierUnique;
        }

        public void setSupplierUnique(String supplierUnique) {
            this.supplierUnique = supplierUnique;
        }

        public String getSupplierName() {
            return supplierName;
        }

        public void setSupplierName(String supplierName) {
            this.supplierName = supplierName;
        }

        public String getSupplierPhone() {
            return supplierPhone;
        }

        public void setSupplierPhone(String supplierPhone) {
            this.supplierPhone = supplierPhone;
        }

        public String getSupplierAddress() {
            return supplierAddress;
        }

        public void setSupplierAddress(String supplierAddress) {
            this.supplierAddress = supplierAddress;
        }

        public String getContacts() {
            return contacts;
        }

        public void setContacts(String contacts) {
            this.contacts = contacts;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }
    }

    public static class BatchInfoBean {
        /**
         * goodsCategory : 2
         * purchaseCost : null
         * amountPayable : 121.5
         * settlePref : 0.0
         * settledAmount : 0.0
         * outstandingAmount : 121.5
         */

        private int goodsCategory;
        private double purchaseCost;//采购金额
        private double amountPayable;//应结金额
        private double settlePref;//优惠金额
        private double settledAmount;//已结金额
        private double outstandingAmount;//待结金额

        public int getGoodsCategory() {
            return goodsCategory;
        }

        public void setGoodsCategory(int goodsCategory) {
            this.goodsCategory = goodsCategory;
        }

        public double getPurchaseCost() {
            return purchaseCost;
        }

        public void setPurchaseCost(double purchaseCost) {
            this.purchaseCost = purchaseCost;
        }

        public double getAmountPayable() {
            return amountPayable;
        }

        public void setAmountPayable(double amountPayable) {
            this.amountPayable = amountPayable;
        }

        public double getSettlePref() {
            return settlePref;
        }

        public void setSettlePref(double settlePref) {
            this.settlePref = settlePref;
        }

        public double getSettledAmount() {
            return settledAmount;
        }

        public void setSettledAmount(double settledAmount) {
            this.settledAmount = settledAmount;
        }

        public double getOutstandingAmount() {
            return outstandingAmount;
        }

        public void setOutstandingAmount(double outstandingAmount) {
            this.outstandingAmount = outstandingAmount;
        }
    }

    public static class PaymentInfoBean {
        /**
         * paymentId : 2
         * voucherPicturepath : ["111","112"]
         * paymentMoney : 4.0
         * remark : 1111
         */

        private int paymentId;
        private double paymentMoney;
        private String paymentRemark;

        public int getPaymentId() {
            return paymentId;
        }

        public void setPaymentId(int paymentId) {
            this.paymentId = paymentId;
        }

        public double getPaymentMoney() {
            return paymentMoney;
        }

        public void setPaymentMoney(double paymentMoney) {
            this.paymentMoney = paymentMoney;
        }

        public String getPaymentRemark() {
            return paymentRemark;
        }

        public void setPaymentRemark(String paymentRemark) {
            this.paymentRemark = paymentRemark;
        }

    }

    public static class GoodsListBean {
        /**
         * goodsBarcode : 2221
         * goodsPicturepath : https://www.kwokok7.cn/Food
         * purchaseGoodsCount : null
         * detailId : 3
         * goodsName : Raspberry mini
         * tradeGoodsCount : 6.0
         * purchasePrice : 1.5
         * salePrice : 2.0
         * totalPrice : 9.0
         * billUnit : 把
         * restockUnit : 把
         * goodsStatus : 0
         * numberStatus : null
         * retailPriceNow : 5.0
         * netPriceNow : 2.0
         * memberPriceNow : 2.0
         * retailPrice : 2.0
         * netPrice : 2.0
         * memberPrice : 2.0
         * receiptsReceivedCount : null
         */

        private String goodsBarcode;
        private String goodsPicturepath;
        private double purchaseGoodsCount;//采购数量
        private int detailId;
        private String goodsName;
        private double tradeGoodsCount;//配送数量
        private double purchasePrice;//采购单价
        private double salePrice;//建议售价
        private double totalPrice;//配送商品总金额
        private String billUnit;//配送单位
        private String restockUnit;//采购商品单位
        private int goodsStatus;//是否核对 0.否 1.是
        private int numberStatus;//是否缺货 0.正常 1.缺货 2.超出
        private double retailPriceNow;//当前零售价
        private double netPriceNow;//当前网单价
        private double memberPriceNow;//当前会员价
        private double retailPrice;//零售价
        private double netPrice;//网单价
        private double memberPrice;//会员价
        private double receiptsReceivedCount;//实际入库数量

        public String getGoodsBarcode() {
            return goodsBarcode;
        }

        public void setGoodsBarcode(String goodsBarcode) {
            this.goodsBarcode = goodsBarcode;
        }

        public String getGoodsPicturepath() {
            return goodsPicturepath;
        }

        public void setGoodsPicturepath(String goodsPicturepath) {
            this.goodsPicturepath = goodsPicturepath;
        }

        public double getPurchaseGoodsCount() {
            return purchaseGoodsCount;
        }

        public void setPurchaseGoodsCount(double purchaseGoodsCount) {
            this.purchaseGoodsCount = purchaseGoodsCount;
        }

        public int getDetailId() {
            return detailId;
        }

        public void setDetailId(int detailId) {
            this.detailId = detailId;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public double getTradeGoodsCount() {
            return tradeGoodsCount;
        }

        public void setTradeGoodsCount(double tradeGoodsCount) {
            this.tradeGoodsCount = tradeGoodsCount;
        }

        public double getPurchasePrice() {
            return purchasePrice;
        }

        public void setPurchasePrice(double purchasePrice) {
            this.purchasePrice = purchasePrice;
        }

        public double getSalePrice() {
            return salePrice;
        }

        public void setSalePrice(double salePrice) {
            this.salePrice = salePrice;
        }

        public double getTotalPrice() {
            return totalPrice;
        }

        public void setTotalPrice(double totalPrice) {
            this.totalPrice = totalPrice;
        }

        public String getBillUnit() {
            return billUnit;
        }

        public void setBillUnit(String billUnit) {
            this.billUnit = billUnit;
        }

        public String getRestockUnit() {
            return restockUnit;
        }

        public void setRestockUnit(String restockUnit) {
            this.restockUnit = restockUnit;
        }

        public int getGoodsStatus() {
            return goodsStatus;
        }

        public void setGoodsStatus(int goodsStatus) {
            this.goodsStatus = goodsStatus;
        }

        public int getNumberStatus() {
            return numberStatus;
        }

        public void setNumberStatus(int numberStatus) {
            this.numberStatus = numberStatus;
        }

        public double getRetailPriceNow() {
            return retailPriceNow;
        }

        public void setRetailPriceNow(double retailPriceNow) {
            this.retailPriceNow = retailPriceNow;
        }

        public double getNetPriceNow() {
            return netPriceNow;
        }

        public void setNetPriceNow(double netPriceNow) {
            this.netPriceNow = netPriceNow;
        }

        public double getMemberPriceNow() {
            return memberPriceNow;
        }

        public void setMemberPriceNow(double memberPriceNow) {
            this.memberPriceNow = memberPriceNow;
        }

        public double getRetailPrice() {
            return retailPrice;
        }

        public void setRetailPrice(double retailPrice) {
            this.retailPrice = retailPrice;
        }

        public double getNetPrice() {
            return netPrice;
        }

        public void setNetPrice(double netPrice) {
            this.netPrice = netPrice;
        }

        public double getMemberPrice() {
            return memberPrice;
        }

        public void setMemberPrice(double memberPrice) {
            this.memberPrice = memberPrice;
        }

        public double getReceiptsReceivedCount() {
            return receiptsReceivedCount;
        }

        public void setReceiptsReceivedCount(double receiptsReceivedCount) {
            this.receiptsReceivedCount = receiptsReceivedCount;
        }
    }
}
