package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.MallCartData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.utils.StringUtils;

/**
 * Describe:商城-购物车-商品列表（适配器）
 * Created by jingang on 2024/6/4
 */
public class MallCartGoodsAdapter extends BaseAdapter<MallCartData.GoodListBean> {

    public MallCartGoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_mall_cart_goods;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivSelect, ivImg, ivSub, ivAdd;
        TextView tvName, tvPrice, tvCount, tvTotal, tvStartOrder, tvGift;
        LinearLayout linBind, linGift;
        RecyclerView rvBind, rvGift;
        ivSelect = holder.getView(R.id.ivItemSelect);
        ivImg = holder.getView(R.id.ivItemImg);
        tvName = holder.getView(R.id.tvItemName);
        tvPrice = holder.getView(R.id.tvItemPrice);
        ivSub = holder.getView(R.id.ivItemSub);
        tvCount = holder.getView(R.id.tvItemCount);
        ivAdd = holder.getView(R.id.ivItemAdd);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvStartOrder = holder.getView(R.id.tvItemStartOrder);
        linBind = holder.getView(R.id.linItemBind);
        rvBind = holder.getView(R.id.rvItemBind);
        linGift = holder.getView(R.id.linItemGift);
        tvGift = holder.getView(R.id.tvItemGift);
        rvGift = holder.getView(R.id.rvItemGift);

        ivSelect.setSelected(mDataList.get(position).isSelect());
        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoods_img()))
                .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img))
                .into(ivImg);
        tvName.setText(mDataList.get(position).getGoods_name());
        //促销
        int count = mDataList.get(position).getGood_count();
        double price;
        if (mDataList.get(position).getPromotion_price() != -1) {
            if (count <= mDataList.get(position).getPromotion_count()) {
                price = mDataList.get(position).getPromotion_price();
            } else {
                price = mDataList.get(position).getOnline_price();
            }
        } else {
            price = mDataList.get(position).getOnline_price();
        }
        tvPrice.setText(getRstr(R.string.money) + DFUtils.getNum2(price));
        tvCount.setText(String.valueOf(count));
        tvTotal.setText(getRstr(R.string.money) + DFUtils.getNum2(count * price));
        tvStartOrder.setText(String.valueOf(mDataList.get(position).getStart_order()));

        //捆绑
        MallCartBindAdapter bindAdapter = new MallCartBindAdapter(mContext);
        rvBind.setAdapter(bindAdapter);

        //满赠
        MallCartGiftAdapter giftAdapter = new MallCartGiftAdapter(mContext);
        rvGift.setAdapter(giftAdapter);

        if (listener != null) {
            ivSelect.setOnClickListener(v -> listener.onSelectClick(v, position));
            ivSub.setOnClickListener(v -> listener.onSubClick(v, position));
            ivAdd.setOnClickListener(v -> listener.onAddClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onSelectClick(View view, int positionChild);

        void onSubClick(View view, int positionChild);

        void onAddClick(View view, int positionChild);
    }
}
