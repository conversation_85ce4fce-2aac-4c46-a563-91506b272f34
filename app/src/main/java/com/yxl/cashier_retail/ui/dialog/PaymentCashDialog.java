package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.Paint;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogPaymentCashBinding;
import com.yxl.cashier_retail.ui.bean.MemberData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.view.NumberKeyBoardView;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:dialog（收款-现金、组合）
 * Created by jingang on 2024/5/31
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n", "StaticFieldLeak"})
public class PaymentCashDialog extends BaseDialog<DialogPaymentCashBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private int payType,//0.现金收款 1.组合收款
            zeroType,//抹零类型 0.不抹零 1.抹零到1元 2.抹零到5角 3.抹零到1角
            saveType,//存零类型 0.不存零 1.全部存零 2.存零到1元（取小数点后）
            type = -1;//-1.未选择 0.现金 1.微信 2.支付宝 3.银行卡 4.储值卡
    private static double totalOld,//原价
            total,//应收
            money,//收款
            zero,//找零
            zeroMoney,//抹零优惠
            saveMoney,//存零金额
            cashMoney,//现金
            wechatMoney,//微信
            alipayMoney,//支付宝
            bankMoney,//银行卡
            memberMoney,//储值卡
            balance;//会员余额

    private static MemberData data;//会员信息

    public static void showDialog(Activity activity, MemberData data, double total, double totalOld, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        PaymentCashDialog.listener = listener;
        PaymentCashDialog.data = data;
        PaymentCashDialog.total = total;
        PaymentCashDialog.totalOld = totalOld;
        PaymentCashDialog dialog = new PaymentCashDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 7);
        dialog.show();
    }

    public PaymentCashDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.tvCash.setOnClickListener(this);
        mBinding.tvCombination.setOnClickListener(this);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.tvDialogZero0.setOnClickListener(this);
        mBinding.tvDialogZero1.setOnClickListener(this);
        mBinding.tvDialogZero2.setOnClickListener(this);
        mBinding.tvDialogStore0.setOnClickListener(this);
        mBinding.tvDialogStore1.setOnClickListener(this);

        ((AnimationDrawable) mBinding.ivDialogCursorCash.getDrawable()).start();
        ((AnimationDrawable) mBinding.ivDialogCursorWechat.getDrawable()).start();
        ((AnimationDrawable) mBinding.ivDialogCursorAlipay.getDrawable()).start();
        ((AnimationDrawable) mBinding.ivDialogCursorBank.getDrawable()).start();
        ((AnimationDrawable) mBinding.ivDialogCursorMember.getDrawable()).start();
        mBinding.linDialogCash.setOnClickListener(this);
        mBinding.linDialogWechat.setOnClickListener(this);
        mBinding.linDialogAlipay.setOnClickListener(this);
        mBinding.linDialogBank.setOnClickListener(this);
        mBinding.linDialogMember.setOnClickListener(this);

        mBinding.numberKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var) {
                if (payType == 0) {
                    //现金收款
                    mBinding.tvDialogMoney.setText(getRstr(R.string.money) + var);
                    if (TextUtils.isEmpty(var)) {
                        money = 0;
                    } else {
                        money = Double.parseDouble(var);
                    }
                    if (money > DFUtils.getZero(total, zeroType)) {
                        zero = DFUtils.getDouble(money - DFUtils.getZero(total, zeroType));
                        zeroMoney = DFUtils.getDouble(total - DFUtils.getZero(total, zeroType));
                    } else {
                        zero = 0;
                        zeroMoney = 0;
                    }
                    setUIPrice();
                    mBinding.tvDialogZero.setText(getRstr(R.string.money) + DFUtils.getNum2(zero));
                    mBinding.tvDialogDiscount.setText(getRstr(R.string.money) + DFUtils.getNum2(zeroMoney));
                } else {
                    //组合收款
                    switch (type) {
                        case 0:
                            if (TextUtils.isEmpty(var)) {
                                mBinding.tvDialogCash.setText(DFUtils.getNum2(cashMoney));
                            } else {
                                mBinding.tvDialogCash.setVisibility(View.VISIBLE);
                                mBinding.tvDialogCashHint.setVisibility(View.GONE);
                                double money = Double.parseDouble(var);
                                if (money > DFUtils.getDouble(total - wechatMoney - alipayMoney - bankMoney - memberMoney)) {
                                    cashMoney = DFUtils.getDouble(total - wechatMoney - alipayMoney - bankMoney - memberMoney);
                                    mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(cashMoney));
                                } else {
                                    cashMoney = money;
                                }
                                mBinding.tvDialogCash.setText(DFUtils.getNum4(cashMoney));
                            }
                            break;
                        case 1:
                            if (TextUtils.isEmpty(var)) {
                                mBinding.tvDialogWechat.setText(DFUtils.getNum2(wechatMoney));
                            } else {
                                mBinding.tvDialogWechat.setVisibility(View.VISIBLE);
                                mBinding.tvDialogWechatHint.setVisibility(View.GONE);
                                double money = Double.parseDouble(var);
                                if (money > DFUtils.getDouble(total - cashMoney - alipayMoney - bankMoney - memberMoney)) {
                                    wechatMoney = DFUtils.getDouble(total - cashMoney - alipayMoney - bankMoney - memberMoney);
                                    mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(wechatMoney));
                                } else {
                                    wechatMoney = money;
                                }
                                mBinding.tvDialogWechat.setText(DFUtils.getNum4(wechatMoney));
                            }
                            break;
                        case 2:
                            if (TextUtils.isEmpty(var)) {
                                mBinding.tvDialogAlipay.setText(DFUtils.getNum2(alipayMoney));
                            } else {
                                mBinding.tvDialogAlipay.setVisibility(View.VISIBLE);
                                mBinding.tvDialogAlipayHint.setVisibility(View.GONE);
                                double money = Double.parseDouble(var);
                                if (money > DFUtils.getDouble(total - wechatMoney - cashMoney - bankMoney - memberMoney)) {
                                    alipayMoney = DFUtils.getDouble(total - wechatMoney - cashMoney - bankMoney - memberMoney);
                                    mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(alipayMoney));
                                } else {
                                    alipayMoney = money;
                                }
                                mBinding.tvDialogAlipay.setText(DFUtils.getNum4(alipayMoney));
                            }
                            break;
                        case 3:
                            if (TextUtils.isEmpty(var)) {
                                mBinding.tvDialogBank.setText(DFUtils.getNum2(bankMoney));
                            } else {
                                mBinding.tvDialogBank.setVisibility(View.VISIBLE);
                                mBinding.tvDialogBankHint.setVisibility(View.GONE);
                                double money = Double.parseDouble(var);
                                if (money > DFUtils.getDouble(total - wechatMoney - cashMoney - alipayMoney - memberMoney)) {
                                    bankMoney = DFUtils.getDouble(total - wechatMoney - cashMoney - alipayMoney - memberMoney);
                                    mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(bankMoney));
                                } else {
                                    bankMoney = money;
                                }
                                mBinding.tvDialogBank.setText(DFUtils.getNum4(bankMoney));
                            }
                            break;
                        case 4:
                            //储值卡
                            if (TextUtils.isEmpty(var)) {
                                if (balance < memberMoney) {
                                    memberMoney = balance;
                                }
                                mBinding.tvDialogMember.setText(DFUtils.getNum2(memberMoney));
                            } else {
                                mBinding.tvDialogMember.setVisibility(View.VISIBLE);
                                mBinding.tvDialogMemberHint.setVisibility(View.GONE);
                                double money = Double.parseDouble(var);
                                if (money > balance) {
                                    money = balance;
                                }
                                if (money > DFUtils.getDouble(total - wechatMoney - cashMoney - alipayMoney - bankMoney)) {
                                    memberMoney = DFUtils.getDouble(total - wechatMoney - cashMoney - alipayMoney - bankMoney);
                                    mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(memberMoney));
                                } else {
                                    memberMoney = money;
                                }
                                mBinding.tvDialogMember.setText(DFUtils.getNum4(memberMoney));
                            }
                            break;
                        default:
                            break;
                    }
                }
            }

            @Override
            public void onConfirm() {
                if (listener == null) {
                    return;
                }
                saveMoney = 0;
                if (payType == 0) {
                    //现金收款
                    switch (saveType) {
                        case 1:
                            saveMoney = zero;
                            break;
                        case 2:
                            saveMoney = DFUtils.getZero(zero, 1);
                            break;
                        default:
                            saveMoney = 0;
                            break;
                    }
                    listener.onCashClick(money, saveMoney);
                    dismiss();
                } else {
                    //组合收款
                    Log.e(tag, "total = " + total + " cash = " + cashMoney +
                            "\nwechat = " + wechatMoney + " alipay = " + alipayMoney +
                            "\nbank = " + bankMoney + " member = " + memberMoney);
                    if (DFUtils.getDouble(cashMoney + wechatMoney + alipayMoney + bankMoney + memberMoney) != total) {
                        showToast(1, getRstr(R.string.input_money));
                        return;
                    }
                    listener.onCombinationClick(total, cashMoney, wechatMoney, alipayMoney, bankMoney, memberMoney);
                    dismiss();
                }
            }
        });
        mBinding.numberKeyBoardView.setDefaultMoney(true);
        setUI();
        cashMoney = 0;
        wechatMoney = 0;
        alipayMoney = 0;
        bankMoney = 0;
        memberMoney = 0;
        setUICombination();
    }

    @Override
    protected DialogPaymentCashBinding getViewBinding() {
        return DialogPaymentCashBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvCash:
                //现金收款
                if (payType != 0) {
                    payType = 0;
                    mBinding.tvCash.setBackgroundResource(R.drawable.shape_green_22);
                    mBinding.tvCash.setTextColor(getContext().getResources().getColor(R.color.white));
                    mBinding.tvCombination.setBackgroundResource(0);
                    mBinding.tvCombination.setTextColor(getContext().getResources().getColor(R.color.black));
                    mBinding.linCash.setVisibility(View.VISIBLE);
                    mBinding.linCombination.setVisibility(View.GONE);
                    mBinding.numberKeyBoardView.setDefaultMoney(true);
                }
                break;
            case R.id.tvCombination:
                //组合收款
                if (payType != 1) {
                    payType = 1;
                    mBinding.tvCombination.setBackgroundResource(R.drawable.shape_green_22);
                    mBinding.tvCombination.setTextColor(getContext().getResources().getColor(R.color.white));
                    mBinding.tvCash.setBackgroundResource(0);
                    mBinding.tvCash.setTextColor(getContext().getResources().getColor(R.color.black));
                    mBinding.linCash.setVisibility(View.GONE);
                    mBinding.linCombination.setVisibility(View.VISIBLE);
                    mBinding.numberKeyBoardView.setDefaultMoney(false);
                }
                break;
            case R.id.tvDialogZero0:
                //抹零到1元
                if (zeroType == 1) {
                    zeroType = 0;
                } else {
                    zeroType = 1;
                }
                setUIZero();
                money = DFUtils.getZero(total, zeroType);
                setUIPrice();
                zero = 0;
                zeroMoney = DFUtils.getDouble(total - money);
                mBinding.tvDialogMoney.setText(getRstr(R.string.money) + DFUtils.getNum2(money));
                mBinding.tvDialogZero.setText(getRstr(R.string.money) + DFUtils.getNum2(zero));
                mBinding.tvDialogDiscount.setText(getRstr(R.string.money) + DFUtils.getNum2(zeroMoney));
                break;
            case R.id.tvDialogZero1:
                //抹零到5角
                if (zeroType == 2) {
                    zeroType = 0;
                } else {
                    zeroType = 2;
                }
                setUIZero();
                money = DFUtils.getZero(total, zeroType);
                setUIPrice();
                zero = 0;
                zeroMoney = DFUtils.getDouble(total - money);
                mBinding.tvDialogMoney.setText(getRstr(R.string.money) + DFUtils.getNum2(money));
                mBinding.tvDialogZero.setText(getRstr(R.string.money) + DFUtils.getNum2(zero));
                mBinding.tvDialogDiscount.setText(getRstr(R.string.money) + DFUtils.getNum2(zeroMoney));
                break;
            case R.id.tvDialogZero2:
                //抹零到1角
                if (zeroType == 3) {
                    zeroType = 0;
                } else {
                    zeroType = 3;
                }
                setUIZero();
                money = DFUtils.getZero(total, zeroType);
                setUIPrice();
                zero = 0;
                zeroMoney = DFUtils.getDouble(total - money);
                mBinding.tvDialogMoney.setText(getRstr(R.string.money) + DFUtils.getNum2(money));
                mBinding.tvDialogZero.setText(getRstr(R.string.money) + DFUtils.getNum2(zero));
                mBinding.tvDialogDiscount.setText(getRstr(R.string.money) + DFUtils.getNum2(zeroMoney));
                break;
            case R.id.tvDialogStore0:
                //全部存零
                if (saveType == 1) {
                    saveType = 0;
                    mBinding.tvDialogStore0.setBackgroundResource(R.drawable.shape_green_5);
                } else {
                    saveType = 1;
                    mBinding.tvDialogStore0.setBackgroundResource(R.drawable.shape_green_tm_5);
                }
                mBinding.tvDialogStore1.setBackgroundResource(R.drawable.shape_green_5);
                break;
            case R.id.tvDialogStore1:
                //存零到1元
                if (saveType == 2) {
                    saveType = 0;
                    mBinding.tvDialogStore1.setBackgroundResource(R.drawable.shape_green_5);
                } else {
                    saveType = 2;
                    mBinding.tvDialogStore1.setBackgroundResource(R.drawable.shape_green_tm_5);
                }
                mBinding.tvDialogStore0.setBackgroundResource(R.drawable.shape_green_5);
                break;
            case R.id.linDialogCash:
                //现金
                if (type != 0) {
                    type = 0;
                    setTextBg();
                    cashMoney = DFUtils.getDouble(total - wechatMoney - alipayMoney - bankMoney - memberMoney);
                    mBinding.tvDialogCash.setVisibility(View.VISIBLE);
                    mBinding.tvDialogCashHint.setVisibility(View.GONE);
                    mBinding.numberKeyBoardView.setResultStr("");
                }
                break;
            case R.id.linDialogWechat:
                //微信
                if (type != 1) {
                    type = 1;
                    setTextBg();
                    wechatMoney = DFUtils.getDouble(total - cashMoney - alipayMoney - bankMoney - memberMoney);
                    mBinding.tvDialogWechat.setVisibility(View.VISIBLE);
                    mBinding.tvDialogWechatHint.setVisibility(View.GONE);
                    mBinding.numberKeyBoardView.setResultStr("");
                }
                break;
            case R.id.linDialogAlipay:
                //支付宝
                if (type != 2) {
                    type = 2;
                    setTextBg();
                    alipayMoney = DFUtils.getDouble(total - wechatMoney - cashMoney - bankMoney - memberMoney);
                    mBinding.tvDialogAlipay.setVisibility(View.VISIBLE);
                    mBinding.tvDialogAlipayHint.setVisibility(View.GONE);
                    mBinding.numberKeyBoardView.setResultStr("");
                }
                break;
            case R.id.linDialogBank:
                //银行卡
                if (type != 3) {
                    type = 3;
                    setTextBg();
                    bankMoney = DFUtils.getDouble(total - wechatMoney - cashMoney - alipayMoney - memberMoney);
                    mBinding.tvDialogBank.setVisibility(View.VISIBLE);
                    mBinding.tvDialogBankHint.setVisibility(View.GONE);
                    mBinding.numberKeyBoardView.setResultStr("");
                }
                break;
            case R.id.linDialogMember:
                //储值卡
                if (data == null) {
                    //选择会员
                    MemberDialog.showDialog(mActivity, data -> {
                        PaymentCashDialog.data = data;
                        setUICombination();
                    });
                } else {
                    if (type != 4) {
                        type = 4;
                        setTextBg();
                        memberMoney = DFUtils.getDouble(total - wechatMoney - cashMoney - alipayMoney - bankMoney);
                        mBinding.tvDialogBank.setVisibility(View.VISIBLE);
                        mBinding.tvDialogBankHint.setVisibility(View.GONE);
                        mBinding.numberKeyBoardView.setResultStr("");
                    }
                }
                break;
        }
    }

    /**
     * 更新UI
     */
    private void setUI() {
        mBinding.tvDialogTotalOld.setText(getRstr(R.string.money) + DFUtils.getNum2(totalOld));
        mBinding.tvDialogTotalOld.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG);
        zeroType = getZero();
        setUIZero();
        money = DFUtils.getZero(total, zeroType);
        setUIPrice();
        mBinding.tvDialogMoney.setText(getRstr(R.string.money) + DFUtils.getNum2(money));
        mBinding.tvDialogZero.setText(getRstr(R.string.money) + "0.00");
        mBinding.tvDialogDiscount.setText(getRstr(R.string.money) + "0.00");
        if (data == null) {
            mBinding.tvDialogStore0.setVisibility(View.GONE);
            mBinding.tvDialogStore1.setVisibility(View.GONE);
        } else {
            mBinding.tvDialogStore0.setVisibility(View.VISIBLE);
            mBinding.tvDialogStore1.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 更新UI-组合收款
     */
    private void setUICombination() {
        if (data == null) {
            mBinding.linDialogMember.setBackgroundResource(R.drawable.shape_d8_kuang_f0_5);
            mBinding.ivDialogMemberHead.setImageResource(R.mipmap.ic_payment_img014);
            mBinding.tvDialogMemberName.setText(getRstr(R.string.stored_card));
            mBinding.tvDialogMemberSelect.setVisibility(View.VISIBLE);
            mBinding.tvDialogMemberHint.setVisibility(View.GONE);
        } else {
            mBinding.linDialogMember.setBackgroundResource(R.drawable.shape_d8_kuang_5);
//            Glide.with(mActivity)
//                    .load(StringUtils.handledImgUrl(data.getCusHeadPath()))
//                    .apply(new RequestOptions().error(R.mipmap.ic_head003))
//                    .into(mBinding.ivDialogMemberHead);
            mBinding.tvDialogMemberName.setText(data.getCusName());
            mBinding.tvDialogMemberSelect.setVisibility(View.GONE);
            mBinding.tvDialogMemberHint.setVisibility(View.VISIBLE);
            balance = data.getTotalBalance();
            mBinding.tvDialogMemberHint.setHint(getRstr(R.string.balance) + DFUtils.getNum2(balance));
        }
        mBinding.tvDialogCombinationTotal.setText(getRstr(R.string.money) + DFUtils.getNum2(total));
    }

    /**
     * 更新UI-应收/折扣价
     */
    private void setUIPrice() {
        mBinding.tvDialogCashTotal.setText(getRstr(R.string.money) + DFUtils.getNum2(money));
        if (totalOld > money) {
            mBinding.tvDialogTotalName.setText(getRstr(R.string.price_discount));
            mBinding.tvDialogTotalOld.setVisibility(View.VISIBLE);
        } else {
            mBinding.tvDialogTotalName.setText(getRstr(R.string.receivable));
            mBinding.tvDialogTotalOld.setVisibility(View.GONE);
        }
    }

    /**
     * 更新UI-抹零
     */
    private void setUIZero() {
        //抹零类型 0.不抹零 1.抹零到1元 2.抹零到5角 3.抹零到1角
        switch (zeroType) {
            case 1:
                mBinding.tvDialogZero0.setBackgroundResource(R.drawable.shape_red_tm_5);
                mBinding.tvDialogZero1.setBackgroundResource(R.drawable.shape_red_kuang_5);
                mBinding.tvDialogZero2.setBackgroundResource(R.drawable.shape_red_kuang_5);
                break;
            case 2:
                mBinding.tvDialogZero0.setBackgroundResource(R.drawable.shape_red_kuang_5);
                mBinding.tvDialogZero1.setBackgroundResource(R.drawable.shape_red_tm_5);
                mBinding.tvDialogZero2.setBackgroundResource(R.drawable.shape_red_kuang_5);
                break;
            case 3:
                mBinding.tvDialogZero0.setBackgroundResource(R.drawable.shape_red_kuang_5);
                mBinding.tvDialogZero1.setBackgroundResource(R.drawable.shape_red_kuang_5);
                mBinding.tvDialogZero2.setBackgroundResource(R.drawable.shape_red_tm_5);
                break;
            default:
                mBinding.tvDialogZero0.setBackgroundResource(R.drawable.shape_red_kuang_5);
                mBinding.tvDialogZero1.setBackgroundResource(R.drawable.shape_red_kuang_5);
                mBinding.tvDialogZero2.setBackgroundResource(R.drawable.shape_red_kuang_5);
                break;
        }
        mBinding.numberKeyBoardView.setResultStr("");
    }

    /**
     * 更新样式
     */
    private void setTextBg() {
        //-1.未选择 0.现金 1.微信 2.支付宝 3.银行卡 4.储值卡
        switch (type) {
            case 0:
                mBinding.linDialogCash.setBackgroundResource(R.drawable.shape_green_kuang_5);
                mBinding.ivDialogCursorCash.setVisibility(View.VISIBLE);
                mBinding.tvDialogCash.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.linDialogWechat.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorWechat.setVisibility(View.GONE);
                mBinding.tvDialogWechat.setBackgroundResource(0);
                mBinding.linDialogAlipay.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorAlipay.setVisibility(View.GONE);
                mBinding.tvDialogAlipay.setBackgroundResource(0);
                mBinding.linDialogBank.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorBank.setVisibility(View.GONE);
                mBinding.tvDialogBank.setBackgroundResource(0);
                mBinding.linDialogMember.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorMember.setVisibility(View.GONE);
                mBinding.tvDialogWechat.setBackgroundResource(0);
                break;
            case 1:
                mBinding.linDialogCash.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorCash.setVisibility(View.GONE);
                mBinding.tvDialogCash.setBackgroundResource(0);
                mBinding.linDialogWechat.setBackgroundResource(R.drawable.shape_green_kuang_5);
                mBinding.ivDialogCursorWechat.setVisibility(View.VISIBLE);
                mBinding.tvDialogWechat.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.linDialogAlipay.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorAlipay.setVisibility(View.GONE);
                mBinding.tvDialogAlipay.setBackgroundResource(0);
                mBinding.linDialogBank.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorBank.setVisibility(View.GONE);
                mBinding.tvDialogBank.setBackgroundResource(0);
                mBinding.linDialogMember.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorMember.setVisibility(View.GONE);
                mBinding.tvDialogMember.setBackgroundResource(0);
                break;
            case 2:
                mBinding.linDialogCash.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorCash.setVisibility(View.GONE);
                mBinding.tvDialogCash.setBackgroundResource(0);
                mBinding.linDialogWechat.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorWechat.setVisibility(View.GONE);
                mBinding.tvDialogWechat.setBackgroundResource(0);
                mBinding.linDialogAlipay.setBackgroundResource(R.drawable.shape_green_kuang_5);
                mBinding.ivDialogCursorAlipay.setVisibility(View.VISIBLE);
                mBinding.tvDialogAlipay.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.linDialogBank.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorBank.setVisibility(View.GONE);
                mBinding.tvDialogBank.setBackgroundResource(0);
                mBinding.linDialogMember.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorMember.setVisibility(View.GONE);
                mBinding.tvDialogMember.setBackgroundResource(0);
                break;
            case 3:
                mBinding.linDialogCash.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorCash.setVisibility(View.GONE);
                mBinding.tvDialogCash.setBackgroundResource(0);
                mBinding.linDialogWechat.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorWechat.setVisibility(View.GONE);
                mBinding.tvDialogWechat.setBackgroundResource(0);
                mBinding.linDialogAlipay.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorAlipay.setVisibility(View.GONE);
                mBinding.tvDialogAlipay.setBackgroundResource(0);
                mBinding.linDialogBank.setBackgroundResource(R.drawable.shape_green_kuang_5);
                mBinding.ivDialogCursorBank.setVisibility(View.VISIBLE);
                mBinding.tvDialogBank.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.linDialogMember.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorMember.setVisibility(View.GONE);
                mBinding.tvDialogMember.setBackgroundResource(0);
                break;
            case 4:
                mBinding.linDialogCash.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorCash.setVisibility(View.GONE);
                mBinding.tvDialogCash.setBackgroundResource(0);
                mBinding.linDialogWechat.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorWechat.setVisibility(View.GONE);
                mBinding.tvDialogWechat.setBackgroundResource(0);
                mBinding.linDialogAlipay.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorAlipay.setVisibility(View.GONE);
                mBinding.tvDialogAlipay.setBackgroundResource(0);
                mBinding.linDialogBank.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                mBinding.ivDialogCursorBank.setVisibility(View.GONE);
                mBinding.tvDialogBank.setBackgroundResource(0);
                mBinding.linDialogMember.setBackgroundResource(R.drawable.shape_green_kuang_5);
                mBinding.ivDialogCursorMember.setVisibility(View.VISIBLE);
                mBinding.tvDialogMember.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.tvDialogMemberHint.setVisibility(View.GONE);
                break;
        }
    }

    private static MyListener listener;

    public interface MyListener {
//        void onCashClick(double money);

        /**
         * 现金收款
         *
         * @param money     现金收款金额
         * @param saveMoney 存零金额
         */
        void onCashClick(double money, double saveMoney);

        /**
         * 组合收款
         *
         * @param total       应收金额
         * @param cashMoney   现金
         * @param wechatMoney 微信
         * @param alipayMoney 支付宝
         * @param bankMoney   银行卡
         * @param memberMoney 储值卡
         */
        void onCombinationClick(double total, double cashMoney, double wechatMoney, double alipayMoney, double bankMoney, double memberMoney);
    }
}
