package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityGoodsEditBinding;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.ui.bean.GoodsInfoData;
import com.yxl.cashier_retail.ui.bean.GoodsListData;
import com.yxl.cashier_retail.ui.bean.SupplierPcData;
import com.yxl.cashier_retail.ui.dialog.AveragePriceDialog;
import com.yxl.cashier_retail.ui.dialog.CateDialog;
import com.yxl.cashier_retail.ui.dialog.DateDialog;
import com.yxl.cashier_retail.ui.dialog.InDialog;
import com.yxl.cashier_retail.ui.dialog.OutDialog;
import com.yxl.cashier_retail.ui.dialog.SupplierEditDialog;
import com.yxl.cashier_retail.ui.dialog.UnitDialog;
import com.yxl.cashier_retail.ui.popupwindow.SupplierPop;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;
import com.yxl.commonlibrary.utils.StringUtils;

import org.greenrobot.eventbus.Subscribe;
import org.litepal.LitePal;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:商品新增、编辑
 * Created by jingang on 2024/6/29
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class GoodsEditActivity extends BaseActivity<ActivityGoodsEditBinding> implements View.OnClickListener {
    private String barcode, barcode0, barcode1, barcode2,//商品条码
            img0, img1, img2,//商品图片
            name0, name1, name2,//商品名称
            supplierUnique,//供货商编号
            cateUnique,//一级分类编号
            cateChildUnique,//二级分类编号
            unit0, unit1, unit2,//商品单位
            specs0, specs1, specs2,//商品规格
            brand,//商品品牌
            goodsDate;//生产日期
    private double inPrice0, inPrice1, inPrice2,//入库单价
            salePrice0, salePrice1, salePrice2,//零售单价
            memberPrice0, memberPrice1, memberPrice2,//会员单价
            onlinePrice0, onlinePrice1, onlinePrice2,//网购单价
            startOrder0, startOrder1, startOrder2,//起订量
            stock,//商品库存
            warningLow,//库存预警下限
            warningTall;//库存预警上限

    private int specsType,//0.基础 1.中间 2.最大
            goodsId0, goodsId1, goodsId2,//商品id
            chengType,//计价类型 0.计件 1.称重
            goodsLife,//保质期
            count1, count2,//单位换算
            warningStatus,//库存预警状态 0.关闭 1.开启
            printerStatus0 = 1, printerStatus1 = 1, printerStatus2 = 1,//收银机上下架状态 1.上架 2.下架
            appletStatus0 = 1, appletStatus1 = 1, appletStatus2 = 1;//小程序上下架状态 1.上架 2.下架

    private boolean isSpecs1, isSpecs2;//是否含有该规格

    private GoodsListData data0, data1, data2;

    //称重、标品商品条码前两位
    private String goodsWeightBarcodeFirstTwo, goodsCommonBarcodeFirstTwo;

    //供货商列表
    private List<SupplierPcData> supplierList = new ArrayList<>();

    @Override
    protected ActivityGoodsEditBinding getViewBinding() {
        return ActivityGoodsEditBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        //称重商品的前两位
        goodsWeightBarcodeFirstTwo = SPUtils.getInstance().getString(Constants.GOODS_WEIGHT_BARCODE_FIRST_TWO, "21");
        //普通商品的前两位
        goodsCommonBarcodeFirstTwo = SPUtils.getInstance().getString(Constants.GOODS_COMMON_BARCODE_FIRST_TWO, "31");

        mBinding.ivBack.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.lin0.setOnClickListener(this);
        mBinding.lin1.setOnClickListener(this);
        mBinding.lin2.setOnClickListener(this);
        mBinding.tvDel.setOnClickListener(this);
        mBinding.tvSyn.setOnClickListener(this);
        mBinding.tvConfirm.setOnClickListener(this);

        //基础包装
        mBinding.tvIn0.setOnClickListener(this);
        mBinding.tvOut0.setOnClickListener(this);
        mBinding.tvPrint0.setOnClickListener(this);
        mBinding.tvClear0.setOnClickListener(this);
        mBinding.linSupplier.setOnClickListener(this);
        mBinding.linCate0.setOnClickListener(this);
        mBinding.linCate1.setOnClickListener(this);
        mBinding.linGoodsDate.setOnClickListener(this);
        mBinding.ivWarning.setOnClickListener(this);
        mBinding.ivBarcode0.setOnClickListener(this);
        mBinding.linChengType0.setOnClickListener(this);
        mBinding.linChengType1.setOnClickListener(this);
        mBinding.linUnit0.setOnClickListener(this);
        mBinding.linInPrice0.setOnClickListener(this);
        mBinding.ivPrinter0.setOnClickListener(this);
        mBinding.ivApplet0.setOnClickListener(this);

        //中间包装
        mBinding.tvPrint1.setOnClickListener(this);
        mBinding.tvClear1.setOnClickListener(this);
        mBinding.ivBarcode1.setOnClickListener(this);
        mBinding.linUnit1.setOnClickListener(this);
        mBinding.tvIn1.setOnClickListener(this);
        mBinding.tvOut1.setOnClickListener(this);
        mBinding.linInPrice1.setOnClickListener(this);
        mBinding.ivPrinter1.setOnClickListener(this);
        mBinding.ivApplet1.setOnClickListener(this);

        //最大包装
        mBinding.tvPrint2.setOnClickListener(this);
        mBinding.tvClear2.setOnClickListener(this);
        mBinding.ivBarcode2.setOnClickListener(this);
        mBinding.linUnit2.setOnClickListener(this);
        mBinding.tvIn2.setOnClickListener(this);
        mBinding.tvOut2.setOnClickListener(this);
        mBinding.linInPrice2.setOnClickListener(this);
        mBinding.ivPrinter2.setOnClickListener(this);
        mBinding.ivApplet2.setOnClickListener(this);
    }

    @Override
    protected void initData() {
        barcode = getIntent().getStringExtra("barcode");
        if (TextUtils.isEmpty(barcode)) {
            mBinding.tvTitle.setText(getRstr(R.string.goods_newly));
            mBinding.tvType.setVisibility(View.GONE);
            mBinding.tvDel.setVisibility(View.GONE);//删除隐藏
            mBinding.tvSyn.setVisibility(View.GONE);//下载到秤隐藏
//            mBinding.tvPrint0.setVisibility(View.GONE);//打印价签隐藏
            //出入库隐藏
            mBinding.tvIn0.setVisibility(View.GONE);
            mBinding.tvOut0.setVisibility(View.GONE);
            //生产日期显示
            mBinding.linGoodsDate.setVisibility(View.VISIBLE);
            //条码可编辑
            mBinding.linBarcode0.setBackgroundResource(R.drawable.shape_d8_kuang_5);
            mBinding.etBarcode0.setFocusable(true);
            mBinding.etBarcode0.setFocusableInTouchMode(true);
            mBinding.ivBarcode0.setVisibility(View.VISIBLE);
            //入库单价
            mBinding.tvInPrice0.setText(getRstr(R.string.in_price));
            mBinding.ivInPrice0.setVisibility(View.GONE);
            mBinding.etInPrice0.setBackgroundResource(R.drawable.shape_d8_kuang_5);
            mBinding.etInPrice0.setFocusable(true);
            mBinding.etInPrice0.setFocusableInTouchMode(true);
            //库存可编辑
            mBinding.etStock0.setBackgroundResource(R.drawable.shape_d8_kuang_5);
            mBinding.etStock0.setFocusable(true);
            mBinding.etStock0.setFocusableInTouchMode(true);
            //最近入库价隐藏
            mBinding.tvStockPrice0.setVisibility(View.GONE);

            mBinding.ivChengType0.setSelected(true);
            mBinding.ivPrinter0.setSelected(true);
            mBinding.ivApplet0.setSelected(true);
            mBinding.ivPrinter1.setSelected(true);
            mBinding.ivApplet1.setSelected(true);
            mBinding.ivPrinter1.setSelected(true);
            mBinding.ivApplet1.setSelected(true);
        } else {
            mBinding.tvTitle.setText(getRstr(R.string.goods_edit));
            mBinding.tvType.setVisibility(View.VISIBLE);
            mBinding.tvDel.setVisibility(View.VISIBLE);
            mBinding.tvSyn.setVisibility(View.VISIBLE);
//            mBinding.tvPrint0.setVisibility(View.VISIBLE);
            mBinding.tvIn0.setVisibility(View.VISIBLE);
            mBinding.tvOut0.setVisibility(View.VISIBLE);
            mBinding.linGoodsDate.setVisibility(View.INVISIBLE);
            mBinding.linBarcode0.setBackgroundResource(R.drawable.shape_f2_5);
            mBinding.etBarcode0.setFocusable(false);
            mBinding.etBarcode0.setFocusableInTouchMode(false);
            mBinding.ivBarcode0.setVisibility(View.GONE);
            //库存均价
            mBinding.tvInPrice0.setText(getRstr(R.string.stock_price_average));
            mBinding.ivInPrice0.setVisibility(View.VISIBLE);
            mBinding.etInPrice0.setBackgroundResource(R.drawable.shape_f2_5);
            mBinding.etInPrice0.setFocusable(false);
            mBinding.etInPrice0.setFocusableInTouchMode(false);
            mBinding.etStock0.setBackgroundResource(R.drawable.shape_f2_5);
            mBinding.etStock0.setFocusable(false);
            mBinding.etStock0.setFocusableInTouchMode(false);
            mBinding.tvStockPrice0.setVisibility(View.VISIBLE);
            getGoodsInfo();
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.lin0:
                //基础包装
                if (specsType != 0) {
                    specsType = 0;
                    mBinding.tv0.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    mBinding.tv0.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.v0.setVisibility(View.VISIBLE);
                    mBinding.tv1.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tv1.setTypeface(Typeface.DEFAULT);
                    mBinding.v1.setVisibility(View.INVISIBLE);
                    mBinding.tv2.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tv2.setTypeface(Typeface.DEFAULT);
                    mBinding.v2.setVisibility(View.INVISIBLE);
                    mBinding.linSpecs0.setVisibility(View.VISIBLE);
                    mBinding.linSpecs1.setVisibility(View.GONE);
                    mBinding.linSpecs2.setVisibility(View.GONE);
                }
                break;
            case R.id.lin1:
                //中间包装
                if (specsType != 1) {
                    specsType = 1;
                    mBinding.tv1.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    mBinding.tv1.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.v1.setVisibility(View.VISIBLE);
                    mBinding.tv0.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tv0.setTypeface(Typeface.DEFAULT);
                    mBinding.v0.setVisibility(View.INVISIBLE);
                    mBinding.tv2.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tv2.setTypeface(Typeface.DEFAULT);
                    mBinding.v2.setVisibility(View.INVISIBLE);
                    mBinding.linSpecs0.setVisibility(View.GONE);
                    mBinding.linSpecs1.setVisibility(View.VISIBLE);
                    mBinding.linSpecs2.setVisibility(View.GONE);
                }
                break;
            case R.id.lin2:
                //最大包装
                if (specsType != 2) {
                    specsType = 2;
                    mBinding.tv2.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    mBinding.tv2.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.v2.setVisibility(View.VISIBLE);
                    mBinding.tv1.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tv1.setTypeface(Typeface.DEFAULT);
                    mBinding.v1.setVisibility(View.INVISIBLE);
                    mBinding.tv0.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tv0.setTypeface(Typeface.DEFAULT);
                    mBinding.v0.setVisibility(View.INVISIBLE);
                    mBinding.linSpecs0.setVisibility(View.GONE);
                    mBinding.linSpecs1.setVisibility(View.GONE);
                    mBinding.linSpecs2.setVisibility(View.VISIBLE);
                }
                break;
            case R.id.tvDel:
                //删除
                postGoodsDel();
                break;
            case R.id.tvSyn:
                //下载到秤
                showToast(1, "暂未开发");
                break;
            case R.id.tvConfirm:
                //保存
                postGoodsEdit();
                break;

            /*基础包装start*/
            case R.id.tvIn0:
                //入库
                if (data0 != null) {
                    showDialogIn(data0);
                }
                break;
            case R.id.tvOut0:
                //出库
                if (data0 != null) {
                    showDialogOut(data0);
                }
                break;
            case R.id.tvPrint0:
                //打印价签
                break;
            case R.id.tvClear0:
                //清空
                clearUI(0);
                break;
            case R.id.linSupplier:
                //选择供货商
                if (supplierList.isEmpty()) {
                    getSupplierList(v);
                } else {
                    showPopSupplier(v);
                }
                break;
            case R.id.linCate0:
                //一级分类
                CateDialog.showDialog(this, cateUnique, cateChildUnique, mBinding.ivCate0, 0, (unique, name, childUnique, childName) -> {
                    cateUnique = unique;
                    cateChildUnique = childUnique;
                    mBinding.tvCate0.setText(name);
                    mBinding.tvCate1.setText(childName);
                });
                break;
            case R.id.linCate1:
                //二级分类
                CateDialog.showDialog(this, cateUnique, cateChildUnique, mBinding.ivCate1, 0, (unique, name, childUnique, childName) -> {
                    cateUnique = unique;
                    cateChildUnique = childUnique;
                    mBinding.tvCate0.setText(name);
                    mBinding.tvCate1.setText(childName);
                });
                break;
            case R.id.linGoodsDate:
                //生产日期
                DateDialog.showDialog(this, mBinding.ivGoodsDate, 0, goodsDate, date -> {
                    goodsDate = date;
                    mBinding.tvGoodsDate.setText(goodsDate);
                });
                break;
            case R.id.ivWarning:
                //库存预警
                if (warningStatus == 0) {
                    mBinding.linWarning.setVisibility(View.VISIBLE);
                    warningStatus = 1;
                    mBinding.ivWarning.setSelected(true);
                } else {
                    mBinding.linWarning.setVisibility(View.GONE);
                    warningStatus = 0;
                    mBinding.ivWarning.setSelected(false);
                }
                break;
            case R.id.ivBarcode0:
                //生成条码(接口)
                getGoodsBarcode(0);
                break;
            case R.id.linChengType0:
                //计价类型：计件
                if (chengType != 0) {
                    chengType = 0;
                    mBinding.ivChengType0.setSelected(true);
                    mBinding.ivChengType1.setSelected(false);
                    mBinding.etBarcode0.setText("");
                    getGoodsBarcode(0);
                    if (!TextUtils.isEmpty(mBinding.etBarcode1.getText().toString().trim())) {
                        mBinding.etBarcode1.setText("");
                        getGoodsBarcode(1);
                    }
                    if (!TextUtils.isEmpty(mBinding.etBarcode2.getText().toString().trim())) {
                        mBinding.etBarcode2.setText("");
                        getGoodsBarcode(2);
                    }
                }
                break;
            case R.id.linChengType1:
                //计价类型：称重
                if (chengType != 1) {
                    chengType = 1;
                    mBinding.ivChengType0.setSelected(false);
                    mBinding.ivChengType1.setSelected(true);
                    mBinding.etBarcode0.setText("");
                    getGoodsBarcode(0);
                    if (!TextUtils.isEmpty(mBinding.etBarcode1.getText().toString().trim())) {
                        mBinding.etBarcode1.setText("");
                        getGoodsBarcode(1);
                    }
                    if (!TextUtils.isEmpty(mBinding.etBarcode2.getText().toString().trim())) {
                        mBinding.etBarcode2.setText("");
                        getGoodsBarcode(2);
                    }
                }
                break;
            case R.id.linUnit0:
                //选择单位
                UnitDialog.showDialog(this, unit0, mBinding.ivUnit0, name -> {
                    unit0 = name;
                    mBinding.tvUnit0.setText(unit0);
                });
                break;
            case R.id.linInPrice0:
            case R.id.linInPrice1:
            case R.id.linInPrice2:
                //库存均价介绍
                AveragePriceDialog.showDialog(this);
                break;
            case R.id.ivPrinter0:
                //收银机上下架
                if (printerStatus0 == 1) {
                    printerStatus0 = 2;
                } else {
                    printerStatus0 = 1;
                }
                mBinding.ivPrinter0.setSelected(printerStatus0 == 1);
                break;
            case R.id.ivApplet0:
                //小程序上下架
                if (appletStatus0 == 1) {
                    appletStatus0 = 2;
                } else {
                    appletStatus0 = 1;
                }
                mBinding.ivApplet0.setSelected(appletStatus0 == 1);
                break;
            /*基础包装end*/

            /*中间包装start*/
            case R.id.tvIn1:
                //入库
                if (data1 != null) {
                    showDialogIn(data1);
                }
                break;
            case R.id.tvOut1:
                //出库
                if (data1 != null) {
                    showDialogOut(data1);
                }
                break;
            case R.id.tvPrint1:
                //打印价签
                break;
            case R.id.tvClear1:
                //清空
                clearUI(1);
                break;
            case R.id.ivBarcode1:
                //生成条码(接口)
                getGoodsBarcode(1);
                break;
            case R.id.linUnit1:
                //选择单位
                UnitDialog.showDialog(this, unit1, mBinding.ivUnit1, name -> {
                    unit1 = name;
                    mBinding.tvUnit1.setText(unit1);
                });
                break;
            case R.id.ivPrinter1:
                //收银机上下架
                if (printerStatus1 == 1) {
                    printerStatus1 = 2;
                } else {
                    printerStatus1 = 1;
                }
                mBinding.ivPrinter1.setSelected(printerStatus1 == 1);
                break;
            case R.id.ivApplet1:
                //小程序上下架
                if (appletStatus1 == 1) {
                    appletStatus1 = 2;
                } else {
                    appletStatus1 = 1;
                }
                mBinding.ivApplet1.setSelected(appletStatus1 == 1);
                break;
            /*中间包装end*/

            /*最大包装start*/
            case R.id.tvIn2:
                //入库
                if (data2 != null) {
                    showDialogIn(data2);
                }
                break;
            case R.id.tvOut2:
                //出库
                if (data2 != null) {
                    showDialogOut(data2);
                }
                break;
            case R.id.tvPrint2:
                //打印价签
                break;
            case R.id.tvClear2:
                //清空
                clearUI(2);
                break;
            case R.id.ivBarcode2:
                //生成条码(接口)
                getGoodsBarcode(2);
                break;
            case R.id.linUnit2:
                //选择单位
                UnitDialog.showDialog(this, unit2, mBinding.ivUnit2, name -> {
                    unit2 = name;
                    mBinding.tvUnit2.setText(unit2);
                });
                break;
            case R.id.ivPrinter2:
                //收银机上下架
                if (printerStatus2 == 1) {
                    printerStatus2 = 2;
                } else {
                    printerStatus2 = 1;
                }
                mBinding.ivPrinter2.setSelected(printerStatus2 == 1);
                break;
            case R.id.ivApplet2:
                //小程序上下架
                if (appletStatus2 == 1) {
                    appletStatus2 = 2;
                } else {
                    appletStatus2 = 1;
                }
                mBinding.ivApplet2.setSelected(appletStatus2 == 1);
                break;
            /*最大包装end*/
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.GOODS_INFO:
                if (!TextUtils.isEmpty(barcode)) {
                    getGoodsInfo();
                }
                break;
        }
    }

    /**
     * dialog（入库）
     *
     * @param data
     */
    private void showDialogIn(GoodsListData data) {
        InDialog.showDialog(this, goodsLife, data, (barcode, count) -> {

        });
    }

    /**
     * dialog（出库）
     *
     * @param data
     */
    private void showDialogOut(GoodsListData data) {
        OutDialog.showDialog(this, data, (barcode, count) -> {

        });
    }

    /**
     * 选择供货商
     * @param view
     */
    private void showPopSupplier(View view) {
        SupplierPop.showDialog(this,
                mBinding.ivSupplier,
                view,
                mBinding.linSupplier.getMeasuredWidth(),
                supplierList,
                supplierUnique,
                new SupplierPop.MyListener() {
                    @Override
                    public void onAddClick() {
                        //添加供货商
                        SupplierEditDialog.showDialog(GoodsEditActivity.this, 0, "", (id, name, type) -> {
                            getSupplierList(view);
                        });
                    }

                    @Override
                    public void onCallBack(SupplierPcData data) {
                        supplierUnique = data.getSupplier_unique();
                        mBinding.tvSupplier.setText(data.getSupplier_name());
                    }
                });
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(GoodsInfoData data) {
        if (data == null) {
            return;
        }
        //商品库
        if (data.getTableType() == 1) {
            mBinding.tvType.setText(getRstr(R.string.cloud_goods));
        } else {
            mBinding.tvType.setText(getRstr(R.string.local_goods));
        }
        //供货商
        supplierUnique = data.getSupplierUnique();
        mBinding.tvSupplier.setText(TextUtils.isEmpty(data.getSupplierName()) ? "" : data.getSupplierName());

        //计价类型
        chengType = data.getGoodsChengType();
        if (chengType == 0) {
            mBinding.ivChengType0.setSelected(true);
            mBinding.ivChengType1.setSelected(false);
        } else {
            mBinding.ivChengType0.setSelected(false);
            mBinding.ivChengType1.setSelected(true);
        }

        //商品分类
        cateUnique = data.getGroupsUnique();
        mBinding.tvCate0.setText(TextUtils.isEmpty(data.getGroupsName()) ? "" : data.getGroupsName());
        cateChildUnique = data.getKindUnique();
        mBinding.tvCate1.setText(TextUtils.isEmpty(data.getKindName()) ? "" : data.getKindName());

        //商品库存
        stock = data.getGoodsCount();
        mBinding.etStock0.setText(stock == 0 ? "" : DFUtils.getNum4(stock));

        //商品品牌
        brand = data.getGoodsBrand();
        mBinding.etBrand.setText(brand);

        //保质期
        goodsLife = data.getGoodsLife();
        mBinding.etGoodsLife.setText(goodsLife == 0 ? "" : String.valueOf(goodsLife));

        /*基础包装*/
        if (data.getListDetail().size() > 0) {
            data0 = data.getListDetail().get(0);
            goodsId0 = data0.getGoodsId();
            barcode0 = data0.getGoodsBarcode();
            img0 = StringUtils.handledImgUrl(data0.getGoodsPicturepath());
            //条码
            mBinding.etBarcode0.setText(data0.getGoodsBarcode());
            //名称
            mBinding.etName0.setText(data0.getGoodsName());
            //单位
            unit0 = data0.getGoodsUnit();
            mBinding.tvUnit0.setText(unit0);
            //入库单价
            if (data0.getGoodsInPrice() == 0) {
                mBinding.etInPrice0.setText("");
            } else {
                mBinding.etInPrice0.setText(DFUtils.getNum4(data0.getGoodsInPrice()));
            }
            //最近入库价
            mBinding.tvStockPrice0.setText(getRstr(R.string.in_price_recently_colon) + DFUtils.getNum2(data0.getGoodStockPrice()));
            //零售单价
            mBinding.etSalePrice0.setText(data0.getGoodsSalePrice() == 0 ? "" : DFUtils.getNum2(data0.getGoodsSalePrice()));
            //会员单价
            mBinding.etMemberPrice0.setText(data0.getGoodsCusPrice() == 0 ? "" : DFUtils.getNum2(data0.getGoodsCusPrice()));
            //网购单价
            mBinding.etOnlinePrice0.setText(data0.getGoodsWebSalePrice() == 0 ? "" : DFUtils.getNum2(data0.getGoodsWebSalePrice()));
            //起订量
            if (TextUtils.isEmpty(data0.getMinSaleCount())) {
                mBinding.etStartOrder0.setText("");
            } else {
                startOrder0 = Double.parseDouble(data0.getMinSaleCount());
                if (startOrder0 > 0) {
                    mBinding.etStartOrder0.setText(DFUtils.getNum4(startOrder0));
                } else {
                    mBinding.etStartOrder0.setText("");
                }
            }
            //商品规格
            mBinding.etSpecs0.setText(TextUtils.isEmpty(data0.getGoodsStandard()) ? "" : data0.getGoodsStandard());
            //库存预警
            warningStatus = data0.getStock_warning_status();
            if (warningStatus == 0) {
                mBinding.ivWarning.setSelected(false);
                mBinding.linWarning.setVisibility(View.GONE);
            } else {
                mBinding.ivWarning.setSelected(true);
                mBinding.linWarning.setVisibility(View.VISIBLE);
                warningLow = data0.getOut_stock_waring_count();
                warningTall = data0.getUnsalable_count();
                if (warningLow > 0) {
                    mBinding.etWarningLow.setText(DFUtils.getNum4(warningLow));
                } else {
                    mBinding.etWarningLow.setText("");
                }
                if (warningTall > 0) {
                    mBinding.etWarningTall.setText(DFUtils.getNum4(warningTall));
                } else {
                    mBinding.etWarningTall.setText("");
                }
            }
            //收银机上下架状态
            printerStatus0 = data0.getPcShelfState();
            mBinding.ivPrinter0.setSelected(printerStatus0 == 1);
            //小程序上下架状态
            appletStatus0 = data0.getShelfState();
            mBinding.ivApplet0.setSelected(appletStatus0 == 1);
        }

        /*中间包装*/
        if (data.getListDetail().size() > 1) {
            isSpecs1 = true;
            data1 = data.getListDetail().get(1);
            goodsId1 = data1.getGoodsId();
            barcode1 = data1.getGoodsBarcode();
            img1 = StringUtils.handledImgUrl(data1.getGoodsPicturepath());
            mBinding.etBarcode1.setText(data1.getGoodsBarcode());
            mBinding.etName1.setText(data1.getGoodsName());
            mBinding.etCount1.setText(DFUtils.getNum4(data1.getContainCount()));//单位换算
            //入库单价
            mBinding.etInPrice1.setText(data1.getGoodsInPrice() == 0 ? "" : DFUtils.getNum2(data1.getGoodsInPrice()));
            //最近入库价
            mBinding.tvStockPrice1.setText(getRstr(R.string.in_price_recently_colon) + DFUtils.getNum2(data1.getGoodStockPrice()));
            mBinding.etSalePrice1.setText(data1.getGoodsSalePrice() == 0 ? "" : DFUtils.getNum2(data1.getGoodsSalePrice()));
            mBinding.etMemberPrice1.setText(data1.getGoodsCusPrice() == 0 ? "" : DFUtils.getNum2(data1.getGoodsCusPrice()));
            mBinding.etOnlinePrice1.setText(data1.getGoodsWebSalePrice() == 0 ? "" : DFUtils.getNum2(data1.getGoodsWebSalePrice()));
            if (TextUtils.isEmpty(data1.getMinSaleCount())) {
                mBinding.etStartOrder1.setText("");
            } else {
                startOrder1 = Double.parseDouble(data1.getMinSaleCount());
                if (startOrder1 > 0) {
                    mBinding.etStartOrder1.setText(DFUtils.getNum4(startOrder1));
                } else {
                    mBinding.etStartOrder1.setText("");
                }
            }
            mBinding.etSpecs1.setText(TextUtils.isEmpty(data1.getGoodsStandard()) ? "" : data1.getGoodsStandard());
            unit1 = data1.getGoodsUnit();
            mBinding.tvUnit1.setText(unit1);
            printerStatus1 = data1.getPcShelfState();
            mBinding.ivPrinter1.setSelected(printerStatus1 == 1);
            appletStatus1 = data1.getShelfState();
            mBinding.ivApplet1.setSelected(appletStatus1 == 1);
//            mBinding.tvPrint1.setVisibility(View.VISIBLE);
            //条码不可编辑
            mBinding.linBarcode1.setBackgroundResource(R.drawable.shape_f2_5);
            mBinding.etBarcode1.setFocusable(false);
            mBinding.etBarcode1.setFocusableInTouchMode(false);
            mBinding.ivBarcode1.setVisibility(View.GONE);
            //库存
            mBinding.linStock1.setVisibility(View.VISIBLE);
            if (data1.getContainCount() > 0) {
                mBinding.tvStock1.setText(String.valueOf((int) (stock / data1.getContainCount())));
            } else {
                mBinding.tvStock1.setText("0");
            }
            mBinding.tvIn1.setVisibility(View.VISIBLE);
            mBinding.tvOut1.setVisibility(View.VISIBLE);
            mBinding.tvInPrice1.setText(getRstr(R.string.stock_price_average));
            mBinding.ivInPrice1.setVisibility(View.VISIBLE);
            mBinding.etInPrice1.setBackgroundResource(R.drawable.shape_f2_5);
            mBinding.etInPrice1.setFocusable(false);
            mBinding.etInPrice1.setFocusableInTouchMode(false);
            mBinding.tvStockPrice1.setVisibility(View.VISIBLE);
        } else {
            isSpecs1 = false;
            mBinding.tvIn1.setVisibility(View.GONE);
            mBinding.tvOut1.setVisibility(View.GONE);
//            mBinding.tvPrint1.setVisibility(View.GONE);
            mBinding.linBarcode1.setBackgroundResource(R.drawable.shape_d8_kuang_5);
            mBinding.etBarcode1.setFocusable(true);
            mBinding.etBarcode1.setFocusableInTouchMode(true);
            mBinding.ivBarcode1.setVisibility(View.VISIBLE);
            mBinding.linStock1.setVisibility(View.GONE);
            mBinding.tvInPrice1.setText(getRstr(R.string.in_price));
            mBinding.ivInPrice1.setVisibility(View.GONE);
            mBinding.etInPrice1.setBackgroundResource(R.drawable.shape_d8_kuang_5);
            mBinding.etInPrice1.setFocusable(true);
            mBinding.etInPrice1.setFocusableInTouchMode(true);
            mBinding.tvStockPrice1.setVisibility(View.GONE);
        }

        /*最大包装*/
        if (data.getListDetail().size() > 2) {
            isSpecs2 = true;
            data2 = data.getListDetail().get(2);
            goodsId2 = data2.getGoodsId();
            barcode2 = data2.getGoodsBarcode();
            img2 = StringUtils.handledImgUrl(data2.getGoodsPicturepath());
            mBinding.etBarcode2.setText(data2.getGoodsBarcode());
            mBinding.etName2.setText(data2.getGoodsName());
            mBinding.etCount2.setText(DFUtils.getNum4(data2.getContainCount()));//单位换算
            //入库单价
            mBinding.etInPrice2.setText(data2.getGoodsInPrice() == 0 ? "" : DFUtils.getNum2(data2.getGoodsInPrice()));
            //最近入库价
            mBinding.tvStockPrice2.setText(getRstr(R.string.in_price_recently_colon) + DFUtils.getNum2(data2.getGoodStockPrice()));
            mBinding.etSalePrice2.setText(data2.getGoodsSalePrice() == 0 ? "" : DFUtils.getNum2(data2.getGoodsSalePrice()));
            mBinding.etMemberPrice2.setText(data2.getGoodsCusPrice() == 0 ? "" : DFUtils.getNum2(data2.getGoodsCusPrice()));
            mBinding.etOnlinePrice2.setText(data2.getGoodsWebSalePrice() == 0 ? "" : DFUtils.getNum2(data2.getGoodsWebSalePrice()));
            if (TextUtils.isEmpty(data2.getMinSaleCount())) {
                mBinding.etStartOrder2.setText("");
            } else {
                startOrder2 = Double.parseDouble(data2.getMinSaleCount());
                if (startOrder2 > 0) {
                    mBinding.etStartOrder2.setText(DFUtils.getNum4(startOrder2));
                } else {
                    mBinding.etStartOrder2.setText("");
                }
            }
            mBinding.etSpecs2.setText(TextUtils.isEmpty(data2.getGoodsStandard()) ? "" : data2.getGoodsStandard());
            unit2 = data1.getGoodsUnit();
            mBinding.tvUnit2.setText(unit2);
            printerStatus2 = data2.getPcShelfState();
            mBinding.ivPrinter2.setSelected(printerStatus2 == 1);
            appletStatus2 = data2.getShelfState();
            mBinding.ivApplet2.setSelected(appletStatus2 == 1);

//            mBinding.tvPrint2.setVisibility(View.VISIBLE);
            mBinding.linBarcode2.setBackgroundResource(R.drawable.shape_f2_5);
            mBinding.etBarcode2.setFocusable(false);
            mBinding.etBarcode2.setFocusableInTouchMode(false);
            mBinding.ivBarcode2.setVisibility(View.GONE);
            //库存
            mBinding.linStock2.setVisibility(View.VISIBLE);
            if (data2.getContainCount() > 0) {
                mBinding.tvStock2.setText(String.valueOf((int) (stock / data2.getContainCount())));
            } else {
                mBinding.tvStock2.setText("0");
            }
            mBinding.tvIn2.setVisibility(View.VISIBLE);
            mBinding.tvOut2.setVisibility(View.VISIBLE);
            mBinding.tvInPrice2.setText(getRstr(R.string.stock_price_average));
            mBinding.ivInPrice2.setVisibility(View.VISIBLE);
            mBinding.etInPrice2.setBackgroundResource(R.drawable.shape_f2_5);
            mBinding.etInPrice2.setFocusable(false);
            mBinding.etInPrice2.setFocusableInTouchMode(false);
            mBinding.tvStockPrice2.setVisibility(View.VISIBLE);
        } else {
            isSpecs2 = false;
//            mBinding.tvPrint2.setVisibility(View.GONE);
            mBinding.linBarcode2.setBackgroundResource(R.drawable.shape_d8_kuang_5);
            mBinding.etBarcode2.setFocusable(true);
            mBinding.etBarcode2.setFocusableInTouchMode(true);
            mBinding.ivBarcode2.setVisibility(View.VISIBLE);
            mBinding.linStock2.setVisibility(View.GONE);
            mBinding.tvIn2.setVisibility(View.GONE);
            mBinding.tvOut2.setVisibility(View.GONE);
            mBinding.tvInPrice2.setText(getRstr(R.string.in_price));
            mBinding.ivInPrice2.setVisibility(View.GONE);
            mBinding.etInPrice2.setBackgroundResource(R.drawable.shape_d8_kuang_5);
            mBinding.etInPrice2.setFocusable(true);
            mBinding.etInPrice2.setFocusableInTouchMode(true);
            mBinding.tvStockPrice2.setVisibility(View.GONE);
        }
    }

    /**
     * 清空
     *
     * @param type 0.基础 1.中间 2.最大
     */
    private void clearUI(int type) {
        if (type == 0) {
            if (TextUtils.isEmpty(barcode0)) {
                mBinding.etBarcode0.setText("");
                mBinding.etStock0.setText("");
                mBinding.etInPrice0.setText("");
            }
            supplierUnique = "";
            mBinding.tvSupplier.setText("");
            cateUnique = "";
            mBinding.tvCate0.setText("");
            cateChildUnique = "";
            mBinding.tvCate1.setText("");
            mBinding.etName0.setText("");
            unit0 = "";
            mBinding.tvUnit0.setText(unit0);
            mBinding.etSalePrice0.setText("");
            mBinding.etMemberPrice0.setText("");
            mBinding.etOnlinePrice0.setText("");
            mBinding.etStartOrder0.setText("");
            mBinding.etSpecs0.setText("");
            mBinding.etBrand.setText("");
            goodsLife = 0;
            mBinding.etGoodsLife.setText("");
            goodsDate = "";
            mBinding.tvGoodsDate.setText("");
            mBinding.etWarningLow.setText("");
            mBinding.etWarningTall.setText("");
        }
        if (type == 1) {
            if (TextUtils.isEmpty(barcode1)) {
                mBinding.etBarcode1.setText("");
                mBinding.etInPrice1.setText("");
            }
            mBinding.etName1.setText("");
            mBinding.etCount1.setText("");
            mBinding.etSalePrice1.setText("");
            mBinding.etMemberPrice1.setText("");
            mBinding.etOnlinePrice1.setText("");
            mBinding.etStartOrder1.setText("");
            mBinding.etSpecs1.setText("");
            unit1 = "";
            mBinding.tvUnit1.setText(unit1);
        }
        if (type == 2) {
            if (TextUtils.isEmpty(barcode2)) {
                mBinding.etBarcode2.setText("");
                mBinding.etInPrice2.setText("");
            }
            mBinding.etName2.setText("");
            mBinding.etCount2.setText("");
            mBinding.etSalePrice2.setText("");
            mBinding.etMemberPrice2.setText("");
            mBinding.etOnlinePrice2.setText("");
            mBinding.etStartOrder2.setText("");
            mBinding.etSpecs2.setText("");
            unit2 = "";
            mBinding.tvUnit2.setText(unit2);
        }
    }

    /**
     * 供货商列表
     */
    private void getSupplierList(View v) {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("supMsg", "");
        params.put("page", 1);
        params.put("limit", 10000);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getSupplierList(),
                params,
                SupplierPcData.class,
                new RequestListListener<SupplierPcData>() {
                    @Override
                    public void onResult(List<SupplierPcData> list) {
                        supplierList.clear();
                        supplierList.addAll(list);
                        showPopSupplier(v);
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 商品详情
     */
    private void getGoodsInfo() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("goodsBarcode", barcode);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getGoodsInfo(),
                params,
                GoodsInfoData.class,
                new RequestListener<GoodsInfoData>() {
                    @Override
                    public void success(GoodsInfoData data) {
                        hideDialog();
                        setUI(data);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        finish();
                    }
                });
    }

    /**
     * 生成条码
     *
     * @param type 0.基础 1.中间 2.最大
     */
    private void getGoodsBarcode(int type) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        if (chengType == 1) {
            map.put("indexNum", goodsWeightBarcodeFirstTwo);
            map.put("length", 7);
        } else {
            map.put("indexNum", goodsCommonBarcodeFirstTwo);
            map.put("length", 13);
        }
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getGoodsBarcode(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        switch (type) {
                            case 1:
                                mBinding.etBarcode1.setText(s);
                                break;
                            case 2:
                                mBinding.etBarcode2.setText(s);
                                break;
                            default:
                                mBinding.etBarcode0.setText(s);
                                break;
                        }
                    }
                });
    }

    /**
     * 商品新增、编辑
     */
    private void postGoodsEdit() {
        List array = new ArrayList();
        if (TextUtils.isEmpty(cateChildUnique)) {
            showToast(1, getRstr(R.string.select_cate));
            return;
        }
        /*基础包装*/
        barcode0 = mBinding.etBarcode0.getText().toString().trim();
        if (TextUtils.isEmpty(barcode0)) {
            showToast(1, getRstr(R.string.input_goods_barcode));
            return;
        }
        name0 = mBinding.etName0.getText().toString().trim();
        if (TextUtils.isEmpty(name0)) {
            showToast(1, getRstr(R.string.input_goods_name));
            return;
        }
        if (TextUtils.isEmpty(unit0)) {
            showToast(1, getRstr(R.string.select_goods_unit));
            return;
        }
        inPrice0 = TextUtils.isEmpty(mBinding.etInPrice0.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etInPrice0.getText().toString().trim());
        if (TextUtils.isEmpty(barcode)) {
            //新增时有入库单价
            if (TextUtils.isEmpty(mBinding.etStock0.getText().toString().trim())) {
                stock = 0;
            } else {
                stock = Double.parseDouble(mBinding.etStock0.getText().toString().trim());
            }
            if (inPrice0 == 0) {
                showToast(1, getRstr(R.string.input_in_price));
                return;
            }
        }
        salePrice0 = TextUtils.isEmpty(mBinding.etSalePrice0.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etSalePrice0.getText().toString().trim());
        if (salePrice0 == 0) {
            showToast(1, getRstr(R.string.input_price_sale));
            return;
        }
        memberPrice0 = TextUtils.isEmpty(mBinding.etMemberPrice0.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etMemberPrice0.getText().toString().trim());
        onlinePrice0 = TextUtils.isEmpty(mBinding.etOnlinePrice0.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etOnlinePrice0.getText().toString().trim());
        startOrder0 = TextUtils.isEmpty(mBinding.etStartOrder0.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etStartOrder0.getText().toString().trim());
        specs0 = mBinding.etSpecs0.getText().toString().trim();
        brand = mBinding.etBrand.getText().toString().trim();
        goodsLife = TextUtils.isEmpty(mBinding.etGoodsLife.getText().toString().trim()) ? 0 : Integer.parseInt(mBinding.etGoodsLife.getText().toString().trim());
        //库存预警
        if (warningStatus == 1) {
            warningLow = TextUtils.isEmpty(mBinding.etWarningLow.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etWarningLow.getText().toString().trim());
            warningTall = TextUtils.isEmpty(mBinding.etWarningTall.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etWarningTall.getText().toString().trim());
        }
        Map object0 = new HashMap();
        if (!TextUtils.isEmpty(barcode) && goodsId0 != 0) {
            object0.put("goodsId", goodsId0);
        }
        object0.put("goodsPicturePath", img0);
        object0.put("goodsBarcode", barcode0);
        object0.put("goodsName", name0);
        object0.put("goodsUnit", unit0);
        object0.put("goodsInPrice", inPrice0);
        object0.put("goodsSalePrice", salePrice0);
        object0.put("goodsWebSalePrice", onlinePrice0);
        object0.put("goodsCusPrice", memberPrice0);
        object0.put("goodsContain", 1);
        object0.put("minSaleCount", startOrder0);
        object0.put("goodsStandard", specs0);
        object0.put("shelfState", appletStatus0);//线上上架状态：1、已上架；2、已下架
        object0.put("pcShelfState", printerStatus0);//pc收银上架状态：1、已上架；2、已下架
        array.add(object0);

        /*中间包装*/
        barcode1 = mBinding.etBarcode1.getText().toString().trim();
        name1 = mBinding.etName1.getText().toString().trim();
        count1 = TextUtils.isEmpty(mBinding.etCount1.getText().toString().trim()) ? 0 : Integer.parseInt(mBinding.etCount1.getText().toString().trim());
        inPrice1 = TextUtils.isEmpty(mBinding.etInPrice1.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etInPrice1.getText().toString().trim());
        salePrice1 = TextUtils.isEmpty(mBinding.etSalePrice1.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etSalePrice1.getText().toString().trim());
        memberPrice1 = TextUtils.isEmpty(mBinding.etMemberPrice1.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etMemberPrice1.getText().toString().trim());
        onlinePrice1 = TextUtils.isEmpty(mBinding.etOnlinePrice1.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etOnlinePrice1.getText().toString().trim());
        startOrder1 = TextUtils.isEmpty(mBinding.etStartOrder1.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etStartOrder1.getText().toString().trim());
        specs1 = mBinding.etSpecs1.getText().toString().trim();
        //必选项有一个则判断
        if (!TextUtils.isEmpty(barcode1) || !TextUtils.isEmpty(name1) || !TextUtils.isEmpty(unit1)
                || count1 > 0 || inPrice1 > 0 || salePrice1 > 0) {
            if (TextUtils.isEmpty(barcode1)) {
                showToast(1, getRstr(R.string.input_goods_barcode_specs1));
                return;
            }
            if (TextUtils.isEmpty(name1)) {
                showToast(1, getRstr(R.string.input_goods_name_specs1));
                return;
            }
            if (count1 <= 1) {
                showToast(1, getRstr(R.string.base_unit_count_than_one));
                return;
            }
            if (!isSpecs1) {
                if (inPrice1 == 0) {
                    showToast(1, getRstr(R.string.input_in_price_specs1));
                    return;
                }
            }
            if (salePrice1 == 0) {
                showToast(1, getRstr(R.string.input_price_sale_specs1));
                return;
            }
            if (TextUtils.isEmpty(unit1)) {
                showToast(1, getRstr(R.string.select_goods_unit_specs1));
                return;
            }

            Map object1 = new HashMap();
            if (!TextUtils.isEmpty(barcode) && goodsId1 != 0) {
                object1.put("goodsId", goodsId1);
            }
            object1.put("goodsPicturePath", img1);
            object1.put("goodsBarcode", barcode1);
            object1.put("goodsName", name1);
            object1.put("goodsContain", count1);
            if (!isSpecs1) {
                object1.put("goodsInPrice", inPrice1);
            }
            object1.put("goodsSalePrice", salePrice1);
            object1.put("goodsCusPrice", memberPrice1);
            object1.put("goodsWebSalePrice", onlinePrice1);
            object1.put("minSaleCount", startOrder1);
            object1.put("goodsStandard", specs1);
            object1.put("goodsUnit", unit1);
            object1.put("shelfState", appletStatus1);//线上上架状态：1、已上架；2、已下架
            object1.put("pcShelfState", printerStatus1);//pc收银上架状态：1、已上架；2、已下架
            array.add(object1);
        }
        /*最大包装*/
        barcode2 = mBinding.etBarcode2.getText().toString().trim();
        name2 = mBinding.etName2.getText().toString().trim();
        count2 = TextUtils.isEmpty(mBinding.etCount2.getText().toString().trim()) ? 0 : Integer.parseInt(mBinding.etCount2.getText().toString().trim());
        inPrice2 = TextUtils.isEmpty(mBinding.etInPrice2.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etInPrice2.getText().toString().trim());
        salePrice2 = TextUtils.isEmpty(mBinding.etSalePrice2.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etSalePrice2.getText().toString().trim());
        memberPrice2 = TextUtils.isEmpty(mBinding.etMemberPrice2.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etMemberPrice2.getText().toString().trim());
        onlinePrice2 = TextUtils.isEmpty(mBinding.etOnlinePrice2.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etOnlinePrice2.getText().toString().trim());
        startOrder2 = TextUtils.isEmpty(mBinding.etStartOrder2.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etStartOrder2.getText().toString().trim());
        specs2 = mBinding.etSpecs2.getText().toString().trim();
        //判断几个必选项是否全都不为空
        if (!TextUtils.isEmpty(barcode2) || !TextUtils.isEmpty(name2) || !TextUtils.isEmpty(unit2)
                || count2 > 0 || inPrice2 > 0 || salePrice2 > 0) {
            if (TextUtils.isEmpty(barcode2)) {
                showToast(1, getRstr(R.string.input_goods_barcode_specs2));
                return;
            }
            if (TextUtils.isEmpty(name2)) {
                showToast(1, getRstr(R.string.input_goods_name_specs2));
                return;
            }
            if (count2 <= count1) {
                showToast(1, getRstr(R.string.base_unit_count_than_specs1));
                return;
            }
            if (!isSpecs2) {
                if (inPrice2 == 0) {
                    showToast(1, getRstr(R.string.input_in_price_specs2));
                    return;
                }
            }
            if (salePrice2 == 0) {
                showToast(1, getRstr(R.string.input_price_sale_specs2));
                return;
            }
            if (TextUtils.isEmpty(unit2)) {
                showToast(1, getRstr(R.string.select_goods_unit_specs2));
                return;
            }
            Map object2 = new HashMap();
            if (!TextUtils.isEmpty(barcode) && goodsId2 != 0) {
                object2.put("goodsId", goodsId2);
            }
            object2.put("goodsPicturePath", img2);
            object2.put("goodsBarcode", barcode2);
            object2.put("goodsName", name2);
            if (!isSpecs2) {
                object2.put("goodsInPrice", inPrice2);
            }
            object2.put("goodsSalePrice", salePrice2);
            object2.put("goodsWebSalePrice", onlinePrice2);
            object2.put("goodsUnit", unit2);
            object2.put("goodsCusPrice", memberPrice2);
            object2.put("goodsContain", count2);
            object2.put("goodsStandard", specs2);
            object2.put("minSaleCount", startOrder2);
            object2.put("shelfState", appletStatus2);//线上上架状态：1、已上架；2、已下架
            object2.put("pcShelfState", printerStatus2);//pc收银上架状态：1、已上架；2、已下架
            array.add(object2);
        }
        Log.e(tag, "array = " + array);
        showDialog();
        String url;
        Map<String, Object> map = new HashMap<>();
        if (!TextUtils.isEmpty(barcode)) {
            //编辑
            url = ZURL.getGoodsEdit();
        } else {
            //新增
            url = ZURL.getGoodsAdd();
            map.put("goodsCount", stock);//库存
            map.put("goodsProd", goodsDate);//生产日期
        }
        map.put("shopUnique", getShopUnique());
        map.put("supplierUnique", supplierUnique);//供货商编号
        map.put("goodsKindUnique", cateChildUnique);//二级分类编号
        map.put("goodsChengType", chengType);//计价类型 0.计件 1.称重
        map.put("goodsBrand", brand);//品牌
        map.put("foreignKey", barcode0);//包装外键（最小规格的商品条码）
        map.put("goodsLife", goodsLife);//保质期
        //库存预警
        if (warningStatus == 1) {
            map.put("stockWarningStatus", warningStatus);
            map.put("outStockWaringCount", warningLow);
            map.put("unsalableCount", warningTall);
        }
        map.put("goodsMessage", array);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                url,
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 删除商品
     */
    public void postGoodsDel() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("goodsBarcode", barcode);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getGoodsDel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        GoodsData data = LitePal.where("goods_barcode = ?", barcode).findFirst(GoodsData.class);
                        if (data != null) {
                            data.delete();
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_LIST));
                        }
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }
}
