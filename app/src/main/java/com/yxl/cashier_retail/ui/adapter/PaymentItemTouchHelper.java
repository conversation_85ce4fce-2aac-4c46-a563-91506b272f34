package com.yxl.cashier_retail.ui.adapter;

import android.app.Activity;
import android.os.Build;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.RecyclerView;


import com.chad.library.adapter.base.BaseQuickAdapter;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.ui.bean.PaymentData;

import java.util.Collections;
import java.util.List;

/**
 * Describe:item拖拽（支付方式）
 * Created by jingang on 2024/5/15
 */
public class PaymentItemTouchHelper<T> extends ItemTouchHelper.Callback {
    private List<PaymentData> list;
    private BaseAdapter adapter;
    Activity activity;

    public PaymentItemTouchHelper(Activity activity, List<PaymentData> list, BaseAdapter adapter, MyListener listener) {
        this.list = list;
        this.adapter = adapter;
        this.activity = activity;
        this.listener = listener;
    }

    @Override
    public int getMovementFlags(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder) {
        final int dragFlags = ItemTouchHelper.UP | ItemTouchHelper.DOWN | ItemTouchHelper.LEFT | ItemTouchHelper.RIGHT;
        final int swipeFlags = 0;
//        int position = viewHolder.getAdapterPosition();
//        if (list.get(position).getGroupName().equals("更多分类")) {
//            return makeMovementFlags(0, 0);
//        } else {
//            return makeMovementFlags(dragFlags, swipeFlags);
//        }
        return makeMovementFlags(dragFlags, swipeFlags);
    }

    @Override
    public boolean onMove(@NonNull RecyclerView recyclerView, @NonNull RecyclerView.ViewHolder viewHolder, @NonNull RecyclerView.ViewHolder target) {
        recyclerView.getParent().requestDisallowInterceptTouchEvent(true);
        //得到当拖拽的viewHolder的Position
        int fromPosition = viewHolder.getAdapterPosition();
        //拿到当前拖拽到的item的viewHolder
        int toPosition = target.getAdapterPosition();
        if (fromPosition < toPosition) {
            for (int i = fromPosition; i < toPosition; i++) {
                Collections.swap(list, i, i + 1);
            }
        } else {
            for (int i = fromPosition; i > toPosition; i--) {
                Collections.swap(list, i, i - 1);
            }
        }
        adapter.notifyItemMoved(fromPosition, toPosition);
        if (listener != null) {
            listener.onMoved(list);
        }
        return true;
    }

    @Override
    public void onSwiped(@NonNull RecyclerView.ViewHolder viewHolder, int direction) {
    }

    /**
     * 长按选中Item时修改颜色
     *
     * @param viewHolder
     * @param actionState
     */
    @Override
    public void onSelectedChanged(RecyclerView.ViewHolder viewHolder, int actionState) {
        //if (actionState != ItemTouchHelper.ACTION_STATE_IDLE) {
        //viewHolder.itemView.setBackground(getDrawable(R.drawable.card_drag_selected));
        //}
        super.onSelectedChanged(viewHolder, actionState);
    }

    /**
     * 手指松开的时候还原颜色
     *
     * @param recyclerView
     * @param viewHolder
     */
    @RequiresApi(api = Build.VERSION_CODES.M)
    @Override
    public void clearView(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder) {
        super.clearView(recyclerView, viewHolder);
        //viewHolder.itemView.setBackground(getDrawable(R.drawable.card));
    }

    /**
     * 重写拖拽不可用
     *
     * @return
     */
    @Override
    public boolean isLongPressDragEnabled() {
        return true;
    }

    @Override
    public boolean isItemViewSwipeEnabled() {
        return super.isItemViewSwipeEnabled();
    }

    private MyListener listener;

    public interface MyListener {
        void onMoved(List<PaymentData> list);
    }

}
