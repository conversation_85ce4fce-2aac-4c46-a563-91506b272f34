package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.view.View;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmMallBinding;
import com.yxl.cashier_retail.ui.activity.MallCartActivity;
import com.yxl.cashier_retail.ui.activity.MallGoodsActivity;
import com.yxl.cashier_retail.ui.activity.MallGoodsInfoActivity;
import com.yxl.cashier_retail.ui.adapter.MallBrandAdapter;
import com.yxl.cashier_retail.ui.adapter.MallCateAdapter;
import com.yxl.cashier_retail.ui.adapter.MallGoodsAdapter;
import com.yxl.cashier_retail.ui.adapter.MallLabelAdapter;
import com.yxl.cashier_retail.ui.adapter.MallSubsidyAdapter;
import com.yxl.cashier_retail.ui.bean.MallCateData;
import com.yxl.cashier_retail.ui.bean.MallGoodsData;
import com.yxl.cashier_retail.ui.bean.MallLabelData;
import com.yxl.cashier_retail.ui.bean.MallMarketingData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:补货-商城
 * Created by jingang on 2024/6/3
 */
@SuppressLint("NonConstantResourceId")
public class MallFragment extends BaseFragment<FmMallBinding> implements View.OnClickListener {

    //分类
    private MallCateAdapter cateAdapter;
    private List<MallCateData> cateList = new ArrayList<>();

    //品牌精选
    private MallBrandAdapter brandAdapter;
    private List<MallMarketingData.PpjxBean> brandList = new ArrayList<>();

    //硬核补贴
    private MallSubsidyAdapter subsidyAdapter;
    private List<MallMarketingData.ActivityBean.ActivityListBean> subsidyList = new ArrayList<>();

    //商品标签
    private MallLabelAdapter labelAdapter;
    private List<MallLabelData> labelList = new ArrayList<>();
    private int labelId;

    //商品
    private MallGoodsAdapter mAdapter;
    private List<MallGoodsData> dataList = new ArrayList<>();

    @Override
    protected FmMallBinding getViewBinding() {
        return FmMallBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.tvCredit.setOnClickListener(this);
        mBinding.tvOrder.setOnClickListener(this);
        mBinding.relCart.setOnClickListener(this);
        mBinding.tvCoupons.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected void initData() {
        getCateList();
        getMarketing();
        getLabelList();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvCredit:
                //赊销
                break;
            case R.id.tvOrder:
                //订单
                break;
            case R.id.relCart:
                //购物车
                goToActivity(MallCartActivity.class);
                break;
            case R.id.tvCoupons:
                //优惠券
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //分类
        cateAdapter = new MallCateAdapter(getActivity());
        mBinding.rvCate.setAdapter(cateAdapter);
        cateAdapter.setOnItemClickListener((view, position) -> {
            startActivity(new Intent(getActivity(), MallGoodsActivity.class)
                    .putExtra("unique", cateList.get(position).getGoods_kind_unique())
                    .putExtra("title", cateList.get(position).getGoods_kind_name())
            );
        });

        //品牌精选
        brandAdapter = new MallBrandAdapter(getActivity());
        mBinding.rvBrand.setAdapter(brandAdapter);
        brandAdapter.setOnItemClickListener((view, position) -> {

        });

        //硬核补贴
        subsidyAdapter = new MallSubsidyAdapter(getActivity());
        mBinding.rvSubsidy.setAdapter(subsidyAdapter);
        subsidyAdapter.setOnItemClickListener((view, position) -> {

        });

        //商品标签
        labelAdapter = new MallLabelAdapter(getActivity());
        mBinding.rvLabel.setAdapter(labelAdapter);
        labelAdapter.setOnItemClickListener((view, position) -> {
            if (!labelList.get(position).isSelect()) {
                for (int i = 0; i < labelList.size(); i++) {
                    if (labelList.get(i).isSelect()) {
                        labelList.get(i).setSelect(false);
                        labelAdapter.notifyItemChanged(i);
                    }
                }
                labelList.get(position).setSelect(true);
                labelAdapter.notifyItemChanged(position);
                labelId = labelList.get(position).getLabel_id();
                page = 1;
                getGoodsList();
            }
        });

        //商品
        mAdapter = new MallGoodsAdapter(getActivity());
        mBinding.rvGoods.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            startActivity(new Intent(getActivity(), MallGoodsInfoActivity.class)
                    .putExtra("goodsId", dataList.get(position).getGoods_id())
            );
        });
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getGoodsList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getCateList();
                getMarketing();
                getLabelList();
            }
        });
    }

    /**
     * 分类列表
     */
    private void getCateList() {
        Map<String, Object> params = new HashMap<>();
        params.put("area_dict_num", getAreaDictNum());
        RXHttpUtil.requestByFormPostAsResponseList(getActivity(),
                ZURL.getMallCateList(),
                params,
                MallCateData.class,
                new RequestListListener<MallCateData>() {
                    @Override
                    public void onResult(List<MallCateData> list) {
                        cateList.clear();
                        cateList.addAll(list);
                        cateAdapter.setDataList(cateList);
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 首页活动列表（banner、硬核补贴、品牌精选、预售）
     */
    private void getMarketing() {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("area_dict_num", getAreaDictNum());
        RXHttpUtil.requestByFormPostAsOriginalResponse(getActivity(),
                ZURL.getMallMarketing(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        MallMarketingData data = new Gson().fromJson(s, MallMarketingData.class);
                        if (data.getStatus() == 1) {
                            //品牌精选
                            if (data.getPpjx() != null) {
                                mBinding.linBrand.setVisibility(View.VISIBLE);
                                brandList.clear();
                                brandList.addAll(data.getPpjx());
                                brandAdapter.setDataList(brandList);
                            } else {
                                mBinding.linBrand.setVisibility(View.GONE);
                            }

                            //硬核补贴
                            if (data.getActivity() != null) {
                                mBinding.linSubsidy.setVisibility(View.VISIBLE);
                                if (data.getActivity().getActivityList() != null) {
                                    subsidyList.clear();
                                    subsidyList.addAll(data.getActivity().getActivityList());
                                    subsidyAdapter.setDataList(subsidyList);
                                } else {
                                    mBinding.linSubsidy.setVisibility(View.GONE);
                                }
                            } else {
                                mBinding.linSubsidy.setVisibility(View.GONE);
                            }
                        } else {
                            showToast(1, data.getMsg());
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 标签列表
     */
    private void getLabelList() {
        RXHttpUtil.requestByFormPostAsResponseList(getActivity(),
                ZURL.getMallLabelList(),
                new HashMap<>(),
                MallLabelData.class,
                new RequestListListener<MallLabelData>() {
                    @Override
                    public void onResult(List<MallLabelData> list) {
                        labelList.clear();
                        labelList.addAll(list);
                        if (labelList.size() > 0) {
                            labelList.get(0).setSelect(true);
                            labelId = labelList.get(0).getLabel_id();
                        }
                        labelAdapter.setDataList(labelList);
                        getGoodsList();
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 商品列表
     */
    private void getGoodsList() {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("area_dict_num", getAreaDictNum());
        params.put("page", page);
        params.put("limit", Constants.limit);
        params.put("sort_collection", "asc");
        params.put("label_id", labelId);
        RXHttpUtil.requestByFormPostAsResponseList(getActivity(),
                ZURL.getMallGoodList(),
                params,
                MallGoodsData.class,
                new RequestListListener<MallGoodsData>() {
                    @Override
                    public void onResult(List<MallGoodsData> list) {
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                        if (page == 1) {
                            dataList.clear();
                        }
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            mBinding.rvGoods.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.rvGoods.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                        if (dataList.size() > 0) {
                            mBinding.rvGoods.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.rvGoods.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

}
