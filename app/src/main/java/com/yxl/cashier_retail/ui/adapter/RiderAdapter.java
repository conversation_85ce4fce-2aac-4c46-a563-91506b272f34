package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.RiderData;

/**
 * Describe:骑手列表-弹窗（适配器）
 * Created by jingang on 2024/6/18
 */
public class RiderAdapter extends BaseAdapter<RiderData> {

    public RiderAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_rider;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName, tvMobile, tvTime, tvEdit;
        tvName = holder.getView(R.id.tvItemName);
        tvMobile = holder.getView(R.id.tvItemMobile);
        tvTime = holder.getView(R.id.tvItemTime);
        tvEdit = holder.getView(R.id.tvItemEdit);

        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
            tvMobile.setTextColor(mContext.getResources().getColor(R.color.white));
            tvTime.setTextColor(mContext.getResources().getColor(R.color.white));
            tvEdit.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
            tvMobile.setTextColor(mContext.getResources().getColor(R.color.black));
            tvTime.setTextColor(mContext.getResources().getColor(R.color.black));
            tvEdit.setTextColor(mContext.getResources().getColor(R.color.black));
            if (position % 2 == 0) {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
            } else {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
            }
        }
        tvName.setText(mDataList.get(position).getCourier_name());
        tvMobile.setText(mDataList.get(position).getCourier_phone());
        tvTime.setText(TextUtils.isEmpty(mDataList.get(position).getCreate_times()) ? "-" : mDataList.get(position).getCreate_times());
        tvEdit.setText(TextUtils.isEmpty(mDataList.get(position).getUpdate_times()) ? "-" : mDataList.get(position).getUpdate_times());
    }
}
