package com.yxl.cashier_retail.ui.bean;

import java.util.List;

/**
 * Describe:查询-订单列表（实体类）-写出花来
 * Created by jingang on 2024/8/24
 */
public class QueryOrderListData {
    /**
     * status : 0
     * msg : 查询成功！
     * data : [{"sale_list_datetime":"2024-08-22 17:10:14","sale_list_address":"","sale_list_unique_str":"1724317641065286","refundState":"-1","refundCount":0,"sale_list_totalCount":"4","sale_list_unique":1724317641065286,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"支付宝","sale_list_remarks":"","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"55.92","saleListPayment":2,"saleListPur":"0.04","returnType":1},{"sale_list_datetime":"2024-08-22 16:38:27","sale_list_address":"","sale_list_unique_str":"1724315907784486","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1724315907784486,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"支付宝","sale_list_remarks":"","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"13.98","saleListPayment":2,"saleListPur":"0.01","returnType":1},{"sale_list_datetime":"2024-08-22 16:14:02","sale_list_address":"","sale_list_unique_str":"1724314443154586","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1724314443154586,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"支付宝","sale_list_remarks":"","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"13.98","saleListPayment":2,"saleListPur":"0.01","returnType":1},{"sale_list_datetime":"2024-08-22 09:29:55","sale_list_address":"","sale_list_unique_str":"1724290192749486","refundState":"-1","refundCount":0,"sale_list_totalCount":"1","sale_list_unique":1724290192749486,"saleListCode":3,"refundMoney":0,"sale_list_state":"已付款","payMent":"支付宝","sale_list_remarks":"","sale_list_name":"本地订单","sale_list_phone":"","cus_name":"","sale_list_total":"7.00","saleListPayment":2,"saleListPur":"1.32","returnType":1}]
     * data1 : [{"rechargeCode":1,"rechargeMethod":"现金","rechargeMoney":94},{"rechargeCode":6,"rechargeMethod":"金圈平台","rechargeMoney":0.08}]
     * address : [{"payMoney":231.1,"retListTotal":0,"pay_method":1,"sale_list_state":3},{"payMoney":90.88,"retListTotal":0,"pay_method":2,"sale_list_state":3},{"payMoney":71.92,"retListTotal":0,"pay_method":3,"sale_list_state":3},{"payMoney":7.06,"retListTotal":0,"pay_method":5,"sale_list_state":3},{"payMoney":0,"pay_method":"-1"},{"payMoney":33,"pay_method":"-2"},{"payMoney":0,"pay_method":"-5"},{"payMoney":0,"beansUse":0,"count":0,"purSum":0,"pay_method":"-4"},{"payMoney":400.96,"pay_method":"-3"}]
     * cus_data : {"cash_money":0,"sum_return_money":0,"not_arrived_money":0,"shop_beans":0,"plat_beans":0,"shop_coupon_money":0,"sum_sale_money":7.06,"sum_point_goods_money":0,"sum_recharge_money":94.08,"plat_coupon_money":0,"not_cash_money":3549.07}
     */

    private int status;
    private String msg;
    private CusDataBean cus_data;
    private List<DataBean> data;//订单列表
    private List<Data1Bean> data1;//充值统计
    private List<AddressBean> address;//收银统计

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public CusDataBean getCus_data() {
        return cus_data;
    }

    public void setCus_data(CusDataBean cus_data) {
        this.cus_data = cus_data;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public List<Data1Bean> getData1() {
        return data1;
    }

    public void setData1(List<Data1Bean> data1) {
        this.data1 = data1;
    }

    public List<AddressBean> getAddress() {
        return address;
    }

    public void setAddress(List<AddressBean> address) {
        this.address = address;
    }

    public static class CusDataBean {
        /**
         * cash_money : 0.0
         * sum_return_money : 0.0
         * not_arrived_money : 0.0
         * shop_beans : 0
         * plat_beans : 0
         * shop_coupon_money : 0.0
         * sum_sale_money : 7.06
         * sum_point_goods_money : 0.0
         * sum_recharge_money : 94.08
         * plat_coupon_money : 0.0
         * not_cash_money : 3549.07
         */

        private double cash_money;//已提现金额
        private double sum_return_money;//退款金额
        private double not_arrived_money;//未到账金额
        private int shop_beans;//店铺补贴百货豆
        private int plat_beans;//平台补贴百货豆
        private double shop_coupon_money;//店铺优惠券
        private double sum_sale_money;//会员消费·
        private double sum_point_goods_money;//积分商品
        private double sum_recharge_money;//充值金额
        private double plat_coupon_money;//平台优惠券
        private double not_cash_money;//待提现金额

        public double getCash_money() {
            return cash_money;
        }

        public void setCash_money(double cash_money) {
            this.cash_money = cash_money;
        }

        public double getSum_return_money() {
            return sum_return_money;
        }

        public void setSum_return_money(double sum_return_money) {
            this.sum_return_money = sum_return_money;
        }

        public double getNot_arrived_money() {
            return not_arrived_money;
        }

        public void setNot_arrived_money(double not_arrived_money) {
            this.not_arrived_money = not_arrived_money;
        }

        public int getShop_beans() {
            return shop_beans;
        }

        public void setShop_beans(int shop_beans) {
            this.shop_beans = shop_beans;
        }

        public int getPlat_beans() {
            return plat_beans;
        }

        public void setPlat_beans(int plat_beans) {
            this.plat_beans = plat_beans;
        }

        public double getShop_coupon_money() {
            return shop_coupon_money;
        }

        public void setShop_coupon_money(double shop_coupon_money) {
            this.shop_coupon_money = shop_coupon_money;
        }

        public double getSum_sale_money() {
            return sum_sale_money;
        }

        public void setSum_sale_money(double sum_sale_money) {
            this.sum_sale_money = sum_sale_money;
        }

        public double getSum_point_goods_money() {
            return sum_point_goods_money;
        }

        public void setSum_point_goods_money(double sum_point_goods_money) {
            this.sum_point_goods_money = sum_point_goods_money;
        }

        public double getSum_recharge_money() {
            return sum_recharge_money;
        }

        public void setSum_recharge_money(double sum_recharge_money) {
            this.sum_recharge_money = sum_recharge_money;
        }

        public double getPlat_coupon_money() {
            return plat_coupon_money;
        }

        public void setPlat_coupon_money(double plat_coupon_money) {
            this.plat_coupon_money = plat_coupon_money;
        }

        public double getNot_cash_money() {
            return not_cash_money;
        }

        public void setNot_cash_money(double not_cash_money) {
            this.not_cash_money = not_cash_money;
        }
    }

    public static class DataBean {
        /**
         * sale_list_datetime : 2024-08-22 17:10:14
         * sale_list_address :
         * sale_list_unique_str : 1724317641065286
         * refundState : -1
         * refundCount : 0.0
         * sale_list_totalCount : 4
         * sale_list_unique : 1724317641065286
         * saleListCode : 3
         * refundMoney : 0.0
         * sale_list_state : 已付款
         * payMent : 支付宝
         * sale_list_remarks :
         * sale_list_name : 本地订单
         * sale_list_phone :
         * cus_name :
         * sale_list_total : 55.92
         * saleListPayment : 2
         * saleListPur : 0.04
         * returnType : 1
         */

        private boolean select;
        private String sale_list_datetime;//下单日期
        private String sale_list_address;//配送地址
        private String sale_list_unique_str;//订单编号
        private String refundState;//退款状态 1.待处理 2.已处理 3.已退回 4.驳回
        private double refundCount;//退款商品数量
        private String sale_list_totalCount;//订单总数量
        private String sale_list_unique;//订单编号
        private int saleListCode;
        private double refundMoney;//退款金额
        private String sale_list_state;//订单付款状态名称
        private String payMent;//支付方式名称
        private String sale_list_remarks;
        private String sale_list_name;//收件人姓名
        private String sale_list_phone;//收件人电话
        private String cus_name;
        private String sale_list_total;//订单总金额
        private int saleListPayment;//支付方式 1.现金 2.支付宝 3.微信 4.银行卡 5.储值卡 8.混合支付 10.积分兑换 13.金圈平台 全部传空
        private String saleListPur;//进价
        private int returnType;//1.普通订单 2.退款单

        public boolean isSelect() {
            return select;
        }

        public void setSelect(boolean select) {
            this.select = select;
        }

        public String getSale_list_datetime() {
            return sale_list_datetime;
        }

        public void setSale_list_datetime(String sale_list_datetime) {
            this.sale_list_datetime = sale_list_datetime;
        }

        public String getSale_list_address() {
            return sale_list_address;
        }

        public void setSale_list_address(String sale_list_address) {
            this.sale_list_address = sale_list_address;
        }

        public String getSale_list_unique_str() {
            return sale_list_unique_str;
        }

        public void setSale_list_unique_str(String sale_list_unique_str) {
            this.sale_list_unique_str = sale_list_unique_str;
        }

        public String getRefundState() {
            return refundState;
        }

        public void setRefundState(String refundState) {
            this.refundState = refundState;
        }

        public double getRefundCount() {
            return refundCount;
        }

        public void setRefundCount(double refundCount) {
            this.refundCount = refundCount;
        }

        public String getSale_list_totalCount() {
            return sale_list_totalCount;
        }

        public void setSale_list_totalCount(String sale_list_totalCount) {
            this.sale_list_totalCount = sale_list_totalCount;
        }

        public String getSale_list_unique() {
            return sale_list_unique;
        }

        public void setSale_list_unique(String sale_list_unique) {
            this.sale_list_unique = sale_list_unique;
        }

        public int getSaleListCode() {
            return saleListCode;
        }

        public void setSaleListCode(int saleListCode) {
            this.saleListCode = saleListCode;
        }

        public double getRefundMoney() {
            return refundMoney;
        }

        public void setRefundMoney(double refundMoney) {
            this.refundMoney = refundMoney;
        }

        public String getSale_list_state() {
            return sale_list_state;
        }

        public void setSale_list_state(String sale_list_state) {
            this.sale_list_state = sale_list_state;
        }

        public String getPayMent() {
            return payMent;
        }

        public void setPayMent(String payMent) {
            this.payMent = payMent;
        }

        public String getSale_list_remarks() {
            return sale_list_remarks;
        }

        public void setSale_list_remarks(String sale_list_remarks) {
            this.sale_list_remarks = sale_list_remarks;
        }

        public String getSale_list_name() {
            return sale_list_name;
        }

        public void setSale_list_name(String sale_list_name) {
            this.sale_list_name = sale_list_name;
        }

        public String getSale_list_phone() {
            return sale_list_phone;
        }

        public void setSale_list_phone(String sale_list_phone) {
            this.sale_list_phone = sale_list_phone;
        }

        public String getCus_name() {
            return cus_name;
        }

        public void setCus_name(String cus_name) {
            this.cus_name = cus_name;
        }

        public String getSale_list_total() {
            return sale_list_total;
        }

        public void setSale_list_total(String sale_list_total) {
            this.sale_list_total = sale_list_total;
        }

        public int getSaleListPayment() {
            return saleListPayment;
        }

        public void setSaleListPayment(int saleListPayment) {
            this.saleListPayment = saleListPayment;
        }

        public String getSaleListPur() {
            return saleListPur;
        }

        public void setSaleListPur(String saleListPur) {
            this.saleListPur = saleListPur;
        }

        public int getReturnType() {
            return returnType;
        }

        public void setReturnType(int returnType) {
            this.returnType = returnType;
        }
    }

    public static class Data1Bean {
        /**
         * rechargeCode : 1
         * rechargeMethod : 现金
         * rechargeMoney : 94.0
         */

        private int rechargeCode;
        private String rechargeMethod;
        private double rechargeMoney;

        public int getRechargeCode() {
            return rechargeCode;
        }

        public void setRechargeCode(int rechargeCode) {
            this.rechargeCode = rechargeCode;
        }

        public String getRechargeMethod() {
            return rechargeMethod;
        }

        public void setRechargeMethod(String rechargeMethod) {
            this.rechargeMethod = rechargeMethod;
        }

        public double getRechargeMoney() {
            return rechargeMoney;
        }

        public void setRechargeMoney(double rechargeMoney) {
            this.rechargeMoney = rechargeMoney;
        }
    }

    public static class AddressBean {
        /**
         * payMoney : 231.1
         * retListTotal : 0.0
         * pay_method : 1
         * sale_list_state : 3
         * beansUse : 0.0
         * count : 0
         * purSum : 0.0
         */

        private double payMoney;//金额
        private double retListTotal;
        private String pay_method;//1.现金 2.支付宝 3.微信 4.银行卡 5.储值卡 6.美团 7.饿了吗 9.免密 10.积分
        //11.百货豆 -1.赊账 -2.订单数量 -3.营业额 -4.小程序 -5.订单利润
        private int sale_list_state;
        private double beansUse;//百货豆抵扣数量
        private int count;//订单数量
        private double purSum;

        public double getPayMoney() {
            return payMoney;
        }

        public void setPayMoney(double payMoney) {
            this.payMoney = payMoney;
        }

        public double getRetListTotal() {
            return retListTotal;
        }

        public void setRetListTotal(double retListTotal) {
            this.retListTotal = retListTotal;
        }

        public String getPay_method() {
            return pay_method;
        }

        public void setPay_method(String pay_method) {
            this.pay_method = pay_method;
        }

        public int getSale_list_state() {
            return sale_list_state;
        }

        public void setSale_list_state(int sale_list_state) {
            this.sale_list_state = sale_list_state;
        }

        public double getBeansUse() {
            return beansUse;
        }

        public void setBeansUse(double beansUse) {
            this.beansUse = beansUse;
        }

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public double getPurSum() {
            return purSum;
        }

        public void setPurSum(double purSum) {
            this.purSum = purSum;
        }
    }
}
