package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.RefundRefuseData;

import java.util.List;

/**
 * Describe:退款-拒绝话术（适配器）
 * Created by jingang on 2024/6/19
 */
public class RefundRefuseAdapter extends BaseAdapter<RefundRefuseData> {

    public RefundRefuseAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_refund_refuse;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        TextView tvName = holder.getView(R.id.tvItemName);
        tvName.setText(mDataList.get(position).getMsg());
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName = holder.getView(R.id.tvItemName);
        ImageView ivDel, ivEdit;
        ivDel = holder.getView(R.id.ivItemDel);
        ivEdit = holder.getView(R.id.ivItemEdit);

        tvName.setText(mDataList.get(position).getMsg());
        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(v, position));
            ivDel.setOnClickListener(v -> listener.onDelClick(v, position));
            ivEdit.setOnClickListener(v -> listener.onEditClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onDelClick(View view, int position);

        void onEditClick(View view, int position);
    }
}
