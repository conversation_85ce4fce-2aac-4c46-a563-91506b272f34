package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:商城-分类（实体类）
 * Created by jingang on 2024/6/3
 */
public class MallCateData implements Serializable {
    /**
     * goods_kind_name : 休闲副食
     * shopUnique : 0
     * area_dict_num : 371302
     * goodkindTwo : [{"goods_kind_name":"饼干糕点","goodsclass_parentid":1000000,"goods_kind_unique":1000001},{"goods_kind_name":"休闲即食","goodsclass_parentid":1000000,"goods_kind_unique":1000009},{"goods_kind_name":"糖巧果冻","goodsclass_parentid":1000000,"goods_kind_unique":1000005},{"goods_kind_name":"膨化食品","goodsclass_parentid":1000000,"goods_kind_unique":1000002},{"goods_kind_name":"炒货瓜子","goodsclass_parentid":1000000,"goods_kind_unique":1000004},{"goods_kind_name":"干果蜜饯","goodsclass_parentid":1000000,"goods_kind_unique":1000003}]
     * goods_kind_image : image/1000000/f45a43d9-1b6b-40fb-b1b4-edd342c455ca.png
     * goods_kind_unique : 1000000
     */
    private String goods_kind_name;//名称
    private String shopUnique;//店铺编号
    private String area_dict_num;//区域编号
    private String goods_kind_image;//图片
    private String goods_kind_unique;//分类编号
    private List<GoodkindTwoBean> goodkindTwo;

    public String getGoods_kind_name() {
        return goods_kind_name;
    }

    public void setGoods_kind_name(String goods_kind_name) {
        this.goods_kind_name = goods_kind_name;
    }

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getArea_dict_num() {
        return area_dict_num;
    }

    public void setArea_dict_num(String area_dict_num) {
        this.area_dict_num = area_dict_num;
    }

    public String getGoods_kind_image() {
        return goods_kind_image;
    }

    public void setGoods_kind_image(String goods_kind_image) {
        this.goods_kind_image = goods_kind_image;
    }

    public String getGoods_kind_unique() {
        return goods_kind_unique;
    }

    public void setGoods_kind_unique(String goods_kind_unique) {
        this.goods_kind_unique = goods_kind_unique;
    }

    public List<GoodkindTwoBean> getGoodkindTwo() {
        return goodkindTwo;
    }

    public void setGoodkindTwo(List<GoodkindTwoBean> goodkindTwo) {
        this.goodkindTwo = goodkindTwo;
    }

    public static class GoodkindTwoBean {
        /**
         * goods_kind_name : 饼干糕点
         * goodsclass_parentid : 1000000
         * goods_kind_unique : 1000001
         */

        private String goods_kind_name;//名称
        private String goodsclass_parentid;//id
        private String goods_kind_unique;//编号

        public String getGoods_kind_name() {
            return goods_kind_name;
        }

        public void setGoods_kind_name(String goods_kind_name) {
            this.goods_kind_name = goods_kind_name;
        }

        public String getGoodsclass_parentid() {
            return goodsclass_parentid;
        }

        public void setGoodsclass_parentid(String goodsclass_parentid) {
            this.goodsclass_parentid = goodsclass_parentid;
        }

        public String getGoods_kind_unique() {
            return goods_kind_unique;
        }

        public void setGoods_kind_unique(String goods_kind_unique) {
            this.goods_kind_unique = goods_kind_unique;
        }
    }
}
