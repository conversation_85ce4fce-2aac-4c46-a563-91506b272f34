package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.OrderGoodsData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:订单内商品列表（适配器）
 * Created by jingang on 2024/5/21
 */
public class OrderGoodsAdapter extends BaseAdapter<OrderGoodsData> {
    /*一样的结果，非得定义不同的名称*/
    private int type;//0.订单详情 1.退款详情

    public OrderGoodsAdapter(Context context, int type) {
        super(context);
        this.type = type;
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_order_goods;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName, tvRefund, tvPrice, tvCount, tvTotal;
        tvName = holder.getView(R.id.tvItemName);
        tvRefund = holder.getView(R.id.tvItemRefund);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTotal = holder.getView(R.id.tvItemTotal);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
        }
        switch (type) {
            case 1:
                tvName.setText(mDataList.get(position).getGoodsName());
                tvPrice.setText(DFUtils.getNum2(mDataList.get(position).getRetListDetailPrice()));
                if (TextUtils.isEmpty(mDataList.get(position).getGoodsUnit())) {
                    tvCount.setText(DFUtils.getNum4(mDataList.get(position).getRetListDetailCount()) + mDataList.get(position).getGoodsUnit());
                } else {
                    tvCount.setText(DFUtils.getNum4(mDataList.get(position).getRetListDetailCount()));
                }
                tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getRetListDetailPrice() * mDataList.get(position).getRetListDetailCount()));
                break;
            default:
                tvName.setText(mDataList.get(position).getGoodsName());
                tvPrice.setText(DFUtils.getNum2(mDataList.get(position).getSaleListDetailPrice()));
                if (TextUtils.isEmpty(mDataList.get(position).getGoods_unit())) {
                    tvCount.setText(DFUtils.getNum4(mDataList.get(position).getSaleListDetailCount()));
                } else {
                    tvCount.setText(DFUtils.getNum4(mDataList.get(position).getSaleListDetailCount()) + mDataList.get(position).getGoods_unit());
                }
                tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getSubTotal()));
                break;
        }
    }
}