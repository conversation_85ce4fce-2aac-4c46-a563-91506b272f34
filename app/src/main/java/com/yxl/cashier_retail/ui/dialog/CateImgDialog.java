package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshListener;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogCateImgBinding;
import com.yxl.cashier_retail.databinding.DialogKefuBinding;
import com.yxl.cashier_retail.ui.adapter.CateImgDialogAdapter;
import com.yxl.cashier_retail.ui.bean.CateImgData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:dialog（分类图标）
 * Created by jingang on 2024/8/21
 */
@SuppressLint("NonConstantResourceId")
public class CateImgDialog extends BaseDialog<DialogCateImgBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private static int id;

    private CateImgDialogAdapter mAdapter;
    private List<CateImgData> dataList = new ArrayList<>();

    public static void showDialog(Activity activity, int id, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        CateImgDialog.id = id;
        CateImgDialog.listener = listener;
        CateImgDialog dialog = new CateImgDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 7);
        dialog.show();
    }

    public CateImgDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        setAdapter();
        getCateImg();
    }

    @Override
    protected DialogCateImgBinding getViewBinding() {
        return DialogCateImgBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new CateImgDialogAdapter(getContext());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (listener != null) {
                listener.onConfirm(dataList.get(position).getGoods_kind_icon_id(), dataList.get(position).getGoods_kind_icon_picture());
                dismiss();
            }
        });
        mBinding.smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            getCateImg();
        });
        mBinding.smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 分类图标
     */
    private void getCateImg() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shop_unique", getShopUnique());
        map.put("icon_type", 1);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getCateImg(),
                map,
                CateImgData.class,
                new RequestListListener<CateImgData>() {
                    @Override
                    public void onResult(List<CateImgData> list) {
                        hideDialog();
                        mBinding.smartRefreshLayout.finishRefresh();
                        dataList.clear();
                        dataList.addAll(list);
                        for (int i = 0; i < dataList.size(); i++) {
                            if (dataList.get(i).getGoods_kind_icon_id() == id) {
                                dataList.get(i).setCheck(true);
                            }
                        }
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(int id, String img);
    }
}
