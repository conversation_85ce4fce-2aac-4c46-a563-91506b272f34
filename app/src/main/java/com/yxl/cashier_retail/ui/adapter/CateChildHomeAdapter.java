package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.CateChildData;

/**
 * Describe:首页二级分类
 * Created by jingang on 2024/8/21
 */
public class CateChildHomeAdapter extends BaseAdapter<CateChildData> {

    public CateChildHomeAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_cate_child_home;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvDefault;
        ImageView ivEdit, ivDel;
        tvName = holder.getView(R.id.tvItemName);
        tvDefault = holder.getView(R.id.tvItemDefault);
        ivEdit = holder.getView(R.id.ivItemEdit);
        ivDel = holder.getView(R.id.ivItemDel);

        tvName.setText(mDataList.get(position).getKindName());
        if (TextUtils.isEmpty(mDataList.get(position).getEditType())) {
            tvDefault.setVisibility(View.VISIBLE);
            ivEdit.setVisibility(View.GONE);
            ivDel.setVisibility(View.GONE);
        } else {
            if (mDataList.get(position).getEditType().equals("2")) {
                tvDefault.setVisibility(View.GONE);
                ivEdit.setVisibility(View.VISIBLE);
                ivDel.setVisibility(View.VISIBLE);
            } else {
                tvDefault.setVisibility(View.VISIBLE);
                ivEdit.setVisibility(View.GONE);
                ivDel.setVisibility(View.GONE);
            }
        }
        if (listener != null) {
            ivEdit.setOnClickListener(v -> listener.onEditClick(v, position));
            ivDel.setOnClickListener(v -> listener.onDelClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onEditClick(View view, int position);

        void onDelClick(View view, int position);
    }
}
