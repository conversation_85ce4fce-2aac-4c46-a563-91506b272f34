package com.yxl.cashier_retail.ui.bean;

import java.util.List;

/**
 * Describe: 网单订单列表（实体类）
 * Created by jingang on 2025/6/6
 */
public class NetListData {

    /**
     * status : 0
     * msg : 查询成功！
     * data : [{"sale_list_datetime":"2025.06.06 11:16","sale_list_address":"山东省临沂市兰山区南京路与卧虎山路交汇处北B座七楼","sale_list_unique_str":"1747278973163","addr_longitude":118.30527977,"sale_list_unique":1747278973163,"sale_list_state":"已付款","sale_list_name":"赵","sale_list_number":1,"sale_list_phone":"15318538362","sale_list_total":0.01,"sale_list_handlestate":6,"receipt_datetime":"2025.05.15 11:55","addr_latitude":35.13380929,"goods_weight":0},{"sale_list_datetime":"2025.06.06 11:16","sale_list_address":"","sale_list_number":1,"sale_list_unique_str":"1747279437621","sale_list_phone":"15318538362","sale_list_total":0.05,"sale_list_handlestate":6,"sale_list_unique":1747279437621,"sale_list_state":"已付款","receipt_datetime":"2025.05.15 11:24","sale_list_name":"微信用户","goods_weight":0},{"sale_list_datetime":"2025.06.06 11:16","sale_list_address":"","sale_list_number":1,"sale_list_unique_str":"1747279559464","sale_list_phone":"15318538362","sale_list_total":0.01,"sale_list_handlestate":6,"sale_list_unique":1747279559464,"sale_list_state":"已付款","receipt_datetime":"2025.05.15 11:41","sale_list_name":"微信用户","goods_weight":0},{"sale_list_datetime":"2025.06.06 11:16","sale_list_address":"","sale_list_number":1,"sale_list_unique_str":"1747280652557","sale_list_phone":"15318538362","sale_list_total":0.01,"sale_list_handlestate":6,"sale_list_unique":1747280652557,"sale_list_state":"已付款","receipt_datetime":"2025.05.15 11:45","sale_list_name":"微信用户","goods_weight":0},{"sale_list_datetime":"2025.06.06 11:16","sale_list_address":"","sale_list_number":1,"sale_list_unique_str":"1747280972352","sale_list_phone":"15318538362","sale_list_total":0.01,"sale_list_handlestate":6,"sale_list_unique":1747280972352,"sale_list_state":"已付款","receipt_datetime":"2025.05.15 11:53","sale_list_name":"微信用户","goods_weight":0},{"sale_list_datetime":"2025.06.06 11:16","sale_list_address":"","sale_list_number":1,"sale_list_unique_str":"1747281205126","sale_list_phone":"15318538362","sale_list_total":0.01,"sale_list_handlestate":6,"sale_list_unique":1747281205126,"sale_list_state":"已付款","receipt_datetime":"2025.05.15 11:53","sale_list_name":"微信用户","goods_weight":0}]
     * data1 : null
     * address : null
     * cus_data : null
     */

    private int status;
    private String msg;
    private Object data1;
    private Object address;
    private Object cus_data;
    private List<DataBean> data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getData1() {
        return data1;
    }

    public void setData1(Object data1) {
        this.data1 = data1;
    }

    public Object getAddress() {
        return address;
    }

    public void setAddress(Object address) {
        this.address = address;
    }

    public Object getCus_data() {
        return cus_data;
    }

    public void setCus_data(Object cus_data) {
        this.cus_data = cus_data;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * sale_list_datetime : 2025.06.06 11:16
         * sale_list_address : 山东省临沂市兰山区南京路与卧虎山路交汇处北B座七楼
         * sale_list_unique_str : 1747278973163
         * addr_longitude : 118.30527977
         * sale_list_unique : 1747278973163
         * sale_list_state : 已付款
         * sale_list_name : 赵
         * sale_list_number : 1
         * sale_list_phone : 15318538362
         * sale_list_total : 0.01
         * sale_list_handlestate : 6
         * receipt_datetime : 2025.05.15 11:55
         * addr_latitude : 35.13380929
         * goods_weight : 0.0
         */
        private boolean select;
        private String sale_list_datetime;//下单时间
        private String sale_list_address;//配送地址
        private String sale_list_unique_str;//订单编号字符串形式，订单号取这个值
        private String addr_longitude;//配送地址纬度
        private String sale_list_unique;//订单号，有可能溢出导致异常，sale_list_unique_str替代
        private String sale_list_state;//支付状态：1、2：待支付；3、已支付
        private String sale_list_name;//收货人
        private String sale_list_number;//宁宇用，当然订单编号
        private String sale_list_phone;//收货人联系方式
        private double sale_list_total;//订单总金额
        private int sale_list_handlestate;//订单状态
        private String receipt_datetime;//收货时间
        private String addr_latitude;//配送地址经度
        private double goods_weight;//商品重量

        public boolean isSelect() {
            return select;
        }

        public void setSelect(boolean select) {
            this.select = select;
        }

        public String getSale_list_datetime() {
            return sale_list_datetime;
        }

        public void setSale_list_datetime(String sale_list_datetime) {
            this.sale_list_datetime = sale_list_datetime;
        }

        public String getSale_list_address() {
            return sale_list_address;
        }

        public void setSale_list_address(String sale_list_address) {
            this.sale_list_address = sale_list_address;
        }

        public String getSale_list_unique_str() {
            return sale_list_unique_str;
        }

        public void setSale_list_unique_str(String sale_list_unique_str) {
            this.sale_list_unique_str = sale_list_unique_str;
        }

        public String getAddr_longitude() {
            return addr_longitude;
        }

        public void setAddr_longitude(String addr_longitude) {
            this.addr_longitude = addr_longitude;
        }

        public String getSale_list_unique() {
            return sale_list_unique;
        }

        public void setSale_list_unique(String sale_list_unique) {
            this.sale_list_unique = sale_list_unique;
        }

        public String getSale_list_state() {
            return sale_list_state;
        }

        public void setSale_list_state(String sale_list_state) {
            this.sale_list_state = sale_list_state;
        }

        public String getSale_list_name() {
            return sale_list_name;
        }

        public void setSale_list_name(String sale_list_name) {
            this.sale_list_name = sale_list_name;
        }

        public String getSale_list_number() {
            return sale_list_number;
        }

        public void setSale_list_number(String sale_list_number) {
            this.sale_list_number = sale_list_number;
        }

        public String getSale_list_phone() {
            return sale_list_phone;
        }

        public void setSale_list_phone(String sale_list_phone) {
            this.sale_list_phone = sale_list_phone;
        }

        public double getSale_list_total() {
            return sale_list_total;
        }

        public void setSale_list_total(double sale_list_total) {
            this.sale_list_total = sale_list_total;
        }

        public int getSale_list_handlestate() {
            return sale_list_handlestate;
        }

        public void setSale_list_handlestate(int sale_list_handlestate) {
            this.sale_list_handlestate = sale_list_handlestate;
        }

        public String getReceipt_datetime() {
            return receipt_datetime;
        }

        public void setReceipt_datetime(String receipt_datetime) {
            this.receipt_datetime = receipt_datetime;
        }

        public String getAddr_latitude() {
            return addr_latitude;
        }

        public void setAddr_latitude(String addr_latitude) {
            this.addr_latitude = addr_latitude;
        }

        public double getGoods_weight() {
            return goods_weight;
        }

        public void setGoods_weight(double goods_weight) {
            this.goods_weight = goods_weight;
        }
    }
}
