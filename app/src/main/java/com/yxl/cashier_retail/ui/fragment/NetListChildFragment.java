package com.yxl.cashier_retail.ui.fragment;

import android.view.View;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.LazyBaseFragment;
import com.yxl.cashier_retail.databinding.FmNetlistChildBinding;
import com.yxl.cashier_retail.ui.adapter.NetListAdapter;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.NetListData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:网单-网单订单
 * Created by jingang on 2025/6/6
 */
public class NetListChildFragment extends LazyBaseFragment<FmNetlistChildBinding> {
    private boolean isVisible,//当前fragment是否显示
            isFirst = true;//首次进入
    private final int status;
    private int pos;

    private NetListAdapter mAdapter;
    private final List<NetListData.DataBean> dataList = new ArrayList<>();

    public NetListChildFragment(int status) {
        this.status = status;
    }

    @Override
    protected FmNetlistChildBinding getViewBinding() {
        return FmNetlistChildBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        setAdapter();
    }

    @Override
    protected void initData() {
    }

    @Override
    protected void onFragmentFirstVisible() {
        super.onFragmentFirstVisible();
        getOrderList();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        this.isVisible = isVisibleToUser;
        if (isVisible && !isFirst) {
            page = 1;
            getOrderList();
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.ORDER_LIST:
                //订单列表
                if (isVisible) {
                    page = 1;
                    getOrderList();
                }
                break;
            case Constants.ORDER_LIST_UPDATE:
                //订单列表局部刷新
                if (isVisible) {
                    if (dataList.size() > pos) {
                        if (status == -1) {
                            dataList.get(pos).setSale_list_handlestate(event.getNum());
                            mAdapter.notifyItemChanged(pos);
                        } else {
                            dataList.remove(pos);
                            mAdapter.remove(pos);
                            if (dataList.size() > 0) {
                                pos = 0;
                                dataList.get(pos).setSelect(true);
                                mAdapter.notifyItemChanged(pos);
                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(dataList.get(pos).getSale_list_unique_str(), Constants.ORDER_INFO));
                            } else {
                                page = 1;
                                getOrderList();
                            }
                        }
                    }
                }
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new NetListAdapter(getActivity());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (!dataList.get(position).isSelect()) {
                for (int i = 0; i < dataList.size(); i++) {
                    if (dataList.get(i).isSelect()) {
                        dataList.get(i).setSelect(false);
                        mAdapter.notifyItemChanged(i);
                    }
                }
                dataList.get(position).setSelect(true);
                mAdapter.notifyItemChanged(position);
                pos = position;
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(dataList.get(position).getSale_list_unique_str(), Constants.ORDER_INFO));
            }
        });
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getOrderList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getOrderList();
            }
        });
    }

    /**
     * 网单订单查询
     */
    public void getOrderList() {
        if (isFirst) {
            isFirst = false;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("flag", true);
        params.put("sale_list_handlestate", status);
        params.put("startTime", NetListFragment.startDate + " 00:00:00");
        params.put("endTime", NetListFragment.endDate + " 23:59:59");
        params.put("pageIndex", page);
        params.put("pageSize", Constants.limit);
        RXHttpUtil.requestByFormPostAsOriginalResponse(getActivity(),
                ZURL.getOrderList(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        NetListData data = new Gson().fromJson(s, NetListData.class);
                        if (data == null) {
                            setEmpty();
                            return;
                        }
                        if (data.getStatus() != 0) {
                            setEmpty();
                            return;
                        }
                        if (page == 1) {
                            mBinding.smartRefreshLayout.finishRefresh();
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.ORDER_COUNT));
                            dataList.clear();
                            if (data.getData() != null) {
                                dataList.addAll(data.getData());
                            }
                            if (!dataList.isEmpty()) {
                                pos = 0;
                                dataList.get(pos).setSelect(true);
                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(dataList.get(pos).getSale_list_unique_str(), Constants.ORDER_INFO));
                            } else {
                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("", Constants.ORDER_INFO));
                            }
                        } else {
                            mBinding.smartRefreshLayout.finishLoadMore();
                            if (data.getData() != null) {
                                dataList.addAll(data.getData());
                            }
                        }
                        if (!dataList.isEmpty()) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData("", Constants.ORDER_INFO));
                        setEmpty();
                    }
                });
    }

    private void setEmpty() {
        mBinding.smartRefreshLayout.finishRefresh();
        mBinding.smartRefreshLayout.finishLoadMore();
        if (!dataList.isEmpty()) {
            mBinding.recyclerView.setVisibility(View.VISIBLE);
            mBinding.linEmpty.setVisibility(View.GONE);
        } else {
            mBinding.recyclerView.setVisibility(View.GONE);
            mBinding.linEmpty.setVisibility(View.VISIBLE);
        }
    }
}