package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmQueryShiftBinding;
import com.yxl.cashier_retail.ui.adapter.QueryShiftRecordAdapter;
import com.yxl.cashier_retail.ui.adapter.ShiftInfoOrderAdapter;
import com.yxl.cashier_retail.ui.adapter.ShiftInfoRechargeAdapter;
import com.yxl.cashier_retail.ui.bean.OrderListData;
import com.yxl.cashier_retail.ui.bean.ShiftRecordData;
import com.yxl.cashier_retail.ui.dialog.DateStartEndDialog;
import com.yxl.cashier_retail.ui.dialog.StaffDialog;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.utils.DateUtils;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:查询-查交班
 * Created by jingang on 2024/5/23
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class QueryShiftFragment extends BaseFragment<FmQueryShiftBinding> implements View.OnClickListener {
    private int staffId = -1;
    private String staffName, startDate, endDate;

    //交接班记录
    private QueryShiftRecordAdapter mAdapter;
    private List<ShiftRecordData> dataList = new ArrayList<>();

    //充值统计、收银统计
    private ShiftInfoRechargeAdapter rechargeAdapter, cashierAdapter;
    private List<ShiftRecordData.DetailBean> rechargeList = new ArrayList<>(),
            cashierList = new ArrayList<>();

    //收银订单
    private ShiftInfoOrderAdapter orderAdapter;
    private List<OrderListData.DataBean> orderList = new ArrayList<>();

    @Override
    protected FmQueryShiftBinding getViewBinding() {
        return FmQueryShiftBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.linType.setOnClickListener(this);
        mBinding.linStartDate.setOnClickListener(this);
        mBinding.linEndDate.setOnClickListener(this);

        mBinding.tvPrint.setOnClickListener(this);
        startDate = DateUtils.getOldDate(0);
        endDate = startDate;
        mBinding.tvStartDate.setText(startDate);
        mBinding.tvEndDate.setText(endDate);
        setAdapter();
    }

    @Override
    protected void initData() {
        getShiftRecord();
    }

    @Override
    public void onClick(View v) {
        if (isQuicklyClick()) {
            return;
        }
        switch (v.getId()) {
            case R.id.linType:
                //选择收银员
                StaffDialog.showDialog(getActivity(), staffId, staffName, mBinding.ivType, (id, name) -> {
                    staffId = id;
                    staffName = name;
                    mBinding.tvType.setText(staffName);
                    page = 1;
                    getShiftRecord();
                });
                break;
            case R.id.linStartDate:
                //开始日期
                showDialogDate(0, mBinding.ivStartDate);
                break;
            case R.id.linEndDate:
                //结束日期
                showDialogDate(1, mBinding.ivEndDate);
                break;
            case R.id.tvPrint:
                //打印交班小票
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //交接班记录
        mAdapter = new QueryShiftRecordAdapter(getActivity());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (!dataList.get(position).isCheck()) {
                for (int i = 0; i < dataList.size(); i++) {
                    if (dataList.get(i).isCheck()) {
                        dataList.get(i).setCheck(false);
                        mAdapter.notifyItemChanged(i);
                    }
                }
                dataList.get(position).setCheck(true);
                mAdapter.notifyItemChanged(position);
                setUI(dataList.get(position));
            }
        });
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getShiftRecord();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getShiftRecord();
            }
        });

        //充值统计
        rechargeAdapter = new ShiftInfoRechargeAdapter(getActivity());
        mBinding.rvRecharge.setAdapter(rechargeAdapter);

        //收银统计
        cashierAdapter = new ShiftInfoRechargeAdapter(getActivity());
        mBinding.rvCashier.setAdapter(cashierAdapter);

        //收银订单
        orderAdapter = new ShiftInfoOrderAdapter(getActivity());
        mBinding.rvOrder.setAdapter(orderAdapter);
    }

    /**
     * dialog（选择日期时间）
     */
    private void showDialogDate(int type, View view) {
        DateStartEndDialog.showDialog(getActivity(),
                view,
                startDate,
                endDate,
                type,
                (startDate, endDate) -> {
                    this.startDate = startDate;
                    this.endDate = endDate;
                    mBinding.tvStartDate.setText(startDate);
                    mBinding.tvEndDate.setText(endDate);
                    //请求数据
                    page = 1;
                    getShiftRecord();
                });
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(ShiftRecordData data) {
        if (data == null) {
            mBinding.tvNothing.setVisibility(View.VISIBLE);
            mBinding.linInfo.setVisibility(View.GONE);
            return;
        }
        mBinding.tvNothing.setVisibility(View.GONE);
        mBinding.linInfo.setVisibility(View.VISIBLE);

        //充值统计
        mBinding.tvTotalRecharge.setText(getRstr(R.string.amount_colon) + DFUtils.getNum2(data.getRechargeSum()));
        rechargeList.clear();
        if (data.getRechargeDetail() != null) {
            rechargeList.addAll(data.getRechargeDetail());
        }
        rechargeAdapter.setDataList(rechargeList);

        //收银统计
        mBinding.tvTotalCashier.setText(getRstr(R.string.amount_colon) + DFUtils.getNum2(data.getSumMoney()));
        cashierList.clear();
        if (data.getDetail() != null) {
            cashierList.addAll(data.getDetail());
        }
        cashierAdapter.setDataList(cashierList);

        //收银订单
        orderList.clear();
        orderAdapter.clear();
        getOrderList(data.getSale_list_cashier(), data.getLogin_datetime(), data.getSign_out_datetime());
    }

    /**
     * 交接班记录
     */
    private void getShiftRecord() {
        mBinding.tvNothing.setVisibility(View.VISIBLE);
        mBinding.linInfo.setVisibility(View.GONE);
        showDialog();
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("sale_list_cashier", staffId);
        params.put("startTime", startDate);
        params.put("endTime", endDate);
        params.put("pageNum", page);
        params.put("pageSize", Constants.limit);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getShiftRecord(),
                params,
                ShiftRecordData.class,
                new RequestListListener<ShiftRecordData>() {
                    @Override
                    public void onResult(List<ShiftRecordData> list) {
                        hideDialog();
                        if (page == 1) {
                            mBinding.smartRefreshLayout.finishRefresh();
                            dataList.clear();
                            if (list.size() > 0) {
                                list.get(0).setCheck(true);
                                setUI(list.get(0));
                            }
                        } else {
                            mBinding.smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        if (page == 1) {
                            mBinding.smartRefreshLayout.finishRefresh();
                            dataList.clear();
                            mAdapter.clear();
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        } else {
                            mBinding.smartRefreshLayout.finishLoadMore();
                        }
                    }
                });
    }

    /**
     * 员工交接班周期内商品销量统计
     *
     * @param unique
     */
    private void getOrderList(String unique, String startDate, String endDate) {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("startTime", startDate);
        if (TextUtils.isEmpty(endDate)) {
            params.put("endTime", DateUtils.getCurrentDate(DateUtils.PATTERN_SECOND));
        } else {
            params.put("endTime", endDate);
        }
        params.put("staffId", unique);
        showDialog();
        RXHttpUtil.requestByFormPostAsOriginalResponse(getActivity(),
                ZURL.getShiftRecordOrder(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        OrderListData data = new Gson().fromJson(s, OrderListData.class);
                        if (data == null) {
                            return;
                        }
                        if (data.getStatus() != Constants.SUCCESS_CODE) {
                            return;
                        }
                        if (data.getData() == null) {
                            return;
                        }
                        orderList.addAll(data.getData());
                        orderAdapter.setDataList(orderList);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }
}
