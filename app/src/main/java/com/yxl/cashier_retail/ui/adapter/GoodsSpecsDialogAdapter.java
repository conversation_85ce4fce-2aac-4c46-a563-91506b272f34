package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.GoodsListData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.utils.StringUtils;

import java.util.List;

/**
 * Describe:选择商品规格-弹窗（适配器）
 * Created by jingang on 2024/6/6
 */
public class GoodsSpecsDialogAdapter extends BaseAdapter<GoodsListData> {

    public GoodsSpecsDialogAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_specs_dialog;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        ImageView ivSelect = holder.getView(R.id.ivItemSelect);
        ivSelect.setSelected(mDataList.get(position).isCheck());
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivSelect, ivImg;
        TextView tvName, tvBarcode, tvPrice, tvUnit;
        ivSelect = holder.getView(R.id.ivItemSelect);
        ivImg = holder.getView(R.id.ivItemImg);
        tvName = holder.getView(R.id.tvItemName);
        tvBarcode = holder.getView(R.id.tvItemBarcode);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvUnit = holder.getView(R.id.tvItemUnit);

        ivSelect.setSelected(mDataList.get(position).isCheck());
        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoodsPicturepath()))
                .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img))
                .into(ivImg);
        tvName.setText(mDataList.get(position).getGoodsName());
        tvBarcode.setText(mDataList.get(position).getGoodsBarcode());
        tvPrice.setText(DFUtils.getNum2(mDataList.get(position).getGoodsInPrice()));
        if (TextUtils.isEmpty(mDataList.get(position).getGoodsUnit())) {
            tvUnit.setText("元");
        } else {
            tvUnit.setText("元/" + mDataList.get(position).getGoodsUnit());
        }
    }
}
