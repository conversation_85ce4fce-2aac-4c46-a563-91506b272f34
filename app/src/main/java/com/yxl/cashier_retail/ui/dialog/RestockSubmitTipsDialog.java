package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogRestockSubmitTipsBinding;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:dialog（自采补货-提交补货计划提示）
 * Created by jingang on 2024/6/6
 */
@SuppressLint("NonConstantResourceId")
public class RestockSubmitTipsDialog extends BaseDialog<DialogRestockSubmitTipsBinding> implements View.OnClickListener {

    public static void showDialog(Activity activity, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        RestockSubmitTipsDialog.listener = listener;
        RestockSubmitTipsDialog dialog = new RestockSubmitTipsDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 3, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public RestockSubmitTipsDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.tvDialogCancel.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
    }

    @Override
    protected DialogRestockSubmitTipsBinding getViewBinding() {
        return DialogRestockSubmitTipsBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
            case R.id.tvDialogCancel:
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //确认提交
                if (listener != null) {
                    listener.onConfirm();
                    dismiss();
                }
                break;
        }
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm();
    }
}
