package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:供货商列表（实体类）
 * Created by jingang on 2023/9/20
 */
public class SupplierListData implements Serializable {
    /**
     * list : [{"id":2,"supplierUnique":"d60054eac842485aa3fa31a4dcfc57f3","supplierName":"供应商甲","contacts":"张","contactMobile":"131*********","bindFlagDesc":"未绑定","enableStatusDesc":"启用","orderCount":2,"debts":25}]
     * totalDebts : 25
     */

    private double totalDebts;//总欠款
    private List<SupplierData> list;

    public double getTotalDebts() {
        return totalDebts;
    }

    public void setTotalDebts(double totalDebts) {
        this.totalDebts = totalDebts;
    }

    public List<SupplierData> getList() {
        return list;
    }

    public void setList(List<SupplierData> list) {
        this.list = list;
    }
}
