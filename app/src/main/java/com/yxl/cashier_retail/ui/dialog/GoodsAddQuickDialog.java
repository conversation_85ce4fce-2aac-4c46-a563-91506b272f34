package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogGoodsAddQuickBinding;
import com.yxl.cashier_retail.ui.activity.GoodsEditActivity;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.EventBusManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:dialog（快速新增商品）
 * Created by jingang on 2025/01/07
 */
@SuppressLint("NonConstantResourceId")
public class GoodsAddQuickDialog extends BaseDialog<DialogGoodsAddQuickBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private static String title,
            barcode,
            name,
            unit,
            cateUnique = "99991";//二级分类：常用
    private double inPrice, salePrice, memberPrice, stock;
    private int chengType;//计价类型 0.计件 1.称重

    public static void showDialog(Activity activity, String title, String barcode) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        GoodsAddQuickDialog.title = title;
        GoodsAddQuickDialog.barcode = barcode;
        GoodsAddQuickDialog dialog = new GoodsAddQuickDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 10 * 7, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public GoodsAddQuickDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.tvDialogTitle.setText(title);
        mBinding.etDialogBarcode.setText(barcode);
        mBinding.ivDialogChengType0.setSelected(true);
        mBinding.tvDialogMore.setOnClickListener(this);
        mBinding.ivDialogBarcode.setOnClickListener(this);
        mBinding.linDialogUnit.setOnClickListener(this);
        mBinding.linDialogChengType0.setOnClickListener(this);
        mBinding.linDialogChengType1.setOnClickListener(this);
        mBinding.tvDialogCancel.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
    }

    @Override
    protected DialogGoodsAddQuickBinding getViewBinding() {
        return DialogGoodsAddQuickBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvDialogMore:
                //更多商品信息
                mActivity.startActivity(new Intent(mActivity, GoodsEditActivity.class)

                );
                dismiss();
                break;
            case R.id.ivDialogBarcode:
                //生成条码
                getGoodsBarcode();
                break;
            case R.id.linDialogUnit:
                //选择商品单位
                UnitDialog.showDialog(mActivity, unit, mBinding.ivDialogUnit, name -> {
                    unit = name;
                    mBinding.tvDialogUnit.setText(unit);
                });
                break;
            case R.id.linDialogChengType0:
                //计件
                if (chengType != 0) {
                    chengType = 0;
                    mBinding.ivDialogChengType0.setSelected(true);
                    mBinding.ivDialogChengType1.setSelected(false);
                    mBinding.etDialogBarcode.setText("");
                }
                break;
            case R.id.linDialogChengType1:
                //称重
                if (chengType != 1) {
                    chengType = 1;
                    mBinding.ivDialogChengType0.setSelected(false);
                    mBinding.ivDialogChengType1.setSelected(true);
                    mBinding.etDialogBarcode.setText("");
                }
                break;
            case R.id.tvDialogCancel:
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //保存
                postGoodsEdit();
                break;
        }
    }

    /**
     * 生成条码
     */
    private void getGoodsBarcode() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        if (chengType == 1) {
            map.put("indexNum", SPUtils.getInstance().getString(Constants.GOODS_WEIGHT_BARCODE_FIRST_TWO, "21"));
        } else {
            map.put("indexNum", SPUtils.getInstance().getString(Constants.GOODS_COMMON_BARCODE_FIRST_TWO, "31"));
        }
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getGoodsBarcode(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        mBinding.etDialogBarcode.setText(s);
                    }
                });
    }

    /**
     * 商品新增
     */
    private void postGoodsEdit() {
        List array = new ArrayList();
        barcode = mBinding.etDialogBarcode.getText().toString().trim();
        if (TextUtils.isEmpty(barcode)) {
            showToast(1, getRstr(R.string.input_goods_barcode));
            return;
        }
        name = mBinding.etDialogName.getText().toString().trim();
        if (TextUtils.isEmpty(name)) {
            showToast(1, getRstr(R.string.input_goods_name));
            return;
        }
        if (TextUtils.isEmpty(unit)) {
            showToast(1, getRstr(R.string.select_goods_unit));
            return;
        }
        inPrice = TextUtils.isEmpty(mBinding.etDialogInPrice.getText().toString().trim())
                ? 0 : Double.parseDouble(mBinding.etDialogInPrice.getText().toString().trim());
        if (inPrice == 0) {
            showToast(1, getRstr(R.string.input_in_price));
            return;
        }
        salePrice = TextUtils.isEmpty(mBinding.etDialogSalePrice.getText().toString().trim())
                ? 0 : Double.parseDouble(mBinding.etDialogSalePrice.getText().toString().trim());
        if (salePrice == 0) {
            showToast(1, getRstr(R.string.input_price_sale));
            return;
        }
        memberPrice = TextUtils.isEmpty(mBinding.etDialogMemberPrice.getText().toString().trim())
                ? 0 : Double.parseDouble(mBinding.etDialogMemberPrice.getText().toString().trim());
        stock = TextUtils.isEmpty(mBinding.etDialogStock.getText().toString().trim())
                ? 0 : Double.parseDouble(mBinding.etDialogStock.getText().toString().trim());
//        if (stock == 0) {
//            showToast(1, getRstr(R.string.input_goods_stock));
//            return;
//        }
        Map object0 = new HashMap();
        object0.put("goodsBarcode", barcode);
        object0.put("goodsName", name);
        object0.put("goodsInPrice", inPrice);
        object0.put("goodsSalePrice", salePrice);
        object0.put("goodsCusPrice", memberPrice);
        object0.put("goodsUnit", unit);
        object0.put("goodsContain", 1);
        object0.put("shelfState", 1);//线上上架状态：1、已上架；2、已下架
        object0.put("pcShelfState", 1);//pc收银上架状态：1、已上架；2、已下架
        array.add(object0);

        Log.e(tag, "array = " + array);
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("goodsKindUnique", cateUnique);//二级分类编号
        map.put("goodsChengType", chengType);//计价类型 0.计件 1.称重
        map.put("foreignKey", barcode);//包装外键（最小规格的商品条码）
        map.put("goodsCount", stock);//库存
        map.put("goodsMessage", array);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getGoodsAdd(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        //更新本地数据库
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_LIST));
                        dismiss();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }
}
