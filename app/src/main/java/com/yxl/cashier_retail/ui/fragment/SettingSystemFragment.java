package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Typeface;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.allenliu.versionchecklib.core.http.HttpParams;
import com.allenliu.versionchecklib.v2.AllenVersionChecker;
import com.allenliu.versionchecklib.v2.builder.DownloadBuilder;
import com.allenliu.versionchecklib.v2.builder.NotificationBuilder;
import com.allenliu.versionchecklib.v2.builder.UIData;
import com.allenliu.versionchecklib.v2.callback.CustomDownloadFailedListener;
import com.allenliu.versionchecklib.v2.callback.CustomDownloadingDialogListener;
import com.allenliu.versionchecklib.v2.callback.CustomVersionDialogListener;
import com.allenliu.versionchecklib.v2.callback.RequestVersionListener;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.SPUtils;
import com.google.gson.Gson;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.MyApplication;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.DialogDownloadFailBinding;
import com.yxl.cashier_retail.databinding.DialogDownloadingBinding;
import com.yxl.cashier_retail.databinding.DialogVersionBinding;
import com.yxl.cashier_retail.databinding.FmSettingSystemBinding;
import com.yxl.cashier_retail.ui.activity.LauncherActivity;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.OrderOfflineData;
import com.yxl.cashier_retail.ui.bean.SaleListUniqueData;
import com.yxl.cashier_retail.ui.bean.VersionData;
import com.yxl.cashier_retail.ui.popupwindow.LanguagePop;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.base.BaseData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.EventBusManager;
import com.yxl.commonlibrary.utils.PackageUtils;

import org.litepal.LitePal;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:设置-系统设置
 * Created by jingang on 2024/5/13
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n", "MissingPermission"})
public class SettingSystemFragment extends BaseFragment<FmSettingSystemBinding> implements View.OnClickListener {
    private int type;//0.系统设置 1.声音设置 2.入口设置
    private String goodsWeightBarcodeFirstTwo,//称重商品的前两位
            goodsCommonBarcodeFirstTwo;//普通商品的前两位
    private boolean isUseSerialPortWeighingScale,//串口市斤秤
            isUseGoodsNoBarcodeAddCart,//未创建无码商品直接添加到购物车
            isStartUp,//开机自启
            isShowGoodsNoBarcode,//显示“无码商品/称重”
            isUseChangeGoodsNoBarcodeAndWeight,//交换"无码商品/称重"位置
            isUseHangPrintReceipt,//挂单打印小票
            isHomeInputDigitCreateGoodsNoBarcode,//首页输入数字创建无码商品
            isShowStock,//是否展示库存
            isExitClose,//退出时关闭计算机
            isVoiceHome,//播放首页提示音
            isVoiceKeyboard,//播放键盘音
            isVoicePaymentOnline,//播报在线支付
            isVoicePaymentCash,//播报现金支付
            isVoicePaymentMember,//播报会员支付
            isVoicePaymentOffline,//播报线下支付
            isVoicePaymentApplet,//播报小程序支付
            isVoiceRefundApplet,//播报小程序退款
            isVoiceOrderApplet,//播报小程序接单
            isVoicePaymentCombination;//播报组合支付

    @Override
    protected FmSettingSystemBinding getViewBinding() {
        return FmSettingSystemBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.tvSystem.setOnClickListener(this);
        mBinding.tvVoice.setOnClickListener(this);
        mBinding.tvInlet.setOnClickListener(this);
        mBinding.ivSystemSerialPortWeighingScale.setOnClickListener(this);
        mBinding.ivSystemGoodsNoBarcodeAddCart.setOnClickListener(this);
        mBinding.ivSystemStartUp.setOnClickListener(this);
        mBinding.ivSystemShowGoodsNoBarcode.setOnClickListener(this);
        mBinding.ivSystemChangeGoodsNoBarcodeAndWeight.setOnClickListener(this);
        mBinding.ivSystemHangPrintReceipt.setOnClickListener(this);
        mBinding.tvSyn.setOnClickListener(this);
        mBinding.linLanguage.setOnClickListener(this);
        mBinding.ivSystemHomeInputDigitCreateGoodsNoBarcode.setOnClickListener(this);
        mBinding.ivSystemShowStock.setOnClickListener(this);
        mBinding.ivSystemExitClose.setOnClickListener(this);
        mBinding.tvVersion.setOnClickListener(this);
        mBinding.tvLog.setOnClickListener(this);
        mBinding.tvExit.setOnClickListener(this);
        mBinding.etSystemGoodsWeightBarcodeFirstTwo.setOnEditorActionListener((v, actionId, event) -> {
            String str = mBinding.etSystemGoodsWeightBarcodeFirstTwo.getText().toString().trim();
            if (str.length() == 2) {
                if (str.charAt(0) == '0') {
                    showToast(1, getRstr(R.string.goods_barcode_first_no_zero));
                } else {
                    goodsWeightBarcodeFirstTwo = str;
                    SPUtils.getInstance().put(Constants.GOODS_WEIGHT_BARCODE_FIRST_TWO, str);
                    mBinding.etSystemGoodsWeightBarcodeFirstTwo.setText(goodsWeightBarcodeFirstTwo);
                    hideSoftInput(requireActivity());
                }
            } else {
                mBinding.etSystemGoodsWeightBarcodeFirstTwo.setText(goodsWeightBarcodeFirstTwo);
                mBinding.etSystemGoodsWeightBarcodeFirstTwo.setSelection(goodsWeightBarcodeFirstTwo.length());
                hideSoftInput(requireActivity());
            }
            return true;
        });
        mBinding.etSystemGoodsCommonBarcodeFirstTwo.setOnEditorActionListener((v, actionId, event) -> {
            String str = mBinding.etSystemGoodsCommonBarcodeFirstTwo.getText().toString().trim();
            if (str.length() == 2) {
                if (str.charAt(0) == '0') {
                    showToast(1, getRstr(R.string.goods_barcode_first_no_zero));
                } else {
                    goodsCommonBarcodeFirstTwo = str;
                    SPUtils.getInstance().put(Constants.GOODS_COMMON_BARCODE_FIRST_TWO, str);
                    mBinding.etSystemGoodsCommonBarcodeFirstTwo.setText(goodsCommonBarcodeFirstTwo);
                    hideSoftInput(requireActivity());
                }
            } else {
                mBinding.etSystemGoodsCommonBarcodeFirstTwo.setText(goodsCommonBarcodeFirstTwo);
                mBinding.etSystemGoodsCommonBarcodeFirstTwo.setSelection(goodsCommonBarcodeFirstTwo.length());
                hideSoftInput(requireActivity());
            }
            return true;
        });

        //声音设置
        mBinding.ivVoiceHome.setOnClickListener(this);
        mBinding.ivVoiceKeyboard.setOnClickListener(this);
        mBinding.ivVoicePaymentOnline.setOnClickListener(this);
        mBinding.ivVoicePaymentCash.setOnClickListener(this);
        mBinding.ivVoicePaymentMember.setOnClickListener(this);
        mBinding.ivVoicePaymentOffline.setOnClickListener(this);
        mBinding.ivVoicePaymentApplet.setOnClickListener(this);
        mBinding.ivVoiceRefundApplet.setOnClickListener(this);
        mBinding.ivVoiceOrderApplet.setOnClickListener(this);
        mBinding.ivVoicePaymentCombination.setOnClickListener(this);
    }

    @Override
    protected void initData() {
        setUI();
        getOrderOfflineList();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvSystem:
                //系统设置
                if (type != 0) {
                    type = 0;
                    mBinding.tvSystem.setBackgroundResource(R.drawable.shape_white_topleft_5);
                    mBinding.tvSystem.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.tvSystem.setTextColor(getResources().getColor(R.color.black));
                    mBinding.linSystem.setVisibility(View.VISIBLE);
                    mBinding.tvVoice.setBackgroundResource(0);
                    mBinding.tvVoice.setTypeface(Typeface.DEFAULT);
                    mBinding.tvVoice.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
                    mBinding.linVoice.setVisibility(View.GONE);
                    mBinding.tvInlet.setBackgroundResource(0);
                    mBinding.tvInlet.setTypeface(Typeface.DEFAULT);
                    mBinding.tvInlet.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
                    mBinding.linInlet.setVisibility(View.GONE);
                }
                break;
            case R.id.tvVoice:
                //声音设置
                if (type != 1) {
                    type = 1;
                    mBinding.tvSystem.setBackgroundResource(0);
                    mBinding.tvSystem.setTypeface(Typeface.DEFAULT);
                    mBinding.tvSystem.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
                    mBinding.linSystem.setVisibility(View.GONE);
                    mBinding.tvVoice.setBackgroundColor(getResources().getColor(R.color.white));
                    mBinding.tvVoice.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.tvVoice.setTextColor(getResources().getColor(R.color.black));
                    mBinding.linVoice.setVisibility(View.VISIBLE);
                    mBinding.tvInlet.setBackgroundResource(0);
                    mBinding.tvInlet.setTypeface(Typeface.DEFAULT);
                    mBinding.tvInlet.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
                    mBinding.linInlet.setVisibility(View.GONE);
                }
                break;
            case R.id.tvInlet:
                //入口设置
                if (type != 2) {
                    type = 2;
                    mBinding.tvSystem.setBackgroundResource(0);
                    mBinding.tvSystem.setTypeface(Typeface.DEFAULT);
                    mBinding.tvSystem.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
                    mBinding.linSystem.setVisibility(View.GONE);
                    mBinding.tvVoice.setBackgroundResource(0);
                    mBinding.tvVoice.setTypeface(Typeface.DEFAULT);
                    mBinding.tvVoice.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
                    mBinding.linVoice.setVisibility(View.GONE);
                    mBinding.tvInlet.setBackgroundColor(getResources().getColor(R.color.white));
                    mBinding.tvInlet.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.tvInlet.setTextColor(getResources().getColor(R.color.black));
                    mBinding.linInlet.setVisibility(View.VISIBLE);
                }
                break;
            case R.id.ivSystemSerialPortWeighingScale:
                //串口市斤秤
                SPUtils.getInstance().put(Constants.IS_USE_SERIAL_PORT_WEIGHING_SCALE, isUseSerialPortWeighingScale ? "" : Constants.IS_USE_SERIAL_PORT_WEIGHING_SCALE);
                isUseSerialPortWeighingScale = !isUseSerialPortWeighingScale;
                mBinding.ivSystemSerialPortWeighingScale.setSelected(isUseSerialPortWeighingScale);
                break;
            case R.id.ivSystemGoodsNoBarcodeAddCart:
                //未创建无码商品直接添加到购物车
                SPUtils.getInstance().put(Constants.IS_USE_GOODS_NO_BARCODE_ADD_CART, isUseGoodsNoBarcodeAddCart ? "" : Constants.IS_USE_GOODS_NO_BARCODE_ADD_CART);
                isUseGoodsNoBarcodeAddCart = !isUseGoodsNoBarcodeAddCart;
                mBinding.ivSystemGoodsNoBarcodeAddCart.setSelected(isUseGoodsNoBarcodeAddCart);
                break;
            case R.id.ivSystemStartUp:
                //开机自启
                SPUtils.getInstance().put(Constants.IS_START_UP, isStartUp ? "" : Constants.IS_START_UP);
                isStartUp = !isStartUp;
                mBinding.ivSystemStartUp.setSelected(isStartUp);
                break;
            case R.id.ivSystemShowGoodsNoBarcode:
                //显示“无码商品/称重”
                SPUtils.getInstance().put(Constants.IS_USE_SHOW_GOODS_NO_BARCODE, isShowGoodsNoBarcode ? Constants.IS_USE_SHOW_GOODS_NO_BARCODE : "");
                isShowGoodsNoBarcode = !isShowGoodsNoBarcode;
                mBinding.ivSystemShowGoodsNoBarcode.setSelected(isShowGoodsNoBarcode);
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_LIST));
                break;
            case R.id.ivSystemChangeGoodsNoBarcodeAndWeight:
                //交换"无码商品/称重"位置
                SPUtils.getInstance().put(Constants.IS_USE_CHANGE_GOODS_NO_BARCODE_AND_WEIGHT, isUseChangeGoodsNoBarcodeAndWeight ? "" : Constants.IS_USE_CHANGE_GOODS_NO_BARCODE_AND_WEIGHT);
                isUseChangeGoodsNoBarcodeAndWeight = !isUseChangeGoodsNoBarcodeAndWeight;
                mBinding.ivSystemChangeGoodsNoBarcodeAndWeight.setSelected(isUseChangeGoodsNoBarcodeAndWeight);
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_LIST));
                break;
            case R.id.ivSystemHangPrintReceipt:
                //挂单打印小票
                SPUtils.getInstance().put(Constants.IS_USE_HANG_PRINT_RECEIPT, isUseHangPrintReceipt ? "" : Constants.IS_USE_HANG_PRINT_RECEIPT);
                isUseHangPrintReceipt = !isUseHangPrintReceipt;
                mBinding.ivSystemHangPrintReceipt.setSelected(isUseHangPrintReceipt);
                break;
            case R.id.tvSyn:
                //重新同步商品
                AppManager.getInstance().finishAllActivity();
                startActivity(new Intent(getActivity(), LauncherActivity.class)
                        .putExtra("type", 1)
                );
                break;
            case R.id.linLanguage:
                //选择语言
                LanguagePop.showDialog(mContext, mBinding.ivLanguageMore, v, mBinding.linLanguage.getMeasuredWidth(), () -> {
                    AppManager.getInstance().finishAllActivity();
                    goToActivity(LauncherActivity.class);
                });
                break;
            case R.id.ivSystemHomeInputDigitCreateGoodsNoBarcode:
                //首页输入数字创建无码商品
                SPUtils.getInstance().put(Constants.IS_USE_HOME_INPUT_DIGIT_CREATE_GOODS_NO_BARCODE, isHomeInputDigitCreateGoodsNoBarcode ? Constants.IS_USE_HOME_INPUT_DIGIT_CREATE_GOODS_NO_BARCODE : "");
                isHomeInputDigitCreateGoodsNoBarcode = !isHomeInputDigitCreateGoodsNoBarcode;
                mBinding.ivSystemHomeInputDigitCreateGoodsNoBarcode.setSelected(isHomeInputDigitCreateGoodsNoBarcode);
                break;
            case R.id.ivSystemShowStock:
                //是否展示库存
                SPUtils.getInstance().put(Constants.IS_SHOW_STOCK, isShowStock ? Constants.IS_SHOW_STOCK : "");
                isShowStock = !isShowStock;
                mBinding.ivSystemShowStock.setSelected(isShowStock);
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_LIST));
                break;
            case R.id.ivSystemExitClose:
                //退出时关闭计算机
                SPUtils.getInstance().put(Constants.IS_EXIT_CLOSE, isExitClose ? "" : Constants.IS_EXIT_CLOSE);
                isExitClose = !isExitClose;
                mBinding.ivSystemExitClose.setSelected(isExitClose);
                break;
            case R.id.tvVersion:
                //检查更新
                if (isQuicklyClick()) {
                    return;
                }
                checkUpgrade();
                break;
            case R.id.tvLog:
                //上传当前日志
                break;
            case R.id.tvExit:
                //退出程序 1.提交离线订单 2.交班 3.退出app 4.是否关机
                if (isQuicklyClick()) {
                    return;
                }
                showDialog();
                if (offlineList.size() > 0) {
                    getSaleListUnique();
                } else {
                    postLoginOut();
                }
                break;
            /*声音设置start*/
            case R.id.ivVoiceHome:
                //播放首页提示音
                SPUtils.getInstance().put(Constants.IS_VOICE_HOME, isVoiceHome ? "" : Constants.IS_VOICE_HOME);
                isVoiceHome = !isVoiceHome;
                mBinding.ivVoiceHome.setSelected(isVoiceHome);
                break;
            case R.id.ivVoiceKeyboard:
                //播放键盘声
                SPUtils.getInstance().put(Constants.IS_VOICE_KEYBOARD, isVoiceKeyboard ? "" : Constants.IS_VOICE_KEYBOARD);
                isVoiceKeyboard = !isVoiceKeyboard;
                mBinding.ivVoiceKeyboard.setSelected(isVoiceKeyboard);
                break;
            case R.id.ivVoicePaymentOnline:
                //播报在线支付
                SPUtils.getInstance().put(Constants.IS_VOICE_PAYMENT_ONLINE, isVoicePaymentOnline ? "" : Constants.IS_VOICE_PAYMENT_ONLINE);
                isVoicePaymentOnline = !isVoicePaymentOnline;
                mBinding.ivVoicePaymentOnline.setSelected(isVoicePaymentOnline);
                break;
            case R.id.ivVoicePaymentCash:
                //播报现金支付
                SPUtils.getInstance().put(Constants.IS_VOICE_PAYMENT_CASH, isVoicePaymentCash ? "" : Constants.IS_VOICE_PAYMENT_CASH);
                isVoicePaymentCash = !isVoicePaymentCash;
                mBinding.ivVoicePaymentCash.setSelected(isVoicePaymentCash);
                break;
            case R.id.ivVoicePaymentMember:
                //播报会员支付
                SPUtils.getInstance().put(Constants.IS_VOICE_PAYMENT_MEMBER, isVoicePaymentMember ? "" : Constants.IS_VOICE_PAYMENT_MEMBER);
                isVoicePaymentMember = !isVoicePaymentMember;
                mBinding.ivVoicePaymentMember.setSelected(isVoicePaymentMember);
                break;
            case R.id.ivVoicePaymentOffline:
                //播报线下支付
                SPUtils.getInstance().put(Constants.IS_VOICE_PAYMENT_OFFLINE, isVoicePaymentOffline ? "" : Constants.IS_VOICE_PAYMENT_OFFLINE);
                isVoicePaymentOffline = !isVoicePaymentOffline;
                mBinding.ivVoicePaymentOffline.setSelected(isVoicePaymentOffline);
                break;
            case R.id.ivVoicePaymentApplet:
                //播报小程序支付
                SPUtils.getInstance().put(Constants.IS_VOICE_PAYMENT_APPLET, isVoicePaymentApplet ? "" : Constants.IS_VOICE_PAYMENT_APPLET);
                isVoicePaymentApplet = !isVoicePaymentApplet;
                mBinding.ivVoicePaymentApplet.setSelected(isVoicePaymentApplet);
                break;
            case R.id.ivVoiceRefundApplet:
                //播报小程序退款
                SPUtils.getInstance().put(Constants.IS_VOICE_REFUND_APPLET, isVoiceRefundApplet ? "" : Constants.IS_VOICE_REFUND_APPLET);
                isVoiceRefundApplet = !isVoiceRefundApplet;
                mBinding.ivVoiceRefundApplet.setSelected(isVoiceRefundApplet);
                break;
            case R.id.ivVoiceOrderApplet:
                //播报小程序接单
                SPUtils.getInstance().put(Constants.IS_VOICE_ORDER_APPLET, isVoiceOrderApplet ? "" : Constants.IS_VOICE_ORDER_APPLET);
                isVoiceOrderApplet = !isVoiceOrderApplet;
                mBinding.ivVoiceOrderApplet.setSelected(isVoiceOrderApplet);
                break;
            case R.id.ivVoicePaymentCombination:
                //播报组合支付
                SPUtils.getInstance().put(Constants.IS_VOICE_PAYMENT_COMBINATION, isVoicePaymentCombination ? "" : Constants.IS_VOICE_PAYMENT_COMBINATION);
                isVoicePaymentCombination = !isVoicePaymentCombination;
                mBinding.ivVoicePaymentCombination.setSelected(isVoicePaymentCombination);
                break;
            /*声音设置end*/
        }
    }

    /**
     * 更新UI
     */
    private void setUI() {
        String language = SPUtils.getInstance().getString(Constants.LANGUAGE, "");
        if (TextUtils.isEmpty(language)) {
            mBinding.ivLanguage.setImageResource(R.mipmap.ic_language001);
            mBinding.tvLanguage.setText("中文");
        } else {
            switch (language) {
                case "en":
                    mBinding.ivLanguage.setImageResource(R.mipmap.ic_language002);
                    mBinding.tvLanguage.setText("English");
                    break;
                case "th":
                    mBinding.ivLanguage.setImageResource(R.mipmap.ic_language003);
                    mBinding.tvLanguage.setText("แบบไทย");
                    break;
                case "ru":
                    mBinding.ivLanguage.setImageResource(R.mipmap.ic_language004);
                    mBinding.tvLanguage.setText("Русский");
                    break;
                case "ms":
                    mBinding.ivLanguage.setImageResource(R.mipmap.ic_language005);
                    mBinding.tvLanguage.setText("Melayu");
                    break;
                case "kk":
                    mBinding.ivLanguage.setImageResource(R.mipmap.ic_language006);
                    mBinding.tvLanguage.setText("قازاقشا");
                    break;
                case "vi":
                    mBinding.ivLanguage.setImageResource(R.mipmap.ic_language007);
                    mBinding.tvLanguage.setText("Tiếng Việt");
                    break;
                default:
                    mBinding.ivLanguage.setImageResource(R.mipmap.ic_language001);
                    mBinding.tvLanguage.setText("中文");
                    break;
            }
        }
        //称重商品的前两位
        goodsWeightBarcodeFirstTwo = SPUtils.getInstance().getString(Constants.GOODS_WEIGHT_BARCODE_FIRST_TWO, "21");
        mBinding.etSystemGoodsWeightBarcodeFirstTwo.setText(goodsWeightBarcodeFirstTwo);
        //普通商品的前两位
        goodsCommonBarcodeFirstTwo = SPUtils.getInstance().getString(Constants.GOODS_COMMON_BARCODE_FIRST_TWO, "31");
        mBinding.etSystemGoodsCommonBarcodeFirstTwo.setText(goodsCommonBarcodeFirstTwo);
        //串口市斤秤
        isUseSerialPortWeighingScale = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_SERIAL_PORT_WEIGHING_SCALE, ""));
        mBinding.ivSystemSerialPortWeighingScale.setSelected(isUseSerialPortWeighingScale);
        //未创建无码商品直接添加到购物车
        isUseGoodsNoBarcodeAddCart = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_GOODS_NO_BARCODE_ADD_CART, ""));
        mBinding.ivSystemGoodsNoBarcodeAddCart.setSelected(isUseGoodsNoBarcodeAddCart);
        //开机自启
        isStartUp = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_START_UP, ""));
        mBinding.ivSystemStartUp.setSelected(isStartUp);
        //显示“无码商品/称重”
        isShowGoodsNoBarcode = TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_SHOW_GOODS_NO_BARCODE, ""));
        mBinding.ivSystemShowGoodsNoBarcode.setSelected(isShowGoodsNoBarcode);
        //交换"无码商品/称重"位置
        isUseChangeGoodsNoBarcodeAndWeight = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_CHANGE_GOODS_NO_BARCODE_AND_WEIGHT, ""));
        mBinding.ivSystemChangeGoodsNoBarcodeAndWeight.setSelected(isUseChangeGoodsNoBarcodeAndWeight);
        //挂单打印小票
        isUseHangPrintReceipt = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_HANG_PRINT_RECEIPT, ""));
        mBinding.ivSystemHangPrintReceipt.setSelected(isUseHangPrintReceipt);
        //首页输入数字创建无码商品
        isHomeInputDigitCreateGoodsNoBarcode = TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_HOME_INPUT_DIGIT_CREATE_GOODS_NO_BARCODE, ""));
        mBinding.ivSystemHomeInputDigitCreateGoodsNoBarcode.setSelected(isHomeInputDigitCreateGoodsNoBarcode);
        //是否展示库存
        isShowStock = TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_SHOW_STOCK, ""));
        mBinding.ivSystemShowStock.setSelected(isShowStock);
        //退出时关闭计算机
        isExitClose = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_EXIT_CLOSE, ""));
        mBinding.ivSystemExitClose.setSelected(isExitClose);
        /*声音设置start*/
        //播放首页提示音
        isVoiceHome = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_VOICE_HOME, ""));
        mBinding.ivVoiceHome.setSelected(isVoiceHome);
        //播放键盘音
        isVoiceKeyboard = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_VOICE_KEYBOARD, ""));
        mBinding.ivVoiceKeyboard.setSelected(isVoiceKeyboard);
        //播报在线支付
        isVoicePaymentOnline = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_VOICE_PAYMENT_ONLINE, ""));
        mBinding.ivVoicePaymentOnline.setSelected(isVoicePaymentOnline);
        //播报现金支付
        isVoicePaymentCash = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_VOICE_PAYMENT_CASH, ""));
        mBinding.ivVoicePaymentCash.setSelected(isVoicePaymentCash);
        //播报会员支付
        isVoicePaymentMember = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_VOICE_PAYMENT_MEMBER, ""));
        mBinding.ivVoicePaymentMember.setSelected(isVoicePaymentMember);
        //播报线下支付
        isVoicePaymentOffline = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_VOICE_PAYMENT_OFFLINE, ""));
        mBinding.ivVoicePaymentOffline.setSelected(isVoicePaymentOffline);
        //播报小程序支付
        isVoicePaymentApplet = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_VOICE_PAYMENT_APPLET, ""));
        mBinding.ivVoicePaymentApplet.setSelected(isVoicePaymentApplet);
        //播报小程序退款
        isVoiceRefundApplet = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_VOICE_REFUND_APPLET, ""));
        mBinding.ivVoiceRefundApplet.setSelected(isVoiceRefundApplet);
        //播报小程序接单
        isVoiceOrderApplet = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_VOICE_ORDER_APPLET, ""));
        mBinding.ivVoiceOrderApplet.setSelected(isVoiceOrderApplet);
        //播报组合支付
        isVoicePaymentCombination = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_VOICE_PAYMENT_COMBINATION, ""));
        mBinding.ivVoicePaymentCombination.setSelected(isVoicePaymentCombination);
        /*声音设置end*/
    }


    /******************************版本更新start******************************/
    private VersionData versionData;

    private void checkUpgrade() {
        HttpParams map = new HttpParams();
        map.put("app_id", "9");
        map.put("app_type", "1");
        DownloadBuilder builder = AllenVersionChecker
                .getInstance()
                .requestVersion()
                .setRequestUrl(ZURL.getVersion())
                .setRequestParams(map)
                .request(new RequestVersionListener() {
                    @Nullable
                    @Override
                    public UIData onRequestVersionSuccess(DownloadBuilder downloadBuilder, String result) {
                        Log.e(tag, "版本更新 = " + result);
                        versionData = new Gson().fromJson(result, VersionData.class);
                        if (versionData == null) {
                            return null;
                        }
                        if (versionData.getStatus() != 0) {
                            showToast(1, versionData.getMsg());
                            return null;
                        }
                        if (versionData.getData() == null) {
                            showToast(1, versionData.getMsg());
                            return null;
                        }
                        if (versionData.getData().getCode() <= PackageUtils.getPackageCode(mContext)) {
                            showToast(0, getRstr(R.string.version_newed));
                            return null;
                        }
                        if (versionData.getData().getUpdate_install() == 1) {
                            //强制更新
                            downloadBuilder.setForceUpdateListener(() -> {
                                getActivity().finish();
                            });
                        }
                        return crateUIData();
                    }

                    @Override
                    public void onRequestVersionFailure(String message) {

                    }
                });
        builder.setNotificationBuilder(createCustomNotification());
        builder.setDownloadAPKPath(mContext.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS).getPath() + "/");
        builder.setCustomVersionDialogListener(createCustomDialog());
        builder.setCustomDownloadingDialogListener(createCustomDownloadingDialog());
        builder.setCustomDownloadFailedListener(createCustomDownloadFailedDialog());
        builder.setForceRedownload(true);
        builder.executeMission(mContext);
    }

    /**
     * 自定义下载中对话框，下载中会连续回调此方法 updateUI
     * 务必用库传回来的context 实例化你的dialog
     *
     * @return
     */
    private CustomDownloadingDialogListener createCustomDownloadingDialog() {
        return new CustomDownloadingDialogListener() {
            @Override
            public Dialog getCustomDownloadingDialog(Context context, int progress, UIData versionBundle) {
                BaseDialog<DialogDownloadingBinding> dialog = new BaseDialog<DialogDownloadingBinding>(context, R.style.dialog_style) {
                    @Override
                    protected DialogDownloadingBinding getViewBinding() {
                        return DialogDownloadingBinding.inflate(getLayoutInflater());
                    }
                };
                dialog.setCancelable(false);
                return dialog;
            }

            @Override
            public void updateUI(Dialog dialog, int progress, UIData versionBundle) {
                TextView tvProgress = dialog.findViewById(R.id.tv_progress);
                ProgressBar progressBar = dialog.findViewById(R.id.pb);
                progressBar.setProgress(progress);
                tvProgress.setText(getString(R.string.versionchecklib_progress, progress));
            }
        };
    }

    /**
     * 务必用库传回来的context 实例化你的dialog
     *
     * @return
     */
    private CustomDownloadFailedListener createCustomDownloadFailedDialog() {
        return (context, versionBundle) -> {
            BaseDialog<DialogDownloadFailBinding> dialog = new BaseDialog<DialogDownloadFailBinding>(context, R.style.dialog_style) {
                @Override
                protected DialogDownloadFailBinding getViewBinding() {
                    return DialogDownloadFailBinding.inflate(getLayoutInflater());
                }
            };
            dialog.setCancelable(false);
            return dialog;
        };
    }

    /**
     * dialog（发现新版本）
     *
     * @return
     */
    private CustomVersionDialogListener createCustomDialog() {
        return (context, versionBundle) -> {
            BaseDialog<DialogVersionBinding> dialog = new BaseDialog<DialogVersionBinding>(context, R.style.dialog_style) {
                @Override
                protected DialogVersionBinding getViewBinding() {
                    return DialogVersionBinding.inflate(getLayoutInflater());
                }
            };
            dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
            dialog.setCancelable(false);
            TextView tvVersionNew = dialog.findViewById(R.id.tvDialogVersionNew),
                    tvVersion = dialog.findViewById(R.id.tvDialogVersion),
                    tvContent = dialog.findViewById(R.id.tvDialogContent);
            tvVersionNew.setText(getRstr(R.string.version_find) + " for V" + versionData.getData().getUpdate_version());
            tvVersion.setText(getRstr(R.string.version_now) + " V" + PackageUtils.getPackageName(mContext));
            tvContent.setText(versionData.getData().getUpdate_log());
            dialog.setCanceledOnTouchOutside(false);
            return dialog;
        };
    }

    /**
     * 创建通知
     *
     * @return
     */
    private NotificationBuilder createCustomNotification() {
        return NotificationBuilder.create()
                .setRingtone(true)
                .setIcon(R.mipmap.ic_launcher)
                .setTicker("custom_ticker")
                .setContentTitle("金圈收银升级")
                .setContentText("升级中");
    }

    /**
     * @return
     * @important 使用请求版本功能，可以在这里设置downloadUrl
     * 这里可以构造UI需要显示的数据
     * UIData 内部是一个Bundle
     */
    private UIData crateUIData() {
        UIData uiData = UIData.create();
        uiData.setTitle(versionData.getData().getUpdate_des());
        uiData.setDownloadUrl(versionData.getData().getUpdate_url());
        uiData.setContent(versionData.getData().getUpdate_log());
        return uiData;
    }

    /******************************版本更新end******************************/

    /******************************退出程序start******************************/

    //离线订单
    private List<OrderOfflineData> offlineList = new ArrayList<>();

    /**
     * 退出登录
     */
    private void loginOut() {
        SPUtils.getInstance().put(Constants.LOGIN_ID, "");
        MyApplication.getInstance().disConnect_mqtt();
        MyApplication.getInstance().disConnectSerialPort();
        //退出程序
        AppManager.getInstance().finishAllActivity();
//        //关机
//        if (isExitClose) {
//            getActivity().sendBroadcast(new Intent()
//                    .putExtra("android.intent.extra.KEY_CONFIRM", false)
//            );
//        }
    }

    /**
     * 获取离线订单列表
     */
    private void getOrderOfflineList() {
        List<OrderOfflineData> list = LitePal.findAll(OrderOfflineData.class);
        offlineList.clear();
        if (list != null) {
            offlineList.addAll(list);
        }
        Collections.reverse(offlineList);
    }

    /**
     * 退出登录（交班）
     */
    private void postLoginOut() {
        if (!NetworkUtils.isConnected()) {
            showToast(1, getRstr(R.string.no_network_connect));
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("login_id", getLoginId());
        showDialog();
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getLoginOut(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        if (data.getStatus() == 0) {
                            showToast(0, data.getMsg());
                            loginOut();
                        } else {
                            showToast(1, data.getMsg());
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 创建订单编号
     */
    private void getSaleListUnique() {
        if (!NetworkUtils.isConnected()) {
            hideDialog();
            showToast(1, getRstr(R.string.no_network_connect));
            return;
        }
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getSaleListUniqueCreate(),
                new HashMap<>(),
                SaleListUniqueData.class,
                new RequestListener<SaleListUniqueData>() {
                    @Override
                    public void success(SaleListUniqueData data) {
                        postPayment(data.getSale_list_unique());
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 普通线下订单结算
     *
     * @param saleListUnique
     */
    private void postPayment(String saleListUnique) {
        if (!NetworkUtils.isConnected()) {
            hideDialog();
            showToast(1, getRstr(R.string.no_network_connect));
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("saleListState", offlineList.get(0).getSaleListState());//付款状态 3.已付款 4.赊账
        params.put("saleListUnique", saleListUnique);//订单编号(接口创建)
        params.put("goodsPurprice", offlineList.get(0).getGoodsPurprice());//商品进价 “,”隔开
        params.put("saleListDetailPrice", offlineList.get(0).getSaleListDetailPrice());//商品售价 “,”隔开
        params.put("goods_old_price", offlineList.get(0).getGoods_old_price());//商品原价 “,”隔开
        params.put("saleListDetailCount", offlineList.get(0).getSaleListDetailCount());//商品数量 “,”隔开
        params.put("goodsName", offlineList.get(0).getGoodsName());//商品名称 “,”隔开
        params.put("goodsBarcode", offlineList.get(0).getGoodsBarcode());//商品条码 “,”隔开
        params.put("goodsId", offlineList.get(0).getGoodsId());//商品id ","隔开
        params.put("saleListTotal", offlineList.get(0).getSaleListTotal());//订单总金额
        params.put("saleListCashier", getStaffUnique());//员工id
//        params.put("saleListRemarks", );//订单备注
//        params.put("machine_num", );//机器编号
        params.put("saleListActuallyReceived", offlineList.get(0).getSaleListActuallyReceived());
        params.put("sale_list_payment", offlineList.get(0).getSale_list_payment());//支付方式 1.现金 2.支付宝 3.微信 4.银行卡 5.储值卡 8.混合支付 9.免密支付
//        params.put("machineTime", );//失败时上传当前时间
        params.put("type", offlineList.get(0).getType());//固定值2
//        params.put("wholesale_phone", );//批发客户的手机号
        params.put("saleListPayDetail", offlineList.get(0).getSaleListPayDetail());//支付详情
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getPayment(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        OrderOfflineData data = offlineList.get(0);
                        data.delete();
                        offlineList.remove(0);
                        if (offlineList.size() > 0) {
                            getSaleListUnique();
                        } else {
                            postLoginOut();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /******************************退出程序end******************************/
}
