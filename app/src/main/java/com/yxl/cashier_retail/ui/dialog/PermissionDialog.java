package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogPermissionBinding;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:dialog（权限申请）
 * Created by jingang on 2023/6/17
 */
@SuppressLint("NonConstantResourceId")
public class PermissionDialog extends BaseDialog<DialogPermissionBinding> implements View.OnClickListener {
    private static String content;

    public static void showDialog(Activity activity, String content, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        PermissionDialog.content = content;
        PermissionDialog.listener = listener;
        PermissionDialog dialog = new PermissionDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public PermissionDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(false);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        mBinding.tvDialogCancel.setOnClickListener(this);
        mBinding.tvDialogAgain.setOnClickListener(this);
        mBinding.tvDialogContent.setText(content);
    }

    @Override
    protected DialogPermissionBinding getViewBinding() {
        return DialogPermissionBinding.inflate(getLayoutInflater());
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvDialogConfirm:
                //同意并继续
                if (listener != null) {
                    listener.onClick(0);
                    dismiss();
                }
                break;
            case R.id.tvDialogCancel:
                //不同意
                if (listener != null) {
                    listener.onClick(1);
                    dismiss();
                }
                break;
            case R.id.tvDialogAgain:
                //不在提醒
                if (listener != null) {
                    listener.onClick(2);
                    dismiss();
                }
                break;
        }
    }

    private static MyListener listener;

    public interface MyListener {
        /**
         * @param type 0.同意并继续 1.不同意 2.不再提醒
         */
        void onClick(int type);
    }
}
