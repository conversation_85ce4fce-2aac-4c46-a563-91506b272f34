package com.yxl.cashier_retail.ui.bean;

/**
 * Describe:会员消费记录（实体类）
 * Created by jingang on 2024/8/7
 */
public class MemberRecordData {
    /**
     * recId : 607647
     * recDate : 2024-05-18
     * recMoney : 30.0
     * salePoints : 0.0
     * staffName : 益农社服务员
     * consumptionType : 混合支付
     * cusType : 3
     * saleListUnique : 1716019628322486
     * rechargeMethod : 0
     */

    private int recId;
    private String recDate;//消费日期
    private double recMoney;//消费金额
    private double salePoints;//消费积分
    private String staffName;
    private String consumptionType;//支付方式
    private int cusType;//记录类型 1:充值； 2:取现 ；3:消费；4：积分清零
    private String saleListUnique;//订单编号
    private int rechargeMethod;//充值方式 1:现金 2:微信 3:支付宝 4：存零;5:退款;6免密;7:公众号或小程序；8、抽奖红包;9、福利充值

    public int getRecId() {
        return recId;
    }

    public void setRecId(int recId) {
        this.recId = recId;
    }

    public String getRecDate() {
        return recDate;
    }

    public void setRecDate(String recDate) {
        this.recDate = recDate;
    }

    public double getRecMoney() {
        return recMoney;
    }

    public void setRecMoney(double recMoney) {
        this.recMoney = recMoney;
    }

    public double getSalePoints() {
        return salePoints;
    }

    public void setSalePoints(double salePoints) {
        this.salePoints = salePoints;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public String getConsumptionType() {
        return consumptionType;
    }

    public void setConsumptionType(String consumptionType) {
        this.consumptionType = consumptionType;
    }

    public int getCusType() {
        return cusType;
    }

    public void setCusType(int cusType) {
        this.cusType = cusType;
    }

    public String getSaleListUnique() {
        return saleListUnique;
    }

    public void setSaleListUnique(String saleListUnique) {
        this.saleListUnique = saleListUnique;
    }

    public int getRechargeMethod() {
        return rechargeMethod;
    }

    public void setRechargeMethod(int rechargeMethod) {
        this.rechargeMethod = rechargeMethod;
    }
}
