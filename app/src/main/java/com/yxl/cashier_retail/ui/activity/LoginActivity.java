package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.Editable;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.SPUtils;
import com.google.gson.Gson;
import com.yxl.cashier_retail.BuildConfig;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityLoginBinding;
import com.yxl.cashier_retail.ui.popupwindow.LanguagePop;
import com.yxl.cashier_retail.utils.PermissionUtils;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.bean.LoginBaseData;
import com.yxl.commonlibrary.bean.LoginData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.PackageUtils;
import com.yxl.commonlibrary.utils.SystemUtils;

import org.litepal.LitePal;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:登录
 * Created by jingang on 2024/5/7
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n", "MissingPermission"})
public class LoginActivity extends BaseActivity<ActivityLoginBinding> implements View.OnClickListener {
    private String account, pwd, device_id;
    private boolean isEye;//密码可见

    @Override
    protected ActivityLoginBinding getViewBinding() {
        return ActivityLoginBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        device_id = SPUtils.getInstance().getString(Constants.DEVICE_ID, "");
        mBinding.tvKefu.setOnClickListener(this);
        mBinding.tvShutdown.setOnClickListener(this);
        mBinding.linLanguage.setOnClickListener(this);
        mBinding.ivAccountClear.setOnClickListener(this);
        mBinding.ivPwdClear.setOnClickListener(this);
        mBinding.ivEye.setOnClickListener(this);
        mBinding.linRemember.setOnClickListener(this);
        mBinding.tvForget.setOnClickListener(this);
        mBinding.tvConfirm.setOnClickListener(this);
        mBinding.butUrl.setOnClickListener(this);
        mBinding.etAccount.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                account = s.toString().trim();
                if (TextUtils.isEmpty(account)) {
                    mBinding.ivAccountClear.setVisibility(View.GONE);
                } else {
                    mBinding.ivAccountClear.setVisibility(View.VISIBLE);
                }
                setTextBg();
            }
        });
        mBinding.etPwd.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                pwd = s.toString().trim();
                if (TextUtils.isEmpty(pwd)) {
                    mBinding.ivPwdClear.setVisibility(View.GONE);
                } else {
                    mBinding.ivPwdClear.setVisibility(View.VISIBLE);
                }
                setTextBg();
            }
        });
    }

    @Override
    protected void initData() {
        mBinding.tvKefu.setText(getRstr(R.string.kefu_phone_colon) + Constants.MOBILE);
        mBinding.tvForget.setText(getString(R.string.pwd_forget) + "?");
        String language = SPUtils.getInstance().getString(Constants.LANGUAGE, "");
        if (TextUtils.isEmpty(language)) {
            mBinding.ivLanguage.setImageResource(R.mipmap.ic_language001);
            mBinding.tvLanguage.setText("中文");
        } else {
            switch (language) {
                case "en":
                    mBinding.ivLanguage.setImageResource(R.mipmap.ic_language002);
                    mBinding.tvLanguage.setText("English");
                    break;
                case "th":
                    mBinding.ivLanguage.setImageResource(R.mipmap.ic_language003);
                    mBinding.tvLanguage.setText("แบบไทย");
                    break;
                case "ru":
                    mBinding.ivLanguage.setImageResource(R.mipmap.ic_language004);
                    mBinding.tvLanguage.setText("Русский");
                    break;
                case "ms":
                    mBinding.ivLanguage.setImageResource(R.mipmap.ic_language005);
                    mBinding.tvLanguage.setText("Melayu");
                    break;
                case "kk":
                    mBinding.ivLanguage.setImageResource(R.mipmap.ic_language006);
                    mBinding.tvLanguage.setText("قازاقشا");
                    break;
                case "vi":
                    mBinding.ivLanguage.setImageResource(R.mipmap.ic_language007);
                    mBinding.tvLanguage.setText("Tiếng Việt");
                    break;
                default:
                    mBinding.ivLanguage.setImageResource(R.mipmap.ic_language001);
                    mBinding.tvLanguage.setText("中文");
                    break;
            }
        }
        mBinding.ivRemember.setSelected(isRemember());
        if (isRemember()) {
            mBinding.etAccount.setText(SPUtils.getInstance().getString(Constants.REMEMBER_ACCOUNT, ""));
            mBinding.etPwd.setText(SPUtils.getInstance().getString(Constants.REMEMBER_PWD, ""));
        }
        mBinding.tvVersion.setText(getRstr(R.string.version) + "V" + PackageUtils.getPackageName(this));
        if (BuildConfig.DEBUG) {
            mBinding.butUrl.setVisibility(View.VISIBLE);
        } else {
            mBinding.butUrl.setVisibility(View.GONE);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvKefu:
                //联系客服
//                KefuDialog.showDialog(this, (language, area) -> {
//
//                });
                break;
            case R.id.tvShutdown:
                //关闭软件
                AppManager.getInstance().AppExit();
//                try {
//                    Runtime.getRuntime().exec("reboot -p"); //关机
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
                break;
            case R.id.linLanguage:
                //选择语言
                LanguagePop.showDialog(this, mBinding.ivLanguageMore, v, mBinding.linLanguage.getMeasuredWidth(), () -> {
                    AppManager.getInstance().finishAllActivity();
                    goToActivity(LauncherActivity.class);
                });
                break;
            case R.id.ivAccountClear:
                //清除账号输入
                mBinding.etAccount.setText("");
                break;
            case R.id.ivPwdClear:
                //清除密码输入
                mBinding.etPwd.setText("");
                break;
            case R.id.ivEye:
                //密码可见
                isEye = !isEye;
                mBinding.ivEye.setSelected(isEye);
                if (isEye) {
                    mBinding.etPwd.setInputType(InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD);//设置密码可见
                } else {
                    mBinding.etPwd.setInputType(InputType.TYPE_TEXT_VARIATION_PASSWORD | InputType.TYPE_CLASS_TEXT);//设置密码不可见
                }
                mBinding.etPwd.setSelection(mBinding.etPwd.getText().length());
                break;
            case R.id.linRemember:
                //记住密码
                if (isRemember()) {
                    mBinding.ivRemember.setSelected(false);
                    SPUtils.getInstance().put(Constants.IS_REMEMBER, "");
                } else {
                    mBinding.ivRemember.setSelected(true);
                    SPUtils.getInstance().put(Constants.IS_REMEMBER, Constants.IS_REMEMBER);
                }
                break;
            case R.id.tvForget:
                //忘记密码
                goToActivity(ForgetActivity.class);
                break;
            case R.id.tvConfirm:
                //登录
                if (TextUtils.isEmpty(account)) {
                    showToast(1, getRstr(R.string.input_mobile));
                    return;
                }
                if (TextUtils.isEmpty(pwd)) {
                    showToast(1, getRstr(R.string.input_pwd));
                    return;
                }
                if (TextUtils.isEmpty(device_id)) {
                    if (PermissionUtils.checkPermissionsGroup(this, 5)) {
                        device_id = SystemUtils.getDeviceId(this);
                        SPUtils.getInstance().put(Constants.DEVICE_ID, device_id);
                        postLogin();
                    } else {
                        PermissionUtils.requestPermissions(this, Constants.PERMISSION_PHONE, 5);
                    }
                } else {
                    postLogin();
                }
//                postLogin();
                break;
            case R.id.butUrl:
                //设置域名（测试用）
                goToActivity(TestActivity.class);
                break;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        //0.测试 1.正式 2.自定义
        int i = SPUtils.getInstance().getInt("test", 0);
        switch (i) {
            case 1:
                mBinding.butUrl.setText("正式环境");
                break;
            case 2:
                mBinding.butUrl.setText("自定义环境");
                break;
            case 3:
                mBinding.butUrl.setText("开发环境");
                break;
            default:
                mBinding.butUrl.setText("测试环境");
                break;
        }
    }

    /**
     * 根据账号密码输入改变按钮背景
     */
    private void setTextBg() {
        if (TextUtils.isEmpty(account)) {
            mBinding.tvConfirm.setBackgroundResource(R.drawable.shape_green_tm_5);
            return;
        }
        if (TextUtils.isEmpty(pwd)) {
            mBinding.tvConfirm.setBackgroundResource(R.drawable.shape_green_tm_5);
            return;
        }
        mBinding.tvConfirm.setBackgroundResource(R.drawable.shape_green_5);
    }

    /**
     * 登录
     */
    private void postLogin() {
        if (NetworkUtils.isConnected()) {
            //走接口
            Map<String, Object> params = new HashMap<>();
            params.put("staff_account", account);
            params.put("staff_pwd", pwd);
            params.put("macId", device_id);
            showDialog();
            RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                    ZURL.getLogin(),
                    params,
                    new RequestListener<String>() {
                        @Override
                        public void success(String s) {
                            hideDialog();
                            LoginBaseData baseData = new Gson().fromJson(s, LoginBaseData.class);
                            if (baseData == null) {
                                showToast(1, getRstr(R.string.login_fail));
                                return;
                            }
                            if (baseData.getStatus() != 0) {
                                showToast(1, baseData.getMsg());
                                return;
                            }
                            LoginData data = baseData.getData();
                            if (data == null) {
                                showToast(1, baseData.getMsg());
                                return;
                            }
                            SPUtils.getInstance().put(Constants.LOGIN_ID, data.getLogin_id());//登录id
                            BaseApplication.getInstance().getLoginData(data.getLogin_id());
                            SPUtils.getInstance().put(Constants.REMEMBER_ACCOUNT, account);//登录账号
                            SPUtils.getInstance().put(Constants.REMEMBER_PWD, pwd);//登录密码
                            showToast(0, getRstr(R.string.login_success));

                            data.setAccount(account);
                            data.setPwd(pwd);
                            data.save();
                            startActivity(new Intent(LoginActivity.this, LauncherActivity.class)
                                    .putExtra("type", 1)
                            );
                            finish();
                        }
                    });
        } else {
            //走离线
            List<LoginData> list = LitePal
                    .where("account = ? AND pwd = ?", account, pwd)
                    .find(LoginData.class);
            if (list == null) {
                showToast(1, getRstr(R.string.no_network_connect));
                return;
            }
            if (list.size() < 1) {
                showToast(1, getRstr(R.string.no_network_connect));
                return;
            }
            SPUtils.getInstance().put(Constants.LOGIN_ID, list.get(0).getLogin_id());
            BaseApplication.getInstance().getLoginData(list.get(0).getLogin_id());
            startActivity(new Intent(LoginActivity.this, LauncherActivity.class)
                    .putExtra("type", 1)
            );
            finish();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION_PHONE:
                //设备信息
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showToast(1, getRstr(R.string.no_permission_tips));
                } else {
                    device_id = SystemUtils.getDeviceId(this);
                    SPUtils.getInstance().put(Constants.DEVICE_ID, device_id);
                    postLogin();
                }
                break;
        }
    }
}
