package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.OrderData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:网单-网单订单（适配器）
 * Created by jingang on 2024/6/17
 */
public class OrderAdapter extends BaseAdapter<OrderData> {

    public OrderAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_order;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvNo, tvName, tvMobile, tvType, tvTotal, tvCount, tvStatus;
        tvNo = holder.getView(R.id.tvItemNo);
        tvName = holder.getView(R.id.tvItemName);
        tvMobile = holder.getView(R.id.tvItemMobile);
        tvType = holder.getView(R.id.tvItemType);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvCount = holder.getView(R.id.tvItemCount);
        tvStatus = holder.getView(R.id.tvItemStatus);

        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvNo.setTextColor(mContext.getResources().getColor(R.color.white));
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
            tvMobile.setTextColor(mContext.getResources().getColor(R.color.white));
            tvType.setTextColor(mContext.getResources().getColor(R.color.white));
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.white));
            tvCount.setTextColor(mContext.getResources().getColor(R.color.white));
            tvStatus.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            tvNo.setTextColor(mContext.getResources().getColor(R.color.black));
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
            tvMobile.setTextColor(mContext.getResources().getColor(R.color.black));
            tvType.setTextColor(mContext.getResources().getColor(R.color.black));
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.black));
            tvCount.setTextColor(mContext.getResources().getColor(R.color.black));
            tvStatus.setTextColor(mContext.getResources().getColor(R.color.black));
            if (position % 2 == 0) {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
            } else {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
            }
        }

        tvNo.setText(mDataList.get(position).getSaleListUnique());
        tvName.setText(TextUtils.isEmpty(mDataList.get(position).getSaleListName()) ? "-" : mDataList.get(position).getSaleListName());
        tvMobile.setText(TextUtils.isEmpty(mDataList.get(position).getSaleListPhone()) ? "-" : mDataList.get(position).getSaleListPhone());
        //配送方式1:送货上门 2:自提
        if (mDataList.get(position).getShipping_method() == 1) {
            tvType.setText(getRstr(R.string.order_type2));
        } else {
            tvType.setText(getRstr(R.string.order_type1));
        }
        if (mDataList.get(position).getSaleListDelfee() == 0) {
            tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getActuallyReceived()));
        } else {
            tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getActuallyReceived()) + "(含配送费:" + DFUtils.getNum2(mDataList.get(position).getSaleListDelfee()) + ")");
        }
        tvCount.setText(DFUtils.getNum(mDataList.get(position).getTotalCount()));
        //订单状态 1.无效订单 2.新订单（待发货） 3.已发货（代收货）4.已完成（已收货）5.已取消 6.待评论 7.待骑手配送 10.配送异常
        switch (mDataList.get(position).getHandleStateCode()) {
            case 2:
                tvStatus.setText(getRstr(R.string.order_status0));
                break;
            case 3:
                tvStatus.setText(getRstr(R.string.order_status3));
                break;
            case 4:
                tvStatus.setText(getRstr(R.string.order_status6));
                break;
            case 5:
                tvStatus.setText(getRstr(R.string.order_status8));
                break;
            case 6:
                tvStatus.setText(getRstr(R.string.order_status5));
                break;
            case 7:
                //配送单待确认
                tvStatus.setText(getRstr(R.string.order_status1));
                break;
            case 8:
                tvStatus.setText(getRstr(R.string.order_status7));
                break;
            case 9:
                tvStatus.setText(getRstr(R.string.order_status4));
                break;
            case 10:
                tvStatus.setText(getRstr(R.string.order_status2));
                break;
            default:
                tvStatus.setText("");
                break;
        }
        //退款单
        if (!TextUtils.isEmpty(mDataList.get(position).getRetListUnique())) {
            tvStatus.setText(getRstr(R.string.refund_order));
        }
    }
}
