package com.yxl.cashier_retail.ui.bean;

import java.util.List;

/**
 * Describe:会员统计（实体类）-写出花来
 * Created by jingang on 2024/8/5
 */
public class MemberStatisticsData {
//    /**
//     * customerType : [{"value":0,"key":"赊欠会员"},{"value":14,"key":"普通会员"}]
//     * cusPoint : [{"value":12,"key":"0-100"},{"value":1,"key":"100-200"},{"value":0,"key":"200-500"},{"value":0,"key":"500以上"}]
//     * creditMoney : 0
//     * customerNumber : 14
//     * storedMoney : 6749.0
//     * customerLevel : [{"value":11,"key":"铜牌会员"},{"value":1,"key":"白银会员"},{"value":1,"key":"黄金会员"},{"value":0,"key":"钻石会员"}]
//     * cusBalance : [{"value":9,"key":"0-50"},{"value":1,"key":"50-100"},{"value":0,"key":"100-200"},{"value":1,"key":"200-500"},{"value":2,"key":"500以上"}]
//     */
//
//    private int creditMoney;
//    private int customerNumber;
//    private double storedMoney;
//    private List<CustomerTypeBean> customerType;//会员类型占比
//    private List<CusPointBean> cusPoint;//会员积分占比
//    private List<CustomerLevelBean> customerLevel;//会员等级占比
//    private List<CusBalanceBean> cusBalance;//储值金额占比
//
//    public int getCreditMoney() {
//        return creditMoney;
//    }
//
//    public void setCreditMoney(int creditMoney) {
//        this.creditMoney = creditMoney;
//    }
//
//    public int getCustomerNumber() {
//        return customerNumber;
//    }
//
//    public void setCustomerNumber(int customerNumber) {
//        this.customerNumber = customerNumber;
//    }
//
//    public double getStoredMoney() {
//        return storedMoney;
//    }
//
//    public void setStoredMoney(double storedMoney) {
//        this.storedMoney = storedMoney;
//    }
//
//    public List<CustomerTypeBean> getCustomerType() {
//        return customerType;
//    }
//
//    public void setCustomerType(List<CustomerTypeBean> customerType) {
//        this.customerType = customerType;
//    }
//
//    public List<CusPointBean> getCusPoint() {
//        return cusPoint;
//    }
//
//    public void setCusPoint(List<CusPointBean> cusPoint) {
//        this.cusPoint = cusPoint;
//    }
//
//    public List<CustomerLevelBean> getCustomerLevel() {
//        return customerLevel;
//    }
//
//    public void setCustomerLevel(List<CustomerLevelBean> customerLevel) {
//        this.customerLevel = customerLevel;
//    }
//
//    public List<CusBalanceBean> getCusBalance() {
//        return cusBalance;
//    }
//
//    public void setCusBalance(List<CusBalanceBean> cusBalance) {
//        this.cusBalance = cusBalance;
//    }
//
//    public static class CustomerTypeBean {
//        /**
//         * value : 0
//         * key : 赊欠会员
//         */
//
//        private int value;
//        private String key;
//
//        public int getValue() {
//            return value;
//        }
//
//        public void setValue(int value) {
//            this.value = value;
//        }
//
//        public String getKey() {
//            return key;
//        }
//
//        public void setKey(String key) {
//            this.key = key;
//        }
//    }
//
//    public static class CusPointBean {
//        /**
//         * value : 12
//         * key : 0-100
//         */
//
//        private int value;
//        private String key;
//
//        public int getValue() {
//            return value;
//        }
//
//        public void setValue(int value) {
//            this.value = value;
//        }
//
//        public String getKey() {
//            return key;
//        }
//
//        public void setKey(String key) {
//            this.key = key;
//        }
//    }
//
//    public static class CustomerLevelBean {
//        /**
//         * value : 11
//         * key : 铜牌会员
//         */
//
//        private int value;
//        private String key;
//
//        public int getValue() {
//            return value;
//        }
//
//        public void setValue(int value) {
//            this.value = value;
//        }
//
//        public String getKey() {
//            return key;
//        }
//
//        public void setKey(String key) {
//            this.key = key;
//        }
//    }
//
//    public static class CusBalanceBean {
//        /**
//         * value : 9
//         * key : 0-50
//         */
//
//        private int value;
//        private String key;
//
//        public int getValue() {
//            return value;
//        }
//
//        public void setValue(int value) {
//            this.value = value;
//        }
//
//        public String getKey() {
//            return key;
//        }
//
//        public void setKey(String key) {
//            this.key = key;
//        }
//    }

    /**
     * customerType : {"赊欠会员":0,"储值会员":0,"普通会员":0}
     * cusPoint : {"500以上":0,"200-500":0,"100-200":0,"0-100":0}
     * creditMoney : 0
     * customerNumber : 0
     * storedMoney : 0
     * customerLevel : {"白银会员":0,"黄金会员":0,"钻石会员":0,"铜牌会员":0}
     * cusBalance : {"500以上":0,"200-500":0,"50-100":0,"100-200":0,"0-50":0}
     */

    private String customerType;//会员类型占比
    private String cusPoint;//会员积分占比
    private double creditMoney;//赊欠金额
    private int customerNumber;//会员总数
    private double storedMoney;//储值金额
    private String customerLevel;//会员等级占比
    private String cusBalance;//储值金额占比

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getCusPoint() {
        return cusPoint;
    }

    public void setCusPoint(String cusPoint) {
        this.cusPoint = cusPoint;
    }

    public double getCreditMoney() {
        return creditMoney;
    }

    public void setCreditMoney(double creditMoney) {
        this.creditMoney = creditMoney;
    }

    public int getCustomerNumber() {
        return customerNumber;
    }

    public void setCustomerNumber(int customerNumber) {
        this.customerNumber = customerNumber;
    }

    public double getStoredMoney() {
        return storedMoney;
    }

    public void setStoredMoney(double storedMoney) {
        this.storedMoney = storedMoney;
    }

    public String getCustomerLevel() {
        return customerLevel;
    }

    public void setCustomerLevel(String customerLevel) {
        this.customerLevel = customerLevel;
    }

    public String getCusBalance() {
        return cusBalance;
    }

    public void setCusBalance(String cusBalance) {
        this.cusBalance = cusBalance;
    }


}
