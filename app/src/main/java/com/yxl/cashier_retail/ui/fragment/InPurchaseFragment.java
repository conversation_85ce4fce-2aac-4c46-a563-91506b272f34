package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.view.View;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmInPurchaseBinding;
import com.yxl.cashier_retail.ui.adapter.PurchaseAdapter;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.PurchaseListData;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:入库-购销单
 * Created by jingang on 2024/5/23
 */
@SuppressLint({"NonConstantResourceId", "NotifyDataSetChanged"})
public class InPurchaseFragment extends BaseFragment<FmInPurchaseBinding> implements View.OnClickListener {
    private int index,//0.全部 1.待入库 2.待付款 3.已打款 4.已完成
            currentIndex;

    //购销单列表
    private PurchaseAdapter mAdapter;
    private List<PurchaseListData> dataList = new ArrayList<>();

    @Override
    protected FmInPurchaseBinding getViewBinding() {
        return FmInPurchaseBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.tvType0.setOnClickListener(this);
        mBinding.tvType1.setOnClickListener(this);
        mBinding.tvType2.setOnClickListener(this);
        mBinding.tvType3.setOnClickListener(this);
        mBinding.tvType4.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected void initData() {
        getPurchaseList();
    }

    @Override
    public void onClick(View v) {
        if (isQuicklyClick()) {
            return;
        }
        switch (v.getId()) {
            case R.id.tvType0:
                //全部
                index = 0;
                fragmentControl();
                break;
            case R.id.tvType1:
                //待入库
                index = 1;
                fragmentControl();
                break;
            case R.id.tvType2:
                //待付款
                index = 2;
                fragmentControl();
                break;
            case R.id.tvType3:
                //已打款
                index = 3;
                fragmentControl();
                break;
            case R.id.tvType4:
                //已完成
                index = 4;
                fragmentControl();
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.PURCHASE_LIST:
                page = 1;
                getPurchaseList();
                break;
        }
    }

    /**
     * 控制fragment的变化
     */
    public void fragmentControl() {
        if (currentIndex != index) {
            removeBottomColor();
            setBottomColor();
            currentIndex = index;
            page = 1;
            getPurchaseList();
        }
    }

    /**
     * 设置底部栏按钮变色
     */
    private void setBottomColor() {
        switch (index) {
            case 0:
                mBinding.tvType0.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.tvType0.setTextColor(getResources().getColor(R.color.white));
                break;
            case 1:
                mBinding.tvType1.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.tvType1.setTextColor(getResources().getColor(R.color.white));
                break;
            case 2:
                mBinding.tvType2.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.tvType2.setTextColor(getResources().getColor(R.color.white));
                break;
            case 3:
                mBinding.tvType3.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.tvType3.setTextColor(getResources().getColor(R.color.white));
                break;
            case 4:
                mBinding.tvType4.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.tvType4.setTextColor(getResources().getColor(R.color.white));
                break;
            default:
                break;
        }
    }

    /**
     * 清除底部栏颜色
     */
    private void removeBottomColor() {
        switch (currentIndex) {
            case 0:
                mBinding.tvType0.setBackgroundResource(0);
                mBinding.tvType0.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
                break;
            case 1:
                mBinding.tvType1.setBackgroundResource(0);
                mBinding.tvType1.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
                break;
            case 2:
                mBinding.tvType2.setBackgroundResource(0);
                mBinding.tvType2.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
                break;
            case 3:
                mBinding.tvType3.setBackgroundResource(0);
                mBinding.tvType3.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
                break;
            case 4:
                mBinding.tvType4.setBackgroundResource(0);
                mBinding.tvType4.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
                break;
            default:
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new PurchaseAdapter(getActivity(), 1);
        mBinding.vSmartrefreshlayout.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            EventBusManager.getInstance().getGlobaEventBus().post(new EventData(String.valueOf(dataList.get(position).getId()), Constants.PURCHASE_INFO));
        });
        mBinding.vSmartrefreshlayout.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getPurchaseList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getPurchaseList();
            }
        });
    }

    /**
     * 购销单列表
     */
    private void getPurchaseList() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", BaseApplication.getInstance().getShopUnique());
        if ((index != 0)) {
            params.put("status", index);
        }
        params.put("pageIndex", page);
        params.put("pageSize", Constants.limit);
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponseList(this,
                ZURL.getPurchaseList(),
                params,
                PurchaseListData.class,
                new RequestListListener<PurchaseListData>() {
                    @Override
                    public void onResult(List<PurchaseListData> list) {
                        hideDialog();
                        if (page == 1) {
                            mBinding.vSmartrefreshlayout.smartRefreshLayout.finishRefresh();
                            dataList.clear();
                        } else {
                            mBinding.vSmartrefreshlayout.smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.GONE);
                            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        if (page == 1) {
                            mBinding.vSmartrefreshlayout.smartRefreshLayout.finishRefresh();
                        } else {
                            mBinding.vSmartrefreshlayout.smartRefreshLayout.finishLoadMore();
                        }
                        if (dataList.size() > 0) {
                            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.GONE);
                            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

}
