package com.yxl.cashier_retail.ui.contract;

import com.yxl.cashier_retail.ui.bean.CateData;
import com.yxl.cashier_retail.ui.bean.GoodsInfoData;
import com.yxl.cashier_retail.ui.bean.GoodsListData;
import com.yxl.commonlibrary.base.BasePresenter;
import com.yxl.commonlibrary.base.BaseView;

import java.util.List;

/**
 * Describe:商品
 * Created by jingang on 2024/5/23
 */
public class GoodsContract {
    public interface View extends BaseView {
        void onListSuccess(List<GoodsListData> list);

        void onListError(String msg);

        void onInfoSuccess(GoodsInfoData data);

        void onInfoError(String msg);

        void onCateList(List<CateData> list);

        void onCateListError(String msg);
    }

    public interface Presenter extends BasePresenter<View> {
        void getGoodsList(String shopUnique, String groupUnique, String keyWords, int page, int limit);

        void getGoodsInfo(String shopUnique, String goodsBarcode);

        void getCateList();
    }
}
