package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogRefundRefuseEditBinding;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:dialog（退款拒绝原因-编辑）
 * Created by jingang on 2024/6/19
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class RefundRefuseEditDialog extends BaseDialog<DialogRefundRefuseEditBinding> implements View.OnClickListener {
    private static String name;

    public static void showDialog(Activity activity, String name, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        RefundRefuseEditDialog.listener = listener;
        RefundRefuseEditDialog.name = name;
        RefundRefuseEditDialog dialog = new RefundRefuseEditDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public RefundRefuseEditDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        if (TextUtils.isEmpty(name)) {
            mBinding.tvDialogTitle.setText(getRstr(R.string.refuse_reason_add));
            mBinding.etDialog.setText("");
        } else {
            mBinding.tvDialogTitle.setText(getRstr(R.string.refuse_reason_edit));
            mBinding.etDialog.setText(name);
        }
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        mBinding.etDialog.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                name = s.toString().trim();
                mBinding.tvDialogCount.setText(name.length() + "/200");
            }
        });
    }

    @Override
    protected DialogRefundRefuseEditBinding getViewBinding() {
        return DialogRefundRefuseEditBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //确认
                if (TextUtils.isEmpty(mBinding.etDialog.getText().toString().trim())) {
                    showToast(1, getRstr(R.string.input_refuse_reason));
                    return;
                }
                if (listener != null) {
                    listener.onConfirm(mBinding.etDialog.getText().toString().trim());
                    dismiss();
                }
                break;
        }
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(String name);
    }
}
