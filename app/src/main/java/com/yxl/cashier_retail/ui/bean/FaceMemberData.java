package com.yxl.cashier_retail.ui.bean;

/**
 * Describe:通过人脸搜索会员信息（实体类）
 * Created by jingang on 2024/9/9
 */
public class FaceMemberData {

    /**
     * status : 1
     * msg : 查询成功！
     * data : {}
     * data1 : null
     * address : null
     */

    private int status;
    private String msg;
    private DataBean data;
    private Object data1;
    private Object address;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public Object getData1() {
        return data1;
    }

    public void setData1(Object data1) {
        this.data1 = data1;
    }

    public Object getAddress() {
        return address;
    }

    public void setAddress(Object address) {
        this.address = address;
    }

    public static class DataBean {

        /**
         * cusWeixin :
         * cus_level_name : 无等级会员
         * cusName : 微信用户
         * cusRegeditDate : 2024-01-25 10:26:05
         * cusBirthday : 2000-01-01
         * cus_balance : 1000000.00
         * beansUseCount : 0.52
         * cusQQ :
         * cusPhone : 17865069350
         * cusHeadPath : image/face/f8d1cf71a47846cd860d444dcf57e436.jpg
         * cusEmail :
         * cusId : 13536
         * shopUnique :
         * cusPoints : 0
         * cusTotal : 0
         * cusPassword :
         * cus_status : 1
         * buyhoBeans : 199
         * cusFaceImage : image/face/f8d1cf71a47846cd860d444dcf57e436.jpg
         * cusUnique : 1706149565079
         * cusFaceToken : 35c8e437ee06ef29e22c0d08bc5a600f
         * cus_remark :
         * cusSex : 1
         * cusUsePoints : 0
         * cusType :
         * totalPoints : 0
         * cus_level_id : 0
         */

        private String cusWeixin;
        private String cus_level_name;
        private String cusName;
        private String cusRegeditDate;
        private String cusBirthday;
        private String cus_balance;//会员余额 为啥是string
        private double beansUseCount;
        private String cusQQ;
        private String cusPhone;
        private String cusHeadPath;
        private String cusEmail;
        private String cusId;
        private String shopUnique;
        private int cusPoints;
        private int cusTotal;
        private String cusPassword;
        private String cus_status;
        private int buyhoBeans;
        private String cusFaceImage;
        private String cusUnique;
        private String cusFaceToken;
        private String cus_remark;
        private int cusSex;
        private int cusUsePoints;
        private String cusType;
        private int totalPoints;
        private int cus_level_id;

        public String getCusWeixin() {
            return cusWeixin;
        }

        public void setCusWeixin(String cusWeixin) {
            this.cusWeixin = cusWeixin;
        }

        public String getCus_level_name() {
            return cus_level_name;
        }

        public void setCus_level_name(String cus_level_name) {
            this.cus_level_name = cus_level_name;
        }

        public String getCusName() {
            return cusName;
        }

        public void setCusName(String cusName) {
            this.cusName = cusName;
        }

        public String getCusRegeditDate() {
            return cusRegeditDate;
        }

        public void setCusRegeditDate(String cusRegeditDate) {
            this.cusRegeditDate = cusRegeditDate;
        }

        public String getCusBirthday() {
            return cusBirthday;
        }

        public void setCusBirthday(String cusBirthday) {
            this.cusBirthday = cusBirthday;
        }

        public String getCus_balance() {
            return cus_balance;
        }

        public void setCus_balance(String cus_balance) {
            this.cus_balance = cus_balance;
        }

        public double getBeansUseCount() {
            return beansUseCount;
        }

        public void setBeansUseCount(double beansUseCount) {
            this.beansUseCount = beansUseCount;
        }

        public String getCusQQ() {
            return cusQQ;
        }

        public void setCusQQ(String cusQQ) {
            this.cusQQ = cusQQ;
        }

        public String getCusPhone() {
            return cusPhone;
        }

        public void setCusPhone(String cusPhone) {
            this.cusPhone = cusPhone;
        }

        public String getCusHeadPath() {
            return cusHeadPath;
        }

        public void setCusHeadPath(String cusHeadPath) {
            this.cusHeadPath = cusHeadPath;
        }

        public String getCusEmail() {
            return cusEmail;
        }

        public void setCusEmail(String cusEmail) {
            this.cusEmail = cusEmail;
        }

        public String getCusId() {
            return cusId;
        }

        public void setCusId(String cusId) {
            this.cusId = cusId;
        }

        public String getShopUnique() {
            return shopUnique;
        }

        public void setShopUnique(String shopUnique) {
            this.shopUnique = shopUnique;
        }

        public int getCusPoints() {
            return cusPoints;
        }

        public void setCusPoints(int cusPoints) {
            this.cusPoints = cusPoints;
        }

        public int getCusTotal() {
            return cusTotal;
        }

        public void setCusTotal(int cusTotal) {
            this.cusTotal = cusTotal;
        }

        public String getCusPassword() {
            return cusPassword;
        }

        public void setCusPassword(String cusPassword) {
            this.cusPassword = cusPassword;
        }

        public String getCus_status() {
            return cus_status;
        }

        public void setCus_status(String cus_status) {
            this.cus_status = cus_status;
        }

        public int getBuyhoBeans() {
            return buyhoBeans;
        }

        public void setBuyhoBeans(int buyhoBeans) {
            this.buyhoBeans = buyhoBeans;
        }

        public String getCusFaceImage() {
            return cusFaceImage;
        }

        public void setCusFaceImage(String cusFaceImage) {
            this.cusFaceImage = cusFaceImage;
        }

        public String getCusUnique() {
            return cusUnique;
        }

        public void setCusUnique(String cusUnique) {
            this.cusUnique = cusUnique;
        }

        public String getCusFaceToken() {
            return cusFaceToken;
        }

        public void setCusFaceToken(String cusFaceToken) {
            this.cusFaceToken = cusFaceToken;
        }

        public String getCus_remark() {
            return cus_remark;
        }

        public void setCus_remark(String cus_remark) {
            this.cus_remark = cus_remark;
        }

        public int getCusSex() {
            return cusSex;
        }

        public void setCusSex(int cusSex) {
            this.cusSex = cusSex;
        }

        public int getCusUsePoints() {
            return cusUsePoints;
        }

        public void setCusUsePoints(int cusUsePoints) {
            this.cusUsePoints = cusUsePoints;
        }

        public String getCusType() {
            return cusType;
        }

        public void setCusType(String cusType) {
            this.cusType = cusType;
        }

        public int getTotalPoints() {
            return totalPoints;
        }

        public void setTotalPoints(int totalPoints) {
            this.totalPoints = totalPoints;
        }

        public int getCus_level_id() {
            return cus_level_id;
        }

        public void setCus_level_id(int cus_level_id) {
            this.cus_level_id = cus_level_id;
        }
    }
}

