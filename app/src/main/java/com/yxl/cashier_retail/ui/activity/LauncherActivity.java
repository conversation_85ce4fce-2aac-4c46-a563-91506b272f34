package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.text.TextUtils;

import com.blankj.utilcode.util.NetworkUtils;
import com.google.gson.Gson;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityLauncherBinding;
import com.yxl.cashier_retail.ui.bean.BaseGoodsData;
import com.yxl.cashier_retail.ui.bean.CatePcData;
import com.yxl.cashier_retail.ui.bean.DiscountData;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.ui.bean.PaymentData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;

import org.litepal.LitePal;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:启动页
 * Created by jingang on 2024/5/7
 */
@SuppressLint({"SetTextI18n", "MissingPermission"})
public class LauncherActivity extends BaseActivity<ActivityLauncherBinding> {

    @Override
    protected ActivityLauncherBinding getViewBinding() {
        return ActivityLauncherBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        if (TextUtils.isEmpty(getLoginId())) {
            goToActivity(LoginActivity.class);
            finish();
        } else {
            getGoodsList();
        }
    }

    @Override
    protected void initData() {
    }

    /**
     * 跳转到首页
     */
    private void gotoMain() {
        goToActivity(MainActivity.class);
        finish();
    }

    /**
     * 全部商品列表
     */
    private void getGoodsList() {
        if (NetworkUtils.isConnected()) {
            Map<String, Object> params = new HashMap<>();
            params.put("shop_unique", getShopUnique());
            params.put("today", 2);
            RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                    ZURL.getGoodsListPc(),
                    params,
                    new RequestListener<String>() {
                        @Override
                        public void success(String s) {
                            BaseGoodsData data = new Gson().fromJson(s, BaseGoodsData.class);
                            if (data == null) {
                                mBinding.progressBar.setProgress(1);
                                mBinding.tvProgress.setText("20%");
                                getCateList();
                                return;
                            }
                            if (data.getStatus() != 0) {
                                mBinding.progressBar.setProgress(1);
                                mBinding.tvProgress.setText("20%");
                                getCateList();
                                return;
                            }

                            if (LitePal.findFirst(GoodsData.class) != null) {
                                LitePal.deleteAll(GoodsData.class);
                            }
                            LitePal.saveAll(data.getData());
                            mBinding.progressBar.setProgress(1);
                            mBinding.tvProgress.setText("20%");
                            getCateList();
                        }

                        @Override
                        public void onError(String msg) {
                            mBinding.progressBar.setProgress(1);
                            mBinding.tvProgress.setText("20%");
                            getCateList();
                        }
                    });
        } else {
            mBinding.progressBar.setProgress(1);
            mBinding.tvProgress.setText("20%");
            getCateList();
        }
    }

    /**
     * 虚拟分类列表
     */
    private void getCateList() {
        if (NetworkUtils.isConnected()) {
            //接口获取数据
            Map<String, Object> map = new HashMap<>();
            map.put("shopUnique", getShopUnique());
            RXHttpUtil.requestByFormPostAsResponseList(this,
                    ZURL.getCatePcList(),
                    map,
                    CatePcData.class,
                    new RequestListListener<CatePcData>() {
                        @Override
                        public void onResult(List<CatePcData> list) {
                            if (LitePal.findFirst(CatePcData.class) != null) {
                                LitePal.deleteAll(CatePcData.class);
                            }
                            LitePal.saveAll(list);
                            for (int i = 0; i < list.size(); i++) {
                                for (int j = 0; j < list.get(i).getGoodsList().size(); j++) {
                                    String barcode = list.get(i).getGoodsList().get(j).getGoods_barcode();
                                    GoodsData data = LitePal
                                            .where("goods_barcode = ?", barcode)
                                            .findFirst(GoodsData.class);
                                    if (data != null) {
                                        data.setVirtual_kind_unique(list.get(i).getGoods_kind_unique());
                                        data.save();
                                    }
                                }
                            }
                            mBinding.progressBar.setProgress(2);
                            mBinding.tvProgress.setText("40%");
                            initPaymentData();
                        }
                    });
        } else {
            mBinding.progressBar.setProgress(2);
            mBinding.tvProgress.setText("40%");
            initPaymentData();
        }
    }

    /**
     * 支付方式数据
     */
    private void initPaymentData() {
        List<PaymentData> paymentList = LitePal.findAll(PaymentData.class);
        if (paymentList.isEmpty()) {
            //生成数据
            List<PaymentData> list = new ArrayList<>();
            list.add(new PaymentData(1, true));
            list.add(new PaymentData(2, true));
            list.add(new PaymentData(3, true));
//            list.add(new PaymentData(4, true));
            list.add(new PaymentData(5, true));
            list.add(new PaymentData(6, true));
            list.add(new PaymentData(7, true));
            list.add(new PaymentData(8, true));

            if (LitePal.findFirst(PaymentData.class) != null) {
                LitePal.deleteAll(PaymentData.class);
            }
            LitePal.saveAll(list);
        }
        mBinding.progressBar.setProgress(4);
        mBinding.tvProgress.setText("80%");
        initDiscountData();
    }

    /**
     * 折扣数据
     */
    private void initDiscountData() {
        List<DiscountData> discountList = LitePal.findAll(DiscountData.class);
        if (discountList.isEmpty()) {
            //生成数据
            List<DiscountData> list = new ArrayList<>();
            list.add(new DiscountData(9.8));
            list.add(new DiscountData(9.5));
            list.add(new DiscountData(9));
            DiscountData data = LitePal.findFirst(DiscountData.class);
            if (data != null) {
                LitePal.deleteAll(DiscountData.class);
            }
            LitePal.saveAll(list);
        }
        mBinding.progressBar.setProgress(5);
        mBinding.tvProgress.setText("100%");
        gotoMain();
    }
}
