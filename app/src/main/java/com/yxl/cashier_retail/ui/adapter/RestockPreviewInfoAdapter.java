package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.ChildGoodsData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.utils.StringUtils;

/**
 * Describe:自采补货-预览-详情商品列表（适配器）
 * Created by jingang on 2024/6/6
 */
public class RestockPreviewInfoAdapter extends BaseAdapter<ChildGoodsData> {

    public RestockPreviewInfoAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_restock_preview_info;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvName, tvPrice, tvCount, tvTotal;
        tvName = holder.getView(R.id.tvItemName);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTotal = holder.getView(R.id.tvItemTotal);

        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoodsPicturepath()))
                .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img))
                .into(ivImg);
        tvName.setText(mDataList.get(position).getGoodsName());
        tvPrice.setText(DFUtils.getNum2(mDataList.get(position).getGoodsInPrice()));
        tvCount.setText(DFUtils.getNum4(mDataList.get(position).getGoodsCount()));
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getGoodsTotal()));
    }
}
