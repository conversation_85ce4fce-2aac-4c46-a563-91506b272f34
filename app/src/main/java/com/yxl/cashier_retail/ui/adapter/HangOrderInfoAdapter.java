package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.graphics.Paint;
import android.text.TextUtils;
import android.util.Log;
import android.util.TypedValue;
import android.view.View;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:挂单详情（适配器）
 * Created by jingang on 2024/5/29
 */
public class HangOrderInfoAdapter extends BaseAdapter<GoodsData> {
    private int type;//2.会员

    public void setType(int type) {
        this.type = type;
    }

    public HangOrderInfoAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_hang_order_info;
    }


    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvDiscount, tvDiscountMember, tvLost, tvActivity, tvName, tvPrice, tvPrice1, tvCount, tvTotal;
        tvDiscount = holder.getView(R.id.tvItemDiscount);
        tvDiscountMember = holder.getView(R.id.tvItemDiscountMember);
        tvLost = holder.getView(R.id.tvItemLost);
        tvActivity = holder.getView(R.id.tvItemActivity);
        tvName = holder.getView(R.id.tvItemName);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvPrice1 = holder.getView(R.id.tvItemPrice1);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTotal = holder.getView(R.id.tvItemTotal);

        double price,
                count = mDataList.get(position).getCartNum(),
                newPrice = mDataList.get(position).getNewPrice(),
                memberPrice = TextUtils.isEmpty(mDataList.get(position).getGoods_cus_price()) ? 0 : Double.parseDouble(mDataList.get(position).getGoods_cus_price()),
                salePrice = mDataList.get(position).getGoods_sale_price(),
                inPrice = mDataList.get(position).getGoods_in_price();
        Log.e(tag, "price = " + newPrice + " inPrice = " + inPrice + " salePrice = " + salePrice + " memberPrice = " + memberPrice);

        if (newPrice > 0) {
            price = newPrice;
            tvDiscount.setVisibility(price < salePrice ? View.VISIBLE : View.GONE);
            tvDiscountMember.setVisibility(View.GONE);
        } else {
            tvDiscount.setVisibility(View.GONE);
            if (type != 2) {
                price = salePrice;
                tvDiscountMember.setVisibility(View.GONE);
            } else {
                price = memberPrice;
                tvDiscountMember.setVisibility(View.VISIBLE);
            }
        }

        //原价
        if (price < salePrice) {
            tvPrice1.setVisibility(View.VISIBLE);
        } else {
            tvPrice1.setVisibility(View.GONE);
        }
        //售价低于进价
        if (price < inPrice) {
            tvLost.setVisibility(View.VISIBLE);
        } else {
            tvLost.setVisibility(View.GONE);
        }

        tvName.setText(mDataList.get(position).getGoods_name());
        if (price > 9999.99) {
            tvPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10);
        } else {
            tvPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
        }
        tvPrice.setText(DFUtils.getNum2(price));
        tvPrice1.setText(DFUtils.getNum2(salePrice));
        tvPrice1.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG);
        tvCount.setText(DFUtils.getNum4(count));
        double total = price * count;
        if (total > 9999.99) {
            tvTotal.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10);
        } else {
            tvTotal.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
        }
        tvTotal.setText(DFUtils.getNum2(total));
    }
}
