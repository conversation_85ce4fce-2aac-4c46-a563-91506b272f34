package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.MallLabelData;

import java.util.List;

/**
 * Describe:商城-商品标签（适配器）
 * Created by jingang on 2024/6/3
 */
public class MallLabelAdapter extends BaseAdapter<MallLabelData> {

    public MallLabelAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_mall_label;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        TextView tvName = holder.getView(R.id.tvItemName);
        if (mDataList.get(position).isSelect()) {
            tvName.setBackgroundResource(R.drawable.shape_green_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            tvName.setBackgroundResource(R.drawable.shape_d8_kuang_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
        }
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName = holder.getView(R.id.tvItemName);
        tvName.setText(mDataList.get(position).getLabel_name());
        if (mDataList.get(position).isSelect()) {
            tvName.setBackgroundResource(R.drawable.shape_green_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            tvName.setBackgroundResource(R.drawable.shape_d8_kuang_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
        }
    }
}
