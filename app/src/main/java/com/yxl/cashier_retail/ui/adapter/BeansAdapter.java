package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.BeansListData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:网单-百货豆（适配器）
 * Created by jingang on 2024/6/20
 */
public class BeansAdapter extends BaseAdapter<BeansListData.DataBean> {

    public BeansAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_beans;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvNo, tvName, tvCount, tvTime, tvStatus;
        tvNo = holder.getView(R.id.tvItemNo);
        tvName = holder.getView(R.id.tvItemName);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTime = holder.getView(R.id.tvItemTime);
        tvStatus = holder.getView(R.id.tvItemStatus);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
        }
        tvNo.setText(TextUtils.isEmpty(mDataList.get(position).getSaleListUnique()) ? "-" : mDataList.get(position).getSaleListUnique());
        tvName.setText(TextUtils.isEmpty(mDataList.get(position).getName()) ? "-" : mDataList.get(position).getName());
        tvCount.setText(DFUtils.getNum4(mDataList.get(position).getBeanCount()));
        tvTime.setText(TextUtils.isEmpty(mDataList.get(position).getSaletime()) ? "-" : mDataList.get(position).getSaletime());
        //订单状态只有出账有：0 待处理 1 到账 4 驳回
        switch (mDataList.get(position).getState()) {
            case 0:
                tvStatus.setText(getRstr(R.string.beans_status0));
                break;
            case 1:
                tvStatus.setText(getRstr(R.string.beans_status1));
                break;
            case 4:
                tvStatus.setText(getRstr(R.string.beans_status2));
                break;
            default:
                tvStatus.setText("");
                break;
        }
    }
}
