package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:退款订单（实体类）
 * Created by jingang on 2024/5/22
 */
public class RefundData implements Serializable {
    /**
     * id : null
     * saleListUnique : 17160238740880
     * cusName : 微信用户
     * cusProtrait : https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132
     * shopUnique : null
     * retListDatetime : 2024-05-18 17:19:20
     * retListTotal : 21.0
     * retListCount : 7.0
     * retListState : 2
     * retListStateMsg : 已退款
     * retListHandlestate : 3
     * retListHandlestateMsg : 已退款
     * retListRemarks : null
     * staffId : null
     * macId : null
     * retOrigin : 1
     * retOriginMsg : PC收银
     * retMoneyType : null
     * retMoneyTypeMsg : null
     * retListTotalMoney : null
     * retListOriginTotal : 15.19
     * outRefundNo : null
     * retBackDatetime : 2024-05-18 17:19:20
     * retListUnique : 36086195759369495
     * retListBean : null
     * retListReason : 收银机退款
     * retListDelfee : null
     * saleListPhone :
     * saleListAddress :
     * goodsList : [{"retListDetailId":1299,"saleListUnique":"17160238740880","retListUnique":"36086195759369495","goodsBarcode":"6944312688143","goodsName":"测试6944312688143","retListDetailCount":7,"retListDetailPrice":3,"retListOriginPrice":2.17,"saleListDetailId":null,"goodsPicturepath":""}]
     * payList : []
     */
    private boolean select;
    private Object id;
    private String saleListUnique;
    private String cusName;//退款人
    private String cusProtrait;
    private Object shopUnique;
    private String retListDatetime;//申请时间
    private double retListTotal;//退款总金额
    private double retListCount;
    private int retListState;
    private String retListStateMsg;
    private int retListHandlestate;//退款状态 1.待审核 3.已退款 4.已拒绝
    private String retListHandlestateMsg;//退款状态名称
    private Object retListRemarks;
    private Object staffId;
    private Object macId;
    private int retOrigin;
    private String retOriginMsg;
    private Object retMoneyType;
    private Object retMoneyTypeMsg;
    private Object retListTotalMoney;
    private double retListOriginTotal;
    private Object outRefundNo;
    private String retBackDatetime;
    private String retListUnique;//退款单号
    private Object retListBean;
    private String retListReason;
    private Object retListDelfee;
    private String saleListPhone;//联系电话
    private String saleListAddress;
    private List<GoodsListBean> goodsList;
    private List<?> payList;

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }

    public Object getId() {
        return id;
    }

    public void setId(Object id) {
        this.id = id;
    }

    public String getSaleListUnique() {
        return saleListUnique;
    }

    public void setSaleListUnique(String saleListUnique) {
        this.saleListUnique = saleListUnique;
    }

    public String getCusName() {
        return cusName;
    }

    public void setCusName(String cusName) {
        this.cusName = cusName;
    }

    public String getCusProtrait() {
        return cusProtrait;
    }

    public void setCusProtrait(String cusProtrait) {
        this.cusProtrait = cusProtrait;
    }

    public Object getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(Object shopUnique) {
        this.shopUnique = shopUnique;
    }

    public String getRetListDatetime() {
        return retListDatetime;
    }

    public void setRetListDatetime(String retListDatetime) {
        this.retListDatetime = retListDatetime;
    }

    public double getRetListTotal() {
        return retListTotal;
    }

    public void setRetListTotal(double retListTotal) {
        this.retListTotal = retListTotal;
    }

    public double getRetListCount() {
        return retListCount;
    }

    public void setRetListCount(double retListCount) {
        this.retListCount = retListCount;
    }

    public int getRetListState() {
        return retListState;
    }

    public void setRetListState(int retListState) {
        this.retListState = retListState;
    }

    public String getRetListStateMsg() {
        return retListStateMsg;
    }

    public void setRetListStateMsg(String retListStateMsg) {
        this.retListStateMsg = retListStateMsg;
    }

    public int getRetListHandlestate() {
        return retListHandlestate;
    }

    public void setRetListHandlestate(int retListHandlestate) {
        this.retListHandlestate = retListHandlestate;
    }

    public String getRetListHandlestateMsg() {
        return retListHandlestateMsg;
    }

    public void setRetListHandlestateMsg(String retListHandlestateMsg) {
        this.retListHandlestateMsg = retListHandlestateMsg;
    }

    public Object getRetListRemarks() {
        return retListRemarks;
    }

    public void setRetListRemarks(Object retListRemarks) {
        this.retListRemarks = retListRemarks;
    }

    public Object getStaffId() {
        return staffId;
    }

    public void setStaffId(Object staffId) {
        this.staffId = staffId;
    }

    public Object getMacId() {
        return macId;
    }

    public void setMacId(Object macId) {
        this.macId = macId;
    }

    public int getRetOrigin() {
        return retOrigin;
    }

    public void setRetOrigin(int retOrigin) {
        this.retOrigin = retOrigin;
    }

    public String getRetOriginMsg() {
        return retOriginMsg;
    }

    public void setRetOriginMsg(String retOriginMsg) {
        this.retOriginMsg = retOriginMsg;
    }

    public Object getRetMoneyType() {
        return retMoneyType;
    }

    public void setRetMoneyType(Object retMoneyType) {
        this.retMoneyType = retMoneyType;
    }

    public Object getRetMoneyTypeMsg() {
        return retMoneyTypeMsg;
    }

    public void setRetMoneyTypeMsg(Object retMoneyTypeMsg) {
        this.retMoneyTypeMsg = retMoneyTypeMsg;
    }

    public Object getRetListTotalMoney() {
        return retListTotalMoney;
    }

    public void setRetListTotalMoney(Object retListTotalMoney) {
        this.retListTotalMoney = retListTotalMoney;
    }

    public double getRetListOriginTotal() {
        return retListOriginTotal;
    }

    public void setRetListOriginTotal(double retListOriginTotal) {
        this.retListOriginTotal = retListOriginTotal;
    }

    public Object getOutRefundNo() {
        return outRefundNo;
    }

    public void setOutRefundNo(Object outRefundNo) {
        this.outRefundNo = outRefundNo;
    }

    public String getRetBackDatetime() {
        return retBackDatetime;
    }

    public void setRetBackDatetime(String retBackDatetime) {
        this.retBackDatetime = retBackDatetime;
    }

    public String getRetListUnique() {
        return retListUnique;
    }

    public void setRetListUnique(String retListUnique) {
        this.retListUnique = retListUnique;
    }

    public Object getRetListBean() {
        return retListBean;
    }

    public void setRetListBean(Object retListBean) {
        this.retListBean = retListBean;
    }

    public String getRetListReason() {
        return retListReason;
    }

    public void setRetListReason(String retListReason) {
        this.retListReason = retListReason;
    }

    public Object getRetListDelfee() {
        return retListDelfee;
    }

    public void setRetListDelfee(Object retListDelfee) {
        this.retListDelfee = retListDelfee;
    }

    public String getSaleListPhone() {
        return saleListPhone;
    }

    public void setSaleListPhone(String saleListPhone) {
        this.saleListPhone = saleListPhone;
    }

    public String getSaleListAddress() {
        return saleListAddress;
    }

    public void setSaleListAddress(String saleListAddress) {
        this.saleListAddress = saleListAddress;
    }

    public List<GoodsListBean> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<GoodsListBean> goodsList) {
        this.goodsList = goodsList;
    }

    public List<?> getPayList() {
        return payList;
    }

    public void setPayList(List<?> payList) {
        this.payList = payList;
    }

    public static class GoodsListBean {
        /**
         * retListDetailId : 1299
         * saleListUnique : 17160238740880
         * retListUnique : 36086195759369495
         * goodsBarcode : 6944312688143
         * goodsName : 测试6944312688143
         * retListDetailCount : 7.0
         * retListDetailPrice : 3.0
         * retListOriginPrice : 2.17
         * saleListDetailId : null
         * goodsPicturepath :
         */

        private int retListDetailId;
        private String saleListUnique;
        private String retListUnique;
        private String goodsBarcode;
        private String goodsName;
        private double retListDetailCount;
        private double retListDetailPrice;
        private double retListOriginPrice;
        private Object saleListDetailId;
        private String goodsPicturepath;

        public int getRetListDetailId() {
            return retListDetailId;
        }

        public void setRetListDetailId(int retListDetailId) {
            this.retListDetailId = retListDetailId;
        }

        public String getSaleListUnique() {
            return saleListUnique;
        }

        public void setSaleListUnique(String saleListUnique) {
            this.saleListUnique = saleListUnique;
        }

        public String getRetListUnique() {
            return retListUnique;
        }

        public void setRetListUnique(String retListUnique) {
            this.retListUnique = retListUnique;
        }

        public String getGoodsBarcode() {
            return goodsBarcode;
        }

        public void setGoodsBarcode(String goodsBarcode) {
            this.goodsBarcode = goodsBarcode;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public double getRetListDetailCount() {
            return retListDetailCount;
        }

        public void setRetListDetailCount(double retListDetailCount) {
            this.retListDetailCount = retListDetailCount;
        }

        public double getRetListDetailPrice() {
            return retListDetailPrice;
        }

        public void setRetListDetailPrice(double retListDetailPrice) {
            this.retListDetailPrice = retListDetailPrice;
        }

        public double getRetListOriginPrice() {
            return retListOriginPrice;
        }

        public void setRetListOriginPrice(double retListOriginPrice) {
            this.retListOriginPrice = retListOriginPrice;
        }

        public Object getSaleListDetailId() {
            return saleListDetailId;
        }

        public void setSaleListDetailId(Object saleListDetailId) {
            this.saleListDetailId = saleListDetailId;
        }

        public String getGoodsPicturepath() {
            return goodsPicturepath;
        }

        public void setGoodsPicturepath(String goodsPicturepath) {
            this.goodsPicturepath = goodsPicturepath;
        }
    }
}
