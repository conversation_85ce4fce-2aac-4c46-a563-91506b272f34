package com.yxl.cashier_retail.ui.bean;

import org.litepal.crud.LitePalSupport;

import java.io.Serializable;

/**
 * Describe:二级分类（实体类）
 * Created by jingang on 2024/5/27
 */
public class CateChildData extends LitePalSupport implements Serializable {
    /**
     * kindName : 常用
     * kindUnique : 99991
     */

    private int kindId;
    private String kindName;
    private String kindUnique;
    private String groupUnique;//父类编号
    private boolean check;
    private String editType;//1.不可编辑 2.可编辑

    public int getKindId() {
        return kindId;
    }

    public void setKindId(int kindId) {
        this.kindId = kindId;
    }

    public String getKindName() {
        return kindName;
    }

    public void setKindName(String kindName) {
        this.kindName = kindName;
    }

    public String getKindUnique() {
        return kindUnique;
    }

    public void setKindUnique(String kindUnique) {
        this.kindUnique = kindUnique;
    }

    public String getGroupUnique() {
        return groupUnique;
    }

    public void setGroupUnique(String groupUnique) {
        this.groupUnique = groupUnique;
    }

    public boolean isCheck() {
        return check;
    }

    public void setCheck(boolean check) {
        this.check = check;
    }

    public String getEditType() {
        return editType;
    }

    public void setEditType(String editType) {
        this.editType = editType;
    }
}
