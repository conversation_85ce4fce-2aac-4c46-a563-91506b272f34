package com.yxl.cashier_retail.ui.bean;

/**
 * Describe:统计-支付分类销量占比（实体类）
 * Created by jingang on 2024/8/30
 */
public class StatisticsPayTypeData {
    /**
     * saleTotal : 4065.04
     * saleListPayment : 2
     * payment : 支付宝
     * listCount : 52
     */

    private double saleTotal;//营业额
    private int saleListPayment;//消费类别编号
    private String payment;//支付方式
    private int listCount;//订单量

    public double getSaleTotal() {
        return saleTotal;
    }

    public void setSaleTotal(double saleTotal) {
        this.saleTotal = saleTotal;
    }

    public int getSaleListPayment() {
        return saleListPayment;
    }

    public void setSaleListPayment(int saleListPayment) {
        this.saleListPayment = saleListPayment;
    }

    public String getPayment() {
        return payment;
    }

    public void setPayment(String payment) {
        this.payment = payment;
    }

    public int getListCount() {
        return listCount;
    }

    public void setListCount(int listCount) {
        this.listCount = listCount;
    }
}
