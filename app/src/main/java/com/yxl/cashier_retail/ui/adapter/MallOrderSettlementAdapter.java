package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.EditText;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;

/**
 * Describe:商城-提交订单（适配器）
 * Created by jingang on 2024/6/4
 */
public class MallOrderSettlementAdapter extends BaseAdapter<String> {

    public MallOrderSettlementAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_mall_order_settlement;
    }

    @Override
    public int getItemCount() {
        return 3;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvCompany, tvFree, tvCoupons;
        tvCompany = holder.getView(R.id.tvItemCompany);
        RecyclerView recyclerView = holder.getView(R.id.rvItem);
        tvFree = holder.getView(R.id.tvItemFree);
        tvCoupons = holder.getView(R.id.tvItemCoupons);
        EditText etRemarks = holder.getView(R.id.etItemRemarks);

        MallOrderSettlementGoodsAdapter goodsAdapter = new MallOrderSettlementGoodsAdapter(mContext);
        recyclerView.setAdapter(goodsAdapter);
    }
}
