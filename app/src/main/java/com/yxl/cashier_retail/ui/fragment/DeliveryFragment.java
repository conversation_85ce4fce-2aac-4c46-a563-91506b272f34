package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.view.View;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmDeliveryBinding;
import com.yxl.cashier_retail.ui.bean.ShopDeliveryData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.HashMap;
import java.util.Map;

/**
 * Describe:网单-配送设置
 * Created by jingang on 2024/6/20
 */
@SuppressLint("NonConstantResourceId")
public class DeliveryFragment extends BaseFragment<FmDeliveryBinding> implements View.OnClickListener {
    private int deliveryType,//0.自配送 2.一刻钟配送
            duration;//预计时长
    private double range,//配送范围
            startOrder,//起送价格
            free;//免赔金额

    @Override
    protected FmDeliveryBinding getViewBinding() {
        return FmDeliveryBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivType0.setOnClickListener(this);
        mBinding.ivType1.setOnClickListener(this);
        mBinding.etRange.setOnEditorActionListener((v, actionId, event) -> {
            range = TextUtils.isEmpty(mBinding.etRange.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etRange.getText().toString().trim());
            postShopDeliveryEdit(1);
            return true;
        });
        mBinding.etStartOrder.setOnEditorActionListener((v, actionId, event) -> {
            startOrder = TextUtils.isEmpty(mBinding.etStartOrder.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etStartOrder.getText().toString().trim());
            postShopDeliveryEdit(2);
            return true;
        });
        mBinding.etFree.setOnEditorActionListener((v, actionId, event) -> {
            free = TextUtils.isEmpty(mBinding.etFree.getText().toString().trim()) ? 0 : Double.parseDouble(mBinding.etFree.getText().toString().trim());
            postShopDeliveryEdit(3);
            return true;
        });
        mBinding.etDuration.setOnEditorActionListener((v, actionId, event) -> {
            duration = TextUtils.isEmpty(mBinding.etDuration.getText().toString().trim()) ? 0 : Integer.parseInt(mBinding.etDuration.getText().toString().trim());
            postShopDeliveryEdit(4);
            return true;
        });
    }

    @Override
    protected void initData() {
        getShopDelivery();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivType0:
                //自配送
                if (deliveryType != 0) {
                    deliveryType = 0;
                    mBinding.ivType0.setSelected(true);
                    mBinding.ivType1.setSelected(false);
                    postShopDeliveryEdit(0);
                }
                getShopDelivery();
                break;
            case R.id.ivType1:
                //一刻钟配送
                if (deliveryType != 2) {
                    deliveryType = 2;
                    mBinding.ivType0.setSelected(false);
                    mBinding.ivType1.setSelected(true);
                    postShopDeliveryEdit(0);
                }
                break;
        }
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(ShopDeliveryData data) {
        if (data == null) {
            return;
        }
        deliveryType = data.getIs_order_taking();
        range = data.getDistribution_scope();
        startOrder = data.getTake_fee();
        free = data.getTake_free_price();
        duration = data.getTake_estimate_time();
        if (deliveryType == 2) {
            mBinding.ivType0.setSelected(false);
            mBinding.ivType1.setSelected(true);
        } else {
            mBinding.ivType0.setSelected(true);
            mBinding.ivType1.setSelected(false);
        }
        mBinding.etRange.setText(DFUtils.getNum2(range));
        mBinding.etStartOrder.setText(DFUtils.getNum2(startOrder));
        mBinding.etFree.setText(DFUtils.getNum2(free));
        mBinding.etDuration.setText(DFUtils.getNum2(duration));
    }

    /**
     * 查询店铺配送设置
     */
    private void getShopDelivery() {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getShopDelivery(),
                params,
                ShopDeliveryData.class,
                new RequestListener<ShopDeliveryData>() {
                    @Override
                    public void success(ShopDeliveryData data) {
                        hideDialog();
                        setUI(data);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 编辑店铺配送设置
     *
     * @param type 0.配送方式 1.配送范围 2.起送价格 3.免配金额 4.预计时长
     */
    private void postShopDeliveryEdit(int type) {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        switch (type) {
            case 1:
                params.put("distribution_scope", range);
                break;
            case 2:
                params.put("take_fee", startOrder);
                break;
            case 3:
                params.put("take_free_price", free);
                break;
            case 4:
                params.put("take_estimate_time", Integer.parseInt(String.valueOf(duration)));
                break;
            default:
                params.put("delivery_type", Integer.parseInt(String.valueOf(deliveryType)));
                break;
        }
        showDialog();
        hideSoftInput(getActivity());
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getShopDeliveryEdit(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }
}
