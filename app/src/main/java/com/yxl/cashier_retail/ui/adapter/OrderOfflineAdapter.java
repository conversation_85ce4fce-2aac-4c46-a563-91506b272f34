package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.OrderOfflineData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:离线订单（适配器）
 * Created by jingang on 2024/9/18
 */
public class OrderOfflineAdapter extends BaseAdapter<OrderOfflineData> {

    public OrderOfflineAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_order_offline;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvTime, tvTotal, tvMoney, tvCount;
        tvTime = holder.getView(R.id.tvItemTime);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvMoney = holder.getView(R.id.tvItemMoney);
        tvCount = holder.getView(R.id.tvItemCount);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        }
        tvTime.setText(mDataList.get(position).getCreateTime());
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getSaleListTotal()));
        tvMoney.setText(DFUtils.getNum2(mDataList.get(position).getSaleListActuallyReceived()));
        tvCount.setText(DFUtils.getNum4(mDataList.get(position).getCount()));
    }
}
