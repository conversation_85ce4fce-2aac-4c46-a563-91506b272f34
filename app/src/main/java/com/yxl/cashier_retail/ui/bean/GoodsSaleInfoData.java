package com.yxl.cashier_retail.ui.bean;

import java.util.List;

/**
 * Describe:商品销售统计详情（实体类）-写出花来
 * Created by jingang on 2024/8/24
 */
public class GoodsSaleInfoData {
    /**
     * status : 1
     * msg : Запрос удался！
     * data : [{"saleListUnique":17035718267810,"saleListDetailCount":1,"saleSum":10,"mlr_sum":-30,"purSum":40,"saleListDatetime":"2023-12-26 14:23:46","goodsName":"大白兔奶糖","goodsBarcode":"6665555","grossProfit":-0.75},{"saleListUnique":1724393115877882,"saleListDetailCount":1,"saleSum":5.3,"mlr_sum":-0.2,"purSum":5.5,"saleListDatetime":"2024-08-23 14:26:03","goodsName":"大白兔奶糖","goodsBarcode":"6665555","grossProfit":-0.0364},{"saleListUnique":1724393115877883,"saleListDetailCount":1,"saleSum":5.3,"mlr_sum":-0.2,"purSum":5.5,"saleListDatetime":"2024-08-23 14:55:48","goodsName":"大白兔奶糖","goodsBarcode":"6665555","grossProfit":-0.0364},{"saleListUnique":1724393115877884,"saleListDetailCount":1,"saleSum":5.3,"mlr_sum":-0.2,"purSum":5.5,"saleListDatetime":"2024-08-23 15:03:18","goodsName":"大白兔奶糖","goodsBarcode":"6665555","grossProfit":-0.0364},{"saleListUnique":1724393115877885,"saleListDetailCount":1,"saleSum":5.3,"mlr_sum":-0.2,"purSum":5.5,"saleListDatetime":"2024-08-23 15:05:04","goodsName":"大白兔奶糖","goodsBarcode":"6665555","grossProfit":-0.0364},{"saleListUnique":1724393115877887,"saleListDetailCount":1,"saleSum":5.3,"mlr_sum":-0.2,"purSum":5.5,"saleListDatetime":"2024-08-23 15:12:50","goodsName":"大白兔奶糖","goodsBarcode":"6665555","grossProfit":-0.0364},{"saleListUnique":1724397350069686,"saleListDetailCount":1,"saleSum":5,"mlr_sum":-0.5,"purSum":5.5,"saleListDatetime":"2024-08-23 15:16:04","goodsName":"大白兔奶糖","goodsBarcode":"6665555","grossProfit":-0.0909},{"saleListUnique":1724393115877886,"saleListDetailCount":1,"saleSum":5.3,"mlr_sum":-0.2,"purSum":5.5,"saleListDatetime":"2024-08-23 15:16:04","goodsName":"大白兔奶糖","goodsBarcode":"6665555","grossProfit":-0.0364}]
     * data1 : {"saleCount":20,"saleSum":110.4,"lirun_sum":-34.1,"purSum":144.5}
     * address : 3
     * cus_data : null
     */

    private int status;
    private String msg;
    private Data1Bean data1;
    private int address;
    private Object cus_data;
    private List<DataBean> data;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Data1Bean getData1() {
        return data1;
    }

    public void setData1(Data1Bean data1) {
        this.data1 = data1;
    }

    public int getAddress() {
        return address;
    }

    public void setAddress(int address) {
        this.address = address;
    }

    public Object getCus_data() {
        return cus_data;
    }

    public void setCus_data(Object cus_data) {
        this.cus_data = cus_data;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class Data1Bean {
        /**
         * saleCount : 20.0
         * saleSum : 110.4
         * lirun_sum : -34.1
         * purSum : 144.5
         */

        private double saleCount;//销售总数量
        private double saleSum;//销售总金额
        private double lirun_sum;//销售总利润
        private double purSum;//销售总成本

        public double getSaleCount() {
            return saleCount;
        }

        public void setSaleCount(double saleCount) {
            this.saleCount = saleCount;
        }

        public double getSaleSum() {
            return saleSum;
        }

        public void setSaleSum(double saleSum) {
            this.saleSum = saleSum;
        }

        public double getLirun_sum() {
            return lirun_sum;
        }

        public void setLirun_sum(double lirun_sum) {
            this.lirun_sum = lirun_sum;
        }

        public double getPurSum() {
            return purSum;
        }

        public void setPurSum(double purSum) {
            this.purSum = purSum;
        }
    }

    public static class DataBean {
        /**
         * saleListUnique : 17035718267810
         * saleListDetailCount : 1.0
         * saleSum : 10.0
         * mlr_sum : -30.0
         * purSum : 40.0
         * saleListDatetime : 2023-12-26 14:23:46
         * goodsName : 大白兔奶糖
         * goodsBarcode : 6665555
         * grossProfit : -0.75
         */

        private String saleListUnique;//订单编号
        private double saleListDetailCount;//销售数量
        private double saleSum;//销售总额
        private double mlr_sum;//利润
        private double purSum;//成本
        private String saleListDatetime;//下单时间
        private String goodsName;
        private String goodsBarcode;
        private double grossProfit;//利润率

        public String getSaleListUnique() {
            return saleListUnique;
        }

        public void setSaleListUnique(String saleListUnique) {
            this.saleListUnique = saleListUnique;
        }

        public double getSaleListDetailCount() {
            return saleListDetailCount;
        }

        public void setSaleListDetailCount(double saleListDetailCount) {
            this.saleListDetailCount = saleListDetailCount;
        }

        public double getSaleSum() {
            return saleSum;
        }

        public void setSaleSum(double saleSum) {
            this.saleSum = saleSum;
        }

        public double getMlr_sum() {
            return mlr_sum;
        }

        public void setMlr_sum(double mlr_sum) {
            this.mlr_sum = mlr_sum;
        }

        public double getPurSum() {
            return purSum;
        }

        public void setPurSum(double purSum) {
            this.purSum = purSum;
        }

        public String getSaleListDatetime() {
            return saleListDatetime;
        }

        public void setSaleListDatetime(String saleListDatetime) {
            this.saleListDatetime = saleListDatetime;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public String getGoodsBarcode() {
            return goodsBarcode;
        }

        public void setGoodsBarcode(String goodsBarcode) {
            this.goodsBarcode = goodsBarcode;
        }

        public double getGrossProfit() {
            return grossProfit;
        }

        public void setGrossProfit(double grossProfit) {
            this.grossProfit = grossProfit;
        }
    }
}
