package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivitySupplierInfoBinding;
import com.yxl.cashier_retail.ui.adapter.ImgAdapter;
import com.yxl.cashier_retail.ui.adapter.PurchaseGoodsAdapter;
import com.yxl.cashier_retail.ui.adapter.SupplierPurchaseAdapter;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.PurchaseInfoData;
import com.yxl.cashier_retail.ui.bean.PurchaseListData;
import com.yxl.cashier_retail.ui.bean.PurchaseVoucherData;
import com.yxl.cashier_retail.ui.bean.SupplierData;
import com.yxl.cashier_retail.ui.bean.SupplierInfosData;
import com.yxl.cashier_retail.ui.bean.SupplierPaymentInfoData;
import com.yxl.cashier_retail.ui.dialog.PurchaseGoodsCheckCancelDialog;
import com.yxl.cashier_retail.ui.dialog.PurchaseGoodsCheckDialog;
import com.yxl.cashier_retail.ui.dialog.PurchaseVoucherAddDialog;
import com.yxl.cashier_retail.ui.dialog.PurchaseVoucherDialog;
import com.yxl.cashier_retail.ui.dialog.SupplierPaymentDialog;
import com.yxl.cashier_retail.ui.fragment.SupplierGoodsFragment;
import com.yxl.cashier_retail.ui.fragment.SupplierPurchaseFragment;
import com.yxl.cashier_retail.ui.fragment.SupplierPaymentFragment;
import com.yxl.cashier_retail.ui.fragment.SupplierReplaceFragment;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.utils.PermissionUtils;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:供货商管理-详情
 * Created by jingang on 2024/6/8
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class SupplierInfoActivity extends BaseActivity<ActivitySupplierInfoBinding> implements View.OnClickListener {
    private Fragment[] fragments;
    private SupplierGoodsFragment goodsFragment;
    private SupplierPurchaseFragment orderFragment;
    private SupplierPaymentFragment paymentFragment;
    private SupplierReplaceFragment replaceFragment;
    private int index = 0,//点击的页卡索引
            currentTabIndex = 0;//当前的页卡索引

    private SupplierData data;
    private String supplierUnique;//供货商编号

    //购销单详情
    private String purchaseId;//购销单id
    private PurchaseGoodsAdapter purchaseGoodsAdapter;
    private List<PurchaseInfoData.GoodsListBean> purchaseGoodsList = new ArrayList<>();
    private ImgAdapter imgAdapter;
    private List<String> imgList = new ArrayList<>();

    //结款记录详情
    //还款凭证
    private ImgAdapter imgAdapter1;
    private List<String> imgList1 = new ArrayList<>();
    private SupplierPurchaseAdapter purchaseAdapter;
    private List<PurchaseListData> purchaseList = new ArrayList<>();

    @Override
    protected ActivitySupplierInfoBinding getViewBinding() {
        return ActivitySupplierInfoBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        supplierUnique = getIntent().getStringExtra("unique");
        //禁止手势滑动
        mBinding.drawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED);
        mBinding.ivBack.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.tvEdit.setOnClickListener(this);
        mBinding.tvDel.setOnClickListener(this);
        mBinding.tvSettlement.setOnClickListener(this);
        mBinding.lin0.setOnClickListener(this);
        mBinding.lin1.setOnClickListener(this);
        mBinding.lin2.setOnClickListener(this);
        mBinding.lin3.setOnClickListener(this);

        //购销单详情
        mBinding.ivBackPurchase.setOnClickListener(this);
        mBinding.tvPurchaseCopy.setOnClickListener(this);
        mBinding.tvPurchaseConfirm.setOnClickListener(this);
        mBinding.tvPurchasePayment.setOnClickListener(this);
        mBinding.tvPurchaseVoucher.setOnClickListener(this);

        //结款记录详情
        mBinding.ivBackSettlement.setOnClickListener(this);

        setFragment();
        setAdapter();
    }

    @Override
    protected void initData() {
        getSupplierInfo();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.tvEdit:
                //编辑
                startActivity(new Intent(this, SupplierEditActivity.class)
                        .putExtra("data", data)
                );
                break;
            case R.id.tvDel:
                //删除
                postSupplierDel(data.getId());
                break;
            case R.id.tvSettlement:
                //结款
                SupplierPaymentDialog.showDialog(this, supplierUnique, this::getSupplierInfo);
                break;
            case R.id.lin0:
                //所供商品
                index = 0;
                fragmentControl();
                break;
            case R.id.lin1:
                //购销订单
                index = 1;
                fragmentControl();
                break;
            case R.id.lin2:
                //结款记录
                index = 2;
                fragmentControl();
                break;
            case R.id.lin3:
                //替换供货商
                index = 3;
                fragmentControl();
                break;
            case R.id.ivBackPurchase:
                //购销单详情返回
            case R.id.ivBackSettlement:
                //结款订单详情返回
                mBinding.drawerLayout.closeDrawer(Gravity.RIGHT);
                break;
            case R.id.tvPurchaseCopy:
                //复制
                ClipboardManager cm = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
                // 创建普通字符型ClipData
                ClipData mClipData = ClipData.newPlainText("code", mBinding.tvPurchaseNo.getText().toString().trim());
                // 将ClipData内容放到系统剪贴板里。
                cm.setPrimaryClip(mClipData);
                showToast(1, getRstr(R.string.copy_to_clipboard));
                break;
            case R.id.tvPurchaseConfirm:
                //确认商品入库
                if (!isAll()) {
                    showToast(1, getRstr(R.string.goods_not_check));
                    return;
                }
                postGoodsCheckAll();
                break;
            case R.id.tvPurchasePayment:
                //还款（添加单据凭证）
                if (PermissionUtils.checkPermissionsGroup(this, 0)) {
                    showDialogVoucherAdd();
                } else {
                    PermissionUtils.requestPermissions(this, Constants.PERMISSION_CAMERA, 0);
                }
                break;
            case R.id.tvPurchaseVoucher:
                //查看单据凭证
                getPayment();
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.PURCHASE_INFO:
                //购销单详情
                purchaseId = event.getTypes();
                getPurchaseInfo();
                break;
            case Constants.SETTLEMENT_INFO:
                //结款记录详情
                getPaymentInfo(event.getTypes());
                break;
        }
    }

    /**
     * 设置fragment
     */
    public void setFragment() {
        goodsFragment = new SupplierGoodsFragment(supplierUnique);
        orderFragment = new SupplierPurchaseFragment(supplierUnique);
        paymentFragment = new SupplierPaymentFragment(supplierUnique);
        replaceFragment = new SupplierReplaceFragment();
        fragments = new Fragment[]{goodsFragment,
                orderFragment,
                paymentFragment,
                replaceFragment};
        setBottomColor();

        getSupportFragmentManager().beginTransaction().add(R.id.fragment_container, fragments[index]).show(fragments[index]).commit();
    }

    /**
     * 控制fragment的变化
     */
    public void fragmentControl() {
        if (currentTabIndex != index) {
            removeBottomColor();
            setBottomColor();
            FragmentTransaction trx = getSupportFragmentManager().beginTransaction();
            trx.hide(fragments[currentTabIndex]);
            if (!fragments[index].isAdded()) {
                trx.add(R.id.fragment_container, fragments[index]);
            }
            trx.show(fragments[index]).commitAllowingStateLoss();
            currentTabIndex = index;
        }
    }

    /**
     * 设置底部栏按钮变色
     */
    private void setBottomColor() {
        switch (index) {
            case 0:
                mBinding.lin0.setBackgroundResource(R.drawable.shape_green_top_5);
                mBinding.iv0.setImageResource(R.mipmap.ic_supplier_tab001);
                mBinding.tv0.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvCount0.setTextColor(getResources().getColor(R.color.white));
                mBinding.ivMore0.setImageResource(R.mipmap.ic_more002);
                break;
            case 1:
                mBinding.lin1.setBackgroundColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                mBinding.iv1.setImageResource(R.mipmap.ic_supplier_tab011);
                mBinding.tv1.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvCount1.setTextColor(getResources().getColor(R.color.white));
                mBinding.ivMore1.setImageResource(R.mipmap.ic_more002);
                break;
            case 2:
                mBinding.lin2.setBackgroundColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                mBinding.iv2.setImageResource(R.mipmap.ic_supplier_tab021);
                mBinding.tv2.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvCount2.setTextColor(getResources().getColor(R.color.white));
                mBinding.ivMore2.setImageResource(R.mipmap.ic_more002);
                break;
            case 3:
                mBinding.lin3.setBackgroundColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                mBinding.iv3.setImageResource(R.mipmap.ic_supplier_tab031);
                mBinding.tv3.setTextColor(getResources().getColor(R.color.white));
                mBinding.ivMore3.setImageResource(R.mipmap.ic_more002);
                break;
            default:
                break;
        }
    }

    /**
     * 清除底部栏颜色
     */
    private void removeBottomColor() {
        switch (currentTabIndex) {
            case 0:
                mBinding.lin0.setBackgroundResource(R.drawable.shape_white_top_5);
                mBinding.iv0.setImageResource(R.mipmap.ic_supplier_tab002);
                mBinding.tv0.setTextColor(getResources().getColor(R.color.black));
                mBinding.tvCount0.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                mBinding.ivMore0.setImageResource(R.mipmap.ic_more001);
                break;
            case 1:
                mBinding.lin1.setBackgroundColor(getResources().getColor(R.color.white));
                mBinding.iv1.setImageResource(R.mipmap.ic_supplier_tab012);
                mBinding.tv1.setTextColor(getResources().getColor(R.color.black));
                mBinding.tvCount1.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                mBinding.ivMore1.setImageResource(R.mipmap.ic_more001);
                break;
            case 2:
                mBinding.lin2.setBackgroundColor(getResources().getColor(R.color.white));
                mBinding.iv2.setImageResource(R.mipmap.ic_supplier_tab022);
                mBinding.tv2.setTextColor(getResources().getColor(R.color.black));
                mBinding.tvCount2.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                mBinding.ivMore2.setImageResource(R.mipmap.ic_more001);
                break;
            case 3:
                mBinding.lin3.setBackgroundColor(getResources().getColor(R.color.white));
                mBinding.iv3.setImageResource(R.mipmap.ic_supplier_tab032);
                mBinding.tv3.setTextColor(getResources().getColor(R.color.black));
                mBinding.ivMore3.setImageResource(R.mipmap.ic_more001);
                break;
            default:
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //购销单详情-价格明细
        purchaseGoodsAdapter = new PurchaseGoodsAdapter(this);
        mBinding.rvPurchaseGoods.setAdapter(purchaseGoodsAdapter);
        purchaseGoodsAdapter.setListener((view, position) -> {
            if (purchaseGoodsList.get(position).getGoodsStatus() == 0) {
                //核对
                PurchaseGoodsCheckDialog.showDialog(this,
                        purchaseGoodsList.get(position),
                        (count, inPrice, salePrice, webPrice, cusPrice) -> {
                            postGoodsCheck(purchaseGoodsList.get(position).getDetailId(), count, inPrice, salePrice, webPrice, cusPrice, position);
                        });
            } else {
                //撤销核对
                PurchaseGoodsCheckCancelDialog.showDialog(this, purchaseGoodsList.get(position), () -> {
                    postGoodsCheckCancel(purchaseGoodsList.get(position).getDetailId(), position);
                });
            }
        });

        //购销单详情-单据凭证
        imgAdapter = new ImgAdapter(this);
        mBinding.rvPurchaseVoucher.setAdapter(imgAdapter);
        imgAdapter.setOnItemClickListener((view, position) -> {
            //查看图片
            startActivity(new Intent(this, ImgBigActivity.class)
                    .putExtra("img", (Serializable) imgList)
                    .putExtra("index", position)
            );
        });

        //结款记录详情-付款凭证
        imgAdapter1 = new ImgAdapter(this);
        mBinding.rvSettlementImg.setAdapter(imgAdapter1);
        imgAdapter1.setOnItemClickListener((view, position) -> {
            //查看图片
            startActivity(new Intent(this, ImgBigActivity.class)
                    .putExtra("img", (Serializable) imgList1)
                    .putExtra("index", position)
            );
        });

        //结款记录详情-还款订单
        purchaseAdapter = new SupplierPurchaseAdapter(this);
        mBinding.rvSettlement.setAdapter(purchaseAdapter);
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(SupplierData data) {
        if (data == null) {
            return;
        }
        this.data = data;
        //1.购销 2.自采（本地）
        if (data.getPurchaseType() == 2) {
            mBinding.tvName.setText(data.getSupplierName() + getRstr(R.string.local));
            mBinding.ivSupplierImg.setVisibility(View.GONE);
        } else {
            mBinding.tvName.setText(data.getSupplierName());
            mBinding.ivSupplierImg.setVisibility(View.VISIBLE);
        }
        mBinding.tvMobile.setText(data.getContacts() + " " + data.getContactMobile());
        mBinding.tvCount.setText(String.valueOf(data.getOrderCount()));
        mBinding.tvDebt.setText(DFUtils.getNum2(data.getDebts()));
    }

    /**
     * 更新UI-购销单详情
     *
     * @param data
     */
    private void setUIPurchase(PurchaseInfoData data) {
        if (data == null) {
            return;
        }
        mBinding.linPurchase.setVisibility(View.VISIBLE);
        mBinding.linSettlement.setVisibility(View.GONE);
        mBinding.drawerLayout.openDrawer(mBinding.relSliding);
        //1-已提交(待收货)，2-已收货(待付款)，3-待确认，4-已完成，5-作废 6-异常
        switch (data.getStatus()) {
            case 1:
                mBinding.tvPurchaseStatus.setText(getRstr(R.string.purchase_order_status1));
                mBinding.tvPurchaseStatusTips.setText(getRstr(R.string.purchase_order_status1_tips));
                mBinding.ivPurchaseStatus.setImageResource(R.mipmap.ic_order_status001);
                mBinding.tvPurchaseConfirm.setVisibility(View.VISIBLE);
                mBinding.tvPurchasePayment.setVisibility(View.GONE);
                mBinding.tvPurchaseVoucher.setVisibility(View.GONE);
                break;
            case 2:
                mBinding.tvPurchaseStatus.setText(getRstr(R.string.purchase_order_status2));
                mBinding.tvPurchaseStatusTips.setText(getRstr(R.string.purchase_order_status2_tips));
                mBinding.ivPurchaseStatus.setImageResource(R.mipmap.ic_order_status001);
                mBinding.tvPurchaseConfirm.setVisibility(View.GONE);
                mBinding.tvPurchasePayment.setVisibility(View.VISIBLE);
                mBinding.tvPurchaseVoucher.setVisibility(View.GONE);
                break;
            case 3:
                mBinding.tvPurchaseStatus.setText(getRstr(R.string.purchase_order_status3));
                mBinding.tvPurchaseStatusTips.setText(getRstr(R.string.purchase_order_status3_tips));
                mBinding.ivPurchaseStatus.setImageResource(R.mipmap.ic_order_status001);
                mBinding.tvPurchaseConfirm.setVisibility(View.GONE);
                mBinding.tvPurchasePayment.setVisibility(View.GONE);
                mBinding.tvPurchaseVoucher.setVisibility(View.GONE);
                break;
            case 4:
                mBinding.tvPurchaseStatus.setText(getRstr(R.string.order_status6));
                mBinding.tvPurchaseStatusTips.setText(getRstr(R.string.order_status6));
                mBinding.ivPurchaseStatus.setImageResource(R.mipmap.ic_order_status001);
                mBinding.tvPurchaseConfirm.setVisibility(View.GONE);
                mBinding.tvPurchasePayment.setVisibility(View.GONE);
                mBinding.tvPurchaseVoucher.setVisibility(View.VISIBLE);
                break;
            case 5:
                mBinding.tvPurchaseStatus.setText(getRstr(R.string.order_status11));
                mBinding.tvPurchaseStatusTips.setText(getRstr(R.string.order_status11));
                mBinding.ivPurchaseStatus.setImageResource(R.mipmap.ic_order_status001);
                mBinding.tvPurchaseConfirm.setVisibility(View.GONE);
                mBinding.tvPurchasePayment.setVisibility(View.GONE);
                mBinding.tvPurchaseVoucher.setVisibility(View.GONE);
                break;
        }

        //备注
        mBinding.tvPurchaseRemarks.setText(TextUtils.isEmpty(data.getRemark()) ? "-" : data.getRemark());

        //价格明细
        if (data.getGoodsList() == null) {
            mBinding.linPurchaseGoods.setVisibility(View.GONE);
        } else {
            if (data.getGoodsList().size() > 0) {
                mBinding.linPurchaseGoods.setVisibility(View.VISIBLE);
                purchaseGoodsList.clear();
                purchaseGoodsList.addAll(data.getGoodsList());
                purchaseGoodsAdapter.setStatus(data.getStatus());
                purchaseGoodsAdapter.setDataList(purchaseGoodsList);
            } else {
                mBinding.linPurchaseGoods.setVisibility(View.GONE);
            }
        }

        //批次信息
        if (data.getBatchInfo() != null) {
            mBinding.linPurchaseBatch.setVisibility(View.VISIBLE);
            mBinding.tvPurchaseCount.setText(data.getBatchInfo().getGoodsCategory() + getRstr(R.string.classX));
            mBinding.tvPurchasePrice.setText(DFUtils.getNum2(data.getBatchInfo().getPurchaseCost()));
            mBinding.tvPurchasePayable.setText(DFUtils.getNum2(data.getBatchInfo().getAmountPayable()));
            mBinding.tvPurchaseDiscount.setText(DFUtils.getNum2(data.getBatchInfo().getSettlePref()));
            mBinding.tvPurchaseSettle.setText(DFUtils.getNum2(data.getBatchInfo().getSettledAmount()));
            mBinding.tvPurchasePending.setText(DFUtils.getNum2(data.getBatchInfo().getOutstandingAmount()));
        } else {
            mBinding.linPurchaseBatch.setVisibility(View.GONE);
        }

        //订单信息
        if (data.getOrderInfo() != null) {
            mBinding.linPurchaseOrder.setVisibility(View.VISIBLE);
            mBinding.tvPurchaseNo.setText(TextUtils.isEmpty(data.getOrderInfo().getBillNo()) ? "-" : data.getOrderInfo().getBillNo());
            mBinding.tvPurchaseTime.setText(TextUtils.isEmpty(data.getOrderInfo().getCreateTime()) ? "-" : data.getOrderInfo().getCreateTime());
            mBinding.tvPurchaseSupplier.setText(TextUtils.isEmpty(data.getOrderInfo().getSupplierName()) ? "-" : data.getOrderInfo().getSupplierName());
            mBinding.tvPurchaseContact.setText(TextUtils.isEmpty(data.getOrderInfo().getContacts()) ? "-" : data.getOrderInfo().getContacts());
            mBinding.tvPurchaseMobile.setText(TextUtils.isEmpty(data.getOrderInfo().getSupplierPhone()) ? "-" : data.getOrderInfo().getSupplierPhone());
            mBinding.tvPurchaseAddress.setText(TextUtils.isEmpty(data.getOrderInfo().getSupplierAddress()) ? "-" : data.getOrderInfo().getSupplierAddress());
        } else {
            mBinding.linPurchaseOrder.setVisibility(View.GONE);
        }

        //单据凭证
        if (data.getVoucherPicturepath() == null) {
            mBinding.linPurchaseVoucher.setVisibility(View.GONE);
        } else {
            if (data.getVoucherPicturepath().size() > 0) {
                mBinding.linPurchaseVoucher.setVisibility(View.VISIBLE);
                imgList.clear();
                imgList.addAll(data.getVoucherPicturepath());
                imgAdapter.setDataList(imgList);
            } else {
                mBinding.linPurchaseVoucher.setVisibility(View.GONE);
            }
        }
    }

    /**
     * 判断是否全部
     */
    private boolean isAll() {
        boolean isAll = false;
        for (int i = 0; i < purchaseGoodsList.size(); i++) {
            if (purchaseGoodsList.get(i).getGoodsStatus() == 0) {
                isAll = false;
                break;//根据标记，跳出嵌套循环
            } else {
                isAll = true;
            }
        }
        return isAll;
    }

    /**
     * 更新UI-结款记录详情
     *
     * @param data
     */
    private void setUIPayment(SupplierPaymentInfoData data) {
        if (data == null) {
            return;
        }
        mBinding.linPurchase.setVisibility(View.GONE);
        mBinding.linSettlement.setVisibility(View.VISIBLE);
        mBinding.drawerLayout.openDrawer(mBinding.relSliding);
        mBinding.tvSettlementName.setText(data.getCreateBy());
        mBinding.tvSettlementTotal.setText(DFUtils.getNum2(data.getPaymentMoney()));
        mBinding.tvSettlementRemarks.setText(TextUtils.isEmpty(data.getRemark()) ? "-" : data.getRemark());
        //付款凭证
        imgList1.clear();
        if (data.getVoucherPicturePath() != null) {
            imgList1.addAll(data.getVoucherPicturePath());
        }
        if (imgList1.size() > 0) {
            mBinding.rvSettlementImg.setVisibility(View.VISIBLE);
            imgAdapter1.setDataList(imgList1);
        } else {
            mBinding.rvSettlementImg.setVisibility(View.GONE);
        }

        //还款订单
        purchaseList.clear();
        if (data.getBillList() != null) {
            purchaseList.addAll(data.getBillList());
        }
        purchaseAdapter.setDataList(purchaseList);
    }

    /**
     * dialog（添加单据凭证）
     */
    private void showDialogVoucherAdd() {
        PurchaseVoucherAddDialog.showDialog(this, 0, this::postPaymentAdd);
    }

    /**
     * 供货商详情
     */
    private void getSupplierInfo() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("supplierUnique", supplierUnique);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getSupplierInfo(),
                map,
                SupplierData.class,
                new RequestListener<SupplierData>() {
                    @Override
                    public void success(SupplierData data) {
                        setUI(data);
                        getInfo();
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 业务信息
     */
    private void getInfo() {
        if (TextUtils.isEmpty(supplierUnique)) {
            return;
        }
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("supplierUnique", supplierUnique);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getSupplierInfos(),
                map,
                SupplierInfosData.class,
                new RequestListener<SupplierInfosData>() {
                    @Override
                    public void success(SupplierInfosData data) {
                        hideDialog();
                        mBinding.tvTotal.setText(DFUtils.getNum2(data.getPurchaseAmount()));
                        mBinding.tvPayment.setText(DFUtils.getNum2(data.getSettledAmount()));
                        mBinding.tvCount0.setText(data.getGoodsTypeCount() + getRstr(R.string.classX));
                        mBinding.tvCount1.setText(String.valueOf(data.getBillCount()));
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 供货商删除
     *
     * @param id
     */
    private void postSupplierDel(int id) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("createId", getStaffUnique());
        map.put("createBy", getStaffName());
        map.put("id", id);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getSupplierDel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.SUPPLIER_LIST));
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 购销单详情
     */
    private void getPurchaseInfo() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("id", purchaseId);
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getGouXOrderInfo(),
                map,
                PurchaseInfoData.class,
                new RequestListener<PurchaseInfoData>() {
                    @Override
                    public void success(PurchaseInfoData data) {
                        hideDialog();
                        setUIPurchase(data);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 入库单个商品
     *
     * @param count     实际入库数量
     * @param inPrice   进价
     * @param salePrice 售价
     * @param webPrice  网单价
     * @param cusPrice  会员价
     */
    private void postGoodsCheck(int detailId, double count, double inPrice, double salePrice, double webPrice, double cusPrice, int position) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("id", purchaseId);
        map.put("detailId", detailId);
        map.put("createId", getStaffUnique());
        map.put("createBy", getStaffName());
        map.put("supplierUnique", supplierUnique);
        map.put("goodsActualCount", count);
        map.put("goodsInPrice", inPrice);
        map.put("goodsSalePrice", salePrice);
        map.put("goodsWebSalePrice", webPrice);
        map.put("goodsCusPrice", cusPrice);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getGouXGoodsCheck(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        purchaseGoodsList.get(position).setGoodsStatus(1);
                        purchaseGoodsAdapter.notifyItemChanged(position, purchaseGoodsList.get(position));
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 撤销核对入库
     *
     * @param detailId
     * @param position
     */
    private void postGoodsCheckCancel(int detailId, int position) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("supplierUnique", supplierUnique);
        map.put("detailId", detailId);
        map.put("createId", getStaffUnique());
        map.put("createBy", getStaffName());
        map.put("id", purchaseId);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getGouXGoodsCheckCancel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        purchaseGoodsList.get(position).setGoodsStatus(0);
                        purchaseGoodsAdapter.notifyItemChanged(position, purchaseGoodsList.get(position));
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 入库全部商品
     */
    private void postGoodsCheckAll() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("createId", getStaffUnique());
        map.put("createBy", getStaffName());
        map.put("id", purchaseId);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getGouXGoodsCheckAll(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        getPurchaseInfo();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 添加单据凭证
     *
     * @param money
     * @param remarks
     * @param array
     */
    private void postPaymentAdd(double money, String remarks, List array) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("createId", getStaffUnique());
        map.put("createBy", getStaffName());
        map.put("billId", purchaseId);
        map.put("supplierUnique", supplierUnique);
        map.put("paymentMoney", money);
        map.put("remark", remarks);
        map.put("voucherPicturepath", array);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getGouXPaymentAdd(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        getPurchaseInfo();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 查看单据凭证
     */
    private void getPayment() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("id", purchaseId);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getGouXPayment(),
                map,
                PurchaseVoucherData.class,
                new RequestListener<PurchaseVoucherData>() {
                    @Override
                    public void success(PurchaseVoucherData data) {
                        PurchaseVoucherDialog.showDialog(SupplierInfoActivity.this, data);
                    }
                });
    }

    /**
     * 付款详情
     */
    private void getPaymentInfo(String id) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("supplierUnique", supplierUnique);
        map.put("paymentId", id);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getSupplierInfoPaymentInfo(),
                map,
                SupplierPaymentInfoData.class,
                new RequestListener<SupplierPaymentInfoData>() {
                    @Override
                    public void success(SupplierPaymentInfoData data) {
                        hideDialog();
                        setUIPayment(data);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });

    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION_CAMERA:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showToast(1, getRstr(R.string.no_permission_tips));
                } else {
                    showDialogVoucherAdd();
                }
                break;
        }
    }
}
