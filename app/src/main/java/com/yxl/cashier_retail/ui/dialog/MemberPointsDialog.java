package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogMemberPointsBinding;
import com.yxl.cashier_retail.view.NumberKeyBoardView;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Describe:dialog（会员-增加、减少积分）
 * Created by jingang on 2024/5/11
 */
@SuppressLint("NonConstantResourceId")
public class MemberPointsDialog extends BaseDialog<DialogMemberPointsBinding> implements View.OnClickListener {
    private static int type,//1.添加 2.减少 0.兑换
            cusId;//会员id
    private static double points;//积分

    public static void showDialog(Activity activity, int type, int cusId,double points, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        MemberPointsDialog.listener = listener;
        MemberPointsDialog.type = type;
        MemberPointsDialog.cusId = cusId;
        MemberPointsDialog.points = points;
        MemberPointsDialog dialog = new MemberPointsDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public MemberPointsDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        switch (type) {
            case 1:
                mBinding.tvDialogTitle.setText(getRstr(R.string.points_add));
                break;
            case 2:
                mBinding.tvDialogTitle.setText(getRstr(R.string.points_sub));
                break;
            default:
                mBinding.tvDialogTitle.setText(getRstr(R.string.exchange_points));
                break;
        }
        ((AnimationDrawable) mBinding.ivDialogCursor.getDrawable()).start();
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.numberKeyBoardView.setMaxLength(Constants.MAX_MONEY_LENGTH);
        mBinding.numberKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var) {
                mBinding.tvDialogPoints.setText(var);
                if (TextUtils.isEmpty(var)) {
                    mBinding.tvDialogPoints.setVisibility(View.GONE);
                    mBinding.tvDialogHints.setVisibility(View.VISIBLE);
                    points = 0;
                } else {
                    mBinding.tvDialogPoints.setVisibility(View.VISIBLE);
                    mBinding.tvDialogHints.setVisibility(View.GONE);
                    points = Double.parseDouble(var);
                }
            }

            @Override
            public void onConfirm() {
                if (points <= 0) {
                    showToast(1, getRstr(R.string.input_points_count));
                    return;
                }
                postMemberPointsEdit();
            }
        });
    }

    @Override
    protected DialogMemberPointsBinding getViewBinding() {
        return DialogMemberPointsBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
        }
    }

    /**
     * 会员积分变动
     */
    private void postMemberPointsEdit() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("cusId", cusId);
        if (type == 1) {
            params.put("operationType", 1);//1.添加 2.减少或兑换
        } else {
            params.put("operationType", 2);
        }
        params.put("cusPoints", points);
        params.put("remark", "");//变动理由
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getMemberPointsEdit(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        if (listener != null) {
                            if (type == 1) {
                                listener.onConfirm(points);
                            } else {
                                listener.onConfirm(-points);
                            }
                            dismiss();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(double points);
    }
}
