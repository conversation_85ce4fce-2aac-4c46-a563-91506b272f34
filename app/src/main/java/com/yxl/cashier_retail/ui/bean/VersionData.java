package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;

/**
 * Describe:版本更新（实体类）
 * Created by jingang on 2024/5/10
 */
public class VersionData implements Serializable {
    private String msg;
    private int status;
    private DataBean data;

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public static class DataBean {
        private int update_install;//是否强制更新 1.强制
        private String update_url;//下载地址
        private String update_version;//版本名称
        private int update_id;//版本id
        private int code;//版本号

        private String update_des;//升级描述
        private String update_log;//升级日志

        public int getUpdate_install() {
            return update_install;
        }

        public void setUpdate_install(int update_install) {
            this.update_install = update_install;
        }

        public String getUpdate_url() {
            return update_url;
        }

        public void setUpdate_url(String update_url) {
            this.update_url = update_url;
        }

        public String getUpdate_version() {
            return update_version;
        }

        public void setUpdate_version(String update_version) {
            this.update_version = update_version;
        }

        public int getUpdate_id() {
            return update_id;
        }

        public void setUpdate_id(int update_id) {
            this.update_id = update_id;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getUpdate_des() {
            return update_des;
        }

        public void setUpdate_des(String update_des) {
            this.update_des = update_des;
        }

        public String getUpdate_log() {
            return update_log;
        }

        public void setUpdate_log(String update_log) {
            this.update_log = update_log;
        }
    }
}
