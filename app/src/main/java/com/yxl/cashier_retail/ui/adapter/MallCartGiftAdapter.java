package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;

/**
 * Describe:商城-购物车-满赠（适配器）
 * Created by jingang on 2024/6/4
 */
public class MallCartGiftAdapter extends BaseAdapter<String> {

    public MallCartGiftAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_mall_cart_gift;
    }

    @Override
    public int getItemCount() {
        return 10;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvName, tvPrice, tvCount;
        tvName = holder.getView(R.id.tvItemName);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvCount = holder.getView(R.id.tvItemCount);
    }
}
