package com.yxl.cashier_retail.ui.fragment;

import android.view.View;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmSupplierReplaceBinding;
import com.yxl.cashier_retail.ui.adapter.SupplierCateHorAdapter;
import com.yxl.cashier_retail.ui.adapter.SupplierReplaceAdapter;
import com.yxl.cashier_retail.ui.bean.SupplierCateData;
import com.yxl.cashier_retail.ui.bean.SupplierData;
import com.yxl.cashier_retail.ui.bean.SupplierListData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:供货商管理-详情-替换供货商
 * Created by jingang on 2024/6/12
 */
public class SupplierReplaceFragment extends BaseFragment<FmSupplierReplaceBinding> {

    private String cateUnique;

    //供货商分类列表
    private SupplierCateHorAdapter cateAdapter;
    private List<SupplierCateData> cateList = new ArrayList<>();

    //供货商列表
    private SupplierReplaceAdapter mAdapter;
    private List<SupplierData> dataList = new ArrayList<>();

    @Override
    protected FmSupplierReplaceBinding getViewBinding() {
        return FmSupplierReplaceBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        setAdapter();
    }

    @Override
    protected void initData() {
        getCateList();
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //分类
        cateAdapter = new SupplierCateHorAdapter(getActivity());
        mBinding.rvCate.setAdapter(cateAdapter);
        cateAdapter.setOnItemClickListener((view, position) -> {
            if (!cateList.get(position).isSelect()) {
                for (int i = 0; i < cateList.size(); i++) {
                    if (cateList.get(i).isSelect()) {
                        cateList.get(i).setSelect(false);
                        cateAdapter.notifyItemChanged(i);
                    }
                }
                cateList.get(position).setSelect(true);
                cateAdapter.notifyItemChanged(position);
                cateUnique = cateList.get(position).getSupplierKindUnique();
                page = 1;
                getSupplierList();
            }
        });

        //供货商列表
        mAdapter = new SupplierReplaceAdapter(getActivity());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {

        });
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getSupplierList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getSupplierList();
            }
        });
    }


    /**
     * 分类列表
     */
    private void getCateList() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        RXHttpUtil.requestByBodyPostAsResponseList(this,
                ZURL.getSupplierCateList(),
                map,
                SupplierCateData.class,
                new RequestListListener<SupplierCateData>() {
                    @Override
                    public void onResult(List<SupplierCateData> list) {
                        hideDialog();
                        cateList.clear();
                        cateList.add(new SupplierCateData(true, "", getRstr(R.string.all)));
                        cateList.addAll(list);
                        cateAdapter.setDataList(cateList);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 供货商列表
     */
    private void getSupplierList() {
        showDialog();
        hideSoftInput(getActivity());
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("pageIndex", page);
        map.put("pageSize", Constants.limit);
        RXHttpUtil.requestByBodyPostAsResponse(getActivity(),
                ZURL.getSupplierList(),
                map,
                SupplierListData.class,
                new RequestListener<SupplierListData>() {
                    @Override
                    public void success(SupplierListData data) {
                        hideDialog();
                        if (page == 1) {
                            mBinding.smartRefreshLayout.finishRefresh();
                            dataList.clear();
                        } else {
                            mBinding.smartRefreshLayout.finishLoadMore();
                        }
                        dataList.addAll(data.getList());
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.smartRefreshLayout.finishRefresh();
                        mBinding.smartRefreshLayout.finishLoadMore();
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }
}
