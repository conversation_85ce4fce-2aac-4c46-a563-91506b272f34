package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogSupplierPaymentBinding;
import com.yxl.cashier_retail.ui.adapter.SupplierGouXDialogAdapter;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.PurchaseListData;
import com.yxl.cashier_retail.ui.bean.SupplierPurchaseData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.EventBusManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:dialog（供货商管理-结款）
 * Created by jingang on 2024/6/12
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class SupplierPaymentDialog extends BaseDialog<DialogSupplierPaymentBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private static String supplierUnique;
    private int count;
    private double total;
    private List array;
    private boolean isAll;

    private SupplierGouXDialogAdapter mAdapter;
    private List<PurchaseListData> dataList = new ArrayList<>();

    public static void showDialog(Activity activity, String supplierUnique, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        SupplierPaymentDialog.listener = listener;
        SupplierPaymentDialog.supplierUnique = supplierUnique;
        SupplierPaymentDialog dialog = new SupplierPaymentDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 9);
        dialog.show();
    }

    public SupplierPaymentDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.ivDialogAll.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        setAdapter();
        getOrderList();
    }

    @Override
    protected DialogSupplierPaymentBinding getViewBinding() {
        return DialogSupplierPaymentBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.ivDialogAll:
                //全选
                isAll = !isAll;
                for (int i = 0; i < dataList.size(); i++) {
                    if (dataList.get(i).isSelect() != isAll) {
                        dataList.get(i).setSelect(isAll);
                        mAdapter.notifyItemChanged(i, dataList.get(i));
                    }
                }
                isAll();
                break;
            case R.id.tvDialogConfirm:
                //确认
                if (count < 1) {
                    showToast(1, getRstr(R.string.select_order));
                    return;
                }
                PurchaseVoucherAddDialog.showDialog(mActivity, total, this::postOrderRepayment);
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new SupplierGouXDialogAdapter(getContext());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            dataList.get(position).setSelect(!dataList.get(position).isSelect());
            mAdapter.notifyItemChanged(position);
            isAll();
        });
        mBinding.smartRefreshLayout.setOnRefreshListener(refreshLayout -> getOrderList());
        mBinding.smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(SupplierPurchaseData data) {
        if (data == null) {
            return;
        }
        mBinding.tvDialogCount.setText(String.valueOf(data.getOutstandingCount()));
        mBinding.tvDialogTotal.setText(DFUtils.getNum2(data.getPurchaseAmounts()));
        dataList.clear();
        if (data.getBillList() != null) {
            dataList.addAll(data.getBillList());
        }
        if (dataList.size() > 0) {
            mBinding.recyclerView.setVisibility(View.VISIBLE);
            mBinding.linEmpty.setVisibility(View.GONE);
            mAdapter.setDataList(dataList);
            isAll();
        } else {
            mBinding.recyclerView.setVisibility(View.GONE);
            mBinding.linEmpty.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 是否全选
     */
    private void isAll() {
        for (int i = 0; i < dataList.size(); i++) {
            boolean isSelect = dataList.get(i).isSelect();
            if (!isSelect) {
                isAll = false;
                break;//根据标记，跳出嵌套循环
            } else {
                isAll = true;
            }
        }
        mBinding.ivDialogAll.setSelected(isAll);
        count = 0;
        total = 0;
        array = new ArrayList();
        for (int i = 0; i < dataList.size(); i++) {
            if (dataList.get(i).isSelect()) {
                count++;
                total = total + dataList.get(i).getTotalPrice();
                try {
                    array.add(dataList.get(i).getId());
                } catch (Exception ignored) {
                }
            }
        }
        if (count > 0) {
            mBinding.linDialogTotal.setVisibility(View.VISIBLE);
            mBinding.tvDialogCount1.setText(String.valueOf(count));
            mBinding.tvDialogTotal1.setText(DFUtils.getNum2(total));
        } else {
            mBinding.linDialogTotal.setVisibility(View.GONE);
        }
    }

    /**
     * 未还款购销单列表
     */
    private void getOrderList() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("supplierUnique", supplierUnique);
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getSupplierInfoUnpaidGouXList(),
                map,
                SupplierPurchaseData.class,
                new RequestListener<SupplierPurchaseData>() {
                    @Override
                    public void success(SupplierPurchaseData data) {
                        hideDialog();
                        mBinding.smartRefreshLayout.finishRefresh();
                        setUI(data);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 还款
     *
     * @param money
     * @param remarks
     * @param arrayImg
     */
    private void postOrderRepayment(double money, String remarks, List arrayImg) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("createId", getStaffUnique());
        map.put("createBy", getStaffName());
        map.put("supplierUnique", supplierUnique);
        map.put("billIdList", array);
        map.put("paymentMoney", money);
        map.put("remark", remarks);
        map.put("voucherPicturepath", arrayImg);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getSupplierInfoRepayment(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.SUPPLIER_LIST));
                        if (listener != null) {
                            listener.onConfirm();
                            dismiss();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm();
    }
}
