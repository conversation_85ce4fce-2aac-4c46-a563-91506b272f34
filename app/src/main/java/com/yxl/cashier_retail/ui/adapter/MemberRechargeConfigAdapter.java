package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.MemberRechargeConfigData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe: 会员充值配置（适配器）
 * Created by jingang on 2025/6/6
 */
public class MemberRechargeConfigAdapter extends BaseAdapter<MemberRechargeConfigData> {
    public MemberRechargeConfigAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_member_recharge_config;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvMoney, tvGift;
        tvMoney = holder.getView(R.id.tvItemMoney);
        tvGift = holder.getView(R.id.tvItemGift);

        tvMoney.setText(DFUtils.getNum2(mDataList.get(position).getRecharge_money()));
        tvGift.setText(DFUtils.getNum2(mDataList.get(position).getGive_money()));
    }
}
