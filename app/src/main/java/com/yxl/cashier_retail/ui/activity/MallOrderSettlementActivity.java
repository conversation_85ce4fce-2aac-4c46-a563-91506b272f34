package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.view.View;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityMallOrderSettlementBinding;
import com.yxl.cashier_retail.ui.adapter.MallOrderSettlementAdapter;
import com.yxl.commonlibrary.AppManager;

/**
 * Describe:商城-提交订单
 * Created by jingang on 2024/6/4
 */
@SuppressLint("NonConstantResourceId")
public class MallOrderSettlementActivity extends BaseActivity<ActivityMallOrderSettlementBinding> implements View.OnClickListener {

    private MallOrderSettlementAdapter mAdapter;

    @Override
    protected ActivityMallOrderSettlementBinding getViewBinding() {
        return ActivityMallOrderSettlementBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivBack.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.relAds.setOnClickListener(this);
        mBinding.tvConfirm.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected void initData() {

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.relAds:
                //收货地址
                break;
            case R.id.tvConfirm:
                //提交订单
                goToActivity(MallPaymentActivity.class);
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new MallOrderSettlementAdapter(this);
        mBinding.recyclerView.setAdapter(mAdapter);
        mBinding.smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            mBinding.smartRefreshLayout.finishRefresh();
        });
        mBinding.smartRefreshLayout.setEnableLoadMore(false);
    }

}
