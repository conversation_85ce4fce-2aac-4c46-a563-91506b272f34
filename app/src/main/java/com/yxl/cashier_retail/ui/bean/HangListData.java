package com.yxl.cashier_retail.ui.bean;

import org.threeten.bp.YearMonth;

import java.util.List;

/**
 * Describe:挂单列表-以月筛选（实体类）
 * Created by jingang on 2025/1/20
 */
public class HangListData {
    private YearMonth month;
    private List<HangData> data;

    public HangListData(YearMonth month, List<HangData> data) {
        this.month = month;
        this.data = data;
    }

    public YearMonth getMonth() {
        return month;
    }

    public void setMonth(YearMonth month) {
        this.month = month;
    }

    public List<HangData> getData() {
        return data;
    }

    public void setData(List<HangData> data) {
        this.data = data;
    }
}
