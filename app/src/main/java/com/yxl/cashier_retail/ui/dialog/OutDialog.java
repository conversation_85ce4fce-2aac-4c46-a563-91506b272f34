package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;

import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogOutBinding;
import com.yxl.cashier_retail.ui.activity.ImgBigActivity;
import com.yxl.cashier_retail.ui.adapter.GoodsStockBatchAdapter;
import com.yxl.cashier_retail.ui.adapter.GoodsStockReasonAdapter;
import com.yxl.cashier_retail.ui.adapter.ImgSelectAdapter;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.GoodsBatchData;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.ui.bean.GoodsListData;
import com.yxl.cashier_retail.ui.bean.GoodsStockReasonData;
import com.yxl.cashier_retail.ui.bean.UrlData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.utils.DecimalDigitsInputFilter;
import com.yxl.cashier_retail.utils.picture.GlideEngine;
import com.yxl.cashier_retail.utils.picture.ImageFileCompressEngine;
import com.yxl.cashier_retail.utils.picture.ImageFileCropEngine;
import com.yxl.cashier_retail.utils.picture.MeSandboxFileEngine;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.litepal.LitePal;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:dialog（商品出库）
 * Created by jingang on 2024/7/2
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class OutDialog extends BaseDialog<DialogOutBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private static GoodsListData data;
    private static String barcode,
            reason,
            remarks,//备注
            img;//图片
    private int count;//数量
    private double price,//单价
            total,//总价
            stock;//库存

    //原因
    private List<GoodsStockReasonData.ListBean> reasonList = new ArrayList<>();
    private GoodsStockReasonAdapter reasonAdapter;

    //批次
    private GoodsStockBatchAdapter batchAdapter;
    private List<GoodsBatchData> batchList = new ArrayList<>();

    //图片
    private ImgSelectAdapter imgAdapter;
    private List<String> imgList = new ArrayList<>();

    public static void showDialog(Activity activity, GoodsListData data, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        OutDialog.mActivity = activity;
        OutDialog.listener = listener;
        OutDialog.data = data;
        OutDialog dialog = new OutDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public OutDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(false);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.ivDialogRemarksClear.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        mBinding.etDialogCount.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        mBinding.etDialogPrice.setFilters(new InputFilter[]{new DecimalDigitsInputFilter(2)});
        mBinding.etDialogCount.addTextChangedListener(textWatcher);
        mBinding.etDialogPrice.addTextChangedListener(textWatcher);
        mBinding.etDialogRemarks.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                remarks = s.toString().trim();
                if (TextUtils.isEmpty(remarks)) {
                    mBinding.ivDialogRemarksClear.setVisibility(View.GONE);
                } else {
                    mBinding.ivDialogRemarksClear.setVisibility(View.VISIBLE);
                }
            }
        });
        setAdapter();
        setUI();
    }

    @Override
    protected DialogOutBinding getViewBinding() {
        return DialogOutBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.ivDialogRemarksClear:
                //清除备注输入
                mBinding.etDialogRemarks.setText("");
                break;
            case R.id.tvDialogConfirm:
                //确认出库
                if (TextUtils.isEmpty(reason)) {
                    showToast(1, getRstr(R.string.select_out_reason));
                    return;
                }
                if (count <= 0) {
                    showToast(1, getRstr(R.string.input_out_count));
                    return;
                }
//                    if (outCount > kucun) {
//                        showToast(1,"出库数量不可大于库存数量");
//                        return;
//                    }
                if (price <= 0) {
                    showToast(1, getRstr(R.string.input_out_price));
                    return;
                }
                double countTotal = 0;
                for (int i = 0; i < batchList.size(); i++) {
                    countTotal = countTotal + batchList.get(i).getCount();
                }
                if (countTotal > count) {
                    showToast(1, getRstr(R.string.out_count_total_no_than_count));
                    return;
                }
                img = "";
                for (int i = 0; i < imgList.size(); i++) {
                    if (TextUtils.isEmpty(img)) {
                        img = imgList.get(i);
                    } else {
                        img = img + "," + imgList.get(i);
                    }
                }
                setSum_num();
                break;
        }
    }

    /**
     * 更新UI
     */
    private void setUI() {
        if (data == null) {
            return;
        }
        barcode = data.getGoodsBarcode();
        mBinding.etDialogPrice.setText(data.getStockPrice() == 0 ? "" : DFUtils.getNum4(data.getStockPrice()));//出库单价
        getReason();
        getBatchList();
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //入库原因
        reasonAdapter = new GoodsStockReasonAdapter(getContext());
        mBinding.rvDialogReason.setAdapter(reasonAdapter);
        reasonAdapter.setOnItemClickListener((view, position) -> {
            if (!reasonList.get(position).isSelect()) {
                for (int i = 0; i < reasonList.size(); i++) {
                    if (reasonList.get(i).isSelect()) {
                        reasonList.get(i).setSelect(false);
                        reasonAdapter.notifyItemChanged(i);
                    }
                }
                reasonList.get(position).setSelect(true);
                reasonAdapter.notifyItemChanged(position);
                reason = reasonList.get(position).getDictLabel();
            }
        });

        //批次
        batchAdapter = new GoodsStockBatchAdapter(getContext());
        mBinding.rvDialogBatch.setAdapter(batchAdapter);
        batchAdapter.setListener((count, position) -> {
            batchList.get(position).setCount(count);
        });

        //图片
        imgAdapter = new ImgSelectAdapter(getContext());
        mBinding.rvDialogImg.setAdapter(imgAdapter);
        imgAdapter.setSize(3);
        imgAdapter.setListener(new ImgSelectAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                if (isQuicklyClick()) {
                    return;
                }
                if (position == imgAdapter.getDataList().size()) {
                    //选择图片
                    showDialogCamera();
                } else {
                    //查看图片
                    getContext().startActivity(new Intent(getContext(), ImgBigActivity.class)
                            .putExtra("img", (Serializable) imgList)
                            .putExtra("index", position)
                    );
                }
            }

            @Override
            public void onDelClick(View view, int position) {
                //删除
                if (isQuicklyClick()) {
                    return;
                }
                IAlertDialog.showDialog(getContext(),
                        getRstr(R.string.confirm_del_img),
                        getRstr(R.string.confirm),
                        (dialog, which) -> {
                            imgList.remove(position);
                            imgAdapter.setDataList(imgList);
                            mBinding.tvDialogImgCount.setText("(" + imgList.size() + "/3)");
                        });
            }
        });
    }

    TextWatcher textWatcher = new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {

        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {

        }

        @Override
        public void afterTextChanged(Editable s) {
            getInTotal();
        }
    };

    /**
     * 计算总价(入库)
     */
    private void getInTotal() {
        String countStr = mBinding.etDialogCount.getText().toString().trim(),
                priceStr = mBinding.etDialogPrice.getText().toString().trim();
        count = TextUtils.isEmpty(countStr) ? 0 : Integer.parseInt(countStr);
        price = TextUtils.isEmpty(priceStr) ? 0 : Double.parseDouble(priceStr);
        total = count * price;
        if (total > 0) {
            mBinding.tvDialogTotal.setText(DFUtils.getNum2(total));
        } else {
            mBinding.tvDialogTotal.setText(getRstr(R.string.no_count));
        }
    }

    /**********选择图片start*********/
    /**
     * 选择图片方式弹窗
     */
    private void showDialogCamera() {
        CameraDialog.showDialog(mActivity, type -> {
            //type: 0.拍照 1.从相册选择
            if (type == 1) {
                pickPhoto();
            } else {
                takePhoto();
            }
        });
    }

    //拍照获取图片
    private void takePhoto() {
        PictureSelector.create(mActivity)
                .openCamera(SelectMimeType.ofImage())
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())
                .setCropEngine(new ImageFileCropEngine(mActivity))
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        mediaList.clear();
                        mediaList.addAll(result);
                        i = 0;
                        postFile();
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    //从相册中取图片
    private void pickPhoto() {
        PictureSelector.create(mActivity)
                .openGallery(SelectMimeType.ofImage())
                .setImageEngine(GlideEngine.createGlideEngine())
                .setMaxSelectNum(3 - imgList.size())
                .setCropEngine(new ImageFileCropEngine(mActivity))
                .setCompressEngine(new ImageFileCompressEngine())
                .setSandboxFileEngine(new MeSandboxFileEngine())
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        mediaList.clear();
                        mediaList.addAll(result);
                        i = 0;
                        postFile();
                    }

                    @Override
                    public void onCancel() {

                    }
                });
    }

    private List<LocalMedia> mediaList = new ArrayList<>();
    private int i;

    /**
     * 上传文件
     */
    private void postFile() {
        if (mediaList.size() < 1) {
            return;
        }
        showDialog();
        File file = new File(mediaList.get(i).getAvailablePath());
        RXHttpUtil.requestByPostUploadFile((LifecycleOwner) mActivity,
                ZURL.getUploadFile(),
                file,
                UrlData.class,
                new RequestListener<UrlData>() {
                    @Override
                    public void success(UrlData data) {
                        imgList.add(data.getUrl());
                        imgAdapter.setDataList(imgList);
                        if (mediaList.size() > i) {
                            i++;
                            mBinding.tvDialogImgCount.setText("(" + imgList.size() + "/3)");
                            postFile();
                        } else {
                            hideDialog();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        if (mediaList.size() > i) {
                            i++;
                            postFile();
                        } else {
                            hideDialog();
                        }
                    }
                });
    }
    /**********选择图片end*********/

    /**
     * 出入库原因
     */
    private void getReason() {
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getGoodsStockReason(),
                new HashMap<>(),
                GoodsStockReasonData.class,
                new RequestListListener<GoodsStockReasonData>() {
                    @Override
                    public void onResult(List<GoodsStockReasonData> list) {
                        if (list != null) {
                            reasonList.clear();
                            for (int i = 0; i < list.size(); i++) {
                                if (!TextUtils.isEmpty(list.get(i).getDictType())) {
                                    if (list.get(i).getDictType().equals("sys_outbound_reason")) {
                                        reasonList.addAll(list.get(i).getList());
                                    }
                                }
                            }
                            reasonAdapter.setDataList(reasonList);
                        }
                    }
                });
    }

    /**
     * 查询批次列表
     */
    private void getBatchList() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("goodsBarcode", barcode);
        params.put("pageIndex", 1);
        params.put("pageSize", 10000);
        RXHttpUtil.requestByBodyPostAsResponseList(this,
                ZURL.getGoodBatchList(),
                params,
                GoodsBatchData.class,
                new RequestListListener<GoodsBatchData>() {
                    @Override
                    public void onResult(List<GoodsBatchData> list) {
                        if (list == null) {
                            if (batchList.size() > 0) {
                                mBinding.linDialogBatch.setVisibility(View.VISIBLE);
                            } else {
                                mBinding.linDialogBatch.setVisibility(View.GONE);
                            }
                            return;
                        }
                        batchList.clear();
                        batchList.addAll(list);
                        if (batchList.size() > 0) {
                            mBinding.linDialogBatch.setVisibility(View.VISIBLE);
                            batchAdapter.setDataList(batchList);
                        } else {
                            mBinding.linDialogBatch.setVisibility(View.GONE);
                        }
                    }
                });
    }

    /**
     * 出入库接口
     */
    public void setSum_num() {
        JSONArray array = new JSONArray();
        for (int i = 0; i < batchList.size(); i++) {
            if (batchList.get(i).getCount() > 0) {
                JSONObject object = new JSONObject();
                try {
                    object.put("batchUnique", batchList.get(i).getBatchUnique());
                    object.put("outStockCount", batchList.get(i).getCount());
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                array.put(object);
            }
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("goodsBarcode", barcode);
        params.put("stockType", 2);//1.入库 2.出库
        params.put("stockOrigin", 2);//操作来源：1、手机；2、PC端；3、web网页端；4、小程序
        params.put("goodsCount", count);
        params.put("stockPrice", DFUtils.getNum4(price));
        params.put("reason", reason);
        if (array.length() > 0) {
            params.put("goodBatchMessage", array.toString());//批次信息
        }
        params.put("stockRemarks", remarks);
        params.put("stockPicture", img);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getGoodsStockEdit(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        showToast(0, s);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_LIST));
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_INFO));
                        GoodsData data = LitePal.where("goods_barcode = ?", barcode).findFirst(GoodsData.class);
                        if (data != null) {
                            data.setGoods_count(data.getGoods_count() - count);
                            data.save();
                        }
                        if (listener != null) {
                            listener.onConfirm(barcode, -count);
                        }
                        dismiss();
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(String barcode, double count);
    }

}
