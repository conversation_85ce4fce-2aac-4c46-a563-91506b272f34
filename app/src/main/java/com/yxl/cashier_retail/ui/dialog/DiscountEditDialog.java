package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogDiscountEditBinding;
import com.yxl.cashier_retail.ui.adapter.DiscountAdapter;
import com.yxl.cashier_retail.ui.bean.DiscountData;
import com.yxl.cashier_retail.view.NumberKeyBoardView;
import com.yxl.commonlibrary.utils.DensityUtils;

import org.litepal.LitePal;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Describe:dialog（整单折扣编辑）
 * Created by jingang on 2024/5/30
 */
@SuppressLint("NonConstantResourceId")
public class DiscountEditDialog extends BaseDialog<DialogDiscountEditBinding> implements View.OnClickListener {
    private int zeroType,//抹零 0.不抹零 1.抹零到1元 2.抹零到5角 3.抹零到1角
            position;//折扣列表下标

    private DiscountAdapter discountAdapter;
    private List<DiscountData> discountList = new ArrayList<>();

    public static void showDialog(Activity activity, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        DiscountEditDialog.listener = listener;
        DiscountEditDialog dialog = new DiscountEditDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 7);
        dialog.show();
    }

    public DiscountEditDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        setUI();
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.tvDialogZero0.setOnClickListener(this);
        mBinding.tvDialogZero1.setOnClickListener(this);
        mBinding.tvDialogZero2.setOnClickListener(this);
        mBinding.numberKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var) {
                double discount;
                if (TextUtils.isEmpty(var)) {
                    discount = 0;
                } else {
                    discount = Double.parseDouble(var);
                }
                discountList.get(position).setDiscount(discount);
                discountAdapter.notifyItemChanged(position);
            }

            @SuppressLint("NewApi")
            @Override
            public void onConfirm() {
                SPUtils.getInstance().put(Constants.ZERO, zeroType);
                Collections.sort(discountList, Comparator.comparingDouble(DiscountData::getDiscount).reversed());//排序

                List<DiscountData> list = new ArrayList<>();
                for (int i = 0; i < discountList.size(); i++) {
                    list.add(new DiscountData(discountList.get(i).getDiscount()));
                }
                DiscountData data = LitePal.findFirst(DiscountData.class);
                if (data != null) {
                    LitePal.deleteAll(DiscountData.class);
                }
                if (LitePal.saveAll(list)) {
                    Log.e(tag, "折扣数据保存成功");
                    if (listener != null) {
                        listener.onConfirm();
                        dismiss();
                    }
                } else {
                    Log.e(tag, "折扣数据保存失败");
                }
            }
        });
    }

    @Override
    protected DialogDiscountEditBinding getViewBinding() {
        return DialogDiscountEditBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogZero0:
                //抹零到1元
                if (zeroType != 1) {
                    zeroType = 1;
                    mBinding.tvDialogZero0.setBackgroundResource(R.drawable.shape_red_tm_5);
                    mBinding.tvDialogZero1.setBackgroundResource(R.drawable.shape_red_kuang_5);
                    mBinding.tvDialogZero2.setBackgroundResource(R.drawable.shape_red_kuang_5);
                }else{
                    zeroType = 0;
                    mBinding.tvDialogZero0.setBackgroundResource(R.drawable.shape_red_kuang_5);
                }
                break;
            case R.id.tvDialogZero1:
                //抹零到5角
                if (zeroType != 2) {
                    zeroType = 2;
                    mBinding.tvDialogZero0.setBackgroundResource(R.drawable.shape_red_kuang_5);
                    mBinding.tvDialogZero1.setBackgroundResource(R.drawable.shape_red_tm_5);
                    mBinding.tvDialogZero2.setBackgroundResource(R.drawable.shape_red_kuang_5);
                }else{
                    zeroType = 0;
                    mBinding.tvDialogZero1.setBackgroundResource(R.drawable.shape_red_kuang_5);
                }
                break;
            case R.id.tvDialogZero2:
                //抹零到1角
                if (zeroType != 3) {
                    zeroType = 3;
                    mBinding.tvDialogZero0.setBackgroundResource(R.drawable.shape_red_kuang_5);
                    mBinding.tvDialogZero1.setBackgroundResource(R.drawable.shape_red_kuang_5);
                    mBinding.tvDialogZero2.setBackgroundResource(R.drawable.shape_red_tm_5);
                }else{
                    zeroType = 0;
                    mBinding.tvDialogZero2.setBackgroundResource(R.drawable.shape_red_kuang_5);
                }
                break;
        }
    }

    /**
     * 更新UI
     */
    private void setUI() {
        //抹零 0.不抹零 1.抹零到1元 2.抹零到5角 3.抹零到1角
        zeroType = SPUtils.getInstance().getInt(Constants.ZERO, 0);
        switch (zeroType) {
            case 1:
                mBinding.tvDialogZero0.setBackgroundResource(R.drawable.shape_red_tm_5);
                mBinding.tvDialogZero1.setBackgroundResource(R.drawable.shape_red_kuang_5);
                mBinding.tvDialogZero2.setBackgroundResource(R.drawable.shape_red_kuang_5);
                break;
            case 2:
                mBinding.tvDialogZero0.setBackgroundResource(R.drawable.shape_red_kuang_5);
                mBinding.tvDialogZero1.setBackgroundResource(R.drawable.shape_red_tm_5);
                mBinding.tvDialogZero2.setBackgroundResource(R.drawable.shape_red_kuang_5);
                break;
            case 3:
                mBinding.tvDialogZero0.setBackgroundResource(R.drawable.shape_red_kuang_5);
                mBinding.tvDialogZero1.setBackgroundResource(R.drawable.shape_red_kuang_5);
                mBinding.tvDialogZero2.setBackgroundResource(R.drawable.shape_red_tm_5);
                break;
            default:
                mBinding.tvDialogZero0.setBackgroundResource(R.drawable.shape_red_kuang_5);
                mBinding.tvDialogZero1.setBackgroundResource(R.drawable.shape_red_kuang_5);
                mBinding.tvDialogZero2.setBackgroundResource(R.drawable.shape_red_kuang_5);
                break;
        }

        //折扣
        discountList.clear();
        discountList.addAll(LitePal.findAll(DiscountData.class));
        discountAdapter = new DiscountAdapter(getContext(), 1);
        mBinding.rvDialogDiscount.setAdapter(discountAdapter);
        discountAdapter.setDataList(discountList);
        discountAdapter.setOnItemClickListener((view, position) -> {
            this.position = position;
            if (!discountList.get(position).isSelect()) {
                for (int i = 0; i < discountList.size(); i++) {
                    if (discountList.get(i).isSelect()) {
                        discountList.get(i).setSelect(false);
                        discountAdapter.notifyItemChanged(i);
                    }
                }
                discountList.get(position).setSelect(true);
                discountAdapter.notifyItemChanged(position);
            }
        });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm();
    }
}
