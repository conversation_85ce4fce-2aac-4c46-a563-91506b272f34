package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.MallCartData;

import java.util.List;

/**
 * Describe:商城-购物车（适配器）
 * Created by jingang on 2024/6/4
 */
public class MallCartAdapter extends BaseAdapter<MallCartData> {

    public MallCartAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_mall_cart;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        ImageView ivSelect = holder.getView(R.id.ivItemSelect);
        TextView  tvDelivery, tvShippedMoney, tvShipped;
        tvDelivery = holder.getView(R.id.tvItemDelivery);
        LinearLayout linShipped = holder.getView(R.id.linItemShipped);
        tvShippedMoney = holder.getView(R.id.tvItemShippedMoney);
        tvShipped = holder.getView(R.id.tvItemShipped);
        RecyclerView recyclerView = holder.getView(R.id.rvItem);

        ivSelect.setSelected(mDataList.get(position).isSelect());

        if (mDataList.get(position).getGood_list() != null) {
            if (mDataList.get(position).getGood_list().size() > 0) {
                recyclerView.setVisibility(View.VISIBLE);
                MallCartGoodsAdapter goodsAdapter = new MallCartGoodsAdapter(mContext);
                recyclerView.setAdapter(goodsAdapter);
                goodsAdapter.setDataList(mDataList.get(position).getGood_list());
                goodsAdapter.setListener(new MallCartGoodsAdapter.MyListener() {
                    @Override
                    public void onSelectClick(View view, int positionChild) {
                        if (listener != null) {
                            listener.onChildSelectClick(view, position, positionChild);
                        }
                    }

                    @Override
                    public void onSubClick(View view, int positionChild) {
                        if (listener != null) {
                            listener.onChildSubClick(view, position, positionChild);
                        }
                    }

                    @Override
                    public void onAddClick(View view, int positionChild) {
                        if (listener != null) {
                            listener.onChildAddClick(view, position, positionChild);
                        }
                    }
                });
            } else {
                recyclerView.setVisibility(View.GONE);
            }
        } else {
            recyclerView.setVisibility(View.GONE);
        }
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivSelect = holder.getView(R.id.ivItemSelect);
        TextView tvCompany, tvDelivery, tvShippedMoney, tvShipped;
        tvCompany = holder.getView(R.id.tvItemCompany);
        tvDelivery = holder.getView(R.id.tvItemDelivery);
        LinearLayout linShipped = holder.getView(R.id.linItemShipped);
        tvShippedMoney = holder.getView(R.id.tvItemShippedMoney);
        tvShipped = holder.getView(R.id.tvItemShipped);
        RecyclerView recyclerView = holder.getView(R.id.rvItem);

        ivSelect.setSelected(mDataList.get(position).isSelect());
        tvCompany.setText(mDataList.get(position).getCompany_name());

        if (mDataList.get(position).getGood_list() != null) {
            if (mDataList.get(position).getGood_list().size() > 0) {
                recyclerView.setVisibility(View.VISIBLE);
                MallCartGoodsAdapter goodsAdapter = new MallCartGoodsAdapter(mContext);
                recyclerView.setAdapter(goodsAdapter);
                goodsAdapter.setDataList(mDataList.get(position).getGood_list());
                goodsAdapter.setListener(new MallCartGoodsAdapter.MyListener() {
                    @Override
                    public void onSelectClick(View view, int positionChild) {
                        if (listener != null) {
                            listener.onChildSelectClick(view, position, positionChild);
                        }
                    }

                    @Override
                    public void onSubClick(View view, int positionChild) {
                        if (listener != null) {
                            listener.onChildSubClick(view, position, positionChild);
                        }
                    }

                    @Override
                    public void onAddClick(View view, int positionChild) {
                        if (listener != null) {
                            listener.onChildAddClick(view, position, positionChild);
                        }
                    }
                });
            } else {
                recyclerView.setVisibility(View.GONE);
            }
        } else {
            recyclerView.setVisibility(View.GONE);
        }

        if (listener != null) {
            ivSelect.setOnClickListener(v -> listener.onSelectClick(v, position));
            tvShipped.setOnClickListener(v -> listener.onShippedClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onSelectClick(View view, int position);

        void onShippedClick(View view, int position);

        void onChildSelectClick(View view, int position, int positionChild);

        void onChildSubClick(View view, int position, int positionChild);

        void onChildAddClick(View view, int position, int positionChild);
    }
}
