package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.view.View;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmQueryGoodsBinding;
import com.yxl.cashier_retail.ui.adapter.GoodsQueryAdapter;
import com.yxl.cashier_retail.ui.adapter.GoodsSaleInfoOrderAdapter;
import com.yxl.cashier_retail.ui.bean.GoodsSaleInfoData;
import com.yxl.cashier_retail.ui.bean.GoodsSaleStatisticsData;
import com.yxl.cashier_retail.ui.bean.SupplierPcData;
import com.yxl.cashier_retail.ui.dialog.CateDialog;
import com.yxl.cashier_retail.ui.dialog.DateStartEndDialog;
import com.yxl.cashier_retail.ui.dialog.SupplierEditDialog;
import com.yxl.cashier_retail.ui.popupwindow.SupplierPop;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.utils.DateUtils;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:查询-查商品
 * Created by jingang on 2024/5/23
 */
@SuppressLint({"NonConstantResourceId", "NotifyDataSetChanged", "SetTextI18n"})
public class QueryGoodsFragment extends BaseFragment<FmQueryGoodsBinding> implements View.OnClickListener {
    private String cateUnique,//一级分类编号
            cateChildUnique,//二级分类编号
            supplierUnique,//供货商编号
            startDate, endDate,
            keyWords,//搜索关键字
            barcode;
    private int pageInfo = 1;

    //供货商列表
    private List<SupplierPcData> supplierList = new ArrayList<>();

    //商品列表
    private GoodsQueryAdapter mAdapter;
    private final List<GoodsSaleStatisticsData.DataBean> dataList = new ArrayList<>();

    //销售订单
    private GoodsSaleInfoOrderAdapter orderAdapter;
    private final List<GoodsSaleInfoData.DataBean> orderList = new ArrayList<>();

    /**
     * 搜索
     *
     * @param keyWords
     */
    public void setKeyWords(String keyWords) {
        this.keyWords = keyWords;
        page = 1;
        getGoodsSaleStatistics();
    }

    @Override
    protected FmQueryGoodsBinding getViewBinding() {
        return FmQueryGoodsBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.tvCount.setText(getRstr(R.string.sale_count_total_colon) + "0");
        mBinding.tvTotal.setText(getRstr(R.string.sale_amount_total_colon) + "0");
        mBinding.tvPurchase.setText(getRstr(R.string.sale_purchase_total_colon) + "0");
        mBinding.tvProfit.setText(getRstr(R.string.sale_profit_total_colon) + "0");
        mBinding.linCate0.setOnClickListener(this);
        mBinding.linCate1.setOnClickListener(this);
        mBinding.linSupplier.setOnClickListener(this);
        mBinding.linStartDate.setOnClickListener(this);
        mBinding.linEndDate.setOnClickListener(this);

        startDate = DateUtils.getOldDate(0);
        endDate = startDate;
        mBinding.tvStartDate.setText(startDate);
        mBinding.tvEndDate.setText(endDate);
        setAdapter();
    }

    @Override
    protected void initData() {
        getGoodsSaleStatistics();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.linCate0:
                //选择一级分类
                CateDialog.showDialog(getActivity(), cateUnique, cateChildUnique, mBinding.ivCate0, 1, (unique, name, childUnique, childName) -> {
                    cateUnique = unique;
                    cateChildUnique = childUnique;
                    mBinding.tvCate0.setText(name);
                    mBinding.tvCate1.setText(childName);
                    page = 1;
                    getGoodsSaleStatistics();
                });
                break;
            case R.id.linCate1:
                //选择二级分类
                CateDialog.showDialog(getActivity(), cateUnique, cateChildUnique, mBinding.ivCate1, 1, (unique, name, childUnique, childName) -> {
                    cateUnique = unique;
                    cateChildUnique = childUnique;
                    mBinding.tvCate0.setText(name);
                    mBinding.tvCate1.setText(childName);
                    page = 1;
                    getGoodsSaleStatistics();
                });
                break;
            case R.id.linSupplier:
                //选择供货商
                if (supplierList.isEmpty()) {
                    getSupplierList(v);
                } else {
                    showPopSupplier(v);
                }
                break;
            case R.id.linStartDate:
                //开始日期
                showDialogDate(0, mBinding.ivStartDate);
                break;
            case R.id.linEndDate:
                //结束日期
                showDialogDate(1, mBinding.ivEndDate);
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //商品列表
        mAdapter = new GoodsQueryAdapter(getActivity());
        mBinding.vSmartrefreshlayout.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (!dataList.get(position).isSelect()) {
                for (int i = 0; i < dataList.size(); i++) {
                    if (dataList.get(i).isSelect()) {
                        dataList.get(i).setSelect(false);
                        mAdapter.notifyItemChanged(i);
                    }
                }
                dataList.get(position).setSelect(true);
                mAdapter.notifyItemChanged(position);
                barcode = dataList.get(position).getGoodsBarcode();
                getGoodsSaleInfo();
            }
        });
        mBinding.vSmartrefreshlayout.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getGoodsSaleStatistics();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getGoodsSaleStatistics();
            }
        });

        //详情-销售订单
        orderAdapter = new GoodsSaleInfoOrderAdapter(getActivity());
        mBinding.rvOrder.setAdapter(orderAdapter);
        mBinding.srlInfo.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                pageInfo++;
                getGoodsSaleInfo();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                pageInfo = 1;
                getGoodsSaleInfo();
            }
        });
    }

    /**
     * dialog（选择日期时间）
     */
    private void showDialogDate(int type, View view) {
        DateStartEndDialog.showDialog(getActivity(),
                view,
                startDate,
                endDate,
                type,
                (startDate, endDate) -> {
                    this.startDate = startDate;
                    this.endDate = endDate;
                    mBinding.tvStartDate.setText(startDate);
                    mBinding.tvEndDate.setText(endDate);
                    //请求数据
                    page = 1;
                    getGoodsSaleStatistics();
                });
    }

    /**
     * pop 选择供货商
     * @param view
     */
    private void showPopSupplier(View view) {
        SupplierPop.showDialog(getActivity(),
                mBinding.ivSupplier,
                view,
                mBinding.linSupplier.getMeasuredWidth(),
                supplierList,
                supplierUnique,
                new SupplierPop.MyListener() {
                    @Override
                    public void onAddClick() {
                        //添加供货商
                        SupplierEditDialog.showDialog(getActivity(), 0, "", (id, name, type) -> {
                            getSupplierList(view);
                        });
                    }

                    @Override
                    public void onCallBack(SupplierPcData data) {
                        supplierUnique = data.getSupplier_unique();
                        mBinding.tvSupplier.setText(data.getSupplier_name());
                        mBinding.vSmartrefreshlayout.smartRefreshLayout.autoRefresh();
                    }
                });
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(GoodsSaleStatisticsData data) {
        //写出花来
        if (data.getData1() != null) {
            if (!data.getData1().isEmpty()) {
                mBinding.tvCount.setText(getRstr(R.string.sale_count_total_colon) + DFUtils.getNum4(data.getData1().get(0).getSaleCount()));
                mBinding.tvTotal.setText(getRstr(R.string.sale_amount_total_colon) + DFUtils.getNum2(data.getData1().get(0).getSaleSum()));
                mBinding.tvPurchase.setText(getRstr(R.string.sale_purchase_total_colon) + DFUtils.getNum2(data.getData1().get(0).getPurSum()));
                mBinding.tvProfit.setText(getRstr(R.string.sale_profit_total_colon) + DFUtils.getNum2(data.getData1().get(0).getGrossProfit()));
            }
        }
        if (page == 1) {
            dataList.clear();
            if (data.getData() != null) {
                if (!data.getData().isEmpty()) {
                    data.getData().get(0).setSelect(true);
                    barcode = data.getData().get(0).getGoodsBarcode();
                    getGoodsSaleInfo();
                }
            }
        }
        if (data.getData() != null) {
            dataList.addAll(data.getData());
        }
        if (!dataList.isEmpty()) {
            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.VISIBLE);
            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.GONE);
            mAdapter.setDataList(dataList);
        } else {
            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.GONE);
            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 更新UI-详情
     *
     * @param data
     */
    private void setUIInfo(GoodsSaleInfoData data) {
        if (data == null) {
            mBinding.tvNothing.setVisibility(View.VISIBLE);
            mBinding.linInfo.setVisibility(View.GONE);
            return;
        }
        mBinding.tvNothing.setVisibility(View.GONE);
        mBinding.linInfo.setVisibility(View.VISIBLE);
        //销售统计
        if (data.getData1() != null) {
            mBinding.tvCountInfo.setText(DFUtils.getNum4(data.getData1().getSaleCount()));
            mBinding.tvTotalInfo.setText(DFUtils.getNum2(data.getData1().getSaleSum()));
            mBinding.tvPurchaseInfo.setText(DFUtils.getNum2(data.getData1().getPurSum()));
            mBinding.tvProfitInfo.setText(DFUtils.getNum2(data.getData1().getLirun_sum()));
        }
        //销售订单
        if (pageInfo == 1) {
            orderList.clear();
        }
        if (data.getData() != null) {
            orderList.addAll(data.getData());
        }
        if (orderList.size() > 0) {
            mBinding.rvOrder.setVisibility(View.VISIBLE);
            mBinding.linEmptyInfo.setVisibility(View.GONE);
            orderAdapter.setDataList(orderList);
        } else {
            mBinding.rvOrder.setVisibility(View.GONE);
            mBinding.linEmptyInfo.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 供货商列表
     */
    private void getSupplierList(View v) {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("supMsg", "");
        params.put("page", 1);
        params.put("limit", 10000);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getSupplierList(),
                params,
                SupplierPcData.class,
                new RequestListListener<SupplierPcData>() {
                    @Override
                    public void onResult(List<SupplierPcData> list) {
                        supplierList.clear();
                        supplierList.addAll(list);
                        showPopSupplier(v);
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 商品销售统计
     */
    private void getGoodsSaleStatistics() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("goodsMsg", keyWords);//搜索关键字
        params.put("groupUnique", cateUnique);//一级分类编号
        params.put("kindUnique", cateChildUnique);//二级分类编号
        params.put("supplierUnique", supplierUnique);//供货商编号
        params.put("startTime", startDate);
        params.put("endTime", endDate);
        params.put("pageNum", page);
        params.put("pageSize", Constants.limit);
        showDialog();
        RXHttpUtil.requestByFormPostAsOriginalResponse(getActivity(),
                ZURL.getGoodsSaleStatistics(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        mBinding.vSmartrefreshlayout.smartRefreshLayout.finishRefresh();
                        mBinding.vSmartrefreshlayout.smartRefreshLayout.finishLoadMore();
                        GoodsSaleStatisticsData data = new Gson().fromJson(s, GoodsSaleStatisticsData.class);
                        if (data == null) {
                            return;
                        }
                        if (data.getStatus() != Constants.SUCCESS_CODE) {
                            showToast(1, data.getMsg());
                            return;
                        }
                        setUI(data);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.vSmartrefreshlayout.smartRefreshLayout.finishRefresh();
                        mBinding.vSmartrefreshlayout.smartRefreshLayout.finishLoadMore();
                        if (!dataList.isEmpty()) {
                            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.GONE);
                            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 商品销售统计详情
     */
    private void getGoodsSaleInfo() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("goodsBarcode", barcode);
        params.put("startTime", startDate);
        params.put("endTime", endDate);
        params.put("pageNum", pageInfo);
        params.put("pageSize", Constants.limit);
        showDialog();
        RXHttpUtil.requestByFormPostAsOriginalResponse(getActivity(),
                ZURL.getGoodsSaleInfo(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        mBinding.srlInfo.finishRefresh();
                        mBinding.srlInfo.finishLoadMore();
                        GoodsSaleInfoData data = new Gson().fromJson(s, GoodsSaleInfoData.class);
                        if (data == null) {
                            setUIInfo(null);
                            return;
                        }
                        if (data.getStatus() != Constants.SUCCESS_CODE) {
                            showToast(1, data.getMsg());
                            return;
                        }
                        setUIInfo(data);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.srlInfo.finishRefresh();
                        mBinding.srlInfo.finishLoadMore();
                    }
                });
    }

}
