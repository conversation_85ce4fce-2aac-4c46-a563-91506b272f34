package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.HangData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.utils.DateUtils;

import java.util.List;

/**
 * Describe:挂单列表（适配器）
 * Created by jingang on 2024/5/29
 */
public class HangOrderAdapter extends BaseAdapter<HangData> {

    public HangOrderAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_hang_order;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName, tvTime, tvTotal, tvCount;
        ImageView ivTotal, ivCount;
        tvName = holder.getView(R.id.tvItemName);
        tvTime = holder.getView(R.id.tvItemTime);
        ivTotal = holder.getView(R.id.ivItemTotal);
        tvTotal = holder.getView(R.id.tvItemTotal);
        ivCount = holder.getView(R.id.ivItemCount);
        tvCount = holder.getView(R.id.tvItemCount);
        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundResource(R.drawable.shape_green_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
            tvTime.setTextColor(mContext.getResources().getColor(R.color.white));
            ivTotal.setImageResource(R.mipmap.ic_hang_money002);
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.white));
            ivCount.setImageResource(R.mipmap.ic_hang_count002);
            tvCount.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundResource(R.drawable.shape_white_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
            tvTime.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
            ivTotal.setImageResource(R.mipmap.ic_hang_money001);
            tvTotal.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.orange));
            ivCount.setImageResource(R.mipmap.ic_hang_count001);
            tvCount.setTextColor(mContext.getResources().getColor(R.color.black));
        }
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName, tvTime, tvTotal, tvCount;
        ImageView ivDel, ivTotal, ivCount;
        ivDel = holder.getView(R.id.ivItemDel);
        tvName = holder.getView(R.id.tvItemName);
        tvTime = holder.getView(R.id.tvItemTime);
        ivTotal = holder.getView(R.id.ivItemTotal);
        tvTotal = holder.getView(R.id.tvItemTotal);
        ivCount = holder.getView(R.id.ivItemCount);
        tvCount = holder.getView(R.id.tvItemCount);

        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundResource(R.drawable.shape_green_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
            tvTime.setTextColor(mContext.getResources().getColor(R.color.white));
            ivTotal.setImageResource(R.mipmap.ic_hang_money002);
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.white));
            ivCount.setImageResource(R.mipmap.ic_hang_count002);
            tvCount.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundResource(R.drawable.shape_white_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
            tvTime.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
            ivTotal.setImageResource(R.mipmap.ic_hang_money001);
            tvTotal.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.orange));
            ivCount.setImageResource(R.mipmap.ic_hang_count001);
            tvCount.setTextColor(mContext.getResources().getColor(R.color.black));
        }
        tvName.setText(TextUtils.isEmpty(mDataList.get(position).getMemberName()) ? getRstr(R.string.fit) : mDataList.get(position).getMemberName());
        long timestamp = TextUtils.isEmpty(mDataList.get(position).getTimestamp()) ? 0 : Long.parseLong(mDataList.get(position).getTimestamp());
        tvTime.setText(DateUtils.getDateToString(timestamp, DateUtils.PATTERN_SECOND));
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getTotal()));
        tvCount.setText(String.valueOf(mDataList.get(position).getCount()));
        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(v, position));
            ivDel.setOnClickListener(v -> listener.onDelClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onDelClick(View view, int position);
    }
}