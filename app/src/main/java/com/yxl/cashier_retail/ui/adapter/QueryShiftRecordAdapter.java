package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.ShiftRecordData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:查询-交接班记录（适配器）
 * Created by jingang on 2024/8/23
 */
public class QueryShiftRecordAdapter extends BaseAdapter<ShiftRecordData> {

    public QueryShiftRecordAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_query_shift;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName, tvNo, tvDateStart, tvDateEnd, tvCount, tvTotal;
        tvName = holder.getView(R.id.tvItemName);
        tvNo = holder.getView(R.id.tvItemNo);
        tvDateStart = holder.getView(R.id.tvItemDateStart);
        tvDateEnd = holder.getView(R.id.tvItemDateEnd);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTotal = holder.getView(R.id.tvItemTotal);

        if (mDataList.get(position).isCheck()) {
            lin.setBackgroundColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
            tvNo.setTextColor(mContext.getResources().getColor(R.color.white));
            tvDateStart.setTextColor(mContext.getResources().getColor(R.color.white));
            tvDateEnd.setTextColor(mContext.getResources().getColor(R.color.white));
            tvCount.setTextColor(mContext.getResources().getColor(R.color.white));
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            if (position % 2 == 0) {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
            } else {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
            }
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
            tvNo.setTextColor(mContext.getResources().getColor(R.color.black));
            tvDateStart.setTextColor(mContext.getResources().getColor(R.color.black));
            tvDateEnd.setTextColor(mContext.getResources().getColor(R.color.black));
            tvCount.setTextColor(mContext.getResources().getColor(R.color.black));
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.black));
        }
        tvName.setText(TextUtils.isEmpty(mDataList.get(position).getStaff_name()) ? "-" : mDataList.get(position).getStaff_name());
        tvNo.setText(TextUtils.isEmpty(mDataList.get(position).getSale_list_cashier()) ? "-" : mDataList.get(position).getSale_list_cashier());
        tvDateStart.setText(TextUtils.isEmpty(mDataList.get(position).getLogin_datetime()) ? "-" : mDataList.get(position).getLogin_datetime());
        tvDateEnd.setText(TextUtils.isEmpty(mDataList.get(position).getSign_out_datetime()) ? getRstr(R.string.cashiering) : mDataList.get(position).getSign_out_datetime());
        tvCount.setText(String.valueOf(mDataList.get(position).getOrderCount()));
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getSumMoney()));
    }
}
