package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageFormat;
import android.graphics.Rect;
import android.graphics.YuvImage;
import android.hardware.Camera;
import android.hardware.camera2.CameraCharacteristics;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.SurfaceHolder;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogPaymentFaceBinding;
import com.yxl.cashier_retail.faceplat.MTCNN;
import com.yxl.cashier_retail.ui.bean.FaceMemberData;
import com.yxl.cashier_retail.ui.bean.MemberData;
import com.yxl.cashier_retail.utils.BitmapUtils;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.view.DifferentDisplay;
import com.yxl.commonlibrary.base.BaseData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:dialog（刷脸付）
 * Created by jingang on 2024/6/24
 */
@SuppressLint({"NonConstantResourceId", "StaticFieldLeak", "SetTextI18n"})
public class PaymentFaceDialog extends BaseDialog<DialogPaymentFaceBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private static DifferentDisplay mPresentation;
    private static double total;
    private SurfaceHolder mHolder;
    private Camera mCamera;
    private MTCNN mMtcnn;
    private boolean isfinsh;
    private Bitmap mCropBitmap, mDetectedBitmap;
    private long mLastFaceDetectTime = System.currentTimeMillis() + 1500;
    private int mTimeInterval = 200; // 人脸检测间隔时间

    public static void showDialog(Activity activity, DifferentDisplay mPresentation, double total, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        PaymentFaceDialog.listener = listener;
        PaymentFaceDialog.mPresentation = mPresentation;
        PaymentFaceDialog.total = total;
        PaymentFaceDialog dialog = new PaymentFaceDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public PaymentFaceDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(false);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.tvDialogTotal.setText(getRstr(R.string.consumption_total_colon) + DFUtils.getNum2(total) + getRstr(R.string.yuan));
        initViews();
    }

    @Override
    protected DialogPaymentFaceBinding getViewBinding() {
        return DialogPaymentFaceBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void dismiss() {
        super.dismiss();
        // 关闭摄像头
        isfinsh = true;
        if (mCropBitmap != null) {
            if (!mCropBitmap.isRecycled()) {
                mCropBitmap.recycle();
            }
            mCropBitmap = null;
        }
        if (mDetectedBitmap != null) {
            if (!mDetectedBitmap.isRecycled()) {
                mDetectedBitmap.recycle();
            }
            mDetectedBitmap = null;
        }
        closeCamera();
    }

    @Override
    public void hide() {
        super.hide();
        // 关闭摄像头
        isfinsh = true;
        if (mCropBitmap != null) {
            if (!mCropBitmap.isRecycled()) {
                mCropBitmap.recycle();
            }
            mCropBitmap = null;
        }
        if (mDetectedBitmap != null) {
            if (!mDetectedBitmap.isRecycled()) {
                mDetectedBitmap.recycle();
            }
            mDetectedBitmap = null;
        }
        closeCamera();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
        }
    }

    private void initViews() {
        mHolder = mBinding.surfaceView.getHolder();
        mHolder.addCallback(new SurfaceHolder.Callback() {
            @Override
            public void surfaceCreated(SurfaceHolder holder) {
                Log.e(tag, "surfaceCreated");
                openCamera();
            }

            @Override
            public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
                if (mHolder.getSurface() == null) {
                    Log.e(tag, "mHolder.getSurface() == null");
                    return;
                }
                try {
                    mCamera.stopPreview();
                } catch (Exception e) {
                    Log.e(tag, "Error stopping camera preview: " + e.getMessage());
                    if (listener != null) {
                        listener.onCameraFail(getRstr(R.string.camera_fail) + e.getMessage());
                        dismiss();
                    }
                }

                try {
                    int measuredWidth = mBinding.surfaceView.getMeasuredWidth();
                    int measuredHeight = mBinding.surfaceView.getMeasuredHeight();
                    setCameraParms(mCamera, measuredWidth, measuredHeight);
                    mCamera.startPreview();
                    startFaceDetection(); // re-start face detection feature
                    mCamera.setPreviewCallback((data, camera) -> {
                        try {
                            if (isfinsh || camera.getParameters() == null) {
                                return;
                            }
                        } catch (Exception e) {
                            return;
                        }

                        Camera.Size size = camera.getParameters().getPreviewSize();
                        YuvImage image = new YuvImage(data, ImageFormat.NV21, size.width, size.height, null);
                        //人脸同步到副屏
                        ByteArrayOutputStream stream = new ByteArrayOutputStream();
                        image.compressToJpeg(new Rect(0, 0, size.width, size.height), 50, stream);
                        mCropBitmap = BitmapFactory.decodeByteArray(stream.toByteArray(), 0, stream.size());
                        if (mCropBitmap != null) {
                            faceDetect(mCropBitmap);
                        }
                        try {
                            stream.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    });
                } catch (Exception e) {
                    Log.e(tag, "Error starting camera preview: " + e.getMessage());
                    if (listener != null) {
                        listener.onCameraFail(getRstr(R.string.camera_fail) + e.getMessage());
                        dismiss();
                    }
                }
            }

            @Override
            public void surfaceDestroyed(SurfaceHolder holder) {
                Log.e(tag, "surfaceDestroyed");
                // mCamera.setPreviewCallback(null);// 防止 Method called after release()
            }
        });
    }

    /**
     * 开启
     */
    private void openCamera() {
        isfinsh = false;
        mBinding.ivDialogImg.setVisibility(View.GONE);
        if (mCamera == null) {
            try {
                try {
                    mCamera = Camera.open(CameraCharacteristics.LENS_FACING_FRONT);
                    if (null == mCamera) {
                        if (listener != null) {
                            listener.onCameraFail(getRstr(R.string.camera_fail));
                            dismiss();
                        }
                    }
                    mCamera.setFaceDetectionListener((faces, camera) -> {
                        if (faces.length > 0) {
                            if (faces.length > 1) {
                                mBinding.tvDialogTips.setText(getRstr(R.string.payment_face_tips1));
                            }
                            if (faces.length == 1 && !isfinsh) {
                                facePay();
                            }
                        } else {
                            // 只会执行一次
                            if (!isfinsh) {
                                mBinding.tvDialogTips.setText(getRstr(R.string.payment_face_tips2));
                            }

                        }
                    });
                    mCamera.setPreviewDisplay(mHolder);
                    startFaceDetection();
                } catch (RuntimeException e) {
                    dismiss();
                    if (listener != null) {
                        listener.onCameraFail(getRstr(R.string.camera_fail));
                        dismiss();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 关闭
     */
    private void closeCamera() {
        if (mCamera == null) {
            return;
        }
        mCamera.setPreviewCallback(null);
        mCamera.stopPreview();
        mCamera.release();
    }

    private void faceDetect(Bitmap bitmap) {
        if (System.currentTimeMillis() - mLastFaceDetectTime < mTimeInterval || isfinsh) {
            return;
        }
        mLastFaceDetectTime = System.currentTimeMillis();
        if (null != bitmap && null != mMtcnn) {
            mDetectedBitmap = bitmap;
            int count = mMtcnn.facesCount(bitmap, 100);
            if (mPresentation != null) {
                mPresentation.setFace(bitmap, count, isfinsh);
            }
            if (count == 0) {
                mBinding.tvDialogTips.setText(getRstr(R.string.payment_face_tips1));
            } else if (count > 1) {
                mBinding.tvDialogTips.setText(getRstr(R.string.payment_face_tips2));
                mTimeInterval = 500;
            } else if (count == 1 && !isfinsh) {
                facePay();
            }
        }
    }

    public void startFaceDetection() {
        Camera.Parameters params = mCamera.getParameters();
        if (params.getMaxNumDetectedFaces() > 0) {
            try {
                mCamera.startFaceDetection();
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {// 摄相头不支持人脸检测时走手动判断画面中是否有人脸
            if (mMtcnn == null) {
                mMtcnn = new MTCNN(getContext().getAssets());
            }
//            showToast(1,"该设备不支持人脸检测");
//            dismiss();
        }
    }

    /**
     * 在摄像头启动前设置参数
     *
     * @param camera
     * @param width
     * @param height
     */
    private void setCameraParms(Camera camera, int width, int height) {
        // 获取摄像头支持的pictureSize列表
        Camera.Parameters parameters = camera.getParameters();
        List<Camera.Size> pictureSizeList = parameters.getSupportedPictureSizes();
        // 从列表中选择合适的分辨率
        Camera.Size pictureSize = getProperSize(pictureSizeList, (float) height / width);
        if (null == pictureSize) {
            pictureSize = parameters.getPictureSize();
        }
        // 根据选出的PictureSize重新设置SurfaceView大小
        float w = pictureSize.width;
        float h = pictureSize.height;
        parameters.setPictureSize(pictureSize.width, pictureSize.height);
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams((int) (height / (h / w)), height);
        layoutParams.gravity = Gravity.CENTER;
        mBinding.surfaceView.setLayoutParams(layoutParams);

        // 获取摄像头支持的PreviewSize列表
        List<Camera.Size> previewSizeList = parameters.getSupportedPreviewSizes();
        Camera.Size preSize = getProperSize(previewSizeList, (float) height / width);
        if (null != preSize) {
            parameters.setPreviewSize(preSize.width, preSize.height);
        }
        parameters.setJpegQuality(80);
        if (parameters.getSupportedFocusModes().contains(Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE)) {
            // 连续对焦
            parameters.setFocusMode(Camera.Parameters.FOCUS_MODE_CONTINUOUS_PICTURE);
        }
        camera.setDisplayOrientation(0);
        camera.setParameters(parameters);
    }

    private Camera.Size getProperSize(List<Camera.Size> pictureSizes, float screenRatio) {
        Camera.Size result = null;
        for (Camera.Size size : pictureSizes) {
            float currenRatio = ((float) size.width) / size.height;
            if (currenRatio - screenRatio == 0) {
                result = size;
                break;
            }
        }
        if (null == result) {
            for (Camera.Size size : pictureSizes) {
                float curRatio = ((float) size.width) / size.height;
                if (curRatio == 4f / 3) {
                    result = size;
                    break;
                }
            }
        }
        return result;
    }

    /**
     * 检测到人脸后处理刷脸支付
     */
    private void facePay() {
        isfinsh = true;
        mBinding.tvDialogTips.setText(getRstr(R.string.payment_face_tips3));
        mBinding.ivDialogImg.setVisibility(View.VISIBLE);
        // 获取头像
        if (mCropBitmap != null) {
            mBinding.ivDialogImg.setImageBitmap(BitmapUtils.convertHorizontalDirectionBmp(mCropBitmap));//需不需要镜像翻转？可能存在机型适配问题
            getMemberInfo(mCropBitmap);
        }
    }

    /**
     * 通过人脸搜索会员信息
     *
     * @param bitmap
     */
    private void getMemberInfo(Bitmap bitmap) {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("saleListTotal", total);//订单总金额
        params.put("onlineType", 1);//1.线上会员 其他.线下会员
        params.put("imgFormat", "jpg");//图片源文件格式
        params.put("imageMsg", BitmapUtils.bitmapToBase64(bitmap));
        showDialog();
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getFaceToMemberInfo(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        BaseData baseData = new Gson().fromJson(s, BaseData.class);
                        if (baseData == null) {
                            showToast(1, getRstr(R.string.member_information_fail));
                            dismiss();
                            return;
                        }
                        if (baseData.getStatus() != Constants.SUCCESS_CODE) {
                            showToast(1, baseData.getMsg());
                            dismiss();
                            return;
                        }

                        FaceMemberData data = new Gson().fromJson(s, FaceMemberData.class);
                        if (data == null) {
                            showToast(1, getRstr(R.string.member_information_fail));
                            dismiss();
                            return;
                        }
                        if (data.getData() == null) {
                            showToast(1, data.getMsg());
                            dismiss();
                            return;
                        }
                        if (data.getStatus() != Constants.SUCCESS_CODE) {
                            showToast(1, data.getMsg());
                            dismiss();
                            return;
                        }
                        double balance = TextUtils.isEmpty(data.getData().getCus_balance()) ? 0
                                : Double.parseDouble(data.getData().getCus_balance());
                        if (balance < total) {
                            showToast(1, getRstr(R.string.balance_not_enough));
                            dismiss();
                            return;
                        }
                        MemberData memberData = new MemberData(data.getData().getCusUnique());
                        if (listener != null) {
                            listener.onPaymentMember(memberData);
                            dismiss();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        dismiss();
                    }
                });
//        FaceMemberData.class,
//                new RequestListener<FaceMemberData>() {
//                    @Override
//                    public void success(FaceMemberData data) {
//                        hideDialog();
//                        if (data == null) {
//                            showToast(1, getRstr(R.string.member_information_fail));
//                            openCamera();
//                            return;
//                        }
//                        double balance = TextUtils.isEmpty(data.getCus_balance()) ? 0 : Double.parseDouble(data.getCus_balance());
//                        if (balance < total) {
//                            showToast(1, getRstr(R.string.balance_not_enough));
//                            openCamera();
//                            return;
//                        }
//                        MemberData memberData = new MemberData(data.getCusUnique());
//                        if (listener != null) {
//                            listener.onPaymentMember(memberData);
//                            dismiss();
//                        }
//                    }
//
//                    @Override
//                    public void onError(String msg) {
//                        hideDialog();
//                        showToast(1, msg);
//                        dismiss();
//                    }
//                });
    }

    private static MyListener listener;

    public interface MyListener {
        /**
         * 储值卡支付
         *
         * @param data
         */
        void onPaymentMember(MemberData data);

        /**
         * 获取相机失败
         *
         * @param msg
         */
        void onCameraFail(String msg);
    }
}
