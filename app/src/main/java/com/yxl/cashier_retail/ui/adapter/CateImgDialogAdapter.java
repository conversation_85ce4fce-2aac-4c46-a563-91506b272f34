package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.CateImgData;
import com.yxl.commonlibrary.utils.StringUtils;

/**
 * Describe:分类图标（适配器）
 * Created by jingang on 2024/8/21
 */
public class CateImgDialogAdapter extends BaseAdapter<CateImgData> {

    public CateImgDialogAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_cate_img;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg, ivCheck;
        ivImg = holder.getView(R.id.ivItemImg);
        ivCheck = holder.getView(R.id.ivItemCheck);

        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoods_kind_icon_picture()))
                .apply(RequestOptions.bitmapTransform(new CircleCrop()).error(com.yxl.commonlibrary.R.mipmap.ic_default_img))
                .into(ivImg);
        if (mDataList.get(position).isCheck()) {
            ivCheck.setVisibility(View.VISIBLE);
        } else {
            ivCheck.setVisibility(View.GONE);
        }
    }
}
