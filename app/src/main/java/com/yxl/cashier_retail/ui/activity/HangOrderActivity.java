package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.view.View;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.MyApplication;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityHangOrderBinding;
import com.yxl.cashier_retail.ui.adapter.HangListAdapter;
import com.yxl.cashier_retail.ui.adapter.HangOrderInfoAdapter;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.ui.bean.HangData;
import com.yxl.cashier_retail.ui.bean.HangListData;
import com.yxl.cashier_retail.ui.bean.MemberData;
import com.yxl.cashier_retail.ui.dialog.MenuDialog;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.utils.PrintReceiptUsbUtils;

import org.litepal.LitePal;
import org.threeten.bp.Instant;
import org.threeten.bp.LocalDateTime;
import org.threeten.bp.YearMonth;
import org.threeten.bp.ZoneId;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Describe:挂单
 * Created by jingang on 2024/5/29
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class HangOrderActivity extends BaseActivity<ActivityHangOrderBinding> implements View.OnClickListener {
    private int type,//0.全部 1.散客挂单 2.会员挂单
            pos,
            pos1;//挂单列表下标
    private String timestamp;//时间戳-作为挂单列表的唯一标识

    //挂单列表
    private List<HangListData> dataList = new ArrayList<>();
    private HangListAdapter mAdapter;

    //挂单详情
    private HangOrderInfoAdapter goodsAdapter;
    private List<GoodsData> goodsList = new ArrayList<>();

    @Override
    protected ActivityHangOrderBinding getViewBinding() {
        return ActivityHangOrderBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivLogo.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.linMainOrder.setOnClickListener(this);
        mBinding.linPreviousOrder.setOnClickListener(this);
        mBinding.linDel.setOnClickListener(this);
        mBinding.linPrint.setOnClickListener(this);
        mBinding.linConfirm.setOnClickListener(this);
        mBinding.tvType0.setOnClickListener(this);
        mBinding.tvType1.setOnClickListener(this);
        mBinding.tvType2.setOnClickListener(this);
        mBinding.tvClear.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected void initData() {
        setNetwork();
        getOrderList();
    }

    @Override
    public void getNetwork() {
        setNetwork();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivLogo:
                //菜单
                MenuDialog.showDialog(this, type -> {
                    //0.收银 1.入库 2.查询 3.统计 4.网单 5.会员 6.补货 7.营销 8.交班 9.设置
                    switch (type) {
                        case 1:
                            goToActivity(InActivity.class);
                            break;
                        case 2:
                            goToActivity(QueryActivity.class);
                            break;
                        case 3:
                            goToActivity(StatisticsActivity.class);
                            break;
                        case 4:
                            goToActivity(OrderActivity.class);
                            break;
                        case 5:
                            goToActivity(MemberActivity.class);
                            break;
                        case 6:
                            goToActivity(MallActivity.class);
                            break;
                        case 7:
                            goToActivity(MarketingActivity.class);
                            break;
                        case 8:
                            goToActivity(ShiftActivity.class);
                            break;
                        case 9:
                            goToActivity(SettingActivity.class);
                            break;
                    }
                    finish();
                });
                break;
            case R.id.tvCashier:
                //收银台
            case R.id.linMainOrder:
                //主单
                onBackPressed();
                break;
            case R.id.linPreviousOrder:
                //上一单
                goToActivity(QueryActivity.class);
                break;
            case R.id.linDel:
                //删除
                if (TextUtils.isEmpty(timestamp)) {
                    return;
                }
                HangData data = dataList.get(pos).getData().get(pos1);
                data.delete();
                dataList.get(pos).getData().remove(pos1);
                mAdapter.notifyItemChanged(pos);
                if (dataList.get(pos).getData().isEmpty()) {
                    dataList.remove(pos);
                    mAdapter.remove(pos);
                    if (dataList.isEmpty()) {
                        mBinding.recyclerView.setVisibility(View.GONE);
                        mBinding.linEmpty.setVisibility(View.VISIBLE);
                        setUI("", 0);
                    } else {
                        pos = 0;
                        if (!dataList.get(pos).getData().isEmpty()) {
                            pos1 = 0;
                            dataList.get(pos).getData().get(pos1).setSelect(true);
                            timestamp = dataList.get(pos).getData().get(pos1).getTimestamp();
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                            setUI(dataList.get(pos).getData().get(pos1).getJsonGoods(), dataList.get(pos).getData().get(pos1).getType());
                        }
                    }
                } else {
                    pos1 = 0;
                    dataList.get(pos).getData().get(pos1).setSelect(true);
                    mAdapter.notifyItemChanged(pos, dataList.get(pos));
                    timestamp = dataList.get(pos).getData().get(pos1).getTimestamp();
                    setUI(dataList.get(pos).getData().get(pos1).getJsonGoods(), dataList.get(pos).getData().get(pos1).getType());
                }
                getTotal();
                break;
            case R.id.linPrint:
                //打印
                if (TextUtils.isEmpty(timestamp)) {
                    return;
                }
                double discount = 0, total = 0;
                MemberData memberData = new Gson().fromJson(dataList.get(pos).getData().get(pos1).getJsonMember(), MemberData.class);
                for (int i = 0; i < goodsList.size(); i++) {
                    double newPrice = goodsList.get(i).getNewPrice(),
                            memberPrice = TextUtils.isEmpty(goodsList.get(i).getGoods_cus_price()) ? 0 : Double.parseDouble(goodsList.get(i).getGoods_cus_price()),
                            salePrice = goodsList.get(i).getGoods_sale_price();
                    if (newPrice > 0) {
                        total = total + newPrice;
                        discount = discount + (salePrice - newPrice) * goodsList.get(i).getCartNum();
                    } else {
                        if (memberData == null) {
                            total = total + salePrice;
                            discount = 0;
                        } else {
                            discount = discount + (salePrice - memberPrice) * goodsList.get(i).getCartNum();
                        }
                    }
                }

                if (!MyApplication.getInstance().isReceiptPrinterConnect) {
                    showToast(1, getRstr(R.string.printer_no_connect));
                    return;
                }
                PrintReceiptUsbUtils.printHang(this, goodsList, discount, total, memberData);
                break;
            case R.id.linConfirm:
                //取单
                if (TextUtils.isEmpty(timestamp)) {
                    return;
                }
                HangData data1 = LitePal.where("timestamp = ?", timestamp).findFirst(HangData.class);
                if (data1 == null) {
                    return;
                }
                setResult(Constants.HANG_ORDER, new Intent()
                        .putExtra("hangData", data1)
                );
                data1.delete();
                finish();
                break;
            case R.id.tvType0:
                //全部
                if (type != 0) {
                    type = 0;
                    mBinding.tvType0.setBackgroundResource(R.drawable.shape_green_left_5);
                    mBinding.tvType0.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvType0.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.tvType1.setBackgroundColor(getResources().getColor(R.color.white));
                    mBinding.tvType1.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType1.setTypeface(Typeface.DEFAULT);
                    mBinding.tvType2.setBackgroundResource(R.drawable.shape_white_right_5);
                    mBinding.tvType2.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType2.setTypeface(Typeface.DEFAULT);
                    getOrderList();
                }
                break;
            case R.id.tvType1:
                //散客挂单
                if (type != 1) {
                    type = 1;
                    mBinding.tvType0.setBackgroundResource(R.drawable.shape_white_left_5);
                    mBinding.tvType0.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType0.setTypeface(Typeface.DEFAULT);
                    mBinding.tvType1.setBackgroundColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                    mBinding.tvType1.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvType1.setTypeface(Typeface.DEFAULT_BOLD);
                    mBinding.tvType2.setBackgroundResource(R.drawable.shape_white_right_5);
                    mBinding.tvType2.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType2.setTypeface(Typeface.DEFAULT);
                    getOrderList();
                }
                break;
            case R.id.tvType2:
                //会员挂单
                if (type != 2) {
                    type = 2;
                    mBinding.tvType0.setBackgroundResource(R.drawable.shape_white_left_5);
                    mBinding.tvType0.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType0.setTypeface(Typeface.DEFAULT);
                    mBinding.tvType1.setBackgroundColor(getResources().getColor(R.color.white));
                    mBinding.tvType1.setTextColor(getResources().getColor(R.color.black));
                    mBinding.tvType1.setTypeface(Typeface.DEFAULT);
                    mBinding.tvType2.setBackgroundResource(R.drawable.shape_green_right_5);
                    mBinding.tvType2.setTextColor(getResources().getColor(R.color.white));
                    mBinding.tvType2.setTypeface(Typeface.DEFAULT_BOLD);

                    getOrderList();
                }
                break;
            case R.id.tvClear:
                //清空挂单
                LitePal.deleteAll(HangData.class);
                dataList.clear();
                mAdapter.clear();
                setUI("", 0);
                getTotal();
                break;
        }
    }

    /**
     * 网络监听
     */
    private void setNetwork() {
        if (MyApplication.mNetwork != null) {
            if (MyApplication.mNetwork.isConnect()) {
                switch (MyApplication.mNetwork.getConnectType()) {
                    case 1:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network001);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 2:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network002);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 3:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network003);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    default:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                        mBinding.tvNetwork.setText(getRstr(R.string.no_network));
                        break;
                }
            } else {
                mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                mBinding.tvNetwork.setText(getRstr(R.string.no_network));
            }
        } else {
            mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
            mBinding.tvNetwork.setText(getRstr(R.string.no_network));
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //挂单列表
        mAdapter = new HangListAdapter(this);
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new HangListAdapter.MyListener() {
            @Override
            public void onItemClick(int position, int position1) {
                //详情
                pos = position;
                pos1 = position1;
                if (!dataList.get(pos).getData().get(pos1).isSelect()) {
                    for (int i = 0; i < dataList.size(); i++) {
                        for (int j = 0; j < dataList.get(i).getData().size(); j++) {
                            if (dataList.get(i).getData().get(j).isSelect()) {
                                dataList.get(i).getData().get(j).setSelect(false);
                                mAdapter.notifyItemChanged(i);
                            }
                        }
                    }
                    dataList.get(pos).getData().get(pos1).setSelect(true);
                    mAdapter.notifyItemChanged(pos);
                    timestamp = dataList.get(pos).getData().get(pos1).getTimestamp();
                    setUI(dataList.get(pos).getData().get(pos1).getJsonGoods(), dataList.get(pos).getData().get(pos1).getType());
                }
            }

            @Override
            public void onDelClick(int position, int position1) {
                //删除
                HangData data = dataList.get(position).getData().get(position1);
                data.delete();
                dataList.get(position).getData().remove(position1);
                mAdapter.notifyItemChanged(position);
                if (dataList.get(position).getData().isEmpty()) {
                    dataList.remove(position);
                    mAdapter.remove(position);
                    if (dataList.isEmpty()) {
                        mBinding.recyclerView.setVisibility(View.GONE);
                        mBinding.linEmpty.setVisibility(View.VISIBLE);
                        setUI("", 0);
                    } else {
                        pos = 0;
                        if (!dataList.get(pos).getData().isEmpty()) {
                            pos1 = 0;
                            dataList.get(pos).getData().get(pos1).setSelect(true);
                            timestamp = dataList.get(pos).getData().get(pos1).getTimestamp();
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                            setUI(dataList.get(pos).getData().get(pos1).getJsonGoods(), dataList.get(pos).getData().get(pos1).getType());
                        }
                    }
                } else {
                    if (pos == position && pos1 == position1) {
                        pos1 = 0;
                        dataList.get(pos).getData().get(pos1).setSelect(true);
                        mAdapter.notifyItemChanged(pos, dataList.get(pos));
                        timestamp = dataList.get(pos).getData().get(pos1).getTimestamp();
                        setUI(dataList.get(pos).getData().get(pos1).getJsonGoods(), dataList.get(pos).getData().get(pos1).getType());
                    }
                }
                getTotal();
            }
        });

        mBinding.smartRefreshLayout.setOnRefreshListener(refreshLayout -> getOrderList());
        mBinding.smartRefreshLayout.setEnableLoadMore(false);

        //挂单详情
        goodsAdapter = new HangOrderInfoAdapter(this);
        mBinding.rvInfo.setAdapter(goodsAdapter);
    }

    /**
     * 更新UI-商品列表
     *
     * @param jsonGoods
     */
    private void setUI(String jsonGoods, int type) {
        goodsList.clear();
        if (TextUtils.isEmpty(jsonGoods)) {
            goodsAdapter.clear();
            timestamp = "";
            return;
        }
        List<GoodsData> list = JSON.parseArray(jsonGoods, GoodsData.class);
        if (list != null) {
            goodsList.addAll(list);
        }
        goodsAdapter.setType(type);
        goodsAdapter.setDataList(goodsList);

    }

    /**
     * 计算总数、总金额
     */
    private void getTotal() {
        int count = 0;
        double total = 0;
        for (int i = 0; i < dataList.size(); i++) {
            count = count + dataList.get(i).getData().size();
            for (int j = 0; j < dataList.get(i).getData().size(); j++) {
                total = total + dataList.get(i).getData().get(j).getTotal();
            }
        }
        mBinding.tvCount.setText(String.valueOf(count));
        mBinding.tvTotal.setText(DFUtils.getNum2(total));
    }

    /**
     * 挂单列表
     */
    private void getOrderList() {
        mBinding.smartRefreshLayout.finishRefresh();
        List<HangData> list;
        if (type == 0) {
            list = LitePal.findAll(HangData.class);
        } else {
            list = LitePal.where("type = ?", String.valueOf(type)).find(HangData.class);
        }
        dataList.clear();
        if (list != null) {
            Collections.reverse(list);
            List list1 = groupEventsByMonth(list);
            if (list1 != null) {
                dataList.addAll(list1);
                mAdapter.setDataList(dataList);
            }
        }
        if (dataList.isEmpty()) {
            mBinding.recyclerView.setVisibility(View.GONE);
            mBinding.linEmpty.setVisibility(View.VISIBLE);
        } else {
            pos = 0;
            if (!dataList.get(pos).getData().isEmpty()) {
                pos1 = 0;
                dataList.get(pos).getData().get(pos1).setSelect(true);
                timestamp = dataList.get(pos).getData().get(pos1).getTimestamp();
                mBinding.recyclerView.setVisibility(View.VISIBLE);
                mBinding.linEmpty.setVisibility(View.GONE);
                mAdapter.setDataList(dataList);
                setUI(dataList.get(pos).getData().get(pos1).getJsonGoods(), dataList.get(pos).getData().get(pos1).getType());
            }
        }
        getTotal();
    }

    /**
     * 以月筛选数据
     *
     * @param events
     * @return
     */
    @SuppressLint("NewApi")
    public static List<HangListData> groupEventsByMonth(List<HangData> events) {
        return events.stream()
                .collect(Collectors.groupingBy(event ->
                        YearMonth.from(LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(event.getTimestamp())), ZoneId.systemDefault()))))
                .entrySet().stream()
                .map(entry -> new HangListData(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
    }
}
