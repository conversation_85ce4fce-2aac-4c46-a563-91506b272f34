package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogRestockGoodsSelectBinding;
import com.yxl.cashier_retail.ui.bean.GoodsInfoData;
import com.yxl.cashier_retail.ui.bean.GoodsListData;
import com.yxl.cashier_retail.ui.bean.RestockGoodsSaleData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.view.NumberKeyBoardView;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Describe:dialog（自采补货-选择商品）
 * Created by jingang on 2024/6/5
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n", "StaticFieldLeak"})
public class RestockGoodsSelectDialog extends BaseDialog<DialogRestockGoodsSelectBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private static GoodsInfoData data;
    private static int type,//焦点位置：0.价格 1.数量
            id,//补货计划id
            purchaseType;//采购类型:1-购销2自采(本地)
    private static double count,//数量
            price,//价格
            inPrice;//进货价（本地）
    private static String barcode,
            supplierUnique;//供货商编号

    public static void showDialog(Activity activity, int id, GoodsInfoData data, String barcode, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        RestockGoodsSelectDialog.listener = listener;
        RestockGoodsSelectDialog.id = id;
        RestockGoodsSelectDialog.data = data;
        RestockGoodsSelectDialog.barcode = barcode;
        RestockGoodsSelectDialog dialog = new RestockGoodsSelectDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public RestockGoodsSelectDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        ((AnimationDrawable) mBinding.ivDialogInPrice.getDrawable()).start();
        ((AnimationDrawable) mBinding.ivDialogInPriceHints.getDrawable()).start();
        ((AnimationDrawable) mBinding.ivDialogCount.getDrawable()).start();
        ((AnimationDrawable) mBinding.ivDialogCountHint.getDrawable()).start();
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.tvDialogSpecs.setOnClickListener(this);
        mBinding.tvDialogChange.setOnClickListener(this);
        mBinding.ivDialogSubPrice.setOnClickListener(this);
        mBinding.ivDialogAddPrice.setOnClickListener(this);
        mBinding.relDialogInPrice.setOnClickListener(this);
        mBinding.ivDialogSubCount.setOnClickListener(this);
        mBinding.relDialogCount.setOnClickListener(this);
        mBinding.ivDialogAddCount.setOnClickListener(this);
        mBinding.numberKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var) {
                if (type == 0) {
                    //价格
                    mBinding.tvDialogInPrice.setText(var);
                    if (TextUtils.isEmpty(var)) {
                        mBinding.linDialogInPrice.setVisibility(View.GONE);
                        mBinding.linDialogInPriceHint.setVisibility(View.VISIBLE);
                        mBinding.ivDialogInPriceHints.setVisibility(View.VISIBLE);
                        price = 0;
                    } else {
                        mBinding.linDialogInPrice.setVisibility(View.VISIBLE);
                        mBinding.linDialogInPriceHint.setVisibility(View.GONE);
                        mBinding.ivDialogInPrice.setVisibility(View.VISIBLE);
                        price = Double.parseDouble(var);
                    }
                } else {
                    //数量
                    mBinding.tvDialogCount.setText(var);
                    if (TextUtils.isEmpty(var)) {
                        mBinding.linDialogCount.setVisibility(View.GONE);
                        mBinding.linDialogCountHint.setVisibility(View.VISIBLE);
                        mBinding.ivDialogCountHint.setVisibility(View.VISIBLE);
                        count = 0;
                    } else {
                        mBinding.linDialogCount.setVisibility(View.VISIBLE);
                        mBinding.linDialogCountHint.setVisibility(View.GONE);
                        mBinding.ivDialogCount.setVisibility(View.VISIBLE);
                        count = Double.parseDouble(var);
                    }
                }
            }

            @Override
            public void onConfirm() {
                if (purchaseType == 2) {
                    if (price == 0) {
                        showToast(1, getRstr(R.string.input_in_price));
                        return;
                    }
                }
                if (count == 0) {
                    showToast(1, getRstr(R.string.input_restock_count));
                    return;
                }
                if (TextUtils.isEmpty(supplierUnique)) {
                    showToast(1, getRstr(R.string.select_supplier));
                    return;
                }
                postGoodsAdd();
            }
        });

        if (data == null) {
            return;
        }
        if (data.getListDetail() == null) {
            return;
        }
        if (data.getListDetail().size() < 1) {
            return;
        }
        if (data.getListDetail().size() > 1) {
            mBinding.tvDialogSpecs.setVisibility(View.VISIBLE);
        }
        for (int i = 0; i < data.getListDetail().size(); i++) {
            if (!TextUtils.isEmpty(barcode) && !TextUtils.isEmpty(data.getListDetail().get(i).getGoodsBarcode())) {
                if (barcode.equals(data.getListDetail().get(i).getGoodsBarcode())) {
                    data.getListDetail().get(i).setCheck(true);
                    setUI(data.getListDetail().get(i));
                }
            }
        }
    }

    @Override
    protected DialogRestockGoodsSelectBinding getViewBinding() {
        return DialogRestockGoodsSelectBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogSpecs:
                //选择规格
                RestockGoodsSpecsDialog.showDialog(mActivity, data, data -> {
                    RestockGoodsSelectDialog.data = data;
                    if (data == null) {
                        return;
                    }
                    if (data.getListDetail() == null) {
                        return;
                    }
                    for (int i = 0; i < data.getListDetail().size(); i++) {
                        if (data.getListDetail().get(i).isCheck()) {
                            setUI(data.getListDetail().get(i));
                        }
                    }
                });
                break;
            case R.id.tvDialogChange:
                //更改供货商
                SupplierDialog.showDialog(mActivity, null,0, data -> {
                    postRestockSupplierEdit(data.getSupplierUnique(), data.getSupplierName(), data.getPurchaseType());
                });
//                WebSupplierDialog.showDialog(mActivity, null,data -> {
//                    postRestockSupplierEdit(data.getSupplier_unique(), data.getSupplier_name(), 0);
//                });
                break;
            case R.id.relDialogInPrice:
                //焦点选择进价
                if (type != 0) {
                    type = 0;
                    mBinding.ivDialogCount.setVisibility(View.GONE);
                    mBinding.ivDialogCountHint.setVisibility(View.GONE);
                    if (price > 0) {
                        mBinding.ivDialogInPrice.setVisibility(View.VISIBLE);
                        mBinding.ivDialogInPriceHints.setVisibility(View.GONE);
                        mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(price));
                    } else {
                        mBinding.ivDialogInPrice.setVisibility(View.GONE);
                        mBinding.ivDialogInPriceHints.setVisibility(View.VISIBLE);
                        mBinding.numberKeyBoardView.setResultStr("");
                    }
                }
                break;
            case R.id.ivDialogSubPrice:
                //减（进价）
                if (type != 0) {
                    type = 0;
                    mBinding.ivDialogCount.setVisibility(View.GONE);
                    mBinding.ivDialogCountHint.setVisibility(View.GONE);
                    mBinding.ivDialogInPrice.setVisibility(View.VISIBLE);
                    mBinding.ivDialogInPriceHints.setVisibility(View.VISIBLE);
                }
                if (price < 1) {
                    return;
                }
                price--;
                mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(price));
                break;
            case R.id.ivDialogAddPrice:
                //加（进价）
                if (type != 0) {
                    type = 0;
                    mBinding.ivDialogCount.setVisibility(View.GONE);
                    mBinding.ivDialogCountHint.setVisibility(View.GONE);
                    mBinding.ivDialogInPrice.setVisibility(View.VISIBLE);
                    mBinding.ivDialogInPriceHints.setVisibility(View.VISIBLE);
                }
                price++;
                mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(price));
                break;
            case R.id.relDialogCount:
                //焦点选择数量
                if (type != 1) {
                    type = 1;
                    mBinding.ivDialogInPrice.setVisibility(View.GONE);
                    mBinding.ivDialogInPriceHints.setVisibility(View.GONE);
                    if (count > 0) {
                        mBinding.ivDialogCount.setVisibility(View.VISIBLE);
                        mBinding.ivDialogCountHint.setVisibility(View.GONE);
                        mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(count));
                    } else {
                        mBinding.ivDialogCount.setVisibility(View.GONE);
                        mBinding.ivDialogCountHint.setVisibility(View.VISIBLE);
                        mBinding.numberKeyBoardView.setResultStr("");
                    }
                }
                break;
            case R.id.ivDialogSubCount:
                //减（数量）
                if (type != 1) {
                    type = 1;
                    mBinding.ivDialogInPrice.setVisibility(View.GONE);
                    mBinding.ivDialogInPriceHints.setVisibility(View.GONE);
                    mBinding.ivDialogCount.setVisibility(View.VISIBLE);
                    mBinding.ivDialogCountHint.setVisibility(View.VISIBLE);
                }
                if (count < 1) {
                    return;
                }
                count--;
                mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(count));
                break;
            case R.id.ivDialogAddCount:
                //加（数量）
                if (type != 1) {
                    type = 1;
                    mBinding.ivDialogInPrice.setVisibility(View.GONE);
                    mBinding.ivDialogInPriceHints.setVisibility(View.GONE);
                    mBinding.ivDialogCount.setVisibility(View.VISIBLE);
                    mBinding.ivDialogCountHint.setVisibility(View.VISIBLE);
                }
                count++;
                mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(count));
                break;
        }
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(GoodsListData data) {
        barcode = data.getGoodsBarcode();
        inPrice = data.getGoodsInPrice();
        Glide.with(getContext())
                .load(StringUtils.handledImgUrl(data.getGoodsPicturepath()))
                .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img))
                .into(mBinding.ivDialogImg);
        mBinding.tvDialogName.setText(data.getGoodsName());
        mBinding.tvDialogBarcode.setText(data.getGoodsBarcode());
        mBinding.tvDialogPrice.setText(DFUtils.getNum2(data.getGoodsInPrice()));
        if (TextUtils.isEmpty(data.getGoodsUnit())) {
            mBinding.tvDialogUnit.setText(getRstr(R.string.yuan));
        } else {
            mBinding.tvDialogUnit.setText(getRstr(R.string.yuan) + "/" + data.getGoodsUnit());
        }
        getGoodsSale();
    }

    /**
     * 更新UI-商品销量信息
     *
     * @param data
     */
    private void setUISale(RestockGoodsSaleData data) {
        if (data == null) {
            return;
        }
        supplierUnique = data.getSupplierUnique();
        purchaseType = data.getPurchaseType();
        String supplierName;
        if (TextUtils.isEmpty(data.getSupplierName())) {
            supplierName = getRstr(R.string.supplier_colon);
        } else {
            supplierName = getRstr(R.string.supplier_colon) + data.getSupplierName();
        }
        //采购类型:1-购销2自采(本地)
        if (purchaseType == 2) {
            type = 0;
            mBinding.tvDialogSupplier.setText(supplierName + "(" + getRstr(R.string.local) + ")");
            mBinding.ivDialogSupplierImg.setVisibility(View.GONE);
            mBinding.linDialogPrice.setVisibility(View.VISIBLE);
            mBinding.ivDialogCount.setVisibility(View.GONE);
            mBinding.ivDialogCountHint.setVisibility(View.GONE);
        } else {
            type = 1;
            mBinding.tvDialogSupplier.setText(supplierName);
            if (TextUtils.isEmpty(data.getSupplierName())) {
                mBinding.ivDialogSupplierImg.setVisibility(View.GONE);
            } else {
                mBinding.ivDialogSupplierImg.setVisibility(View.VISIBLE);
            }
            mBinding.linDialogPrice.setVisibility(View.GONE);
        }
        String unit = data.getGoodsUnit();
        String lastCreateTime = data.getLastCreateTime();
        if (TextUtils.isEmpty(lastCreateTime)) {
            lastCreateTime = "";
        } else {
            lastCreateTime = data.getLastCreateTime() + " | ";
        }
        if (TextUtils.isEmpty(unit)) {
            mBinding.tvDialogCount3.setText(getRstr(R.string.sales_three_colon) + DFUtils.getNum2(data.getCount3()));
            mBinding.tvDialogCount7.setText(getRstr(R.string.sales_seven_colon) + DFUtils.getNum2(data.getCount7()));
            mBinding.tvDialogStock.setText(getRstr(R.string.stock_sale) + DFUtils.getNum2(data.getGoodsCount()));
            mBinding.tvDialogLast.setText(getRstr(R.string.buy_info_last) + lastCreateTime
                    + DFUtils.getNum2(data.getLastGoodsInPrice()) + " | "
                    + DFUtils.getNum2(data.getLastGoodsCount()));
            mBinding.tvDialogSuggest.setText(getRstr(R.string.buy_count_suggest) + DFUtils.getNum2(data.getBestCount()));
        } else {
            mBinding.tvDialogCount3.setText(getRstr(R.string.sales_three_colon) + DFUtils.getNum2(data.getCount3()) + unit);
            mBinding.tvDialogCount7.setText(getRstr(R.string.sales_seven_colon) + DFUtils.getNum2(data.getCount7()) + unit);
            mBinding.tvDialogStock.setText(getRstr(R.string.stock_sale) + DFUtils.getNum2(data.getGoodsCount()) + unit);
            mBinding.tvDialogLast.setText(getRstr(R.string.buy_info_last) + lastCreateTime
                    + DFUtils.getNum2(data.getLastGoodsInPrice()) + "/" + unit + " | "
                    + DFUtils.getNum2(data.getLastGoodsCount()) + unit);
            mBinding.tvDialogSuggest.setText(getRstr(R.string.buy_count_suggest) + DFUtils.getNum2(data.getBestCount()) + unit);
        }
        //是否低于安全库存
        if (data.getGoodsCount() < data.getOutStockCount()) {
            mBinding.tvDialogSafe.setVisibility(View.VISIBLE);
        } else {
            mBinding.tvDialogSafe.setVisibility(View.GONE);
        }
        count = data.getBestCount();
        if (count > 0) {
            mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(count));
        } else {
            mBinding.numberKeyBoardView.setResultStr("");
        }
    }

    /**
     * 商品销量信息
     */
    private void getGoodsSale() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("goodsBarcode", barcode);
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getRestockSale(),
                map,
                RestockGoodsSaleData.class,
                new RequestListener<RestockGoodsSaleData>() {
                    @Override
                    public void success(RestockGoodsSaleData data) {
                        hideDialog();
                        setUISale(data);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        dismiss();
                    }
                });
    }

    /**
     * 更换商品对应的供货商信息
     *
     * @param unique
     * @param name
     */
    private void postRestockSupplierEdit(String unique, String name, int type) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("supplierUnique", unique);
        map.put("goodsBarcode", barcode);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getRestockSupplierEdit(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        supplierUnique = unique;
                        purchaseType = type;
                        String str;
                        if (TextUtils.isEmpty(name)) {
                            str = getRstr(R.string.supplier_colon);
                        } else {
                            str = getRstr(R.string.supplier_colon) + name;
                        }
                        if (purchaseType == 2) {
                            mBinding.tvDialogSupplier.setText(str + "(" + getRstr(R.string.local) + ")");
                            mBinding.ivDialogSupplierImg.setVisibility(View.GONE);
                            mBinding.linDialogPrice.setVisibility(View.VISIBLE);
                            mBinding.ivDialogCount.setVisibility(View.GONE);
                            mBinding.ivDialogCountHint.setVisibility(View.GONE);
                        } else {
                            mBinding.tvDialogSupplier.setText(str);
                            if (TextUtils.isEmpty(data.getSupplierName())) {
                                mBinding.ivDialogSupplierImg.setVisibility(View.GONE);
                            } else {
                                mBinding.ivDialogSupplierImg.setVisibility(View.VISIBLE);
                            }
                            mBinding.linDialogPrice.setVisibility(View.GONE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 补货单-添加商品
     */
    private void postGoodsAdd() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("restockPlanId", id);
        map.put("supplierUnique", supplierUnique);
        map.put("goodsBarcode", barcode);
        map.put("goodsCount", count);
        map.put("createUser", getStaffUnique());
        if (purchaseType == 2) {
            map.put("goodsInPrice", price);
        } else {
            map.put("goodsInPrice", inPrice);
        }
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getRestockGoodsAdd(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        if (listener != null) {
                            listener.onConfirm();
                            dismiss();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm();
    }
}
