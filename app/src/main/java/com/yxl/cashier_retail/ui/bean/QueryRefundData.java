package com.yxl.cashier_retail.ui.bean;

import java.util.List;

/**
 * Describe: 查询-查退款（实体类）
 * Created by jingang on 2025/2/24
 */
public class QueryRefundData {
    /**
     * list : [{"staffId":"14612","staffName":"东方购物商家","cusUnique":0,"cusName":null,"phone":null,"returnTime":1740216923000,"returnSalistId":"3","returnAmount":62,"returnMethod":"1","returnSum":2,"saleListUnique":"20250220082411858"},{"staffId":"14612","staffName":"东方购物商家","cusUnique":0,"cusName":null,"phone":null,"returnTime":1740210673000,"returnSalistId":"2","returnAmount":1,"returnMethod":"1","returnSum":1,"saleListUnique":"20250221162533457"}]
     * totalAmount : 63.0
     */

    private double totalAmount;//退款总额
    private List<ListBean> list;

    public double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public List<ListBean> getList() {
        return list;
    }

    public void setList(List<ListBean> list) {
        this.list = list;
    }

    public static class ListBean {
        /**
         * staffId : 14612
         * staffName : 东方购物商家
         * cusUnique : 0
         * cusName : null
         * phone : null
         * returnTime : 1740216923000
         * returnSalistId : 3
         * returnAmount : 62.0
         * returnMethod : 1
         * returnSum : 2.0
         * saleListUnique : 20250220082411858
         */

        private boolean select;
        private String staffId;
        private String staffName;
        private String cusUnique;
        private String cusName;
        private String phone;
        private long returnTime;//退款时间
        private String returnSalistId;
        private double returnAmount;//退款金额
        private String returnMethod;//1、现金；2、支付宝；3、微信；4、银行卡；5、储值卡  6 易通（原路退回）
        private double returnSum;//退款数量
        private String saleListUnique;//订单编号
        private String returnSaleListUnique;//退款订单编号

        public boolean isSelect() {
            return select;
        }

        public void setSelect(boolean select) {
            this.select = select;
        }

        public String getStaffId() {
            return staffId;
        }

        public void setStaffId(String staffId) {
            this.staffId = staffId;
        }

        public String getStaffName() {
            return staffName;
        }

        public void setStaffName(String staffName) {
            this.staffName = staffName;
        }

        public String getCusUnique() {
            return cusUnique;
        }

        public void setCusUnique(String cusUnique) {
            this.cusUnique = cusUnique;
        }

        public String getCusName() {
            return cusName;
        }

        public void setCusName(String cusName) {
            this.cusName = cusName;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public long getReturnTime() {
            return returnTime;
        }

        public void setReturnTime(long returnTime) {
            this.returnTime = returnTime;
        }

        public String getReturnSalistId() {
            return returnSalistId;
        }

        public void setReturnSalistId(String returnSalistId) {
            this.returnSalistId = returnSalistId;
        }

        public double getReturnAmount() {
            return returnAmount;
        }

        public void setReturnAmount(double returnAmount) {
            this.returnAmount = returnAmount;
        }

        public String getReturnMethod() {
            return returnMethod;
        }

        public void setReturnMethod(String returnMethod) {
            this.returnMethod = returnMethod;
        }

        public double getReturnSum() {
            return returnSum;
        }

        public void setReturnSum(double returnSum) {
            this.returnSum = returnSum;
        }

        public String getSaleListUnique() {
            return saleListUnique;
        }

        public void setSaleListUnique(String saleListUnique) {
            this.saleListUnique = saleListUnique;
        }

        public String getReturnSaleListUnique() {
            return returnSaleListUnique;
        }

        public void setReturnSaleListUnique(String returnSaleListUnique) {
            this.returnSaleListUnique = returnSaleListUnique;
        }
    }
}
