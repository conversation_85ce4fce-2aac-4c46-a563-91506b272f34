package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.PurchaseListData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:购销单（适配器）
 * Created by jingang on 2024/6/12
 */
public class PurchaseAdapter extends BaseAdapter<PurchaseListData> {
    private final int type;//0.供货商详情 1.入库

    public PurchaseAdapter(Context context, int type) {
        super(context);
        this.type = type;
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_purchase;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvTime, tvNo, tvSupplier, tvMobile, tvCount, tvTotal, tvRemarks, tvStatus;
        tvTime = holder.getView(R.id.tvItemTime);
        tvNo = holder.getView(R.id.tvItemNo);
        tvSupplier = holder.getView(R.id.tvItemSupplier);
        tvMobile = holder.getView(R.id.tvItemMobile);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvRemarks = holder.getView(R.id.tvItemRemarks);
        tvStatus = holder.getView(R.id.tvItemStatus);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
        }
        if (type == 0) {
            tvSupplier.setVisibility(View.GONE);
            tvMobile.setVisibility(View.GONE);
            tvRemarks.setVisibility(View.VISIBLE);
            tvRemarks.setText(TextUtils.isEmpty(mDataList.get(position).getRemark()) ? "-" : mDataList.get(position).getRemark());
        } else {
            tvSupplier.setVisibility(View.VISIBLE);
            tvMobile.setVisibility(View.VISIBLE);
            tvRemarks.setVisibility(View.GONE);
            tvSupplier.setText(TextUtils.isEmpty(mDataList.get(position).getSupplierName()) ? "-" : mDataList.get(position).getSupplierName());
            tvMobile.setText(TextUtils.isEmpty(mDataList.get(position).getSupplierPhone()) ? "-" : mDataList.get(position).getSupplierPhone());
        }

        tvTime.setText(TextUtils.isEmpty(mDataList.get(position).getCreateTime()) ? "-" : mDataList.get(position).getCreateTime());
        tvNo.setText(TextUtils.isEmpty(mDataList.get(position).getBillNo()) ? "-" : mDataList.get(position).getBillNo());
        tvCount.setText(String.valueOf(mDataList.get(position).getGoodsCategory()));
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getTotalPrice()));
        //1-已提交(待收货)，2-已收货(待付款)，3-待确认，4-已完成，5-异常，6-作废
        switch (mDataList.get(position).getStatus()) {
            case 1:
                tvStatus.setText(getRstr(R.string.order_status9));
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
                break;
            case 2:
                tvStatus.setText(getRstr(R.string.order_status7));
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.red));
                break;
            case 3:
                tvStatus.setText(getRstr(R.string.order_status10));
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.orange));
                break;
            case 4:
                tvStatus.setText(getRstr(R.string.order_status6));
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.black));
                break;
            case 5:
                tvStatus.setText(getRstr(R.string.order_status11));
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
                break;
            default:
                tvStatus.setText("");
                break;
        }
    }
}
