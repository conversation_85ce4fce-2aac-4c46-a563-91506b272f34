package com.yxl.cashier_retail.ui.presenter;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;

import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.ui.bean.PurchaseInfoData;
import com.yxl.cashier_retail.ui.bean.PurchaseListData;
import com.yxl.cashier_retail.ui.contract.GouXContract;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:入库-购销单
 * Created by jingang on 2024/5/24
 */
public class GouXPresenter implements GouXContract.Presenter {
    private Context mContext;
    private GouXContract.View mView;

    public GouXPresenter(Context mContext) {
        this.mContext = mContext;
    }

    @Override
    public void attachView(@NonNull GouXContract.View view) {
        this.mView = view;
    }

    @Override
    public void detachView() {
        this.mView = null;
    }

    /**
     * 购销单列表
     *
     * @param status
     * @param page
     */
    @Override
    public void getGouXOrderList(int status, int page) {
    }

    /**
     * 购销单详情
     *
     * @param id
     */
    @Override
    public void getGouXOrderInfo(String id) {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", BaseApplication.getInstance().getShopUnique());
        params.put("id", id);
        RXHttpUtil.requestByBodyPostAsResponse((LifecycleOwner) mContext,
                ZURL.getGouXOrderInfo(),
                params,
                PurchaseInfoData.class,
                new RequestListener<PurchaseInfoData>() {
                    @Override
                    public void success(PurchaseInfoData data) {
                        if (mView != null) {
                            mView.onInfoSuccess(data);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        if (mView != null) {
                            mView.onInfoError(msg);
                        }
                    }
                });
    }
}
