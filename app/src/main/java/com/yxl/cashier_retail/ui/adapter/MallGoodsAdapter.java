package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.MallGoodsData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.utils.StringUtils;

/**
 * Describe:商城-商品（适配器）
 * Created by jingang on 2024/6/3
 */
public class MallGoodsAdapter extends BaseAdapter<MallGoodsData> {

    public MallGoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_mall_goods;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvName, tvPrice, tvCount, tvSupplier, tvStartOrder;
        tvName = holder.getView(R.id.tvItemName);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvCount = holder.getView(R.id.tvItemCount);
        tvSupplier = holder.getView(R.id.tvItemSupplier);
        tvStartOrder = holder.getView(R.id.tvItemStartOrder);

        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoods_img()))
                .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img))
                .into(ivImg);
        tvName.setText(mDataList.get(position).getGoods_name());
        tvPrice.setText("¥ " + DFUtils.getNum2(mDataList.get(position).getOnline_price() - mDataList.get(position).getGold_deduct()));
        tvCount.setText("已售" + "不知道" + "件");
        tvSupplier.setText("供货商：" + mDataList.get(position).getCompany_name());
        tvStartOrder.setText("起订量：" + mDataList.get(position).getStart_order());
    }
}
