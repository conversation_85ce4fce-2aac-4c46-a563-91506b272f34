package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogPaymentCombinationBinding;
import com.yxl.cashier_retail.ui.bean.MemberData;
import com.yxl.cashier_retail.ui.bean.SaleListUniqueData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.view.NumberKeyBoardView;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Describe:dialog（组合收款）
 * Created by jingang on 2024/7/8
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class PaymentCombinationDialog extends BaseDialog<DialogPaymentCombinationBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private int type;//0.现金 1.扫码
    private static MemberData memberData;
    private static double total,//应收金额
            cashTotal,//现金
            jinqTotal;//金圈

    public static void showDialog(Activity activity, MemberData memberData, double total, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        PaymentCombinationDialog.mActivity = activity;
        PaymentCombinationDialog.listener = listener;
        PaymentCombinationDialog.memberData = memberData;
        PaymentCombinationDialog.total = total;
        PaymentCombinationDialog dialog = new PaymentCombinationDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public PaymentCombinationDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        ((AnimationDrawable) mBinding.ivDialogCursorCash.getDrawable()).start();
        ((AnimationDrawable) mBinding.ivDialogCursorJinQ.getDrawable()).start();
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.linDialogCash.setOnClickListener(this);
        mBinding.linDialogJinQ.setOnClickListener(this);
        mBinding.ivDialogOpen0.setOnClickListener(this);
        mBinding.ivDialogOpen1.setOnClickListener(this);
        mBinding.numberKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var) {
                if (type == 0) {
                    //现金
                    mBinding.tvDialogCash.setText(var);
                    if (TextUtils.isEmpty(var)) {
                        mBinding.tvDialogCash.setVisibility(View.GONE);
                        mBinding.tvDialogCashHint.setVisibility(View.VISIBLE);
                        cashTotal = 0;
                    } else {
                        mBinding.tvDialogCash.setVisibility(View.VISIBLE);
                        mBinding.tvDialogCashHint.setVisibility(View.GONE);
                        double money = Double.parseDouble(var);
                        if (money > total) {
                            cashTotal = total;
                            mBinding.tvDialogCash.setText(DFUtils.getNum4(cashTotal));
                            mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(cashTotal));
                        } else {
                            cashTotal = money;
                        }
                    }
                    //计算扫码收款金额
                    jinqTotal = DFUtils.getDouble(total - cashTotal);
                    mBinding.tvDialogJinQ.setText(DFUtils.getNum2(jinqTotal));
                    if (jinqTotal > 0) {
                        mBinding.tvDialogJinQ.setVisibility(View.VISIBLE);
                        mBinding.tvDialogJinQHint.setVisibility(View.GONE);
                    } else {
                        mBinding.tvDialogJinQ.setVisibility(View.GONE);
                        mBinding.tvDialogJinQHint.setVisibility(View.VISIBLE);
                    }
                } else {
                    //扫码
                    mBinding.tvDialogJinQ.setText(var);
                    if (TextUtils.isEmpty(var)) {
                        mBinding.tvDialogJinQ.setVisibility(View.GONE);
                        mBinding.tvDialogJinQHint.setVisibility(View.VISIBLE);
                        jinqTotal = 0;
                    } else {
                        mBinding.tvDialogJinQ.setVisibility(View.VISIBLE);
                        mBinding.tvDialogJinQHint.setVisibility(View.GONE);
                        double money = Double.parseDouble(var);
                        if (money > total) {
                            jinqTotal = total;
                            mBinding.tvDialogJinQ.setText(DFUtils.getNum4(jinqTotal));
                            mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(jinqTotal));
                        } else {
                            jinqTotal = money;
                        }
                    }
                    //计算现金收款金额
                    cashTotal = DFUtils.getDouble(total - jinqTotal);
                    mBinding.tvDialogCash.setText(DFUtils.getNum2(cashTotal));
                    if (cashTotal > 0) {
                        mBinding.tvDialogCash.setVisibility(View.VISIBLE);
                        mBinding.tvDialogCashHint.setVisibility(View.GONE);
                    } else {
                        mBinding.tvDialogCash.setVisibility(View.GONE);
                        mBinding.tvDialogCashHint.setVisibility(View.VISIBLE);
                    }
                }
            }

            @Override
            public void onConfirm() {
                //现金收款
                if (cashTotal == total && listener != null) {
                    listener.onPaymentCash(total);
                    dismiss();
                }
            }
        });
        mBinding.etDialogScan.requestFocus();
        //获取焦点不弹出软键盘
        mBinding.etDialogScan.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                InputMethodManager imm = (InputMethodManager) v.getContext().getSystemService(Context.INPUT_METHOD_SERVICE);
                if (imm != null) {
                    imm.hideSoftInputFromWindow(v.getWindowToken(), 0);
                }
            }
        });
        mBinding.etDialogScan.setScanResultListener(result -> {
            Log.e(tag, "扫码结果 = " + result);
            if (jinqTotal == 0) {
                showToast(1, getRstr(R.string.input_money));
                return;
            }
            if (jinqTotal == total) {
                //金圈收款
                listener.onPaymentJinq(total, result);
                dismiss();
            } else {
                //组合收款
                getSaleListUnique(result);
            }

        });
        mBinding.tvDialogTotal.setText(getRstr(R.string.money) + DFUtils.getNum2(total));
        Glide.with(mActivity)
                .asGif()
                .load(R.drawable.cashiering002)
                .into(mBinding.ivDialogImg);
        cashTotal = 0;
        jinqTotal = 0;
    }

    @Override
    protected DialogPaymentCombinationBinding getViewBinding() {
        return DialogPaymentCombinationBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.linDialogCash:
                //现金
                if (type != 0) {
                    type = 0;
                    mBinding.linDialogCash.setBackgroundResource(R.drawable.shape_green_kuang_5);
                    mBinding.ivDialogCursorCash.setVisibility(View.VISIBLE);
                    mBinding.linDialogJinQ.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursorJinQ.setVisibility(View.GONE);
                    if (cashTotal > 0) {
                        mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(cashTotal));
                    } else {
                        mBinding.numberKeyBoardView.setResultStr("");
                    }
                }
                break;
            case R.id.linDialogJinQ:
                //扫码
                if (type != 1) {
                    type = 1;
                    mBinding.linDialogJinQ.setBackgroundResource(R.drawable.shape_green_kuang_5);
                    mBinding.ivDialogCursorJinQ.setVisibility(View.VISIBLE);
                    mBinding.linDialogCash.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursorCash.setVisibility(View.GONE);
                    if (jinqTotal > 0) {
                        mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(jinqTotal));
                    } else {
                        mBinding.numberKeyBoardView.setResultStr("");
                    }
                }
                break;
            case R.id.ivDialogOpen0:
                //显示扫码
                if (jinqTotal == 0) {
                    showToast(1, getRstr(R.string.input_money));
                    return;
                }
                mBinding.linDialogKeyboard.setVisibility(View.GONE);
                mBinding.linDialogImg.setVisibility(View.VISIBLE);
                break;
            case R.id.ivDialogOpen1:
                //显示键盘
                mBinding.linDialogKeyboard.setVisibility(View.VISIBLE);
                mBinding.linDialogImg.setVisibility(View.GONE);
                break;
        }
    }

    /**
     * 创建订单编号
     */
    private void getSaleListUnique(String scanCode) {
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getSaleListUniqueCreate(),
                new HashMap<>(),
                SaleListUniqueData.class,
                new RequestListener<SaleListUniqueData>() {
                    @Override
                    public void success(SaleListUniqueData data) {
                        postPaymentScan(data.getSale_list_unique(), scanCode);
                    }

                    @Override
                    public void onError(String msg) {
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 收银机单独扫码支付
     *
     * @param saleListUnique
     * @param scanCode
     */
    //结算成功：{"status":1,"msg":"20240906174254083:结算成功！","totals":0,"perPageNum":0,"data":20240906174254083,"goodsData":3,"cusData":null,"resultCode":0,"sale_list_unique":null}
    private void postPaymentScan(String saleListUnique, String scanCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("sale_list_unique", saleListUnique);
        if (memberData != null) {
            params.put("cus_unique", memberData.getCusUnique());
        }
        params.put("money", jinqTotal);
        params.put("auth_code", scanCode);
        showDialog();
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getPaymentScanAlone(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        PaymentDoingDialog.showDialog(mActivity, 1, jinqTotal, saleListUnique, new PaymentDoingDialog.MyListener() {
                            @Override
                            public void onPaymentSuccess(double salePoints, double cusPoints) {
                                if (listener != null) {
                                    listener.onPaymentCombination(total, cashTotal, jinqTotal);
                                    dismiss();
                                }
                            }

                            @Override
                            public void onPaymentFail(String msg) {
                                showToast(1, msg);
                            }
                        });
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        /**
         * 金圈收款
         *
         * @param total
         * @param saleListUnique
         */
        void onPaymentJinq(double total, String saleListUnique);

        /**
         * 现金收款
         *
         * @param total
         */
        void onPaymentCash(double total);

        /**
         * 组合收款
         *
         * @param total     总金额
         * @param cashTotal 现金金额
         * @param jinqTotal 金圈收款金额
         */
        void onPaymentCombination(double total, double cashTotal, double jinqTotal);
    }
}
