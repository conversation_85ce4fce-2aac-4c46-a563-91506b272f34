package com.yxl.cashier_retail.ui.fragment;

import android.view.View;

import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.LazyBaseFragment;
import com.yxl.cashier_retail.databinding.LayoutSmartrefreshlayoutBinding;
import com.yxl.cashier_retail.ui.activity.StatisticsActivity;
import com.yxl.cashier_retail.ui.adapter.StatisticsStaffAdapter;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.StatisticsStaffData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:统计-员工统计
 * Created by jingang on 2024/6/17
 */
public class StatisticsStaffFragment extends LazyBaseFragment<LayoutSmartrefreshlayoutBinding> {
    private boolean isHidden, isKeywords;
    private List<StatisticsStaffData> dataList = new ArrayList<>();
    private StatisticsStaffAdapter mAdapter;

    @Override
    protected LayoutSmartrefreshlayoutBinding getViewBinding() {
        return LayoutSmartrefreshlayoutBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        setAdapter();
    }

    @Override
    protected void initData() {

    }

    @Override
    protected void onFragmentFirstVisible() {
        super.onFragmentFirstVisible();
        mBinding.smartRefreshLayout.autoRefresh();
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        this.isHidden = hidden;
        if (!isHidden && isKeywords) {
            mBinding.smartRefreshLayout.autoRefresh();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.STATISTICS_LIST:
                if (!isHidden) {
                    mBinding.smartRefreshLayout.autoRefresh();
                    isKeywords = false;
                } else {
                    isKeywords = true;
                }
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new StatisticsStaffAdapter(getActivity());
        mBinding.recyclerView.setAdapter(mAdapter);
        mBinding.smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            getStatisticsStaff();
        });
        mBinding.smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 员工统计
     */
    private void getStatisticsStaff() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        //1 当天 2 七天 3 半个月 4 一个月
        switch (StatisticsActivity.day) {
            case 1:
                params.put("timeFlg", 2);
                break;
            case 2:
                params.put("timeFlg", 4);
                break;
            default:
                params.put("timeFlg", 1);
                break;
        }
        showDialog();
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getStatisticsStaff(),
                params,
                StatisticsStaffData.class,
                new RequestListListener<StatisticsStaffData>() {
                    @Override
                    public void onResult(List<StatisticsStaffData> list) {
                        hideDialog();
                        mBinding.smartRefreshLayout.finishRefresh();
                        dataList.clear();
                        mAdapter.clear();
                        dataList.addAll(list);
                        mAdapter.addAll(list);
                        if (dataList.isEmpty()) {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.smartRefreshLayout.finishRefresh();
                        if (dataList.isEmpty()) {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        }
                    }
                });
    }
}
