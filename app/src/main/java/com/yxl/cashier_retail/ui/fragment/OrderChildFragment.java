package com.yxl.cashier_retail.ui.fragment;

import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.LazyBaseFragment;
import com.yxl.cashier_retail.databinding.FmOrderChildBinding;
import com.yxl.cashier_retail.ui.adapter.OrderAdapter;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.OrderData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:网单-网单订单
 * Created by jingang on 2024/6/17
 */
public class OrderChildFragment extends LazyBaseFragment<FmOrderChildBinding> {
    private boolean isVisible,//当前fragment是否显示
            isFirst = true;//首次进入
    private final int status;
    private int pos;

    private OrderAdapter mAdapter;
    private final List<OrderData> dataList = new ArrayList<>();

    public OrderChildFragment(int status) {
        this.status = status;
    }

    @Override
    protected FmOrderChildBinding getViewBinding() {
        return FmOrderChildBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        setAdapter();
    }

    @Override
    protected void initData() {
    }

    @Override
    protected void onFragmentFirstVisible() {
        super.onFragmentFirstVisible();
        getOrderList();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        this.isVisible = isVisibleToUser;
        if (isVisible && !isFirst) {
            page = 1;
            getOrderList();
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.ORDER_LIST:
                //订单列表
                if (isVisible) {
                    page = 1;
                    getOrderList();
                }
                break;
            case Constants.ORDER_LIST_UPDATE:
                //订单列表局部刷新
                if (isVisible) {
                    if (dataList.size() > pos) {
                        if (status == -1) {
                            dataList.get(pos).setHandleStateCode(event.getNum());
                            mAdapter.notifyItemChanged(pos);
                        } else {
                            dataList.remove(pos);
                            mAdapter.remove(pos);
                            if (dataList.size() > 0) {
                                pos = 0;
                                dataList.get(pos).setSelect(true);
                                mAdapter.notifyItemChanged(pos);
                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(dataList.get(pos).getSaleListUnique(), Constants.ORDER_INFO));
                            } else {
                                page = 1;
                                getOrderList();
                            }
                        }
                    }
                }
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new OrderAdapter(getActivity());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (!dataList.get(position).isSelect()) {
                for (int i = 0; i < dataList.size(); i++) {
                    if (dataList.get(i).isSelect()) {
                        dataList.get(i).setSelect(false);
                        mAdapter.notifyItemChanged(i);
                    }
                }
                dataList.get(position).setSelect(true);
                mAdapter.notifyItemChanged(position);
                pos = position;
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(dataList.get(position).getSaleListUnique(), Constants.ORDER_INFO));
            }
        });
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getOrderList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getOrderList();
            }
        });
    }

    /**
     * 网单订单查询
     */
    public void getOrderList() {
        if (isFirst) {
            isFirst = false;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("handleState", status);
        params.put("pageIndex", page);
        params.put("pageSize", Constants.limit);
        //搜索关键字
        if (!TextUtils.isEmpty(OrderFragment.keyWords)) {
            params.put("saleListMessage", OrderFragment.keyWords);
        }
        //下单时间
        if (!TextUtils.isEmpty(OrderFragment.startDate) && !TextUtils.isEmpty(OrderFragment.endDate)) {
            params.put("orderStartDate", OrderFragment.startDate);
            params.put("orderEndDate", OrderFragment.endDate);
        }
        //网单类型
        if (OrderFragment.conditionId != 0) {
            params.put("saleType", OrderFragment.conditionId);
        }
//        //付款时间
//        if (SaleFragment.saleListPayment != -1) {
//            params.put("saleListPayment", SaleFragment.saleListPayment);
//        }
        showDialog();
        RXHttpUtil.requestByFormPostAsResponseList(getActivity(),
                ZURL.getOrderList(),
                params,
                OrderData.class,
                new RequestListListener<OrderData>() {
                    @Override
                    public void onResult(List<OrderData> list) {
                        hideDialog();
                        if (page == 1) {
                            mBinding.smartRefreshLayout.finishRefresh();
                            dataList.clear();
                            dataList.addAll(list);
                            if (dataList.size() > 0) {
                                pos = 0;
                                dataList.get(pos).setSelect(true);
                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(dataList.get(pos).getSaleListUnique(), Constants.ORDER_INFO));
                            } else {
                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("", Constants.ORDER_INFO));
                            }
                        } else {
                            mBinding.smartRefreshLayout.finishLoadMore();
                            dataList.addAll(list);
                        }
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
//                            if (mBinding.recyclerView.getItemDecorationCount() == 0) {
//                                mBinding.recyclerView.addItemDecoration(new TitleItemDecoration(getActivity(), dataList));
//                            }
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData("", Constants.ORDER_INFO));
                        if (page == 1) {
                            mBinding.smartRefreshLayout.finishRefresh();
                        } else {
                            mBinding.smartRefreshLayout.finishLoadMore();
                        }
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }
}
