package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.yxl.cashier_retail.MyApplication;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityQueryBinding;
import com.yxl.cashier_retail.ui.dialog.MenuDialog;
import com.yxl.cashier_retail.ui.fragment.QueryBatchFragment;
import com.yxl.cashier_retail.ui.fragment.QueryChuRuFragment;
import com.yxl.cashier_retail.ui.fragment.QueryGoodsFragment;
import com.yxl.cashier_retail.ui.fragment.QueryOrderFragment;
import com.yxl.cashier_retail.ui.fragment.QueryRefundFragmentNew;
import com.yxl.cashier_retail.ui.fragment.QueryShiftFragment;

/**
 * Describe:查询
 * Created by jingang on 2024/5/20
 */
@SuppressLint("NonConstantResourceId")
public class QueryActivity extends BaseActivity<ActivityQueryBinding> implements View.OnClickListener {
    private Fragment[] fragments;
    private QueryOrderFragment orderFragment;
    private QueryRefundFragmentNew refundFragment;
    private QueryGoodsFragment goodsFragment;
    private QueryShiftFragment shiftFragment;
    private QueryBatchFragment batchFragment;
    private QueryChuRuFragment chuRuFragment;

    private int index = 0;//点击的页卡索引
    private int currentTabIndex = 0;//当前的页卡索引

    //搜索关键字
    private String keyWordsOrder,//订单
            keyWordsGoods,//商品
            keyWordsChuru;//出入库

    @Override
    protected ActivityQueryBinding getViewBinding() {
        return ActivityQueryBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivLogo.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.ivSearchClear.setOnClickListener(this);
        mBinding.linOrder.setOnClickListener(this);
        mBinding.linRefund.setOnClickListener(this);
        mBinding.linGoods.setOnClickListener(this);
        mBinding.linShift.setOnClickListener(this);
        mBinding.linBatch.setOnClickListener(this);
        mBinding.linChuRu.setOnClickListener(this);
        mBinding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                mBinding.ivSearchClear.setVisibility(TextUtils.isEmpty(s.toString().trim()) ? View.GONE : View.VISIBLE);
            }
        });
        mBinding.etSearch.setOnEditorActionListener((v, actionId, event) -> {
            getKeyWords();
            return true;
        });
        setFragment();
    }

    @Override
    protected void initData() {
        setNetwork();
    }

    @Override
    public void getNetwork() {
        setNetwork();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivLogo:
                //菜单
                MenuDialog.showDialog(this, type -> {
                    //0.收银 1.入库 2.查询 3.统计 4.网单 5.会员 6.补货 7.营销 8.交班 9.设置
                    if (type != 2) {
                        switch (type) {
                            case 1:
                                goToActivity(InActivity.class);
                                break;
                            case 3:
                                goToActivity(StatisticsActivity.class);
                                break;
                            case 4:
                                goToActivity(OrderActivity.class);
                                break;
                            case 5:
                                goToActivity(MemberActivity.class);
                                break;
                            case 6:
                                goToActivity(MallActivity.class);
                                break;
                            case 7:
                                goToActivity(MarketingActivity.class);
                                break;
                            case 8:
                                goToActivity(ShiftActivity.class);
                                break;
                            case 9:
                                goToActivity(SettingActivity.class);
                                break;
                        }
                        finish();
                    }
                });
                break;
            case R.id.tvCashier:
                //收银机
                onBackPressed();
                break;
            case R.id.ivSearchClear:
                //清除搜索输入
                mBinding.etSearch.setText("");
                getKeyWords();
                break;
            case R.id.linOrder:
                //查订单
                index = 0;
                fragmentControl();
                break;
            case R.id.linRefund:
                //查退款
                index = 1;
                fragmentControl();
                break;
            case R.id.linGoods:
                //查商品
                index = 2;
                fragmentControl();
                break;
            case R.id.linShift:
                //查交班
                index = 3;
                fragmentControl();
                break;
            case R.id.linBatch:
                //查批次
                index = 4;
                fragmentControl();
                break;
            case R.id.linChuRu:
                //查出入库
                index = 5;
                fragmentControl();
                break;
        }
    }

    /**
     * 网络监听
     */
    private void setNetwork() {
        if (MyApplication.mNetwork != null) {
            if (MyApplication.mNetwork.isConnect()) {
                switch (MyApplication.mNetwork.getConnectType()) {
                    case 1:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network001);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 2:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network002);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 3:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network003);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    default:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                        mBinding.tvNetwork.setText(getRstr(R.string.no_network));
                        break;
                }
            } else {
                mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                mBinding.tvNetwork.setText(getRstr(R.string.no_network));
            }
        } else {
            mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
            mBinding.tvNetwork.setText(getRstr(R.string.no_network));
        }
    }

    /**
     * 搜索
     */
    private void getKeyWords() {
        hideSoftInput(this);
        //0.订单 2.商品
        switch (index) {
            case 0:
                keyWordsOrder = mBinding.etSearch.getText().toString().trim();
                if (orderFragment != null) {
                    orderFragment.setKeyWords(keyWordsOrder);
                }
                break;
            case 2:
                keyWordsGoods = mBinding.etSearch.getText().toString().trim();
                if (goodsFragment != null) {
                    goodsFragment.setKeyWords(keyWordsGoods);
                }
                break;
            case 5:
                keyWordsChuru = mBinding.etSearch.getText().toString().trim();
                if (chuRuFragment != null) {
                    chuRuFragment.setKeyWords(keyWordsChuru);
                }
                break;
        }
    }

    /**
     * 设置fragment
     */
    public void setFragment() {
        orderFragment = new QueryOrderFragment();
        refundFragment = new QueryRefundFragmentNew();
        goodsFragment = new QueryGoodsFragment();
        shiftFragment = new QueryShiftFragment();
        batchFragment = new QueryBatchFragment();
        chuRuFragment = new QueryChuRuFragment();
        fragments = new Fragment[]{orderFragment,
                refundFragment,
                goodsFragment,
                shiftFragment,
                batchFragment,
                chuRuFragment};
        setBottomColor();

        getSupportFragmentManager().beginTransaction().add(R.id.fragment_container, fragments[index]).show(fragments[index]).commit();
    }

    /**
     * 控制fragment的变化
     */
    public void fragmentControl() {
        if (currentTabIndex != index) {
            removeBottomColor();
            setBottomColor();

            FragmentTransaction trx = getSupportFragmentManager().beginTransaction();
            trx.hide(fragments[currentTabIndex]);
            if (!fragments[index].isAdded()) {
                trx.add(R.id.fragment_container, fragments[index]);
            }
            trx.show(fragments[index]).commitAllowingStateLoss();
            currentTabIndex = index;
        }
    }

    /**
     * 设置底部栏按钮变色
     */
    private void setBottomColor() {
        switch (index) {
            case 0:
                mBinding.linOrder.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivOrder.setImageResource(R.mipmap.ic_setting_shop001);
                mBinding.tvOrder.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.query_order));
                mBinding.linSearch.setVisibility(View.VISIBLE);
                mBinding.etSearch.setText(keyWordsOrder);
                mBinding.etSearch.setSelection(mBinding.etSearch.getText().toString().trim().length());
                break;
            case 1:
                mBinding.linRefund.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivRefund.setImageResource(R.mipmap.ic_setting_cashier001);
                mBinding.tvRefund.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.query_refund));
                break;
            case 2:
                mBinding.linGoods.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivGoods.setImageResource(R.mipmap.ic_setting_parts001);
                mBinding.tvGoods.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.query_goods));
                mBinding.linSearch.setVisibility(View.VISIBLE);
                mBinding.etSearch.setText(keyWordsGoods);
                mBinding.etSearch.setSelection(mBinding.etSearch.getText().toString().trim().length());
                break;
            case 3:
                mBinding.linShift.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivShift.setImageResource(R.mipmap.ic_setting_online001);
                mBinding.tvShift.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.query_shift));
                mBinding.linSearch.setVisibility(View.GONE);
                break;
            case 4:
                mBinding.linBatch.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivBatch.setImageResource(R.mipmap.ic_setting_system001);
                mBinding.tvBatch.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.query_batch));
                break;
            case 5:
                mBinding.linChuRu.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivChuRu.setImageResource(R.mipmap.ic_query_churu_tab001);
                mBinding.tvChuRu.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.query_chu_ru));
                mBinding.linSearch.setVisibility(View.VISIBLE);
                mBinding.etSearch.setText(keyWordsChuru);
                mBinding.etSearch.setSelection(mBinding.etSearch.getText().toString().trim().length());
                break;
            default:
                break;
        }
    }

    /**
     * 清除底部栏颜色
     */
    private void removeBottomColor() {
        switch (currentTabIndex) {
            case 0:
                mBinding.linOrder.setBackgroundResource(0);
                mBinding.ivOrder.setImageResource(R.mipmap.ic_setting_shop002);
                mBinding.tvOrder.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 1:
                mBinding.linRefund.setBackgroundResource(0);
                mBinding.ivRefund.setImageResource(R.mipmap.ic_setting_cashier002);
                mBinding.tvRefund.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 2:
                mBinding.linGoods.setBackgroundResource(0);
                mBinding.ivGoods.setImageResource(R.mipmap.ic_setting_parts002);
                mBinding.tvGoods.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 3:
                mBinding.linShift.setBackgroundResource(0);
                mBinding.ivShift.setImageResource(R.mipmap.ic_setting_online002);
                mBinding.tvShift.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 4:
                mBinding.linBatch.setBackgroundResource(0);
                mBinding.ivBatch.setImageResource(R.mipmap.ic_setting_system002);
                mBinding.tvBatch.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 5:
                mBinding.linChuRu.setBackgroundResource(0);
                mBinding.ivChuRu.setImageResource(R.mipmap.ic_query_churu_tab002);
                mBinding.tvChuRu.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            default:
                break;
        }
    }
}
