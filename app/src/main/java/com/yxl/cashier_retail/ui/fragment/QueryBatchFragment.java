package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.view.View;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmQueryBatchBinding;
import com.yxl.cashier_retail.ui.adapter.QueryBatchAdapter;
import com.yxl.cashier_retail.ui.dialog.DateStartEndDialog;

/**
 * Describe:查询-查批次
 * Created by jingang on 2024/5/23
 */
@SuppressLint("NonConstantResourceId")
public class QueryBatchFragment extends BaseFragment<FmQueryBatchBinding> implements View.OnClickListener {
    private String startDate, endDate;
    private QueryBatchAdapter mAdapter;

    @Override
    protected FmQueryBatchBinding getViewBinding() {
        return FmQueryBatchBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.linType.setOnClickListener(this);
        mBinding.linStartDate.setOnClickListener(this);
        mBinding.linEndDate.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected void initData() {
    }

    @Override
    public void onClick(View v) {
        if (isQuicklyClick()) {
            return;
        }
        switch (v.getId()) {
            case R.id.linType:
                //全部来源
                break;
            case R.id.linStartDate:
                //开始日期
                showDialogDate(0, mBinding.ivStartDate);
                break;
            case R.id.linEndDate:
                //结束日期
                showDialogDate(1, mBinding.ivEndDate);
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new QueryBatchAdapter(getActivity());
        mBinding.vSmartrefreshlayout.recyclerView.setAdapter(mAdapter);
        mBinding.vSmartrefreshlayout.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                mBinding.vSmartrefreshlayout.smartRefreshLayout.finishLoadMore();
                getBatchList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                mBinding.vSmartrefreshlayout.smartRefreshLayout.finishRefresh();
                getBatchList();
            }
        });
    }

    /**
     * dialog（选择日期时间）
     */
    private void showDialogDate(int type, View view) {
        DateStartEndDialog.showDialog(getActivity(),
                view,
                startDate,
                endDate,
                type,
                (startDate, endDate) -> {
                    this.startDate = startDate;
                    this.endDate = endDate;
                    mBinding.tvStartDate.setText(startDate);
                    mBinding.tvEndDate.setText(endDate);
                    //请求数据
                    page = 1;
                    getBatchList();
                });
    }

    /**
     * 查询批次列表
     */
    private void getBatchList() {
        showToast(1, "暂无接口");
    }
}
