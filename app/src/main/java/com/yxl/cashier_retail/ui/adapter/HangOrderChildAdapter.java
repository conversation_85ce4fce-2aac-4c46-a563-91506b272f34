package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;

/**
 * Describe:挂单列表-子列表（适配器）
 * Created by jingang on 2024/5/29
 */
public class HangOrderChildAdapter extends BaseAdapter<String> {

    public HangOrderChildAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_hang_order_child;
    }

    @Override
    public int getItemCount() {
        return 4;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName, tvMobile, tvTime, tvTotal, tvCount;
        ImageView ivDel, ivTotal, ivCount;
        ivDel = holder.getView(R.id.ivItemDel);
        tvName = holder.getView(R.id.tvItemName);
        tvMobile = holder.getView(R.id.tvItemMobile);
        tvTime = holder.getView(R.id.tvItemTime);
        ivTotal = holder.getView(R.id.ivItemTotal);
        tvTotal = holder.getView(R.id.tvItemTotal);
        ivCount = holder.getView(R.id.ivItemCount);
        tvCount = holder.getView(R.id.tvItemCount);
        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(v, position));
            ivDel.setOnClickListener(v -> listener.onDelClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onDelClick(View view, int position);
    }
}
