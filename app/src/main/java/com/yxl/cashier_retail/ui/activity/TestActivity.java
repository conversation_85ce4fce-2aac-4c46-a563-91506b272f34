package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.view.View;

import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.MyApplication;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityTestBinding;

/**
 * Describe:测试（选择接口地址）
 * Created by jingang on 2024/5/9
 */
@SuppressLint("NonConstantResourceId")
public class TestActivity extends BaseActivity<ActivityTestBinding> implements View.OnClickListener {
    @Override
    protected ActivityTestBinding getViewBinding() {
        return ActivityTestBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivBack.setOnClickListener(this);
        mBinding.butInput.setOnClickListener(this);
        mBinding.butDebug.setOnClickListener(this);
        mBinding.butRelease.setOnClickListener(this);
        mBinding.butDev.setOnClickListener(this);
    }

    @Override
    protected void initData() {

    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.butInput:
                //输入
                if (TextUtils.isEmpty(mBinding.etInput.getText().toString().trim())) {
                    return;
                }
                SPUtils.getInstance().put("testInput", mBinding.etInput.getText().toString().trim());
                SPUtils.getInstance().put("test", 2);
                MyApplication.setUrl();
                finish();
                break;
            case R.id.butDebug:
                //测试域名
                SPUtils.getInstance().put("testInput", "");
                SPUtils.getInstance().put("test", 0);
                MyApplication.setUrl();
                finish();
                break;
            case R.id.butRelease:
                //正式域名
                SPUtils.getInstance().put("testInput", "");
                SPUtils.getInstance().put("test", 1);
                MyApplication.setUrl();
                finish();
                break;
            case R.id.butDev:
                //开发域名
                SPUtils.getInstance().put("testInput", "");
                SPUtils.getInstance().put("test", 3);
                MyApplication.setUrl();
                finish();
                break;
        }
    }
}
