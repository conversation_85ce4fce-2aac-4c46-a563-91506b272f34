package com.yxl.cashier_retail.ui.bean;

import java.util.List;

/**
 * Describe:营销（实体类）
 * Created by jingang on 2024/10/12
 */
public class ActivityListData {
    /**
     * promotionActivityId : 239
     * promotionActivityName : 商品促销哦
     * orderActivity : 0
     * createTime : 2024-09-30 17:52:57
     * startTime : 2024-09-30 00:00:00
     * endTime : 2025-09-30 00:00:00
     * type : 1
     * shopUnique : 1536215939565
     * activityRange : 1
     * status : 1
     * cusActivity : 0
     * promotionGoodsGiftList : null
     * promotionGoodsSingleList : null
     * promotionGoodsMarkdownList : [{"detailId":24,"goodsId":1543305416,"goodsName":"花生2","goodsBarcode":"3190878","salePrice":45,"meetCount1":5,"discountPercent1":8,"meetCount2":6,"discountPercent2":7,"meetCount3":10,"discountPercent3":6,"limitCount":100},{"detailId":25,"goodsId":1543305415,"goodsName":"花生1","goodsBarcode":"3114574","salePrice":12,"meetCount1":2,"discountPercent1":9,"meetCount2":3,"discountPercent2":8,"meetCount3":4,"discountPercent3":7,"limitCount":20}]
     * promotionOrderMarkdownList : null
     */

    private int promotionActivityId;
    private String promotionActivityName;//活动名称
    private int orderActivity;//是否餐饮订单优惠，0、不餐饮；1、不参与；
    private String createTime;//创建时间
    private String startTime;//活动开始时间
    private String endTime;//活动结束时间
    private int type;//活动类型：1、商品折扣；2、商品满赠；3、订单促销；4、单品促销
    private String shopUnique;
    private int activityRange;//活动范围：1、PC收银机；2、一刻钟到家小程序
    private int status;//状态：1、正常；2、停用
    private int cusActivity;//会员价是否参与 1:是 0:否
    private List<PromotionGoodsGiftListBean> promotionGoodsGiftList;//商品满赠列表
    private List<PromotionGoodsSingleListBean> promotionGoodsSingleList;//单品促销活动列表
    private List<PromotionOrderMarkdownListBean> promotionOrderMarkdownList;//订单促销活动列表
    private List<PromotionGoodsMarkdownListBean> promotionGoodsMarkdownList;//商品折扣活动列表

    public int getPromotionActivityId() {
        return promotionActivityId;
    }

    public void setPromotionActivityId(int promotionActivityId) {
        this.promotionActivityId = promotionActivityId;
    }

    public String getPromotionActivityName() {
        return promotionActivityName;
    }

    public void setPromotionActivityName(String promotionActivityName) {
        this.promotionActivityName = promotionActivityName;
    }

    public int getOrderActivity() {
        return orderActivity;
    }

    public void setOrderActivity(int orderActivity) {
        this.orderActivity = orderActivity;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public int getActivityRange() {
        return activityRange;
    }

    public void setActivityRange(int activityRange) {
        this.activityRange = activityRange;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getCusActivity() {
        return cusActivity;
    }

    public void setCusActivity(int cusActivity) {
        this.cusActivity = cusActivity;
    }

    public List<PromotionGoodsGiftListBean> getPromotionGoodsGiftList() {
        return promotionGoodsGiftList;
    }

    public void setPromotionGoodsGiftList(List<PromotionGoodsGiftListBean> promotionGoodsGiftList) {
        this.promotionGoodsGiftList = promotionGoodsGiftList;
    }

    public List<PromotionGoodsSingleListBean> getPromotionGoodsSingleList() {
        return promotionGoodsSingleList;
    }

    public void setPromotionGoodsSingleList(List<PromotionGoodsSingleListBean> promotionGoodsSingleList) {
        this.promotionGoodsSingleList = promotionGoodsSingleList;
    }

    public List<PromotionOrderMarkdownListBean> getPromotionOrderMarkdownList() {
        return promotionOrderMarkdownList;
    }

    public void setPromotionOrderMarkdownList(List<PromotionOrderMarkdownListBean> promotionOrderMarkdownList) {
        this.promotionOrderMarkdownList = promotionOrderMarkdownList;
    }

    public List<PromotionGoodsMarkdownListBean> getPromotionGoodsMarkdownList() {
        return promotionGoodsMarkdownList;
    }

    public void setPromotionGoodsMarkdownList(List<PromotionGoodsMarkdownListBean> promotionGoodsMarkdownList) {
        this.promotionGoodsMarkdownList = promotionGoodsMarkdownList;
    }

    public static class PromotionGoodsGiftListBean {
        /**
         * 商品满赠
         * detailId : 27
         * goodsId : 1543305209
         * goodsName : 鸥露
         * goodsBarcode : 6944312688140
         * salePrice : 3.0
         * goodsIdGift1 : 1543305208
         * goodsIdGift2 : 1543305208
         * goodsIdGift3 : 1543305208
         * goodsBarcodeGift1 : 3103193
         * goodsBarcodeGift2 : 3103193
         * goodsBarcodeGift3 : 3103193
         * goodsNameGift1 : 清风抽纸新款
         * goodsNameGift2 : 清风抽纸新款
         * goodsNameGift3 : 清风抽纸新款
         * meetCount1 : 4
         * giftCount1 : 2
         * meetCount2 : 6
         * giftCount2 : 3
         * meetCount3 : 10
         * giftCount3 : 6
         */

        private int detailId;//促销活动id
        private int goodsId;//促销商品id
        private String goodsName;
        private String goodsBarcode;
        private double salePrice;//售价
        private int goodsIdGift1;
        private int goodsIdGift2;
        private int goodsIdGift3;
        private String goodsBarcodeGift1;
        private String goodsBarcodeGift2;
        private String goodsBarcodeGift3;
        private String goodsNameGift1;
        private String goodsNameGift2;
        private String goodsNameGift3;
        private int meetCount1;//优惠条件1、满足此条件，赠送赠品1
        private int giftCount1;//优惠条件1满足时，赠送的赠品1数量
        private int meetCount2;
        private int giftCount2;
        private int meetCount3;
        private int giftCount3;

        public int getDetailId() {
            return detailId;
        }

        public void setDetailId(int detailId) {
            this.detailId = detailId;
        }

        public int getGoodsId() {
            return goodsId;
        }

        public void setGoodsId(int goodsId) {
            this.goodsId = goodsId;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public String getGoodsBarcode() {
            return goodsBarcode;
        }

        public void setGoodsBarcode(String goodsBarcode) {
            this.goodsBarcode = goodsBarcode;
        }

        public double getSalePrice() {
            return salePrice;
        }

        public void setSalePrice(double salePrice) {
            this.salePrice = salePrice;
        }

        public int getGoodsIdGift1() {
            return goodsIdGift1;
        }

        public void setGoodsIdGift1(int goodsIdGift1) {
            this.goodsIdGift1 = goodsIdGift1;
        }

        public int getGoodsIdGift2() {
            return goodsIdGift2;
        }

        public void setGoodsIdGift2(int goodsIdGift2) {
            this.goodsIdGift2 = goodsIdGift2;
        }

        public int getGoodsIdGift3() {
            return goodsIdGift3;
        }

        public void setGoodsIdGift3(int goodsIdGift3) {
            this.goodsIdGift3 = goodsIdGift3;
        }

        public String getGoodsBarcodeGift1() {
            return goodsBarcodeGift1;
        }

        public void setGoodsBarcodeGift1(String goodsBarcodeGift1) {
            this.goodsBarcodeGift1 = goodsBarcodeGift1;
        }

        public String getGoodsBarcodeGift2() {
            return goodsBarcodeGift2;
        }

        public void setGoodsBarcodeGift2(String goodsBarcodeGift2) {
            this.goodsBarcodeGift2 = goodsBarcodeGift2;
        }

        public String getGoodsBarcodeGift3() {
            return goodsBarcodeGift3;
        }

        public void setGoodsBarcodeGift3(String goodsBarcodeGift3) {
            this.goodsBarcodeGift3 = goodsBarcodeGift3;
        }

        public String getGoodsNameGift1() {
            return goodsNameGift1;
        }

        public void setGoodsNameGift1(String goodsNameGift1) {
            this.goodsNameGift1 = goodsNameGift1;
        }

        public String getGoodsNameGift2() {
            return goodsNameGift2;
        }

        public void setGoodsNameGift2(String goodsNameGift2) {
            this.goodsNameGift2 = goodsNameGift2;
        }

        public String getGoodsNameGift3() {
            return goodsNameGift3;
        }

        public void setGoodsNameGift3(String goodsNameGift3) {
            this.goodsNameGift3 = goodsNameGift3;
        }

        public int getMeetCount1() {
            return meetCount1;
        }

        public void setMeetCount1(int meetCount1) {
            this.meetCount1 = meetCount1;
        }

        public int getGiftCount1() {
            return giftCount1;
        }

        public void setGiftCount1(int giftCount1) {
            this.giftCount1 = giftCount1;
        }

        public int getMeetCount2() {
            return meetCount2;
        }

        public void setMeetCount2(int meetCount2) {
            this.meetCount2 = meetCount2;
        }

        public int getGiftCount2() {
            return giftCount2;
        }

        public void setGiftCount2(int giftCount2) {
            this.giftCount2 = giftCount2;
        }

        public int getMeetCount3() {
            return meetCount3;
        }

        public void setMeetCount3(int meetCount3) {
            this.meetCount3 = meetCount3;
        }

        public int getGiftCount3() {
            return giftCount3;
        }

        public void setGiftCount3(int giftCount3) {
            this.giftCount3 = giftCount3;
        }
    }

    public static class PromotionGoodsSingleListBean {
        /**
         * 单品促销
         * detailId : 23
         * goodsId : 1543304960
         * goodsName : null
         * goodsBarcode : null
         * salePrice : null
         * number1 : 1
         * discountPercent1 : 9.0
         * number2 : 5
         * discountPercent2 : 7.0
         * number3 : 10
         * discountPercent3 : 1.0
         */

        private int detailId;//活动详情id
        private int goodsId;//活动商品id
        private String goodsName;
        private String goodsBarcode;
        private double salePrice;
        private int number1;//条件1、第几件
        private double discountPercent1;//条件1、几折
        private int number2;
        private double discountPercent2;
        private int number3;
        private double discountPercent3;

        public int getDetailId() {
            return detailId;
        }

        public void setDetailId(int detailId) {
            this.detailId = detailId;
        }

        public int getGoodsId() {
            return goodsId;
        }

        public void setGoodsId(int goodsId) {
            this.goodsId = goodsId;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public String getGoodsBarcode() {
            return goodsBarcode;
        }

        public void setGoodsBarcode(String goodsBarcode) {
            this.goodsBarcode = goodsBarcode;
        }

        public double getSalePrice() {
            return salePrice;
        }

        public void setSalePrice(double salePrice) {
            this.salePrice = salePrice;
        }

        public int getNumber1() {
            return number1;
        }

        public void setNumber1(int number1) {
            this.number1 = number1;
        }

        public double getDiscountPercent1() {
            return discountPercent1;
        }

        public void setDiscountPercent1(double discountPercent1) {
            this.discountPercent1 = discountPercent1;
        }

        public int getNumber2() {
            return number2;
        }

        public void setNumber2(int number2) {
            this.number2 = number2;
        }

        public double getDiscountPercent2() {
            return discountPercent2;
        }

        public void setDiscountPercent2(double discountPercent2) {
            this.discountPercent2 = discountPercent2;
        }

        public int getNumber3() {
            return number3;
        }

        public void setNumber3(int number3) {
            this.number3 = number3;
        }

        public double getDiscountPercent3() {
            return discountPercent3;
        }

        public void setDiscountPercent3(double discountPercent3) {
            this.discountPercent3 = discountPercent3;
        }
    }

    public static class PromotionOrderMarkdownListBean{

        /**订单促销
         * detailId : 11
         * shopUnique : 1536215939565
         * meetPrice1 : 10.0
         * discountPrice1 : 1.0
         * giftGoodsId1 : 1543305222
         * giftGoodsBarcode1 : 3106974
         * giftGoodsName1 : 顶顶顶
         * giftCount1 : 1
         * meetPrice2 : 100.0
         * discountPrice2 : null
         * giftGoodsId2 : 1543305158
         * giftGoodsBarcode2 : 2180201
         * giftGoodsName2 : 雪碧袋装
         * giftCount2 : 1
         * meetPrice3 : null
         * discountPrice3 : null
         * giftGoodsId3 : null
         * giftGoodsBarcode3 : 2180201
         * giftGoodsName3 : 雪碧袋装
         * giftCount3 : null
         */

        private int detailId;//活动详情id
        private String shopUnique;
        private double meetPrice1;//优惠条件1、满足金额
        private double discountPrice1;//优惠条件1、优惠金额
        private int giftGoodsId1;//优惠条件1、赠送商品ID
        private String giftGoodsBarcode1;//优惠条件1、赠送商品条码
        private String giftGoodsName1;//优惠条件1、赠送商品名称
        private int giftCount1;//优惠条件1、赠送商品数量
        private double meetPrice2;
        private double discountPrice2;
        private int giftGoodsId2;
        private String giftGoodsBarcode2;
        private String giftGoodsName2;
        private int giftCount2;
        private double meetPrice3;
        private double discountPrice3;
        private int giftGoodsId3;
        private String giftGoodsBarcode3;
        private String giftGoodsName3;
        private int giftCount3;

        public int getDetailId() {
            return detailId;
        }

        public void setDetailId(int detailId) {
            this.detailId = detailId;
        }

        public String getShopUnique() {
            return shopUnique;
        }

        public void setShopUnique(String shopUnique) {
            this.shopUnique = shopUnique;
        }

        public double getMeetPrice1() {
            return meetPrice1;
        }

        public void setMeetPrice1(double meetPrice1) {
            this.meetPrice1 = meetPrice1;
        }

        public double getDiscountPrice1() {
            return discountPrice1;
        }

        public void setDiscountPrice1(double discountPrice1) {
            this.discountPrice1 = discountPrice1;
        }

        public int getGiftGoodsId1() {
            return giftGoodsId1;
        }

        public void setGiftGoodsId1(int giftGoodsId1) {
            this.giftGoodsId1 = giftGoodsId1;
        }

        public String getGiftGoodsBarcode1() {
            return giftGoodsBarcode1;
        }

        public void setGiftGoodsBarcode1(String giftGoodsBarcode1) {
            this.giftGoodsBarcode1 = giftGoodsBarcode1;
        }

        public String getGiftGoodsName1() {
            return giftGoodsName1;
        }

        public void setGiftGoodsName1(String giftGoodsName1) {
            this.giftGoodsName1 = giftGoodsName1;
        }

        public int getGiftCount1() {
            return giftCount1;
        }

        public void setGiftCount1(int giftCount1) {
            this.giftCount1 = giftCount1;
        }

        public double getMeetPrice2() {
            return meetPrice2;
        }

        public void setMeetPrice2(double meetPrice2) {
            this.meetPrice2 = meetPrice2;
        }

        public double getDiscountPrice2() {
            return discountPrice2;
        }

        public void setDiscountPrice2(double discountPrice2) {
            this.discountPrice2 = discountPrice2;
        }

        public int getGiftGoodsId2() {
            return giftGoodsId2;
        }

        public void setGiftGoodsId2(int giftGoodsId2) {
            this.giftGoodsId2 = giftGoodsId2;
        }

        public String getGiftGoodsBarcode2() {
            return giftGoodsBarcode2;
        }

        public void setGiftGoodsBarcode2(String giftGoodsBarcode2) {
            this.giftGoodsBarcode2 = giftGoodsBarcode2;
        }

        public String getGiftGoodsName2() {
            return giftGoodsName2;
        }

        public void setGiftGoodsName2(String giftGoodsName2) {
            this.giftGoodsName2 = giftGoodsName2;
        }

        public int getGiftCount2() {
            return giftCount2;
        }

        public void setGiftCount2(int giftCount2) {
            this.giftCount2 = giftCount2;
        }

        public double getMeetPrice3() {
            return meetPrice3;
        }

        public void setMeetPrice3(double meetPrice3) {
            this.meetPrice3 = meetPrice3;
        }

        public double getDiscountPrice3() {
            return discountPrice3;
        }

        public void setDiscountPrice3(double discountPrice3) {
            this.discountPrice3 = discountPrice3;
        }

        public int getGiftGoodsId3() {
            return giftGoodsId3;
        }

        public void setGiftGoodsId3(int giftGoodsId3) {
            this.giftGoodsId3 = giftGoodsId3;
        }

        public String getGiftGoodsBarcode3() {
            return giftGoodsBarcode3;
        }

        public void setGiftGoodsBarcode3(String giftGoodsBarcode3) {
            this.giftGoodsBarcode3 = giftGoodsBarcode3;
        }

        public String getGiftGoodsName3() {
            return giftGoodsName3;
        }

        public void setGiftGoodsName3(String giftGoodsName3) {
            this.giftGoodsName3 = giftGoodsName3;
        }

        public int getGiftCount3() {
            return giftCount3;
        }

        public void setGiftCount3(int giftCount3) {
            this.giftCount3 = giftCount3;
        }
    }

    public static class PromotionGoodsMarkdownListBean {
        /**
         * 商品折扣
         * detailId : 24
         * goodsId : 1543305416
         * goodsName : 花生2
         * goodsBarcode : 3190878
         * salePrice : 45.0
         * meetCount1 : 5
         * discountPercent1 : 8.0
         * meetCount2 : 6
         * discountPercent2 : 7.0
         * meetCount3 : 10
         * discountPercent3 : 6.0
         * limitCount : 100
         */

        private int detailId;//促销详情id
        private int goodsId;//促销商品id
        private String goodsName;
        private String goodsBarcode;
        private double salePrice;
        private int meetCount1;//优惠条件1、满足件数
        private double discountPercent1;//优惠条件1、商品折扣
        private int meetCount2;
        private double discountPercent2;
        private int meetCount3;
        private double discountPercent3;
        private int limitCount;

        public int getDetailId() {
            return detailId;
        }

        public void setDetailId(int detailId) {
            this.detailId = detailId;
        }

        public int getGoodsId() {
            return goodsId;
        }

        public void setGoodsId(int goodsId) {
            this.goodsId = goodsId;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public String getGoodsBarcode() {
            return goodsBarcode;
        }

        public void setGoodsBarcode(String goodsBarcode) {
            this.goodsBarcode = goodsBarcode;
        }

        public double getSalePrice() {
            return salePrice;
        }

        public void setSalePrice(double salePrice) {
            this.salePrice = salePrice;
        }

        public int getMeetCount1() {
            return meetCount1;
        }

        public void setMeetCount1(int meetCount1) {
            this.meetCount1 = meetCount1;
        }

        public double getDiscountPercent1() {
            return discountPercent1;
        }

        public void setDiscountPercent1(double discountPercent1) {
            this.discountPercent1 = discountPercent1;
        }

        public int getMeetCount2() {
            return meetCount2;
        }

        public void setMeetCount2(int meetCount2) {
            this.meetCount2 = meetCount2;
        }

        public double getDiscountPercent2() {
            return discountPercent2;
        }

        public void setDiscountPercent2(double discountPercent2) {
            this.discountPercent2 = discountPercent2;
        }

        public int getMeetCount3() {
            return meetCount3;
        }

        public void setMeetCount3(int meetCount3) {
            this.meetCount3 = meetCount3;
        }

        public double getDiscountPercent3() {
            return discountPercent3;
        }

        public void setDiscountPercent3(double discountPercent3) {
            this.discountPercent3 = discountPercent3;
        }

        public int getLimitCount() {
            return limitCount;
        }

        public void setLimitCount(int limitCount) {
            this.limitCount = limitCount;
        }
    }
}
