package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogUnitEditBinding;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * Describe:dialog（商品单位新增、编辑）
 * Created by jingang on 2025/03/19
 */
@SuppressLint({"NonConstantResourceId", "StaticFieldLeak"})
public class UnitEditDialog extends BaseDialog<DialogUnitEditBinding> implements View.OnClickListener {
    private static String id;
    private static String name;

    public static void showDialog(Activity activity, String id, String name, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        UnitEditDialog.listener = listener;
        UnitEditDialog.id = id;
        UnitEditDialog.name = name;
        UnitEditDialog dialog = new UnitEditDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public UnitEditDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.ivDialogClear.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        mBinding.tvDialogTitle.setText(TextUtils.isEmpty(id) ? getRstr(R.string.goods_unit_add) : getRstr(R.string.goods_unit_edit));
        mBinding.etDialogName.setText(name);
    }

    @Override
    protected DialogUnitEditBinding getViewBinding() {
        return DialogUnitEditBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.ivDialogSearchClear:
                //清除搜索输入
                mBinding.etDialogName.setText("");
                break;
            case R.id.tvDialogConfirm:
                //确认
                name = mBinding.etDialogName.getText().toString().trim();
                if (TextUtils.isEmpty(name)) {
                    showToast(1, getRstr(R.string.input_goods_unit));
                    return;
                }
                if (TextUtils.isEmpty(id)) {
                    postGoodsUnitAdd();
                } else {
                    postGoodsUnitEdit();
                }
                break;
        }
    }

    /**
     * 单位新增
     */
    private void postGoodsUnitAdd() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shop_unique", getShopUnique());
        map.put("goods_unit", name);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getGoodsUnitAdd(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        if (listener != null) {
                            listener.onConfirm(name);
                            dismiss();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 单位编辑
     */
    private void postGoodsUnitEdit() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("goods_unit_id", id);
        map.put("goods_unit", name);
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getGoodsUnitEdit(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        if (listener != null) {
                            listener.onConfirm(name);
                            dismiss();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(String name);
    }
}
