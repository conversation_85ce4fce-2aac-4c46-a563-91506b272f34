package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Paint;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.ui.bean.MemberData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:购物车（适配器）
 * Created by jingang on 2024/9/19
 */
public class CartAdapter extends BaseAdapter<GoodsData> {

    private MemberData memberData;

    public void setMemberData(MemberData memberData) {
        this.memberData = memberData;
        notifyDataSetChanged();
    }

    public CartAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_cart;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvDiscount, tvDiscountMember, tvLost, tvActivity, tvName, tvPrice, tvPrice1, tvCount, tvTotal;
        tvDiscount = holder.getView(R.id.tvItemDiscount);//优惠
        tvDiscountMember = holder.getView(R.id.tvItemDiscountMember);//会员优惠
        tvLost = holder.getView(R.id.tvItemLost);//售价低于进价
        tvActivity = holder.getView(R.id.tvItemActivity);//活动商品
        tvName = holder.getView(R.id.tvItemName);
        RelativeLayout relPrice = holder.getView(R.id.relItemPrice);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvPrice1 = holder.getView(R.id.tvItemPrice1);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTotal = holder.getView(R.id.tvItemTotal);
        ImageView ivDel = holder.getView(R.id.ivItemDel);

        //是否编辑单价
        if (mDataList.get(position).isEditPrice()) {
            tvPrice.setBackgroundResource(R.drawable.shape_green_5);
            tvPrice.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            tvPrice.setBackgroundResource(0);
            tvPrice.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.red));
        }
        //是否编辑数量
        if (mDataList.get(position).isEditCount()) {
            tvCount.setBackgroundResource(R.drawable.shape_green_5);
            tvCount.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            tvCount.setBackgroundResource(0);
            tvCount.setTextColor(mContext.getResources().getColor(R.color.black));
        }
        //是否编辑总价
        if (mDataList.get(position).isEditTotal()) {
            tvTotal.setBackgroundResource(R.drawable.shape_green_5);
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            tvTotal.setBackgroundResource(0);
            tvTotal.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.red));
        }

        double price,//单价
                count = mDataList.get(position).getCartNum(),//数量
                newPrice = mDataList.get(position).getNewPrice(),//现价
                inPrice = mDataList.get(position).getGoods_in_price(),//进价
                salePrice = mDataList.get(position).getGoods_sale_price(),//售价
                memberPrice = TextUtils.isEmpty(mDataList.get(position).getGoods_cus_price()) ?
                        0 : Double.parseDouble(mDataList.get(position).getGoods_cus_price());//会员价
        /**
         * 价格判断逻辑
         * 1.是否有改价
         * 3.是否有会员
         */
        if (newPrice > 0) {
            //有改价
            price = newPrice;
            tvDiscount.setVisibility(price < salePrice ? View.VISIBLE : View.GONE);
            tvDiscountMember.setVisibility(View.GONE);
        } else {
            tvDiscount.setVisibility(View.GONE);
            if (memberData != null) {
                //有会员价
                price = memberPrice;
                tvDiscountMember.setVisibility(View.VISIBLE);
            } else {
                price = salePrice;
                tvDiscountMember.setVisibility(View.GONE);
            }
        }

        //原价
        if (price < salePrice) {
            tvPrice1.setVisibility(View.VISIBLE);
        } else {
            tvPrice1.setVisibility(View.GONE);
        }
        //售价低于进价
        if (price < inPrice) {
            tvLost.setVisibility(View.VISIBLE);
        } else {
            tvLost.setVisibility(View.GONE);
        }
        String barcode = mDataList.get(position).getGoods_barcode();
        if (TextUtils.isEmpty(barcode)) {
            tvName.setText(mDataList.get(position).getGoods_name());
        } else {
            switch (barcode) {
                case "999999999":
                case "999999998":
                    tvName.setText(DFUtils.getNum4(price) + getRstr(R.string.yuan_goods));
                    break;
                default:
                    tvName.setText(mDataList.get(position).getGoods_name());
                    break;
            }
        }
        if (price > 9999.99) {
            tvPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10);
        } else {
            tvPrice.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
        }
        double total = price * count;
        if (total > 9999.99) {
            tvTotal.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10);
        } else {
            tvTotal.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16);
        }
        tvPrice.setText(DFUtils.getNum2(price));
        tvPrice1.setText(DFUtils.getNum2(mDataList.get(position).getGoods_sale_price()));
        tvPrice1.getPaint().setFlags(Paint.STRIKE_THRU_TEXT_FLAG);
        tvCount.setText(DFUtils.getNum4(count));
        tvTotal.setText(DFUtils.getNum2(total));

        if (listener != null) {
            relPrice.setOnClickListener(v -> listener.onPriceClick(v, position));
            tvCount.setOnClickListener(v -> listener.onCountClick(v, position));
            tvTotal.setOnClickListener(v -> listener.onTotalClick(v, position));
            ivDel.setOnClickListener(v -> listener.onDelClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onPriceClick(View view, int position);

        void onCountClick(View view, int position);

        void onTotalClick(View view, int position);

        void onDelClick(View view, int position);
    }
}
