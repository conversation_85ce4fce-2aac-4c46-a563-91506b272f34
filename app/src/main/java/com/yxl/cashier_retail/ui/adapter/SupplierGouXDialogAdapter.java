package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.PurchaseListData;
import com.yxl.cashier_retail.utils.DFUtils;

import java.util.List;

/**
 * Describe:供货商管理-结款弹窗（适配器）
 * Created by jingang on 2024/6/12
 */
public class SupplierGouXDialogAdapter extends BaseAdapter<PurchaseListData> {

    public SupplierGouXDialogAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_supplier_goux_dialog;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        ImageView ivSelect = holder.getView(R.id.ivItemSelect);
        ivSelect.setSelected(mDataList.get(position).isSelect());
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivSelect = holder.getView(R.id.ivItemSelect);
        TextView tvName, tvStatus, tvCount, tvTotal;
        tvName = holder.getView(R.id.tvItemName);
        tvStatus = holder.getView(R.id.tvItemStatus);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTotal = holder.getView(R.id.tvItemTotal);
        RecyclerView recyclerView = holder.getView(R.id.rvItem);

        ivSelect.setSelected(mDataList.get(position).isSelect());
        tvName.setText(mDataList.get(position).getSupplierName());
        tvCount.setText(getRstr(R.string.total) + DFUtils.getNum4(mDataList.get(position).getGoodsCategory()) + getRstr(R.string.classX));
        tvTotal.setText(getRstr(R.string.money) + DFUtils.getNum2(mDataList.get(position).getTotalPrice()));
        //1-已提交(待收货)，2-已收货(待付款)，3-待确认，4-已完成，5-异常，6-作废
        switch (mDataList.get(position).getStatus()) {
            case 1:
                tvStatus.setText(getRstr(R.string.order_status9));
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.blue));
                break;
            case 2:
                tvStatus.setText(getRstr(R.string.order_status7));
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 3:
                tvStatus.setText(getRstr(R.string.order_status10));
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 4:
                tvStatus.setText(getRstr(R.string.order_status6));
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 5:
                tvStatus.setText(getRstr(R.string.order_status11));
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.red));
                break;
            default:
                tvStatus.setText("");
                break;
        }

        if (mDataList.get(position).getGoodsList() == null) {
            recyclerView.setVisibility(View.GONE);
        } else {
            if (mDataList.get(position).getGoodsList().size() > 0) {
                recyclerView.setVisibility(View.VISIBLE);
                ChildGoodsAdapter adapter = new ChildGoodsAdapter(mContext);
                recyclerView.setAdapter(adapter);
                adapter.setDataList(mDataList.get(position).getGoodsList());
            } else {
                recyclerView.setVisibility(View.GONE);
            }
        }

        if (onItemClickListener != null) {
            ivSelect.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }

    }
}
