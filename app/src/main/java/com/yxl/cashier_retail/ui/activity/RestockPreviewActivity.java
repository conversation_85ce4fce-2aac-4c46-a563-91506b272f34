package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;

import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityRestockPreviewBinding;
import com.yxl.cashier_retail.ui.adapter.RestockPreviewAdapter;
import com.yxl.cashier_retail.ui.adapter.RestockPreviewInfoAdapter;
import com.yxl.cashier_retail.ui.bean.ChildGoodsData;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.RestockPreviewData;
import com.yxl.cashier_retail.ui.dialog.RestockSubmitTipsDialog;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:自采补货-预览
 * Created by jingang on 2024/6/5
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class RestockPreviewActivity extends BaseActivity<ActivityRestockPreviewBinding> implements View.OnClickListener {
    private int id,
            pos;

    //预览
    private RestockPreviewAdapter mAdapter;
    private List<RestockPreviewData> dataList = new ArrayList<>();

    //详情
    private RestockPreviewInfoAdapter goodsAdapter;
    private List<ChildGoodsData> goodsList = new ArrayList<>();

    @Override
    protected ActivityRestockPreviewBinding getViewBinding() {
        return ActivityRestockPreviewBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivBack.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.tvConfirm.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected void initData() {
        id = getIntent().getIntExtra("id", 0);
        getRestockPreview();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.tvConfirm:
                //提交补货计划
                RestockSubmitTipsDialog.showDialog(this, () -> postRestockPlanStatus(2));
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //预览
        mAdapter = new RestockPreviewAdapter(this);
        mBinding.rvRestock.setAdapter(mAdapter);
        mAdapter.setListener(new RestockPreviewAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情
                if (!dataList.get(position).isSelect()) {
                    for (int i = 0; i < dataList.size(); i++) {
                        if (dataList.get(i).isSelect()) {
                            dataList.get(i).setSelect(false);
                            mAdapter.notifyItemChanged(i);
                        }
                    }
                    dataList.get(position).setSelect(true);
                    mAdapter.notifyItemChanged(position);
                    pos = position;
                    setUI(dataList.get(position));
                }
            }

            @Override
            public void onRemarks(String remarks, int position) {
                //修改备注
                postRemarks(dataList.get(position).getShopRestockplanPresentId(), remarks, position);
            }
        });
        mBinding.srlRestock.setOnRefreshListener(refreshLayout -> getRestockPreview());
        mBinding.srlRestock.setEnableLoadMore(false);

        //详情
        goodsAdapter = new RestockPreviewInfoAdapter(this);
        mBinding.rvGoods.setAdapter(goodsAdapter);
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(RestockPreviewData data) {
        if (data == null) {
            return;
        }
        mBinding.tvName.setText(data.getSupplierName());
        mBinding.tvSupplier.setText(TextUtils.isEmpty(data.getSupplierName()) ? "-" : data.getSupplierName());
        mBinding.tvContacts.setText(TextUtils.isEmpty(data.getCompanyLeagl()) ? "-" : data.getCompanyLeagl());
        mBinding.tvMobile.setText(TextUtils.isEmpty(data.getSupplierPhone()) ? "-" : data.getSupplierPhone());
        mBinding.tvAds.setText(TextUtils.isEmpty(data.getSupplierAddress()) ? "-" : data.getSupplierAddress());
        mBinding.tvRemarks.setText(TextUtils.isEmpty(data.getRemark()) ? "-" : data.getRemark());
        mBinding.tvCount.setText(getRstr(R.string.goods_type_colon) + data.getGoodsCounts() + "类");
        mBinding.tvTotal.setText(getRstr(R.string.price_estimate) + getRstr(R.string.money) + DFUtils.getNum2(data.getGoodsTotal()));
        goodsList.clear();
        if (data.getGoodsList() != null) {
            goodsList.addAll(data.getGoodsList());
        }
        goodsAdapter.setDataList(goodsList);
    }

    /**
     * 补货计划-预览
     */
    private void getRestockPreview() {
        if (id == 0) {
            showToast(1, getRstr(R.string.restock_id_wrong));
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("restockPlanId", id);
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponseList(this,
                ZURL.getRestockPreview(),
                map,
                RestockPreviewData.class,
                new RequestListListener<RestockPreviewData>() {
                    @Override
                    public void onResult(List<RestockPreviewData> list) {
                        hideDialog();
                        mBinding.srlRestock.finishRefresh();
                        dataList.clear();
                        dataList.addAll(list);
                        if (dataList.size() > 0) {
                            dataList.get(0).setSelect(true);
                            setUI(dataList.get(0));
                            mBinding.rvRestock.setVisibility(View.VISIBLE);
                            mBinding.linEmptyRestock.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.rvRestock.setVisibility(View.GONE);
                            mBinding.linEmptyRestock.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.srlRestock.finishRefresh();
                        if (dataList.size() > 0) {
                            mBinding.rvRestock.setVisibility(View.VISIBLE);
                            mBinding.linEmptyRestock.setVisibility(View.GONE);
                        } else {
                            mBinding.rvRestock.setVisibility(View.GONE);
                            mBinding.linEmptyRestock.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    /**
     * 修改供货商备注
     *
     * @param presentId
     * @param remarks
     */
    private void postRemarks(int presentId, String remarks, int position) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("restockPlanId", id);
        map.put("shopRestockplanPresentId", presentId);
        map.put("remark", remarks);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getRestockRemarks(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        dataList.get(position).setRemark(remarks);
                        if (pos == position) {
                            mBinding.tvRemarks.setText(remarks);
                        }
                    }
                });
    }

    /**
     * 修改补货计划状态信息
     */
    private void postRestockPlanStatus(int status) {
        if (id == 0) {
            showToast(1, getRstr(R.string.restock_id_wrong));
            return;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("restockPlanId", id);
        map.put("planStatus", status);
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getRestockUpdate(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.RESTOCK_LIST));
                        setResult(Constants.RESTOCK, new Intent());
                        finish();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }
}
