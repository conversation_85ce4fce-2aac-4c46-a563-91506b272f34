package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogMemberPointsRuleBinding;
import com.yxl.cashier_retail.view.NumberKeyBoardView;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:dialog（会员管理-积分规则）
 * Created by jingang on 2024/7/3
 */
@SuppressLint("NonConstantResourceId")
public class MemberPointsRuleDialog extends BaseDialog<DialogMemberPointsRuleBinding> implements View.OnClickListener {
    private boolean isEnable,//是否使用积分
            isCount;//是否不计算
    private int points;//等于多少积分

    public static void showDialog(Activity activity, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        MemberPointsRuleDialog.listener = listener;
        MemberPointsRuleDialog dialog = new MemberPointsRuleDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public MemberPointsRuleDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        isEnable = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_POINTS, ""));
        points = SPUtils.getInstance().getInt(Constants.POINTS_RATIO, 1);
        isCount = TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.LESS_THAN_ONE_TO_POINTS, ""));
        mBinding.ivDialogEnable.setSelected(isEnable);
        mBinding.tvDialogPoints.setText(String.valueOf(points));
        mBinding.tvDialogPoints.setVisibility(View.VISIBLE);
        mBinding.tvDialogHint.setVisibility(View.GONE);
        if (isCount) {
            mBinding.ivDialogType0.setSelected(true);
            mBinding.ivDialogType1.setSelected(false);
        } else {
            mBinding.ivDialogType0.setSelected(false);
            mBinding.ivDialogType1.setSelected(true);
        }

        ((AnimationDrawable) mBinding.ivDialogCursor.getDrawable()).start();
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.ivDialogEnable.setOnClickListener(this);
        mBinding.ivDialogType0.setOnClickListener(this);
        mBinding.ivDialogType1.setOnClickListener(this);
        mBinding.numberKeyBoardView.setDrop(false);
        mBinding.numberKeyBoardView.setMaxLength(Constants.MAX_MONEY_LENGTH);
        mBinding.numberKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var) {
                mBinding.tvDialogPoints.setText(var);
                if (TextUtils.isEmpty(var)) {
                    mBinding.tvDialogPoints.setVisibility(View.GONE);
                    mBinding.tvDialogHint.setVisibility(View.VISIBLE);
                    points = 0;
                } else {
                    mBinding.tvDialogPoints.setVisibility(View.VISIBLE);
                    mBinding.tvDialogHint.setVisibility(View.GONE);
                    points = Integer.parseInt(var);
                }
            }

            @Override
            public void onConfirm() {
                if (points <= 0) {
                    showToast(1, getRstr(R.string.input_points));
                    return;
                }
                SPUtils.getInstance().put(Constants.POINTS_RATIO, points);
                dismiss();
            }
        });
    }

    @Override
    protected DialogMemberPointsRuleBinding getViewBinding() {
        return DialogMemberPointsRuleBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.ivDialogEnable:
                //使用积分
                if (isEnable) {
                    isEnable = false;
                    SPUtils.getInstance().put(Constants.IS_USE_POINTS, "");
                } else {
                    isEnable = true;
                    SPUtils.getInstance().put(Constants.IS_USE_POINTS, Constants.IS_USE_POINTS);
                }
                mBinding.ivDialogEnable.setSelected(isEnable);
                if (listener != null) {
                    listener.onPointsRule();
                }
                break;
            case R.id.ivDialogType0:
                //四舍五入
                if (!isCount) {
                    isCount = true;
                    mBinding.ivDialogType0.setSelected(true);
                    mBinding.ivDialogType1.setSelected(false);
                    SPUtils.getInstance().put(Constants.LESS_THAN_ONE_TO_POINTS, "");
                }
                break;
            case R.id.ivDialogType1:
                //不计算
                if (isCount) {
                    isCount = false;
                    mBinding.ivDialogType0.setSelected(false);
                    mBinding.ivDialogType1.setSelected(true);
                    SPUtils.getInstance().put(Constants.LESS_THAN_ONE_TO_POINTS, Constants.LESS_THAN_ONE_TO_POINTS);
                }
                break;
        }
    }

    private static MyListener listener;

    public interface MyListener {
        void onPointsRule();
    }
}
