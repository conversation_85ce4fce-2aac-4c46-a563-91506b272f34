package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.commonlibrary.utils.StringUtils;

/**
 * Describe:图片列表（适配器）
 * Created by jingang on 2023/9/14
 */
public class ImgAdapter extends BaseAdapter<String> {
    public ImgAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_img;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        if (onItemClickListener != null) {
            holder.itemView.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position)))
                .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img))
                .into(ivImg);
    }
}
