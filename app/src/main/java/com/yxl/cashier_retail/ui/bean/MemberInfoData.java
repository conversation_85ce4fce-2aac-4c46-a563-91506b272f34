package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;

/**
 * Describe:会员详情（实体类）
 * Created by jingang on 2024/7/3
 */
public class MemberInfoData implements Serializable {
    /**
     * cusWeixin :
     * cusPicPath : image/public/moren.jpg
     * cus_level_name : 铜牌会员
     * cusName : 100867
     * cusRegeditDate : 2024-05-28
     * cusBirthday :
     * cus_balance : 0.00
     * cusQQ :
     * cusPhone : 10086701312
     * cusHeadPath : image/public/moren.jpg
     * cusEmail :
     * cus_level_discount : 0.95
     * cusId : 444269
     * shopUnique : 1536215939565
     * cusPoints : 32.0
     * cusTotal : 32.0
     * cusPassword : 474a5812b5dfcf88fc3cbff3ead0144e
     * cus_status : 1
     * validityEnd :
     * cusUnique : 100867
     * validityStart :
     * cus_remark : 11
     * cusRebate : 0.0
     * cusSex : 1
     * cusUsePoints : 0.0
     * cusType : 会,储
     * totalPoints : 32.0
     * cus_level_id : 1549
     */

    private String cusWeixin;
    private String cusPicPath;//人脸照片
    private String cus_level_name;//等级名称
    private String cusName;//名称
    private String cusRegeditDate;//注册时间
    private String cusBirthday;//生日
    private String cus_balance;//余额
    private String cusQQ;
    private String cusPhone;//手机好
    private String cusHeadPath;//头像图片
    private String cusEmail;
    private double cus_level_discount;//会员折扣
    private int cusId;
    private String shopUnique;
    private double cusPoints;//剩余积分
    private double cusTotal;//消费总金额
    private String cusPassword;//密码
    private String cus_status;//0.禁用 1.正常
    private String validityEnd;//有效期止
    private String cusUnique;//编号
    private String validityStart;//有效期始
    private String cus_remark;//备注
    private double cusRebate;//赠送金额
    private String cusSex;//0.保密 1.男 2.女
    private double cusUsePoints;//已用积分
    private String cusType;//会员类型 会 或储 或 （会,储）
    private double totalPoints;//总积分
    private int cus_level_id;//会员等级id

    public String getCusWeixin() {
        return cusWeixin;
    }

    public void setCusWeixin(String cusWeixin) {
        this.cusWeixin = cusWeixin;
    }

    public String getCusPicPath() {
        return cusPicPath;
    }

    public void setCusPicPath(String cusPicPath) {
        this.cusPicPath = cusPicPath;
    }

    public String getCus_level_name() {
        return cus_level_name;
    }

    public void setCus_level_name(String cus_level_name) {
        this.cus_level_name = cus_level_name;
    }

    public String getCusName() {
        return cusName;
    }

    public void setCusName(String cusName) {
        this.cusName = cusName;
    }

    public String getCusRegeditDate() {
        return cusRegeditDate;
    }

    public void setCusRegeditDate(String cusRegeditDate) {
        this.cusRegeditDate = cusRegeditDate;
    }

    public String getCusBirthday() {
        return cusBirthday;
    }

    public void setCusBirthday(String cusBirthday) {
        this.cusBirthday = cusBirthday;
    }

    public String getCus_balance() {
        return cus_balance;
    }

    public void setCus_balance(String cus_balance) {
        this.cus_balance = cus_balance;
    }

    public String getCusQQ() {
        return cusQQ;
    }

    public void setCusQQ(String cusQQ) {
        this.cusQQ = cusQQ;
    }

    public String getCusPhone() {
        return cusPhone;
    }

    public void setCusPhone(String cusPhone) {
        this.cusPhone = cusPhone;
    }

    public String getCusHeadPath() {
        return cusHeadPath;
    }

    public void setCusHeadPath(String cusHeadPath) {
        this.cusHeadPath = cusHeadPath;
    }

    public String getCusEmail() {
        return cusEmail;
    }

    public void setCusEmail(String cusEmail) {
        this.cusEmail = cusEmail;
    }

    public double getCus_level_discount() {
        return cus_level_discount;
    }

    public void setCus_level_discount(double cus_level_discount) {
        this.cus_level_discount = cus_level_discount;
    }

    public int getCusId() {
        return cusId;
    }

    public void setCusId(int cusId) {
        this.cusId = cusId;
    }

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public double getCusPoints() {
        return cusPoints;
    }

    public void setCusPoints(double cusPoints) {
        this.cusPoints = cusPoints;
    }

    public double getCusTotal() {
        return cusTotal;
    }

    public void setCusTotal(double cusTotal) {
        this.cusTotal = cusTotal;
    }

    public String getCusPassword() {
        return cusPassword;
    }

    public void setCusPassword(String cusPassword) {
        this.cusPassword = cusPassword;
    }

    public String getCus_status() {
        return cus_status;
    }

    public void setCus_status(String cus_status) {
        this.cus_status = cus_status;
    }

    public String getValidityEnd() {
        return validityEnd;
    }

    public void setValidityEnd(String validityEnd) {
        this.validityEnd = validityEnd;
    }

    public String getCusUnique() {
        return cusUnique;
    }

    public void setCusUnique(String cusUnique) {
        this.cusUnique = cusUnique;
    }

    public String getValidityStart() {
        return validityStart;
    }

    public void setValidityStart(String validityStart) {
        this.validityStart = validityStart;
    }

    public String getCus_remark() {
        return cus_remark;
    }

    public void setCus_remark(String cus_remark) {
        this.cus_remark = cus_remark;
    }

    public double getCusRebate() {
        return cusRebate;
    }

    public void setCusRebate(double cusRebate) {
        this.cusRebate = cusRebate;
    }

    public String getCusSex() {
        return cusSex;
    }

    public void setCusSex(String cusSex) {
        this.cusSex = cusSex;
    }

    public double getCusUsePoints() {
        return cusUsePoints;
    }

    public void setCusUsePoints(double cusUsePoints) {
        this.cusUsePoints = cusUsePoints;
    }

    public String getCusType() {
        return cusType;
    }

    public void setCusType(String cusType) {
        this.cusType = cusType;
    }

    public double getTotalPoints() {
        return totalPoints;
    }

    public void setTotalPoints(double totalPoints) {
        this.totalPoints = totalPoints;
    }

    public int getCus_level_id() {
        return cus_level_id;
    }

    public void setCus_level_id(int cus_level_id) {
        this.cus_level_id = cus_level_id;
    }
}
