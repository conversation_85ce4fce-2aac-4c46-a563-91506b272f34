package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogRefundRefuseReasonBinding;
import com.yxl.cashier_retail.ui.adapter.RefundRefuseAdapter;
import com.yxl.cashier_retail.ui.bean.RefundRefuseData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:dialog（退款-拒绝原因）
 * Created by jingang on 2024/6/19
 */
@SuppressLint({"NonConstantResourceId","StaticFieldLeak"})
public class RefundRefuseReasonDialog extends BaseDialog<DialogRefundRefuseReasonBinding> implements View.OnClickListener {
    private static Activity mActivity;

    private RefundRefuseAdapter mAdapter;
    private List<RefundRefuseData> dataList = new ArrayList<>();

    public static void showDialog(Activity activity, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        RefundRefuseReasonDialog.listener = listener;
        RefundRefuseReasonDialog dialog = new RefundRefuseReasonDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 7);
        dialog.show();
    }

    public RefundRefuseReasonDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.ivDialogClear.setOnClickListener(this);
        mBinding.tvDialogAdd.setOnClickListener(this);
        mBinding.etDialog.setOnEditorActionListener((v, actionId, event) -> {
            if (TextUtils.isEmpty(mBinding.etDialog.getText().toString().trim())) {
                showToast(1, getRstr(R.string.input_refuse_reason));
            } else {
                if (listener != null) {
                    listener.onConfirm(mBinding.etDialog.getText().toString().trim());
                    dismiss();
                }
            }
            return true;
        });
        setAdapter();
        getRefuseMsg();
    }

    @Override
    protected DialogRefundRefuseReasonBinding getViewBinding() {
        return DialogRefundRefuseReasonBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.ivDialogClear:
                //清除内容输入
                mBinding.etDialog.setText("");
                break;
            case R.id.tvDialogAdd:
                //新增
                RefundRefuseEditDialog.showDialog(mActivity, "", this::postRefuseAdd);
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new RefundRefuseAdapter(getContext());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setListener(new RefundRefuseAdapter.MyListener() {
            @Override
            public void onItemClick(View view, int position) {
                //详情
                if (listener != null) {
                    listener.onConfirm(dataList.get(position).getMsg());
                    dismiss();
                }
            }

            @Override
            public void onDelClick(View view, int position) {
                //删除
                postRefuseDel(dataList.get(position).getId(), position);
            }

            @Override
            public void onEditClick(View view, int position) {
                //编辑
                RefundRefuseEditDialog.showDialog(mActivity, dataList.get(position).getMsg(), name -> {
                    if (!name.equals(dataList.get(position).getMsg())) {
                        postRefuseEdit(dataList.get(position).getId(), name, position);
                    }
                });
            }
        });
        mBinding.smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            getRefuseMsg();
        });
        mBinding.smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 查询拒绝退款原因
     */
    private void getRefuseMsg() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        showDialog();
        RXHttpUtil.requestByFormPostAsResponseList(this, ZURL.getRefundRefuseMsg(), params, RefundRefuseData.class, new RequestListListener<RefundRefuseData>() {
            @Override
            public void onResult(List<RefundRefuseData> list) {
                hideDialog();
                mBinding.smartRefreshLayout.finishRefresh();
                dataList.clear();
                dataList.addAll(list);
                if (dataList.size() > 0) {
                    mBinding.recyclerView.setVisibility(View.VISIBLE);
                    mBinding.linEmpty.setVisibility(View.GONE);
                    mAdapter.setDataList(dataList);
                } else {
                    mBinding.recyclerView.setVisibility(View.GONE);
                    mBinding.linEmpty.setVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onError(String msg) {
                hideDialog();
                mBinding.smartRefreshLayout.finishRefresh();
                if (dataList.size() > 0) {
                    mBinding.recyclerView.setVisibility(View.VISIBLE);
                    mBinding.linEmpty.setVisibility(View.GONE);
                } else {
                    mBinding.recyclerView.setVisibility(View.GONE);
                    mBinding.linEmpty.setVisibility(View.VISIBLE);
                }
            }
        });
    }

    /**
     * 添加
     */
    private void postRefuseAdd(String msg) {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("staffId", getStaffUnique());
        params.put("msg", msg);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getRefundRefuseAdd(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        getRefuseMsg();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 修改
     *
     * @param id
     * @param msg
     * @param position
     */
    private void postRefuseEdit(int id, String msg, int position) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("msg", msg);
        params.put("delFlag", 1);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getRefundRefuseUpdate(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        dataList.get(position).setMsg(msg);
                        mAdapter.notifyItemChanged(position);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 删除
     *
     * @param id
     * @param position
     */
    private void postRefuseDel(int id, int position) {
        Map<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("delFlag", 2);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getRefundRefuseUpdate(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        dataList.remove(position);
                        mAdapter.remove(position);
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(String reason);
    }
}
