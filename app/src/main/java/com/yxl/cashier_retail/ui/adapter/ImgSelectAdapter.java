package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.graphics.BitmapFactory;
import android.view.View;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;

/**
 * Describe:选择图片（适配器）
 * Created by jingang on 2020/1/13
 */
public class ImgSelectAdapter extends BaseAdapter<String> {
    private int size;

    public void setSize(int size) {
        this.size = size;
    }

    public ImgSelectAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_img;
    }

    @Override
    public int getItemCount() {
        return super.getItemCount() + 1;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        ImageView ivDel = holder.getView(R.id.ivItemDel);
        if (position == mDataList.size()) {
            ivDel.setVisibility(View.GONE);
            ivImg.setImageBitmap(BitmapFactory.decodeResource(
                    mContext.getResources(), R.mipmap.ic_camera001));
            if (position == size) {
                ivImg.setVisibility(View.GONE);
            }
        } else {
            ivDel.setVisibility(View.VISIBLE);
            Glide.with(mContext)
                    .load(mDataList.get(position))
                    .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img))
                    .into(ivImg);
        }

        if (listener != null) {
            ivImg.setOnClickListener(v -> listener.onItemClick(v, position));
            ivDel.setOnClickListener(v -> listener.onDelClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onItemClick(View view, int position);

        void onDelClick(View view, int position);
    }
}
