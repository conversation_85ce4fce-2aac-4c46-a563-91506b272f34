package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.view.View;

import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.SPUtils;
import com.google.gson.Gson;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.MyApplication;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityShiftBinding;
import com.yxl.cashier_retail.ui.adapter.ShiftCashierAdapter;
import com.yxl.cashier_retail.ui.bean.OrderOfflineData;
import com.yxl.cashier_retail.ui.bean.SaleListUniqueData;
import com.yxl.cashier_retail.ui.bean.ShiftData;
import com.yxl.cashier_retail.ui.dialog.MenuDialog;
import com.yxl.cashier_retail.ui.dialog.ShiftStatisticsExplainDialog;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.base.BaseData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import org.litepal.LitePal;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:菜单-交班
 * Created by jingang on 2024/6/1
 */
@SuppressLint({"NonConstantResourceId", "MissingPermission", "SetTextI18n"})
public class ShiftActivity extends BaseActivity<ActivityShiftBinding> implements View.OnClickListener {
    //离线订单
    private List<OrderOfflineData> offlineList = new ArrayList<>();

    //收银记录
    private ShiftCashierAdapter cashierAdapter;
    private List<ShiftData.Data1Bean> cashierList = new ArrayList<>();

    @Override
    protected ActivityShiftBinding getViewBinding() {
        return ActivityShiftBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivLogo.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.linOrderOffline.setOnClickListener(this);
        mBinding.tvTips.setOnClickListener(this);
        mBinding.tvConfirm.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected void initData() {
        setNetwork();
        getShift();
    }

    @Override
    public void getNetwork() {
        setNetwork();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivLogo:
                //菜单
                MenuDialog.showDialog(this, type -> {
                    //0.收银 1.入库 2.查询 3.统计 4.网单 5.会员 6.补货 7.营销 8.交班 9.设置
                    if (type != 8) {
                        switch (type) {
                            case 1:
                                goToActivity(InActivity.class);
                                break;
                            case 2:
                                goToActivity(QueryActivity.class);
                                break;
                            case 3:
                                goToActivity(StatisticsActivity.class);
                                break;
                            case 4:
                                goToActivity(OrderActivity.class);
                                break;
                            case 5:
                                goToActivity(MemberActivity.class);
                                break;
                            case 6:
                                goToActivity(MallActivity.class);
                                break;
                            case 7:
                                goToActivity(MarketingActivity.class);
                                break;
                            case 9:
                                goToActivity(SettingActivity.class);
                                break;
                        }
                        finish();
                    }
                });
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.linOrderOffline:
                //离线订单
                if (isQuicklyClick()) {
                    return;
                }
                if (offlineList.size() > 0) {
                    goToActivity(OrderOfflineActivity.class);
                }
                break;
            case R.id.tvTips:
                //统计字段说明
                ShiftStatisticsExplainDialog.showDialog(this);
                break;
            case R.id.tvConfirm:
                //确认交班
                if (isQuicklyClick()) {
                    return;
                }
                showDialog();
                if (offlineList.size() > 0) {
                    getSaleListUnique();
                } else {
                    postLoginOut();
                }
                break;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        getOrderOfflineList();
    }

    /**
     * 网络监听
     */
    private void setNetwork() {
        if (MyApplication.mNetwork != null) {
            if (MyApplication.mNetwork.isConnect()) {
                switch (MyApplication.mNetwork.getConnectType()) {
                    case 1:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network001);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 2:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network002);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 3:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network003);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    default:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                        mBinding.tvNetwork.setText(getRstr(R.string.no_network));
                        break;
                }
            } else {
                mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                mBinding.tvNetwork.setText(getRstr(R.string.no_network));
            }
        } else {
            mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
            mBinding.tvNetwork.setText(getRstr(R.string.no_network));
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //收银统计
        cashierAdapter = new ShiftCashierAdapter(this);
        mBinding.rvCashier.setAdapter(cashierAdapter);
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(ShiftData data) {
        if (data.getData() != null) {
            //员工信息
            mBinding.tvName.setText(data.getData().getStaffName());
            mBinding.tvStartDate.setText(data.getData().getLogin_datetime());
            mBinding.tvEndDate.setText(TextUtils.isEmpty(data.getData().getSign_out_datetime()) ? "-" : data.getData().getSign_out_datetime());

            //营收统计
            mBinding.tvRechargeInfo.setText(getRstr(R.string.recharge_info_colon) + data.getData().getCusRecharge());
            mBinding.tvCount.setText(String.valueOf(data.getData().getSum()));
            mBinding.tvTotal.setText(DFUtils.getNum2(data.getData().getTotal()));
            mBinding.tvReceivable.setText(DFUtils.getNum2(data.getData().getReceivable()));
        }

        //收银统计
        cashierList.clear();
        if (data.getAddress() != null) {
            cashierList.addAll(data.getData1());
        }
        cashierAdapter.setDataList(cashierList);
    }

    /**
     * 获取离线订单列表
     */
    private void getOrderOfflineList() {
        List<OrderOfflineData> list = LitePal.findAll(OrderOfflineData.class);
        offlineList.clear();
        if (list != null) {
            offlineList.addAll(list);
        }
        Collections.reverse(offlineList);
        mBinding.tvOrderOffline.setText(String.valueOf(offlineList.size()));
    }

    /**
     * 交班查询
     */
    private void getShift() {
        if (!NetworkUtils.isConnected()) {
            showToast(1, getRstr(R.string.no_network_connect));
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("login_id", getLoginId());
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getShift(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        ShiftData data = new Gson().fromJson(s, ShiftData.class);
                        if (data == null) {
                            return;
                        }
                        if (data.getStatus() != 0) {
                            showToast(1, data.getMsg());
                            return;
                        }
                        setUI(data);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 退出登录（交班）
     */
    private void postLoginOut() {
        if (!NetworkUtils.isConnected()) {
            showToast(1, getRstr(R.string.no_network_connect));
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("login_id", getLoginId());
        showDialog();
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getLoginOut(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        BaseData data = new Gson().fromJson(s, BaseData.class);
                        if (data.getStatus() == 0) {
                            showToast(0, data.getMsg());
                            loginOut();
                        } else {
                            showToast(1, data.getMsg());
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 退出登录
     */
    private void loginOut() {
        SPUtils.getInstance().put(Constants.LOGIN_ID, "");
        MyApplication.getInstance().disConnect_mqtt();
        MyApplication.getInstance().disConnectSerialPort();
        AppManager.getInstance().finishAllActivity();
        goToActivity(LoginActivity.class);
    }

    /**
     * 创建订单编号
     */
    private void getSaleListUnique() {
        if (!NetworkUtils.isConnected()) {
            hideDialog();
            showToast(1, getRstr(R.string.no_network_connect));
            return;
        }
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getSaleListUniqueCreate(),
                new HashMap<>(),
                SaleListUniqueData.class,
                new RequestListener<SaleListUniqueData>() {
                    @Override
                    public void success(SaleListUniqueData data) {
                        postPayment(data.getSale_list_unique());
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 普通线下订单结算
     *
     * @param saleListUnique
     */
    private void postPayment(String saleListUnique) {
        if (!NetworkUtils.isConnected()) {
            hideDialog();
            showToast(1, getRstr(R.string.no_network_connect));
            return;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("saleListState", offlineList.get(0).getSaleListState());//付款状态 3.已付款 4.赊账
        params.put("saleListUnique", saleListUnique);//订单编号(接口创建)
        params.put("goodsPurprice", offlineList.get(0).getGoodsPurprice());//商品进价 “,”隔开
        params.put("saleListDetailPrice", offlineList.get(0).getSaleListDetailPrice());//商品售价 “,”隔开
        params.put("goods_old_price", offlineList.get(0).getGoods_old_price());//商品原价 “,”隔开
        params.put("saleListDetailCount", offlineList.get(0).getSaleListDetailCount());//商品数量 “,”隔开
        params.put("goodsName", offlineList.get(0).getGoodsName());//商品名称 “,”隔开
        params.put("goodsBarcode", offlineList.get(0).getGoodsBarcode());//商品条码 “,”隔开
        params.put("goodsId", offlineList.get(0).getGoodsId());//商品id ","隔开
        params.put("saleListTotal", offlineList.get(0).getSaleListTotal());//订单总金额
        params.put("saleListCashier", getStaffUnique());//员工id
//        params.put("saleListRemarks", );//订单备注
//        params.put("machine_num", );//机器编号
        params.put("saleListActuallyReceived", offlineList.get(0).getSaleListActuallyReceived());
        params.put("sale_list_payment", offlineList.get(0).getSale_list_payment());//支付方式 1.现金 2.支付宝 3.微信 4.银行卡 5.储值卡 8.混合支付 9.免密支付
//        params.put("machineTime", );//失败时上传当前时间
        params.put("type", offlineList.get(0).getType());//固定值2
//        params.put("wholesale_phone", );//批发客户的手机号
        params.put("saleListPayDetail", offlineList.get(0).getSaleListPayDetail());//支付详情
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getPayment(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        OrderOfflineData data = offlineList.get(0);
                        data.delete();
                        offlineList.remove(0);
                        if (offlineList.size() > 0) {
                            getSaleListUnique();
                        } else {
                            postLoginOut();
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

}
