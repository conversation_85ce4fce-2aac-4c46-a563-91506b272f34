package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogStaffBinding;
import com.yxl.cashier_retail.ui.adapter.StaffDialogAdapter;
import com.yxl.cashier_retail.ui.bean.StaffData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Describe:dialog（商品单位）
 * Created by jingang on 2024/7/1
 */
@SuppressLint("NonConstantResourceId")
public class StaffDialog extends BaseDialog<DialogStaffBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private static int id;
    private static String name, keyWords;
    private static View viewIcon;
    private final Animation openAnim, closeAnim;

    private List<StaffData> dataList = new ArrayList<>();
    private StaffDialogAdapter mAdapter;

    public static void showDialog(Activity activity, int id, String name, View viewIcon, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        StaffDialog.listener = listener;
        StaffDialog.id = id;
        StaffDialog.name = name;
        StaffDialog.viewIcon = viewIcon;
        StaffDialog dialog = new StaffDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 7);
        dialog.show();
    }

    public StaffDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        openAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_1);
        closeAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_2);
        openAnim.setFillAfter(true);
        closeAnim.setFillAfter(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.ivDialogSearchClear.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        mBinding.etDialogSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                mBinding.ivDialogSearchClear.setVisibility(TextUtils.isEmpty(keyWords) ? View.GONE : View.VISIBLE);
            }
        });
        mBinding.etDialogSearch.setOnEditorActionListener((v, actionId, event) -> {
            getStaffList();
            return true;
        });
        setAdapter();
        getStaffList();
    }

    @Override
    protected DialogStaffBinding getViewBinding() {
        return DialogStaffBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
        if (viewIcon != null) {
            viewIcon.startAnimation(openAnim);
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
        if (viewIcon != null) {
            viewIcon.startAnimation(closeAnim);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.ivDialogSearchClear:
                //请输入搜索输入
                mBinding.etDialogSearch.setText("");
                keyWords = "";
                page = 1;
                getStaffList();
                break;
            case R.id.tvDialogConfirm:
                //确认
                if (TextUtils.isEmpty(name)) {
                    showToast(1, getRstr(R.string.select_cashier));
                    return;
                }
                if (listener != null) {
                    listener.onConfirm(id, name);
                    dismiss();
                }
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new StaffDialogAdapter(getContext());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (!dataList.get(position).isSelect()) {
                for (int i = 0; i < dataList.size(); i++) {
                    if (dataList.get(i).isSelect()) {
                        dataList.get(i).setSelect(false);
                        mAdapter.notifyItemChanged(i);
                    }
                }
                dataList.get(position).setSelect(true);
                mAdapter.notifyItemChanged(position);
                id = dataList.get(position).getStaffId();
                name = dataList.get(position).getStaffName();
            }
        });
        mBinding.smartRefreshLayout.setOnRefreshListener(refreshLayout -> getStaffList());
        mBinding.smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 员工列表
     */
    private void getStaffList() {
        showDialog();
        hideSoftInput(mActivity);
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("staffId", keyWords);
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getStaffList(),
                map,
                StaffData.class,
                new RequestListListener<StaffData>() {
                    @Override
                    public void onResult(List<StaffData> list) {
                        hideDialog();
                        mBinding.smartRefreshLayout.finishRefresh();
                        dataList.clear();
                        dataList.add(new StaffData(getRstr(R.string.cashier_all), -1));
                        dataList.addAll(list);
                        mBinding.recyclerView.setVisibility(View.VISIBLE);
                        mBinding.linEmpty.setVisibility(View.GONE);
                        mAdapter.setDataList(dataList);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        mBinding.smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(int id, String name);
    }
}
