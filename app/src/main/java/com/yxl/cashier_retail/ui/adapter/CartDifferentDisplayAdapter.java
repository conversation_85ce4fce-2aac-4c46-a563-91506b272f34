package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.ui.bean.MemberData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:副屏-购物车（适配器）
 * Created by jingang on 2024/6/28
 */
public class CartDifferentDisplayAdapter extends BaseAdapter<GoodsData> {
    private MemberData memberData;

    public void setMemberData(MemberData memberData) {
        this.memberData = memberData;
    }

    public CartDifferentDisplayAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_cart_different_display;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvPrice, tvCount, tvTotal;
        tvName = holder.getView(R.id.tvItemName);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTotal = holder.getView(R.id.tvItemTotal);

        double price,//单价
                count = mDataList.get(position).getCartNum(),//数量
                salePrice = mDataList.get(position).getGoods_sale_price(),//售价
                memberPrice = TextUtils.isEmpty(mDataList.get(position).getGoods_cus_price())
                        ? 0 : Double.parseDouble(mDataList.get(position).getGoods_cus_price());//会员价
        if (mDataList.get(position).getNewPrice() > 0) {
            price = mDataList.get(position).getNewPrice();
        } else {
            if (memberData == null) {
                price = salePrice;
            } else {
                price = memberPrice;
            }
        }
        tvPrice.setText(getRstr(R.string.money) + DFUtils.getNum2(price));
        tvCount.setText("x" + DFUtils.getNum4(count));
        tvTotal.setText(getRstr(R.string.money) + DFUtils.getNum2(price * count));
        tvName.setText(mDataList.get(position).getGoods_name());
    }
}
