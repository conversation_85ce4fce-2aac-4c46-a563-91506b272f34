package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;

/**
 * Describe:商品批次列表（实体类）
 * Created by jingang on 2024/7/1
 */
public class GoodsBatchData implements Serializable {
    /**
     * goodsBatchId : 84
     * batchUnique : 1715736033696039
     * goodsProd : 2024-05-15 00:00:00
     * goodsExp : 2024-05-15 00:00:00
     * goodsCount : 2.0
     * goodsInCount : 2.0
     * outStockCount : 0.0
     * createTime : 2024-05-15 09:20:34
     * goodsInPrice : 4.5
     */

    private int goodsBatchId;
    private String batchUnique;
    private String goodsProd;//生产日期
    private String goodsExp;//到期日期
    private double goodsCount;//剩余数量
    private double goodsInCount;//入库数量
    private double outStockCount;//出库数量
    private String createTime;//入库日期
    private double goodsInPrice;//入库单价
    private double count;//输入数量

    public int getGoodsBatchId() {
        return goodsBatchId;
    }

    public void setGoodsBatchId(int goodsBatchId) {
        this.goodsBatchId = goodsBatchId;
    }

    public String getBatchUnique() {
        return batchUnique;
    }

    public void setBatchUnique(String batchUnique) {
        this.batchUnique = batchUnique;
    }

    public String getGoodsProd() {
        return goodsProd;
    }

    public void setGoodsProd(String goodsProd) {
        this.goodsProd = goodsProd;
    }

    public String getGoodsExp() {
        return goodsExp;
    }

    public void setGoodsExp(String goodsExp) {
        this.goodsExp = goodsExp;
    }

    public double getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(double goodsCount) {
        this.goodsCount = goodsCount;
    }

    public double getGoodsInCount() {
        return goodsInCount;
    }

    public void setGoodsInCount(double goodsInCount) {
        this.goodsInCount = goodsInCount;
    }

    public double getOutStockCount() {
        return outStockCount;
    }

    public void setOutStockCount(double outStockCount) {
        this.outStockCount = outStockCount;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public double getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(double goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public double getCount() {
        return count;
    }

    public void setCount(double count) {
        this.count = count;
    }
}
