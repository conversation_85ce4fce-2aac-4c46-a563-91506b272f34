package com.yxl.cashier_retail.ui.contract;

import com.yxl.cashier_retail.ui.bean.MemberData;
import com.yxl.commonlibrary.base.BasePresenter;
import com.yxl.commonlibrary.base.BaseView;

import java.util.List;

/**
 * 会员
 */
public class MemberContract {
    public interface View extends BaseView {
        /**
         * 获取会员列表成功
         *
         * @param list
         */
        void successMemberList(List<MemberData> list);
    }

    public interface Presenter extends BasePresenter<View> {
        /**
         * 会员列表
         *
         * @param keyWords   搜索关键字
         * @param searchType 1、全部信息（默认）；2、会员编号;3、手机号;4、姓名
         * @param valueType  1、完全匹配；2、模糊搜索（默认）
         */
        void getCusList(String shopUnique, String keyWords, int searchType, int valueType);
    }
}
