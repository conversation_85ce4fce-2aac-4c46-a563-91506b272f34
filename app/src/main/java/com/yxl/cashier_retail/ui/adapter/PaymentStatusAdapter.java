package com.yxl.cashier_retail.ui.adapter;

import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.yxl.cashier_retail.R;

import java.util.List;

/**
 * Describe:收款成功-支付方式（适配器）
 * Created by jingang on 2024/5/10
 */
public class PaymentStatusAdapter extends BaseQuickAdapter<String, BaseViewHolder> {

    public PaymentStatusAdapter(@Nullable List<String> data) {
        super(R.layout.item_payment_status, data);
    }
 
    @Override
    protected void convert(@NonNull BaseViewHolder holder, String s) {
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvName, tvMoney;
        tvName = holder.getView(R.id.tvItemName);
        tvMoney = holder.getView(R.id.tvItemMoney);
    }
}
