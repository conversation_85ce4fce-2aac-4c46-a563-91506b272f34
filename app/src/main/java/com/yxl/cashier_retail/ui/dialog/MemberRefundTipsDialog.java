package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogMemberRefundTipsBinding;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:dialog（会员详情-退费）
 * Created by jingang on 2024/7/3
 */
@SuppressLint("NonConstantResourceId")
public class MemberRefundTipsDialog extends BaseDialog<DialogMemberRefundTipsBinding> implements View.OnClickListener {

    public static void showDialog(Activity activity, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        MemberRefundTipsDialog.listener = listener;
        MemberRefundTipsDialog dialog = new MemberRefundTipsDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public MemberRefundTipsDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.tvDialogCancel.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
    }

    @Override
    protected DialogMemberRefundTipsBinding getViewBinding() {
        return DialogMemberRefundTipsBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.tvDialogCancel:
                //取消
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //确认
                if (listener != null) {
                    listener.onConfirm();
                    dismiss();
                }
                break;
        }
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm();
    }
}
