package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.ActivityListData;

/**
 * Describe:营销活动列表（适配器）
 * Created by jingang on 2024/6/1
 */
public class MarketingAdapter extends BaseAdapter<ActivityListData> {

    public MarketingAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_marketing;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvTime, tvTips;
        tvName = holder.getView(R.id.tvItemName);
        tvTime = holder.getView(R.id.tvItemTime);
        tvTips = holder.getView(R.id.tvItemTips);
        RecyclerView recyclerView = holder.getView(R.id.rvItem);

        tvName.setText(mDataList.get(position).getPromotionActivityName());
        tvTime.setText(mDataList.get(position).getStartTime() + "~" + mDataList.get(position).getEndTime());
        ///会员价是否参与 1:是 0:否
        tvTips.setVisibility(mDataList.get(position).getCusActivity() == 1 ? View.VISIBLE : View.GONE);

        //活动类型：1、商品折扣；2、商品满赠；3、订单促销；4、单品促销
        switch (mDataList.get(position).getType()) {
            case 2:
                if (mDataList.get(position).getPromotionGoodsGiftList() == null) {
                    recyclerView.setVisibility(View.GONE);
                } else {
                    if (mDataList.get(position).getPromotionGoodsGiftList().size() > 0) {
                        recyclerView.setVisibility(View.VISIBLE);
                        MarketingGiftAdapter giftAdapter = new MarketingGiftAdapter(mContext);
                        recyclerView.setAdapter(giftAdapter);
                        giftAdapter.setDataList(mDataList.get(position).getPromotionGoodsGiftList());
                    } else {
                        recyclerView.setVisibility(View.GONE);
                    }
                }
                break;
            case 3:
                if (mDataList.get(position).getPromotionOrderMarkdownList() == null) {
                    recyclerView.setVisibility(View.GONE);
                } else {
                    if (mDataList.get(position).getPromotionOrderMarkdownList().size() > 0) {
                        recyclerView.setVisibility(View.VISIBLE);
                        MarketingOrderAdapter orderAdapter = new MarketingOrderAdapter(mContext);
                        recyclerView.setAdapter(orderAdapter);
                        orderAdapter.setDataList(mDataList.get(position).getPromotionOrderMarkdownList());
                    } else {
                        recyclerView.setVisibility(View.GONE);
                    }
                }
                break;
            case 4:
                if (mDataList.get(position).getPromotionGoodsSingleList() == null) {
                    recyclerView.setVisibility(View.GONE);
                } else {
                    if (mDataList.get(position).getPromotionGoodsSingleList().size() > 0) {
                        recyclerView.setVisibility(View.VISIBLE);
                        MarketingSingleAdapter singleAdapter = new MarketingSingleAdapter(mContext);
                        recyclerView.setAdapter(singleAdapter);
                        singleAdapter.setDataList(mDataList.get(position).getPromotionGoodsSingleList());
                    } else {
                        recyclerView.setVisibility(View.GONE);
                    }
                }
                break;
            default:
                if (mDataList.get(position).getPromotionGoodsMarkdownList() == null) {
                    recyclerView.setVisibility(View.GONE);
                } else {
                    if (mDataList.get(position).getPromotionGoodsMarkdownList().size() > 0) {
                        recyclerView.setVisibility(View.VISIBLE);
                        MarketingGoodsAdapter goodsAdapter = new MarketingGoodsAdapter(mContext);
                        recyclerView.setAdapter(goodsAdapter);
                        goodsAdapter.setDataList(mDataList.get(position).getPromotionGoodsMarkdownList());
                    } else {
                        recyclerView.setVisibility(View.GONE);
                    }
                }
                break;
        }
    }
}
