package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogMenuBinding;

/**
 * Describe:dialog（菜单）
 * Created by jingang on 2024/5/13
 */
@SuppressLint("NonConstantResourceId")
public class MenuDialog extends BaseDialog<DialogMenuBinding> implements View.OnClickListener {

    public static void showDialog(Activity activity, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        MenuDialog.listener = listener;
        MenuDialog dialog = new MenuDialog(activity);
        dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        dialog.show();
    }

    public MenuDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.linDialog.setOnClickListener(this);
        mBinding.relDialog0.setOnClickListener(this);
        mBinding.relDialog1.setOnClickListener(this);
        mBinding.relDialog2.setOnClickListener(this);
        mBinding.relDialog3.setOnClickListener(this);
        mBinding.relDialog4.setOnClickListener(this);
        mBinding.relDialog5.setOnClickListener(this);
        mBinding.relDialog6.setOnClickListener(this);
        mBinding.relDialog7.setOnClickListener(this);
        mBinding.relDialog8.setOnClickListener(this);
        mBinding.relDialog9.setOnClickListener(this);
    }

    @Override
    protected DialogMenuBinding getViewBinding() {
        return DialogMenuBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        if (isQuicklyClick()) {
            return;
        }
        switch (v.getId()) {
            case R.id.linDialog:
                break;
            case R.id.relDialog0:
                if (listener != null) {
                    listener.onCallBack(0);
                }
                break;
            case R.id.relDialog1:
                if (listener != null) {
                    listener.onCallBack(1);
                }
                break;
            case R.id.relDialog2:
                if (listener != null) {
                    listener.onCallBack(2);
                }
                break;
            case R.id.relDialog3:
                if (listener != null) {
                    listener.onCallBack(3);
                }
                break;
            case R.id.relDialog4:
                if (listener != null) {
                    listener.onCallBack(4);
                }
                break;
            case R.id.relDialog5:
                if (listener != null) {
                    listener.onCallBack(5);
                }
                break;
            case R.id.relDialog6:
                if (listener != null) {
                    listener.onCallBack(6);
                }
                break;
            case R.id.relDialog7:
                if (listener != null) {
                    listener.onCallBack(7);
                }
                break;
            case R.id.relDialog8:
                if (listener != null) {
                    listener.onCallBack(8);
                }
                break;
            case R.id.relDialog9:
                if (listener != null) {
                    listener.onCallBack(9);
                }
                break;
        }
        dismiss();
    }

    private static MyListener listener;

    public interface MyListener {
        /**
         * @param type 0.收银 1.入库 2.查询 3.统计 4.网单 5.会员 6.补货 7.营销 8.交班 9.设置
         */
        void onCallBack(int type);
    }
}
