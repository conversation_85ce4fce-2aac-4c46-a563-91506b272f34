package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.MyApplication;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityInBinding;
import com.yxl.cashier_retail.ui.adapter.ImgAdapter;
import com.yxl.cashier_retail.ui.adapter.PurchaseGoodsAdapter;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.PurchaseInfoData;
import com.yxl.cashier_retail.ui.bean.PurchaseVoucherData;
import com.yxl.cashier_retail.ui.dialog.MenuDialog;
import com.yxl.cashier_retail.ui.dialog.PurchaseGoodsCheckCancelDialog;
import com.yxl.cashier_retail.ui.dialog.PurchaseGoodsCheckDialog;
import com.yxl.cashier_retail.ui.dialog.PurchaseVoucherAddDialog;
import com.yxl.cashier_retail.ui.dialog.PurchaseVoucherDialog;
import com.yxl.cashier_retail.ui.fragment.InGoodsFragment;
import com.yxl.cashier_retail.ui.fragment.InOutFragment;
import com.yxl.cashier_retail.ui.fragment.InPurchaseFragment;
import com.yxl.cashier_retail.ui.fragment.InBatchFragment;
import com.yxl.cashier_retail.ui.fragment.RestockFragment;
import com.yxl.cashier_retail.ui.fragment.SupplierFragment;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.utils.PermissionUtils;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:菜单-商品
 * Created by jingang on 2024/5/23
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class InActivity extends BaseActivity<ActivityInBinding> implements View.OnClickListener {
    private Fragment[] fragments;
    private InGoodsFragment goodsFragment;
    private InOutFragment outFragment;
    private InBatchFragment inBatchFragment;
    private RestockFragment replenishmentFragment;
    private InPurchaseFragment gouXFragment;
    private SupplierFragment supplierFragment;
    private int index = 0,//点击的页卡索引
            currentTabIndex = 0;//当前的页卡索引

    private String keyWordsGoods,//商品管理
            keyWordsSupplier;//供货商管理

    //购销单详情
    private String purchaseId,//购销单id
            purchaseSupplierUnique;//购销单供货商编号
    private PurchaseGoodsAdapter purchaseGoodsAdapter;
    private List<PurchaseInfoData.GoodsListBean> purchaseGoodsList = new ArrayList<>();
    private ImgAdapter imgAdapter;
    private List<String> imgList = new ArrayList<>();

    @Override
    protected ActivityInBinding getViewBinding() {
        return ActivityInBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        //禁止手势滑动
        mBinding.drawerLayout.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED);
        mBinding.ivLogo.setOnClickListener(this);
        mBinding.ivSearchClear.setOnClickListener(this);
        mBinding.tvCashier.setOnClickListener(this);
        mBinding.linGoodsManage.setOnClickListener(this);
        mBinding.linOut.setOnClickListener(this);
        mBinding.linIn.setOnClickListener(this);
        mBinding.linReplenishment.setOnClickListener(this);
        mBinding.linGouX.setOnClickListener(this);
        mBinding.linSupplier.setOnClickListener(this);

        //购销单详情
        mBinding.ivBackPurchase.setOnClickListener(this);
        mBinding.tvPurchaseCopy.setOnClickListener(this);
        mBinding.tvPurchaseConfirm.setOnClickListener(this);
        mBinding.tvPurchasePayment.setOnClickListener(this);
        mBinding.tvPurchaseVoucher.setOnClickListener(this);

        mBinding.etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                mBinding.ivSearchClear.setVisibility(TextUtils.isEmpty(s.toString().trim()) ? View.GONE : View.VISIBLE);
                getKeyWords();
            }
        });

        setFragment();
        setAdapter();
    }

    @Override
    protected void initData() {
        setNetwork();
    }

    @Override
    public void getNetwork() {
        setNetwork();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivLogo:
                //菜单
                MenuDialog.showDialog(this, type -> {
                    //0.收银 1.入库 2.查询 3.统计 4.网单 5.会员 6.补货 7.营销 8.交班 9.设置
                    if (type != 1) {
                        switch (type) {
                            case 2:
                                goToActivity(QueryActivity.class);
                                break;
                            case 3:
                                goToActivity(StatisticsActivity.class);
                                break;
                            case 4:
                                goToActivity(OrderActivity.class);
                                break;
                            case 5:
                                goToActivity(MemberActivity.class);
                                break;
                            case 6:
                                goToActivity(MallActivity.class);
                                break;
                            case 7:
                                goToActivity(MarketingActivity.class);
                                break;
                            case 8:
                                goToActivity(ShiftActivity.class);
                                break;
                            case 9:
                                goToActivity(SettingActivity.class);
                                break;
                        }
                        finish();
                    }
                });
                break;
            case R.id.ivSearchClear:
                //清除搜素
                mBinding.etSearch.setText("");
                break;
            case R.id.tvCashier:
                //收银台
                AppManager.getInstance().finishAllActivityToIgnore(MainActivity.class);
                break;
            case R.id.linGoodsManage:
                //商品管理
                index = 0;
                fragmentControl();
                break;
            case R.id.linOut:
                //批量出库
                index = 1;
                fragmentControl();
                break;
            case R.id.linIn:
                //批量入库
                index = 2;
                fragmentControl();
                break;
            case R.id.linReplenishment:
                //自采补货
                index = 3;
                fragmentControl();
                break;
            case R.id.linGouX:
                //购销单
                index = 4;
                fragmentControl();
                break;
            case R.id.linSupplier:
                //供货商管理
                index = 5;
                fragmentControl();
                break;
            case R.id.ivBackPurchase:
                //购销单详情返回
                mBinding.drawerLayout.closeDrawer(Gravity.RIGHT);
                break;
            case R.id.tvPurchaseCopy:
                //复制
                ClipboardManager cm = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
                // 创建普通字符型ClipData
                ClipData mClipData = ClipData.newPlainText("code", mBinding.tvPurchaseNo.getText().toString().trim());
                // 将ClipData内容放到系统剪贴板里。
                cm.setPrimaryClip(mClipData);
                showToast(1, getRstr(R.string.copy_to_clipboard));
                break;
            case R.id.tvPurchaseConfirm:
                //确认商品入库
                if (isQuicklyClick()) {
                    return;
                }
                if (!isAll()) {
                    showToast(1, "有商品未核对");
                    return;
                }
                postGoodsCheckAll();
                break;
            case R.id.tvPurchasePayment:
                //还款（添加单据凭证）
                if (isQuicklyClick()) {
                    return;
                }
                if (PermissionUtils.checkPermissionsGroup(this, 0)) {
                    showDialogVoucherAdd();
                } else {
                    PermissionUtils.requestPermissions(this, Constants.PERMISSION_CAMERA, 0);
                }
                break;
            case R.id.tvPurchaseVoucher:
                //查看单据凭证
                if (isQuicklyClick()) {
                    return;
                }
                getPayment();
                break;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.GOODS_LIST:
                if (index == 0) {
                    getKeyWords();
                }
                break;
            case Constants.PURCHASE_INFO:
                //购销单详情
                purchaseId = event.getTypes();
                getPurchaseInfo();
                break;
        }
    }

    /**
     * 网络监听
     */
    private void setNetwork() {
        if (MyApplication.mNetwork != null) {
            if (MyApplication.mNetwork.isConnect()) {
                switch (MyApplication.mNetwork.getConnectType()) {
                    case 1:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network001);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 2:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network002);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    case 3:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network003);
                        mBinding.tvNetwork.setText(getRstr(R.string.connected));
                        break;
                    default:
                        mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                        mBinding.tvNetwork.setText(getRstr(R.string.no_network));
                        break;
                }
            } else {
                mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
                mBinding.tvNetwork.setText(getRstr(R.string.no_network));
            }
        } else {
            mBinding.ivNetwork.setImageResource(R.mipmap.ic_network004);
            mBinding.tvNetwork.setText(getRstr(R.string.no_network));
        }
    }

    /**
     * 设置fragment
     */
    public void setFragment() {
        goodsFragment = new InGoodsFragment();
        outFragment = new InOutFragment();
        inBatchFragment = new InBatchFragment();
        replenishmentFragment = new RestockFragment();
        gouXFragment = new InPurchaseFragment();
        supplierFragment = new SupplierFragment();
        fragments = new Fragment[]{goodsFragment,
                outFragment,
                inBatchFragment,
                replenishmentFragment,
                gouXFragment,
                supplierFragment};
        setBottomColor();

        getSupportFragmentManager().beginTransaction().add(R.id.fragment_container, fragments[index]).show(fragments[index]).commit();
    }

    /**
     * 控制fragment的变化
     */
    public void fragmentControl() {
        if (currentTabIndex != index) {
            removeBottomColor();
            setBottomColor();

            FragmentTransaction trx = getSupportFragmentManager().beginTransaction();
            trx.hide(fragments[currentTabIndex]);
            if (!fragments[index].isAdded()) {
                trx.add(R.id.fragment_container, fragments[index]);
            }
            trx.show(fragments[index]).commitAllowingStateLoss();
            currentTabIndex = index;
        }
    }

    /**
     * 设置底部栏按钮变色
     */
    private void setBottomColor() {
        switch (index) {
            case 0:
                mBinding.linGoodsManage.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivGoodsManage.setImageResource(R.mipmap.ic_in_tab001);
                mBinding.tvGoodsManage.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.goods_manage));
                mBinding.linSearch.setVisibility(View.VISIBLE);
                mBinding.etSearch.setText(keyWordsGoods);
                mBinding.etSearch.setSelection(mBinding.etSearch.getText().toString().trim().length());
                break;
            case 1:
                mBinding.linOut.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivOut.setImageResource(R.mipmap.ic_out_tab001);
                mBinding.tvOut.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.batch_out));
                mBinding.linSearch.setVisibility(View.GONE);
                break;
            case 2:
                mBinding.linIn.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivIn.setImageResource(R.mipmap.ic_in_tab021);
                mBinding.tvIn.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.in_batch));
                mBinding.linSearch.setVisibility(View.GONE);
                break;
            case 3:
                mBinding.linReplenishment.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivReplenishment.setImageResource(R.mipmap.ic_mall_replenishment_tab001);
                mBinding.tvReplenishment.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.restock_self));
                mBinding.linSearch.setVisibility(View.GONE);
                break;
            case 4:
                mBinding.linGouX.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivGouX.setImageResource(R.mipmap.ic_in_tab031);
                mBinding.tvGouX.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.purchase));
                mBinding.linSearch.setVisibility(View.GONE);
                break;
            case 5:
                mBinding.linSupplier.setBackgroundResource(R.drawable.shape_green_5);
                mBinding.ivSupplier.setImageResource(R.mipmap.ic_mall_supplier_tab001);
                mBinding.tvSupplier.setTextColor(getResources().getColor(R.color.white));
                mBinding.tvTitle.setText(getRstr(R.string.supplier_manage));
                mBinding.linSearch.setVisibility(View.VISIBLE);
                mBinding.etSearch.setText(keyWordsSupplier);
                mBinding.etSearch.setSelection(mBinding.etSearch.getText().toString().trim().length());
                break;
            default:
                break;
        }
    }

    /**
     * 清除底部栏颜色
     */
    private void removeBottomColor() {
        switch (currentTabIndex) {
            case 0:
                mBinding.linGoodsManage.setBackgroundResource(0);
                mBinding.ivGoodsManage.setImageResource(R.mipmap.ic_in_tab002);
                mBinding.tvGoodsManage.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 1:
                mBinding.linOut.setBackgroundResource(0);
                mBinding.ivOut.setImageResource(R.mipmap.ic_out_tab002);
                mBinding.tvOut.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 2:
                mBinding.linIn.setBackgroundResource(0);
                mBinding.ivIn.setImageResource(R.mipmap.ic_in_tab022);
                mBinding.tvIn.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 3:
                mBinding.linReplenishment.setBackgroundResource(0);
                mBinding.ivReplenishment.setImageResource(R.mipmap.ic_mall_replenishment_tab002);
                mBinding.tvReplenishment.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 4:
                mBinding.linGouX.setBackgroundResource(0);
                mBinding.ivGouX.setImageResource(R.mipmap.ic_in_tab032);
                mBinding.tvGouX.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            case 5:
                mBinding.linSupplier.setBackgroundResource(0);
                mBinding.ivSupplier.setImageResource(R.mipmap.ic_mall_supplier_tab002);
                mBinding.tvSupplier.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
                break;
            default:
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //购销单详情-价格明细
        purchaseGoodsAdapter = new PurchaseGoodsAdapter(this);
        mBinding.rvPurchaseGoods.setAdapter(purchaseGoodsAdapter);
        purchaseGoodsAdapter.setListener((view, position) -> {
            if (purchaseGoodsList.get(position).getGoodsStatus() == 0) {
                //核对
                PurchaseGoodsCheckDialog.showDialog(this,
                        purchaseGoodsList.get(position),
                        (count, inPrice, salePrice, webPrice, cusPrice) -> {
                            postGoodsCheck(purchaseGoodsList.get(position).getDetailId(), count, inPrice,
                                    salePrice, webPrice, cusPrice,
                                    position);
                        });
            } else {
                //撤销核对
                PurchaseGoodsCheckCancelDialog.showDialog(this, purchaseGoodsList.get(position), () -> {
                    postGoodsCheckCancel(purchaseGoodsList.get(position).getDetailId(), position);
                });
            }
        });

        //购销单详情-单据凭证
        imgAdapter = new ImgAdapter(this);
        mBinding.rvPurchaseVoucher.setAdapter(imgAdapter);
        imgAdapter.setOnItemClickListener((view, position) -> {
            //查看图片
            startActivity(new Intent(this, ImgBigActivity.class)
                    .putExtra("img", (Serializable) imgList)
                    .putExtra("index", position)
            );
        });
    }

    /**
     * 搜索
     */
    private void getKeyWords() {
        //0.商品管理 5.供货商管理
        switch (index) {
            case 0:
                keyWordsGoods = mBinding.etSearch.getText().toString().trim();
                if (goodsFragment != null) {
                    goodsFragment.setKeyWords(keyWordsGoods);
                }
                break;
            case 5:
                keyWordsSupplier = mBinding.etSearch.getText().toString().trim();
                if (supplierFragment != null) {
                    supplierFragment.setKeyWords(keyWordsSupplier);
                }
                break;
        }
    }

    /**
     * 更新UI-购销单详情
     *
     * @param data
     */
    private void setUIPurchase(PurchaseInfoData data) {
        if (data == null) {
            return;
        }
        mBinding.drawerLayout.openDrawer(mBinding.linPurchase);
        //1-已提交(待收货)，2-已收货(待付款)，3-待确认，4-已完成，5-作废 6-异常
        switch (data.getStatus()) {
            case 1:
                mBinding.tvPurchaseStatus.setText(getRstr(R.string.purchase_order_status1));
                mBinding.tvPurchaseStatusTips.setText(getRstr(R.string.purchase_order_status1_tips));
                mBinding.ivPurchaseStatus.setImageResource(R.mipmap.ic_order_status001);
                mBinding.tvPurchaseConfirm.setVisibility(View.VISIBLE);
                mBinding.tvPurchasePayment.setVisibility(View.GONE);
                mBinding.tvPurchaseVoucher.setVisibility(View.GONE);
                break;
            case 2:
                mBinding.tvPurchaseStatus.setText(getRstr(R.string.purchase_order_status2));
                mBinding.tvPurchaseStatusTips.setText(getRstr(R.string.purchase_order_status2_tips));
                mBinding.ivPurchaseStatus.setImageResource(R.mipmap.ic_order_status001);
                mBinding.tvPurchaseConfirm.setVisibility(View.GONE);
                mBinding.tvPurchasePayment.setVisibility(View.VISIBLE);
                mBinding.tvPurchaseVoucher.setVisibility(View.GONE);
                break;
            case 3:
                mBinding.tvPurchaseStatus.setText(getRstr(R.string.purchase_order_status3));
                mBinding.tvPurchaseStatusTips.setText(getRstr(R.string.purchase_order_status3_tips));
                mBinding.ivPurchaseStatus.setImageResource(R.mipmap.ic_order_status001);
                mBinding.tvPurchaseConfirm.setVisibility(View.GONE);
                mBinding.tvPurchasePayment.setVisibility(View.GONE);
                mBinding.tvPurchaseVoucher.setVisibility(View.GONE);
                break;
            case 4:
                mBinding.tvPurchaseStatus.setText(getRstr(R.string.order_status6));
                mBinding.tvPurchaseStatusTips.setText(getRstr(R.string.order_status6));
                mBinding.ivPurchaseStatus.setImageResource(R.mipmap.ic_order_status001);
                mBinding.tvPurchaseConfirm.setVisibility(View.GONE);
                mBinding.tvPurchasePayment.setVisibility(View.GONE);
                mBinding.tvPurchaseVoucher.setVisibility(View.VISIBLE);
                break;
            case 5:
                mBinding.tvPurchaseStatus.setText(getRstr(R.string.order_status11));
                mBinding.tvPurchaseStatusTips.setText(getRstr(R.string.order_status11));
                mBinding.ivPurchaseStatus.setImageResource(R.mipmap.ic_order_status001);
                mBinding.tvPurchaseConfirm.setVisibility(View.GONE);
                mBinding.tvPurchasePayment.setVisibility(View.GONE);
                mBinding.tvPurchaseVoucher.setVisibility(View.GONE);
                break;
        }

        //备注
        mBinding.tvPurchaseRemarks.setText(TextUtils.isEmpty(data.getRemark()) ? "-" : data.getRemark());

        //价格明细
        if (data.getGoodsList() == null) {
            mBinding.linPurchaseGoods.setVisibility(View.GONE);
        } else {
            if (data.getGoodsList().size() > 0) {
                mBinding.linPurchaseGoods.setVisibility(View.VISIBLE);
                purchaseGoodsList.clear();
                purchaseGoodsList.addAll(data.getGoodsList());
                purchaseGoodsAdapter.setStatus(data.getStatus());
                purchaseGoodsAdapter.setDataList(purchaseGoodsList);
            } else {
                mBinding.linPurchaseGoods.setVisibility(View.GONE);
            }
        }

        //批次信息
        if (data.getBatchInfo() != null) {
            mBinding.linPurchaseBatch.setVisibility(View.VISIBLE);
            mBinding.tvPurchaseCount.setText(data.getBatchInfo().getGoodsCategory() + getRstr(R.string.classX));
            mBinding.tvPurchasePrice.setText(DFUtils.getNum2(data.getBatchInfo().getPurchaseCost()));
            mBinding.tvPurchasePayable.setText(DFUtils.getNum2(data.getBatchInfo().getAmountPayable()));
            mBinding.tvPurchaseDiscount.setText(DFUtils.getNum2(data.getBatchInfo().getSettlePref()));
            mBinding.tvPurchaseSettle.setText(DFUtils.getNum2(data.getBatchInfo().getSettledAmount()));
            mBinding.tvPurchasePending.setText(DFUtils.getNum2(data.getBatchInfo().getOutstandingAmount()));
        } else {
            mBinding.linPurchaseBatch.setVisibility(View.GONE);
        }

        //订单信息
        if (data.getOrderInfo() != null) {
            purchaseSupplierUnique = data.getOrderInfo().getSupplierUnique();
            mBinding.linPurchaseOrder.setVisibility(View.VISIBLE);
            mBinding.tvPurchaseNo.setText(TextUtils.isEmpty(data.getOrderInfo().getBillNo()) ? "-" : data.getOrderInfo().getBillNo());
            mBinding.tvPurchaseTime.setText(TextUtils.isEmpty(data.getOrderInfo().getCreateTime()) ? "-" : data.getOrderInfo().getCreateTime());
            mBinding.tvPurchaseSupplier.setText(TextUtils.isEmpty(data.getOrderInfo().getSupplierName()) ? "-" : data.getOrderInfo().getSupplierName());
            mBinding.tvPurchaseContact.setText(TextUtils.isEmpty(data.getOrderInfo().getContacts()) ? "-" : data.getOrderInfo().getContacts());
            mBinding.tvPurchaseMobile.setText(TextUtils.isEmpty(data.getOrderInfo().getSupplierPhone()) ? "-" : data.getOrderInfo().getSupplierPhone());
            mBinding.tvPurchaseAddress.setText(TextUtils.isEmpty(data.getOrderInfo().getSupplierAddress()) ? "-" : data.getOrderInfo().getSupplierAddress());
        } else {
            mBinding.linPurchaseOrder.setVisibility(View.GONE);
        }

        //单据凭证
        if (data.getVoucherPicturepath() == null) {
            mBinding.linPurchaseVoucher.setVisibility(View.GONE);
        } else {
            if (data.getVoucherPicturepath().size() > 0) {
                mBinding.linPurchaseVoucher.setVisibility(View.VISIBLE);
                imgList.clear();
                imgList.addAll(data.getVoucherPicturepath());
                imgAdapter.setDataList(imgList);
            } else {
                mBinding.linPurchaseVoucher.setVisibility(View.GONE);
            }
        }
    }

    /**
     * dialog（添加单据凭证）
     */
    private void showDialogVoucherAdd() {
        PurchaseVoucherAddDialog.showDialog(this, 0, this::postPaymentAdd);
    }

    /**
     * 判断是否全部
     */
    private boolean isAll() {
        boolean isAll = false;
        for (int i = 0; i < purchaseGoodsList.size(); i++) {
            if (purchaseGoodsList.get(i).getGoodsStatus() == 0) {
                isAll = false;
                break;//根据标记，跳出嵌套循环
            } else {
                isAll = true;
            }
        }
        return isAll;
    }

    /**
     * 购销单详情
     */
    private void getPurchaseInfo() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("id", purchaseId);
        showDialog();
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getGouXOrderInfo(),
                map,
                PurchaseInfoData.class,
                new RequestListener<PurchaseInfoData>() {
                    @Override
                    public void success(PurchaseInfoData data) {
                        hideDialog();
                        setUIPurchase(data);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 入库单个商品
     *
     * @param count     实际入库数量
     * @param inPrice   进价
     * @param salePrice 售价
     * @param webPrice  网单价
     * @param cusPrice  会员价
     */
    private void postGoodsCheck(int detailId, double count, double inPrice,
                                double salePrice, double webPrice, double cusPrice,
                                int position) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("id", purchaseId);
        map.put("detailId", detailId);
        map.put("createId", getStaffUnique());
        map.put("createBy", getStaffName());
        map.put("supplierUnique", purchaseSupplierUnique);
        map.put("goodsActualCount", count);
        map.put("goodsInPrice", inPrice);
        map.put("goodsSalePrice", salePrice);
        map.put("goodsWebSalePrice", webPrice);
        map.put("goodsCusPrice", cusPrice);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getGouXGoodsCheck(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        purchaseGoodsList.get(position).setGoodsStatus(1);
                        purchaseGoodsAdapter.notifyItemChanged(position, purchaseGoodsList.get(position));
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 撤销核对入库
     *
     * @param detailId
     * @param position
     */
    private void postGoodsCheckCancel(int detailId, int position) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("supplierUnique", purchaseSupplierUnique);
        map.put("detailId", detailId);
        map.put("createId", getStaffUnique());
        map.put("createBy", getStaffName());
        map.put("id", purchaseId);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getGouXGoodsCheckCancel(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        purchaseGoodsList.get(position).setGoodsStatus(0);
                        purchaseGoodsAdapter.notifyItemChanged(position, purchaseGoodsList.get(position));
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 入库全部商品
     */
    private void postGoodsCheckAll() {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("createId", getStaffUnique());
        map.put("createBy", getStaffName());
        map.put("id", purchaseId);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getGouXGoodsCheckAll(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        getPurchaseInfo();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 添加单据凭证
     *
     * @param money
     * @param remarks
     * @param array
     */
    private void postPaymentAdd(double money, String remarks, List array) {
        showDialog();
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("createId", getStaffUnique());
        map.put("createBy", getStaffName());
        map.put("billId", purchaseId);
        map.put("supplierUnique", purchaseSupplierUnique);
        map.put("paymentMoney", money);
        map.put("remark", remarks);
        map.put("voucherPicturepath", array);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getGouXPaymentAdd(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        getPurchaseInfo();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 查看单据凭证
     */
    private void getPayment() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        map.put("id", purchaseId);
        RXHttpUtil.requestByBodyPostAsResponse(this,
                ZURL.getGouXPayment(),
                map,
                PurchaseVoucherData.class,
                new RequestListener<PurchaseVoucherData>() {
                    @Override
                    public void success(PurchaseVoucherData data) {
                        PurchaseVoucherDialog.showDialog(InActivity.this, data);
                    }
                });
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION_CAMERA:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showToast(1, "因权限未开启，该功能无法使用，请去设置中开启。");
                } else {
                    showDialogVoucherAdd();
                }
                break;
        }
    }
}
