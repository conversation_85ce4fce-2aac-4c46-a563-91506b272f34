package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.ImageView;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.PaymentData;

import java.util.List;

/**
 * Describe:支付方式-开关（适配器）
 * Created by jingang on 2024/5/15
 */
public class PaymentSwitchAdapter extends BaseAdapter<PaymentData> {

    public PaymentSwitchAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_payment_switch;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        ivImg.setSelected(mDataList.get(position).isSelect());
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivIcon, ivImg;
        TextView tvName, tvTips;
        ivIcon = holder.getView(R.id.ivItemIcon);
        tvName = holder.getView(R.id.tvItemName);
        tvTips = holder.getView(R.id.tvItemTips);
        ivImg = holder.getView(R.id.ivItemImg);

        //1.现金 2.支付宝 3.微信 4.银行卡 5.人脸 6.储值卡 7.组合 8.商品分类
        switch (mDataList.get(position).getPaymentId()) {
            case 2:
                ivIcon.setImageResource(R.mipmap.ic_payment_img012);
                tvName.setText(mContext.getResources().getString(R.string.payment_alipay));
                tvTips.setText(mContext.getResources().getString(R.string.payment_alipay_tips));
                break;
            case 3:
                ivIcon.setImageResource(R.mipmap.ic_payment_img013);
                tvName.setText(mContext.getResources().getString(R.string.payment_wechat));
                tvTips.setText(mContext.getResources().getString(R.string.payment_wechat_tips));
                break;
            case 4:
                ivIcon.setImageResource(R.mipmap.ic_payment_img014);
                tvName.setText(mContext.getResources().getString(R.string.payment_bank_card));
                tvTips.setText(mContext.getResources().getString(R.string.payment_bank_card_tips));
                break;
            case 5:
                ivIcon.setImageResource(R.mipmap.ic_payment_img015);
                tvName.setText(mContext.getResources().getString(R.string.payment_face));
                tvTips.setText(mContext.getResources().getString(R.string.payment_face_tips));
                break;
            case 6:
                ivIcon.setImageResource(R.mipmap.ic_payment_img016);
                tvName.setText(mContext.getResources().getString(R.string.payment_stored_card));
                tvTips.setText(mContext.getResources().getString(R.string.payment_stored_card_tips));
                break;
            case 7:
                ivIcon.setImageResource(R.mipmap.ic_payment_img017);
                tvName.setText(mContext.getResources().getString(R.string.payment_combination));
                tvTips.setText(mContext.getResources().getString(R.string.payment_combination_tips));
                break;
            case 8:
                ivIcon.setImageResource(R.mipmap.ic_payment_img018);
                tvName.setText(mContext.getResources().getString(R.string.goods_manage));
                tvTips.setText(mContext.getResources().getString(R.string.goods_manage_tips));
                break;
            default:
                ivIcon.setImageResource(R.mipmap.ic_payment_img011);
                tvName.setText(mContext.getResources().getString(R.string.payment_cash));
                tvTips.setText(mContext.getResources().getString(R.string.payment_cash_tips));
                break;
        }
        ivImg.setSelected(mDataList.get(position).isSelect());
        if (onItemClickListener != null) {
            ivImg.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }
    }
}
