package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;

/**
 * Describe:经营统计-热销商品（适配器）
 * Created by jingang on 2024/8/3
 */
public class StatisticsHotSalesAdapter extends BaseAdapter<String> {

    public StatisticsHotSalesAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_statistics_hot_sales;
    }

    @Override
    public int getItemCount() {
        return 10;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvCount;
        tvName = holder.getView(R.id.tvItemName);
        tvCount = holder.getView(R.id.tvItemCount);
    }
}
