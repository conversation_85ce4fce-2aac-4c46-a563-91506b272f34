package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:商品列表管理（适配器）
 * Created by jingang on 2024/7/10
 */
public class GoodsManageAdapter extends BaseAdapter<GoodsData> {
    private String virtual_kind_unique;

    public void setVirtual_kind_unique(String virtual_kind_unique) {
        this.virtual_kind_unique = virtual_kind_unique;
    }

    public GoodsManageAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_manage;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName, tvBarcode, tvShofar, tvPrice, tvUnit, tvStock, tvHouse;
        ImageView ivScale, ivDel;
        tvName = holder.getView(R.id.tvItemName);
        tvBarcode = holder.getView(R.id.tvItemBarcode);
        tvShofar = holder.getView(R.id.tvItemShofar);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvUnit = holder.getView(R.id.tvItemUnit);
        tvStock = holder.getView(R.id.tvItemStock);
        tvHouse = holder.getView(R.id.tvItemHouse);
        ivScale = holder.getView(R.id.ivItemScale);
        ivDel = holder.getView(R.id.ivItemDel);
        ivDel.setVisibility(TextUtils.isEmpty(virtual_kind_unique) ? View.GONE : View.VISIBLE);

        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundResource(R.drawable.shape_green_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
            tvBarcode.setTextColor(mContext.getResources().getColor(R.color.white));
            tvShofar.setTextColor(mContext.getResources().getColor(R.color.white));
            tvPrice.setTextColor(mContext.getResources().getColor(R.color.white));
            tvUnit.setTextColor(mContext.getResources().getColor(R.color.white));
            tvStock.setBackgroundResource(R.drawable.shape_black_tm_left_2);
            tvStock.setTextColor(mContext.getResources().getColor(R.color.white));
            tvHouse.setBackgroundResource(R.drawable.shape_white_right_2);
            tvHouse.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
        } else {
            lin.setBackgroundResource(R.drawable.shape_white_5);
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
            tvBarcode.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
            tvShofar.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvPrice.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvUnit.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.color_666));
            tvStock.setBackgroundResource(R.drawable.shape_green_tm_left_2);
            tvStock.setTextColor(mContext.getResources().getColor(R.color.black));
            tvHouse.setBackgroundResource(R.drawable.shape_green_right_2);
            tvHouse.setTextColor(mContext.getResources().getColor(R.color.white));
        }

        //0.标品 1.称重
        ivScale.setVisibility(mDataList.get(position).getGoodsChengType() == 0 ? View.GONE : View.VISIBLE);
        tvName.setText(mDataList.get(position).getGoods_name());
        tvBarcode.setText(mDataList.get(position).getGoods_barcode());
        tvPrice.setText(DFUtils.getNum2(mDataList.get(position).getGoods_sale_price()));
        tvUnit.setText(TextUtils.isEmpty(mDataList.get(position).getGoods_unit()) ? "" : "/" + mDataList.get(position).getGoods_unit());
        tvStock.setText(DFUtils.getNum4(mDataList.get(position).getGoods_count()));

        if (listener != null) {
            holder.itemView.setOnClickListener(v -> listener.onItemClick(position));
            ivDel.setOnClickListener(v -> listener.onDelClick(position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        /**
         * 详情
         *
         * @param position
         */
        void onItemClick(int position);

        /**
         * 删除
         *
         * @param position
         */
        void onDelClick(int position);
    }
}
