package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.GoodsStockReasonData;

/**
 * Describe:商品出入库-原因（适配器）
 * Created by jingang on 2024/7/1
 */
public class GoodsStockReasonAdapter extends BaseAdapter<GoodsStockReasonData.ListBean> {

    public GoodsStockReasonAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_goods_stock_reason;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName = holder.getView(R.id.tvItemName);
        tvName.setText(mDataList.get(position).getDictLabel());
        if (mDataList.get(position).isSelect()) {
            tvName.setBackgroundResource(R.drawable.shape_green_tm_5);
        } else {
            tvName.setBackgroundResource(R.drawable.shape_green_kuang_5);
        }
    }
}
