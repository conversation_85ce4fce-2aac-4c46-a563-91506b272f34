package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogCatePcBinding;
import com.yxl.cashier_retail.ui.adapter.CatePcDialogAdapter;
import com.yxl.cashier_retail.ui.bean.CatePcData;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.litepal.LitePal;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:dialog（首页分类列表）
 * Created by jingang on 2024/8/21
 */
@SuppressLint("NonConstantResourceId")
public class CatePcDialog extends BaseDialog<DialogCatePcBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private static List<CatePcData> dataList;

    private CatePcDialogAdapter mAdapter;
    private int id;
    private String name, unique;

    public static void showDialog(Activity activity, List<CatePcData> dataList) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        CatePcDialog.dataList = dataList;
        CatePcDialog dialog = new CatePcDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 10 * 7, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public CatePcDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected DialogCatePcBinding getViewBinding() {
        return DialogCatePcBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //新增分类
                id = 0;
                name = "";
                unique = "";
                showDialogEdit();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new CatePcDialogAdapter(getContext());
        mBinding.recyclerView.setAdapter(mAdapter);
        if (dataList != null) {
            mAdapter.setDataList(dataList);
        }
        mAdapter.setListener(new CatePcDialogAdapter.MyListener() {
            @Override
            public void onItemClick(int position) {
                //编辑
                id = dataList.get(position).getGoodsKindInventedId();
                name = dataList.get(position).getGoods_kind_name();
                unique = dataList.get(position).getGoods_kind_unique();
                showDialogEdit();
            }

            @Override
            public void onDelClick(int position) {
                //删除
                IAlertDialog.showDialog(mActivity, getRstr(R.string.confirm_cate_del), getRstr(R.string.confirm), (dialog, which) -> {
                    postCateEdit(2, dataList.get(position).getGoodsKindInventedId(),
                            dataList.get(position).getGoods_kind_name(),
                            dataList.get(position).getGoods_kind_unique());
                });
            }
        });
    }

    /**
     * dialog(首页虚拟分类编辑)
     */
    private void showDialogEdit() {
        CatePcEditDialog.showDialog(mActivity, id, name, unique, (type, id, name, unique) -> {
            postCateEdit(type, id, name, unique);
        });
    }

    /**
     * 虚拟分类新增、删除、编辑
     */
    private void postCateEdit(int type, int id, String name, String unique) {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        //0.新增 1.编辑 2.删除
        switch (type) {
            case 1:
                map.put("goodsKindName", name);
                map.put("goodsKindInventedId", id);
                map.put("goodsKindUnique", unique);
                break;
            case 2:
                map.put("goodsKindInventedId", id);
                map.put("goodsKindUnique", unique);
                map.put("validType", 0);
                break;
            default:
                map.put("goodsKindName", name);
                break;
        }

        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getCatePcEdit(),
                map,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        getCatePcList();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }

    /**
     * 虚拟分类列表
     */
    private void getCatePcList() {
        Map<String, Object> map = new HashMap<>();
        map.put("shopUnique", getShopUnique());
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getCatePcList(),
                map,
                CatePcData.class,
                new RequestListListener<CatePcData>() {
                    @Override
                    public void onResult(List<CatePcData> list) {
                        if (LitePal.findFirst(CatePcData.class) != null) {
                            LitePal.deleteAll(CatePcData.class);
                        }
                        LitePal.saveAll(list);
                        dataList.clear();
                        dataList.addAll(list);
                        mAdapter.setDataList(dataList);
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.CATE_LIST));
                    }
                });
    }

}
