package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.view.View;

import com.google.gson.Gson;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmQueryOrderBinding;
import com.yxl.cashier_retail.ui.adapter.QueryOrderAdapter;
import com.yxl.cashier_retail.ui.adapter.QueryOrderGoodsAdapter;
import com.yxl.cashier_retail.ui.bean.ConditionData;
import com.yxl.cashier_retail.ui.bean.QueryOrderInfoData;
import com.yxl.cashier_retail.ui.bean.QueryOrderListData;
import com.yxl.cashier_retail.ui.dialog.DateStartEndDialog;
import com.yxl.cashier_retail.ui.dialog.OrderRefundGoodsDialog;
import com.yxl.cashier_retail.ui.popupwindow.ConditionPop;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.utils.DateUtils;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:查询-查订单
 * Created by jingang on 2024/5/20
 */
@SuppressLint({"NonConstantResourceId", "NotifyDataSetChanged", "SetTextI18n"})
public class QueryOrderFragment extends BaseFragment<FmQueryOrderBinding> implements View.OnClickListener {
    private boolean isOpen;//是否展示更多信息
    private String startDate, endDate, keyWords;

    //筛选条件（收款方式）
    private List<ConditionData> conditionList = new ArrayList<>();
    private int conditionId;//1.现金 2.支付宝 3.微信 4.银行卡 5.储值卡 8.混合支付 10.积分兑换 100.金圈平台 全部传空

    //订单列表
    private QueryOrderAdapter mAdapter;
    private List<QueryOrderListData.DataBean> dataList = new ArrayList<>();
    private String saleListUnique;
    private int pos,//订单列表下标
            saleListPayment;//支付方式

    private QueryOrderInfoData.DataBean infoData;//订单详情
    //商品列表
    private QueryOrderGoodsAdapter goodsAdapter;
    private List<QueryOrderInfoData.DataBean.ListDetailBean> goodsList = new ArrayList<>();

    /**
     * 搜索
     *
     * @param keyWords
     */
    public void setKeyWords(String keyWords) {
        this.keyWords = keyWords;
        page = 1;
        getOrderList();
    }

    @Override
    protected FmQueryOrderBinding getViewBinding() {
        return FmQueryOrderBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.tvTotal.setText(getRstr(R.string.business_money_colon) + "0");
        mBinding.tvCount.setText(getRstr(R.string.order_count_colon) + "0");
        mBinding.tvProfit.setText(getRstr(R.string.order_profit_colon) + "0");
        mBinding.tvDebt.setText(getRstr(R.string.debt_colon) + "0");
        mBinding.tvWechat.setText(getRstr(R.string.wechat_colon) + "0");
        mBinding.tvNoPwd.setText(getRstr(R.string.no_password_colon) + "0");
        mBinding.tvCash.setText(getRstr(R.string.cash_colon) + "0");
        mBinding.tvAlipay.setText(getRstr(R.string.alipay_colon) + "0");
        mBinding.tvBank.setText(getRstr(R.string.bank_card_colon) + "0");
        mBinding.tvApplet.setText(getRstr(R.string.online_income_colon) + "0");
        mBinding.tvRecharge.setText(getRstr(R.string.member_recharge_colon) + "0");
        mBinding.tvConsumption.setText(getRstr(R.string.member_consumption_colon) + "0");
        mBinding.tvPoints.setText(getRstr(R.string.points_goods_colon) + "0");
        mBinding.tvRefund.setText(getRstr(R.string.refund_colon) + "0");
        mBinding.tvNoCredit.setText(getRstr(R.string.to_be_credited_colon) + "0");
        mBinding.tvWithdrawal.setText(getRstr(R.string.withdrawn_colon) + "0");
        mBinding.tvNoWithdrawal.setText(getRstr(R.string.pending_withdrawn_colon) + "0");
        mBinding.tvCouponsPlat.setText(getRstr(R.string.platform_coupons_colon) + "0");
        mBinding.tvBeansPlat.setText(getRstr(R.string.platform_beans_colon) + "0");
        mBinding.tvCouponsShop.setText(getRstr(R.string.shop_coupons_colon) + "0");
        mBinding.tvBeansShop.setText(getRstr(R.string.shop_beans_colon) + "0");
        mBinding.linType.setOnClickListener(this);
        mBinding.linStartDate.setOnClickListener(this);
        mBinding.linEndDate.setOnClickListener(this);
        mBinding.linOpen.setOnClickListener(this);
        mBinding.tvPrint.setOnClickListener(this);
        mBinding.tvOrderRefund.setOnClickListener(this);
        setAdapter();
    }

    @Override
    protected void initData() {
        conditionList.clear();
        conditionList.add(new ConditionData(0, getRstr(R.string.collection_type), true));
        conditionList.add(new ConditionData(1, getRstr(R.string.cash), false));
        conditionList.add(new ConditionData(2, getRstr(R.string.alipay), false));
        conditionList.add(new ConditionData(3, getRstr(R.string.wechat), false));
        conditionList.add(new ConditionData(4, getRstr(R.string.bank_card), false));
        conditionList.add(new ConditionData(5, getRstr(R.string.stored_card), false));
        conditionList.add(new ConditionData(8, getRstr(R.string.combination), false));
        conditionList.add(new ConditionData(10, getRstr(R.string.points_exchange), false));
        conditionList.add(new ConditionData(100, getRstr(R.string.jinquan_plat), false));

        startDate = DateUtils.getOldDate(0);

        endDate = DateUtils.getOldDate(0);
        mBinding.tvStartDate.setText(startDate);
        mBinding.tvEndDate.setText(endDate);
        getOrderList();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.linType:
                //收款方式
                ConditionPop.showDialog(mContext, mBinding.ivType, v, mBinding.linType.getMeasuredWidth(), conditionList, conditionId, data -> {
                    conditionId = data.getId();
                    mBinding.tvType.setText(data.getName());
                    getOrderList();
                });
                break;
            case R.id.linStartDate:
                //开始日期
                showDialogDate(0, mBinding.ivStartDate);
                break;
            case R.id.linEndDate:
                //结束日期
                showDialogDate(1, mBinding.ivEndDate);
                break;
            case R.id.linOpen:
                //展开/收起更多信息
                isOpen = !isOpen;
                if (isOpen) {
                    mBinding.linMore.setVisibility(View.VISIBLE);
                    mBinding.tvOpen.setText(getRstr(R.string.hide_more_info));
                } else {
                    mBinding.linMore.setVisibility(View.GONE);
                    mBinding.tvOpen.setText(getRstr(R.string.show_more_info));
                }
                break;
            case R.id.tvPrint:
                //打印小票
                if (isQuicklyClick()) {
                    return;
                }
                showToast(1, "暂未开发");
                break;
            case R.id.tvOrderRefund:
                //订单退款
                if (isQuicklyClick()) {
                    return;
                }
                if (infoData == null) {
                    return;
                }
                OrderRefundGoodsDialog.showDialog(getActivity(), infoData, saleListPayment, () -> {
                    if (dataList.size() > pos) {
                        dataList.get(pos).setReturnType(2);
                        mAdapter.notifyItemChanged(pos, dataList);
                    }
                    getOrderInfo();
                });
                break;
        }
    }

    /**
     * dialog（选择日期时间）
     */
    private void showDialogDate(int type, View view) {
        DateStartEndDialog.showDialog(getActivity(), view, startDate, endDate, type, (startDate, endDate) -> {
            this.startDate = startDate;
            this.endDate = endDate;
            mBinding.tvStartDate.setText(startDate);
            mBinding.tvEndDate.setText(endDate);
            getOrderList();
        });
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        //订单列表
        mAdapter = new QueryOrderAdapter(getActivity());
        mBinding.vSmartrefreshlayout.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (!dataList.get(position).isSelect()) {
                for (int i = 0; i < dataList.size(); i++) {
                    if (dataList.get(i).isSelect()) {
                        dataList.get(i).setSelect(false);
                        mAdapter.notifyItemChanged(i);
                    }
                }
                dataList.get(position).setSelect(true);
                mAdapter.notifyItemChanged(position);
                pos = position;
                saleListUnique = dataList.get(position).getSale_list_unique();
                saleListPayment = dataList.get(position).getSaleListPayment();
                getOrderInfo();
            }
        });
        mBinding.vSmartrefreshlayout.smartRefreshLayout.setOnRefreshListener(refreshLayout -> getOrderList());
        mBinding.vSmartrefreshlayout.smartRefreshLayout.setEnableLoadMore(false);

        //商品列表
        goodsAdapter = new QueryOrderGoodsAdapter(getActivity());
        mBinding.rvGoods.setAdapter(goodsAdapter);
    }

    /**
     * 更新UI
     *
     * @param data
     */
    private void setUI(QueryOrderListData data) {
        //订单列表
        dataList.clear();
        if (data.getData() != null) {
            dataList.addAll(data.getData());
        }
        if (dataList.size() > 0) {
            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.VISIBLE);
            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.GONE);
            mAdapter.setDataList(dataList);
            pos = 0;
            dataList.get(0).setSelect(true);
            saleListUnique = dataList.get(0).getSale_list_unique();
            saleListPayment = dataList.get(0).getSaleListPayment();
            getOrderInfo();
        } else {
            mBinding.vSmartrefreshlayout.recyclerView.setVisibility(View.GONE);
            mBinding.vSmartrefreshlayout.linEmpty.setVisibility(View.VISIBLE);
        }

        if (data.getAddress() != null) {
            for (int i = 0; i < data.getAddress().size(); i++) {
                String method = data.getAddress().get(i).getPay_method();
                //1.现金 2.支付宝 3.微信 4.银行卡 5.储值卡 6.美团 7.饿了吗 9.免密 10.积分
                //11.百货豆 -1.赊账 -2.订单数量 -3.营业额 -4.小程序 -5.订单利润
                if (!TextUtils.isEmpty(method)) {
                    double money = data.getAddress().get(i).getPayMoney();
                    switch (data.getAddress().get(i).getPay_method()) {
                        case "-3":
                            mBinding.tvTotal.setText(getRstr(R.string.business_money_colon) + DFUtils.getNum4(money));
                            break;
                        case "-2":
                            mBinding.tvCount.setText(getRstr(R.string.order_count_colon) + DFUtils.getNum4(money));
                            break;
                        case "-5":
                            mBinding.tvProfit.setText(getRstr(R.string.order_profit_colon) + DFUtils.getNum4(money));
                            break;
                        case "-1":
                            mBinding.tvDebt.setText(getRstr(R.string.debt_colon) + DFUtils.getNum4(money));
                            break;
                        case "3":
                            mBinding.tvWechat.setText(getRstr(R.string.wechat_colon) + DFUtils.getNum4(money));
                            break;
                        case "9":
                            mBinding.tvNoPwd.setText(getRstr(R.string.no_password_colon) + DFUtils.getNum4(money));
                            break;
                        case "1":
                            mBinding.tvCash.setText(getRstr(R.string.cash_colon) + DFUtils.getNum4(money));
                            break;
                        case "2":
                            mBinding.tvAlipay.setText(getRstr(R.string.alipay_colon) + DFUtils.getNum4(money));
                            break;
                        case "4":
                            mBinding.tvBank.setText(getRstr(R.string.bank_card_colon) + DFUtils.getNum4(money));
                            break;
                        case "-4":
                            mBinding.tvApplet.setText(getRstr(R.string.online_income_colon) + DFUtils.getNum4(money));
                            break;
                    }
                }
            }
        }

        if (data.getCus_data() != null) {
            mBinding.tvRecharge.setText(getRstr(R.string.member_recharge_colon) + DFUtils.getNum4(data.getCus_data().getSum_recharge_money()));
            mBinding.tvConsumption.setText(getRstr(R.string.member_consumption_colon) + DFUtils.getNum4(data.getCus_data().getSum_sale_money()));
            mBinding.tvPoints.setText(getRstr(R.string.points_goods_colon) + DFUtils.getNum4(data.getCus_data().getSum_point_goods_money()));
            mBinding.tvRefund.setText(getRstr(R.string.refund_colon) + DFUtils.getNum4(data.getCus_data().getSum_return_money()));
            mBinding.tvNoCredit.setText(getRstr(R.string.to_be_credited_colon) + DFUtils.getNum4(data.getCus_data().getNot_arrived_money()));
            mBinding.tvWithdrawal.setText(getRstr(R.string.withdrawn_colon) + DFUtils.getNum4(data.getCus_data().getCash_money()));
            mBinding.tvNoWithdrawal.setText(getRstr(R.string.pending_withdrawn_colon) + DFUtils.getNum4(data.getCus_data().getNot_cash_money()));
            mBinding.tvCouponsPlat.setText(getRstr(R.string.platform_coupons_colon) + DFUtils.getNum4(data.getCus_data().getPlat_coupon_money()));
            mBinding.tvBeansPlat.setText(getRstr(R.string.platform_beans_colon) + DFUtils.getNum4(data.getCus_data().getPlat_beans()));
            mBinding.tvCouponsShop.setText(getRstr(R.string.shop_coupons_colon) + DFUtils.getNum4(data.getCus_data().getShop_coupon_money()));
            mBinding.tvBeansShop.setText(getRstr(R.string.shop_beans_colon) + DFUtils.getNum4(data.getCus_data().getShop_beans()));
        }
    }

    /**
     * 更新UI-订单详情
     *
     * @param data
     */
    private void setUIInfo(QueryOrderInfoData.DataBean data) {
        if (data == null) {
            mBinding.tvNothing.setVisibility(View.VISIBLE);
            mBinding.linInfo.setVisibility(View.GONE);
            return;
        }
        infoData = data;
        mBinding.tvNothing.setVisibility(View.GONE);
        mBinding.linInfo.setVisibility(View.VISIBLE);
        mBinding.tvOrderNo.setText(data.getSale_list_unique());
        mBinding.tvOrderTotal.setText(DFUtils.getNum2(data.getSale_list_total()));
        mBinding.tvOrderTime.setText(TextUtils.isEmpty(data.getSale_list_datetime()) ? "-" : data.getSale_list_datetime());
        mBinding.tvOrderCoupons.setText("-" + DFUtils.getNum2(data.getCoupon_amount()));
        mBinding.tvOrderStatus.setText(data.getSale_list_handlestate());
        mBinding.tvOrderInfo.setText(data.getPayDetailStr());
        //商品列表
        goodsList.clear();
        if (data.getListDetail() != null) {
            goodsList.addAll(data.getListDetail());
        }
        goodsAdapter.setDataList(goodsList);

        //4.已完成 6.已收货
        if (TextUtils.isEmpty(data.getSale_list_handlestatecode())) {
            mBinding.tvOrderRefund.setVisibility(View.GONE);
        } else {
            switch (data.getSale_list_handlestatecode()) {
                case "4":
                case "6":
                    mBinding.tvOrderRefund.setVisibility(View.VISIBLE);
                    break;
                default:
                    mBinding.tvOrderRefund.setVisibility(View.GONE);
                    break;
            }
        }
    }

    /**
     * 订单列表
     */
    private void getOrderList() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("staff_id", getStaffUnique());
        params.put("startTime", startDate + " 00:00");
        params.put("endTime", endDate + " 23:59");
        params.put("msg", keyWords);
//        params.put("queryType", );//模糊查询：1.订单编号 2.会员手机号 3.会员姓名 4.会员编号 5.包含1-4
        params.put("source_type", 1);//1.安卓
        if (conditionId != 0) {
            params.put("sale_list_payment", conditionId);//1.现金 2.支付宝 3.微信 4.银行卡 5.储值卡 8.混合支付 10.积分兑换 100.金圈平台 全部传空
        }
        showDialog();
        RXHttpUtil.requestByFormPostAsOriginalResponse(getActivity(), ZURL.getQueryOrderList(), params, new RequestListener<String>() {
            @Override
            public void success(String s) {
                hideDialog();
                mBinding.vSmartrefreshlayout.smartRefreshLayout.finishRefresh();
                QueryOrderListData data = new Gson().fromJson(s, QueryOrderListData.class);
                if (data == null) {
                    return;
                }
                if (data.getStatus() != 0) {
                    showToast(1, data.getMsg());
                    return;
                }
                setUI(data);
            }

            @Override
            public void onError(String msg) {
                hideDialog();
                showToast(1, msg);
                mBinding.vSmartrefreshlayout.smartRefreshLayout.finishRefresh();
            }
        });
    }

    /**
     * 订单详情
     */
    private void getOrderInfo() {
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("sale_list_unique", saleListUnique);
        showDialog();
        RXHttpUtil.requestByFormPostAsOriginalResponse(getActivity(), ZURL.getQueryOrderInfo(), params, new RequestListener<String>() {
            @Override
            public void success(String s) {
                hideDialog();
                QueryOrderInfoData data = new Gson().fromJson(s, QueryOrderInfoData.class);
                if (data == null) {
                    return;
                }
                if (data.getStatus() != 0) {
                    showToast(1, data.getMsg());
                    setUIInfo(null);
                    return;
                }
                setUIInfo(data.getData());
            }

            @Override
            public void onError(String msg) {
                hideDialog();
                showToast(1, msg);
                setUIInfo(null);
            }
        });
    }

}
