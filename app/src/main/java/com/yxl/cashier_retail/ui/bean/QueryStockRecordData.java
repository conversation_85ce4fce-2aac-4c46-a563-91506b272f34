package com.yxl.cashier_retail.ui.bean;

import java.util.List;

/**
 * Describe:查询-出入库记录（实体类）
 * Created by jingang on 2025/1/6
 */
public class QueryStockRecordData {
    /**
     * status : 1
     * msg : 查询成功！
     * data : [{"stockType":"出库","stockSource":"订单修改","stockPrice":5,"goodsCount":1,"stockTypeCode":2,"stockTime":"2025-01-04 11:20:18","stockId":10639,"staffName":"益农社服务员","auditStatus":1,"stockOrigin":"PC收银机","originalCount":95,"stockTotal":5,"goodsName":"白雪可换囊直液式钢笔","listUnique":"1735960798681186","goodsBarcode":"6920024540044","goodsStandard":"","stockCount":94},{"stockType":"出库","stockSource":"订单修改","stockPrice":2,"goodsCount":1,"stockTypeCode":2,"stockTime":"2025-01-04 11:20:18","stockId":10637,"staffName":"益农社服务员","auditStatus":1,"stockOrigin":"PC收银机","originalCount":80,"stockTotal":2,"goodsName":"墨囊蓝","listUnique":"1735960798681186","goodsBarcode":"6920024535057","goodsStandard":"\"\"","stockCount":79},{"stockType":"出库","stockSource":"订单修改","stockPrice":5,"goodsCount":1,"stockTypeCode":2,"stockTime":"2025-01-04 11:20:18","stockId":10638,"staffName":"益农社服务员","auditStatus":1,"stockOrigin":"PC收银机","originalCount":93,"stockTotal":5,"goodsName":"白雪钢笔1","listUnique":"1735960798681186","goodsBarcode":"6920024536320","goodsStandard":"","stockCount":92},{"stockType":"出库","stockSource":"订单修改","stockPrice":2,"goodsCount":1,"stockTypeCode":2,"stockTime":"2025-01-04 11:15:18","stockId":10634,"staffName":"益农社服务员","auditStatus":1,"stockOrigin":"PC收银机","originalCount":81,"stockTotal":2,"goodsName":"墨囊蓝","listUnique":"1735960488924686","goodsBarcode":"6920024535057","goodsStandard":"\"\"","stockCount":80},{"stockType":"出库","stockSource":"订单修改","stockPrice":5,"goodsCount":1,"stockTypeCode":2,"stockTime":"2025-01-04 11:15:18","stockId":10635,"staffName":"益农社服务员","auditStatus":1,"stockOrigin":"PC收银机","originalCount":94,"stockTotal":5,"goodsName":"白雪钢笔1","listUnique":"1735960488924686","goodsBarcode":"6920024536320","goodsStandard":"","stockCount":93},{"stockType":"出库","stockSource":"订单修改","stockPrice":5,"goodsCount":1,"stockTypeCode":2,"stockTime":"2025-01-04 11:15:18","stockId":10636,"staffName":"益农社服务员","auditStatus":1,"stockOrigin":"PC收银机","originalCount":96,"stockTotal":5,"goodsName":"白雪可换囊直液式钢笔","listUnique":"1735960488924686","goodsBarcode":"6920024540044","goodsStandard":"","stockCount":95}]
     */

    private int status;
    private String msg;
    private List<DataBean> data;
    private ObjectBean object;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public ObjectBean getObject() {
        return object;
    }

    public void setObject(ObjectBean object) {
        this.object = object;
    }

    public static class DataBean {
        /**
         * stockType : 出库
         * stockSource : 订单修改
         * stockPrice : 5.0
         * goodsCount : 1.0
         * stockTypeCode : 2
         * stockTime : 2025-01-04 11:20:18
         * stockId : 10639
         * staffName : 益农社服务员
         * auditStatus : 1
         * stockOrigin : PC收银机
         * originalCount : 95.0
         * stockTotal : 5.0
         * goodsName : 白雪可换囊直液式钢笔
         * listUnique : 1735960798681186
         * goodsBarcode : 6920024540044
         * goodsStandard :
         * stockCount : 94.0
         */

        private String stockType;//出入库类型
        private String stockSource;//操作方式
        private double stockPrice;//出入库时商品价格
        private double goodsCount;//出入库数量
        private int stockTypeCode;
        private String stockTime;//操作时间
        private int stockId;
        private String staffName;
        private int auditStatus;
        private String stockOrigin;//操作源
        private double originalCount;//操作前数量
        private double stockTotal;//出入库总额
        private String goodsName;//商品名称
        private String listUnique;//订单编号
        private String goodsBarcode;//商品条码
        private String goodsStandard;//规格
        private double stockCount;//修改后库存数量

        public String getStockType() {
            return stockType;
        }

        public void setStockType(String stockType) {
            this.stockType = stockType;
        }

        public String getStockSource() {
            return stockSource;
        }

        public void setStockSource(String stockSource) {
            this.stockSource = stockSource;
        }

        public double getStockPrice() {
            return stockPrice;
        }

        public void setStockPrice(double stockPrice) {
            this.stockPrice = stockPrice;
        }

        public double getGoodsCount() {
            return goodsCount;
        }

        public void setGoodsCount(double goodsCount) {
            this.goodsCount = goodsCount;
        }

        public int getStockTypeCode() {
            return stockTypeCode;
        }

        public void setStockTypeCode(int stockTypeCode) {
            this.stockTypeCode = stockTypeCode;
        }

        public String getStockTime() {
            return stockTime;
        }

        public void setStockTime(String stockTime) {
            this.stockTime = stockTime;
        }

        public int getStockId() {
            return stockId;
        }

        public void setStockId(int stockId) {
            this.stockId = stockId;
        }

        public String getStaffName() {
            return staffName;
        }

        public void setStaffName(String staffName) {
            this.staffName = staffName;
        }

        public int getAuditStatus() {
            return auditStatus;
        }

        public void setAuditStatus(int auditStatus) {
            this.auditStatus = auditStatus;
        }

        public String getStockOrigin() {
            return stockOrigin;
        }

        public void setStockOrigin(String stockOrigin) {
            this.stockOrigin = stockOrigin;
        }

        public double getOriginalCount() {
            return originalCount;
        }

        public void setOriginalCount(double originalCount) {
            this.originalCount = originalCount;
        }

        public double getStockTotal() {
            return stockTotal;
        }

        public void setStockTotal(double stockTotal) {
            this.stockTotal = stockTotal;
        }

        public String getGoodsName() {
            return goodsName;
        }

        public void setGoodsName(String goodsName) {
            this.goodsName = goodsName;
        }

        public String getListUnique() {
            return listUnique;
        }

        public void setListUnique(String listUnique) {
            this.listUnique = listUnique;
        }

        public String getGoodsBarcode() {
            return goodsBarcode;
        }

        public void setGoodsBarcode(String goodsBarcode) {
            this.goodsBarcode = goodsBarcode;
        }

        public String getGoodsStandard() {
            return goodsStandard;
        }

        public void setGoodsStandard(String goodsStandard) {
            this.goodsStandard = goodsStandard;
        }

        public double getStockCount() {
            return stockCount;
        }

        public void setStockCount(double stockCount) {
            this.stockCount = stockCount;
        }
    }

    public static class ObjectBean {
        private double outTotal;//出库总金额
        private double entryTotal;//入库总金额

        public double getOutTotal() {
            return outTotal;
        }

        public void setOutTotal(double outTotal) {
            this.outTotal = outTotal;
        }

        public double getEntryTotal() {
            return entryTotal;
        }

        public void setEntryTotal(double entryTotal) {
            this.entryTotal = entryTotal;
        }
    }
}
