package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.QueryOrderInfoData;
import com.yxl.cashier_retail.utils.DFUtils;

import java.util.List;

/**
 * Describe:订单退款-商品列表
 * Created by jingang on 2024/6/19
 */
public class OrderRefundGoodsAdapter extends BaseAdapter<QueryOrderInfoData.DataBean.ListDetailBean> {

    public OrderRefundGoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_order_refund_goods;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        TextView tvRefund, tvRefundHint, tvRefundTotal;

        RelativeLayout rel = holder.getView(R.id.relItem);
        tvRefund = holder.getView(R.id.tvItemRefund);
        ImageView ivRefund = holder.getView(R.id.ivItemRefund);
        tvRefundHint = holder.getView(R.id.tvItemRefundHint);
        tvRefundTotal = holder.getView(R.id.tvItemRefundTotal);

        ((AnimationDrawable) ivRefund.getDrawable()).start();

        double refundCount = mDataList.get(position).getRefundCount(),//退款数量;
                countCan = mDataList.get(position).getSale_list_detail_count() - mDataList.get(position).getRetCount();//可退数量
        if (countCan > 0) {
            if (mDataList.get(position).isSelect()) {
                rel.setBackgroundResource(R.drawable.shape_green_kuang_5);
                ivRefund.setVisibility(View.VISIBLE);
            } else {
                rel.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                ivRefund.setVisibility(View.GONE);
            }
            if (refundCount > 0) {
                tvRefund.setVisibility(View.VISIBLE);
                tvRefundHint.setVisibility(View.GONE);
                tvRefund.setText(DFUtils.getNum4(refundCount));
            } else {
                tvRefund.setVisibility(View.GONE);
                tvRefundHint.setVisibility(View.VISIBLE);
                tvRefund.setText("");
            }
        } else {
            rel.setBackgroundResource(R.drawable.shape_d8_kuang_f0_5);
            tvRefund.setVisibility(View.VISIBLE);
            ivRefund.setVisibility(View.GONE);
            tvRefundHint.setVisibility(View.GONE);
            tvRefund.setText("0");
        }
        tvRefundTotal.setText(DFUtils.getNum2(refundCount * mDataList.get(position).getSale_list_detail_price()));
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvBarcode, tvPrice, tvCount, tvTotal, tvRefunded, tvRefund, tvRefundHint, tvRefundTotal;

        LinearLayout lin = holder.getView(R.id.linItem);
        tvName = holder.getView(R.id.tvItemName);
        tvBarcode = holder.getView(R.id.tvItemBarcode);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvRefunded = holder.getView(R.id.tvItemRefunded);//已退数量
        RelativeLayout rel = holder.getView(R.id.relItem);
        tvRefund = holder.getView(R.id.tvItemRefund);
        ImageView ivRefund = holder.getView(R.id.ivItemRefund);
        tvRefundHint = holder.getView(R.id.tvItemRefundHint);
        tvRefundTotal = holder.getView(R.id.tvItemRefundTotal);

        ((AnimationDrawable) ivRefund.getDrawable()).start();

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
        }
        double refundCount = mDataList.get(position).getRefundCount(),//退款数量;
                countCan = mDataList.get(position).getSale_list_detail_count() - mDataList.get(position).getRetCount();//可退数量

        tvName.setText(mDataList.get(position).getGoods_name());
        tvBarcode.setText(mDataList.get(position).getGoods_barcode());
        tvPrice.setText(DFUtils.getNum2(mDataList.get(position).getSale_list_detail_price()));
        tvCount.setText(DFUtils.getNum4(mDataList.get(position).getSale_list_detail_count()));
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getSub_total()));
        tvRefunded.setText(DFUtils.getNum4(mDataList.get(position).getRetCount()));
        tvRefundTotal.setText(DFUtils.getNum2(refundCount * mDataList.get(position).getSale_list_detail_price()));

        if (countCan > 0) {
            if (mDataList.get(position).isSelect()) {
                rel.setBackgroundResource(R.drawable.shape_green_kuang_5);
                ivRefund.setVisibility(View.VISIBLE);
            } else {
                rel.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                ivRefund.setVisibility(View.GONE);
            }
            if (refundCount > 0) {
                tvRefund.setVisibility(View.VISIBLE);
                tvRefundHint.setVisibility(View.GONE);
                tvRefund.setText(DFUtils.getNum4(refundCount));
            } else {
                tvRefund.setVisibility(View.GONE);
                tvRefundHint.setVisibility(View.VISIBLE);
                tvRefund.setText("");
            }
        } else {
            rel.setBackgroundResource(R.drawable.shape_d8_kuang_f0_5);
            tvRefund.setVisibility(View.VISIBLE);
            ivRefund.setVisibility(View.GONE);
            tvRefundHint.setVisibility(View.GONE);
            tvRefund.setText("0");
        }
    }
}
