package com.yxl.cashier_retail.ui.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.SoundEffectConstants;
import android.view.View;
import android.view.ViewGroup;

import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.bumptech.glide.Glide;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityImgBigBinding;
import com.yxl.cashier_retail.view.BigImageView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Describe:查看大图
 * Created by jingang on 2019/10/15
 */
@SuppressLint({"NonConstantResourceId","SetTextI18n"})
public class ImgBigActivity extends BaseActivity<ActivityImgBigBinding> {
    private Adapter mAdapter;
    private List<String> list = new ArrayList<>();
    private int index;

    private BigImageView mCurrentView;

    @Override
    protected ActivityImgBigBinding getViewBinding() {
        return ActivityImgBigBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);

        list = getIntent().getStringArrayListExtra("img");
        index = getIntent().getIntExtra("index", 1);
        mBinding.textView.setText(index + 1 + "/" + list.size());

        mAdapter = new Adapter(this, list);
        mBinding.viewPager.setAdapter(mAdapter);
        mBinding.viewPager.setCurrentItem(index);
        mBinding.viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {

            }

            @Override
            public void onPageSelected(int i) {
                mBinding.textView.setText(i + 1 + "/" + list.size());
            }

            @Override
            public void onPageScrollStateChanged(int i) {

            }
        });
    }

    @Override
    public void initData() {

    }

    @Override
    public void finish() {
        super.finish();
        overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
    }

    public void setCurrPhotoView(BigImageView currentView) {
        this.mCurrentView = null;
        this.mCurrentView = currentView;
        mCurrentView.setOnClickListener(v -> {
            mCurrentView.playSoundEffect(SoundEffectConstants.CLICK);
            finish();
        });
    }

    /**
     * ViewPage适配器
     */
    public class Adapter extends PagerAdapter {
        private Context mContext;
        private List<String> list;
        private HashMap<Integer, BigImageView> mViewCache;

        public Adapter(Context mContext, List<String> list) {
            this.mContext = mContext;
            this.list = list;
            mViewCache = new HashMap<>();
        }

        @Override
        public int getCount() {
            return list.size();
        }

        @Override
        public void setPrimaryItem(ViewGroup container, int position, Object object) {
            super.setPrimaryItem(container, position, object);
            BigImageView piv = mViewCache.get(position);
            setCurrPhotoView(piv);
        }


        @Override
        public boolean isViewFromObject(View view, Object o) {
            return view == o;
        }

        @Override
        public Object instantiateItem(ViewGroup container, int position) {
            BigImageView photoView = mViewCache.get(position);
            if (photoView == null) {
                photoView = new BigImageView(mContext);
                photoView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
                Glide.with(mContext)
                        .load(list.get(position))
                        .into(photoView);
                mViewCache.put(position, photoView);
            }
            container.addView(photoView);
            return photoView;
        }

        @Override
        public void destroyItem(ViewGroup container, int position, Object object) {
            container.removeView((View) object);
            BigImageView photoView = mViewCache.get(position);
            if (photoView != null) {
                mViewCache.remove(position);
            }
        }

    }
}
