package com.yxl.cashier_retail.ui.bean;

/**
 * Describe: 扫码支付、扫码支付结果查询（实体类）
 * Created by jingang on 2025/6/7
 */
public class CashierStatusData {
    /**
     * status : 1
     * msg : 支付成功！
     * totals : 0
     * perPageNum : 0
     * data : {"out_trade_no":"20250607162547115","trade_state":"SUCCESS","total_fee":0.02,"saleListActuallyReceived":0.02,"pay_money":0.02}
     * goodsData : null
     * cusData : {"cus_name":"哈哈哈","cus_points":186.88,"shop_unique":1536215939565,"cus_unique":"17865069350","cus_phone":"17865069350","cus_balance":0,"sale_points":"0.02","cusBalance":0,"cus_id":444279}
     * resultCode : 0
     * sale_list_unique : null
     */

    private int status;
    private String msg;
    private DataBean data;
    private CusDataBean cusData;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public DataBean getData() {
        return data;
    }

    public void setData(DataBean data) {
        this.data = data;
    }

    public CusDataBean getCusData() {
        return cusData;
    }

    public void setCusData(CusDataBean cusData) {
        this.cusData = cusData;
    }


    public static class DataBean {
        /**
         * out_trade_no : 20250607162547115
         * trade_state : SUCCESS
         * total_fee : 0.02
         * saleListActuallyReceived : 0.02
         * pay_money : 0.02
         */

        private String out_trade_no;
        private String trade_state;

        public String getOut_trade_no() {
            return out_trade_no;
        }

        public void setOut_trade_no(String out_trade_no) {
            this.out_trade_no = out_trade_no;
        }

        public String getTrade_state() {
            return trade_state;
        }

        public void setTrade_state(String trade_state) {
            this.trade_state = trade_state;
        }

    }

    public static class CusDataBean {
        /**
         * cus_name : 哈哈哈
         * cus_points : 186.88
         * shop_unique : 1536215939565
         * cus_unique : 17865069350
         * cus_phone : 17865069350
         * cus_balance : 0.0
         * sale_points : 0.02
         * cusBalance : 0
         * cus_id : 444279
         */

        private double cus_points;
        private String sale_points;


        public double getCus_points() {
            return cus_points;
        }

        public void setCus_points(double cus_points) {
            this.cus_points = cus_points;
        }


        public String getSale_points() {
            return sale_points;
        }

        public void setSale_points(String sale_points) {
            this.sale_points = sale_points;
        }

    }
}
