package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogOrderRefundGoodsBinding;
import com.yxl.cashier_retail.ui.adapter.OrderRefundGoodsAdapter;
import com.yxl.cashier_retail.ui.bean.QueryOrderInfoData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.view.NumberKeyBoardView;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Describe:dialog（订单退款-选择商品）
 * Created by jingang on 2024/6/19
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class OrderRefundGoodsDialog extends BaseDialog<DialogOrderRefundGoodsBinding> implements View.OnClickListener {
    private static Activity mActivity;

    private int pos = -1;
    private static QueryOrderInfoData.DataBean data;
    private static int saleListPayment;//支付方式
    private List<QueryOrderInfoData.DataBean.ListDetailBean> dataList = new ArrayList<>();
    private OrderRefundGoodsAdapter mAdapter;
    private String goodsMsg;//商品信息 id:count:price 多个以;隔开
    private double total;//退款金额

    public static void showDialog(Activity activity, QueryOrderInfoData.DataBean data, int saleListPayment, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        OrderRefundGoodsDialog.listener = listener;
        OrderRefundGoodsDialog.data = data;
        OrderRefundGoodsDialog.saleListPayment = saleListPayment;
        OrderRefundGoodsDialog dialog = new OrderRefundGoodsDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 10 * 7, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 7);
        dialog.show();
    }

    public OrderRefundGoodsDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.relDialogAll.setOnClickListener(this);
        mBinding.numberKeyBoardView.setConfirmType(1);
        mBinding.numberKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var) {
                //此处应判断退货数量不可大于（商品数量-已退数量）
                if (pos == -1) {
                    return;
                }
                //可退数量 = 商品数量 - 已退数量
                double countCan = dataList.get(pos).getSale_list_detail_count() - dataList.get(pos).getRetCount();
                if (countCan <= 0) {
                    return;
                }
                double count = TextUtils.isEmpty(var) ? 0 : Double.parseDouble(var);
                dataList.get(pos).setRefundCount(Math.min(count, countCan));
                mAdapter.notifyItemChanged(pos, dataList);
                getRefundTotal();
            }

            @Override
            public void onConfirm() {
                goodsMsg = "";
                total = 0;
                for (int i = 0; i < dataList.size(); i++) {
                    if (dataList.get(i).getRefundCount() > 0) {
                        goodsMsg = TextUtils.isEmpty(goodsMsg) ? dataList.get(i).getSale_list_detail_id()
                                + ":" + dataList.get(i).getRefundCount()
                                + ":" + dataList.get(i).getSale_list_detail_price()
                                : goodsMsg + ";"
                                + dataList.get(i).getSale_list_detail_id()
                                + ":" + dataList.get(i).getRefundCount()
                                + ":" + dataList.get(i).getSale_list_detail_price();
                        total = total + dataList.get(i).getRefundCount() * dataList.get(i).getSale_list_detail_price();
                    }
                }
                goToRefund();
            }
        });
        getRefundTotal();
        setAdapter();
    }

    @Override
    protected DialogOrderRefundGoodsBinding getViewBinding() {
        return DialogOrderRefundGoodsBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.relDialogAll:
                //整单退款
                getGoodsMsg();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new OrderRefundGoodsAdapter(getContext());
        mBinding.rvDialog.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            double count = dataList.get(position).getSale_list_detail_count(),//商品数量
                    retCount = dataList.get(position).getRetCount(),//已退数量
                    countCan;//可退数量
            countCan = count - retCount;
            if (countCan <= 0) {
                return;
            }
            if (!dataList.get(position).isSelect()) {
                for (int i = 0; i < dataList.size(); i++) {
                    if (dataList.get(i).isSelect()) {
                        dataList.get(i).setSelect(false);
                        mAdapter.notifyItemChanged(i, dataList);
                    }
                }
                dataList.get(position).setSelect(true);
                mAdapter.notifyItemChanged(position, dataList);
                pos = position;
                if (dataList.get(position).getRefundCount() > 0) {
                    mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(dataList.get(position).getRefundCount()));
                } else {
                    mBinding.numberKeyBoardView.setResultStr("");
                }
            }
        });
        dataList.clear();
        if (data != null) {
            dataList.addAll(data.getListDetail());
        }
        mAdapter.setDataList(dataList);
        double total = 0;
        for (int i = 0; i < dataList.size(); i++) {
            //此处数量应减去已退数量
            double count = dataList.get(i).getSale_list_detail_count() - dataList.get(i).getRetCount();
            if (count > 0) {
                total = total + dataList.get(i).getSale_list_detail_price() * count;
            }
        }
        mBinding.tvDialogAll.setText(DFUtils.getNum2(total));
    }

    /**
     * 计算合计退款金额
     */
    private void getRefundTotal() {
        double total = 0;
        for (int i = 0; i < dataList.size(); i++) {
            total = total + dataList.get(i).getSale_list_detail_price() * dataList.get(i).getRefundCount();
        }
        mBinding.tvDialogTotal.setText(getRstr(R.string.rtotal_refund_colon) + DFUtils.getNum2(total));
    }

    /**
     * 获取商品信息
     */
    private void getGoodsMsg() {
        goodsMsg = "";
        total = 0;
        for (int i = 0; i < dataList.size(); i++) {
            double count = dataList.get(i).getSale_list_detail_count() - dataList.get(i).getRetCount();
            if (count > 0) {
                goodsMsg = TextUtils.isEmpty(goodsMsg) ? dataList.get(i).getSale_list_detail_id()
                        + ":" + count
                        + ":" + dataList.get(i).getSale_list_detail_price()
                        : goodsMsg + ";"
                        + dataList.get(i).getSale_list_detail_id()
                        + ":" + count
                        + ":" + dataList.get(i).getSale_list_detail_price();
                total = total + count * dataList.get(i).getSale_list_detail_price();
            }
        }
        goToRefund();
    }

    /**
     * 订单退款
     */
    private void goToRefund() {
        Log.e(tag, "goodsMsg = " + goodsMsg);
        if (TextUtils.isEmpty(goodsMsg)) {
            showToast(1, getRstr(R.string.select_refund_goods));
            return;
        }
        if (data == null) {
            return;
        }
        OrderRefundDialog.showDialog(mActivity, data, saleListPayment, goodsMsg, total, () -> {
            if (listener != null) {
                listener.onConfirm();
                dismiss();
            }
        });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm();
    }
}
