package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;

/**
 * Describe:列表中的商品列表（实体类）
 * Created by jingang on 2024/6/12
 */
public class ChildGoodsData implements Serializable {
    /**
     * 自采补货-预览
     * shopRestockplanGoodsId : 12
     * goodsPicturepath :
     * goodsName : 长城干红葡萄酒
     * goodsCount : 3.0
     * goodsInPrice : null
     * goodsUnit : null
     * goodsTotal : 326.25
     * goodsBarcode : 16901009906076
     */

    private int shopRestockplanGoodsId;
    private String goodsPicturepath;
    private String goodsName;
    private double goodsCount;
    private double goodsInPrice;
    private String goodsUnit;
    private double goodsTotal;
    private String goodsBarcode;


    /**
     * 供货商管理-购销单
     * goodsBarcode : 8960757619486
     * goodsPicturepath : https://auth.ikeda10.xyz/ClothingShoesandJewelry
     * purchaseGoodsCount : 9.0
     */

    private double purchaseGoodsCount;//商品数量


    public int getShopRestockplanGoodsId() {
        return shopRestockplanGoodsId;
    }

    public void setShopRestockplanGoodsId(int shopRestockplanGoodsId) {
        this.shopRestockplanGoodsId = shopRestockplanGoodsId;
    }

    public String getGoodsPicturepath() {
        return goodsPicturepath;
    }

    public void setGoodsPicturepath(String goodsPicturepath) {
        this.goodsPicturepath = goodsPicturepath;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public double getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(double goodsCount) {
        this.goodsCount = goodsCount;
    }

    public double getGoodsInPrice() {
        return goodsInPrice;
    }

    public void setGoodsInPrice(double goodsInPrice) {
        this.goodsInPrice = goodsInPrice;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public double getGoodsTotal() {
        return goodsTotal;
    }

    public void setGoodsTotal(double goodsTotal) {
        this.goodsTotal = goodsTotal;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public double getPurchaseGoodsCount() {
        return purchaseGoodsCount;
    }

    public void setPurchaseGoodsCount(double purchaseGoodsCount) {
        this.purchaseGoodsCount = purchaseGoodsCount;
    }
}
