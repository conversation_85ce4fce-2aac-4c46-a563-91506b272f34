package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.GoodsListData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:自采补货-选择商品列表（适配器）
 * Created by jingang on 2024/6/5
 */
public class RestockGoodsAdapter extends BaseAdapter<GoodsListData> {

    public RestockGoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_restock_goods;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        ImageView ivScale, ivCashier, ivApplet;
        TextView tvName, tvBarcode, tvShofar, tvPrice, tvUnit, tvStock, tvHouse, tvCount;
        tvName = holder.getView(R.id.tvItemName);
        tvBarcode = holder.getView(R.id.tvItemBarcode);
        tvShofar = holder.getView(R.id.tvItemShofar);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvUnit = holder.getView(R.id.tvItemUnit);
        tvStock = holder.getView(R.id.tvItemStock);
        tvHouse = holder.getView(R.id.tvItemHouse);
        tvCount = holder.getView(R.id.tvItemCount);
        ivCashier = holder.getView(R.id.ivItemCashier);
        ivApplet = holder.getView(R.id.ivItemApplet);
        ivScale = holder.getView(R.id.ivItemScale);

        tvName.setText(mDataList.get(position).getGoodsName());
        tvBarcode.setText(mDataList.get(position).getGoodsBarcode());
        tvPrice.setText(DFUtils.getNum2(mDataList.get(position).getGoodsSalePrice()));
        tvStock.setText(DFUtils.getNum4(mDataList.get(position).getGoodsCount()));
        tvCount.setText(getRstr(R.string.sales_month)+ DFUtils.getNum4(mDataList.get(position).getSaleCount()));
        ivCashier.setSelected(mDataList.get(position).getPcShelfState() == 1);
        ivApplet.setSelected(mDataList.get(position).getShelfState() == 1);
    }
}
