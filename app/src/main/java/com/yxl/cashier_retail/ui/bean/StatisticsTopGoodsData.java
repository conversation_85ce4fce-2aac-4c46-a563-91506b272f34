package com.yxl.cashier_retail.ui.bean;

/**
 * Describe:热销、滞销商品TOP5
 * Created by jingang on 2024/8/30
 */
public class StatisticsTopGoodsData {
    /**
     * yesSaleCount : 0.0
     * porfitPercentage : 0.0
     * saleCount : 0.0
     * goodsPicturePath :
     * goodsName : 特仑苏250ml*12盒
     * goodsBarcode : 000454554
     */

    private double yesSaleCount;
    private double porfitPercentage;
    private double saleCount;//销量
    private String goodsPicturePath;
    private String goodsName;//商品名称
    private String goodsBarcode;

    public double getYesSaleCount() {
        return yesSaleCount;
    }

    public void setYesSaleCount(double yesSaleCount) {
        this.yesSaleCount = yesSaleCount;
    }

    public double getPorfitPercentage() {
        return porfitPercentage;
    }

    public void setPorfitPercentage(double porfitPercentage) {
        this.porfitPercentage = porfitPercentage;
    }

    public double getSaleCount() {
        return saleCount;
    }

    public void setSaleCount(double saleCount) {
        this.saleCount = saleCount;
    }

    public String getGoodsPicturePath() {
        return goodsPicturePath;
    }

    public void setGoodsPicturePath(String goodsPicturePath) {
        this.goodsPicturePath = goodsPicturePath;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }
}
