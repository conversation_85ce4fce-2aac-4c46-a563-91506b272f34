package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:购销单-单据凭证（实体类）
 * Created by jingang on 2023/9/15
 */
public class PurchaseVoucherData implements Serializable {
    /**
     * billNo : B202309091735581220000
     * paymentMoney : 100.0
     * paymentRemark : 哈哈哈
     * imageUrlList : ["https://file.buyhoo.cc/publicImage/2023/09/15/b74f9e5022744fc7b068fe32a525cd79.jpg"]
     */

    private String billNo;
    private double paymentMoney;
    private String paymentRemark;
    private List<String> imageUrlList;

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public double getPaymentMoney() {
        return paymentMoney;
    }

    public void setPaymentMoney(double paymentMoney) {
        this.paymentMoney = paymentMoney;
    }

    public String getPaymentRemark() {
        return paymentRemark;
    }

    public void setPaymentRemark(String paymentRemark) {
        this.paymentRemark = paymentRemark;
    }

    public List<String> getImageUrlList() {
        return imageUrlList;
    }

    public void setImageUrlList(List<String> imageUrlList) {
        this.imageUrlList = imageUrlList;
    }
}
