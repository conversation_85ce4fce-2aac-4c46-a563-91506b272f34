package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.StatisticsTopGoodsData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:统计-热销、滞销商品TOP5（适配器）
 * Created by jingang on 2024/8/30
 */
public class StatisticsTopGoodsAdapter extends BaseAdapter<StatisticsTopGoodsData> {

    public StatisticsTopGoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_statistics_top_goods;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName, tvCount;
        tvName = holder.getView(R.id.tvItemName);
        tvCount = holder.getView(R.id.tvItemCount);
        tvName.setText(mDataList.get(position).getGoodsName());
        tvCount.setText(DFUtils.getNum4(mDataList.get(position).getSaleCount()));
    }
}
