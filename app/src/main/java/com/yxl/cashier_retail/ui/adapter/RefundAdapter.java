package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.RefundData;
import com.yxl.cashier_retail.utils.DFUtils;

import java.util.List;

/**
 * Describe:退款列表（适配器）
 * Created by jingang on 2024/5/22
 */
public class RefundAdapter extends BaseAdapter<RefundData> {

    public RefundAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_refund;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvTime, tvNo, tvName, tvMobile, tvTotal, tvStatus;
        tvTime = holder.getView(R.id.tvItemTime);
        tvNo = holder.getView(R.id.tvItemNo);
        tvName = holder.getView(R.id.tvItemName);
        tvMobile = holder.getView(R.id.tvItemMobile);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvStatus = holder.getView(R.id.tvItemStatus);

        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvTime.setTextColor(mContext.getResources().getColor(R.color.white));
            tvNo.setTextColor(mContext.getResources().getColor(R.color.white));
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
            tvMobile.setTextColor(mContext.getResources().getColor(R.color.white));
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.white));
            tvStatus.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            tvTime.setTextColor(mContext.getResources().getColor(R.color.black));
            tvNo.setTextColor(mContext.getResources().getColor(R.color.black));
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
            tvMobile.setTextColor(mContext.getResources().getColor(R.color.black));
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.black));
            tvStatus.setTextColor(mContext.getResources().getColor(R.color.black));
            if (position % 2 == 0) {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
            } else {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
            }
        }
        //退款状态 1.待审核 3.已退款 4.已拒绝
        switch (mDataList.get(position).getRetListHandlestate()) {
            case 1:
                tvStatus.setText(getRstr(R.string.refund_status0));
                break;
            case 3:
                tvStatus.setText(getRstr(R.string.refund_status1));
                break;
            case 4:
                tvStatus.setText(getRstr(R.string.refund_status2));
                break;
            default:
                tvStatus.setText("");
                break;
        }
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvTime, tvNo, tvName, tvMobile, tvTotal, tvStatus;
        tvTime = holder.getView(R.id.tvItemTime);
        tvNo = holder.getView(R.id.tvItemNo);
        tvName = holder.getView(R.id.tvItemName);
        tvMobile = holder.getView(R.id.tvItemMobile);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvStatus = holder.getView(R.id.tvItemStatus);

        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvTime.setTextColor(mContext.getResources().getColor(R.color.white));
            tvNo.setTextColor(mContext.getResources().getColor(R.color.white));
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
            tvMobile.setTextColor(mContext.getResources().getColor(R.color.white));
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.white));
            tvStatus.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            tvTime.setTextColor(mContext.getResources().getColor(R.color.black));
            tvNo.setTextColor(mContext.getResources().getColor(R.color.black));
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
            tvMobile.setTextColor(mContext.getResources().getColor(R.color.black));
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.black));
            tvStatus.setTextColor(mContext.getResources().getColor(R.color.black));
            if (position % 2 == 0) {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
            } else {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
            }
        }

        tvTime.setText(mDataList.get(position).getRetListDatetime());
        tvNo.setText(mDataList.get(position).getRetListUnique());
        tvName.setText(TextUtils.isEmpty(mDataList.get(position).getCusName()) ? "-" : mDataList.get(position).getCusName());
        tvMobile.setText(TextUtils.isEmpty(mDataList.get(position).getSaleListPhone()) ? "-" : mDataList.get(position).getSaleListPhone());
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getRetListTotal()));
        //退款状态 1.待审核 3.已退款 4.已拒绝
        switch (mDataList.get(position).getRetListHandlestate()) {
            case 1:
                tvStatus.setText(getRstr(R.string.refund_status0));
                break;
            case 3:
                tvStatus.setText(getRstr(R.string.refund_status1));
                break;
            case 4:
                tvStatus.setText(getRstr(R.string.refund_status2));
                break;
            default:
                tvStatus.setText("");
                break;
        }
    }
}