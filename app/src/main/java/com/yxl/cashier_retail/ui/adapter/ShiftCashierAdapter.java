package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.ShiftData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:交班-收银统计（适配器）
 * Created by jingang on 2024/8/29
 */
public class ShiftCashierAdapter extends BaseAdapter<ShiftData.Data1Bean> {

    public ShiftCashierAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_shift_recharge;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvTotal, tvName;
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvName = holder.getView(R.id.tvItemName);

//        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getSale_list_actually_received()));
//        tvName.setText(mDataList.get(position).getName());
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getPayment_total()));
        tvName.setText(mDataList.get(position).getPay_ment());
    }
}
