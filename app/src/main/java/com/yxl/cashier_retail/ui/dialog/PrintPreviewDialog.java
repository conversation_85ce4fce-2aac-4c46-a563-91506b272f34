package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.view.Gravity;
import android.view.View;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogPrintPreviewBinding;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:dialog（打印预览）
 * Created by jingang on 2024/9/10
 */
@SuppressLint("NonConstantResourceId")
public class PrintPreviewDialog extends BaseDialog<DialogPrintPreviewBinding> implements View.OnClickListener {
    private static Activity mActivity;

    public static void showDialog(Activity activity, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        PrintPreviewDialog.listener = listener;
        PrintPreviewDialog dialog = new PrintPreviewDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, DensityUtils.getScreenHeight(dialog.getContext()) / 5 * 3);
        dialog.show();
    }

    public PrintPreviewDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        mBinding.tvDialogEdit.setOnClickListener(this);
    }

    @Override
    protected DialogPrintPreviewBinding getViewBinding() {
        return DialogPrintPreviewBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()){
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.tvDialogConfirm:
                //确认打印
                break;
            case R.id.tvDialogEdit:
                //修改打印参数
                break;
        }
    }

    private static MyListener listener;

    public interface MyListener {
        void onLanguageClick(String language, String area);
    }
}
