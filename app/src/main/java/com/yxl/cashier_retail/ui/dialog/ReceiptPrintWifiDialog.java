package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogReceiptPrintWifiBinding;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:dialog（连接小票打印机-WiFi）
 * Created by jingang on 2024/09/27
 */
@SuppressLint("NonConstantResourceId")
public class ReceiptPrintWifiDialog extends BaseDialog<DialogReceiptPrintWifiBinding> implements View.OnClickListener {
    private static Activity mActivity;
    private String ip;

    public static void showDialog(Activity activity, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        mActivity = activity;
        ReceiptPrintWifiDialog.listener = listener;
        ReceiptPrintWifiDialog dialog = new ReceiptPrintWifiDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public ReceiptPrintWifiDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.ivDialogClear.setOnClickListener(this);
        mBinding.tvDialogConfirm.setOnClickListener(this);
        mBinding.etDialog.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                ip = s.toString().trim();
                mBinding.ivDialogClear.setVisibility(TextUtils.isEmpty(ip) ? View.GONE : View.VISIBLE);
            }
        });
    }

    @Override
    protected DialogReceiptPrintWifiBinding getViewBinding() {
        return DialogReceiptPrintWifiBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.ivDialogClear:
                //清除输入框内容
                mBinding.etDialog.setText("");
                break;
            case R.id.tvDialogConfirm:
                //确认
                if (TextUtils.isEmpty(ip)) {
                    showToast(1, "请输入IP地址");
                    return;
                }
                if (listener != null) {
                    listener.onConfirm(ip);
                    dismiss();
                }
                break;
        }
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(String ip);
    }
}
