package com.yxl.cashier_retail.ui.presenter;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;

import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.ui.bean.MemberData;
import com.yxl.cashier_retail.ui.contract.MemberContract;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 会员
 */
public class MemberPresenter implements MemberContract.Presenter {
    private Context context;
    private MemberContract.View mView;

    public MemberPresenter(Context context) {
        this.context = context;
    }

    @Override
    public void attachView(@NonNull MemberContract.View view) {
        mView = view;
    }

    @Override
    public void detachView() {
        mView = null;
    }

    @Override
    public void getCusList(String shopUnique, String keyWords, int searchType, int valueType) {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", shopUnique);
        params.put("cusMsg", keyWords);
        params.put("searchType", searchType);
        params.put("valueType", valueType);
        RXHttpUtil.requestByFormPostAsResponseList((LifecycleOwner) context,
                ZURL.getMemberList(),
                params,
                MemberData.class,
                new RequestListListener<MemberData>() {
                    @Override
                    public void onResult(List<MemberData> list) {
                        if (mView != null) {
                            mView.successMemberList(list);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        if (mView != null) {
                            mView.onError(msg);
                        }
                    }
                });
    }
}
