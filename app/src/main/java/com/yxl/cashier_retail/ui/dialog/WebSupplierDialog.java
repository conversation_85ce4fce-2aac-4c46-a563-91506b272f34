package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogSupplierBinding;
import com.yxl.cashier_retail.ui.adapter.WebSupplierDialogAdapter;
import com.yxl.cashier_retail.ui.bean.WebSupplierData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.DensityUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:dialog（选择供货商-网页端）
 * Created by jingang on 2024/6/5
 */
@SuppressLint("NonConstantResourceId")
public class WebSupplierDialog extends BaseDialog<DialogSupplierBinding> implements View.OnClickListener {
    private static View viewIcon;
    private String keyWords;
    private final Animation openAnim, closeAnim;

    private WebSupplierDialogAdapter mAdapter;
    private List<WebSupplierData.DataBean> dataList = new ArrayList<>();

    public static void showDialog(Activity activity, View viewIcon, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        WebSupplierDialog.listener = listener;
        WebSupplierDialog.viewIcon = viewIcon;
        WebSupplierDialog dialog = new WebSupplierDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, DensityUtils.getScreenHeight(dialog.getContext()) / 10 * 7);
        dialog.show();
    }

    public WebSupplierDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        openAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_1);
        closeAnim = AnimationUtils.loadAnimation(context, R.anim.btn_rotate_anim_2);
        openAnim.setFillAfter(true);
        closeAnim.setFillAfter(true);

        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.ivDialogClear.setOnClickListener(this);
        mBinding.etDialog.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                keyWords = s.toString().trim();
                if (TextUtils.isEmpty(keyWords)) {
                    mBinding.ivDialogClear.setVisibility(View.GONE);
                } else {
                    mBinding.ivDialogClear.setVisibility(View.VISIBLE);
                }
            }
        });
        mBinding.etDialog.setOnEditorActionListener((v, actionId, event) -> {
            page = 1;
            getSupplierList();
            return true;
        });
        setAdapter();
        getSupplierList();
    }

    @Override
    protected DialogSupplierBinding getViewBinding() {
        return DialogSupplierBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
        if (viewIcon != null) {
            viewIcon.startAnimation(openAnim);
        }
    }

    @Override
    public void dismiss() {
        super.dismiss();
        if (viewIcon != null) {
            viewIcon.startAnimation(closeAnim);
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.ivDialogClear:
                //清除搜索输入
                mBinding.etDialog.setText("");
                keyWords = "";
                page = 1;
                getSupplierList();
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new WebSupplierDialogAdapter(getContext());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (listener != null) {
                listener.onConfirm(dataList.get(position));
                dismiss();
            }
        });
        mBinding.smartRefreshLayout.setOnRefreshListener(refreshLayout -> {
            getSupplierList();
        });
        mBinding.smartRefreshLayout.setEnableLoadMore(false);
    }

    /**
     * 供货商列表
     */
    private void getSupplierList() {
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        showDialog();
        RXHttpUtil.requestByFormPostAsOriginalResponse(this,
                ZURL.getSupplierListWeb(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        WebSupplierData data = new Gson().fromJson(s, WebSupplierData.class);
                        if (data == null) {
                            return;
                        }
                        if (data.getStatus() != Constants.SUCCESS_CODE) {
                            showToast(1, data.getMsg());
                            return;
                        }
                        if (data.getData() == null) {
                            showToast(1, data.getMsg());
                            return;
                        }
                        dataList.clear();
                        dataList.addAll(data.getData());
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.smartRefreshLayout.finishRefresh();
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(WebSupplierData.DataBean data);
    }
}
