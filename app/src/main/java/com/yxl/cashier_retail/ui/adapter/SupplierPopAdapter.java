package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.SupplierPcData;

/**
 * Describe: 选择供货商-pop（适配器）
 * Created by jingang on 2025/6/7
 */
public class SupplierPopAdapter extends BaseAdapter<SupplierPcData> {
    public SupplierPopAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_supplier_pop;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        TextView tvName = holder.getView(R.id.tvItemName);
        tvName.setText(mDataList.get(position).getSupplier_name());
        tvName.setTextColor(mDataList.get(position).isSelect()
                ? mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green)
                : mContext.getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
    }
}
