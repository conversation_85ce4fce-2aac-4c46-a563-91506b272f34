package com.yxl.cashier_retail.ui.presenter;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;

import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.ui.contract.ForgetContract;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;

import java.util.HashMap;
import java.util.Map;

/**
 * 忘记密码
 */
public class ForgetPresenter implements ForgetContract.Presenter {
    private Context context;
    private ForgetContract.View mView;

    public ForgetPresenter(Context context) {
        this.context = context;
    }

    @Override
    public void attachView(@NonNull ForgetContract.View view) {
        mView = view;
    }

    @Override
    public void detachView() {
        mView = null;
    }

    @Override
    public void getCode(String account) {
        Map<String, Object> params = new HashMap<>();
        params.put("staffAccount", account);
        params.put("phoneType", 1);
        RXHttpUtil.requestByFormPostAsResponse((LifecycleOwner) mView,
                ZURL.getCode(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        if (mView != null) {
                            mView.successCode(s);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        if (mView != null) {
                            mView.onError(msg);
                        }
                    }
                });
    }

    @Override
    public void postPwdUpdate(String account, String code, String pwd) {
        Map<String, Object> params = new HashMap<>();
        params.put("staffAccount", account);
        params.put("smsCode", code);
        params.put("newStaffPwd", pwd);
        RXHttpUtil.requestByFormPostAsResponse((LifecycleOwner) mView,
                ZURL.getUpdatePwd(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        if (mView != null) {
                            mView.successForget(s);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        if (mView != null) {
                            mView.onError(msg);
                        }
                    }
                });
    }
}
