package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.QueryOrderListData;

import java.util.List;

/**
 * Describe:查询-订单列表（适配器）
 * Created by jingang on 2024/8/24
 */
public class QueryOrderAdapter extends BaseAdapter<QueryOrderListData.DataBean> {

    public QueryOrderAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_query_order;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        TextView tvRefund = holder.getView(R.id.tvItemRefund);
        tvRefund.setVisibility(mDataList.get(position).getReturnType() == 2 ? View.VISIBLE : View.GONE);
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvTime, tvRefund, tvName, tvNo, tvType, tvTotal, tvCount, tvMobile;
        tvTime = holder.getView(R.id.tvItemTime);
        tvRefund = holder.getView(R.id.tvItemRefund);
        tvName = holder.getView(R.id.tvItemName);
        tvNo = holder.getView(R.id.tvItemNo);
        tvType = holder.getView(R.id.tvItemType);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvCount = holder.getView(R.id.tvItemCount);
        tvMobile = holder.getView(R.id.tvItemMobile);

        if (mDataList.get(position).isSelect()) {
            lin.setBackgroundColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.green));
            tvTime.setTextColor(mContext.getResources().getColor(R.color.white));
            tvName.setTextColor(mContext.getResources().getColor(R.color.white));
            tvNo.setTextColor(mContext.getResources().getColor(R.color.white));
            tvType.setTextColor(mContext.getResources().getColor(R.color.white));
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.white));
            tvCount.setTextColor(mContext.getResources().getColor(R.color.white));
            tvMobile.setTextColor(mContext.getResources().getColor(R.color.white));
        } else {
            if (position % 2 == 0) {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
            } else {
                lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
            }
            tvTime.setTextColor(mContext.getResources().getColor(R.color.black));
            tvName.setTextColor(mContext.getResources().getColor(R.color.black));
            tvNo.setTextColor(mContext.getResources().getColor(R.color.black));
            tvType.setTextColor(mContext.getResources().getColor(R.color.black));
            tvTotal.setTextColor(mContext.getResources().getColor(R.color.black));
            tvCount.setTextColor(mContext.getResources().getColor(R.color.black));
            tvMobile.setTextColor(mContext.getResources().getColor(R.color.black));
        }
        tvTime.setText(TextUtils.isEmpty(mDataList.get(position).getSale_list_datetime()) ? "-" : mDataList.get(position).getSale_list_datetime());
        tvRefund.setVisibility(mDataList.get(position).getReturnType() == 2 ? View.VISIBLE : View.GONE);
        tvName.setText(TextUtils.isEmpty(mDataList.get(position).getSale_list_name()) ? "-" : mDataList.get(position).getSale_list_name());
        tvNo.setText(mDataList.get(position).getSale_list_unique());
        tvType.setText(mDataList.get(position).getPayMent());
        tvTotal.setText(mDataList.get(position).getSale_list_total());
        tvCount.setText(mDataList.get(position).getSale_list_totalCount());
        tvMobile.setText(TextUtils.isEmpty(mDataList.get(position).getSale_list_phone()) ? "-" : mDataList.get(position).getSale_list_phone());
    }
}
