package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.SupplierPaymentData;
import com.yxl.cashier_retail.utils.DFUtils;

import java.util.List;

/**
 * Describe:供货商管理-详情-结款记录（适配器）
 * Created by jingang on 2024/6/12
 */
public class SupplierPaymentAdapter extends BaseAdapter<SupplierPaymentData> {

    public SupplierPaymentAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_supplier_payment;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        TextView tvStatus = holder.getView(R.id.tvItemStatus);
        //状态1-已提交(待收货)，2-已收货(待付款)，3-待确认，4-已完成，5-作废，6-异常
        switch (mDataList.get(position).getStatus()) {
            case 1:
                tvStatus.setText(getRstr(R.string.order_status3));
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.red));
                break;
            case 2:
                tvStatus.setText(getRstr(R.string.order_status7));
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.red));
                break;
            case 3:
                tvStatus.setText(getRstr(R.string.order_status10));
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.red));
                break;
            case 4:
                tvStatus.setText(getRstr(R.string.order_status6));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.black));
                break;
            case 5:
                tvStatus.setText(getRstr(R.string.order_status11));
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
                break;
            default:
                tvStatus.setText("");
                break;
        }
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvTime, tvName, tvTotal, tvRemarks, tvStatus;
        tvTime = holder.getView(R.id.tvItemTime);
        tvName = holder.getView(R.id.tvItemName);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvRemarks = holder.getView(R.id.tvItemRemarks);
        tvStatus = holder.getView(R.id.tvItemStatus);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
        }

        //状态1-已提交(待收货)，2-已收货(待付款)，3-待确认，4-已完成，5-作废，6-异常
        switch (mDataList.get(position).getStatus()) {
            case 1:
                tvStatus.setText(getRstr(R.string.order_status3));
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.red));
                break;
            case 2:
                tvStatus.setText(getRstr(R.string.order_status7));
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.red));
                break;
            case 3:
                tvStatus.setText(getRstr(R.string.order_status10));
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.red));
                break;
            case 4:
                tvStatus.setText(getRstr(R.string.order_status6));
                tvStatus.setTextColor(mContext.getResources().getColor(R.color.black));
                break;
            case 5:
                tvStatus.setText(getRstr(R.string.order_status11));
                tvStatus.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
                break;
            default:
                tvStatus.setText("");
                break;
        }
        tvTime.setText(mDataList.get(position).getCreateTime());
        tvName.setText(mDataList.get(position).getCreateBy());
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getPaymentMoney()));
        tvRemarks.setText(TextUtils.isEmpty(mDataList.get(position).getRemark()) ? "-" : mDataList.get(position).getRemark());
    }
}
