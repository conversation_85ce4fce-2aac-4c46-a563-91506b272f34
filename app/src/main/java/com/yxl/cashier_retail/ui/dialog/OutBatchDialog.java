package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogOutBatchBinding;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.view.NumberKeyBoardView;
import com.yxl.commonlibrary.utils.DensityUtils;
import com.yxl.commonlibrary.utils.StringUtils;

/**
 * Describe:dialog（批量出库选择商品）
 * Created by jingang on 2025/01/22
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class OutBatchDialog extends BaseDialog<DialogOutBatchBinding> implements View.OnClickListener {
    private int type;//0.数量 1.单价
    private static GoodsData data;
    private double count,
            price,//单价
            total;//总价

    public static void showDialog(Activity activity, GoodsData data, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        OutBatchDialog.listener = listener;
        OutBatchDialog.data = data;
        OutBatchDialog dialog = new OutBatchDialog(activity);
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) /2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    public OutBatchDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);

        ((AnimationDrawable) mBinding.ivDialogCursorCount.getDrawable()).start();
        ((AnimationDrawable) mBinding.ivDialogCursorPrice.getDrawable()).start();
        mBinding.ivDialogClose.setOnClickListener(this);
        mBinding.linDialogCount.setOnClickListener(this);
        mBinding.linDialogPrice.setOnClickListener(this);
        mBinding.numberKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var) {
                switch (type) {
                    case 0:
                        mBinding.tvDialogCount.setText(var);
                        if (TextUtils.isEmpty(var)) {
                            mBinding.tvDialogCount.setVisibility(View.GONE);
                            mBinding.tvDialogCountHint.setVisibility(View.VISIBLE);
                            count = 0;
                        } else {
                            mBinding.tvDialogCount.setVisibility(View.VISIBLE);
                            mBinding.tvDialogCountHint.setVisibility(View.GONE);
                            count = Double.parseDouble(var);
                        }
                        total = count * price;
                        if (total > 0) {
                            mBinding.tvDialogTotal.setText(DFUtils.getNum2(total));
                        } else {
                            mBinding.tvDialogTotal.setText(getRstr(R.string.no_count));
                        }
                        break;
                    case 1:
                        mBinding.tvDialogPrice.setText(var);
                        if (TextUtils.isEmpty(var)) {
                            mBinding.tvDialogPrice.setVisibility(View.GONE);
                            mBinding.tvDialogPrice.setVisibility(View.VISIBLE);
                            price = 0;
                        } else {
                            mBinding.tvDialogPrice.setVisibility(View.VISIBLE);
                            mBinding.tvDialogPriceHint.setVisibility(View.GONE);
                            price = Double.parseDouble(var);
                        }
                        total = count * price;
                        if (total > 0) {
                            mBinding.tvDialogTotal.setText(DFUtils.getNum2(total));
                        } else {
                            mBinding.tvDialogTotal.setText(getRstr(R.string.no_count));
                        }
                        break;
                }
            }

            @Override
            public void onConfirm() {
                if (count <= 0) {
                    showToast(1, getRstr(R.string.input_out_count));
                    return;
                }
                if (price <= 0) {
                    showToast(1, getRstr(R.string.input_out_price));
                    return;
                }
                if (listener != null) {
                    listener.onConfirm(count, price, total, data);
                    dismiss();
                }
            }
        });
        setUI();
    }

    @Override
    protected DialogOutBatchBinding getViewBinding() {
        return DialogOutBatchBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    @Override
    public void onClick(View v) {
        if (isQuicklyClick()) {
            return;
        }
        switch (v.getId()) {
            case R.id.ivDialogClose:
                dismiss();
                break;
            case R.id.linDialogCount:
                //数量
                if (type != 0) {
                    type = 0;
                    mBinding.linDialogCount.setBackgroundResource(R.drawable.shape_green_kuang_5);
                    mBinding.linDialogPrice.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursorCount.setVisibility(View.VISIBLE);
                    mBinding.ivDialogCursorPrice.setVisibility(View.GONE);
                    if (count > 0) {
                        mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(count));
                    } else {
                        mBinding.numberKeyBoardView.setResultStr("");
                    }
                }
                break;
            case R.id.linDialogPrice:
                //单价
                if (type != 1) {
                    type = 1;
                    mBinding.linDialogPrice.setBackgroundResource(R.drawable.shape_green_kuang_5);
                    mBinding.linDialogCount.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                    mBinding.ivDialogCursorCount.setVisibility(View.GONE);
                    mBinding.ivDialogCursorPrice.setVisibility(View.VISIBLE);
                    if (price > 0) {
                        mBinding.numberKeyBoardView.setResultStr(DFUtils.getNum4(price));
                    } else {
                        mBinding.numberKeyBoardView.setResultStr("");
                    }
                }
                break;
        }
    }

    /**
     * 更新UI
     */
    private void setUI() {
        if (data == null) {
            return;
        }
        String unit = data.getGoods_unit();
        Glide.with(getContext())
                .load(StringUtils.handledImgUrl(data.getGoods_picturepath()))
                .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img))
                .into(mBinding.ivDialogImg);
        mBinding.tvDialogName.setText(data.getGoods_name() + "(" + data.getGoods_barcode() + ")");
        mBinding.tvDialogStock.setText(TextUtils.isEmpty(unit) ? DFUtils.getNum4(data.getGoods_count()) : DFUtils.getNum4(data.getGoods_count()) + unit);
        mBinding.tvDialogInPrice.setText(DFUtils.getNum2(data.getGoods_in_price()));
        mBinding.tvDialogUnit.setText(TextUtils.isEmpty(unit) ? "" : unit);
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(double count, double price, double total, GoodsData data);
    }
}
