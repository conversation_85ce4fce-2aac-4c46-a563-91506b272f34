package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;

/**
 * Describe:条件选择（实体类）
 * Created by jingang on 2024/5/21
 */
public class ConditionData implements Serializable {
    private int id;
    private String name;
    private boolean select;

    public ConditionData() {
    }

    public ConditionData(int id, String name, boolean select) {
        this.id = id;
        this.name = name;
        this.select = select;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }
}
