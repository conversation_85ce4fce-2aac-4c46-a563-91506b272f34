package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.google.android.material.tabs.TabLayout;
import com.google.gson.Gson;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmRefundBinding;
import com.yxl.cashier_retail.ui.adapter.OrderGoodsAdapter;
import com.yxl.cashier_retail.ui.adapter.SimpleFragmentPagerAdapter;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.OrderCountData;
import com.yxl.cashier_retail.ui.bean.OrderGoodsData;
import com.yxl.cashier_retail.ui.bean.RefundInfoData;
import com.yxl.cashier_retail.ui.dialog.DateStartEndDialog;
import com.yxl.cashier_retail.ui.dialog.RefundRefuseReasonDialog;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.utils.DateUtils;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:网单-退款
 * Created by jingang on 2024/6/19
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class RefundFragment extends BaseFragment<FmRefundBinding> implements View.OnClickListener {
    public static String startDate, endDate;

    private SimpleFragmentPagerAdapter pagerAdapter;
    private List<String> titleList = new ArrayList<>();
    private List<Fragment> fragmentList = new ArrayList<>();
    private List<Integer> countList = new ArrayList<>();

    //退款订单详情
    private String retListUnique;//退款单号
    private List<OrderGoodsData> goodsList = new ArrayList<>();
    private OrderGoodsAdapter goodsAdapter;

    @Override
    protected FmRefundBinding getViewBinding() {
        return FmRefundBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.linStartDate.setOnClickListener(this);
        mBinding.linEndDate.setOnClickListener(this);
        mBinding.tvOrderCancel.setOnClickListener(this);
        mBinding.tvOrderConfirm.setOnClickListener(this);

        startDate = DateUtils.getOldDate(0);
        endDate = startDate;
        mBinding.tvStartDate.setText(startDate);
        mBinding.tvEndDate.setText(endDate);

        setFragment();
        setAdapter();
    }

    @Override
    protected void initData() {
        getOrderCount();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.linStartDate:
                //开始日期
                showDialogDate(0, mBinding.ivStartDate);
                break;
            case R.id.linEndDate:
                //结束日期
                showDialogDate(1, mBinding.ivEndDate);
                break;
            case R.id.tvOrderCancel:
                //拒绝退款
                RefundRefuseReasonDialog.showDialog(getActivity(), reason -> {
                    postRefund(4, reason);
                });
                break;
            case R.id.tvOrderConfirm:
                //确认退款
                postRefund(3, "");
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.REFUND_COUNT:
                //订单数量
                getOrderCount();
                break;
            case Constants.REFUND_INFO:
                //订单详情
                retListUnique = event.getTypes();
                if (TextUtils.isEmpty(retListUnique)) {
                    mBinding.linInfo.setVisibility(View.GONE);
                    mBinding.tvNothing.setVisibility(View.VISIBLE);
                } else {
                    getOrderInfo();
                }
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        goodsAdapter = new OrderGoodsAdapter(getActivity(), 1);
        mBinding.rvGoods.setAdapter(goodsAdapter);
    }

    /**
     * dialog（选择日期时间）
     */
    private void showDialogDate(int type, View view) {
        DateStartEndDialog.showDialog(getActivity(), view, startDate, endDate, type, (startDate, endDate) -> {
            RefundFragment.startDate = startDate;
            RefundFragment.endDate = endDate;
            mBinding.tvStartDate.setText(startDate);
            mBinding.tvEndDate.setText(endDate);
            getOrderCount();
            //请求数据
            EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.REFUND_LIST));
        });
    }

    /**
     * 设置viewpage
     */
    private void setFragment() {
        startDate = DateUtils.getOldDate(0);
        endDate = DateUtils.getOldDate(0);
        titleList.clear();
        titleList.add(getRstr(R.string.all));
        titleList.add(getRstr(R.string.refund_status0));
        titleList.add(getRstr(R.string.refund_status1));
        titleList.add(getRstr(R.string.refund_status2));

        countList.clear();
        for (int i = 0; i < 4; i++) {
            countList.add(0);
        }
        fragmentList.clear();
        fragmentList.add(new RefundChildFragment(-1));
        fragmentList.add(new RefundChildFragment(1));
        fragmentList.add(new RefundChildFragment(3));
        fragmentList.add(new RefundChildFragment(4));

        pagerAdapter = new SimpleFragmentPagerAdapter(getActivity(), getChildFragmentManager(), fragmentList, titleList, countList);
        mBinding.viewPager.setAdapter(pagerAdapter);
        mBinding.viewPager.setCurrentItem(0);
        mBinding.viewPager.setOffscreenPageLimit(titleList.size());
        mBinding.tabLayout.setupWithViewPager(mBinding.viewPager);
        mBinding.tabLayout.setSelectedTabIndicatorHeight(0);
        mBinding.tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                updateTabTextView(tab, true);
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                updateTabTextView(tab, false);
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });
        setUpTabBadge();

    }

    /**
     * 设置tabLayout上的标题的角标
     */
    private void setUpTabBadge() {
        for (int i = 0; i < fragmentList.size(); i++) {
            TabLayout.Tab tab = mBinding.tabLayout.getTabAt(i);
            View customView = tab.getCustomView();
            if (customView != null) {
                ViewParent parent = customView.getParent();
                ViewGroup viewGroup = (ViewGroup) parent;
                if (parent != null) {
                    try {
                        viewGroup.removeView(customView);
                    } catch (Exception ignored) {
                    }
                }
            }
            // 更新CustomView
            tab.setCustomView(pagerAdapter.getTabItemView(i));
        }

        // 需加上以下代码,不然会出现更新Tab角标后,选中的Tab字体颜色不是选中状态的颜色
        TabLayout.Tab tabAt = mBinding.tabLayout.getTabAt(mBinding.tabLayout.getSelectedTabPosition());
        updateTabTextView(tabAt, true);
    }

    /**
     * 设置tabLayout标题样式和横杠
     */
    private void updateTabTextView(TabLayout.Tab tab, Boolean isSelect) {
        if (tab == null) {
            return;
        }
        if (tab.getCustomView() == null) {
            return;
        }
        TextView tabSelect = tab.getCustomView().findViewById(R.id.tvItemName);
        View line = tab.getCustomView().findViewById(R.id.vItem);
        if (isSelect) {
            //选中加粗
            tabSelect.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            tabSelect.setText(tab.getText());
            line.setVisibility(View.VISIBLE);
        } else {
            tabSelect.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            tabSelect.setText(tab.getText());
            line.setVisibility(View.GONE);
        }
    }

    /**
     * 更新UI-退款订单详情
     *
     * @param data
     */
    private void setUIInfo(RefundInfoData data) {
        if (data == null) {
            mBinding.linInfo.setVisibility(View.GONE);
            mBinding.tvNothing.setVisibility(View.VISIBLE);
            return;
        }
        mBinding.linInfo.setVisibility(View.VISIBLE);
        mBinding.tvNothing.setVisibility(View.GONE);

        //退款状态 1.待审核 3.已退款 4.已拒绝
        if (data.getRetListHandlestate() == 1) {
            mBinding.tvOrderCancel.setVisibility(View.VISIBLE);
            mBinding.tvOrderConfirm.setVisibility(View.VISIBLE);
        } else {
            mBinding.tvOrderCancel.setVisibility(View.GONE);
            mBinding.tvOrderConfirm.setVisibility(View.GONE);
        }
        mBinding.tvNo.setText(data.getRetListUnique());
        mBinding.tvTime.setText(data.getRetListDatetime());
        if (TextUtils.isEmpty(data.getRetOriginMsg())) {
            mBinding.tvName.setText(TextUtils.isEmpty(data.getSaleListPhone()) ? "-" : data.getSaleListPhone());
        } else {
            mBinding.tvName.setText(TextUtils.isEmpty(data.getSaleListPhone()) ? data.getRetOriginMsg() : data.getRetOriginMsg() + "-" + data.getSaleListPhone());
        }
        mBinding.tvAds.setText(TextUtils.isEmpty(data.getSaleListAddress()) ? "-" : data.getSaleListAddress());

        //商品信息
        goodsList.clear();
        if (data.getDetailList() != null) {
            goodsList.addAll(data.getDetailList());
        }
        goodsAdapter.setDataList(goodsList);

        mBinding.tvTotal.setText(DFUtils.getNum2(data.getRetListTotal()));
        mBinding.tvReason.setText(TextUtils.isEmpty(data.getRetListReason()) ? "-" : data.getRetListReason());
        mBinding.tvOrderNo.setText(data.getSaleListUnique());
    }

    /**
     * 获取每种状态下的订单数量
     */
    private void getOrderCount() {
//        Map<String, Object> params = new HashMap<>();
//        params.put("shopUnique", getShopUnique());
//        params.put("orderType", 2);//1.销售订单 2.退款单
//        params.put("startTime", startDate + " 00:00");
//        params.put("endTime", endDate + " 23:59");
//        RXHttpUtil.requestByFormPostAsOriginalResponse(getActivity(),
//                ZURL.getOrderCount(),
//                params,
//                new RequestListener<String>() {
//                    @Override
//                    public void success(String s) {
//                        OrderCountData data = new Gson().fromJson(s, OrderCountData.class);
//                        countList.clear();
//                        for (int i = 0; i < 4; i++) {
//                            countList.add(0);
//                        }
//                        if (data != null) {
//                            if (data.getStatus() == 0 && data.getData() != null) {
//                                countList.set(0, data.getData().getRetTotalCount());
//                                countList.set(1, data.getData().getAuditCount());
//                                countList.set(2, data.getData().getRefundCount());
//                                countList.set(3, data.getData().getRefuseCount());
//                            }
//                        }
//                        setUpTabBadge();
//                    }
//                });
    }

    /**
     * 退款订单详情
     */
    private void getOrderInfo() {
        Map<String, Object> params = new HashMap<>();
        params.put("retListUnique", retListUnique);
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(getActivity(),
                ZURL.getRefundInfo(),
                params,
                RefundInfoData.class,
                new RequestListener<RefundInfoData>() {
                    @Override
                    public void success(RefundInfoData data) {
                        hideDialog();
                        setUIInfo(data);
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                        mBinding.linInfo.setVisibility(View.GONE);
                        mBinding.tvNothing.setVisibility(View.VISIBLE);
                    }
                });
    }

    /**
     * 退款
     */
    private void postRefund(int status, String reason) {
        Map<String, Object> params = new HashMap<>();
        params.put("retListHandlestate", status);//3.确认退款 4.拒绝退款
        params.put("retListUnique", retListUnique);
        if (status == 4) {
            params.put("retListRemarks", reason);
        }
        params.put("staffId", getStaffUnique());
        params.put("macId", "安卓百货商家端");
        showDialog();
        RXHttpUtil.requestByFormPostAsResponse(this,
                ZURL.getRefundUpdate(),
                params,
                String.class,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        hideDialog();
                        showToast(0, s);
                        //刷新订单数量、列表、详情
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.REFUND_COUNT));
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(4, Constants.REFUND_LIST_UPDATE));
                        getOrderInfo();
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        showToast(1, msg);
                    }
                });
    }
}
