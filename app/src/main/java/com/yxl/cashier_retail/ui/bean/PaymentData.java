package com.yxl.cashier_retail.ui.bean;

import org.litepal.crud.LitePalSupport;

import java.io.Serializable;

/**
 * Describe:支付方式（实体类）
 * Created by jingang on 2024/5/10
 */
public class PaymentData extends LitePalSupport implements Serializable {
    private int id;
    private int paymentId;//1.现金 2.支付宝 3.微信 4.银行卡 5.人脸 6.储值卡 7.组合 8.商品管理
    private boolean select;

    public PaymentData() {
    }

    public PaymentData(int paymentId, boolean select) {
        this.paymentId = paymentId;
        this.select = select;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(int paymentId) {
        this.paymentId = paymentId;
    }

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }
}
