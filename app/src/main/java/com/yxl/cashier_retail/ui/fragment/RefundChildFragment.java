package com.yxl.cashier_retail.ui.fragment;

import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;

import com.scwang.smart.refresh.layout.api.RefreshLayout;
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.ZURL;
import com.yxl.cashier_retail.base.LazyBaseFragment;
import com.yxl.cashier_retail.databinding.FmRefundChildBinding;
import com.yxl.cashier_retail.ui.adapter.RefundAdapter;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.RefundData;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListListener;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe:网单-退款
 * Created by jingang on 2024/6/19
 */
public class RefundChildFragment extends LazyBaseFragment<FmRefundChildBinding> {
    private boolean isVisible,//当前fragment是否显示
            isFirst = true;//首次进入
    private final int status;
    private int pos;

    private RefundAdapter mAdapter;
    private List<RefundData> dataList = new ArrayList<>();

    public RefundChildFragment(int status) {
        this.status = status;
    }

    @Override
    protected void setListener() {
        setAdapter();
    }

    @Override
    protected void initData() {

    }

    @Override
    protected FmRefundChildBinding getViewBinding() {
        return FmRefundChildBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void onFragmentFirstVisible() {
        super.onFragmentFirstVisible();
        getRefundList();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        this.isVisible = isVisibleToUser;
        if (isVisible && !isFirst) {
            page = 1;
            getRefundList();
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        switch (event.getMsg()) {
            case Constants.REFUND_LIST:
                //订单列表
                if (isVisible) {
                    page = 1;
                    getRefundList();
                }
                break;
            case Constants.REFUND_LIST_UPDATE:
                //订单列表局部刷新
                if (isVisible) {
                    if (dataList.size() > pos) {
                        if (status == -1) {
                            dataList.get(pos).setRetListHandlestate(event.getNum());
                            mAdapter.notifyItemChanged(pos, dataList);
                        } else {
                            dataList.remove(pos);
                            mAdapter.remove(pos);
                            if (dataList.size() > 0) {
                                pos = 0;
                                dataList.get(pos).setSelect(true);
                                mAdapter.notifyItemChanged(pos, dataList);
                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(dataList.get(pos).getRetListUnique(), Constants.REFUND_INFO));
                            } else {
                                page = 1;
                                getRefundList();
                            }
                        }
                    }
                }
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new RefundAdapter(getActivity());
        mBinding.recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            if (!dataList.get(position).isSelect()) {
                for (int i = 0; i < dataList.size(); i++) {
                    if (dataList.get(i).isSelect()) {
                        dataList.get(i).setSelect(false);
                        mAdapter.notifyItemChanged(i, dataList);
                    }
                }
                dataList.get(position).setSelect(true);
                mAdapter.notifyItemChanged(position, dataList);
                pos = position;
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(dataList.get(position).getRetListUnique(), Constants.REFUND_INFO));
            }
        });
        mBinding.smartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                page++;
                getRefundList();
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                page = 1;
                getRefundList();
            }
        });
    }

    /**
     * 退款订单列表
     */
    private void getRefundList() {
        if (isFirst) {
            isFirst = false;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("shopUnique", getShopUnique());
        params.put("retListHandlestate", status);
        params.put("page", page);
        params.put("pageSize", Constants.limit);
        //日期
        if (!TextUtils.isEmpty(RefundFragment.startDate)) {
            params.put("startTime", RefundFragment.startDate + " 00:00:00");
        }
        if (!TextUtils.isEmpty(RefundFragment.endDate)) {
            params.put("endTime", RefundFragment.endDate + " 23:59:59");
        }
        showDialog();
        RXHttpUtil.requestByFormPostAsResponseList(this,
                ZURL.getRefundList(),
                params,
                RefundData.class,
                new RequestListListener<RefundData>() {
                    @Override
                    public void onResult(List<RefundData> list) {
                        hideDialog();
                        if (page == 1) {
                            mBinding.smartRefreshLayout.finishRefresh();
                            dataList.clear();
                            dataList.addAll(list);
                            if (dataList.size() > 0) {
                                pos = 0;
                                dataList.get(pos).setSelect(true);
                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(dataList.get(pos).getRetListUnique(), Constants.REFUND_INFO));
                            } else {
                                EventBusManager.getInstance().getGlobaEventBus().post(new EventData("", Constants.REFUND_INFO));
                            }
                        } else {
                            mBinding.smartRefreshLayout.finishLoadMore();
                            dataList.addAll(list);
                        }
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                            mAdapter.setDataList(dataList);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        hideDialog();
                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData("", Constants.REFUND_INFO));
                        if (page == 1) {
                            mBinding.smartRefreshLayout.finishRefresh();
                        } else {
                            mBinding.smartRefreshLayout.finishLoadMore();
                        }
                        if (dataList.size() > 0) {
                            mBinding.recyclerView.setVisibility(View.VISIBLE);
                            mBinding.linEmpty.setVisibility(View.GONE);
                        } else {
                            mBinding.recyclerView.setVisibility(View.GONE);
                            mBinding.linEmpty.setVisibility(View.VISIBLE);
                        }
                    }
                });
    }
}
