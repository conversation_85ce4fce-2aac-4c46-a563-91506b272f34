package com.yxl.cashier_retail.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseDialog;
import com.yxl.cashier_retail.databinding.DialogGoodsNoBarcodeBinding;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.cashier_retail.view.NumberKeyBoardView;
import com.yxl.commonlibrary.utils.DensityUtils;

/**
 * Describe:dialog（添加无码商品）
 * Created by jingang on 2024/09/19
 */
@SuppressLint("NonConstantResourceId")
public class GoodsNoBarcodeDialog extends BaseDialog<DialogGoodsNoBarcodeBinding> {
    public static GoodsNoBarcodeDialog dialog;
    private static int type;//1.无码称重 2.无码商品
    private static double count = 1,
            price;

    public static GoodsNoBarcodeDialog getDialog() {
        return dialog;
    }

    public static void showDialog(Activity activity, int type, MyListener listener) {
        if (activity == null) {
            return;
        }
        if (activity.isFinishing()) {
            return;
        }
        GoodsNoBarcodeDialog.type = type;
        GoodsNoBarcodeDialog.listener = listener;
        GoodsNoBarcodeDialog.price = 0;
        GoodsNoBarcodeDialog.count = 1;
        dialog = new GoodsNoBarcodeDialog(activity);
        dialog.mBinding.numberKeyBoardView.setResultStr("");
        dialog.getWindow().setLayout(DensityUtils.getScreenWidth(dialog.getContext()) / 2, ViewGroup.LayoutParams.WRAP_CONTENT);
        dialog.show();
    }

    /**
     * 串口秤数据
     *
     * @param weight
     */
    public void setWeight(double weight) {
//        Log.e("GoodsNoBarcodeDialog", "weight = " + weight + " type = " + type);
        weight = Double.parseDouble(DFUtils.getNum4(weight));
        if (type == 1 && weight > 0) {
            count = weight;
        } else {
            count = 1;
        }
        mBinding.tvDialogCount.setText(DFUtils.getNum4(count));
    }

    public GoodsNoBarcodeDialog(@NonNull Context context) {
        super(context, R.style.dialog_style);
        setCancelable(true);
        ((AnimationDrawable) mBinding.ivDialogCursor.getDrawable()).start();
        mBinding.ivDialogClose.setOnClickListener(v -> dismiss());
//        mBinding.ivDialogClose.setOnClickListener(v -> {
//            setWeight(0.775);
//        });
        mBinding.tvDialogTitle.setText(type == 1 ? getRstr(R.string.no_barcode_weight_add) : getRstr(R.string.no_barcode_goods_add));
        mBinding.tvDialogCountValue.setText(type == 1 ? getRstr(R.string.weight) + "(kg)" : getRstr(R.string.count));
        mBinding.numberKeyBoardView.setOnMValueChangedListener(new NumberKeyBoardView.OnMValueChangedListener() {
            @Override
            public void onChange(String var) {
                mBinding.tvDialogPrice.setText(var);
                price = TextUtils.isEmpty(var) ? 0 : Double.parseDouble(var);
                mBinding.tvDialogTotal.setText(DFUtils.getNum2(DFUtils.getDouble(price * count)));
            }

            @Override
            public void onConfirm() {
                if (price == 0) {
                    showToast(1, getRstr(R.string.input_price));
                    return;
                }
                if (listener != null) {
                    listener.onConfirm(price, count);
                    dismiss();
                }
            }
        });
    }

    @Override
    protected DialogGoodsNoBarcodeBinding getViewBinding() {
        return DialogGoodsNoBarcodeBinding.inflate(getLayoutInflater());
    }

    @Override
    public void show() {
        super.show();
        getWindow().setGravity(Gravity.CENTER);
    }

    private static MyListener listener;

    public interface MyListener {
        void onConfirm(double price, double count);
    }
}
