package com.yxl.cashier_retail.ui.bean;

import java.util.List;

/**
 * Describe: 供货商列表-网页端（实体类）
 * Created by jingang on 2025/2/27
 */
public class WebSupplierData {

    /**
     * code : 200
     * msg : 操作成功
     * data : [{"supplier_unique":3548513567536,"supplier_name":"酒水供货商"}]
     */

    private int code;
    private int status;
    private String msg;
    private List<DataBean> data;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<DataBean> getData() {
        return data;
    }

    public void setData(List<DataBean> data) {
        this.data = data;
    }

    public static class DataBean {
        /**
         * supplier_unique : 3548513567536
         * supplier_name : 酒水供货商
         */

        private String supplier_unique;
        private String supplier_name;

        public String getSupplier_unique() {
            return supplier_unique;
        }

        public void setSupplier_unique(String supplier_unique) {
            this.supplier_unique = supplier_unique;
        }

        public String getSupplier_name() {
            return supplier_name;
        }

        public void setSupplier_name(String supplier_name) {
            this.supplier_name = supplier_name;
        }
    }
}
