package com.yxl.cashier_retail.ui.contract;

import com.yxl.cashier_retail.ui.bean.RefundInfoData;
import com.yxl.cashier_retail.ui.bean.RefundData;
import com.yxl.commonlibrary.base.BasePresenter;
import com.yxl.commonlibrary.base.BaseView;

import java.util.List;

/**
 * Describe:退款订单
 * Created by jingang on 2024/5/22
 */
public class RefundContact {
    public interface View extends BaseView {
        void onListSuccess(List<RefundData> list);

        void onListError(String msg);

        void onInfoSuccess(RefundInfoData data);

        void onInfoError(String msg);
    }

    public interface Presenter extends BasePresenter<View> {
        void getRefundList(String shopUnique, String startTime, String endTime, int page, int limit);

        void getRefundInfo(String retListUnique);
    }
}
