package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.OrderListData;

/**
 * Describe:查询-交接班记录-收银订单（适配器）
 * Created by jingang on 2024/8/23
 */
public class ShiftInfoOrderAdapter extends BaseAdapter<OrderListData.DataBean> {

    public ShiftInfoOrderAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_shift_info_order;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvTime, tvNo, tvTotal;
        tvTime = holder.getView(R.id.tvItemTime);
        tvNo = holder.getView(R.id.tvItemNo);
        tvTotal = holder.getView(R.id.tvItemTotal);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
        }
        tvTime.setText(TextUtils.isEmpty(mDataList.get(position).getSale_list_datetime()) ? "-" : mDataList.get(position).getSale_list_datetime());
        tvNo.setText(mDataList.get(position).getSale_list_unique());
        tvTotal.setText(mDataList.get(position).getSale_list_total());
    }
}
