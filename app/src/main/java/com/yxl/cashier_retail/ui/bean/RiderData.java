package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;

/**
 * Describe:骑手列表（实体类）
 * Created by jingang on 2023/5/24
 */
public class RiderData implements Serializable {
    /**
     * shop_unique : 1536215939565
     * update_time : 1724827261000
     * courier_name : 哈哈哈
     * create_times : 2024-08-28 14:41:01
     * create_time : 1724827261000
     * id : 744
     * update_times : 2024-08-28 14:41:01
     * courier_phone : 17865069350
     */
    private boolean select;
    private String shop_unique;
    private long update_time;
    private String courier_name;//骑手名称
    private String create_times;//创建时间
    private long create_time;
    private int id;
    private String update_times;//更新时间
    private String courier_phone;//骑手电话

    public boolean isSelect() {
        return select;
    }

    public void setSelect(boolean select) {
        this.select = select;
    }

    public String getShop_unique() {
        return shop_unique;
    }

    public void setShop_unique(String shop_unique) {
        this.shop_unique = shop_unique;
    }

    public long getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(long update_time) {
        this.update_time = update_time;
    }

    public String getCourier_name() {
        return courier_name;
    }

    public void setCourier_name(String courier_name) {
        this.courier_name = courier_name;
    }

    public String getCreate_times() {
        return create_times;
    }

    public void setCreate_times(String create_times) {
        this.create_times = create_times;
    }

    public long getCreate_time() {
        return create_time;
    }

    public void setCreate_time(long create_time) {
        this.create_time = create_time;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getUpdate_times() {
        return update_times;
    }

    public void setUpdate_times(String update_times) {
        this.update_times = update_times;
    }

    public String getCourier_phone() {
        return courier_phone;
    }

    public void setCourier_phone(String courier_phone) {
        this.courier_phone = courier_phone;
    }
}
