package com.yxl.cashier_retail.ui.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.PurchaseInfoData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.utils.StringUtils;

import java.util.List;

/**
 * Describe:购销单详情-商品列表（适配器）
 * Created by jingang on 2024/6/12
 */
public class PurchaseGoodsAdapter extends BaseAdapter<PurchaseInfoData.GoodsListBean> {

    private int status;//1-已提交(待收货)，2-已收货(待付款)，3-待确认，4-已完成，5-异常，6-作废

    public void setStatus(int status) {
        this.status = status;
    }

    public PurchaseGoodsAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_purchase_goods;
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position, List<Object> payloads) {
        super.onBindItemHolder(holder, position, payloads);
        TextView tvOperate = holder.getView(R.id.tvItemOperate);
        if (status == 1) {
            tvOperate.setVisibility(View.VISIBLE);
            //是否核对 0.否 1.是
            if (mDataList.get(position).getGoodsStatus() == 1) {
                tvOperate.setText("撤销核对");
                tvOperate.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                tvOperate.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
            } else {
                tvOperate.setText("核对");
                tvOperate.setBackgroundResource(R.drawable.shape_green_5);
                tvOperate.setTextColor(mContext.getResources().getColor(R.color.white));
            }
        } else {
            tvOperate.setVisibility(View.INVISIBLE);
        }
    }

    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        ImageView ivImg = holder.getView(R.id.ivItemImg);
        TextView tvName, tvCount, tvDelivery, tvPrice, tvTotal, tvSuggest, tvReal, tvOperate;
        tvName = holder.getView(R.id.tvItemName);
        tvCount = holder.getView(R.id.tvItemCount);
        tvDelivery = holder.getView(R.id.tvItemDelivery);
        tvPrice = holder.getView(R.id.tvItemPrice);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvSuggest = holder.getView(R.id.tvItemSuggest);
        tvReal = holder.getView(R.id.tvItemReal);
        tvOperate = holder.getView(R.id.tvItemOperate);

        Glide.with(mContext)
                .load(StringUtils.handledImgUrl(mDataList.get(position).getGoodsPicturepath()))
                .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img))
                .into(ivImg);
        tvName.setText(mDataList.get(position).getGoodsName());
        tvCount.setText(DFUtils.getNum4(mDataList.get(position).getPurchaseGoodsCount()));
        tvDelivery.setText(DFUtils.getNum4(mDataList.get(position).getTradeGoodsCount()));
        tvPrice.setText(DFUtils.getNum2(mDataList.get(position).getPurchasePrice()));
        tvTotal.setText(DFUtils.getNum2(mDataList.get(position).getTotalPrice()));
        tvSuggest.setText(DFUtils.getNum2(mDataList.get(position).getSalePrice()));
        tvReal.setText(DFUtils.getNum4(mDataList.get(position).getReceiptsReceivedCount()));
        if (status == 1) {
            tvOperate.setVisibility(View.VISIBLE);
            //是否核对 0.否 1.是
            if (mDataList.get(position).getGoodsStatus() == 1) {
                tvOperate.setText("撤销核对");
                tvOperate.setBackgroundResource(R.drawable.shape_d8_kuang_5);
                tvOperate.setTextColor(mContext.getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
            } else {
                tvOperate.setText("核对");
                tvOperate.setBackgroundResource(R.drawable.shape_green_5);
                tvOperate.setTextColor(mContext.getResources().getColor(R.color.white));
            }
        } else {
            tvOperate.setVisibility(View.INVISIBLE);
        }

        if (listener != null) {
            tvOperate.setOnClickListener(v -> listener.onOperateClick(v, position));
        }
    }

    private MyListener listener;

    public void setListener(MyListener listener) {
        this.listener = listener;
    }

    public interface MyListener {
        void onOperateClick(View view, int position);
    }
}
