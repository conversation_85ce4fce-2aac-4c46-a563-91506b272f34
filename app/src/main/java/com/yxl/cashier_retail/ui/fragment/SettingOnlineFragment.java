package com.yxl.cashier_retail.ui.fragment;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import com.alibaba.fastjson.JSONArray;
import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.MyApplication;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseFragment;
import com.yxl.cashier_retail.databinding.FmSettingOnlineBinding;
import com.yxl.cashier_retail.ui.bean.ConditionData;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.dialog.ReceiptPrintWifiDialog;
import com.yxl.cashier_retail.ui.popupwindow.ConditionPop;
import com.yxl.cashier_retail.utils.DateUtils;
import com.yxl.cashier_retail.utils.PrintReceiptUsbUtils;
import com.yxl.cashier_retail.utils.ZxingUtils;
import com.yxl.commonlibrary.utils.EventBusManager;

import org.greenrobot.eventbus.Subscribe;

import java.util.ArrayList;
import java.util.List;

/**
 * Describe:设置-线上设置
 * Created by jingang on 2024/5/13
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class SettingOnlineFragment extends BaseFragment<FmSettingOnlineBinding> implements View.OnClickListener {
    private boolean isUsePrintAutoOrder,//自动接单打印
            isUsePrintAutoRefund,//自动打印退款单
            isReceiptPrinterConnect;//小票打印机是否连接
    private String receiptTips;//小票：温馨提示

    private int receiptPrintType,//小票打印机连接方式 1.蓝牙 2.端口 3.USB 4.WIFI
            printCount;

    private final List<ConditionData> receiptPrintTypeList = new ArrayList<>();//小票打印机连接方式

    @Override
    protected FmSettingOnlineBinding getViewBinding() {
        return FmSettingOnlineBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        mBinding.ivAutoOrderPrint.setOnClickListener(this);
        mBinding.ivRefundOrderPrint.setOnClickListener(this);
        //小票设置
        mBinding.linReceiptPrint.setOnClickListener(this);
        mBinding.ivReceiptPrintCountSub.setOnClickListener(this);
        mBinding.ivReceiptPrintCountAdd.setOnClickListener(this);
        mBinding.tvReceiptPrintTest.setOnClickListener(this);
        mBinding.etReceiptTips.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                receiptTips = s.toString().trim();
                mBinding.tvReceiptTipsCount.setText("（" + receiptTips.length() + "/50)");
                mBinding.tvReceiptTips.setText(getRstr(R.string.tips_warm_colon) + receiptTips);
            }
        });
        mBinding.etReceiptTips.setOnEditorActionListener((v, actionId, event) -> {
            SPUtils.getInstance().put(Constants.RECEIPT_TIPS, receiptTips);
            hideSoftInput(getActivity());
            return true;
        });
    }

    @Override
    protected void initData() {
        setUI();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.ivAutoOrderPrint:
                //自动接单打印
                SPUtils.getInstance().put(Constants.IS_USE_PRINT_AUTO_ORDER, isUsePrintAutoOrder ? "" : Constants.IS_USE_PRINT_AUTO_ORDER);
                isUsePrintAutoOrder = !isUsePrintAutoOrder;
                mBinding.ivAutoOrderPrint.setSelected(isUsePrintAutoOrder);
                break;
            case R.id.ivRefundOrderPrint:
                //自动打印退款单
                SPUtils.getInstance().put(Constants.IS_USE_PRINT_AUTO_REFUND, isUsePrintAutoRefund ? "" : Constants.IS_USE_PRINT_AUTO_REFUND);
                isUsePrintAutoRefund = !isUsePrintAutoRefund;
                mBinding.ivRefundOrderPrint.setSelected(isUsePrintAutoRefund);
                break;
            case R.id.linReceiptPrint:
                //选择打印机连接方式
                ConditionPop.showDialog(getActivity(),
                        mBinding.ivReceiptPrint,
                        mBinding.linReceiptPrint,
                        mBinding.linReceiptPrint.getMeasuredWidth(),
                        receiptPrintTypeList,
                        receiptPrintType,
                        data -> {
                            receiptPrintType = data.getId();
                            mBinding.tvReceiptPrint.setText(data.getName());
                            SPUtils.getInstance().put(Constants.RECEIPT_PRINTER_CONNECT_TYPE, data.getId());
                            switch (receiptPrintType) {
                                case 3:
                                    MyApplication.getInstance().connectReceiptPrinter();
                                    break;
                                case 4:
                                    ReceiptPrintWifiDialog.showDialog(getActivity(), ip -> {
                                        MyApplication.getInstance().connectNet(ip);
                                    });
                                    break;
                            }
                        });
                break;
            case R.id.ivReceiptPrintCountSub:
                //默认打印数量-减少
                if (printCount > 1) {
                    printCount--;
                    SPUtils.getInstance().put(Constants.DEFAULT_PRINT_COUNT, printCount);
                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.DEFAULT_PRINT_COUNT));
                }
                break;
            case R.id.ivReceiptPrintCountAdd:
                //默认打印数量-增加
                if (printCount < 100) {
                    printCount++;
                    SPUtils.getInstance().put(Constants.DEFAULT_PRINT_COUNT, printCount);
                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.DEFAULT_PRINT_COUNT));
                }
                break;
            case R.id.tvReceiptPrintTest:
                //打印测试
                if (!isReceiptPrinterConnect) {
                    showToast(1, getRstr(R.string.printer_no_connect));
                    return;
                }
//                printReceipt();
                Bitmap bitmap = ZxingUtils.createBarcode("123456", 614, 100);
                PrintReceiptUsbUtils.printOnline(getActivity(),
                        new ArrayList<>(), 0.00, 0.00, 0.00,
                        new JSONArray(),
                        null, 0, 0,
                        "123456", bitmap);
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (!EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().register(this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBusManager.getInstance().getGlobaEventBus().isRegistered(this)) {
            EventBusManager.getInstance().getGlobaEventBus().unregister(this);
        }
    }

    @Subscribe
    public void onEventReceive(EventData event) {
        if (event == null) {
            return;
        }
        String msg = event.getMsg();
        if (TextUtils.isEmpty(msg)) {
            return;
        }
        switch (msg) {
            case Constants.XPRINTER:
                //小票打印机状态监听 1.连接成功 2.连接失败 3.发送失败 4.连接中断 5.USB设备已连上 6.USB设备已断开
                switch (event.getNum()) {
                    case 1:
                        isReceiptPrinterConnect = true;
                        mBinding.vReceiptPrinterStatus.setBackgroundResource(R.drawable.shape_yuan_green);
                        mBinding.tvReceiptPrinterStatus.setText(getRstr(R.string.connected));
                        mBinding.tvReceiptPrinterStatus.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                        break;
                    case 3:
                        showToast(1, getRstr(R.string.send_fail));
                        break;
                    case 5:
                        isReceiptPrinterConnect = true;
                        mBinding.vReceiptPrinterStatus.setBackgroundResource(R.drawable.shape_yuan_green);
                        mBinding.tvReceiptPrinterStatus.setText(getRstr(R.string.connected));
                        mBinding.tvReceiptPrinterStatus.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
                        break;
                    default:
                        isReceiptPrinterConnect = false;
                        mBinding.vReceiptPrinterStatus.setBackgroundResource(R.drawable.shape_yuan_red);
                        mBinding.tvReceiptPrinterStatus.setText(getRstr(R.string.connect_no));
                        mBinding.tvReceiptPrinterStatus.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.red));
                        break;
                }
                if (isReceiptPrinterConnect) {
                    showToast(0, getRstr(R.string.printer_connect_success));
                } else {
                    showToast(1, getRstr(R.string.printer_connect_fail));
                }
                break;
            case Constants.DEFAULT_PRINT_COUNT:
                //默认打印数量
                mBinding.tvReceiptPrintCount.setText(String.valueOf(SPUtils.getInstance().getInt(Constants.DEFAULT_PRINT_COUNT, 1)));
                break;
        }
    }

    /**
     * 更新UI
     */
    private void setUI() {
        //自动接单打印
        isUsePrintAutoOrder = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_PRINT_AUTO_ORDER, ""));
        mBinding.ivAutoOrderPrint.setSelected(isUsePrintAutoOrder);
        //自动打印退款单
        isUsePrintAutoRefund = !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_PRINT_AUTO_REFUND, ""));
        mBinding.ivRefundOrderPrint.setSelected(isUsePrintAutoRefund);
        //打印模版
        mBinding.tvReceiptPrinter.setText(getRstr(R.string.cash_register_colon) + "001");
        mBinding.tvReceiptStaff.setText(getRstr(R.string.cashier_staff_colon) + getRstr(R.string.cash_staff_name));
        mBinding.tvReceiptDiscount.setText(getRstr(R.string.discount_colon) + "0.00" + getRstr(R.string.yuan));
        mBinding.tvReceiptPayment.setText(getRstr(R.string.payable_colon) + "3.00" + getRstr(R.string.yuan));
        mBinding.tvReceiptActualPayment.setText(getRstr(R.string.paid_colon) + "3.00" + getRstr(R.string.yuan));
        mBinding.tvReceiptBalance.setText(getRstr(R.string.balance_colon) + "0.00" + getRstr(R.string.yuan));
        mBinding.tvReceiptCash.setText(getRstr(R.string.cash_colon) + "3.00" + getRstr(R.string.yuan));
        mBinding.tvReceiptMember.setText(getRstr(R.string.member_colon) + "*** 138****8888");
        mBinding.tvReceiptPoints.setText(getRstr(R.string.order_points_colon) + "0");
        mBinding.tvReceiptMemberPoints.setText(getRstr(R.string.member_points_colon) + "0");
        mBinding.tvReceiptMobile.setText(getRstr(R.string.contact_mobile_colon) + "13888888888");
        //小票打印机连接方式
        receiptPrintType = SPUtils.getInstance().getInt(Constants.RECEIPT_PRINTER_CONNECT_TYPE, 3);
        receiptPrintTypeList.clear();
//        receiptPrintTypeList.add(new ConditionData(1, getRstr(R.string.connect_bt), false));
//        receiptPrintTypeList.add(new ConditionData(2, getRstr(R.string.connect_serail), false));
        receiptPrintTypeList.add(new ConditionData(3, getRstr(R.string.connect_usb), false));
        receiptPrintTypeList.add(new ConditionData(4, getRstr(R.string.connect_wifi), false));
        switch (receiptPrintType) {
            case 1:
                mBinding.tvReceiptPrint.setText(getRstr(R.string.connect_bt));
                break;
            case 2:
                mBinding.tvReceiptPrint.setText(getRstr(R.string.connect_serail));
                break;
            case 4:
                mBinding.tvReceiptPrint.setText(getRstr(R.string.connect_wifi));
                break;
            default:
                mBinding.tvReceiptPrint.setText(getRstr(R.string.connect_usb));
                break;
        }
        this.isReceiptPrinterConnect = MyApplication.getInstance().isReceiptPrinterConnect;
        if (isReceiptPrinterConnect) {
            mBinding.vReceiptPrinterStatus.setBackgroundResource(R.drawable.shape_yuan_green);
            mBinding.tvReceiptPrinterStatus.setText(getRstr(R.string.connected));
            mBinding.tvReceiptPrinterStatus.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.green));
        } else {
            mBinding.vReceiptPrinterStatus.setBackgroundResource(R.drawable.shape_yuan_red);
            mBinding.tvReceiptPrinterStatus.setText(getRstr(R.string.connect_no));
            mBinding.tvReceiptPrinterStatus.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.red));
        }
        mBinding.tvReceiptDate.setText(DateUtils.getCurrentDate(DateUtils.PATTERN_SECOND));
        //默认打印数量
        printCount = SPUtils.getInstance().getInt(Constants.DEFAULT_PRINT_COUNT, 1);
        mBinding.tvReceiptPrintCount.setText(String.valueOf(printCount));
        //温馨提示
        receiptTips = SPUtils.getInstance().getString(Constants.RECEIPT_TIPS, getRstr(R.string.thanks_welcome_again));
        mBinding.tvReceiptTipsCount.setText("（" + receiptTips.length() + "/50)");
        mBinding.etReceiptTips.setText(receiptTips);
        mBinding.tvReceiptTips.setText(getRstr(R.string.tips_warm_colon) + receiptTips);
    }
}
