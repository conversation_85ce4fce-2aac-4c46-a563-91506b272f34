package com.yxl.cashier_retail.ui.contract;

import com.yxl.cashier_retail.ui.bean.PurchaseInfoData;
import com.yxl.cashier_retail.ui.bean.PurchaseListData;
import com.yxl.commonlibrary.base.BasePresenter;
import com.yxl.commonlibrary.base.BaseView;

import java.util.List;

/**
 * Describe:入库-购销单
 * Created by jingang on 2024/5/24
 */
public class GouXContract {
    public interface View extends BaseView {
        void onListSuccess(List<PurchaseListData> list);

        void onListError(String msg);

        void onInfoSuccess(PurchaseInfoData data);

        void onInfoError(String msg);
    }

    public interface Presenter extends BasePresenter<View> {
        void getGouXOrderList(int status, int page);

        void getGouXOrderInfo(String id);
    }
}
