package com.yxl.cashier_retail.ui.bean;

import java.io.Serializable;

/**
 * Describe:商品销量信息（实体类）
 * Created by jingang on 2024/6/6
 */
public class RestockGoodsSaleData implements Serializable {
    /**
     * goodsBarcode : 6923083028089
     * goodsName : 猫王抽纸
     * goodsChengType : 0
     * goodsCount : 1.0
     * outStockCount : 0
     * count3 : 1.0
     * count7 : -4.0
     * bestCount : -5
     * lastCreateTime : 2023-08-26 11:08:28
     * lastGoodsCount : 3
     * lastGoodsInPrice : 7.00
     */

    private String goodsBarcode;
    private String goodsName;
    private String supplierUnique;
    private String supplierName;
    private int goodsChengType;
    private double goodsCount;//可销库存
    private double outStockCount;//安全库存
    private double count3;//3日销量
    private double count7;//7日销量
    private double bestCount;//建议采购量
    private String lastCreateTime;//上次采购时间
    private double lastGoodsCount;//上次采购数量
    private double lastGoodsInPrice;//上次采购进货价
    private String goodsUnit;
    private int purchaseType;//采购类型:1-购销2自采

    public String getGoodsBarcode() {
        return goodsBarcode;
    }

    public void setGoodsBarcode(String goodsBarcode) {
        this.goodsBarcode = goodsBarcode;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getSupplierUnique() {
        return supplierUnique;
    }

    public void setSupplierUnique(String supplierUnique) {
        this.supplierUnique = supplierUnique;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public int getGoodsChengType() {
        return goodsChengType;
    }

    public void setGoodsChengType(int goodsChengType) {
        this.goodsChengType = goodsChengType;
    }

    public double getGoodsCount() {
        return goodsCount;
    }

    public void setGoodsCount(double goodsCount) {
        this.goodsCount = goodsCount;
    }

    public double getOutStockCount() {
        return outStockCount;
    }

    public void setOutStockCount(double outStockCount) {
        this.outStockCount = outStockCount;
    }

    public double getCount3() {
        return count3;
    }

    public void setCount3(double count3) {
        this.count3 = count3;
    }

    public double getCount7() {
        return count7;
    }

    public void setCount7(double count7) {
        this.count7 = count7;
    }

    public double getBestCount() {
        return bestCount;
    }

    public void setBestCount(double bestCount) {
        this.bestCount = bestCount;
    }

    public String getLastCreateTime() {
        return lastCreateTime;
    }

    public void setLastCreateTime(String lastCreateTime) {
        this.lastCreateTime = lastCreateTime;
    }

    public double getLastGoodsCount() {
        return lastGoodsCount;
    }

    public void setLastGoodsCount(double lastGoodsCount) {
        this.lastGoodsCount = lastGoodsCount;
    }

    public double getLastGoodsInPrice() {
        return lastGoodsInPrice;
    }

    public void setLastGoodsInPrice(double lastGoodsInPrice) {
        this.lastGoodsInPrice = lastGoodsInPrice;
    }

    public String getGoodsUnit() {
        return goodsUnit;
    }

    public void setGoodsUnit(String goodsUnit) {
        this.goodsUnit = goodsUnit;
    }

    public int getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(int purchaseType) {
        this.purchaseType = purchaseType;
    }
}
