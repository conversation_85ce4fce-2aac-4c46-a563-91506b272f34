package com.yxl.cashier_retail.ui.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseAdapter;
import com.yxl.cashier_retail.base.ViewHolder;
import com.yxl.cashier_retail.ui.bean.RestockData;
import com.yxl.cashier_retail.utils.DFUtils;

/**
 * Describe:自采补货-补货计划-商品列表（适配器）
 * Created by jingang on 2024/6/5
 */
public class RestockChildAdapter extends BaseAdapter<RestockData.GoodsListBean> {
    private int status = 1;

    public void setStatus(int status) {
        this.status = status;
    }

    public RestockChildAdapter(Context context) {
        super(context);
    }

    @Override
    public int getLayoutId() {
        return R.layout.item_restock_child;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindItemHolder(ViewHolder holder, int position) {
        LinearLayout lin = holder.getView(R.id.linItem);
        TextView tvName, tvBarcode, tvCount, tvTotal, tvPrice;
        tvName = holder.getView(R.id.tvItemName);
        tvBarcode = holder.getView(R.id.tvItemBarcode);
        tvCount = holder.getView(R.id.tvItemCount);
        tvTotal = holder.getView(R.id.tvItemTotal);
        tvPrice = holder.getView(R.id.tvItemPrice);
        ImageView ivDel = holder.getView(R.id.ivItemDel);

        if (position % 2 == 0) {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.white));
        } else {
            lin.setBackgroundColor(mContext.getResources().getColor(R.color.color_f2));
        }
        if (status == 1) {
            ivDel.setVisibility(View.VISIBLE);
        } else {
            ivDel.setVisibility(View.GONE);
        }
        tvName.setText(mDataList.get(position).getGoodsName());
        tvBarcode.setText(mDataList.get(position).getGoodsBarcode());
        double count = mDataList.get(position).getGoodsCount(),
                total = mDataList.get(position).getGoodsTotal(),
                price;
        if (count > 0) {
            price = total / count;
        } else {
            price = 0;
        }
        tvCount.setText(DFUtils.getNum4(count));
        tvTotal.setText(DFUtils.getNum2(total));
        if (TextUtils.isEmpty(mDataList.get(position).getGoodsUnit())) {
            tvPrice.setText(DFUtils.getNum2(price));
        } else {
            tvPrice.setText(DFUtils.getNum2(price) + "/" + mDataList.get(position).getGoodsUnit());
        }
        if (onItemClickListener != null) {
            ivDel.setOnClickListener(v -> onItemClickListener.onItemClick(v, position));
        }

    }
}
