package com.yxl.cashier_retail;

/**
 * Describe:接口地址
 * Created by jingang on 2024/5/9
 */
public class ZURL {
    public final static String URL_DEBUG = "http://test170.buyhoo.cc/";//测试
    public final static String URL_DEV = "https://dev-global.buyhoo.cc/";//开发
    public final static String URL_RELEASE = "http://buyhoo.cc/";//正式
    public final static String URL_MALL_DEBUG = "http://test170.buyhoo.cc/";
    public final static String URL_MALL_DEV = "https://dev-global.buyhoo.cc/";
    public final static String URL_MALL_RELEASE = "http://yun.buyhoo.cc/";

    //动态切换域名环境
    public static String ONLINE_URL = BuildConfig.DEBUG ? URL_DEBUG : URL_RELEASE;
    public static String MALL_ONLINE_URL = BuildConfig.DEBUG ? URL_MALL_DEBUG : URL_MALL_RELEASE;

    //检查更新
    public static String getVersion() {
        return URL_RELEASE + "shopmanager/app/shop/updateAppCheck.do";
    }

    public static String getUploadFile() {
        return ONLINE_URL + "shopUpdate/loanMoney/uploadFile.do";
    }

    /********登录start********/
    //登录（接班）
    public static String getLogin() {
        return ONLINE_URL + "shopmanager/pc/pcStaffLogin.do";
    }

    //退出登录（交班）
    public static String getLoginOut() {
        return ONLINE_URL + "shopmanager/pc/staffSignOut.do";
    }

    //获取验证码
    public static String getCode() {
        return ONLINE_URL + "shopUpdate/shopsStaff/sendMessage.do";
    }

    //修改密码
    public static String getUpdatePwd() {
        return ONLINE_URL + "shopUpdate/cash/updateShopsPwd.do";
    }

    /********登录end********/

    /********会员模块start********/

    //会员统计
    public static String getMemberStatistics() {
        return ONLINE_URL + "shopmanager/customer/queryCustomerStat.do";
    }

    //会员到店实时消费
    public static String getMemberToShopTime() {
        return ONLINE_URL + "shopmanager/customer/queryCustomerRealTimeStat.do";
    }

    //会员列表
    public static String getMemberList() {
        return ONLINE_URL + "harricane/customerCheckout/customerPageList.do";
    }

    //会员详情
    public static String getMemberInfo() {
        return ONLINE_URL + "queryShopCusDetail.do";
    }

    //修改会员信息
    public static String getMemberEdit() {
        return ONLINE_URL + "harricane/customerCheckout/saveCustomer.do";
    }

    //会员新增
    public static String getMemberAdd() {
        return ONLINE_URL + "harricane/cuscheckout/addCus.do";
    }

    //会员充值/退费/存零
    public static String getMemberRecharge() {
        return ONLINE_URL + "harricane/cuscheckout/recharge.do";
    }

    //查询充值金额配置
    public static String getMemberRechargeConfig() {
        return ONLINE_URL + "shopUpdate/cash/queryRechargeConfigList.do";
    }

    //查询充值订单状态
    public static String getMemberRechargeStatus() {
        return ONLINE_URL + "harricane/cuscheckout/queryRechargeStatus.do";
    }

    //储值卡充值V2
    public static String getMemberRechargeV2() {
        return ONLINE_URL + "harricane/cuscheckout/rechargeV2.do";
    }

    //会员等级列表
    public static String getMemberLevel() {
        return ONLINE_URL + "harricane/cuscheckout/getCustLevelList.do";
    }

    //批量保存会员等级积分
    public static String getMemberLevelEdit() {
        return ONLINE_URL + "harricane/customerCheckout/saveCustomerLevels.do";
    }

    //会员消费记录
    public static String getMemberRecord() {
        return ONLINE_URL + "harricane/customerCheckout/queryCusConRecord.do";
    }

    //用户积分变动
    public static String getMemberPointsEdit() {
        return ONLINE_URL + "harricane/customerCheckout/updateCusPointsById.do";
    }

    //通过人脸搜索会员信息
    public static String getFaceToMemberInfo() {
        return ONLINE_URL + "shopmanager/app/shop/searchCustomerMsg.do";
    }

    /********会员模块end********/

    /********收银start********/

    //创建订单编号
    public static String getSaleListUniqueCreate() {
        return ONLINE_URL + "shopUpdate/appPay/createSaleListUnique.do";
    }

    //普通线下订单结算(现金、微信、支付宝、银行卡、储值卡)
    public static String getPayment() {
        return ONLINE_URL + "harricane/payOnline/cashierPay.do";
    }

    //易通金服付款码支付
    public static String getPaymentScan() {
        return ONLINE_URL + "harricane/payOnline/yiTongPay.do";
    }

    //扫码支付结果查询
    public static String getPaymentStatus() {
        return ONLINE_URL + "harricane/payOnline/queryOrderYT.do";
    }

    //组合免密支付（收银机单独扫码支付）
    public static String getPaymentScanAlone() {
        return ONLINE_URL + "harricane/payOnline/yiTongPaySale.do";
    }

    //查询组合免密订单状态
    public static String getPaymentScanAloneStatus() {
        return ONLINE_URL + "harricane/payOnline/yiTongPaySaleStatus.do";
    }

    //刷脸支付
    public static String getPaymentFace() {
        return ONLINE_URL + "goBuy/cart/collectMoneyPay.do";
    }

    //取消收款
    public static String getPaymentCancel() {
        return ONLINE_URL + "harricane/payOnline/cancelOrder.do";
    }

    /********收银end********/

    /********商品模块start********/
    //同步虚拟分类信息至PC(虚拟分类列表)
    public static String getCatePcList() {
        return ONLINE_URL + "shopmanager/goodsKindInvented/queryGoodsInventedList.do";
    }

    //新增、编辑、删除虚拟分类信息
    public static String getCatePcEdit() {
        return ONLINE_URL + "shopmanager/goodsKindInvented/addNewGoodsKindInventedMsg.do";
    }

    //删除或增加虚拟分类下的商品信息
    public static String getCatePcGoodsEdit() {
        return ONLINE_URL + "shopmanager/goodsKindInvented/addGoodsKindInventedGoodsList.do";
    }

    //商品分类
    public static String getCateList() {
        return ONLINE_URL + "shopUpdate/goodsKinds/queryGoodsKindsByShop.do";
    }

    //商品分类新增、删除、更新
    public static String getCateEdit() {
        return ONLINE_URL + "shopUpdate/goodsKinds/modifyCustomKinds.do";
    }

    //商品分类图标查询
    public static String getCateImg() {
        return ONLINE_URL + "shopUpdate/goodsKinds/queryGoodsKindIconByIconType.do";
    }

    //商品列表(带分页)
    public static String getGoodsList() {
        return ONLINE_URL + "shopUpdate/goods/queryGoodsMessage.do";
    }

    //全部商品列表(无分页)
    public static String getGoodsListPc() {
        return ONLINE_URL + "shopmanager/pc/pcGoods.do";
    }

    //商品详情
    public static String getGoodsInfo() {
//        return ONLINE_URL + "shopUpdate/goods/goodsDetail.do";
        return ONLINE_URL + "shopUpdate/goods/goodsDetailAndroid.do";
    }

    //生成条码
    public static String getGoodsBarcode() {
//        return ONLINE_URL + "shopUpdate/goods/queryGoodsBarcodeSameForeignkey.do";
        return ONLINE_URL + "shopUpdate/goods/createNewGoodsBarcode.do";
    }

    //商品新增
    public static String getGoodsAdd() {
        return ONLINE_URL + "shopUpdate/goods/v2/addGoods.do";
    }

    //商品编辑
    public static String getGoodsEdit() {
        return ONLINE_URL + "shopUpdate/goods/v2/updateGoods.do";
    }

    //商品删除
    public static String getGoodsDel() {
        return ONLINE_URL + "shopUpdate/goods/deleteShopsGoods.do";
    }

    //商品单位
    public static String getGoodsUnit() {
        return ONLINE_URL + "shopUpdate/goodsUnit/findGoodsUnitList.do";
    }

    //商品-单位新增
    public static String getGoodsUnitAdd() {
        return ONLINE_URL + "shopUpdate/goodsUnit/addGoodsUnit.do";
    }

    //商品-单位删除
    public static String getGoodsUnitDel() {
        return ONLINE_URL + "shopUpdate/goodsUnit/deleteGoodsUnit.do";
    }

    //商品-单位编辑
    public static String getGoodsUnitEdit() {
        return ONLINE_URL + "shopUpdate/goodsUnit/editGoodsUnit.do";
    }

    //出入库原因
    public static String getGoodsStockReason() {
        return ONLINE_URL + "shopUpdate/stock/reason.do";
    }

    //查询批次列表
    public static String getGoodBatchList() {
        return ONLINE_URL + "shopUpdate/goodBatch/queryGoodBatchList.do";
    }

    //商品出入库-修改库存
    public static String getGoodsStockEdit() {
        return ONLINE_URL + "shopUpdate/stock/newStockRecord.do";
    }

    //批量出入库
    public static String getGoodsStockBatch() {
        return ONLINE_URL + "shopUpdate/stock/addBatchStockRecord.do";
    }

    //供货商分类新增
    public static String getSupplierCateAdd() {
        return ONLINE_URL + "shopUpdate/shopSupplier/addSupKind.do";
    }

    //供货商分类修改或删除
    public static String getSupplierCateEdit() {
        return ONLINE_URL + "shopUpdate/shopSupplier/modifySupKind.do";
    }

    //供货商分类排序
    public static String getSupplierCateSort() {
        return ONLINE_URL + "shopUpdate/shopSupplier/updateSupKindSort.do";
    }

    //供货商分类列表
    public static String getSupplierCateList() {
        return ONLINE_URL + "shopUpdate/shopSupplier/querySupKindList.do";
    }

    //供货商新增
    public static String getSupplierAdd() {
//        return ONLINE_URL + "shopUpdate/shopSupplier/addsupInfo.do";
        return ONLINE_URL + "shopmanager/pc/addSupplier.do";
    }

    //供货商修改
    public static String getSupplierEdit() {
        return ONLINE_URL + "shopUpdate/shopSupplier/updateSupInfo.do";
    }

    //供货商删除
    public static String getSupplierDel() {
        return ONLINE_URL + "shopUpdate/shopSupplier/deleteSupInfo.do";
    }

    //供货商列表
    public static String getSupplierList() {
//        return ONLINE_URL + "shopUpdate/shopSupplier/querySupList.do";
        return ONLINE_URL + "harricane/html/supplier/querySupplierList.do";
    }

    //供货商列表web
    public static String getSupplierListWeb() {
        return ONLINE_URL + "shop/html/goods/getGoodsSupplierMsg.do";
    }

    //供货商详情
    public static String getSupplierInfo() {
        return ONLINE_URL + "shopUpdate/shopSupplier/querySupInfo.do";
    }

    //供货商申请列表
    public static String getSupplierApplyList() {
        return ONLINE_URL + "shopUpdate/shopSupplier/querySupExamineList.do";
    }

    //供货商详情-业务信息
    public static String getSupplierInfos() {
        return ONLINE_URL + "shopUpdate/shopSupplier/querySupBusinessInfo.do";
    }

    //供货商详情-业务信息-所供商品
    public static String getSupplierInfoGoods() {
        return ONLINE_URL + "shopUpdate/shopSupplier/querySupRecordGoodList.do";
    }

    //绑定供货商（确认通过）
    public static String getSupplierBind() {
        return ONLINE_URL + "shopUpdate/shopSupplier/bindSupplier.do";
    }

    //供货商详情-购销订单
    public static String getSupplierInfoGouXList() {
        return ONLINE_URL + "shopUpdate/shopSupplier/querySupBillInfo.do";
    }

    //供货商详情-付款记录
    public static String getSupplierInfoPayment() {
        return ONLINE_URL + "shopUpdate/shopSupplier/querySupPaymentInfo.do";
    }

    //供货商详情-付款记录-付款详情
    public static String getSupplierInfoPaymentInfo() {
        return ONLINE_URL + "shopUpdate/shopSupplier/queryQepaymentInfo.do";
    }

    //供货商详情-未还款购销单列表
    public static String getSupplierInfoUnpaidGouXList() {
        return ONLINE_URL + "shopUpdate/shopSupplier/queryUnpaidBillList.do";
    }

    //供货商详情-还款
    public static String getSupplierInfoRepayment() {
        return ONLINE_URL + "shopUpdate/shopSupplier/repaymentBills.do";
    }

    //入库-购销单列表
    public static String getPurchaseList() {
        return ONLINE_URL + "shopUpdate/supBill/querySupBillList.do";
    }

    //购销单详情
    public static String getGouXOrderInfo() {
        return ONLINE_URL + "shopUpdate/supBill/querySupBillGoodsList.do";
    }

    //入库单个商品（核对）
    public static String getGouXGoodsCheck() {
        return ONLINE_URL + "shopUpdate/supBill/checkGoods.do";
    }

    //入库全部商品(核对全部、确认商品入库)
    public static String getGouXGoodsCheckAll() {
        return ONLINE_URL + "shopUpdate/supBill/storageAllGoods.do";
    }

    //撤销核对入库
    public static String getGouXGoodsCheckCancel() {
        return ONLINE_URL + "shopUpdate/supBill/cancelCheckGoods.do";
    }

    //添加单据凭证
    public static String getGouXPaymentAdd() {
        return ONLINE_URL + "shopUpdate/supBill/addPaymentOrder.do";
    }

    //查看单据凭证
    public static String getGouXPayment() {
        return ONLINE_URL + "shopUpdate/supBill/queryPayment.do";
    }

    /********商品模块end********/

    /********商城start********/
    //首页活动列表
    public static String getMallMarketing() {
        return MALL_ONLINE_URL + "purchase-app/shopping/getMarketing.do";
    }

    //商品分类
    public static String getMallCateList() {
        return MALL_ONLINE_URL + "shop/shopping/getGoodsKindList.do";
    }

    //供货商城标签列表
    public static String getMallLabelList() {
        return MALL_ONLINE_URL + "purchase-app/shopping/queryLabelList.do";
    }

    //商品列表
    public static String getMallGoodList() {
        return MALL_ONLINE_URL + "purchase-app/shopping/v2/getGoodList.do";
    }

    //商品详情
    public static String getMallGoodsInfo() {
        return MALL_ONLINE_URL + "purchase-app/shopping/v2/getGoodDetail.do";
    }

    //立即购买
    public static String getMallBuy() {
        return MALL_ONLINE_URL + "purchase-app/shopping/toBuyNow.do";
    }

    //购物车列表
    public static String getMallCartList() {
        return MALL_ONLINE_URL + "purchase-app/shopping/getCartListNew.do";
    }

    //购物车数量加减
    public static String getMallCartUpdate() {
        return MALL_ONLINE_URL + "purchase-app/shopping/updateShoppingCart.do";
    }

    //删除购物车
    public static String getMallCartDel() {
        return MALL_ONLINE_URL + "/purchase-app/shopping/deleteShoppingCartMore.do";
    }

    //购物车提交订单
    public static String getMallCartSettlement() {
        return MALL_ONLINE_URL + "purchase-app/shopping/getSettlementPageNew.do";
    }

    //查询补货计划列表
    public static String getRestockPlanList() {
        return ONLINE_URL + "shopUpdate/restockPlan/queryRestockPlanList.do";
    }

    //添加补货计划
    public static String getRestockAdd() {
        return ONLINE_URL + "shopUpdate/restockPlan/addRestockPlan.do";
    }

    //修改补货计划状态信息
    public static String getRestockUpdate() {
        return ONLINE_URL + "shopUpdate/restockPlan/updatePlanStatus.do";
    }

    //删除补货计划
    public static String getRestockDel() {
        return ONLINE_URL + "shopUpdate/restockPlan/deleteRestockPlan.do";
    }

    //再次补货
    public static String getRestockAgain() {
        return ONLINE_URL + "shopUpdate/restockPlan/restockAgain.do";
    }

    //补货计划详情
    public static String getRestockInfo() {
        return ONLINE_URL + "shopUpdate/restockPlan/queryGoodsListByPlanId.do";
    }

    //补货计划-添加商品
    public static String getRestockGoodsAdd() {
        return ONLINE_URL + "shopUpdate/restockPlan/addRestockPlanGoods.do";
    }

    //补货计划-商品编辑
    public static String getRestockGoodsEdit() {
        return ONLINE_URL + "shopUpdate/restockPlan/modifyRestockPlanGoods.do";
    }

    //补货计划-预览
    public static String getRestockPreview() {
        return ONLINE_URL + "shopUpdate/restockPlan/getPresentListByPlanId.do";
    }

    //添加供应商备注
    public static String getRestockRemarks() {
        return ONLINE_URL + "shopUpdate/restockPlan/updateRestockPlanSupplier.do";
    }

    //查询商品销量信息
    public static String getRestockSale() {
        return ONLINE_URL + "shopUpdate/restockPlan/queryGoodsDetail.do";
    }

    //更换商品对应的供货商信息
    public static String getRestockSupplierEdit() {
        return ONLINE_URL + "shopUpdate/restockPlan/updateSupplier.do";
    }

    /********商城end********/

    /********查询start********/

    //查询-订单列表
    public static String getQueryOrderList() {
        return ONLINE_URL + "shopmanager/pc/queryTodayLists.do";
    }

    //查询-订单详情
    public static String getQueryOrderInfo() {
        return ONLINE_URL + "shopmanager/app/shop/appQuerySaleListDetail.do";
    }

    //新增退款
    public static String getRefundRecordAdd() {
        return ONLINE_URL + "shopmanager/ret/addNewRetRecord.do";
    }

    //查退款
    public static String getQueryRefund() {
        return ONLINE_URL + "goBuy/statistics/returnInfo.do";
    }

    //商品销售统计查询
    public static String getGoodsSaleStatistics() {
        return ONLINE_URL + "shopmanager/pc/queryGoodsSaleStatistics.do";
    }

    //商品销售统计详情
    public static String getGoodsSaleInfo() {
        return ONLINE_URL + "shopmanager/pc/queryGoodsSaleDetail.do";
    }

    //获取员工列表
    public static String getStaffList() {
        return ONLINE_URL + "shopUpdate/shopsStaff/shopsStaffsSearchByShopUnique.do";
    }

    //交接班记录
    public static String getShiftRecord() {
        return ONLINE_URL + "shopUpdate/shopsStaff/queryHandoverRecord.do";
//        return ONLINE_URL+"shopmanager/pc/pcStaffSignOutForBoss.do";
    }

    //员工交接班周期内商品销量统计
    public static String getShiftRecordOrder() {
        return ONLINE_URL + "shopmanager/pc/goodsSaleMsgForStaff.do";
    }

    //商品出入库记录查询
    public static String getStockRecord() {
        return ONLINE_URL + "shopUpdate/stock/queryShopStockRecord.do";
    }
    /********查询end********/

    /********统计start********/

    //收银界面新版-横轴界面(啥呀)
    public static String getStatisticsMain() {
        return ONLINE_URL + "shopmanager/pc/mainMessageForPC.do";
    }

    //销售额走势
    public static String getStatisticsSales() {
        return ONLINE_URL + "shopmanager/pc/salesTrend.do";
    }

    //销售数据周期占比
    public static String getStatisticsSalesCycle() {
        return ONLINE_URL + "shopmanager/pc/saleStatisticsCycleRatio.do";
    }

    //销售品类占比
    public static String getStatisticsClass() {
        return ONLINE_URL + "shopmanager/pc/salesCategoryRatio.do";
    }

    //支付分类销量占比
    public static String getStatisticsPayType() {
        return ONLINE_URL + "shopmanager/pc/paymentTypeRatio.do";
    }

    //营业额24H分布
    public static String getStatisticsSalesByHour() {
        return ONLINE_URL + "shopmanager/pc/querySaleTotalByHour.do";
    }

    //热销、累计利润商品TOP5
    public static String getTopGoods() {
        return ONLINE_URL + "shopmanager/pc/topGoods.do";
    }

    //滞销商品TOP5
    public static String getUnSalableTopGoods() {
        return ONLINE_URL + "shopmanager/pc/queryUnmarketableTop5.do";
    }

    //统计-员工统计
    public static String getStatisticsStaff() {
        return ONLINE_URL + "goBuy/statistics/staff.do";
    }

    //统计-订单金额分布
    public static String getStatisticsSaleList() {
        return ONLINE_URL + "goBuy/statistics/saleList.do";
    }

    //过期预警/需补给商品/商品总分类/今日退款金额
    //#26
    public static String getStatisticsPageInfo() {
        return ONLINE_URL + "goBuy/statistics/pageInfo.do";
    }

    /********统计end********/

    /********网单start********/

    //订单数量查询
    public static String getOrderCount() {
//        return ONLINE_URL + "shopUpdate/saleList/shopsSaleListCount.do";
        return ONLINE_URL + "shopmanager/pc/selectOnlineOrderCountByStatus.do";
    }

    //网单订单列表
    public static String getOrderList() {
//        return ONLINE_URL + "shopUpdate/saleList/querySaleList.do";
        return ONLINE_URL + "shopmanager/pc/pcQuerySaleList.do";
    }

    //销售订单详情
    public static String getOrderInfo() {
        return ONLINE_URL + "shopUpdate/saleList/querySaleListDetail.do";
    }

    //获取商家自配送骑手列表
    public static String getRiderList() {
        return ONLINE_URL + "shopUpdate/cash/quertShopCourierList.do";
    }

    // 添加新的骑手信息或修改现有骑手信息
    public static String getRiderEdit() {
        return ONLINE_URL + "shopUpdate/cash/addShopCourier.do";
    }

    //配送订单创建，根据订单不同的配送方式，创建不同的配送单
    //配送方式为自配送时，需先选择自配送骑手
    public static String getOrderCreate() {
        return ONLINE_URL + "shop/peisong/createOrder.do";
//        return ONLINE_URL + "shopUpdate/cash/createOrder.do";
    }

    //确认自提和确认收货接口,完成
    public static String getOrderConfirm() {
        return ONLINE_URL + "goBuy/my/confirmReceipt.do";
    }

    //取消配送
    public static String getOrderDeliveryCancel() {
        return "http://delivery.buyhoo.cc/outside/cancelDelivery";
    }

    //取消订单
    public static String getOrderCancel() {
        return ONLINE_URL + "goBuy/my/cancelSaleList.do";
    }

    //退款订单列表
    public static String getRefundList() {
        return ONLINE_URL + "shopUpdate/saleList/queryRetLists.do";
    }

    //退款订单详情
    public static String getRefundInfo() {
        return ONLINE_URL + "shopUpdate/saleList/queryReturnDetail.do";
    }

    //审核通过或拒绝退款
    public static String getRefundUpdate() {
        return ONLINE_URL + "shopUpdate/saleList/modifyReturnMsg.do";
    }

    //拒绝退款原因-查询常用话术
    public static String getRefundRefuseMsg() {
        return ONLINE_URL + "shopUpdate/saleList/queryReturnListMsg.do";
    }

    //修改话术内容
    public static String getRefundRefuseUpdate() {
        return ONLINE_URL + "shopUpdate/saleList/modifyReturnListMsg.do";
    }

    //添加话术
    public static String getRefundRefuseAdd() {
        return ONLINE_URL + "shopUpdate/saleList/addNewReturnListMsg.do";
    }

    //查询店铺配送设置
    public static String getShopDelivery() {
        return ONLINE_URL + "shopUpdate/cash/queryShopDelivery.do";
    }

    //更新配送费设置
    public static String getShopDeliveryEdit() {
        return ONLINE_URL + "shopUpdate/cash/updateShopDelivery.do";
    }

    //百货豆首页
    public static String getBeansList() {
        return ONLINE_URL + "shopUpdate/bean/queryBeanList.do";
    }

    /********网单end********/

    /*交班start*/

    //交班查询
    public static String getShift() {
        return ONLINE_URL + "shopmanager/pc/pcStaffSignOut.do";
    }

    /*交班end*/

    /********设置start********/
    //店铺信息编辑
    public static String getShopInfoEdit() {
        return ONLINE_URL + "shopUpdate/shopsStaff/updateShopsMessage.do";
    }

    /********设置end********/

    //店铺商品促销列表
    public static String getPromotionActivityList() {
        return ONLINE_URL + "shopUpdate/goods/queryPromotionActivityList.do";
    }
}
