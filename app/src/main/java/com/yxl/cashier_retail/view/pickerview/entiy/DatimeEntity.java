/*
 * Copyright (c) 2016-present 贵州纳雍穿青人李裕江<<EMAIL>>
 *
 * The software is licensed under the Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 *     http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR
 * PURPOSE.
 * See the Mulan PSL v2 for more details.
 */

package com.yxl.cashier_retail.view.pickerview.entiy;

import androidx.annotation.NonNull;

import java.io.Serializable;
import java.util.Calendar;

/**
 * 日期时间数据实体。
 * <pre>
 *     时间单位换算：
 *         // 1分 = 60秒
 *         // 1刻 = 15分
 *         // 1时 = 60分
 *         // 1日 = 12辰
 *         // 1日 = 24时
 *         // 1周 = 7日
 *         // 1月 = 30日
 *         // 1季 = 3月
 *         // 1年 = 12月
 *         // 1世 = 30年
 *         // 1运 = 12世 = 360年
 *         // 1会 = 30运 = 10800年
 *         // 1元 = 12会 = 129600年
 * </pre>
 *
 * <AUTHOR>
 * @since 2019/5/14 17:30
 */
@SuppressWarnings({"unused"})
public class DatimeEntity implements Serializable {
    private int year;
    private int month;
    private int day;
    private int hour;
    private int minute;
    private int second;

    public static DatimeEntity now() {
        Calendar calendar = Calendar.getInstance();
        DatimeEntity entity = new DatimeEntity();
        entity.setYear(calendar.get(Calendar.YEAR));
        entity.setMonth(calendar.get(Calendar.MONTH)+1);
        entity.setDay(calendar.get(Calendar.DAY_OF_MONTH));
        entity.setHour(calendar.get(Calendar.HOUR_OF_DAY));
        entity.setMinute(calendar.get(Calendar.MINUTE));
        entity.setSecond(calendar.get(Calendar.SECOND));
        return entity;
    }

    public static DatimeEntity yearOnFuture(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, year);
        DatimeEntity entity = new DatimeEntity();
        entity.setYear(calendar.get(Calendar.YEAR));
        entity.setMonth(calendar.get(Calendar.MONTH)+1);
        entity.setDay(calendar.get(Calendar.DAY_OF_MONTH));
        entity.setHour(calendar.get(Calendar.HOUR_OF_DAY));
        entity.setMinute(calendar.get(Calendar.MINUTE));
        entity.setSecond(calendar.get(Calendar.SECOND));
        return entity;
    }

    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }

    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    public int getDay() {
        return day;
    }

    public void setDay(int day) {
        this.day = day;
    }

    public int getHour() {
        return hour;
    }

    public void setHour(int hour) {
        this.hour = hour;
    }

    public int getMinute() {
        return minute;
    }

    public void setMinute(int minute) {
        this.minute = minute;
    }

    public int getSecond() {
        return second;
    }

    public void setSecond(int second) {
        this.second = second;
    }

    public long toTimeInMillis() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1);
        calendar.set(Calendar.DAY_OF_MONTH, day);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, second);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    @NonNull
    @Override
    public String toString() {
        return year + "-" + month + "-" + day+" "+hour + ":" + minute + ":" + second;
    }
}
