package com.yxl.cashier_retail.view;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.SeekBar;

import com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer;

public class MyVideoPlayer extends StandardGSYVideoPlayer {
    private MyVideoAllCallBack myVideoAllCallBack;

    public MyVideoPlayer(Context context, Boolean fullFlag) {
        super(context, fullFlag);
    }

    public MyVideoPlayer(Context context) {
        super(context);
    }

    public MyVideoPlayer(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public void onStartTrackingTouch(SeekBar seekBar) {
        super.onStartTrackingTouch(seekBar);
    }

    @Override
    protected void showProgressDialog(float deltaX, String seekTime, long seekTimePosition, String totalTime, long totalTimeDuration) {
        super.showProgressDialog(deltaX, seekTime, seekTimePosition, totalTime, totalTimeDuration);
        dismissProgressDialog();
        if (null != myVideoAllCallBack) {
            myVideoAllCallBack.onStartSpan();
        }
    }

    public void setMyVideoAllCallBack(MyVideoAllCallBack myVideoAllCallBack) {
        this.myVideoAllCallBack = myVideoAllCallBack;
    }

    public interface MyVideoAllCallBack {
        // 拖动
        void onStartSpan();
    }
}
