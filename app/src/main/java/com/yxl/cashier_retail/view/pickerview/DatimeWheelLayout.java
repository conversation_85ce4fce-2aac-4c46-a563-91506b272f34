/*
 * Copyright (c) 2016-present 贵州纳雍穿青人李裕江<<EMAIL>>
 *
 * The software is licensed under the Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 *     http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR
 * PURPOSE.
 * See the Mulan PSL v2 for more details.
 */

package com.yxl.cashier_retail.view.pickerview;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.github.gzuliyujiang.wheelpicker.widget.BaseWheelLayout;
import com.github.gzuliyujiang.wheelview.annotation.ScrollState;
import com.github.gzuliyujiang.wheelview.widget.NumberWheelView;
import com.github.gzuliyujiang.wheelview.widget.WheelView;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.view.pickerview.Interface.OnDatimeSelectedListener;
import com.yxl.cashier_retail.view.pickerview.entiy.DatimeEntity;

import java.util.Arrays;
import java.util.List;


/**
 * 日期时间滚轮控件
 *
 * <AUTHOR>
 * @since 2019/5/14 15:26
 */
@SuppressWarnings("unused")
@SuppressLint("NonConstantResourceId")
public class DatimeWheelLayout extends BaseWheelLayout {
    private NumberWheelView yearWheelView, monthWheelView, dayWheelView, hoursWheelView, minuteWheelView;
    private TextView yearLabelView, monthLabelView, dayLabelView, hoursLabelView, minuteLabelView;
    private DatimeEntity startValue,endValue;
    private Integer selectedYear, selectedMonth, selectedDay, selectedHours, selectedMinute;

    private OnDatimeSelectedListener onDatimeSelectedListener;
    private boolean resetWhenLinkage = true;

    public DatimeWheelLayout(Context context) {
        super(context);
    }

    public DatimeWheelLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public DatimeWheelLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public DatimeWheelLayout(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    @Override
    protected int provideLayoutRes() {
        return R.layout.wheel_picker_datime;
    }

    @Override
    protected List<WheelView> provideWheelViews() {
        return Arrays.asList(yearWheelView, monthWheelView, dayWheelView, hoursWheelView, minuteWheelView);
    }

    @Override
    protected void onInit(@NonNull Context context) {
        yearWheelView = findViewById(R.id.nwvYear);
        yearLabelView = findViewById(R.id.tvLabelYear);
        monthWheelView = findViewById(R.id.nwvMonth);
        monthLabelView = findViewById(R.id.tvLabelMonth);
        dayWheelView = findViewById(R.id.nwvDay);
        dayLabelView = findViewById(R.id.tvLabelDay);
        hoursWheelView = findViewById(R.id.nwvHours);
        hoursLabelView = findViewById(R.id.tvLabelHours);
        minuteWheelView = findViewById(R.id.nwvMinute);
        minuteLabelView = findViewById(R.id.tvLabelMinute);
    }

    @Override
    protected void onAttributeSet(@NonNull Context context, @Nullable AttributeSet attrs) {
//        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.DateWheelLayout);
//        setDateMode(typedArray.getInt(R.styleable.DateWheelLayout_wheel_dateMode, DateMode.YEAR_MONTH_DAY));
//        String yearLabel = typedArray.getString(R.styleable.DateWheelLayout_wheel_yearLabel);
//        String monthLabel = typedArray.getString(R.styleable.DateWheelLayout_wheel_monthLabel);
//        String dayLabel = typedArray.getString(R.styleable.DateWheelLayout_wheel_dayLabel);
//        typedArray.recycle();
//        setDateLabel(yearLabel, monthLabel, dayLabel);
//        setDateFormatter(new SimpleDateFormatter());
    }

    @Override
    protected void onVisibilityChanged(@NonNull View changedView, int visibility) {
        super.onVisibilityChanged(changedView, visibility);
        if (visibility == VISIBLE && startValue == null && endValue == null) {
            setRange(DatimeEntity.now(), DatimeEntity.yearOnFuture(30), DatimeEntity.now());
        }
    }

    @Override
    public void onWheelSelected(WheelView view, int position) {
        switch (view.getId()) {
            case R.id.nwvYear:
                //年
                selectedYear = yearWheelView.getItem(position);
                if (resetWhenLinkage) {
                    selectedMonth = null;
                    selectedDay = null;
                    selectedHours = null;
                    selectedMinute = null;
                }
                changeMonth(selectedYear);
                dateSelectedCallback();
                break;
            case R.id.nwvMonth:
                //月
                selectedMonth = monthWheelView.getItem(position);
                if (resetWhenLinkage) {
                    selectedDay = null;
                    selectedHours = null;
                    selectedMinute = null;
                }
                changeDay(selectedYear, selectedMonth);
                dateSelectedCallback();
                break;
            case R.id.nwvDay:
                //日
                selectedDay = dayWheelView.getItem(position);
                if (resetWhenLinkage) {
                    selectedHours = null;
                    selectedMinute = null;
                }
                changeHours(selectedYear, selectedMonth, selectedDay);
                dateSelectedCallback();
                break;
            case R.id.nwvHours:
                //时
                selectedHours = hoursWheelView.getItem(position);
                if (resetWhenLinkage) {
                    selectedMinute = null;
                }
                changeMinute(selectedYear, selectedMonth, selectedDay, selectedHours);
                dateSelectedCallback();
                break;
            case R.id.nwvMinute:
                //分
                selectedMinute = minuteWheelView.getItem(position);
                dateSelectedCallback();
                break;
        }
    }

    @Override
    public void onWheelScrollStateChanged(WheelView view, @ScrollState int state) {
        switch (view.getId()) {
            case R.id.nwvYear:
                //年
                monthWheelView.setEnabled(state == ScrollState.IDLE);
                dayWheelView.setEnabled(state == ScrollState.IDLE);
                hoursWheelView.setEnabled(state == ScrollState.IDLE);
                minuteWheelView.setEnabled(state == ScrollState.IDLE);
                break;
            case R.id.nwvMonth:
                //月
                yearWheelView.setEnabled(state == ScrollState.IDLE);
                dayWheelView.setEnabled(state == ScrollState.IDLE);
                hoursWheelView.setEnabled(state == ScrollState.IDLE);
                minuteWheelView.setEnabled(state == ScrollState.IDLE);
                break;
            case R.id.nwvDay:
                //日
                yearWheelView.setEnabled(state == ScrollState.IDLE);
                monthWheelView.setEnabled(state == ScrollState.IDLE);
                hoursWheelView.setEnabled(state == ScrollState.IDLE);
                minuteWheelView.setEnabled(state == ScrollState.IDLE);
                break;
            case R.id.nwvHours:
                //时
                yearWheelView.setEnabled(state == ScrollState.IDLE);
                monthWheelView.setEnabled(state == ScrollState.IDLE);
                dayWheelView.setEnabled(state == ScrollState.IDLE);
                minuteWheelView.setEnabled(state == ScrollState.IDLE);
                break;
            case R.id.nwvMinute:
                //分
                yearWheelView.setEnabled(state == ScrollState.IDLE);
                monthWheelView.setEnabled(state == ScrollState.IDLE);
                dayWheelView.setEnabled(state == ScrollState.IDLE);
                hoursWheelView.setEnabled(state == ScrollState.IDLE);
                break;

        }
    }

    public void setOnDatimeSelectedListener(OnDatimeSelectedListener onDatimeSelectedListener) {
        this.onDatimeSelectedListener = onDatimeSelectedListener;
    }

    public void setResetWhenLinkage(boolean resetWhenLinkage) {
        this.resetWhenLinkage = resetWhenLinkage;
    }

    private void dateSelectedCallback() {
        if (onDatimeSelectedListener == null) {
            return;
        }
        minuteWheelView.post(new Runnable() {
            @Override
            public void run() {
                onDatimeSelectedListener.onDatimeSelected(selectedYear,selectedMonth,selectedDay,selectedHours,selectedMinute,0);
            }
        });
    }

    private void changeYear() {
        final int min = Math.min(startValue.getYear(), endValue.getYear());
        final int max = Math.max(startValue.getYear(), endValue.getYear());
        if (selectedYear == null) {
            selectedYear = min;
        } else {
            selectedYear = Math.max(selectedYear, min);
            selectedYear = Math.min(selectedYear, max);
        }
        yearWheelView.setRange(min, max, 1);
        yearWheelView.setDefaultValue(selectedYear);
        changeMonth(selectedYear);
    }

    private void changeMonth(int year) {
        final int min, max;
        //开始年份和结束年份相同（即只有一个年份，这种情况建议使用月日模式）
        if (startValue.getYear() == endValue.getYear()) {
            min = Math.min(startValue.getMonth(), endValue.getMonth());
            max = Math.max(startValue.getMonth(), endValue.getMonth());
        }
        //当前所选年份和开始年份相同
        else if (year == startValue.getYear()) {
            min = startValue.getMonth();
            max = 12;
        }
        //当前所选年份和结束年份相同
        else if (year == endValue.getYear()) {
            min = 1;
            max = endValue.getMonth();
        }
        //当前所选年份在开始年份和结束年份之间
        else {
            min = 1;
            max = 12;
        }
        if (selectedMonth == null) {
            selectedMonth = min;
        } else {
            selectedMonth = Math.max(selectedMonth, min);
            selectedMonth = Math.min(selectedMonth, max);
        }
        monthWheelView.setRange(min, max, 1);
        monthWheelView.setDefaultValue(selectedMonth);
        changeDay(year, selectedMonth);
    }

    private void changeDay(int year, int month) {
        final int min, max;
        //开始年月及结束年月相同情况
        if (year == startValue.getYear() && month == startValue.getMonth()
                && year == endValue.getYear() && month == endValue.getMonth()) {
            min = startValue.getDay();
            max = endValue.getDay();
        }
        //开始年月相同情况
        else if (year == startValue.getYear() && month == startValue.getMonth()) {
            min = startValue.getDay();
            max = getTotalDaysInMonth(year, month);
        }
        //结束年月相同情况
        else if (year == endValue.getYear() && month == endValue.getMonth()) {
            min = 1;
            max = endValue.getDay();
        } else {
            min = 1;
            max = getTotalDaysInMonth(year, month);
        }
        if (selectedDay == null) {
            selectedDay = min;
        } else {
            selectedDay = Math.max(selectedDay, min);
            selectedDay = Math.min(selectedDay, max);
        }
        dayWheelView.setRange(min, max, 1);
        dayWheelView.setDefaultValue(selectedDay);
        changeHours(year,month,selectedDay);
    }

    private void changeHours(int year,int month,int day){
        final  int min,max;
        //开始年月日及结束年月日相同情况
        if(year==startValue.getYear()&& month==startValue.getMonth() && day==startValue.getDay()
        && year==endValue.getYear() && month==endValue.getMonth()&& day==endValue.getDay()){
            min = startValue.getHour();
            max = endValue.getHour();
        }
        //开始年月日相同情况
        else if (year == startValue.getYear() && month == startValue.getMonth() && day==startValue.getDay()) {
            min = startValue.getHour();
            max = 23;
        }
        //结束年月日相同情况
        else if (year == endValue.getYear() && month == endValue.getMonth() && day==endValue.getDay()) {
            min = 0;
            max = endValue.getHour();
        } else {
            min = 0;
            max = 23;
        }
        if (selectedHours == null) {
            selectedHours = min;
        } else {
            selectedHours = Math.max(selectedHours, min);
            selectedHours = Math.min(selectedHours, max);
        }
        hoursWheelView.setRange(min, max, 1);
        hoursWheelView.setDefaultValue(selectedHours);
        changeMinute(year,month,day,selectedHours);
    }
    private void changeMinute(int year,int month,int day,int hours){
        final  int min,max;
        //开始年月日时及结束年月日时相同情况
        if(year==startValue.getYear()&& month==startValue.getMonth() && day==startValue.getDay()&& hours==startValue.getHour()
                && year==endValue.getYear() && month==endValue.getMonth()&& day==endValue.getDay()&& hours==startValue.getHour()){
            min = startValue.getMinute();
            max = endValue.getMinute();
        }
        //开始年月日时相同情况
        else if (year == startValue.getYear() && month == startValue.getMonth() && day==startValue.getDay() && hours==startValue.getHour()) {
            min = startValue.getMinute();
            max = 59;
        }
        //结束年月日时相同情况
        else if (year == endValue.getYear() && month == endValue.getMonth() && day==endValue.getDay() && hours==endValue.getHour()) {
            min = 0;
            max = endValue.getMinute();
        } else {
            min = 0;
            max = 59;
        }
        if (selectedMinute == null) {
            selectedMinute = min;
        } else {
            selectedMinute = Math.max(selectedMinute, min);
            selectedMinute = Math.min(selectedMinute, max);
        }
        minuteWheelView.setRange(min, max, 1);
        minuteWheelView.setDefaultValue(selectedMinute);
    }

    /**
     * 根据年份及月份获取每月的天数，类似于{@link java.util.Calendar#getActualMaximum(int)}
     */
    private int getTotalDaysInMonth(int year, int month) {
        switch (month) {
            case 1:
            case 3:
            case 5:
            case 7:
            case 8:
            case 10:
            case 12:
                // 大月月份为31天
                return 31;
            case 4:
            case 6:
            case 9:
            case 11:
                // 小月月份为30天
                return 30;
            case 2:
                // 二月需要判断是否闰年
                if (year <= 0) {
                    return 29;
                }
                // 是否闰年：能被4整除但不能被100整除；能被400整除；
                boolean isLeap = (year % 4 == 0 && year % 100 != 0) || year % 400 == 0;
                if (isLeap) {
                    return 29;
                } else {
                    return 28;
                }
            default:
                return 30;
        }
    }

    /**
     * 设置日期时间范围
     */
    public void setRange(DatimeEntity startValue, DatimeEntity endValue, DatimeEntity defaultValue) {
        if (startValue == null) {
            startValue = DatimeEntity.now();
        }
        if (endValue == null) {
            endValue = DatimeEntity.yearOnFuture(30);
        }
        if (endValue.toTimeInMillis() < startValue.toTimeInMillis()) {
            return;
//            throw new IllegalArgumentException("Ensure the start date is less than the end date");
        }
        this.startValue = startValue;
        this.endValue = endValue;
        if (defaultValue != null) {
            selectedYear = defaultValue.getYear();
            selectedMonth = defaultValue.getMonth();
            selectedDay = defaultValue.getDay();
            selectedHours = defaultValue.getHour();
            selectedMinute = defaultValue.getMinute();
        } else {
            selectedYear = null;
            selectedMonth = null;
            selectedDay = null;
            selectedHours = null;
            selectedMinute = null;
        }
        changeYear();
    }
    public void setDefaultValue(DatimeEntity defaultValue) {
        setRange(startValue, endValue, defaultValue);
    }

    public DatimeEntity getStartValue() {
        return startValue;
    }

    public DatimeEntity getEndValue() {
        return endValue;
    }

}
