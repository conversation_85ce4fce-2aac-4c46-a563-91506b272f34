package com.yxl.cashier_retail.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Handler;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.yxl.cashier_retail.R;

/**
 * Describe:数字键盘
 * Created by jingang on 2023/5/27
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n"})
public class NumberKeyBoardView extends LinearLayout {
    private String resultStr = "";
    private final Context mContext;
    private boolean isDrop = true;//是否可输入小数点
    private int maxLength = 9;//最大输入长度限制
    private OnMValueChangedListener onMValueChangedListener;

    //默认金额
    private boolean isDefaultMoney;//实现否显示默认金额 默认false
    private LinearLayout linMoney;

    //确认按钮颜色
    private int confirmType;//确认按钮颜色 0.绿色 1.红色
    private Button btnConfirm;

    public void setOnMValueChangedListener(OnMValueChangedListener onMValueChangedListener) {
        this.onMValueChangedListener = onMValueChangedListener;
    }

    public void setResultStr(String resultStr) {
        this.resultStr = resultStr;
        if (onMValueChangedListener != null) {
            onMValueChangedListener.onChange(resultStr);
        }
    }

    public void setDrop(boolean drop) {
        isDrop = drop;
    }

    public void setMaxLength(int maxLength) {
        this.maxLength = maxLength;
    }

    public void setDefaultMoney(boolean defaultMoney) {
        isDefaultMoney = defaultMoney;
        if (linMoney != null) {
            linMoney.setVisibility(isDefaultMoney ? VISIBLE : GONE);
        }
    }

    public void setConfirmType(int confirmType) {
        this.confirmType = confirmType;
        if (btnConfirm == null) {
            return;
        }
        switch (confirmType) {
            case 1:
                btnConfirm.setBackgroundResource(R.drawable.shape_red_5);
                btnConfirm.setText(getResources().getString(R.string.refund));
                break;
            default:
                btnConfirm.setBackgroundResource(R.drawable.shape_green_5);
                btnConfirm.setText(getResources().getString(R.string.confirm));
                break;
        }
    }

    public interface OnMValueChangedListener {
        void onChange(String var);

        void onConfirm();
    }

    public NumberKeyBoardView(Context context) {
        this(context, null);
    }

    public NumberKeyBoardView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public NumberKeyBoardView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.mContext = context;
        init();
    }

    private void init() {
        LayoutInflater.from(mContext).inflate(R.layout.layout_nkb, this);
        new Handler().postDelayed(() -> {
            initListener();
        }, 10);
    }

    private void initListener() {
        class buttonsOnClickListener implements OnClickListener {
            @Override
            public void onClick(View v) {
                switch (v.getId()) {
                    case R.id.but1:
                        addNumberToResult("1");
                        break;
                    case R.id.but2:
                        addNumberToResult("2");
                        break;
                    case R.id.but3:
                        addNumberToResult("3");
                        break;
                    case R.id.but4:
                        addNumberToResult("4");
                        break;
                    case R.id.but5:
                        addNumberToResult("5");
                        break;
                    case R.id.but6:
                        addNumberToResult("6");
                        break;
                    case R.id.but7:
                        addNumberToResult("7");
                        break;
                    case R.id.but8:
                        addNumberToResult("8");
                        break;
                    case R.id.but9:
                        addNumberToResult("9");
                        break;
                    case R.id.but0:
                        addNumberToResult("0");
                        break;
                    case R.id.butDrop:
                        if (isDrop) {
                            addNumberToResult(".");
                        }
                        break;
                    case R.id.butClear:
                        //清空
                        clearNumber();
                        break;
                    case R.id.linMoney0:
                        //默认金额：100
                        clearNumber();
                        addNumberToResult("100");
                        break;
                    case R.id.linMoney1:
                        //默认金额：50
                        clearNumber();
                        addNumberToResult("50");
                        break;
                    case R.id.linMoney2:
                        //默认金额：10
                        clearNumber();
                        addNumberToResult("10");
                        break;
                    case R.id.butDel:
                        //删除
                        reduceNumberToResult();
                        break;
                    case R.id.butConfirm:
                        //确认
                        if (onMValueChangedListener != null) {
                            onMValueChangedListener.onConfirm();
                        }
                        break;
                }
            }
        }
        findViewById(R.id.but1).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.but2).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.but3).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.but4).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.but5).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.but6).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.but7).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.but8).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.but9).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.but0).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.butDrop).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.butClear).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.butDel).setOnClickListener(new buttonsOnClickListener());

        linMoney = findViewById(R.id.linMoney);
        linMoney.setVisibility(isDefaultMoney ? VISIBLE : GONE);
        findViewById(R.id.linMoney0).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.linMoney1).setOnClickListener(new buttonsOnClickListener());
        findViewById(R.id.linMoney2).setOnClickListener(new buttonsOnClickListener());
        btnConfirm = findViewById(R.id.butConfirm);
        switch (confirmType) {
            case 1:
                btnConfirm.setBackgroundResource(R.drawable.shape_red_5);
                btnConfirm.setText(getResources().getString(R.string.refund));
                break;
            default:
                btnConfirm.setBackgroundResource(R.drawable.shape_green_5);
                btnConfirm.setText(getResources().getString(R.string.confirm));
                break;
        }
        btnConfirm.setOnClickListener(new buttonsOnClickListener());
    }

    //清除数字
    public void clearNumber() {
        resultStr = "";
        if (onMValueChangedListener != null) {
            onMValueChangedListener.onChange(resultStr);
        }
    }

    //增加数字
    private void addNumberToResult(String numberStr) {
        if (!TextUtils.isEmpty(resultStr)) {
            if (resultStr.contains(".")) {
                if (".".equals(numberStr)) {
                    return;
                } else {
                    //限制小数点后面最多有两位小数
                    int strLength = resultStr.substring(resultStr.indexOf(".")).length();
                    if (strLength == 3) {
                        return;
                    }
                }
            }
        }

        //第一次输入的是点，显示0.
        if ("".equals(resultStr) && ".".equals(numberStr)) {
            resultStr = "0";
            //小程序中关系网录入姓名手机号，但收银机上人脸识别不显示addNumberToResult
        }

        //数字前多次输入0，只显示一个0
        if (resultStr != null && !"".equals(resultStr) && "0".equals(resultStr) && "0".equals(numberStr)) {
            return;
        }
        resultStr += numberStr;

        if (null != onMValueChangedListener) {
            if (resultStr.length() > maxLength) {
                resultStr = resultStr.substring(0, maxLength);
//                Toast.makeText(mContext, "金额不能大于9位", Toast.LENGTH_SHORT).show();
            }
            onMValueChangedListener.onChange(resultStr);
        }
    }

    //擦除数字
    private void reduceNumberToResult() {
        if (resultStr.length() > 0) {
            resultStr = resultStr.substring(0, resultStr.length() - 1);
        }
        if (onMValueChangedListener != null) {
            onMValueChangedListener.onChange(resultStr);
        }
    }

}
