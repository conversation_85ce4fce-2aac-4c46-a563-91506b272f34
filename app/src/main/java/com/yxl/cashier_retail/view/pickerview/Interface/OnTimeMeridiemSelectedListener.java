/*
 * Copyright (c) 2016-present 贵州纳雍穿青人李裕江<<EMAIL>>
 *
 * The software is licensed under the Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 *     http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR
 * PURPOSE.
 * See the Mulan PSL v2 for more details.
 */

package com.yxl.cashier_retail.view.pickerview.Interface;

/**
 * 时间选择接口
 *
 * <AUTHOR>
 * @since 2019/5/14 19:58
 */
public interface OnTimeMeridiemSelectedListener {

    /**
     * 时间选择回调
     *
     * @param hour           时
     * @param minute         分
     * @param second         秒
     * @param isAnteMeridiem 是否上午
     */
    void onTimeSelected(int hour, int minute, int second, boolean isAnteMeridiem);

}
