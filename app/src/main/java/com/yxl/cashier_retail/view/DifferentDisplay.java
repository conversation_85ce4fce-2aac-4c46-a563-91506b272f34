package com.yxl.cashier_retail.view;

import android.annotation.SuppressLint;
import android.app.Presentation;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.Display;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.lifecycle.LifecycleOwner;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.SPUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.youth.banner.Banner;
import com.youth.banner.adapter.BannerImageAdapter;
import com.youth.banner.holder.BannerImageHolder;
import com.youth.banner.listener.OnPageChangeListener;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ui.adapter.CartDifferentDisplayAdapter;
import com.yxl.cashier_retail.ui.adapter.MallGoodsInfoImgData;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.ui.bean.MemberData;
import com.yxl.cashier_retail.utils.DFUtils;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.utils.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Describe:副屏
 * Created by jingang on 2024/5/21
 */
@SuppressLint({"MissingInflatedId", "SetTextI18n"})
public class DifferentDisplay extends Presentation {
    private Context mContext;
    private Banner banner;

    private LinearLayout linEmpty;

    //购物车
    private LinearLayout linCart;
    private RecyclerView rvCart;
    private TextView tvDiscount, tvTotal;
    private CartDifferentDisplayAdapter cartAdapter;

    //支付状态
    private LinearLayout linStatus;
    private ImageView ivStatus;
    private TextView tvStatus, tvStatusTips;

    //会员
    private MemberData memberData;
    private RelativeLayout relMember;
    private ImageView ivMemberHead;
    private TextView tvMemberName, tvMemberMobile, tvMemberBalance, tvMemberPoints;

    //人脸
    private RelativeLayout relFace;
    private TextView tvFaceTips;
    private ImageView ivFaceImg;

    private final List<MallGoodsInfoImgData> imgList = new ArrayList();

    public DifferentDisplay(Context outerContext, Display display) {
        super(outerContext, display);
        this.mContext = outerContext;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_different_display);
        banner = findViewById(R.id.banner);
        linEmpty = findViewById(R.id.linEmpty);

        //购物车
        linCart = findViewById(R.id.linCart);
        rvCart = findViewById(R.id.rvCart);
        tvDiscount = findViewById(R.id.tvDiscount);
        tvTotal = findViewById(R.id.tvTotal);

        //支付状态
        linStatus = findViewById(R.id.linStatus);
        ivStatus = findViewById(R.id.ivStatus);
        tvStatus = findViewById(R.id.tvStatus);
        tvStatusTips = findViewById(R.id.tvStatusTips);

        //人脸
        relFace = findViewById(R.id.relFace);
        tvFaceTips = findViewById(R.id.tvFaceTips);
        ivFaceImg = findViewById(R.id.ivFaceImg);

        //会员
        relMember = findViewById(R.id.relMember);
        ivMemberHead = findViewById(R.id.ivMemberHead);
        tvMemberName = findViewById(R.id.tvMemberName);
        tvMemberMobile = findViewById(R.id.tvMemberMobile);
        tvMemberBalance = findViewById(R.id.tvMemberBalance);
        tvMemberPoints = findViewById(R.id.tvMemberPoints);

        setBanner();
        setAdapter();
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        cartAdapter = new CartDifferentDisplayAdapter(mContext);
        rvCart.setAdapter(cartAdapter);
    }

    /**
     * 设置banner
     */
    private void setBanner() {
        imgList.add(new MallGoodsInfoImgData("https://img2.baidu.com/it/u=2513024551,2896067572&fm=253&fmt=auto&app=138&f=JPEG?w=667&h=500", 0));
        imgList.add(new MallGoodsInfoImgData("https://img1.baidu.com/it/u=2310170655,486191485&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=281", 1));
        imgList.add(new MallGoodsInfoImgData("https://img0.baidu.com/it/u=129736533,12607833&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=500", 2));
        imgList.add(new MallGoodsInfoImgData("https://img0.baidu.com/it/u=4202467532,1047906849&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500", 3));
        imgList.add(new MallGoodsInfoImgData("https://img2.baidu.com/it/u=4203239028,119028466&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500", 4));
        imgList.add(new MallGoodsInfoImgData("https://img1.baidu.com/it/u=706201593,1465386589&fm=253&fmt=auto&app=138&f=JPEG?w=889&h=500", 5));
        banner.setAdapter(new BannerImageAdapter<MallGoodsInfoImgData>(imgList) {
                    @Override
                    public void onBindView(BannerImageHolder holder, MallGoodsInfoImgData model, int position, int size) {
                        Glide.with(BaseApplication.getInstance())
                                .load(model.getUrl())
                                .apply(new RequestOptions().error(com.yxl.commonlibrary.R.mipmap.ic_default_img))
                                .into(holder.imageView);
                    }
                })
                .isAutoLoop(true)
                .addBannerLifecycleObserver((LifecycleOwner) mContext)//添加生命周期观察者
                .addOnPageChangeListener(new OnPageChangeListener() {
                    @Override
                    public void onPageScrolled(int i, float v, int i1) {

                    }

                    @Override
                    public void onPageSelected(int i) {
                    }

                    @Override
                    public void onPageScrollStateChanged(int i) {

                    }
                });
    }

    /**
     * 设置购物车、会员信息、支付状态
     *
     * @param cartList   商品列表
     * @param discount   优惠
     * @param total      应付
     * @param memberData 会员西悉尼
     * @param status     收款状态：0.收款成功 1.收款失败 其他.没有收款状态
     * @param statusMsg  收款失败原因
     */
    public void setCart(List<GoodsData> cartList, double discount, double total, MemberData memberData, int status, String statusMsg) {
        Log.e("111111", "status = " + status + " msg = " + statusMsg);
        //是否启用副屏
        if (!TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_SECONDARY_SCREEN, ""))) {
            linEmpty.setVisibility(View.GONE);
            linCart.setVisibility(View.GONE);
            return;
        }
        switch (status) {
            case 0:
                linEmpty.setVisibility(View.GONE);
                linCart.setVisibility(View.VISIBLE);
                linStatus.setVisibility(View.VISIBLE);
                linStatus.setBackgroundResource(com.yxl.commonlibrary.R.color.green);
                ivStatus.setImageResource(R.mipmap.ic_right003);
                tvStatus.setText(getResources().getString(R.string.collection_success));
                tvStatusTips.setText(getResources().getString(R.string.paid_in_colon) + DFUtils.getNum2(total));
                new Handler().postDelayed(() -> {
                    linEmpty.setVisibility(View.VISIBLE);
                    linCart.setVisibility(View.GONE);
                }, 5000);
                break;
            case 1:
                linEmpty.setVisibility(View.GONE);
                linCart.setVisibility(View.VISIBLE);
                linStatus.setVisibility(View.VISIBLE);
                linStatus.setBackgroundResource(com.yxl.commonlibrary.R.color.green);
                ivStatus.setImageResource(R.mipmap.ic_right003);
                tvStatus.setText(getResources().getString(R.string.collection_success));
                tvStatusTips.setText(statusMsg);
                break;
            default:
                linStatus.setVisibility(View.GONE);
                if (cartList.isEmpty()) {
                    linEmpty.setVisibility(View.VISIBLE);
                    linCart.setVisibility(View.GONE);
                    return;
                }
                linEmpty.setVisibility(View.GONE);
                linCart.setVisibility(View.VISIBLE);
                cartAdapter.setMemberData(memberData);
                cartAdapter.setDataList(cartList);
                tvDiscount.setText("-" + DFUtils.getNum2(discount));
                tvTotal.setText(DFUtils.getNum2(total));
                break;
        }
        //会员
        if (memberData != null) {
            relMember.setVisibility(View.VISIBLE);
            tvMemberName.setText(memberData.getCusName());
            tvMemberMobile.setText(StringUtils.getStarMobile(memberData.getCusPhone()));
            tvMemberBalance.setText(getResources().getString(R.string.balance_colon) + DFUtils.getNum2(memberData.getTotalBalance()));
            tvMemberPoints.setText(getResources().getString(R.string.points_colon) + DFUtils.getNum2(memberData.getCusPoints()));
        } else {
            relMember.setVisibility(View.GONE);
        }
    }


    /**
     * @param bitmap
     * @param count
     */
    public void setFace(Bitmap bitmap, int count, boolean isfinsh) {
        if (relFace.getVisibility() == View.GONE) {
            relFace.setVisibility(View.VISIBLE);
        }
        if (bitmap != null) {
            ivFaceImg.setImageBitmap(bitmap);//需不需要镜像翻转？可能存在机型适配问题
        }
        if (count == 0) {
            tvFaceTips.setText(getResources().getString(R.string.payment_face_tips2));
        } else if (count > 1) {
            tvFaceTips.setText(getResources().getString(R.string.payment_face_tips1));
        } else if (count == 1 && !isfinsh) {
            tvFaceTips.setText(getResources().getString(R.string.payment_face_tips3));
        }
    }
}
