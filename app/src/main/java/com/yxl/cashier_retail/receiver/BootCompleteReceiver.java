package com.yxl.cashier_retail.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.ui.activity.LauncherActivity;

/**
 * Describe:创建广播，开机代码
 * Created by jingang on 2024/1/9
 */
public class BootCompleteReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        //This method is called when the BroadcastReceiver is receiving
        Log.e("BootCompleteReceiver", "接收到广播-开机启动");
        if (Intent.ACTION_BOOT_COMPLETED.equals(intent.getAction())) {
            // 在这里执行开机启动时需要执行的任务
            // 如启动服务、初始化数据等
            if (!TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_START_UP, ""))) {
                context.startActivity(new Intent(context, LauncherActivity.class)
                        .setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                );
            }
        }
    }
}