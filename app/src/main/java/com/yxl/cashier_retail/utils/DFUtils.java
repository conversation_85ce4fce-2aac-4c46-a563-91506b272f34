package com.yxl.cashier_retail.utils;

import java.text.DecimalFormat;

/**
 * Descride:小数点后面取两位小数并四舍五入
 * Created by jingang on 2021/2/27
 */
public class DFUtils {
    private static long n0 = 10000;//万
    private static long n1 = n0 * n0;//亿

    public static String getNum(double num) {
        DecimalFormat decimalFormat = new DecimalFormat("0");
//        decimalFormat.setRoundingMode(RoundingMode.DOWN);//舍入模式，其中的值被舍入为零。
        return decimalFormat.format(num);
    }

    //保留double类型小数后两位，不四舍五入，直接取小数后两位 比如：10.1269 返回：10.12
    public static String getNum2(double num) {
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
//        decimalFormat.setRoundingMode(RoundingMode.DOWN);//舍入模式，其中的值被舍入为零。
        return decimalFormat.format(num);
    }

    public static String getNum3(double num) {
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
//        decimalFormat.setRoundingMode(RoundingMode.DOWN);//舍入模式，其中的值被舍入为零。
        if (num > n1) {
            return decimalFormat.format(num / n1) + "亿";
        }
        if (num > n0) {
            return decimalFormat.format(num / n0) + "W";
        }
        return decimalFormat.format(num);
    }

    /**
     * 去掉多余的.与0
     *
     * @param num
     * @return
     */
    public static String getNum4(double num) {
        String s = getNum2(num);
        if (s.indexOf(".") > 0) {
            s = s.replaceAll("0+?$", "");//去掉多余的0
            s = s.replaceAll("[.]$", "");//如最后一位是.则去掉
        }
        return s;
    }

    /**
     * 抹零
     *
     * @param money
     * @param type  0.不抹零 1.抹零到1元 2.抹零到5角 3.抹零到1角
     *              0.抹零到1元 1.抹零到5角 2.抹零到1角
     * @return
     */
    public static double getZero(double money, int type) {
        if (type == 1) {
            return (int) Math.floor(money); // 抹零至最接近的整数元
        } else if (type == 2) {
            return Math.floor(money * 2) / 2; // 抹零至最近的0.5元
        } else if (type == 3) {
            return Math.floor(money * 10) / 10; // 抹零至最近的0.1元
        } else {
            return money;
        }
    }

    /**
     * 四舍五入（用于double计算不准确问题）
     *
     * @param num
     * @return
     */
    public static double getDouble(double num) {
        return Math.round(num * 100.0) / 100.0;
    }

    /**
     * 判断字符串是否为纯数字
     *
     * @param str
     * @return
     */
    public static double isNumeric(String str) {
        try {
            // 如果字符串是整数
            return Double.parseDouble(str);
        } catch (NumberFormatException e) {
            return -1;
        }
    }
}
