package com.yxl.cashier_retail.utils;

/**
 * Descride:进制转换类
 * Created by jingang on 2022/8/5
 */
public class HexUtils {
    private static String tag = "111111";

    /**
     * 二进制转十进制
     *
     * @param two
     * @return
     */
    public static int twoToTen(String two) {
        return Integer.parseInt(two);
    }

    /**
     * 二进制转十六进制
     *
     * @param two
     * @return
     */
    public static String twoToSixteen(String two) {
        int ten = Integer.parseInt(two, 2);
        return Integer.toHexString(ten);
    }

    /**
     * 十进制转二进制
     *
     * @param ten
     * @return
     */
    public static String tenToTwo(int ten) {
        return Integer.toBinaryString(ten);
    }

    /**
     * 十进制转十六进制
     *
     * @param ten
     * @return
     */
    public static String tenToSixteen(int ten) {
        return Integer.toHexString(ten);
    }

    /**
     * 十六进制转二进制
     *
     * @param sixteen
     * @return
     */
    public static String sixteenToTwo(String sixteen) {
        int ten = Integer.parseInt(sixteen, 16);
        return Integer.toBinaryString(ten);
    }

    /**
     * 十六进制转十进制
     *
     * @param sixteen
     * @return
     */
    public static int sixteenToTen(String sixteen) {
        return Integer.parseInt(sixteen, 16);
    }

    /**
     * byte转string
     *
     * @param bytes
     * @return
     */
    public static String bytes2HexStr(byte[] bytes) {
        if (bytes == null) {
            return null;
        }
        StringBuilder b = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            b.append(String.format("%02x", bytes[i] & 0xFF));
        }
        return b.toString();
    }

    /*******************************/

    /**
     * 累加和校验，并取反
     */
    public static String makeCheckSum(String data) {
        if (data == null || data.equals("")) {
            return "";
        }
        int total = 0;
        int len = data.length();
        int num = 0;
        while (num < len) {
            String s = data.substring(num, num + 2);
            System.out.println(s);
            total += Integer.parseInt(s, 16);
            num = num + 2;
        }

        //用256求余最大是255，即16进制的FF
        int mod = total % 256;
        if (mod == 0) {
            return "FF";
        } else {
            String hex = Integer.toHexString(mod).toUpperCase();
            //十六进制数取反结果
            hex = parseHex2Opposite(hex);
            return hex;
        }
    }

    /**
     * 取反
     */
    public static String parseHex2Opposite(String str) {
        String hex;
        //十六进制转成二进制
        byte[] er = parseHexStr2Byte(str);

        //取反
        byte erBefore[] = new byte[er.length];
        for (int i = 0; i < er.length; i++) {
            erBefore[i] = (byte) ~er[i];
        }

        //二进制转成十六进制
        hex = parseByte2HexStr(erBefore);

        // 如果不够校验位的长度，补0,这里用的是两位校验
        hex = (hex.length() < 2 ? "0" + hex : hex);

        return hex;
    }


    /**
     * 将二进制转换成十六进制
     */
    public static String parseByte2HexStr(byte buf[]) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < buf.length; i++) {
            String hex = Integer.toHexString(buf[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex.toUpperCase());
        }
        return sb.toString();
    }

    /**
     * 将十六进制转换为二进制
     */
    public static byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr.length() < 1) {
            return null;
        }
        byte[] result = new byte[hexStr.length() / 2];
        for (int i = 0; i < hexStr.length() / 2; i++) {
            int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
            result[i] = (byte) (high * 16 + low);
        }
        return result;
    }

    public static byte[] hexStringToByteArray(String hexStr) {
        hexStr = hexStr.toUpperCase();
        int len = hexStr.length() / 2;
        char[] hexChars = hexStr.toCharArray();
        byte[] bytes = new byte[len];
        for (int i = 0; i < len; i++) {
            int pos = i * 2;
            bytes[i] = (byte) (charToByte(hexChars[pos]) << 4 | charToByte(hexChars[pos + 1]));
        }
        return bytes;
    }

    public static byte charToByte(char c) {
        return (byte) "0123456789ABCDEF".indexOf(c);
    }

    //byte数组转16进制字符串
    public static String Byte2Hex(byte[] inByte) {
        StringBuilder sb = new StringBuilder();
        String hexString;
        for (byte b : inByte) {
            //toHexString方法用于将16进制参数转换成无符号整数值的字符串
            String hex = Integer.toHexString(b);
            if (hex.length() == 1) {
                sb.append("0");//当16进制为个位数时，在前面补0
            }
            sb.append(hex);//将16进制加入字符串
            sb.append(" ");//16进制字符串后补空格区分开
        }
        hexString = sb.toString();
        hexString = hexString.toUpperCase();//将16进制字符串中的字母大写
        return hexString;
    }
}
