package com.yxl.cashier_retail.utils;

import android.text.TextUtils;
import android.util.Log;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

/**
 * Describe:
 * Created by jingang on 2024/9/28
 */
public class ByteUtils {
    private static String tag = "ByteUtils";

    /**
     * 串口秤数据解析
     *
     * @param bytes
     * @return
     */
    public static double getSerialPortScaleData(byte[] bytes) {
        double weight;
        String result = new String(bytes, 0, bytes.length);
//        Log.e(tag, "串口传输数据 = " + result);
        if (result.length() > 0) {
            String data = "";
            Matcher matcher = Pattern.compile("[0-9]\\d{3}").matcher(result);
            if (matcher.find()) {
                data = matcher.group();
            }
            double a = TextUtils.isEmpty(data) ? 0 : Double.parseDouble(data);
            weight = a / 1000;
        } else {
            weight = 0;
        }
        return weight;
    }

    /**
     * 获取文字首字母缩写
     *
     * @param chinese
     * @return
     */
    public static String getChineseFirstLetter(String chinese) {
        if (chinese == null || chinese.trim().isEmpty()) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        char[] chars = chinese.toCharArray();
        HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
        defaultFormat.setCaseType(HanyuPinyinCaseType.UPPERCASE);
        defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        for (char c : chars) {
            if (c > 128) {
                try {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, defaultFormat);
                    if (pinyinArray != null) {
                        sb.append(pinyinArray[0].charAt(0));
                    }
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    e.printStackTrace();
                }
            } else {
                sb.append(c);
            }
        }
        return sb.toString().replaceAll("[^a-zA-Z]", "").toUpperCase();
    }
}
