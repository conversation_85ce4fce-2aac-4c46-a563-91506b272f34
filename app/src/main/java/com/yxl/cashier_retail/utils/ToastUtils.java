package com.yxl.cashier_retail.utils;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.yxl.cashier_retail.R;

/**
 * Describe:Toast公用类
 * Created by jingang on 2022/11/30
 */
public class ToastUtils {

    protected static Toast toast = null;

    private static volatile ToastUtils mToastUtils;

    private ToastUtils(Context context, int type, String msg, int gravity) {
        LayoutInflater inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View layout = inflater.inflate(R.layout.layout_toast, null);
        LinearLayout linToast = layout.findViewById(R.id.linToast);
        ImageView ivToast = layout.findViewById(R.id.ivToast);
        TextView tvToast = layout.findViewById(R.id.tvToast);
        tvToast.setText(msg);
        if (type == 0) {
            linToast.setBackgroundResource(R.drawable.shape_green_5);
            ivToast.setSelected(true);
        } else {
            linToast.setBackgroundResource(R.drawable.shape_red_5);
            ivToast.setSelected(false);
        }
        toast = new Toast(context);
        toast.setGravity(gravity, 0, 0);
        toast.setDuration(Toast.LENGTH_SHORT);
        toast.setView(layout);
    }

    public static ToastUtils getInstance(Context context, int type, String msg, int gravity) {
//        if (null == mToastUtils) {
//            synchronized (ToastUtils.class) {
//                if (null == mToastUtils) {
//                    mToastUtils = new ToastUtils(context, type, msg, gravity);
//                }
//            }
//        }
        if (toast != null) {
            toast.cancel();
            toast = null;
        }
        mToastUtils = new ToastUtils(context, type, msg, gravity);
        return mToastUtils;
    }

    public void showMessage() {
        if (toast != null) {
            toast.show();
        }
    }

}
