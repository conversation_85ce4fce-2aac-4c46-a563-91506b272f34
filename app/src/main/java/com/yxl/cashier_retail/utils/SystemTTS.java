package com.yxl.cashier_retail.utils;

import android.content.Context;
import android.speech.tts.TextToSpeech;
import android.speech.tts.UtteranceProgressListener;
import android.text.TextUtils;
import android.util.Log;

import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;

import java.util.Locale;

import kotlin.jvm.Volatile;

/**
 * 语音播报
 */
public class SystemTTS extends UtteranceProgressListener implements TTS {
    private Context context;

    public SystemTTS(Context context) {
        this.context = context;
        textToSpeech = new TextToSpeech(context, status -> {
            Log.e("111111", "status = " + status);
            //系统语音初始化成功
            if (status == TextToSpeech.SUCCESS) {
                int result;
                String language = SPUtils.getInstance().getString(Constants.LANGUAGE, "");
                if (TextUtils.isEmpty(language)) {
                    result = textToSpeech.setLanguage(Locale.CHINA);
                } else {
                    switch (language) {
                        case "en":
                            result = textToSpeech.setLanguage(Locale.US);
                            break;
                        case "th":
                            result = textToSpeech.setLanguage(new Locale("th-TH"));
                            break;
                        case "ru":
                            result = textToSpeech.setLanguage(new Locale("ru"));
                            break;
                        default:
                            result = textToSpeech.setLanguage(Locale.CHINA);
                            break;
                    }
                }
//                int languageResult = textToSpeech.setLanguage(Locale.CHINA);
                textToSpeech.setPitch(1.0f);//设置音调，值越大声音越尖（女生），值越小则变成男声,1.0是常规
                textToSpeech.setSpeechRate(1.0f);
                textToSpeech.setOnUtteranceProgressListener(SystemTTS.this);
                //textToSpeech.setOnUtteranceCompletedListener(this)
//                if (languageResult == TextToSpeech.LANG_MISSING_DATA || languageResult == TextToSpeech.LANG_NOT_SUPPORTED) {
//                    //系统不支持中文播报
//                    isSuccess = false;
//                }
                if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                    //系统不支持中文播报
                    isSuccess = false;
                    //系统不支持时可引导用户设置语音引擎
//                    Intent installIntent = new Intent();
//                    installIntent.setAction(TextToSpeech.Engine.ACTION_INSTALL_TTS_DATA);
//                    startActivity(installIntent);
                }
            }
        });
    }

    TextToSpeech textToSpeech;
    boolean isSuccess = true;

    @Volatile
    private static volatile SystemTTS instance;

    public static SystemTTS getInstance(Context context) {
//        if (instance == null) {
//            synchronized (SystemTTS.class) {
//                if (instance == null) {
//                    instance = new SystemTTS(context);
//                }
//            }
//        }
//        return instance;
        return new SystemTTS(context);
    }

    @Override
    public void onStart(String utteranceId) {

    }

    @Override
    public void onDone(String utteranceId) {

    }

    @Override
    public void onError(String utteranceId) {

    }

    @Override
    public void playText(String playText) {
        Log.e("111111", "playText = " + playText + " isSuccess = " + isSuccess);
        if (!isSuccess) {
//            ToastUtils.getInstance(BaseApplication.getInstance(), 1, "系统不支持中文播报", Gravity.TOP).showMessage();
            return;
        }
//        if(textToSpeech.isSpeaking()){
//            textToSpeech.stop();
//        }
        textToSpeech.speak(playText, TextToSpeech.QUEUE_FLUSH, null, null);
    }

    @Override
    public void stopSpeak() {
        textToSpeech.stop();
    }
}
