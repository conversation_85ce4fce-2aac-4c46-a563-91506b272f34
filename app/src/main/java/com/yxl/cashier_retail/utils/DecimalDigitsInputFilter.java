package com.yxl.cashier_retail.utils;

import android.text.InputFilter;
import android.text.Spanned;

/**
 * 输入库类型为小数时：小数点后位数
 */
public class DecimalDigitsInputFilter implements InputFilter {
    private int decimalDigits;

    public DecimalDigitsInputFilter(int decimalDigits) {
        this.decimalDigits = decimalDigits;
    }

    @Override
    public CharSequence filter(CharSequence source, int start, int end, Spanned dest, int dstart, int dend) {
        int dotPos = -1;
        int len = dest.length();
        for (int i = 0; i < len; i++) {
            char c = dest.charAt(i);
            if (c == '.' || c == ',') {
                dotPos = i;
                break;
            }
        }
        if (source == "." && dstart == 0 && dend == 0) {
            return "";
        }
        if (dotPos >= 0) {

            // protects against many dots
            if (source == "." || source == ",") {
                return "";
            }
            // if the text is entered before the dot
            if (dend <= dotPos) {
                return null;
            }
            if ((len - dotPos) > decimalDigits) {
                return "";
            }
        }
        return null;
    }
}
