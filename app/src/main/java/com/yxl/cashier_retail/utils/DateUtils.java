package com.yxl.cashier_retail.utils;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.util.Log;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * Describe:日期工具类
 * Created by jingang on 2024/5/21
 */
public class DateUtils {
    public static String PATTERN_DAY = "yyyy-MM-dd";
    public static String PATTERN_SECOND = "yyyy-MM-dd HH:mm:ss";

    /**
     * 描述：获取表示当前日期时间的字符串.
     *
     * @param format 格式化字符串，如："yyyy-MM-dd HH:mm:ss"
     * @return String类型的当前日期时间
     */
    @SuppressLint("SimpleDateFormat")
    public static String getCurrentDate(String format) {
        String curDateTime = null;
        try {
            SimpleDateFormat mSimpleDateFormat = new SimpleDateFormat(format);
            Calendar c = new GregorianCalendar();
            curDateTime = mSimpleDateFormat.format(c.getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return curDateTime;
    }

    /**
     * 时间戳转换成字符窜
     *
     * @param milSecond
     * @param pattern   "%d:%02d:%02d"
     * @return
     */
    @SuppressLint("SimpleDateFormat")
    public static String getDateToString(long milSecond, String pattern) {
        return new SimpleDateFormat(pattern).format(milSecond);
    }

    /**
     * 获取当前日期前后n天
     *
     * @param distanceDay
     * @return
     */
    @SuppressLint("SimpleDateFormat")
    public static String getOldDate(int distanceDay) {
        SimpleDateFormat dft = new SimpleDateFormat(PATTERN_DAY);
        Date beginDate = new Date();
        Calendar date = Calendar.getInstance();
        date.setTime(beginDate);
        date.set(Calendar.DATE, date.get(Calendar.DATE) + distanceDay);
        Date endDate = null;
        try {
            endDate = dft.parse(dft.format(date.getTime()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        assert endDate != null;
        return dft.format(endDate);
    }

    /**
     * 获取特定日期前后n天
     *
     * @param distanceDay
     * @return
     */
    public static String getOldDate(String strDate, int distanceDay) {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat(PATTERN_DAY);
        try {
            // 将字符串转换为Date
            Date date = sdf.parse(strDate);
            // 将Date设置到Calendar中
            calendar.setTime(date);
        } catch (ParseException e) {
            e.printStackTrace();
            // 处理解析日期字符串时可能发生的异常
        }
        calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) + distanceDay);
        Date endDate = null;
        try {
            endDate = sdf.parse(sdf.format(calendar.getTime()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return sdf.format(endDate);
    }

    /**
     * 时间戳转为date
     *
     * @param timestamp
     * @return
     */
    public static Date getDateForTimestamp(String timestamp) {
        if (TextUtils.isEmpty(timestamp)) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat(PATTERN_DAY);
        Date date;
        try {
            // 将字符串转换为Date
            date = sdf.parse(timestamp);
            // 将Date设置到Calendar中
            calendar.setTime(date);
        } catch (ParseException e) {
            e.printStackTrace();
            // 处理解析日期字符串时可能发生的异常
            date = null;
        }
        return date;
    }
}
