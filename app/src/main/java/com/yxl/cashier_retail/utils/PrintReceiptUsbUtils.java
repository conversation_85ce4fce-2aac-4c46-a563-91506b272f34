package com.yxl.cashier_retail.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.text.TextUtils;

import com.alibaba.fastjson.JSONArray;
import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.MyApplication;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.ui.bean.MemberData;
import com.yxl.cashier_retail.ui.bean.PaymentMethodData;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.utils.StringUtils;

import net.posprinter.POSConst;
import net.posprinter.POSPrinter;
import net.posprinter.model.PTable;

import java.util.List;

/**
 * Describe:小票打印-USB
 * Created by jingang on 2024/9/25
 */
public class PrintReceiptUsbUtils {
    private static Context mContext;
    private static POSPrinter printer;

    /**
     * 打印小票（网单）
     *
     * @param serialNumber  流水号
     * @param cashierName   收银机名称
     * @param goodsList     商品列表
     * @param discount      优惠
     * @param payment       应付
     * @param actualPayment 实付
     * @param arrayPayment  支付方式
     * @param memberData    会员信息
     * @param points        本单积分
     * @param memberPoints  会员积分
     * @param remarks       备注
     * @param orderNo       订单编号
     */
    public static void print(Context context, String serialNumber, String cashierName, List<GoodsData> goodsList,
                             double discount, double payment, double actualPayment,
                             JSONArray arrayPayment,
                             MemberData memberData, double points, double memberPoints,
                             String remarks, String orderNo) {
        mContext = context;
        if (MyApplication.getInstance().curConnect == null) {
            return;
        }
        if (printer == null) {
            printer = new POSPrinter(MyApplication.getInstance().curConnect);
        }
        double count = 0;
        double total = 0;
        printer.initializePrinter();
        printer.printText("**" + BaseApplication.getInstance().getShopName() + "**\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_BOLD, POSConst.TXT_2HEIGHT | POSConst.TXT_1WIDTH);
        printer.printText(getRstr(R.string.order_receipt) + "\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, 10);
        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, 10);
        printer.printText("--" + serialNumber + "--\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_BOLD, POSConst.TXT_2HEIGHT | POSConst.TXT_1WIDTH);
        if (!TextUtils.isEmpty(cashierName)) {
            printer.printText(getRstr(R.string.cash_register_colon) + cashierName + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, 10);
        }
        printer.printText(getRstr(R.string.cashier_staff_colon) + BaseApplication.getInstance().getStaffUnique() + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, 10);
        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, 10);
        /*商品信息start*/
        Integer[] integers = new Integer[]{10, 7, 7, 7};
        printer.printTable(new PTable(new String[]{getRstr(R.string.goods), getRstr(R.string.count), getRstr(R.string.price), getRstr(R.string.amount)}, integers));
        for (int i = 0; i < goodsList.size(); i++) {
            count = count + goodsList.get(i).getCartNum();
//            double price = goodsList.get(i).getNewPrice() > 0 ? goodsList.get(i).getNewPrice() : goodsList.get(i).getGoods_sale_price();
            double price;
            if (goodsList.get(i).getNewPrice() > 0) {
                price = goodsList.get(i).getNewPrice();
            } else {
                if (memberData != null) {
                    price = TextUtils.isEmpty(goodsList.get(i).getGoods_cus_price()) ? 0 : Double.parseDouble(goodsList.get(i).getGoods_cus_price());
                } else {
                    price = goodsList.get(i).getGoods_sale_price();
                }
            }
            total = DFUtils.getDouble(total + DFUtils.getDouble(goodsList.get(i).getCartNum() * price));
            printer.printText(goodsList.get(i).getGoods_name() + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, 10);
            printer.printTable(new PTable(new String[]{"",
                    DFUtils.getNum4(goodsList.get(i).getCartNum()),
                    DFUtils.getNum2(price),
                    DFUtils.getNum2(DFUtils.getDouble(goodsList.get(i).getCartNum()) * price)},
                    integers));
        }
        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, 10);
        printer.printTable(new PTable(new String[]{getRstr(R.string.amount), DFUtils.getNum4(count), "", DFUtils.getNum2(total)}, integers));
        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, 10);
        printer.printText(getRstr(R.string.discount_colon) + DFUtils.getNum2(discount) + getRstr(R.string.yuan) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, 10);
        printer.printText(getRstr(R.string.payable_colon) + DFUtils.getNum2(payment) + getRstr(R.string.yuan) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, 10);
        printer.printText(getRstr(R.string.paid_colon) + DFUtils.getNum2(actualPayment) + getRstr(R.string.yuan) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, 10);
        /*商品信息end*/

        /*支付信息start*/
        if (!TextUtils.isEmpty(arrayPayment.toString())) {
            List<PaymentMethodData> paymentList = arrayPayment.toJavaList(PaymentMethodData.class);
            if (paymentList != null) {
                for (int i = 0; i < paymentList.size(); i++) {
                    if (i == 0) {
                        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, 10);
                    }
                    String paymentName;
                    //支付方式 1.现金 2.支付宝 3.微信 4.银行卡 5.储值卡 8.混合支付 9.免密支付
                    switch (paymentList.get(i).getPay_method()) {
                        case 1:
                            paymentName = getRstr(R.string.balance_colon);
                            break;
                        case 2:
                            paymentName = getRstr(R.string.alipay_colon);
                            break;
                        case 3:
                            paymentName = getRstr(R.string.wechat_colon);
                            break;
                        case 4:
                            paymentName = getRstr(R.string.bank_card_colon);
                            break;
                        case 5:
                            paymentName = getRstr(R.string.stored_card_colon);
                            break;
                        default:
                            paymentName = "";
                            break;
                    }
                    printer.printText(paymentName + DFUtils.getNum2(paymentList.get(i).getPay_money()) + getRstr(R.string.yuan) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, 10);
                }
            }
        }
        /*支付信息end*/

        /*会员信息start*/
        if (memberData != null) {
            printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, 10);
            printer.printText(getRstr(R.string.member_colon) + StringUtils.formattedName(memberData.getCusName()) + " " + StringUtils.getStarMobile(memberData.getCusPhone()) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, 10);
            printer.printText(getRstr(R.string.order_points_colon) + DFUtils.getNum4(points) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, 10);
            printer.printText(getRstr(R.string.member_points_colon) + DFUtils.getNum4(memberPoints) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, 10);
        }
        /*会员信息end*/

        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, 10);
//        printer.printText(getRstr(R.string.star_remarks) + "\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, 12);
//        printer.printText((TextUtils.isEmpty(remarks) ? remarks : getRstr(R.string.nothing)) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, 10);
        printer.printText(DateUtils.getCurrentDate(DateUtils.PATTERN_SECOND) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, 10);
        if (!TextUtils.isEmpty(orderNo)) {
            printer.printBarCode("{B" + orderNo, POSConst.BCS_Code128, 3, 81, POSConst.ALIGNMENT_CENTER, POSConst.HRI_TEXT_NONE);
            printer.printText(getRstr(R.string.order_no_colon) + orderNo + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, 10);
        }
        printer.printText(getRstr(R.string.contact_mobile_colon) + BaseApplication.getInstance().getStaffMobile() + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, 10);
        printer.printText(getRstr(R.string.tips_warm_colon) + SPUtils.getInstance().getString(Constants.RECEIPT_TIPS) + "\n\n\n\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, 10);
        printer.cutHalfAndFeed(1);
    }

    /**
     * 打印小票（网单）
     *
     * @param goodsList     商品列表
     * @param discount      优惠
     * @param payment       应付
     * @param actualPayment 实付
     * @param arrayPayment  支付方式
     * @param memberData    会员信息
     * @param points        本单积分
     * @param memberPoints  会员积分
     * @param orderNo       订单编号
     */
    public static void printOnline(Context context,
                                   List<GoodsData> goodsList, double discount, double payment, double actualPayment,
                                   JSONArray arrayPayment,
                                   MemberData memberData, double points, double memberPoints,
                                   String orderNo, Bitmap bitmap) {
        mContext = context;
        if (MyApplication.getInstance().curConnect == null) {
            return;
        }
        if (printer == null) {
            printer = new POSPrinter(MyApplication.getInstance().curConnect);
        }
        double count = 0;
        double total = 0;
        int textSize = POSConst.TXT_1WIDTH | POSConst.TXT_1HEIGHT;
        printer.initializePrinter();
        printer.printText("**" + BaseApplication.getInstance().getShopName() + "**\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_BOLD, POSConst.TXT_2WIDTH | POSConst.TXT_2HEIGHT);
        printer.feedLine();
        printer.printText(getRstr(R.string.order_receipt) + "\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.cash_register_colon) + "1\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.cashier_staff_colon) + BaseApplication.getInstance().getStaffName() + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
        /*商品信息start*/
        Integer[] integers = new Integer[]{10, 7, 7, 7};
        printer.printTable(new PTable(new String[]{getRstr(R.string.goods), getRstr(R.string.count), getRstr(R.string.price), getRstr(R.string.amount)}, integers));
        for (int i = 0; i < goodsList.size(); i++) {
            count = count + goodsList.get(i).getCartNum();
//            double price = goodsList.get(i).getNewPrice() > 0 ? goodsList.get(i).getNewPrice() : goodsList.get(i).getGoods_sale_price();
            double price;
            if (goodsList.get(i).getNewPrice() > 0) {
                price = goodsList.get(i).getNewPrice();
            } else {
                if (memberData != null) {
                    price = TextUtils.isEmpty(goodsList.get(i).getGoods_cus_price()) ? 0 : Double.parseDouble(goodsList.get(i).getGoods_cus_price());
                } else {
                    price = goodsList.get(i).getGoods_sale_price();
                }
            }
            total = DFUtils.getDouble(total + DFUtils.getDouble(goodsList.get(i).getCartNum() * price));
            printer.printText(goodsList.get(i).getGoods_name() + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
            printer.printTable(new PTable(new String[]{"",
                    DFUtils.getNum4(goodsList.get(i).getCartNum()),
                    DFUtils.getNum2(price),
                    DFUtils.getNum2(DFUtils.getDouble(goodsList.get(i).getCartNum()) * price)},
                    integers));
        }
        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
        printer.printTable(new PTable(new String[]{getRstr(R.string.amount), DFUtils.getNum4(count), "", DFUtils.getNum2(total)}, integers));
        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.payable_colon) + DFUtils.getNum2(payment) + getRstr(R.string.yuan) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.discount_colon) + DFUtils.getNum2(discount) + getRstr(R.string.yuan) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.paid_colon) + DFUtils.getNum2(actualPayment) + getRstr(R.string.yuan) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        /*商品信息end*/

        /*支付信息start*/
        if (!TextUtils.isEmpty(arrayPayment.toString())) {
            List<PaymentMethodData> paymentList = arrayPayment.toJavaList(PaymentMethodData.class);
            if (paymentList != null) {
                for (int i = 0; i < paymentList.size(); i++) {
                    if (i == 0) {
                        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
                    }
                    String paymentName;
                    //支付方式 1.现金 2.支付宝 3.微信 4.银行卡 5.储值卡 8.混合支付 9.免密支付
                    switch (paymentList.get(i).getPay_method()) {
                        case 1:
                            paymentName = getRstr(R.string.cash_colon);
                            break;
                        case 2:
                            paymentName = getRstr(R.string.alipay_colon);
                            break;
                        case 3:
                            paymentName = getRstr(R.string.wechat_colon);
                            break;
                        case 4:
                            paymentName = getRstr(R.string.bank_card_colon);
                            break;
                        case 5:
                            paymentName = getRstr(R.string.stored_card_colon);
                            break;
                        default:
                            paymentName = "";
                            break;
                    }
                    printer.printText(paymentName + DFUtils.getNum2(paymentList.get(i).getPay_money()) + getRstr(R.string.yuan) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
                }
            }
        }
        /*支付信息end*/

        /*会员信息start*/
        if (memberData != null) {
            printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
            printer.printText(getRstr(R.string.member_colon) + StringUtils.formattedName(memberData.getCusName()) + " " + StringUtils.getStarMobile(memberData.getCusPhone()) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
            printer.printText(getRstr(R.string.order_points_colon) + DFUtils.getNum4(points) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
            printer.printText(getRstr(R.string.member_points_colon) + DFUtils.getNum4(memberPoints) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        }
        /*会员信息end*/

        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.star_remarks) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText(SPUtils.getInstance().getString(Constants.RECEIPT_REMARKS, getRstr(R.string.thanks_welcome_again)) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText(DateUtils.getCurrentDate(DateUtils.PATTERN_SECOND) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        if (!TextUtils.isEmpty(orderNo)) {
            if (bitmap != null) {
                printer.printBitmap(bitmap, POSConst.ALIGNMENT_LEFT, 384);
                printer.feedLine();
            }
            printer.printText(getRstr(R.string.order_no_colon) + orderNo + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        }
        printer.printText(getRstr(R.string.contact_mobile_colon) + BaseApplication.getInstance().getStaffMobile() + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.receipt_tips) + "\n\n\n\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.cutHalfAndFeed(1);
    }

    /**
     * 打印小票（收款小票）
     *
     * @param goodsList     商品列表
     * @param discount      优惠
     * @param payment       应付
     * @param actualPayment 实付
     * @param arrayPayment  支付方式
     * @param memberData    会员信息
     * @param points        本单积分
     * @param memberPoints  会员积分
     * @param orderNo       订单编号
     */
    public static void printPayment(Context context,
                                    List<GoodsData> goodsList, double discount, double payment, double actualPayment,
                                    JSONArray arrayPayment,
                                    MemberData memberData, double points, double memberPoints,
                                    String orderNo, Bitmap bitmap) {
        mContext = context;
        if (MyApplication.getInstance().curConnect == null) {
            return;
        }
        if (printer == null) {
            printer = new POSPrinter(MyApplication.getInstance().curConnect);
        }
        double count = 0;
        double total = 0;
        int textSize = POSConst.TXT_1WIDTH | POSConst.TXT_1HEIGHT;
        printer.initializePrinter();
        printer.printText("**" + BaseApplication.getInstance().getShopName() + "**\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_BOLD, POSConst.TXT_2WIDTH | POSConst.TXT_2HEIGHT);
        printer.feedLine();
        printer.printText(getRstr(R.string.statement) + "\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.cash_register_colon) + "1\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.cashier_staff_colon) + BaseApplication.getInstance().getStaffName() + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
        /*商品信息start*/
        Integer[] integers = new Integer[]{10, 7, 7, 7};
        printer.printTable(new PTable(new String[]{getRstr(R.string.goods), getRstr(R.string.count), getRstr(R.string.price), getRstr(R.string.amount)}, integers));
        for (int i = 0; i < goodsList.size(); i++) {
            count = count + goodsList.get(i).getCartNum();
//            double price = goodsList.get(i).getNewPrice() > 0 ? goodsList.get(i).getNewPrice() : goodsList.get(i).getGoods_sale_price();
            double price;
            if (goodsList.get(i).getNewPrice() > 0) {
                price = goodsList.get(i).getNewPrice();
            } else {
                if (memberData != null) {
                    price = TextUtils.isEmpty(goodsList.get(i).getGoods_cus_price()) ? 0 : Double.parseDouble(goodsList.get(i).getGoods_cus_price());
                } else {
                    price = goodsList.get(i).getGoods_sale_price();
                }
            }
            total = DFUtils.getDouble(total + DFUtils.getDouble(goodsList.get(i).getCartNum() * price));
            printer.printText(goodsList.get(i).getGoods_name() + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
            printer.printTable(new PTable(new String[]{"",
                    DFUtils.getNum4(goodsList.get(i).getCartNum()),
                    DFUtils.getNum2(price),
                    DFUtils.getNum2(DFUtils.getDouble(goodsList.get(i).getCartNum()) * price)},
                    integers));
        }
        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
        printer.printTable(new PTable(new String[]{getRstr(R.string.amount), DFUtils.getNum4(count), "", DFUtils.getNum2(total)}, integers));
        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.payable_colon) + DFUtils.getNum2(payment) + getRstr(R.string.yuan) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.discount_colon) + DFUtils.getNum2(discount) + getRstr(R.string.yuan) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.paid_colon) + DFUtils.getNum2(actualPayment) + getRstr(R.string.yuan) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        /*商品信息end*/

        /*支付信息start*/
        if (!TextUtils.isEmpty(arrayPayment.toString())) {
            List<PaymentMethodData> paymentList = arrayPayment.toJavaList(PaymentMethodData.class);
            if (paymentList != null) {
                for (int i = 0; i < paymentList.size(); i++) {
                    if (i == 0) {
                        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
                    }
                    String paymentName;
                    //支付方式 1.现金 2.支付宝 3.微信 4.银行卡 5.储值卡 8.混合支付 9.免密支付
                    switch (paymentList.get(i).getPay_method()) {
                        case 1:
                            paymentName = getRstr(R.string.cash_colon);
                            break;
                        case 2:
                            paymentName = getRstr(R.string.alipay_colon);
                            break;
                        case 3:
                            paymentName = getRstr(R.string.wechat_colon);
                            break;
                        case 4:
                            paymentName = getRstr(R.string.bank_card_colon);
                            break;
                        case 5:
                            paymentName = getRstr(R.string.stored_card_colon);
                            break;
                        case 13:
                            paymentName = getRstr(R.string.jinquan_plat) + ":";
                            break;
                        default:
                            paymentName = "";
                            break;
                    }
                    printer.printText(paymentName + DFUtils.getNum2(paymentList.get(i).getPay_money()) + getRstr(R.string.yuan) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
                }
            }
        }
        /*支付信息end*/

        /*会员信息start*/
        if (memberData != null) {
            printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
            printer.printText(getRstr(R.string.member_colon) + StringUtils.formattedName(memberData.getCusName()) + " " + StringUtils.getStarMobile(memberData.getCusPhone()) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
            if (points != 0) {
                printer.printText(getRstr(R.string.order_points_colon) + DFUtils.getNum4(points) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
            }
            if (memberPoints != 0) {
                printer.printText(getRstr(R.string.member_points_colon) + DFUtils.getNum4(memberPoints) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
            }
        }
        /*会员信息end*/

        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.star_remarks) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText(SPUtils.getInstance().getString(Constants.RECEIPT_REMARKS, getRstr(R.string.thanks_welcome_again)) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText(DateUtils.getCurrentDate(DateUtils.PATTERN_SECOND) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        if (!TextUtils.isEmpty(orderNo)) {
            if (bitmap != null) {
                printer.printBitmap(bitmap, POSConst.ALIGNMENT_LEFT, 384);
                printer.feedLine();
            }
            printer.printText(getRstr(R.string.order_no_colon) + orderNo + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        }
        printer.printText(getRstr(R.string.contact_mobile_colon) + BaseApplication.getInstance().getStaffMobile() + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.receipt_tips) + "\n\n\n\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.cutHalfAndFeed(1);
    }

    /**
     * 打印小票（挂单）
     *
     * @param goodsList  商品列表
     * @param discount   优惠
     * @param payment    应付
     * @param memberData 会员信息
     */
    public static void printHang(Context context,
                                 List<GoodsData> goodsList, double discount, double payment,
                                 MemberData memberData) {
        mContext = context;
        if (MyApplication.getInstance().curConnect == null) {
            return;
        }
        if (printer == null) {
            printer = new POSPrinter(MyApplication.getInstance().curConnect);
        }
        double count = 0;
        double total = 0;
        int textSize = POSConst.TXT_1WIDTH | POSConst.TXT_1HEIGHT;
        printer.initializePrinter();
        printer.printText("**" + BaseApplication.getInstance().getShopName() + "**\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_BOLD, POSConst.TXT_2WIDTH | POSConst.TXT_2HEIGHT);
        printer.feedLine();
        printer.printText(getRstr(R.string.hang_order) + "\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.cash_register_colon) + "1\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.cashier_staff_colon) + BaseApplication.getInstance().getStaffName() + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
        /*商品信息start*/
        Integer[] integers = new Integer[]{10, 7, 7, 7};
        printer.printTable(new PTable(new String[]{getRstr(R.string.goods), getRstr(R.string.count), getRstr(R.string.price), getRstr(R.string.amount)}, integers));
        for (int i = 0; i < goodsList.size(); i++) {
            count = count + goodsList.get(i).getCartNum();
//            double price = goodsList.get(i).getNewPrice() > 0 ? goodsList.get(i).getNewPrice() : goodsList.get(i).getGoods_sale_price();
            double price;
            if (goodsList.get(i).getNewPrice() > 0) {
                price = goodsList.get(i).getNewPrice();
            } else {
                if (memberData != null) {
                    price = TextUtils.isEmpty(goodsList.get(i).getGoods_cus_price()) ? 0 : Double.parseDouble(goodsList.get(i).getGoods_cus_price());
                } else {
                    price = goodsList.get(i).getGoods_sale_price();
                }
            }
            total = DFUtils.getDouble(total + DFUtils.getDouble(goodsList.get(i).getCartNum() * price));
            printer.printText(goodsList.get(i).getGoods_name() + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
            printer.printTable(new PTable(new String[]{"",
                    DFUtils.getNum4(goodsList.get(i).getCartNum()),
                    DFUtils.getNum2(price),
                    DFUtils.getNum2(DFUtils.getDouble(goodsList.get(i).getCartNum()) * price)},
                    integers));
        }
        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
        printer.printTable(new PTable(new String[]{getRstr(R.string.amount), DFUtils.getNum4(count), "", DFUtils.getNum2(total)}, integers));
        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.payable_colon) + DFUtils.getNum2(payment) + getRstr(R.string.yuan) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.discount_colon) + DFUtils.getNum2(discount) + getRstr(R.string.yuan) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        /*商品信息end*/

        /*会员信息start*/
        if (memberData != null) {
            printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
            printer.printText(getRstr(R.string.member_colon) + StringUtils.formattedName(memberData.getCusName()) + " " + StringUtils.getStarMobile(memberData.getCusPhone()) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        }
        /*会员信息end*/

        printer.printText("--------------------------------\n", POSConst.ALIGNMENT_CENTER, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.star_remarks) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText(SPUtils.getInstance().getString(Constants.RECEIPT_REMARKS, getRstr(R.string.thanks_welcome_again)) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText(DateUtils.getCurrentDate(DateUtils.PATTERN_SECOND) + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.printText(getRstr(R.string.contact_mobile_colon) + BaseApplication.getInstance().getStaffMobile() + "\n", POSConst.ALIGNMENT_LEFT, POSConst.FNT_DEFAULT, textSize);
        printer.cutHalfAndFeed(1);
    }

    /**
     * 支付前弹钱箱
     *
     * @param isUseCashBox
     */
    public static void openCashBox(boolean isUseCashBox) {
        if (!isUseCashBox) {
            return;
        }
        if (MyApplication.getInstance().curConnect == null) {
            return;
        }
        if (printer == null) {
            printer = new POSPrinter(MyApplication.getInstance().curConnect);
        }
        printer.openCashBox(POSConst.PIN_TWO);
    }

    /**
     * String转化
     */
    public static String getRstr(int str) {
        return mContext.getResources().getString(str);
    }
}
