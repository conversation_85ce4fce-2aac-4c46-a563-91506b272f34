package com.yxl.cashier_retail;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.lifecycle.LifecycleOwner;
import androidx.multidex.MultiDex;

import com.blankj.utilcode.util.SPUtils;
import com.google.gson.Gson;
import com.jakewharton.threetenabp.AndroidThreeTen;
import com.kongqw.serialportlibrary.Driver;
import com.kongqw.serialportlibrary.SerialUtils;
import com.kongqw.serialportlibrary.enumerate.SerialPortEnum;
import com.kongqw.serialportlibrary.enumerate.SerialStatus;
import com.kongqw.serialportlibrary.listener.SerialPortDirectorListens;
import com.scwang.smart.refresh.footer.ClassicsFooter;
import com.scwang.smart.refresh.header.ClassicsHeader;
import com.scwang.smart.refresh.layout.SmartRefreshLayout;
import com.yxl.cashier_retail.mqtt.MQTTHelper;
import com.yxl.cashier_retail.mqtt.data.Qos;
import com.yxl.cashier_retail.mqtt.data.Topic;
import com.yxl.cashier_retail.network.Network;
import com.yxl.cashier_retail.network.NetworkConnectChangedReceiver;
import com.yxl.cashier_retail.ui.bean.BaseGoodsData;
import com.yxl.cashier_retail.ui.bean.EventData;
import com.yxl.cashier_retail.ui.bean.GoodsData;
import com.yxl.cashier_retail.ui.bean.MqttData;
import com.yxl.cashier_retail.utils.HexUtils;
import com.yxl.cashier_retail.utils.MultiLanguageUtils;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.http.RXHttpUtil;
import com.yxl.commonlibrary.http.RequestListener;
import com.yxl.commonlibrary.http.RxHttpManager;
import com.yxl.commonlibrary.utils.EventBusManager;

import net.posprinter.IConnectListener;
import net.posprinter.IDeviceConnection;
import net.posprinter.POSConnect;

import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.IMqttToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.litepal.LitePal;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

import kotlin.jvm.internal.Intrinsics;
import me.jessyan.autosize.AutoSizeConfig;

@SuppressLint("StaticFieldLeak")
public class MyApplication extends BaseApplication {
    private String tag = "MyApplication";
    static MyApplication instance;
    static Context context;
    private ArrayList<Activity> activity_list = new ArrayList<>();

    private NetworkConnectChangedReceiver networkConnectChangedReceiver = new NetworkConnectChangedReceiver();
    public static Network mNetwork;

    public synchronized static MyApplication getInstance() {
        if (null == instance) {
            instance = new MyApplication();
        }
        return instance;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        setApplication(this);
        getLoginData(SPUtils.getInstance().getString(Constants.LOGIN_ID, ""));//登录用户信息
        AutoSizeConfig autoConfig = AutoSizeConfig.getInstance().setCustomFragment(true).setExcludeFontScale(true);
        Intrinsics.checkNotNullExpressionValue(autoConfig, "AutoSizeConfig.getInstan…setExcludeFontScale(true)");
        autoConfig.setBaseOnWidth(true);
        instance = this;
        context = getApplicationContext();
        //多语言设置
        registerActivityLifecycleCallbacks(MultiLanguageUtils.callbacks);
        RxHttpManager.init(this);
        if (BuildConfig.DEBUG) {
            setUrl();
        }

        //注册网络监听广播
        IntentFilter filter = new IntentFilter();
        filter.addAction("android.net.conn.CONNECTIVITY_CHANGE");
        filter.addAction("android.net.wifi.WIFI_STATE_CHANGE");
        filter.addAction("android.net.conn.STATE_CHANGE");
        registerReceiver(networkConnectChangedReceiver, filter);

        //XPrinter小票打印机
        POSConnect.init(this);

        //
        AndroidThreeTen.init(this);
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        MultiDex.install(this);
    }

    //全局添加Activity
    public void addActivity(Activity _activity) {
        activity_list.add(_activity);
    }

    //删除Activity
    public void removeActivity(Activity activity) {
        activity_list.remove(activity);
    }

    //退出
    public void exit(Activity nowActivity) {
        Iterator<Activity> iterator = activity_list.iterator();
        while (iterator.hasNext()) {
            Activity activity = iterator.next();
            if (activity != nowActivity) {
                activity.finish();
                iterator.remove();
            }
        }
//        Intent intent = new Intent(getApplicationContext(), LoginActivity.class);
//        nowActivity.startActivity(intent);
        nowActivity.finish();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }

    static {
        //设置全局的Header构建器
        SmartRefreshLayout.setDefaultRefreshHeaderCreator((context, layout) -> {
            return new ClassicsHeader(context);//.setTimeFormat(new DynamicTimeFormat("更新于 %s"));//指定为经典Header，默认是 贝塞尔雷达Header
        });
        //设置全局的Footer构建器
        SmartRefreshLayout.setDefaultRefreshFooterCreator((context, layout) -> {
            //指定为经典Footer，默认是 BallPulseFooter
            return new ClassicsFooter(context).setDrawableSize(20);
        });
    }

    /**
     * 设置环境
     */
    public static void setUrl() {
        //0.测试 1.正式
        int i = SPUtils.getInstance().getInt("test", 0);
        String input = SPUtils.getInstance().getString("testInput", "");
        Log.e("111111", "当前环境 = " + i + " input = " + input);
        switch (i) {
            case 1:
                ZURL.ONLINE_URL = ZURL.URL_RELEASE;
                ZURL.MALL_ONLINE_URL = ZURL.URL_MALL_RELEASE;
                break;
            case 2:
                ZURL.ONLINE_URL = input;
                ZURL.MALL_ONLINE_URL = input;
                break;
            case 3:
                ZURL.ONLINE_URL = ZURL.URL_DEV;
                ZURL.MALL_ONLINE_URL = ZURL.URL_MALL_DEV;
                break;
            default:
                ZURL.ONLINE_URL = ZURL.URL_DEBUG;
                ZURL.MALL_ONLINE_URL = ZURL.URL_MALL_DEBUG;
                break;
        }
    }

    /*************************mqtt-start***********************/
    public MQTTHelper mqttHelper;
    public boolean isMqttConnect;//mqtt是否连接

    public void initMqtt() {
        isMqttConnect = false;
        if (mqttHelper == null) {
            mqttHelper = new MQTTHelper(this);
        }
        Log.e(tag, "MQTT开始连接");
        mqttHelper.setMyMqttActionListener(new MQTTHelper.MyMqttActionListener() {
            @Override
            public void onSuccess(@Nullable IMqttToken asyncActionToken) {
                Log.e(tag, "连接成功");
                isMqttConnect = true;
                //订阅
                mqttHelper.subscribe(Topic.TOPIC_SEND, Qos.QOS_TWO);
                String json = "{\"ctrl\":\"msg_init\",\"ID\":"
                        + SPUtils.getInstance().getString(Constants.DEVICE_ID, "")
                        + ",\"data\":{\"shop_unique\":" + getShopUnique()
                        + "}}";
                publish(json);
            }

            @Override
            public void onFailure(@Nullable IMqttToken asyncActionToken) {
                Log.e(tag, "连接失败 = " + asyncActionToken.getMessageId());
                isMqttConnect = false;
            }
        });
        Log.e(tag, "MQTT开始连接1");
        mqttHelper.connect(Qos.QOS_TWO, true, new MqttCallback() {
            @Override
            public void connectionLost(Throwable cause) {
                Log.e(tag, "mqtt连接丢失");
                isMqttConnect = false;
            }

            @Override
            public void messageArrived(String topic, MqttMessage message) {
                //收到消息
                Log.e(tag, "收到消息 topic = " + topic + "\nmessage = " + message);
                setMqttData(message.toString());
            }

            @Override
            public void deliveryComplete(IMqttDeliveryToken token) {
                Log.e(tag, "deliveryComplete = " + token);
            }
        });
    }

    public void disConnect_mqtt() {
        if (mqttHelper != null && isMqttConnect) {
            String json = "{\"ctrl\":\"msg_shutdown\",\"ID\":" + SPUtils.getInstance().getString(Constants.DEVICE_ID, "") + "}";
            publish(json);
            mqttHelper.disconnect();
        }
    }

    /**
     * 电机开关
     *
     * @param message
     */
    public void publish(String message) {
        if (mqttHelper == null) {
            mqttHelper = new MQTTHelper(this);
        }
        if (!isMqttConnect) {
            return;
        }
        Log.e(tag, "msg = " + message + "\ntopic = " + Topic.TOPIC_SEND);
        mqttHelper.publish(Topic.TOPIC_MSG, message, Qos.QOS_TWO);
    }

    /**
     * MQTT消息处理
     *
     * @param message
     */
    private void setMqttData(String message) {
        if (TextUtils.isEmpty(message)) {
            return;
        }
        MqttData data = new Gson().fromJson(message, MqttData.class);
        if (data == null) {
            return;
        }
        if (data.getStatus() != 200) {
            return;
        }
        switch (data.getCtrl()) {
            case "msg_goods_update":
                //商品新增、编辑
//                if (data.getData() == null) {
//                    return;
//                }
//                for (int i = 0; i < data.getData().size(); i++) {
//                    MqttData.DataBean goodsData = data.getData().get(i);
//                    GoodsData dataSave = LitePal.where("goods_barcode = ?", goodsData.getGoods_barcode()).findFirst(GoodsData.class);
//                    if (dataSave == null) {
//                        //新增
//                        Log.e(tag, "新增");
//                        GoodsData dataAdd = new GoodsData();
//                        dataAdd.setGoods_id(goodsData.getGoods_id());
//                        dataAdd.setGoods_barcode(goodsData.getGoods_barcode());
//                        dataAdd.setGoods_name(goodsData.getGoods_name());
//                        dataAdd.setGoods_unit(goodsData.getGoods_unit());
//                        dataAdd.setGoods_sale_price(goodsData.getGoods_sale_price());
//                        dataAdd.setGoods_in_price(goodsData.getGoods_in_price());
//                        dataAdd.setGoodsChengType(goodsData.getGoodsChengType());
//                        dataAdd.setGoods_count(goodsData.getGoods_count());
//                        if (dataAdd.save()) {
//                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_LIST));
//                        }
//                    } else {
//                        //编辑
//                        Log.e(tag, "编辑");
//                        dataSave.setGoods_name(goodsData.getGoods_name());
//                        dataSave.setGoods_unit(goodsData.getGoods_unit());
//                        dataSave.setGoods_sale_price(goodsData.getGoods_sale_price());
//                        dataSave.setGoods_in_price(goodsData.getGoods_in_price());
//                        dataSave.setGoodsChengType(goodsData.getGoodsChengType());
//                        dataSave.setGoods_count(goodsData.getGoods_count());
//                        if (dataSave.save()) {
//                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_LIST));
//                        }
//                    }
//                }
                getGoodsList();
                break;
            case "msg_goods_delete":
                //商品删除
//                if (data.getData() == null) {
//                    return;
//                }
//                for (int i = 0; i < data.getData().size(); i++) {
//                    GoodsData goodsData = LitePal.where("goods_barcode = ?", data.getData().get(i).getGoods_barcode()).findFirst(GoodsData.class);
//                    if (goodsData != null) {
//                        goodsData.delete();
//                        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_LIST));
//                    }
//                }
                getGoodsList();
                break;
            case "msg_net_new":
                //新的网单
                break;
            default:
                break;
        }
    }

    /**
     * 全部商品列表
     */
    private void getGoodsList() {
        Log.e(tag, "同步商品");
        Map<String, Object> params = new HashMap<>();
        params.put("shop_unique", getShopUnique());
        params.put("today", 2);
        RXHttpUtil.requestByFormPostAsOriginalResponse((LifecycleOwner) AppManager.getInstance().currentActivity(),
                ZURL.getGoodsListPc(),
                params,
                new RequestListener<String>() {
                    @Override
                    public void success(String s) {
                        BaseGoodsData data = new Gson().fromJson(s, BaseGoodsData.class);
                        if (data == null) {
                            return;
                        }
                        if (data.getStatus() != 0) {
                            return;
                        }

                        if (LitePal.findFirst(GoodsData.class) != null) {
                            LitePal.deleteAll(GoodsData.class);
                        }
                        LitePal.saveAll(data.getData());
                        if (LitePal.saveAll(data.getData())) {
                            Log.e(tag, "商品列表数据保存成功");
                            EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.GOODS_LIST));
                        }
                    }

                    @Override
                    public void onError(String msg) {
                        Log.e(tag, "error = " + msg);
                    }
                });
    }

    /*************************mqtt-end***********************/

    /*********************串口start**********************/
    public boolean isSerialPortConnect;//串口是否连接

    /**
     * 串口连接
     */
    public void initSerialPort() {
        if (!TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_SERIAL_PORT_SCALE, ""))) {
            disConnectSerialPort();
            return;
        }
        disConnectSerialPort();
        //端口号
        String serialPort = "/dev/ttyS3";
        switch (SPUtils.getInstance().getInt(Constants.WEIGHT_SERIAL_PORT, 0)) {
            case 0:
                serialPort = "/dev/ttyS3";
                break;
        }
        //波特率
        int baudRate;
        switch (SPUtils.getInstance().getInt(Constants.BAUD, 2)) {
            case 0:
                baudRate = 2400;
                break;
            case 1:
                baudRate = 4800;
                break;
            case 3:
                baudRate = 115200;
                break;
            default:
                baudRate = 9600;
                break;
        }

        /**
         * 设置停止位、数据位、校验位
         */
        SerialUtils.getInstance().init(this, true, "TAG",
                50, 8, 0, 1);
        if (isSerialPortConnect) {
            disConnectSerialPort();
        } else {
            //多串口演示
            List<Driver> list = new ArrayList<>();
            list.add(new Driver(serialPort, String.valueOf(baudRate)));
            // 打开串口
            Log.e(tag, "打开的串口为：" + serialPort + " 波特率为：" + baudRate);
            SerialUtils.getInstance().manyOpenSerialPort(list);
        }
        /**
         * 数据监听状态以及打开状况
         */
        SerialUtils.getInstance().setmSerialPortDirectorListens(new SerialPortDirectorListens() {
            /**
             *  接收回调
             * @param bytes 接收到的数据
             * @param serialPortEnum  串口类型
             */
            @Override
            public void onDataReceived(byte[] bytes, SerialPortEnum serialPortEnum) {
                //数据接收
                isSerialPortConnect = true;
                startTimerSerialPort();
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(bytes, Constants.SERIALPORT));
            }

            /**
             *  发送回调
             * @param bytes 发送的数据
             * @param serialPortEnum  串口类型
             */
            @Override
            public void onDataSent(byte[] bytes, SerialPortEnum serialPortEnum) {
                //数据发送
                Log.e(tag, "串口数据发送回调 = " + HexUtils.bytes2HexStr(bytes));
            }

            /**
             * 串口打开回调
             * @param serialPortEnum  串口类型
             * @param device  串口号
             * @param status 打开状态
             */
            @Override
            public void openState(SerialPortEnum serialPortEnum, File device, SerialStatus status) {
                Log.e(tag, "串口打开状态：" + device.getName() + "---打开状态：" + status.name());
                switch (serialPortEnum) {
                    case SERIAL_ONE:
                        switch (status) {
                            case SUCCESS_OPENED:
                                Log.e(tag, "串口打开成功");
                                break;
                            case NO_READ_WRITE_PERMISSION:
                                Log.e(tag, "串口打开失败 没有读写权限");
                                isSerialPortConnect = false;
                                break;
                            case OPEN_FAIL:
                                Log.e(tag, "串口打开失败");
                                isSerialPortConnect = false;
                                break;
                        }
                        break;
                    case SERIAL_TWO:
                        Log.e(tag, "根据实际多串口场景演示");
                        break;
                }
            }
        });
    }

    /**
     * 关闭串口
     */
    public void disConnectSerialPort() {
        if (isSerialPortConnect) {
            SerialUtils.getInstance().serialPortClose();
        }
    }

    /**
     * 发送
     *
     * @param sendBytes
     */
    public void publish(byte[] sendBytes) {
        if (isSerialPortConnect) {
            boolean isPublish = SerialUtils.getInstance().sendData(SerialPortEnum.SERIAL_ONE, sendBytes);
        }
    }

    private Timer timerSerialPort;
    private TimerTask timerTaskSerialPort;

    //倒计时开始
    private void startTimerSerialPort() {
        cancelTimerSerialPort();
        if (timerSerialPort == null) {
            timerSerialPort = new Timer();
        }
        timerTaskSerialPort = new TimerTask() {
            @Override
            public void run() {
                isSerialPortConnect = false;
                EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.SERIALPORT));
            }
        };
        timerSerialPort.schedule(timerTaskSerialPort, Constants.SERIAL_PORT_DATA_TIME);
    }

    //倒计时结束
    private void cancelTimerSerialPort() {
        if (timerSerialPort != null) {
            timerSerialPort.cancel();
            timerSerialPort = null;
        }
        if (timerTaskSerialPort != null) {
            timerTaskSerialPort.cancel();
            timerTaskSerialPort = null;
        }
    }


    /*********************串口end**********************/

    /*********************XPrinter小票打印机start**********************/
    public IDeviceConnection curConnect;
    public boolean isReceiptPrinterConnect;//小票打印机是否连接
    private final IConnectListener connectListener = (code, connInfo, msg) -> {
        Log.e(tag, "code = " + code + " connInfo = " + connInfo + " msg = " + msg);
        EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.XPRINTER, code, connInfo, msg));
        //小票打印机状态监听 1.连接成功 2.连接失败 3.发送失败 4.连接中断 5.USB设备已连上 6.USB设备已断开
        //code = 2
        // connInfo = ***************
        // msg = java.net.ConnectException: failed to connect to /*************** (port 9100) from /*************** (port 44556) after 1000ms: isConnected failed: ECONNREFUSED (Connection refused)
        switch (code) {
            case 1:
            case 5:
                isReceiptPrinterConnect = true;
                break;
            case 3:
                break;
            default:
                isReceiptPrinterConnect = false;
                break;
        }
    };

    /**
     * 连接小票打印机
     */
    public void connectReceiptPrinter() {
        //小票打印机连接方式 1.蓝牙 2.端口 3.USB 4.WIFI
        int receiptPrintType = SPUtils.getInstance().getInt(Constants.RECEIPT_PRINTER_CONNECT_TYPE, 3);
        switch (receiptPrintType) {
            case 3:
                List<String> list = POSConnect.getUsbDevices(this);
                if (list.size() < 1) {
                    EventBusManager.getInstance().getGlobaEventBus().post(new EventData(Constants.XPRINTER, 2, "", ""));
                    return;
                }
                connectUSB(list.get(0));
                break;
        }
    }

    /**
     * 连接小票打印机（USB）
     *
     * @param pathName
     */
    public void connectUSB(String pathName) {
        if (curConnect != null) {
            curConnect.close();
        }
        curConnect = POSConnect.createDevice(POSConnect.DEVICE_TYPE_USB);
        curConnect.connect(pathName, connectListener);
    }

    /**
     * 连接小票打印机（网络）
     *
     * @param ipAddress
     */
    public void connectNet(String ipAddress) {
        if (curConnect != null) {
            curConnect.close();
        }
        curConnect = POSConnect.createDevice(POSConnect.DEVICE_TYPE_ETHERNET);
        curConnect.connect(ipAddress, connectListener);
    }

    /**
     * 连接小票打印机（蓝牙）
     *
     * @param macAddress
     */
    public void connectBt(String macAddress) {
        if (curConnect != null) {
            curConnect.close();
        }
        curConnect = POSConnect.createDevice(POSConnect.DEVICE_TYPE_BLUETOOTH);
        curConnect.connect(macAddress, connectListener);
    }

    /**
     * 连接小票打印机（MAC地址）
     *
     * @param macAddress
     */
    public void connectMAC(String macAddress) {
        if (curConnect != null) {
            curConnect.close();
        }
        curConnect = POSConnect.connectMac(macAddress, connectListener);
    }

    /**
     * 连接小票打印机（串口）
     *
     * @param port     串口名称
     * @param boudrate 波特率
     */
    public void connectSerial(String port, String boudrate) {
        if (curConnect != null) {
            curConnect.close();
        }
        curConnect = POSConnect.createDevice(POSConnect.DEVICE_TYPE_SERIAL);
        curConnect.connect(port + "," + boudrate, connectListener);
    }

    /**
     * 关闭小票打印机
     */
    public void disConnectXPrinter() {
        if (curConnect != null) {
            curConnect.close();
        }
    }
    /*********************XPrinter小票打印机end**********************/
}
