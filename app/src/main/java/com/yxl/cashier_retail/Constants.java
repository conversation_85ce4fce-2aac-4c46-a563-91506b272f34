package com.yxl.cashier_retail;

/**
 * SmartCar 所需要的常量都在这里定义使用
 *
 * <AUTHOR>
 */
public interface Constants {
    int limit = 20;
    int SUCCESS_CODE = 1;//接口请求成功code

    String MOBILE = "400-7088-365";//客服电话

    int SEARCH_DELAY_MILLIS = 1000;//搜索延迟时间（ms）
    int MAX_MOBILE_LENGTH = 11;//手机号最大长度
    int MAX_MONEY_LENGTH = 9;//金额最大长度
    int CHART_ANIMATE_TIME = 1400;//chart统计图动画时间
    int SERIAL_PORT_DATA_TIME = 5000;//串口秤接收数据间隔（ms）注：间隔大于改时间即表示串口断开连接

    String DEVICE_ID = "device_id";//设备唯一标识符
    String LANGUAGE = "language";//多语言-语言
    String COUNTRY = "country";//多语言-国家
    String LOGIN_ID = "login_id";//登录id
    String REMEMBER_ACCOUNT = "remember_account";//登录账号
    String REMEMBER_PWD = "remember_pwd";//登录密码
    String IS_REMEMBER = "is_remember";//是否记住密码
    String IS_START_UP = "is_start_up";//是否开机自启动
    String IS_VERIFY = "is_verify";//是否条码校验
    String IS_PRINT = "is_print";//是否打印小票
    String ZERO = "zero";//抹零
    String MQTT = "mqtt";//MQTT
    String SERIALPORT = "serialPort";//串口
    String POINTS_RATIO = "points_ratio";//积分比例
    String IS_USE_POINTS = "is_use_points";//是否使用积分
    String LESS_THAN_ONE_TO_POINTS = "less_than_one_to_points";//不足1元计算方式
    String RATIO_NO_BARCODE = "ratio_no_barcode";//无码商品利润比
    String RATIO_BARCODE = "ratio_barcode";//普通商品利润比
    String IS_USE_CATERING_RECEIPT = "is_use_catering_receipt";//是否使用餐饮小票
    String DEFAULT_PRINT_COUNT = "default_print_count";//默认打印数量
    String RECEIPT_TIPS = "receipt_tips";//小票：温馨提示
    String RECEIPT_REMARKS = "receipt_remarks";//小票：备注
    String IS_USE_SERIAL_PORT_SCALE = "is_use_serial_port_scale";//启用串口秤
    String BAUD = "BAUD";//波特率
    String DATA_PARSING_TYPE = "data_parsing_type";//数据解析方式
    String IS_USE_SECONDARY_SCREEN = "is_use_secondary_screen";//启用副屏
    String IS_USE_BEFORE_PAY_BOX = "is_use_before_pay_box";//支付前弹钱箱
    String IS_USE_PRINT_AUTO_ORDER = "is_use_print_auto_order";//自动接单打印
    String IS_USE_PRINT_AUTO_REFUND = "is_use_print_auto_refund";//自动打印退款单
    String GOODS_WEIGHT_BARCODE_FIRST_TWO = "goods_weight_barcode_first_two";//称重商品的前两位
    String GOODS_COMMON_BARCODE_FIRST_TWO = "goods_common_barcode_first_two";//普通商品的前两位
    String IS_USE_SERIAL_PORT_WEIGHING_SCALE = "is_use_serial_port_weighing_scale";//串口市斤秤
    String IS_USE_GOODS_NO_BARCODE_ADD_CART = "is_use_goods_no_barcode_add_cart";//未创建无码商品直接添加购物车
    String IS_USE_SHOW_GOODS_NO_BARCODE = "is_use_show_goods_no_barcode";//显示“无码商品/称重”
    String IS_USE_CHANGE_GOODS_NO_BARCODE_AND_WEIGHT = "is_use_change_goods_no_barcode_and_weight";//交换"无码商品/称重"位置
    String IS_USE_HANG_PRINT_RECEIPT = "is_use_hang_print_receipt";//挂单打印小票
    String IS_USE_HOME_INPUT_DIGIT_CREATE_GOODS_NO_BARCODE = "is_home_input_digit_create_goods_no_barcode";//首页输入数字创建无码商品
    String IS_SHOW_STOCK = "is_show_stock";//是否展示库存
    String IS_EXIT_CLOSE = "is_exit_close";//退出时关闭计算机
    String IS_USE_POS = "is_user_pos";//启用pos收银
    String POS_ID = "pos_id";//pos设备id
    /*系统设置-声音设置start*/
    String IS_VOICE_HOME = "is_voice_home";//播放首页提示音
    String IS_VOICE_KEYBOARD = "is_voice_keyboard";//播放键盘音
    String IS_VOICE_PAYMENT_ONLINE = "is_voice_payment_online";//播报在线支付
    String IS_VOICE_PAYMENT_CASH = "is_voice_payment_cash";//播报现金支付
    String IS_VOICE_PAYMENT_MEMBER = "is_voice_payment_member";//播报会员支付
    String IS_VOICE_PAYMENT_OFFLINE = "is_voice_payment_offline";//播报线下支付
    String IS_VOICE_PAYMENT_APPLET = "is_voice_payment_applet";//播报小程序支付
    String IS_VOICE_REFUND_APPLET = "is_voice_refund_applet";//播报小程序退款
    String IS_VOICE_ORDER_APPLET = "is_voice_order_applet";//播报小程序接单
    String IS_VOICE_PAYMENT_COMBINATION = "is_voice_payment_combination";//播报组合支付
    /*系统设置-声音设置end*/

    String SHOP_INFO = "shop_info";//店铺信息
    String CATE_LIST = "cate_list";//商品分类列表
    String GOODS_LIST = "goods_list";//商品列表
    String GOODS_INFO = "goods_info";//商品详情
    String RESTOCK_LIST = "restock_list";//补货计划列表
    String SUPPLIER_CATE_LIST = "supplier_cate_list";//供货商分类列表
    String SUPPLIER_LIST = "supplier_list";//供货商列表
    String PURCHASE_LIST = "purchase_list";//购销单列表
    String PURCHASE_INFO = "purchase_info";//购销单详情
    String SETTLEMENT_LIST = "settlement_list";//结款记录列表
    String SETTLEMENT_INFO = "settlement_info";//结款记录详情
    String STATISTICS_LIST = "statistics_list";//统计列表
    String ORDER_LIST = "order_list";//订单列表
    String ORDER_LIST_UPDATE = "order_list_update";//订单列表局部刷新
    String ORDER_INFO = "order_info";//订单详情
    String ORDER_COUNT = "order_count";//订单数量
    String REFUND_LIST_UPDATE = "refund_list_update";//退款列表局部刷新
    String REFUND_LIST = "refund_list";//退款列表
    String REFUND_INFO = "refund_info";//退款详情
    String REFUND_COUNT = "refund_count";//退款订单数量
    String ORDER_OFFLINE_SUBMIT = "order_offline_submit";//离线订单提交
    String XPRINTER = "xprinter";//xPrinter小票打印机
    String RECEIPT_PRINTER_CONNECT_TYPE = "receipt_printer_connect_type";//小票打印机连接方式
    String WEIGHT_SERIAL_PORT = "weight_serial_port";//称重设置：端口号

    int PERMISSION_OVERLAY = 0x01;//权限：副屏
    int PERMISSION_CAMERA = 0x02;//权限：摄像头
    int PERMISSION_READ = 0x03;//权限：读写
    int PERMISSION_BLUETOOTH = 0x04;//权限：蓝牙
    int PERMISSION_PHONE = 0x05;
    int RESTOCK = 0x06;//自采补货
    int SELECT = 0x07;//选择
    int HANG_ORDER = 0x08;//挂单
}
