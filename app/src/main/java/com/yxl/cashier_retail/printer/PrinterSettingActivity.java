package com.yxl.cashier_retail.printer;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.AlertDialog;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.gprinter.bean.PrinterDevices;
import com.gprinter.utils.CallbackListener;
import com.gprinter.utils.Command;
import com.gprinter.utils.ConnMethod;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.base.BaseActivity;
import com.yxl.cashier_retail.databinding.ActivityPrinterBluetoothBinding;
import com.yxl.cashier_retail.printer.jiabo.Printer;
import com.yxl.cashier_retail.printer.jiabo.SharedPreferencesUtil;
import com.yxl.cashier_retail.printer.jiabo.bean.PrinterData;
import com.yxl.cashier_retail.utils.PermissionUtils;
import com.yxl.commonlibrary.utils.SystemUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * Describe:连接蓝牙打印机
 * Created by jingang on 2024/4/18
 */
@SuppressLint({"NonConstantResourceId", "SetTextI18n", "MissingPermission"})
public class PrinterSettingActivity extends BaseActivity<ActivityPrinterBluetoothBinding> implements CallbackListener {
    @BindView(R.id.tvTitle)
    TextView tvTitle;
    @BindView(R.id.tvPrinterTitle)
    TextView tvPrinterTitle;
    @BindView(R.id.tvPrinterName)
    TextView tvPrinterName;
    @BindView(R.id.ivPrinterImg)
    ImageView ivPrinterImg;
    @BindView(R.id.recyclerView)
    RecyclerView recyclerView;
    @BindView(R.id.ivDirection0)
    ImageView ivDirection0;
    @BindView(R.id.ivDirection1)
    ImageView ivDirection1;
    @BindView(R.id.linTag0)
    LinearLayout linTag0;
    @BindView(R.id.cbTag0)
    CheckBox cbTag0;
    @BindView(R.id.cbTag1)
    CheckBox cbTag1;
    @BindView(R.id.tvConfirm)
    TextView tvConfirm;

    //打印机列表
    private PrinterListAdapter mAdapter;
    private List<PrinterData> dataList = new ArrayList<>();

    private BluetoothAdapter mBluetoothAdapter;

    public static final int REQUEST_ENABLE_BT = 2;
    public static final int REQUEST_ENABLE_GPS = 3;
    private SharedPreferencesUtil sharedPreferencesUtil;

    private int printDirection,//标签方向 0.正向 1.反向
            printType;//标签模版 1.无店铺名称 2.有店铺名称 3.优乐购

    @Override
    protected ActivityPrinterBluetoothBinding getViewBinding() {
        return ActivityPrinterBluetoothBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void setListener() {
        sharedPreferencesUtil = SharedPreferencesUtil.getInstantiation(this);
        setUI();
        setAdapter();
        initPermission();
    }

    @Override
    protected void initData() {

    }

    @OnClick({R.id.ivBack, R.id.linPrinter, R.id.linTag0, R.id.linDirection0, R.id.linDirection1, R.id.linTag1, R.id.tvConfirm})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ivBack:
                onBackPressed();
                break;
            case R.id.linPrinter:
                //搜索
                searchBluetoothJC();
                break;
            case R.id.linDirection0:
                //标签方向（正向）
                if (printDirection != 0) {
//                    sharedPreferencesUtil.putInt(0, Constants.PRINT_DIRECTION);
                    setUI();
                }
                break;
            case R.id.linDirection1:
                //标签方向（反向）
                if (printDirection != 1) {
//                    sharedPreferencesUtil.putInt(1, Constants.PRINT_DIRECTION);
                    setUI();
                }
                break;
            case R.id.linTag0:
                //标签纸样式0
                if (printType != 1) {
//                    sharedPreferencesUtil.putInt(1, Constants.PRINT_TYPE);
                    setUI();
                }
                break;
            case R.id.linTag1:
                //标签纸样式1
                if (printType != 2) {
//                    sharedPreferencesUtil.putInt(2, Constants.PRINT_TYPE);
                    setUI();
                }
                break;
            case R.id.tvConfirm:
                //保存设置并打印
//                setResult(Constants.BLUETOOTH, new Intent());
//                this.finish();
                break;
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mBluetoothAdapter != null) {
            mBluetoothAdapter.cancelDiscovery();
        }
        if (receiver != null) {
            try {
                unregisterReceiver(receiver);
            } catch (Exception ignored) {

            }
        }
    }

    @Override
    public void onConnecting() {
        Log.e(tag, "连接中");
        tvPrinterTitle.setText("设备连接中");
        tvPrinterTitle.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        tvPrinterName.setText("连接中");
        ivPrinterImg.setImageResource(R.mipmap.ic_printer001);
        tvConfirm.setVisibility(View.GONE);
        if (dataList.size() > pos) {
            dataList.get(pos).setState(1);
            mAdapter.notifyItemChanged(pos);
        }
    }

    @Override
    public void onCheckCommand() {
        Log.e(tag, "onCheckCommand");
    }

    @Override
    public void onSuccess(PrinterDevices printerDevices) {
        //连接成功
        Log.e(tag, "连接成功 = " + printerDevices.getMacAddress());
        tvPrinterTitle.setText("设备已连接");
        tvPrinterTitle.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_333));
        tvPrinterName.setText("蓝牙打印机已连接）" + printerDevices.getBlueName() + "）");
        ivPrinterImg.setImageResource(R.mipmap.ic_printer001);
        tvConfirm.setVisibility(View.VISIBLE);
        if (dataList.size() > pos) {
            dataList.get(pos).setState(0);
            mAdapter.notifyItemChanged(pos);
        }
    }

    @Override
    public void onReceive(byte[] bytes) {
        Log.e(tag, "onReceive = " + bytes);
    }

    @Override
    public void onFailure() {
        //连接失败
        Log.e(tag, "连接失败");
        tvPrinterTitle.setText("设备未连接");
        tvPrinterTitle.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        tvPrinterName.setText("请连接蓝牙打印机");
        ivPrinterImg.setImageResource(R.mipmap.ic_printer001);
        tvConfirm.setVisibility(View.GONE);
        if (dataList.size() > pos) {
            dataList.get(pos).setState(2);
            mAdapter.notifyItemChanged(pos);
        }
    }

    @Override
    public void onDisconnect() {
        //断开连接
        Log.e(tag, "断开连接");
        tvPrinterTitle.setText("设备断开连接");
        tvPrinterTitle.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        tvPrinterName.setText("请连接蓝牙打印机");
        ivPrinterImg.setImageResource(R.mipmap.ic_printer001);
        tvConfirm.setVisibility(View.GONE);
        if (dataList.size() > pos) {
            dataList.get(pos).setState(2);
            mAdapter.notifyItemChanged(pos);
        }
    }

    /**
     * 更新UI
     */
    private void setUI() {
//        printDirection = sharedPreferencesUtil.getInt(0, Constants.PRINT_DIRECTION);
//        printType = sharedPreferencesUtil.getInt(1, Constants.PRINT_TYPE);

        //标签方向
        if (printDirection == 0) {
            ivDirection0.setImageResource(R.mipmap.ic_chosen001);
            ivDirection1.setImageResource(R.mipmap.ic_chose001);
        } else {
            ivDirection0.setImageResource(R.mipmap.ic_chose001);
            ivDirection1.setImageResource(R.mipmap.ic_chosen001);
        }

        linTag0.setVisibility(View.VISIBLE);
        //打印模版
        switch (printType) {
            case 1:
                cbTag0.setChecked(true);
                cbTag1.setChecked(false);
                break;
            case 2:
                cbTag0.setChecked(false);
                cbTag1.setChecked(true);
                break;
            default:
//                sharedPreferencesUtil.putInt(1, Constants.PRINT_TYPE);
                cbTag0.setChecked(true);
                cbTag1.setChecked(false);
                break;
        }
    }

    /**
     * 设置适配器
     */
    private void setAdapter() {
        mAdapter = new PrinterListAdapter(this);
        recyclerView.setAdapter(mAdapter);
        mAdapter.setOnItemClickListener((view, position) -> {
            pos = position;
            if (mBluetoothAdapter.isDiscovering()) {
                mBluetoothAdapter.cancelDiscovery();
            }
            connectBluetooth();
        });
    }

    /**
     * 动态申请权限
     */
    private void initPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (PermissionUtils.checkPermissionsGroup(this, 6)) {
                initBluetooth();
            } else {
                PermissionUtils.requestPermissions(this, Constants.PERMISSION_BLUETOOTH, 6);
            }
        } else {
            if (PermissionUtils.checkPermissionsGroup(this, 1)) {
                initBluetooth();
            } else {
                PermissionUtils.requestPermissions(this, Constants.PERMISSION_BLUETOOTH, 1);
            }
        }
    }

    /**
     * 初始化蓝牙
     */
    private void initBluetooth() {
        mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        if (mBluetoothAdapter == null) {
            showToast(1,"设备不支持蓝牙");
            finish();
            return;
        }
        if (mBluetoothAdapter.isEnabled()) {
            if (SystemUtils.hasGPSDevice(this)) {
                if (SystemUtils.isLocationEnabled(this)) {
                    registerReceiver();
                } else {
                    //添加"Yes"按钮
                    new AlertDialog.Builder(this)
                            .setTitle("提示")
                            .setMessage("安卓8.0系统搜索蓝牙需开启GPS定位功能！\\n请选择是否开启")
                            .setIcon(R.mipmap.ic_logo)
                            .setPositiveButton(getString(R.string.confirm), (dialogInterface, i) -> {
                                Intent intent = new Intent();
                                intent.setAction(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                startActivityForResult(intent, REQUEST_ENABLE_GPS);
                            }).create().show();
                }
            } else {
                registerReceiver();
            }
        } else {
            startActivityForResult(new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE), REQUEST_ENABLE_BT);
        }
    }

    /**
     * 注册广播
     */
    private void registerReceiver() {
        //注册广播
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(BluetoothDevice.ACTION_FOUND);
        intentFilter.addAction(BluetoothAdapter.ACTION_DISCOVERY_STARTED);
        intentFilter.addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED);
        intentFilter.addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED);
        intentFilter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED);
        intentFilter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED);
        intentFilter.addAction(BluetoothDevice.ACTION_PAIRING_REQUEST);
        registerReceiver(receiver, intentFilter);
        Log.d(tag, "初始化: 注册完成");
        //注册线程池
        ThreadFactory threadFactory = runnable -> {
            Thread thread = new Thread(runnable);
            thread.setName("connect_activity_pool_%d");
            return thread;
        };
        executorService = new ThreadPoolExecutor(1, 1, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingDeque<>(1024), threadFactory, new ThreadPoolExecutor.AbortPolicy());
        searchBluetoothJC();
    }

    private ExecutorService executorService;
    private int pos;

    private final BroadcastReceiver receiver = new BroadcastReceiver() {
        @SuppressLint("MissingPermission")
        @Override
        public void onReceive(Context context, Intent intent) {
            switch (intent.getAction()) {
                case BluetoothDevice.ACTION_FOUND:
                    //蓝牙发现
                    BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                    if (device == null) {
                        return;
                    }
                    Log.e(tag, "device = " + device.getName());
                    if (TextUtils.isEmpty(device.getName())) {
                        return;
                    }
                    if (!device.getName().startsWith("GP")) {
                        return;
                    }

                    PrinterData data = new PrinterData();
                    data.setName(device.getName());
                    data.setAddress(device.getAddress());
                    data.setState(device.getBondState());

                    for (PrinterData p : dataList) {
                        if (p.getAddress().equals(data.getAddress())) {//防止重复添加
                            return;
                        }
                    }
                    dataList.add(0, data);
                    mAdapter.setDataList(dataList);
                    break;
                case BluetoothAdapter.ACTION_DISCOVERY_STARTED:
                    Log.e(tag, "开始搜索");
                    break;
                case BluetoothAdapter.ACTION_DISCOVERY_FINISHED:
                    Log.e(tag, "搜索结束");
                    break;
                case BluetoothDevice.ACTION_BOND_STATE_CHANGED:
                    //配对状态改变
                    int state = intent.getIntExtra(BluetoothDevice.EXTRA_BOND_STATE, -1);
                    Log.d(tag, "测试:配对状态改变:0 " + state);
                    if (dataList.size() > pos) {
                        dataList.get(pos).setState(state);
                        mAdapter.notifyItemChanged(pos);
                        if (state == 12 && !isQuicklyClick()) {
                            connectBluetooth();
                        }
                    }
                    break;
                default:
                    break;
            }
        }
    };

    /**
     * 搜索设备
     */
    private void searchBluetoothJC() {
        Log.e(tag, "开始搜索设备");
        if (!mBluetoothAdapter.isEnabled()) {
            showToast(1,"蓝牙未开启");
        }
        dataList.clear();
        if (mBluetoothAdapter.isDiscovering()) {
            if (mBluetoothAdapter.cancelDiscovery()) {
                executorService.execute(() -> {
                    try {
                        //取消后等待1s后再次搜索
                        Thread.sleep(1000);
                        mBluetoothAdapter.startDiscovery();
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                });
            }
        } else {
            mBluetoothAdapter.startDiscovery();
        }
    }

    /**
     * 连接设备
     */
    private void connectBluetooth() {
        if (dataList.size() <= pos) {
            return;
        }
        tvPrinterTitle.setText("设备连接中");
        tvPrinterTitle.setTextColor(getResources().getColor(com.yxl.commonlibrary.R.color.color_999));
        tvPrinterName.setText("连接中");
        PrinterDevices blueTooth = new PrinterDevices.Build()
                .setContext(this)
                .setConnMethod(ConnMethod.BLUETOOTH)
                .setMacAddress(dataList.get(pos).getAddress())
                .setCommand(Command.TSC)
                .setCallbackListener(this)
                .build();
        Printer.connect(blueTooth);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        boolean hasPermissionDismiss = false;
        switch (requestCode) {
            case Constants.PERMISSION_BLUETOOTH:
                for (int grantResult : grantResults) {
                    if (grantResult == -1) {
                        hasPermissionDismiss = true;
                        break;
                    }
                }
                //如果有权限没有被允许
                if (hasPermissionDismiss) {
                    showToast(1,"因权限未开启，该功能无法使用，请去设置中开启。");
                } else {
                    initBluetooth();
                }
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case REQUEST_ENABLE_BT:
                if (resultCode == Activity.RESULT_OK) {
                    registerReceiver();
                } else {
                    showToast(1,"蓝牙未开启");
                    finish();
                }
                break;
            case REQUEST_ENABLE_GPS:
                registerReceiver();
                break;
        }
    }

}
