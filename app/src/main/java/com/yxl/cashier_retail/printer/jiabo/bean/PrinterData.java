package com.yxl.cashier_retail.printer.jiabo.bean;

import java.io.Serializable;

/**
 * Describe:打印机（实体类）
 * Created by jingang on 2024/4/16
 */
public class PrinterData implements Serializable {
    private String name;
    private String address;
    private int state;//状态 0.连接成功 10.未配对 11.配对中 12.已配对

    public PrinterData() {
    }

    public PrinterData(String name, String address, int state) {
        this.name = name;
        this.address = address;
        this.state = state;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }
}
