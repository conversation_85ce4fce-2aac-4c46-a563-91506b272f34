package com.yxl.cashier_retail.mqtt;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;

import com.blankj.utilcode.util.DeviceUtils;
import com.blankj.utilcode.util.LogUtils;
import com.itfitness.mqttlibrary.MqttAndroidClient;

import org.eclipse.paho.client.mqttv3.IMqttActionListener;
import org.eclipse.paho.client.mqttv3.IMqttToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;

import java.util.UUID;


/**
 * Describe:
 * Created by jingang on 2023/4/27
 */
public class MQTTHelper {
    private MqttAndroidClient mqttClient;
    private MqttConnectOptions connectOptions;
    private IMqttActionListener mqttActionListener;
    private MyMqttActionListener myMqttActionListener;
    private String mqttUrl = "tcp://**************:1883";
    //        private String mqttUrl = "tcp://mqtt.allscm.net";
    private String macAddress;

    public String getMacAddress() {
        return macAddress;
    }

    public void setMyMqttActionListener(MyMqttActionListener myMqttActionListener) {
        this.myMqttActionListener = myMqttActionListener;
    }

    public MQTTHelper(Context context) {
        init(context);
    }

    private void init(Context context) {
        macAddress = getLocalMacAddress(context);
        LogUtils.e("设备ID", macAddress);
        mqttClient = new MqttAndroidClient(context, mqttUrl, macAddress);
        connectOptions = new MqttConnectOptions();
        connectOptions.setAutomaticReconnect(true);
        connectOptions.setCleanSession(false);
        connectOptions.setConnectionTimeout(30);
        connectOptions.setKeepAliveInterval(10);
        connectOptions.setUserName("name");
        connectOptions.setPassword("pass".toCharArray());
    }

    /**
     * 获得mac
     *
     * @return
     */
    @SuppressLint("MissingPermission")
    private String getLocalMacAddress(Context context) {
        String cacheMac = SharePreferenceUtil.getSharedStringData(context, SPK.SPK_MAC);

        if (!TextUtils.isEmpty(cacheMac)) {
            return cacheMac;
        }
        String mac = DeviceUtils.getMacAddress();
        if (mac == null || TextUtils.isEmpty(mac) || TextUtils.equals(mac, "02:00:00:00:00:00")) {
            return UUID.randomUUID().toString();
        }
        SharePreferenceUtil.setSharedStringData(context, SPK.SPK_MAC, mac);
        return mac;
    }

    /**
     * 连接
     *
     * @param mqttCallback 接到订阅的消息的回调
     * @param isFailRetry  失败是否重新连接
     */
    public void connect(int qos, boolean isFailRetry, MqttCallback mqttCallback) {
        LogUtils.eTag("连接", "连接000");
        mqttClient.setCallback(mqttCallback);
        LogUtils.eTag("连接", "连接111 = "+mqttActionListener);
//        if (mqttActionListener == null) {
//            mqttActionListener = new IMqttActionListener() {
//                @Override
//                public void onSuccess(IMqttToken asyncActionToken) {
//                    LogUtils.eTag("连接", "连接成功");
//                    try {
//                        mqttClient.subscribe("win_qt_cash_" + macAddress, qos);
//                        if (myMqttActionListener != null) {
//                            myMqttActionListener.onSuccess(asyncActionToken);
//                        }
//                    } catch (MqttException e) {
//                        e.printStackTrace();
//                    }
//                }
//
//                @Override
//                public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
//                    //失败重连
//                    LogUtils.eTag("连接", "连接失败重试" + exception.getMessage());
////                    if (isFailRetry){
////                        mqttClient.connect(connectOptions,null,mqttActionListener)
////                    }
//                    if (myMqttActionListener != null) {

//                        myMqttActionListener.onFailure(asyncActionToken);
//                    }
//                }
//            };
//            try {
//                mqttClient.connect(connectOptions, null, mqttActionListener);
//            } catch (MqttException e) {
//                e.printStackTrace();
//                LogUtils.eTag("连接", "连接失败重试 e = " + e.getMessage());
//            }
//        }
        mqttActionListener = new IMqttActionListener() {
            @Override
            public void onSuccess(IMqttToken asyncActionToken) {
                LogUtils.eTag("连接", "连接成功");
                try {
                    mqttClient.subscribe("win_qt_cash_" + macAddress, qos);
                    if (myMqttActionListener != null) {
                        myMqttActionListener.onSuccess(asyncActionToken);
                    }
                } catch (MqttException e) {
                    e.printStackTrace();
                }
            }

            @Override
            public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                //失败重连
                LogUtils.eTag("连接", "连接失败重试" + exception.getMessage());
//                    if (isFailRetry){
//                        mqttClient.connect(connectOptions,null,mqttActionListener)
//                    }
                if (myMqttActionListener != null) {
                    myMqttActionListener.onFailure(asyncActionToken);
                }
            }
        };
        try {
            mqttClient.connect(connectOptions, null, mqttActionListener);
        } catch (MqttException e) {
            e.printStackTrace();
            LogUtils.eTag("连接", "连接失败重试 e = " + e.getMessage());
        }
        LogUtils.eTag("连接", "连接222");
    }

    /**
     * 订阅
     */
    public void subscribe(String topic, int qos) {
        try {
            mqttClient.subscribe(topic, qos);
        } catch (MqttException e) {
            e.printStackTrace();
        }
    }

    /**
     * 发布
     */
    public void publish(String topic, String message, int qos) {
        MqttMessage msg = new MqttMessage();
        msg.setRetained(false);
        msg.setPayload(message.getBytes());
        msg.setQos(qos);
        try {
            mqttClient.publish(topic, msg);
        } catch (MqttException e) {
            e.printStackTrace();
        }
    }

    /**
     * 断开连接
     */
    public void disconnect() {
        try {
            if (mqttClient != null) {
                mqttClient.disconnect();
            }
        } catch (MqttException e) {
            e.printStackTrace();
        }
    }

    /**
     * 重连
     */
    public void reconnect() {
        mqttClient.reconnect();
    }

    public interface MyMqttActionListener {
        void onSuccess(IMqttToken asyncActionToken);

        void onFailure(IMqttToken asyncActionToken);
    }

}
