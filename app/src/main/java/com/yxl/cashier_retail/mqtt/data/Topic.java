package com.yxl.cashier_retail.mqtt.data;


import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;

/**
 * Describe:
 * Created by jingang on 2023/4/27
 */
public class Topic {
    public static final String TOPIC_MSG = "win_qt_cash_1.0";
    //    public static final String TOPIC_SEND = "win_qt_cash_" + SystemUtils.getDeviceId(BaseApplication.getInstance());
    public static final String TOPIC_SEND = "win_qt_cash_" + SPUtils.getInstance().getString(Constants.DEVICE_ID, "");
}
