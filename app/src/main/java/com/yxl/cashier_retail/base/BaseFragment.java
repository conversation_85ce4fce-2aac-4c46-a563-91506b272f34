package com.yxl.cashier_retail.base;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.viewbinding.ViewBinding;

import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.utils.ToastUtils;
import com.yxl.commonlibrary.LoadingDialog;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.bean.LoginData;

/**
 * desc : 基类（每个fragment 继承BaseFragment）
 */
public abstract class BaseFragment<T extends ViewBinding> extends Fragment {
    protected T mBinding;
    protected Context mContext;
    private LoadingDialog dialog;
    private long currentTime;
    public String tag = "零售版金圈收银";
    public int page = 1,
            max;//offset

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        mContext = context;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        mBinding = getViewBinding();
        return mBinding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        setListener();
        initData();
    }

    /**
     * 获取加载布局文件
     */
    protected abstract T getViewBinding();

    /**
     * 设置事件监听
     */
    protected abstract void setListener();

    /**
     * 初始化数据
     */
    protected abstract void initData();

    /**
     * 显示加载等待框
     */
    public void showDialog() {
        if (dialog == null) {
            LoadingDialog.Builder loadBuilder = new LoadingDialog.Builder(getActivity())
                    .setMessage(getRstr(R.string.loading))
                    .setCancelable(true)
                    .setCancelOutside(true);
            dialog = loadBuilder.create();
        }
        dialog.showDialog();
    }

    /**
     * 隐藏加载等待框
     */
    public void hideDialog() {
        if (dialog != null) {
            dialog.hideDialog();
        }
    }

    /**
     * 定义Toast的全局调用方法
     */
    @SuppressLint("InflateParams")
    protected void showToast(int type, String msg) {
        ToastUtils.getInstance(mContext, type, msg, Gravity.TOP).showMessage();
//        ToastUtils.showCustomToast(mContext, type, msg, Gravity.TOP);
    }

    /**
     * String转化
     */
    public String getRstr(int str) {
        return getResources().getString(str);
    }

    /**
     * 跳转到指定的activity
     *
     * @param clazz 目标activity
     */
    public void goToActivity(Class clazz) {
        startActivity(new Intent(getActivity(), clazz));
    }

    /**
     * 跳转到制定activity（带result）
     *
     * @param clazz
     * @param code
     */
    public void goToActivityForResult(Class clazz, int code) {
        startActivityForResult(new Intent(getActivity(), clazz), code);
    }

    /**
     * 防止快速点击引起ThreadPoolExecutor频繁创建销毁引起crash
     *
     * @return
     */
    public boolean isQuicklyClick() {
        boolean result = false;
        if (System.currentTimeMillis() - currentTime <= 500) {
            result = true;
            Log.e("111111", "is too quickly!");
        }
        currentTime = System.currentTimeMillis();
        return result;
    }

    /**
     * 隐藏软键盘
     *
     * @param activity 当前Activity
     */
    public void hideSoftInput(Activity activity) {
        InputMethodManager inputMethodManager =
                (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        inputMethodManager.hideSoftInputFromWindow(
                activity.getWindow().getDecorView().getWindowToken(), 0);
    }

    /**
     * 显示软键盘
     */
    public void showSoftInput(EditText editText) {
        editText.clearFocus();
        editText.requestFocus();
        // 获取InputMethodManager实例
        InputMethodManager imm = (InputMethodManager) mContext.getSystemService(Context.INPUT_METHOD_SERVICE);
        // 显示软键盘
        editText.postDelayed(() -> {
            imm.showSoftInput(editText, InputMethodManager.SHOW_IMPLICIT);
        }, 100);
    }

    /**
     * 登录信息
     * @return
     */
    public LoginData getLoginData() {
        return BaseApplication.getInstance().getLoginData(SPUtils.getInstance().getString(Constants.LOGIN_ID, ""));
    }

    /**
     * 登录编号
     *
     * @return
     */
    public String getLoginId() {
        return SPUtils.getInstance().getString(Constants.LOGIN_ID, "");
    }

    /**
     * 店铺编号
     *
     * @return
     */
    public String getShopUnique() {
        if (getLoginData() == null) {
            return "";
        } else {
            return getLoginData().getShopUnique();
        }
    }

    /**
     * 店铺名称
     *
     * @return
     */
    public String getShopName() {
        if (getLoginData() == null) {
            return "";
        } else {
            return getLoginData().getShop_name();
        }
    }

    /**
     * 区域编号
     *
     * @return
     */
    public String getAreaDictNum() {
        if (getLoginData() == null) {
            return "";
        } else {
            return getLoginData().getArea_dict_num();
        }
    }

    /**
     * 获取员工编号
     */
    public String getStaffUnique() {
        if (getLoginData() == null) {
            return "";
        } else {
            return getLoginData().getCashier_id();
        }
    }

    /**
     * 获取员工名称
     */
    public String getStaffName() {
        if (getLoginData() == null) {
            return "";
        } else {
            return getLoginData().getStaffName();
        }
    }
}