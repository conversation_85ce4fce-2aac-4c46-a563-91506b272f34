package com.yxl.cashier_retail.base;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.viewbinding.ViewBinding;

import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.MyApplication;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.network.Network;
import com.yxl.cashier_retail.network.NetworkChange;
import com.yxl.cashier_retail.network.NetworkWatcher;
import com.yxl.cashier_retail.utils.ToastUtils;
import com.yxl.commonlibrary.LoadingDialog;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.bean.LoginData;

import java.util.Iterator;
import java.util.List;
import java.util.Observable;

/**
 * desc   : 基类（每个activity 继承BaseActivity）
 */
public abstract class BaseActivity<T extends ViewBinding> extends AppCompatActivity {
    protected Context mContext;
    protected T mBinding;
    private LoadingDialog dialog;
    public String tag = "零售版金圈收银";
    private long currentTime;
    public int page = 1,
            max;//offset

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        overridePendingTransition(R.anim.activity_open_enter, R.anim.activity_open_exit);
        MyApplication.getInstance().addActivity(this);
        mContext = this;
        mBinding = getViewBinding();
        //设置布局文件
        setContentView(mBinding.getRoot());
        setStatusBar();
        //定义事件监听的方法
        setListener();
        //定义初始化数据的方法
        initData();
    }

    @Override
    protected void onResume() {
        super.onResume();
        //网络监听观察者往被观察者中添加订阅事件。
        NetworkChange.getInstance().addObserver(watcher);
    }

    @Override
    protected void onPause() {
        super.onPause();
        //网络监听观察者从被观察者队列中移除
        NetworkChange.getInstance().deleteObserver(watcher);
    }

    /**
     * 监听网络变化
     */
    private final NetworkWatcher watcher = new NetworkWatcher() {

        @Override
        public void update(Observable observable, Object data) {
            super.update(observable, data);
            //观察者接受到被观察者的通知，来更新自己的数据操作。
            MyApplication.mNetwork = (Network) data;
            getNetwork();
        }

    };

    /**
     * 设置状态栏、导航栏
     */
    public void setStatusBar() {
        //开启全屏
        final int fullScreenFlags = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_FULLSCREEN //hide statusBar
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION //hide navigationBar
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                | View.SYSTEM_UI_FLAG_LAYOUT_STABLE;

        final Window window = getWindow();
        window.getDecorView().setSystemUiVisibility(fullScreenFlags);
        window.getDecorView().setOnSystemUiVisibilityChangeListener(visibility -> {
            window.getDecorView().setSystemUiVisibility(fullScreenFlags);
        });
    }

    /**
     * 获取布局文件
     */
    protected abstract T getViewBinding();

    /**
     * 设置事件监听
     */
    protected abstract void setListener();

    /**
     * 初始化数据
     */
    protected abstract void initData();

    /**
     * 网络监听
     */
    public void getNetwork() {
    }

    /**
     * 显示加载等待框
     */
    public void showDialog() {
        if (dialog == null) {
            LoadingDialog.Builder loadBuilder = new LoadingDialog.Builder(this)
                    .setMessage(getRstr(R.string.loading))
                    .setCancelable(true)
                    .setCancelOutside(true);
            dialog = loadBuilder.create();
        }
        dialog.showDialog();
    }

    /**
     * 隐藏加载等待框
     */
    public void hideDialog() {
        if (dialog != null) {
            dialog.hideDialog();
        }
    }

    /**
     * 定义Toast的全局调用方法
     */
    @SuppressLint("InflateParams")
    protected void showToast(int type, String msg) {
        ToastUtils.getInstance(mContext, type, msg, Gravity.TOP).showMessage();
//        ToastUtils.showCustomToast(mContext, type, msg, Gravity.TOP);
    }

    /**
     * 切换Fragment
     *
     * @param fragmentClass   Fragment的类
     * @param bundle          传递的参数
     * @param containerViewId 上一个视图的根视图
     * @param replace         是否替换模式，替换模式会重新加载
     * @param tag             Fragment视图的标记，fragmentClass同样的情况下，以此为区分
     */
    public void toggleFragment(Class<? extends Fragment> fragmentClass, Bundle bundle, int containerViewId, boolean replace, String tag) {

        FragmentManager manager = this.getSupportFragmentManager();
        FragmentTransaction transaction = manager.beginTransaction();
        Fragment fragment = manager.findFragmentByTag(tag);
        if (fragment == null) {
            try {
                fragment = fragmentClass.newInstance();
                if (bundle != null) {
                    fragment.setArguments(bundle);
                }
                if (replace) {//调用onDestoryView
                    transaction.replace(containerViewId, fragment, tag);
                } else {
                    transaction.add(containerViewId, fragment, tag);
                }
            } catch (Exception exception) {
            }
        }

        List fragments = manager.getFragments();

        if (fragments != null) {
            Iterator iterator = fragments.iterator();

            while (iterator.hasNext()) {
                Fragment fm = (Fragment) iterator.next();
                if (fm != null && !fm.equals(fragment)) {
                    transaction.hide(fm);
                }
            }
        }

        transaction.show(fragment);
        transaction.commit();
    }


    /**
     * 不区分tag,适用于每个选项的fragmentClass都不相同
     *
     * @param fragmentClass
     * @param bundle
     * @param containerViewId
     * @param replace
     */
    public void toggleFragment(Class<? extends Fragment> fragmentClass, Bundle bundle, int containerViewId, boolean replace) {
        toggleFragment(fragmentClass, bundle, containerViewId, replace, fragmentClass.getName());
    }

    /**
     * replace模式替换
     *
     * @param containerViewId
     * @param fragment
     * @param bundle
     */
    public void toggleFragment(Fragment fragment, Bundle bundle, int containerViewId) {
        FragmentManager manager = this.getSupportFragmentManager();
        FragmentTransaction transaction = manager.beginTransaction();
        if (bundle != null) {
            fragment.setArguments(bundle);
        }
        transaction.replace(containerViewId, fragment);
        transaction.commit();
    }

    /**
     * 不需要传递参数，适用于简单的跳转逻辑
     *
     * @param fragmentClass
     * @param containerViewId
     * @param replace
     */
    public void toggleFragment(Class<? extends Fragment> fragmentClass, int containerViewId, boolean replace) {
        toggleFragment(fragmentClass, null, containerViewId, replace);
    }

    /**
     * String转化
     */
    public String getRstr(int str) {
        return getResources().getString(str);
    }

    /**
     * 跳转到指定的activity
     *
     * @param clazz 目标activity
     */
    public void goToActivity(Class clazz) {
        startActivity(new Intent(this, clazz));
    }

    /**
     * 跳转到制定activity（带result）
     *
     * @param clazz
     * @param code
     */
    public void goToActivityForResult(Class clazz, int code) {
        startActivityForResult(new Intent(this, clazz), code);
    }

    /**
     * 防止快速点击引起ThreadPoolExecutor频繁创建销毁引起crash
     *
     * @return
     */
    public boolean isQuicklyClick() {
        boolean result = false;
        if (System.currentTimeMillis() - currentTime <= 500) {
            result = true;
            Log.e("111111", "is too quickly!");
        }
        currentTime = System.currentTimeMillis();
        return result;
    }

    /**
     * 隐藏软键盘
     *
     * @param activity 当前Activity
     */
    public void hideSoftInput(Activity activity) {
        InputMethodManager inputMethodManager =
                (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        inputMethodManager.hideSoftInputFromWindow(
                activity.getWindow().getDecorView().getWindowToken(), 0);
    }

    /**
     * 显示软键盘
     */
    public void showSoftInput(EditText editText) {
        editText.clearFocus();
        editText.requestFocus();
        // 获取InputMethodManager实例
        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        // 显示软键盘
        editText.postDelayed(() -> {
            imm.showSoftInput(editText, InputMethodManager.SHOW_IMPLICIT);
        }, 100);
    }

    /**
     * 登录信息
     *
     * @return
     */
    public LoginData getLoginData() {
        return BaseApplication.getInstance().getLoginData(getLoginId());
    }

    /**
     * 登录编号
     *
     * @return
     */
    public String getLoginId() {
        return SPUtils.getInstance().getString(Constants.LOGIN_ID, "");
    }

    /**
     * 店铺编号
     *
     * @return
     */
    public String getShopUnique() {
        if (getLoginData() == null) {
            return "";
        } else {
            return getLoginData().getShopUnique();
        }
    }

    /**
     * 区域编号
     *
     * @return
     */
    public String getAreaDictNum() {
        if (getLoginData() == null) {
            return "";
        } else {
            return getLoginData().getArea_dict_num();
        }
    }

    /**
     * 获取员工编号
     */
    public String getStaffUnique() {
        if (getLoginData() == null) {
            return "";
        } else {
            return getLoginData().getCashier_id();
        }
    }

    /**
     * 获取员工名称
     */
    public String getStaffName() {
        if (getLoginData() == null) {
            return "";
        } else {
            return getLoginData().getStaffName();
        }
    }

    /**
     * 是否记住密码
     *
     * @return
     */
    public boolean isRemember() {
        return !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_REMEMBER, ""));
    }

    /**
     * 是否开机自启动
     *
     * @return
     */
    public boolean isStartUp() {
        return !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_START_UP, ""));
    }

    /**
     * 是否条码校验
     *
     * @return
     */
    public boolean isVerify() {
        return !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_VERIFY, ""));
    }

    /**
     * 是否打印小票
     *
     * @return
     */
    public boolean isPrint() {
        return !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_PRINT, ""));
    }

    /**
     * 会员积分比例（-1~100）
     *
     * @return
     */
    public int getPointsRatio() {
        return SPUtils.getInstance().getInt(Constants.POINTS_RATIO, 1);
    }

    /**
     * 显示“无码商品/称重”
     *
     * @return
     */
    public boolean isShowGoodsNoBarcode() {
        return TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_SHOW_GOODS_NO_BARCODE, ""));
    }

    /**
     * 交换"无码商品/称重"位置
     *
     * @return
     */
    public boolean isUseChangeGoodsNoBarcodeAndWeight() {
        return !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_CHANGE_GOODS_NO_BARCODE_AND_WEIGHT, ""));
    }

    /**
     * 首页输入数字创建无码商品
     *
     * @return
     */
    public boolean isHomeInputDigitCreateGoodsNoBarcode() {
        return TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_HOME_INPUT_DIGIT_CREATE_GOODS_NO_BARCODE, ""));
    }

    /**
     * 支付前弹钱箱
     *
     * @return
     */
    public boolean isUseBeforePayBox() {
        return TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_BEFORE_PAY_BOX, ""));
    }

    /**
     * 挂单打印小票
     *
     * @return
     */
    public boolean isUseHangPrintReceipt() {
        return !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_HANG_PRINT_RECEIPT, ""));
    }

    /**
     * 播报线下收款
     *
     * @return
     */
    public boolean isVoicePaymentOffline() {
        return !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_VOICE_PAYMENT_OFFLINE, ""));
    }

    /**
     * 播报会员收款
     *
     * @return
     */
    public boolean isVoicePaymentMember() {
        return !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_VOICE_PAYMENT_MEMBER, ""));
    }

    /**
     * 播报组合收款
     *
     * @return
     */
    public boolean isVoicePaymentCombination() {
        return !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_VOICE_PAYMENT_COMBINATION, ""));
    }

    /**
     * 播报在线收款
     *
     * @return
     */
    public boolean isVoicePaymentOnline() {
        return !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_VOICE_PAYMENT_ONLINE, ""));
    }

    /**
     * 启用POS收银
     *
     * @return
     */
    public boolean isUsePos() {
        return !TextUtils.isEmpty(SPUtils.getInstance().getString(Constants.IS_USE_POS, ""));
    }
}