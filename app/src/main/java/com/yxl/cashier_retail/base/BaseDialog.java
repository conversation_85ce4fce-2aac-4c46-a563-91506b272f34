package com.yxl.cashier_retail.base;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.NonNull;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LifecycleRegistry;
import androidx.viewbinding.ViewBinding;

import com.blankj.utilcode.util.SPUtils;
import com.yxl.cashier_retail.Constants;
import com.yxl.cashier_retail.R;
import com.yxl.cashier_retail.utils.ToastUtils;
import com.yxl.commonlibrary.LoadingDialog;
import com.yxl.commonlibrary.base.BaseApplication;
import com.yxl.commonlibrary.bean.LoginData;

/**
 * Describe:
 * Created by jingang on 2023/5/31
 */
public abstract class BaseDialog<T extends ViewBinding> extends Dialog implements LifecycleObserver, LifecycleOwner {
    protected LifecycleRegistry lifecycleRegistry;
    protected T mBinding;
    private LoadingDialog dialog;
    public String tag = "BaseDialog";
    private long currentTime;
    public int page = 1;

    public BaseDialog(@NonNull Context context, int style) {
        super(context, style);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        lifecycleRegistry = new LifecycleRegistry(this);
        mBinding = getViewBinding();
        //设置布局文件
        setContentView(mBinding.getRoot());
        enterFullScreen(getWindow());
    }

    @NonNull
    @Override
    public Lifecycle getLifecycle() {
        return lifecycleRegistry;
    }

    /**
     * 获取布局文件
     */
    protected abstract T getViewBinding();

    /**
     * 隐藏导航栏（全屏）
     *
     * @param window
     */
    public void enterFullScreen(Window window) {
//        window.setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
//        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE | WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN);
        int uiOptions = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                | View.SYSTEM_UI_FLAG_FULLSCREEN;
        window.getDecorView().setSystemUiVisibility(uiOptions);
    }

    /**
     * 接口请求等待框
     */
    public void showDialog() {
        if (dialog == null) {
            LoadingDialog.Builder loadBuilder = new LoadingDialog.Builder(getContext())
                    .setMessage(getRstr(R.string.loading))
                    .setCancelable(true)
                    .setCancelOutside(true);
            dialog = loadBuilder.create();
        }
        dialog.showDialog();
    }

    public void hideDialog() {
        if (dialog != null) {
            dialog.hideDialog();
        }
    }

    /**
     * String转化
     */
    public String getRstr(int str) {
        return getContext().getResources().getString(str);
    }

    /**
     * toast提示
     *
     * @param type
     * @param msg
     */
    public void showToast(int type, String msg) {
        ToastUtils.getInstance(getContext(), type, msg, Gravity.TOP).showMessage();
    }

    /**
     * 隐藏软键盘
     *
     * @param activity 当前Activity
     */
    public void hideSoftInput(Activity activity) {
        InputMethodManager inputMethodManager =
                (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        inputMethodManager.hideSoftInputFromWindow(
                activity.getWindow().getDecorView().getWindowToken(), 0);
    }

    /**
     * 防止快速点击引起ThreadPoolExecutor频繁创建销毁引起crash
     *
     * @return
     */
    public boolean isQuicklyClick() {
        boolean result = false;
        if (System.currentTimeMillis() - currentTime <= 500) {
            result = true;
            Log.e("111111", "is too quickly!");
        }
        currentTime = System.currentTimeMillis();
        return result;
    }

    /**
     * 登录信息
     * @return
     */
    public LoginData getLoginData(){
        return BaseApplication.getInstance().getLoginData(SPUtils.getInstance().getString(Constants.LOGIN_ID,""));
    }

    /**
     * 店铺编号
     *
     * @return
     */
    public String getShopUnique() {
        if (getLoginData() == null) {
            return "";
        } else {
            return getLoginData().getShopUnique();
        }
    }

    /**
     * 员工编号
     *
     * @return
     */
    public String getStaffUnique() {
        if (getLoginData() == null) {
            return "";
        } else {
            return getLoginData().getCashier_id();
        }
    }

    /**
     * 员工名称
     *
     * @return
     */
    public String getStaffName() {
        if (getLoginData() == null) {
            return "";
        } else {
            return getLoginData().getStaffName();
        }
    }

    /**
     * 抹零方式
     *
     * @return 0.不抹零 1.抹零到1元 2.抹零到5角 3.抹零到1角
     */
    public int getZero() {
        return SPUtils.getInstance().getInt(Constants.ZERO, 0);
    }

}
