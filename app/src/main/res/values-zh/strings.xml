<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">零售版收银机</string>
    <string name="Multi_Language">多语言</string>

    <!--公共start-->
    <string name="versionchecklib_progress" translatable="false">%d/100</string>
    <string name="cashier_table">收银台</string>
    <string name="money">￥</string>
    <string name="cancel">取消</string>
    <string name="confirm">确认</string>
    <string name="retry">重试</string>
    <string name="clear">清空</string>
    <string name="balance">余额</string>
    <string name="points">积分</string>
    <string name="points_colon">积分：</string>
    <string name="not_write">未填写</string>
    <string name="save">保存</string>
    <string name="recharge">充值</string>
    <string name="sex0">保密</string>
    <string name="sex1">男性</string>
    <string name="sex2">女性</string>
    <string name="confirm_add">确认添加</string>
    <string name="setting_not">未设置</string>
    <string name="all">全部</string>
    <string name="no">编号</string>
    <string name="no_colon">编号：</string>
    <string name="exchange">兑换</string>
    <string name="more">更多</string>
    <string name="piece">件</string>
    <string name="remain">余</string>
    <string name="supplier">供货商</string>
    <string name="contacts">联系人</string>
    <string name="del">删除</string>
    <string name="input">请输入</string>
    <string name="custom">自定义</string>
    <string name="edit">编辑</string>
    <string name="total_price">总价</string>
    <string name="search_tips">商品条码/商品名称/助记码</string>
    <string name="connect_no">未连接</string>
    <string name="clear_goods">清空商品</string>
    <string name="total_count_colon">总数：</string>
    <string name="amount_colon">合计：</string>
    <string name="collection">收款</string>
    <string name="generate_no">待生成</string>
    <string name="generated">已生成</string>
    <string name="canceled">已取消</string>
    <string name="preview">预览</string>
    <string name="time_create_colon">创建时间：</string>
    <string name="sort">排序</string>
    <string name="stock">库存</string>
    <string name="sales">销量</string>
    <string name="sales_month">月销量：</string>
    <string name="supplier_colon">供货商：</string>
    <string name="specs">规格</string>
    <string name="tips">提示</string>
    <string name="confirm_passed">确认通过</string>
    <string name="local">(本地)</string>
    <string name="enable_status">启用状态</string>
    <string name="settlement">结款</string>
    <string name="classX">类</string>
    <string name="no_permission_tips">因权限未开启，该功能无法使用，请去设置中开启。</string>
    <string name="house">库</string>
    <string name="remarks">备注</string>
    <string name="remarks_colon">备注：</string>
    <string name="confirm_replace">确认替换</string>
    <string name="km">千米</string>
    <string name="yuan">元</string>
    <string name="minute">分钟</string>
    <string name="reset">重置</string>
    <string name="newly">新增</string>
    <string name="add">添加</string>
    <string name="copy">复制</string>
    <string name="operate">操作</string>
    <string name="newly_success">新增成功</string>
    <string name="add_success">添加成功</string>
    <string name="search_tips_default">请输入搜索关键字</string>
    <string name="day">天</string>
    <string name="connected">已连接</string>
    <string name="no_network">无网络</string>
    <string name="renew">更新</string>
    <string name="confirm_del_img">确认删除该图片?</string>
    <string name="refund_sign">退</string>
    <string name="common">共</string>
    <string name="nothing">无</string>
    <string name="no_network_connect">无网络连接</string>
    <string name="stock_not_enough">库存不足</string>
    <string name="submit_one_touch">一键提交</string>
    <string name="submit_success">提交成功</string>
    <string name="print">打印</string>
    <string name="loading">加载中...</string>
    <string name="operate_success">操作成功</string>
    <string name="name">名称</string>
    <string name="over">以上</string>
    <string name="chong">充</string>
    <string name="give">送</string>
    <!--公共end-->

    <!--菜单start-->
    <string name="cashier">收银</string>
    <string name="warehousing">入库</string>
    <string name="query">查询</string>
    <string name="statistics">统计</string>
    <string name="netlist">网单</string>
    <string name="member">会员</string>
    <string name="replenishment">补货</string>
    <string name="marketing">营销</string>
    <string name="shift_handover">交班</string>
    <string name="setting">设置</string>
    <!--菜单end-->

    <!--登录start-->
    <string name="kefu_contact">联系客服</string>
    <string name="kefu_phone_colon">客服电话：</string>
    <string name="login_tips">智 能 新 零 售 生 态 平 台</string>
    <string name="close_software">关闭软件</string>
    <string name="account_login">账号登录</string>
    <string name="login_account">登录账号</string>
    <string name="input_mobile">请输入手机号</string>
    <string name="login_pwd">登录密码</string>
    <string name="input_pwd">请输入登录密码</string>
    <string name="pwd_remember">记住密码</string>
    <string name="pwd_forget">忘记密码</string>
    <string name="login">登录</string>
    <string name="version">当前版本</string>
    <string name="login_success">登录成功</string>
    <string name="login_fail">登录失败</string>
    <!--登录end-->

    <!--忘记密码start-->
    <string name="backHome">返回首页</string>
    <string name="email_ads">邮箱地址</string>
    <string name="input_email_ads">请输入邮箱地址</string>
    <string name="code">验证码</string>
    <string name="input_code">请输入验证码</string>
    <string name="code_gain">获取验证码</string>
    <string name="new_pwd">新密码</string>
    <string name="input_new_pwd">请输入新密码</string>
    <string name="confirm_edit">确认修改</string>
    <!--忘记密码end-->

    <!--主函数（收银）start-->
    <string name="main_order">主单</string>
    <string name="hang_order">挂单</string>
    <string name="previous_order">上一单</string>
    <string name="barcode_verify">条码校验</string>
    <string name="click_select_member">点击选择会员</string>
    <string name="no_here_goods">无此商品</string>
    <string name="member_information_fail">会员信息获取失败</string>
    <string name="balance_not_enough">余额不足</string>
    <string name="sale_price_lower_in_price">售价低于进价</string>
    <string name="open_points_exchange">开启积分兑换</string>
    <!--主函数（收银）end-->

    <!--挂单start-->
    <string name="pick_order">取单</string>
    <string name="hang_order_fit">散客挂单</string>
    <string name="hang_order_member">会员挂单</string>
    <string name="hang_order_count_colon">挂单总数：</string>
    <string name="hang_order_total_colon">挂单总额：</string>
    <string name="hang_order_clear">清空挂单</string>
    <!--挂单end-->

    <!--版本更新弹窗start-->
    <string name="version_newed">已经是最新版</string>
    <string name="version_find">发现最新版本</string>
    <string name="version_now">当前版本</string>
    <string name="version_later">稍后更新</string>
    <string name="version_update">立即更新</string>
    <string name="version_download_fail">下载失败</string>
    <string name="version_download_tips">请确保网络状况良好，下载过程中不要关闭当前页面</string>
    <string name="version_downloading">正在下载中...</string>
    <string name="version_notification_title">金圈收银升级</string>
    <string name="version_notification_content">升级中</string>
    <!--版本更新弹窗end-->

    <!--折扣start-->
    <string name="discounts">折</string>
    <string name="discounts_order">整单折扣</string>
    <string name="discounts_order_edit">整单折扣编辑</string>
    <string name="discounts_common">常用折扣(折)</string>
    <string name="zero_default">默认抹零</string>
    <string name="zero_no">不抹零</string>
    <string name="zero_1_yuan">抹零到1元</string>
    <string name="zero_5_corner">抹零到5角</string>
    <string name="zero_1_corner">抹零到1角</string>
    <!--折扣end-->

    <!--入库start-->
    <string name="place_search_goods">请先搜索商品</string>
    <!--批量出库-->
    <string name="no_out_information">暂无出库信息</string>
    <string name="click_goods_to_out">点击商品进行出库</string>
    <string name="out_batch_tips">出库后审核通过商品库存、库存均价、修改改后的售价等才会更改生效</string>
    <string name="out_total_colon">总入库价：</string>
    <string name="print_out_order">打印出库单</string>
    <string name="confirm_out">确认出库</string>
    <!--批量入库-->
    <string name="in_batch">批量入库</string>
    <string name="no_in_information">暂无入库信息</string>
    <string name="click_goods_to_in">点击商品进行入库</string>
    <string name="guess_in_goods">猜你想入库的商品</string>
    <string name="in_batch_tips">入库后审核通过商品库存、库存均价、修改改后的售价等才会更改生效</string>
    <string name="in_total_colon">总入库价：</string>
    <string name="print_in_order">打印入库单</string>
    <string name="confirm_in">确认入库</string>
    <string name="select_goods">请选择商品</string>
    <!--购销单-->
    <string name="purchase">购销单</string>
    <string name="purchase_order_status1">等待确认收货入库</string>
    <string name="purchase_order_status1_tips">请仔细核对商品数量及金额</string>
    <string name="purchase_order_status2">等待商户付款</string>
    <string name="purchase_order_status2_tips">商品已全部入库</string>
    <string name="purchase_order_status3">单据凭证已上传</string>
    <string name="purchase_order_status3_tips">等待供货商确认收货</string>
    <string name="ordered_count">订购数</string>
    <string name="delivery_count">配送数量</string>
    <string name="goods_subtotal">货品小计</string>
    <string name="price_suggest">建议售价</string>
    <string name="paid_in_count">实收数量</string>
    <string name="ordered_total">采购金额</string>
    <string name="payable_total">应结金额</string>
    <string name="settlement_discount">结算优惠</string>
    <string name="pending_total">待结金额</string>
    <string name="purchase_no">购销单号</string>
    <string name="time_create">创建时间</string>
    <string name="time_update">更新时间</string>
    <string name="purchase_voucher">单据凭证</string>
    <string name="confirm_goods_in">确认商品入库</string>
    <string name="repayment">还款</string>
    <string name="purchase_voucher_look">查看单据凭证</string>
    <string name="batch_out">批量出库</string>
    <!--入库end-->

    <!--查询start-->
    <string name="query_order">查订单</string>
    <string name="query_refund">查退款</string>
    <string name="query_goods">查商品</string>
    <string name="query_shift">查交班</string>
    <string name="query_batch">查批次</string>
    <string name="query_chu_ru">查出入库</string>
    <!--查订单-->
    <string name="collection_type">收款方式</string>
    <string name="collection_type_colon">收款方式：</string>
    <string name="date_start">开始日期</string>
    <string name="date_end">结束日期</string>
    <string name="sales_overview">销售总览</string>
    <string name="business_money_colon">营业额：</string>
    <string name="order_count">订单数量</string>
    <string name="order_count_colon">订单数量：</string>
    <string name="order_profit_colon">订单利润：</string>
    <string name="business_overview">营业概况</string>
    <string name="member_recharge_colon">会员充值：</string>
    <string name="member_consumption_colon">会员消费：</string>
    <string name="points_goods_colon">积分商品：</string>
    <string name="refund">退款</string>
    <string name="refund_colon">退款：</string>
    <string name="debt_colon">欠款：</string>
    <string name="no_password_colon">免密：</string>
    <string name="applet_overview">小程序概况</string>
    <string name="online_income_colon">线上收入：</string>
    <string name="to_be_credited_colon">待到账：</string>
    <string name="withdrawn_colon">已提现：</string>
    <string name="pending_withdrawn_colon">待提现：</string>
    <string name="platform_coupons_colon">平台优惠券：</string>
    <string name="platform_beans_colon">平台补贴豆：</string>
    <string name="shop_coupons_colon">店铺优惠券：</string>
    <string name="shop_beans_colon">店铺补贴豆：</string>
    <string name="show_more_info">显示更多信息</string>
    <string name="hide_more_info">收起更多信息</string>
    <string name="order_time">下单时间</string>
    <string name="order_time_colon">下单时间：</string>
    <string name="payment_info_colon">支付详情：</string>
    <string name="addressee">收件人</string>
    <string name="order_no">订单号</string>
    <string name="order_no_colon">订单号：</string>
    <string name="total_amount">总金额</string>
    <string name="count">数量</string>
    <string name="fit">散客</string>
    <string name="goods_information">商品信息</string>
    <string name="order_information">订单信息</string>
    <string name="price">单价</string>
    <string name="count_weight">数量/重量</string>
    <string name="weight">重量</string>
    <string name="subtotal">小计</string>
    <string name="receivable_colon">应收：</string>
    <string name="discount">优惠</string>
    <string name="discount_colon">优惠：</string>
    <string name="paid_in_colon">实收：</string>
    <string name="receivable_money">应收金额</string>
    <string name="receivable_money_colon">应收金额：</string>
    <string name="discount_money_colon">优惠金额：</string>
    <string name="beans_deduction_colon">百货豆抵扣：</string>
    <string name="paid_in_money">实收金额</string>
    <string name="paid_in_money_colon">实收金额：</string>
    <string name="cashier_staff_colon">收银员：</string>
    <string name="pay_time_colon">支付时间：</string>
    <string name="pay_type_colon">支付方式：</string>
    <string name="out_batch">出库批次</string>
    <string name="out_batch_colon">出库批次：</string>
    <string name="print_receipt">打印小票</string>
    <string name="order_refund">订单退款</string>
    <string name="no_order_information">暂无订单信息</string>
    <string name="points_exchange">积分兑换</string>
    <string name="jinquan_plat">金圈平台</string>
    <string name="input_refund_total">请输入退款金额</string>
    <string name="refund_jinquan_plat_tips">金圈平台：通过扫客户收款码支付的订单，退款金额会原路径退款</string>
    <string name="refund_stored_tips">储值卡：退款至客户储值卡</string>
    <string name="refund_cash_tips">现金：使用现金的方式退款给客户</string>
    <string name="refund_alipay_tips">支付宝：使用支付宝的方式退款给客户</string>
    <string name="refund_wechat_tips">微信：使用微信的方式退款给客户</string>
    <string name="select_refund_goods">请选择退款商品</string>
    <!--查退款-->
    <string name="operator">操作人</string>
    <string name="operator_colon">操作人：</string>
    <string name="refund_total_colon">退款总额：</string>
    <string name="refund_time">退款时间</string>
    <string name="refund_time_colon">退款时间：</string>
    <string name="refund_no">退款单号</string>
    <string name="refund_no_colon">退款单号：</string>
    <string name="refund_type">退款方式</string>
    <string name="refund_type_colon">退款方式：</string>
    <string name="select_refund_type">请选择退款方式</string>
    <string name="refund_money">退款金额</string>
    <string name="refund_money_colon">退款金额：</string>
    <string name="refund_count">退款数量</string>
    <string name="order_no_old_colon">原订单号：</string>
    <string name="order_money_colon">订单金额：</string>
    <string name="operat_end_colon">操作端：</string>
    <string name="print_refund_receipt">打印退款小票</string>
    <!--查商品-->
    <string name="cate_all">全部分类</string>
    <string name="goods_cate_all_colon">商品总类：</string>
    <string name="goods_total_colon">商品总额：</string>
    <string name="sell_count">售出数量</string>
    <string name="sell_total">售出总价</string>
    <string name="profit">利润</string>
    <string name="profit_colon">利润：</string>
    <string name="sales_seven">7日销量</string>
    <string name="sales_thirty">30日销量</string>
    <string name="sales_day_colon">平均每日销量：</string>
    <string name="on_sales_batch">在售批次</string>
    <string name="chu_ru_record">出入库记录</string>
    <string name="print_tags">打印价签</string>
    <string name="goods_edit">编辑商品</string>
    <string name="no_goods_information">暂无商品信息</string>
    <string name="sale_count_total_colon">销售总数量：</string>
    <string name="sale_amount_total_colon">销售总金额：</string>
    <string name="sale_purchase_total_colon">销售总成本：</string>
    <string name="sale_profit_total_colon">销售总利润：</string>
    <string name="sale_count">销售数量</string>
    <string name="gift_count">赠送数量</string>
    <string name="sale_purchase">销售成本</string>
    <string name="profit_rate">利润率</string>
    <string name="sale_statistics">销售统计</string>
    <string name="sale_order">销售订单</string>
    <!--查交班-->
    <string name="no_shift_information">暂无交班信息</string>
    <string name="cashier_all">全部收银员</string>
    <string name="select_cashier">请选择收银员</string>
    <string name="input_cashier_no">请输入收银员编号</string>
    <string name="cashier_name">收银员名称</string>
    <string name="cashier_no">收银员编号</string>
    <string name="time_shift_login">接班时间</string>
    <string name="time_shift_out">交班时间</string>
    <string name="business_money">营业额</string>
    <string name="cashiering">收银中</string>
    <string name="recharge_statistics">充值统计</string>
    <string name="way">方式</string>
    <string name="amount_money">金额</string>
    <string name="cashier_statistics">收银统计</string>
    <string name="cashier_order">收银订单</string>
    <string name="sale_total">销售金额</string>
    <string name="print_shift_receipt">打印交班小票</string>
    <!--查出入库start-->
    <string name="type_all">全部类型</string>
    <string name="type">类型</string>
    <string name="operate_time">操作时间</string>
    <string name="operate_before_count">操作前数量</string>
    <string name="operate_after_count">操作后数量</string>
    <string name="operate_source">操作源</string>
    <string name="operate_way">操作方式</string>
    <string name="in_amount_colon">入库总额：</string>
    <string name="out_amount_colon">出库总额：</string>
    <!--查询end-->

    <!--商品管理start-->
    <string name="goods_edit_to_right">点击右侧商品进行编辑</string>
    <string name="goods_info">商品详情</string>
    <string name="input_goods_barcode">请输入商品条码</string>
    <string name="input_goods_name">请输入商品名称</string>
    <string name="pricing_type">计价类型</string>
    <string name="pricing_piece">计件</string>
    <string name="pricing_weight">称重</string>
    <string name="stock_price_average">库存均价</string>
    <string name="supplier_name">供货商名称</string>
    <string name="select_supplier">请选择供货商</string>
    <string name="goods_stock">商品库存</string>
    <string name="in">入库</string>
    <string name="out">出库</string>
    <string name="goods_information_more">更多商品信息</string>
    <string name="edit_home_cate">编辑首页分类</string>
    <string name="add_goods_to_cate">添加商品到首页分类</string>
    <string name="in_price_last_colon">上次入库价：</string>
    <string name="date_produce">生产日期</string>
    <string name="select_date_produce">请选择生产日期</string>
    <string name="date_become">到期日期</string>
    <string name="select_date_become">请选择到期日期</string>
    <string name="goods_out">商品出库</string>
    <string name="out_reason">出库原因</string>
    <string name="select_out_reason">请选择出库原因</string>
    <string name="out_count">出库数量</string>
    <string name="input_out_count">请输入出库数量</string>
    <string name="out_total">出库总价</string>
    <string name="out_price">出库单价</string>
    <string name="input_out_price">请输入出库单价</string>
    <string name="out_type">出库类型</string>
    <string name="goods_newly">新增商品</string>
    <string name="pack0">基础包装</string>
    <string name="pack1">中间包装</string>
    <string name="pack2">最大包装</string>
    <string name="local_goods">本地商品</string>
    <string name="cloud_goods">云库商品</string>
    <string name="download_to_scale">下载到秤</string>
    <string name="goods_cate">商品分类</string>
    <string name="select_goods_cate">请选择商品分类</string>
    <string name="select_goods_cate_big">请选择大分类</string>
    <string name="select_goods_cate_small">请选择小分类</string>
    <string name="goods_life">保质期(天)</string>
    <string name="goods_life_colon">保质期：</string>
    <string name="input_goods_life">请输入保质期</string>
    <string name="goods_specs">商品规格</string>
    <string name="input_goods_specs">请输入商品规格</string>
    <string name="abbr">品名缩写</string>
    <string name="input_abbr">请输入品名缩写</string>
    <string name="cashier_shelves">收银机上下架</string>
    <string name="applet_shelves">小程序上下架</string>
    <string name="input_stock_price_average">请输入库存均价</string>
    <string name="input_goods_stock">请输入商品库存</string>
    <string name="units">单位换算</string>
    <string name="how_mush_base_pack">=多少基础包装</string>
    <string name="in_price_recently">最近入库价</string>
    <string name="in_price_recently_colon">最近入库价：</string>
    <string name="start_order_online">线上起订</string>
    <string name="input_start_order_online">请输入线上起订量</string>
    <string name="goods_brand">商品品牌</string>
    <string name="input_goods_brand">请输入商品品牌</string>
    <string name="stock_warning">库存预警</string>
    <string name="low_to_warning">低于该值将预警</string>
    <string name="stock_low_limit">库存数量低限</string>
    <string name="tall_to_warning">高于该值将预警</string>
    <string name="stock_tall_limit">库存数量上限</string>
    <string name="base_unit_to_conversion">(基础库存换算单位后取整)</string>
    <string name="input_goods_barcode_specs1">请输入中间包装商品条码</string>
    <string name="input_goods_name_specs1">请输入中间包装商品名称</string>
    <string name="base_unit_count_than_one">最小单位数量需大于1</string>
    <string name="input_in_price_specs1">请输入中间包装入库单价</string>
    <string name="input_price_sale_specs1">请输入中间包装零售单价</string>
    <string name="select_goods_unit_specs1">请选择中间包装商品单位</string>
    <string name="input_goods_barcode_specs2">请输入最大包装商品条码</string>
    <string name="input_goods_name_specs2">请输入最大包装商品名称</string>
    <string name="base_unit_count_than_specs1">最小单位数量需大于中间包装最小单位数量</string>
    <string name="input_in_price_specs2">请输入最大包装入库单价</string>
    <string name="input_price_sale_specs2">请输入最大包装零售单价</string>
    <string name="select_goods_unit_specs2">请选择最大包装商品单位</string>
    <string name="cate_default">系统默认分类</string>
    <string name="cate_add">新增一级分类</string>
    <string name="cate_edit">编辑一级分类</string>
    <string name="cate_child_add">新增二级分类</string>
    <string name="cate_child_edit">编辑二级分类</string>
    <string name="cate_name">分类名称</string>
    <string name="input_cate_name">请输入分类名称</string>
    <string name="cate_icon">分类图标</string>
    <string name="select_cate_icon">请选择分类图标</string>
    <string name="cate_home_pc_edit">编辑首页虚拟分类</string>
    <string name="cate_home_pc_add">新增首页虚拟分类</string>
    <string name="cate_home_add">新增分类</string>
    <string name="confirm_cate_del">确认删除该分类？</string>
    <string name="pages">页数</string>
    <string name="goods_manage_exit">退出商品管理</string>
    <string name="goods_add_quick">快速新增商品</string>
    <string name="find_new_goods_or_add">发现新商品，是否将商品基础包装添加到本地？</string>
    <string name="wholesale_count">起批数量</string>
    <string name="input_wholesale_count">请输入起批数量</string>
    <string name="wholesale_price">批发单价</string>
    <string name="input_wholesale_price">请输入批发单价</string>
    <string name="goods_location">商品货位</string>
    <string name="select_goods_location">请选择商品货位</string>
    <string name="average_price_describe">库存均价说明</string>
    <string name="average_price_describe_tips">库存均价采用移动加权平均计算；</string>
    <string name="average_price_describe_tips1">移动加权平均单价= (本次入库前库存商品金额+本次入库商品金额)/(本次入库前库存商品数量+本次入库商品数量 )</string>
    <string name="set_wholesale_price">是否设置批发价</string>
    <string name="input_wholesale_count_specs1">请输入中间包装起批数量</string>
    <string name="input_wholesale_price_specs1">请输入中间包装批发单价</string>
    <string name="input_wholesale_count_specs2">请输入最大包装起批数量</string>
    <string name="input_wholesale_price_specs2">请输入最大包装批发单价</string>
    <string name="confirm_del_goods_in_cate">确认将该商品从该分类下删除？</string>
    <!--商品管理end-->

    <!--统计start-->
    <string name="statistics_operating">经营统计</string>
    <string name="today">今日</string>
    <string name="near_day_seven">近7日</string>
    <string name="near_day_fifteen">近15日</string>
    <string name="near_day_thirty">近30日</string>
    <string name="today_turnover">今日营业额</string>
    <string name="gross_profit">毛利润</string>
    <string name="order_quantity">订单量</string>
    <string name="aver_price">客单价</string>
    <string name="online_order_quantity">网单量</string>
    <string name="sales_trend">销售额走势</string>
    <string name="week">周</string>
    <string name="month">月</string>
    <string name="year">年</string>
    <string name="sales_volume">销售额</string>
    <string name="sales_cycle_ratio">销售数据周期占比</string>
    <string name="sales_class_ratio">销售品类占比</string>
    <string name="payment_ratio">收款类型占比</string>
    <string name="sales_volume_hour">营业额24H分布</string>
    <string name="yesterday_turnover">昨日营业额</string>
    <string name="goods_hot_sales_top">热销商品TOP5</string>
    <string name="goods_profit_top">累计利润商品TOP5</string>
    <string name="goods_unhot_sales_top">滞销商品TOP5</string>
    <string name="statistics_payment">打款统计</string>
    <string name="statistics_staff">员工统计</string>
    <string name="real_revenue">实际营收</string>
    <string name="no_password_paid_in">免密实收</string>
    <string name="discount_money">优惠金额</string>
    <string name="member_to_shop">到店会员</string>
    <string name="stored_money">储值金额</string>
    <string name="expired_warning">过期预警</string>
    <string name="restock_goods">需补货商品</string>
    <!--统计end-->

    <!--网单start-->
    <string name="netlist_order">网单订单</string>
    <string name="netlist_refund">网单退款</string>
    <string name="rider_manage">骑手管理</string>
    <string name="beans">百货豆</string>
    <string name="delivery_setting">配送设置</string>
    <string name="order_type">网单类型</string>
    <string name="order_type0">店内收银</string>
    <string name="order_type1">门店自提</string>
    <string name="order_type2">送货上门</string>
    <string name="order_status0">待发货</string>
    <string name="order_status1">待配送</string>
    <string name="order_status2">配送异常</string>
    <string name="order_status3">待收货</string>
    <string name="order_status4">待自提</string>
    <string name="order_status5">待评价</string>
    <string name="order_status6">已完成</string>
    <string name="order_status7">待付款</string>
    <string name="order_status8">已取消</string>
    <string name="order_status9">待入库</string>
    <string name="order_status10">待确认</string>
    <string name="order_status11">已作废</string>
    <string name="order_status12">待结款</string>
    <string name="serial_number">流水号</string>
    <string name="contacts_type">联系方式</string>
    <string name="get_type">收货方式</string>
    <string name="goods_count">商品数量</string>
    <string name="payable_colon">应付：</string>
    <string name="paid_colon">实付：</string>
    <string name="order_receive">接单</string>
    <string name="confirm_get">确认收货</string>
    <string name="delivery_cancel">取消配送</string>
    <string name="order_cancel">取消订单</string>
    <string name="confirm_pick">确认取货</string>
    <string name="order_receive_again">重新接单</string>
    <string name="goods_price">商品单价</string>
    <string name="goods_total">商品总价</string>
    <string name="refunded_count">已退数量</string>
    <string name="refund_goods_count">退货数量</string>
    <string name="refund_goods_total">退货小计</string>
    <string name="rtotal_refund_colon">合计退款：</string>
    <string name="whole_order_refund">整单\n退款</string>
    <string name="total">共计</string>
    <string name="refund_print_receipts">退款打印小票</string>
    <string name="refund_order">退款单</string>
    <string name="order_no_receive">该订单不支持接单</string>
    <string name="receive_address">收件地址</string>
    <string name="order_weight">订单重量</string>

    <!--网单退款-->
    <string name="refund_status0">待审核</string>
    <string name="refund_status1">已退款</string>
    <string name="refund_status2">已拒绝</string>
    <string name="apply_time">申请时间</string>
    <string name="apply_time_colon">申请时间：</string>
    <string name="refunder">退款人</string>
    <string name="should_refund_colon">应退：</string>
    <string name="refund_reason_colon">退款原因：</string>
    <string name="apply_refund_money_colon">申退金额：</string>
    <string name="shopper_colon">配送员：</string>
    <string name="mobile">手机号</string>
    <string name="mobile_colon">手机号：</string>
    <string name="refund_refuse">拒绝退款</string>
    <string name="refund_confirm">确认退款</string>
    <string name="refuse_reason">拒绝原因</string>
    <string name="input_refuse_reason">请输入拒绝原因</string>
    <string name="usually_script">常用话术</string>
    <string name="refuse_reason_add">添加拒绝原因</string>
    <string name="refuse_reason_edit">编辑拒绝原因</string>

    <!--骑手管理-->
    <string name="select_rider">请选择骑手</string>
    <string name="rider_add">新增骑手</string>
    <string name="rider_edit">编辑骑手</string>
    <string name="rider_name">骑手姓名</string>
    <string name="input_rider_name">请输入骑手姓名</string>
    <string name="rider_mobile">骑手电话</string>
    <string name="input_rider_mobile">请输入骑手电话</string>
    <string name="input_rider_mobile_right">请输入正确的骑手电话</string>

    <!--百货豆-->
    <string name="beans_left">剩余百货豆</string>
    <string name="order_count_total">订单总数</string>
    <string name="order_total_total">订单总金额</string>
    <string name="subsidy_total">平台补贴总数</string>
    <string name="deduct_total">会员抵扣总数</string>
    <string name="rule_setting">规则设置</string>
    <string name="trading_record">交易记录</string>
    <string name="withdrawal_record">提现记录</string>
    <string name="order_total">订单金额</string>
    <string name="order_total_colon">订单金额：</string>
    <string name="deduct_count">抵扣数量</string>
    <string name="shop_gift">本店赠送</string>
    <string name="subsidy">平台补贴</string>
    <string name="pay_type">支付方式</string>
    <string name="trading_time">交易时间</string>
    <string name="status_trading">交易状态</string>
    <string name="beans_status0">审核中</string>
    <string name="beans_status1">已到账</string>
    <string name="beans_status2">已驳回</string>

    <!--配送设置-->
    <string name="delivery_type_colon">配送方式：</string>
    <string name="delivery_type0">自配送</string>
    <string name="delivery_type1">一刻钟配送</string>
    <string name="delivery_range_colon">配送范围：</string>
    <string name="input_delivery_range">请输入配送范围</string>
    <string name="delivery_range_tips">提示：设置店铺配送直径范围，最高不超过10千米。</string>
    <string name="delivery_start_order_colon">起送价格：</string>
    <string name="input_delivery_start_order">请输入起送价格</string>
    <string name="delivery_start_order_tips">提示：设置店铺起送价格，用户下单时的商品总额大于或等于时此价格，才可以下单。</string>
    <string name="delivery_free_colon">免配金额：</string>
    <string name="input_delivery_free">请输入免配金额</string>
    <string name="delivery_free_tips">提示：设置店铺免配送费价格，用户下单时的商品总额大于或等于时此价格，免配送费。</string>
    <string name="estimate_duration_colon">预计时长：</string>
    <string name="input_estimate_duration">请输入预计时长</string>
    <!--网单end-->

    <!--会员start-->
    <string name="member_search">搜索会员</string>
    <string name="member_search_phone">搜索会员手机号</string>
    <string name="member_add">新增会员</string>
    <string name="member_manage">会员管理</string>
    <string name="place_select_member">请选择会员</string>
    <string name="no_member">号会员</string>
    <string name="member_search_notfind">未搜索到相关会员</string>
    <string name="member_tips">请更改搜索信息或点击</string>
    <string name="member_mobile">会员手机</string>
    <string name="input_member_mobile">请输入会员手机</string>
    <string name="member_no">会员编号</string>
    <string name="input_member_no">请输入会员编号</string>
    <string name="member_name">会员名称</string>
    <string name="input_member_name">请输入会员名称</string>
    <string name="member_balance">账户余额</string>
    <string name="input_member_balance">请输入账户余额</string>
    <string name="member_points">会员积分</string>
    <string name="points_add">增加积分</string>
    <string name="points_sub">减少积分</string>
    <string name="debt_limit">欠款限额</string>
    <string name="input_debt_limit">请输入欠款限额</string>
    <string name="member_sex">会员性别</string>
    <string name="account_pwd">账号密码</string>
    <string name="input_account_pwd">请输入账号密码</string>
    <string name="remarks_info">备注信息</string>
    <string name="input_remarks_info">请输入备注信息</string>
    <string name="member_analysis">店内会员分析</string>
    <string name="member_total">会员总数</string>
    <string name="stored_total">储值总额</string>
    <string name="credit_total">赊欠总额</string>
    <string name="proportion_member">会员类型占比</string>
    <string name="proportion_stored_money">储值金额占比</string>
    <string name="proportion_member_points">会员积分占比</string>
    <string name="proportion_member_level">会员等级占比</string>
    <string name="proportion_member_time">会员到店时段占比</string>
    <string name="level_manage">等级管理</string>
    <string name="points_rule">积分规则</string>
    <string name="member_credit">赊欠会员</string>
    <string name="member_stored">储值会员</string>
    <string name="member_common">普通会员</string>
    <string name="member_gold">黄金会员</string>
    <string name="member_platinum">铂金会员</string>
    <string name="member_diamond">钻石会员</string>
    <string name="member_info">会员详情</string>
    <string name="consumption_records">消费记录</string>
    <string name="member_level">会员等级</string>
    <string name="member_del">删除会员</string>
    <string name="member_type">会员类型</string>
    <string name="member_type0">会员卡</string>
    <string name="member_type1">储值卡</string>
    <string name="member_type2">会员储值卡</string>
    <string name="register_just">注册即可</string>
    <string name="input_points">请输入积分</string>
    <string name="input_points_gold">请输入黄金会员积分</string>
    <string name="input_points_platinum">请输入铂金会员积分</string>
    <string name="input_points_diamond">请输入钻石会员积分</string>
    <!--会员end-->

    <!--补货start-->
    <string name="mall_jinquan">金圈商城</string>
    <string name="restock_self">自采补货</string>
    <string name="restock_create">创建补货计划</string>
    <string name="input_restock_name">请输入补货计划名称</string>
    <string name="restock_id_wrong">补货计划id有误，请重试</string>
    <string name="buy_count">采购数量</string>
    <string name="price_estimate">预估价格</string>
    <string name="price_estimate_colon">预估价格：</string>
    <string name="restock_cancel">取消补货</string>
    <string name="restock_add">添加补货</string>
    <string name="restock_again">再次补货</string>
    <string name="restock_select_goods">点击选择补货商品</string>
    <string name="sales_three_colon">3日销量：</string>
    <string name="sales_seven_colon">7日销量：</string>
    <string name="stock_sale">可销库存：</string>
    <string name="stock_below_safe">（低于安全库存）</string>
    <string name="buy_info_last">上次采购信息：</string>
    <string name="buy_count_suggest">建议采购量：</string>
    <string name="input_restock_count">请输入补货数量</string>
    <string name="select_restock_specs">请选择补货规格</string>
    <string name="restock_preview">补货计划预览</string>
    <string name="restock_submit">提交补货计划</string>
    <string name="address_where_colon">所在地址：</string>
    <string name="address_where">所在地址</string>
    <string name="input_address_where">请输入所在地址</string>
    <string name="product_info">货品详情</string>
    <string name="goods_type">商品种类</string>
    <string name="goods_type_colon">商品种类：</string>
    <string name="restock_submit_tips">提交补货单后将无法更改， 如需修改可联系供货商修改！</string>
    <string name="place_add_goods">请添加商品</string>
    <string name="supplier_manage">供货商管理</string>
    <string name="supplier_apply">供货商申请</string>
    <string name="supplier_cate">供货商分类</string>
    <string name="supplier_add">添加供货商</string>
    <string name="supplier_edit">编辑供货商</string>
    <string name="purchase_total">采购总额</string>
    <string name="purchase_total_colon">采购总额：</string>
    <string name="debt_total">欠款总额</string>
    <string name="debt_total_colon">欠款总额：</string>
    <string name="settled_total">已结金额</string>
    <string name="settled_total_colon">已结金额：</string>
    <string name="supplier_search_tips">供应商名称/手机号</string>
    <string name="supplier_cate_manage">供货商分类管理</string>
    <string name="supplier_cate_add">新增供货商分类</string>
    <string name="supplier_cate_name">供货商分类名称</string>
    <string name="input_supplier_cate_name">请输入供货商分类名称</string>
    <string name="input_supplier_name">请输入供货商名称</string>
    <string name="supplier_all">全部供货商</string>
    <string name="input_contacts">请输入联系人</string>
    <string name="cate_affiliation">所属分类</string>
    <string name="select_cate">请选择分类</string>
    <string name="supplier_info">供货商详情</string>
    <string name="supplied_goods">所供商品</string>
    <string name="purchase_order">购销订单</string>
    <string name="settlement_record">结款记录</string>
    <string name="supplier_replace">替换供货商</string>
    <string name="goods_archived">已建档商品</string>
    <string name="goods_unfilled">未建档商品</string>
    <string name="price_suggest_colon">建议价：</string>
    <string name="archived">建档</string>
    <string name="status_payment">付款状态</string>
    <string name="status_order">订单状态</string>
    <string name="status_order_colon">订单状态：</string>
    <string name="repayment_time">还款时间</string>
    <string name="repayment_name">还款人</string>
    <string name="repayment_order">还款订单</string>
    <string name="repayment_total">还款金额</string>
    <string name="status_repayment">还款状态</string>
    <string name="purchase_info">购销单详情</string>
    <string name="price_info">价格明细</string>
    <string name="batch_infos">批次信息</string>
    <string name="batch_profit">批次利润</string>
    <string name="goods_all">全部商品</string>
    <string name="supplier_information">供货商信息</string>
    <string name="supplier_name_colon">供货商名称：</string>
    <string name="contacts_colon">联系人：</string>
    <string name="buying_price">进货价</string>
    <string name="goods_purchase">商品补货</string>
    <string name="supplier_change">更改供货商</string>
    <string name="settlement_info">结款详情</string>
    <string name="repayment_intro">还款明细</string>
    <string name="repayment_name_colon">还款人：</string>
    <string name="repayment_total_colon">还款金额：</string>
    <string name="repayment_remarks_colon">还款备注：</string>
    <string name="repayment_voucher_colon">还款凭证：</string>
    <string name="copy_to_clipboard">已复制到剪切板</string>
    <string name="goods_not_check">有商品未核对</string>
    <string name="supplier_settlement">供货商还款</string>
    <string name="batch_debt_total">个批次 总欠款</string>
    <string name="all_select">全选</string>
    <string name="confirm_settlement">确认还款</string>
    <string name="selected">已选</string>
    <string name="order_debt_total">个订单，总欠款</string>
    <string name="select_order">请选择订单</string>
    <string name="input_repayment_total">请输入还款金额</string>
    <string name="purchase_voucher_upload">上传单据凭证</string>
    <string name="plase_purchase_voucher_upload">请上传单据凭证</string>
    <string name="submit">提交</string>
    <string name="goods_check">商品核对</string>
    <string name="ordered_count_colon">订购：</string>
    <string name="actual_received_count_colon">实际收货数量：</string>
    <string name="actual_in_count_colon">实际入库数量：</string>
    <string name="goods_short_stock">商品缺货</string>
    <string name="goods_many_stock">商品多货</string>
    <string name="three_price_syn_suggest_price">三价同步建议价</string>
    <string name="goods_check_cancel">撤销核对</string>
    <!--补货end-->

    <!--营销start-->
    <string name="goods_discount">商品折扣</string>
    <string name="goods_gift">商品满赠</string>
    <string name="order_promotion">订单促销</string>
    <string name="goods_promotion">单品促销</string>
    <string name="member_price_join_discount">会员价参与优惠</string>
    <string name="price_old_colon">原价：</string>
    <string name="full">满</string>
    <string name="piece_gift">件赠送</string>
    <string name="yuan_discount">元优惠</string>
    <string name="yuan_gift">元赠送</string>
    <!--营销end-->

    <!--交班start-->
    <string name="duty_cashier">值班店员</string>
    <string name="confirm_shift">确认交班</string>
    <string name="order_offline">离线订单</string>
    <string name="no_order_offline">暂无离线订单</string>
    <string name="recharge_info_colon">充值详情：</string>
    <string name="revenue_statistics">营收统计</string>
    <string name="order_receivable">订单应收</string>
    <string name="order_paid_in">订单实收</string>
    <!--交班end-->

    <!--设置start-->
    <string name="setting_shop">店铺信息</string>
    <string name="setting_cashier">收银设置</string>
    <string name="setting_parts">配件设置</string>
    <string name="setting_online">线上设置</string>
    <string name="setting_system">系统设置</string>
    <!--店铺信息-->
    <string name="shop_id">店铺ID</string>
    <string name="goods_syn_again">重新同步商品</string>
    <string name="goods_syb_start">开始同步</string>
    <string name="shop_name">店铺名称</string>
    <string name="input_shop_name">请输入店铺名称</string>
    <string name="shop_address">店铺位置</string>
    <string name="input_shop_address">请输入店铺位置</string>
    <string name="machine_unique">机器编号</string>
    <string name="staff_unique">雇员编号</string>
    <string name="machine_mac">机器MAC</string>
    <string name="contact_mobile">联系电话</string>
    <string name="input_contact_moblie">请输入联系电话</string>
    <string name="member_unique_four">会员卡前四位</string>
    <string name="input_member_unique_four">请输入会员卡前四位</string>
    <!--收银设置-利润设置-->
    <string name="setting_profit">利润设置</string>
    <string name="setting_payment">支付设置</string>
    <string name="ratio_nobarcode_profit">无码商品利润比(利润/售价)</string>
    <string name="ratio_nobarcode_profit_tips">按照设置的比例计算利润</string>
    <string name="ratio_standard_profit">普通商品利润比(利润/售价)</string>
    <string name="ratio_standard_profit_tips">商品入库未填写进货价时会按照比例计算利润</string>
    <string name="ratio_points">积分比例</string>
    <string name="ratio_points_tips">消费1元可兑换积分</string>
    <!--收银设置-支付设置-->
    <string name="cash">现金</string>
    <string name="cash_colon">现金：</string>
    <string name="alipay">支付宝</string>
    <string name="alipay_colon">支付宝：</string>
    <string name="wechat">微信</string>
    <string name="wechat_colon">微信：</string>
    <string name="bank_card">银行卡</string>
    <string name="bank_card_colon">银行卡：</string>
    <string name="face_swiping">刷脸付</string>
    <string name="stored_card">储值卡</string>
    <string name="combination">组合付</string>
    <string name="goods_manage">商品管理</string>
    <string name="online">线上</string>
    <string name="offline">线下</string>
    <string name="offline1">(线下)</string>
    <string name="place_select_shortcut_display">请选择首页快捷操作显示功能，开启后功能按钮会在首页展示</string>
    <string name="payment_cash">现金收款</string>
    <string name="payment_cash_tips">(消费者使用现金支付)</string>
    <string name="payment_alipay">支付宝收款</string>
    <string name="payment_alipay_tips">(消费者使用支付宝扫收款码支付)</string>
    <string name="payment_wechat">微信收款</string>
    <string name="payment_wechat_tips">(消费者使用微信扫收款码支付)</string>
    <string name="payment_bank_card">银行卡收款</string>
    <string name="payment_bank_card_tips">(消费者使用银行卡支付)</string>
    <string name="payment_face">人脸收款</string>
    <string name="payment_face_tips">(消费者在一刻钟小程序人脸认证后可使用人脸识别支付)</string>
    <string name="payment_stored_card">储值卡支付</string>
    <string name="payment_stored_card_tips">(消费者使用会员余额支付)</string>
    <string name="payment_combination">组合支付</string>
    <string name="payment_combination_tips">(消费者使用支付宝/微信+现金多种方式支付)</string>
    <string name="goods_manage_tips">(快速添加、修改商品信息)</string>
    <string name="shortcut_display_length">最多选择7个快捷方式</string>
    <!--配件设置-->
    <string name="setting_receipt">小票设置</string>
    <string name="setting_tags">价签设置</string>
    <string name="setting_weight">称重设置</string>
    <string name="setting_camera">摄像头设置</string>
    <string name="setting_pos">POS设置</string>
    <string name="enable_pos_cashier">启用POS收银</string>
    <string name="pos_id">POS设备ID</string>
    <string name="input_pos_id">请输入POS设备ID</string>
    <string name="set_pos_id">请设置POS设备ID</string>
    <string name="pos">POS机</string>
    <string name="setting_other">其他设置</string>
    <string name="receipt_specs">小票规格</string>
    <string name="remarks_content">备注内容</string>
    <string name="print_test">打印测试</string>
    <string name="print_test_tips">提示：请先测试后无误后使用</string>
    <string name="tags_template">自定义价签模版</string>
    <string name="port_scale">串口称</string>
    <string name="enable_port_scale">启用串口称</string>
    <string name="port_name">串口号</string>
    <string name="BAUD">波特率</string>
    <string name="select_BAUD">选择波特率</string>
    <string name="data_parsing_type">数据解析方式</string>
    <string name="select_data_parsing_type">选择数据解析方式</string>
    <string name="scale_status">电子秤状态</string>
    <string name="port_jin">串口称市斤</string>
    <string name="weight_now">当前重量</string>
    <string name="scale_add">新增条码秤</string>
    <string name="input_scale_name">请输入条码秤名称</string>
    <string name="scale_download">下载到秤</string>
    <string name="enable_secondary_screen">启用副屏</string>
    <string name="pay_money_box">支付前弹钱箱</string>
    <string name="camera_face">人脸摄像头</string>
    <string name="select_camera_face">选择人脸摄像头</string>
    <string name="money_box_printer">钱箱打印机</string>
    <string name="select_money_box_printer">选择钱箱打印机</string>
    <string name="tts_engine">TTS引擎</string>
    <string name="select_tts_engine">选择TTS引擎</string>
    <string name="tts_language">TTS语言</string>
    <string name="select_tts_language">选择TTS语言</string>
    <string name="tts_voice">TTS声音</string>
    <string name="select_tts_voice">选择TTS声音</string>
    <string name="mm">（毫米）</string>
    <string name="tags_size">标签尺寸</string>
    <string name="width">宽</string>
    <string name="height">高</string>
    <string name="tags_col">标签列数</string>
    <string name="print_count">打印数量</string>
    <string name="tags_spacing">标签间距</string>
    <string name="print_concentration">打印浓度</string>
    <string name="resolution_ratio">分辨率</string>
    <string name="content_location">内容位置</string>
    <string name="content_location_tips">坐标为0则不打印</string>
    <string name="content_custom">自定义内容</string>
    <string name="content_custom_tips">内容为空或者坐标为0则不打印</string>
    <string name="goods_list">商品列表</string>
    <string name="goods_add">添加商品</string>
    <string name="goods_name">商品名称</string>
    <string name="goods_sale">商品售价</string>
    <string name="goods_barcode">商品条码</string>
    <string name="input_content">请输入内容</string>
    <string name="template_save">保存模版</string>
    <string name="confirm_print">确认打印</string>
    <!--小票设置-->
    <string name="connect_bt">蓝牙连接</string>
    <string name="connect_serail">端口连接</string>
    <string name="connect_usb">USB连接</string>
    <string name="connect_wifi">WIFI连接</string>
    <string name="printer_no_connect">打印机未连接</string>
    <string name="send_success">发送成功</string>
    <string name="send_fail">发送失败</string>
    <string name="balance_colon">余额：</string>
    <string name="stored_card_colon">储值卡：</string>
    <string name="star_shopname">** 店铺名称 **</string>
    <string name="order_receipt">网单结账单</string>
    <string name="statement">结账单</string>
    <string name="cash_register_colon">收银机：</string>
    <string name="cash_staff_name">丽丽</string>
    <string name="goods">商品</string>
    <string name="amount">合计</string>
    <string name="jinquan">金圈</string>
    <string name="member_colon">会员：</string>
    <string name="order_points_colon">本单积分：</string>
    <string name="member_points_colon">会员积分：</string>
    <string name="star_remarks">******备注******</string>
    <string name="tips_warm">温馨提示</string>
    <string name="tips_warm_colon">温馨提示：</string>
    <string name="receipt_tips">温馨提示：如需要发票请联系前台</string>
    <string name="printer_connect_success">打印机连接成功</string>
    <string name="printer_connect_fail">打印机连接失败</string>
    <!--系统设置-->
    <string name="setting_voice">声音设置</string>
    <string name="setting_inlet">入口设置</string>
    <string name="cheng_weight_two">称重商品的前两位</string>
    <string name="cheng_standard_two">普通商品的前两位</string>
    <string name="goods_nobarcode_add_cart">未创建无码商品直接添加到购物车</string>
    <string name="topping">程序置顶</string>
    <string name="startup">开机自启</string>
    <string name="goods_barcode_first_no_zero">商品条码不可开头为0</string>
    <!--    <string name="show_nobarcode_and_weight">显示\'无码商品/称重'</string>-->
    <!--    <string name="change_nobarcode_and_weight">交换\'无码商品/称重' 位置</string>-->
    <string name="use_virtual_keyboard">使用虚拟键盘</string>
    <string name="hang_print_receipts">挂单打印小票</string>
    <string name="xml_ratio">界面比例</string>
    <string name="home_input_digit_create_nobarcode_goods">首页输入数字创建无码商品</string>
    <string name="is_show_stock">是否展示库存</string>
    <string name="is_exit_procedure">退出时关闭计算机</string>
    <string name="version_check">检查更新</string>
    <string name="upload_log">上传当前日志</string>
    <string name="exit_procedure">退出程序</string>
    <string name="is_voice_home">播放首页提示音</string>
    <string name="is_voice_kewboard">播放键盘声</string>
    <string name="is_voice_online_pay">播报在线支付</string>
    <string name="is_voice_cash_pay">播报现金支付</string>
    <string name="is_voice_member_pay">播报会员支付</string>
    <string name="is_voice_offline_pay">播报线下支付</string>
    <string name="is_voice_applet_pay">播报小程序支付</string>
    <string name="is_voice_combination_pay">播报组合支付</string>
    <!--线上设置-->
    <string name="print_auto_order">自动接单打印</string>
    <string name="printer_connect_type">打印机连接方式</string>
    <string name="select_port">选择串口</string>
    <string name="printer_status">打印机状态</string>
    <string name="printer_defaule_count">默认打印数量</string>
    <string name="yourself_remarks">独立的备注内容</string>
    <string name="thanks_welcome_again">谢谢惠顾，欢迎下次光临！</string>
    <string name="print_template">打印模板</string>

    <string name="select_print_type">请选择打印机连接方式</string>
    <string name="is_use_catering_receipt">是否使用餐饮小票</string>
    <string name="reset_catering_receipt_count">重置餐饮小票计数</string>
    <string name="confirm_reset">确认重置</string>
    <string name="port_no">端口号</string>
    <string name="select_port_no">请选择端口号</string>
    <string name="print_auto_refund">自动打印退款单</string>
    <string name="serial_port_weighing_scale">串口市斤秤</string>
    <string name="show_nobarcode_and_weight">显示“无码商品/称重”</string>
    <string name="change_nobarcode_and_weight">交换"无码商品/称重"位置</string>
    <string name="is_voice_applet_refund">播报小程序退款</string>
    <string name="is_voice_applet_order">播报小程序接单</string>
    <string name="data_parsing_type0">方式1</string>
    <string name="data_parsing_type1">方式2</string>
    <string name="data_parsing_type2">方式3</string>
    <string name="data_parsing_type3">方式4</string>
    <!--设置end-->

    <!--dialog（选择日期）start-->
    <string name="select_date">选择日期</string>
    <string name="select_date_start">请选择开始日期</string>
    <string name="select_date_end">请选择结束日期</string>
    <!--dialog（选择日期）end-->

    <!--*****************************弹窗start*****************************-->
    <!--当前登录账号-->
    <string name="this_login_account">当前登录账号</string>
    <string name="staff_name_colon">员工名称：</string>
    <string name="contact_mobile_colon">联系电话：</string>
    <string name="login_account_colon">登录账号：</string>
    <string name="login_pwd_colon">登录密码：</string>
    <string name="pwd_change">修改密码</string>
    <string name="belong_shop_colon">所属店铺：</string>
    <string name="shop_no_colon">店铺编号：</string>
    <string name="staff_position_colon">员工角色：</string>
    <string name="shopowner">店长</string>
    <string name="cashier_staff">收银员</string>

    <!--商品入库-->
    <string name="goods_in">商品入库</string>
    <string name="in_reason">入库原因</string>
    <string name="select_in_reason">请选择入库原因</string>
    <string name="purchase_price">采购单价</string>
    <string name="purchase_price_colon">采购单价：</string>
    <string name="input_purchase_price">请输入采购单价</string>
    <string name="stock_now">当前库存</string>
    <string name="stock_now_colon">当前库存：</string>
    <string name="in_count">入库数量</string>
    <string name="input_in_count">请输入入库数量</string>
    <string name="in_price">入库单价</string>
    <string name="input_in_price">请输入入库单价</string>
    <string name="in_total">入库总价</string>
    <string name="price_now">当前价格</string>
    <string name="change_to">修改为</string>
    <string name="price_sale">零售单价</string>
    <string name="price_sale_colon">零售单价：</string>
    <string name="input_price_sale">请输入零售单价</string>
    <string name="price_online">网购单价</string>
    <string name="price_online_colon">网购单价：</string>
    <string name="input_price_online">请输入网购单价</string>
    <string name="price_member">会员单价</string>
    <string name="price_member_colon">会员单价：</string>
    <string name="input_price_member">请输入会员单价</string>
    <string name="no_count">待计算</string>
    <string name="remarks_img">备注照片</string>
    <string name="batch_no">批次号</string>
    <string name="in_time">入库时间</string>
    <string name="count_left">剩余数量</string>
    <string name="out_count_total_no_than_count">出库数之和不得大于商品出库数量</string>

    <!--会员退费-->
    <string name="refund_fee">退费</string>
    <string name="member_refund">会员退费</string>
    <string name="member_refund_tips">退费时，系统仅清零会员储值余额，请通过现金或者线下转账的形式将余额退还给消费者!</string>
    <string name="refund_fee_money">退费金额</string>
    <string name="input_refund_fee_money">请输入退费金额</string>
    <string name="member_refund_tips1">退费金额不可大于会员余额</string>

    <!--会员充值-->
    <string name="member_recharge">会员充值</string>
    <string name="recharge_money">充值金额</string>
    <string name="input_recharge_money">请输入充值金额</string>
    <string name="recharge_activity_colon">充值活动：</string>
    <string name="jinquan_collection">金圈收款</string>
    <string name="jinquan_collection_tips">扫描消费者付款码即可</string>
    <string name="recharge_success">充值成功</string>
    <string name="refund_success">退费成功</string>

    <!--兑换、增加、减少积分-->
    <string name="points_count">积分数量</string>
    <string name="input_points_count">请输入积分数量</string>
    <string name="add_reason">增加原因</string>
    <string name="sub_reason">减少原因</string>
    <string name="exchange_points">兑换积分</string>
    <string name="exchange_reason">兑换原因</string>

    <!--积分规则-->
    <string name="use_points">使用积分</string>
    <string name="one_yuan_equal">1元=</string>
    <string name="equal_points">等于多少积分</string>
    <string name="count_type">不足1元计算方式</string>
    <string name="half_adjust">四舍五入</string>
    <string name="not_count">不计算</string>

    <!--交班-统计字段说明-->
    <string name="statistics_field_explain">统计字段说明</string>
    <string name="statistics_field_explain_tips0">实际营收=营业额-优惠金额-退款金额；</string>
    <string name="statistics_field_explain_tips1">营业额：指所选时间范围内成交订单金额的总和；</string>
    <string name="statistics_field_explain_tips2">免密支付：指所选时间范围内扫描客户付款码收款的订单金额总和；</string>
    <string name="i_got_it">我知道了</string>

    <!--选择商品单位-->
    <string name="goods_unit">商品单位</string>
    <string name="goods_unit_colon">商品单位：</string>
    <string name="select_goods_unit">请选择商品单位</string>
    <string name="goods_unit_add">新增商品单位</string>
    <string name="goods_unit_edit">编辑商品单位</string>
    <string name="input_goods_unit">请输入商品单位名称</string>

    <!--修改单价、数量、小计-->
    <string name="modify_price">修改单价</string>
    <string name="input_price">请输入单价</string>
    <string name="modify_count">修改数量</string>
    <string name="input_count">请输入数量</string>
    <string name="modify_subtotal">修改小计</string>
    <string name="input_subtotal">请输入小计</string>

    <!--现金收款-->
    <string name="receivable">应收</string>
    <string name="price_discount">折扣价</string>
    <string name="give_change">找零</string>
    <string name="zero_discount">抹零优惠</string>
    <string name="zero_store_all">全部存零</string>
    <string name="zero_store_one">存零到1元</string>
    <string name="collection_success">收款成功</string>
    <string name="print_repair_receipt">补打小票</string>
    <string name="collectioning">收款中</string>
    <string name="collection_cancel">取消收款</string>
    <string name="collection_fail">收款失败</string>
    <string name="fail_reason_colon">失败原因：</string>
    <string name="collection_again">重新收款</string>
    <string name="collection_stored_card">储值卡收款</string>
    <string name="collection_combination">组合收款</string>
    <string name="input_money">请输入金额</string>
    <string name="place_scan_customer_payment_code">请扫描顾客付款码完成收款</string>
    <string name="consumption_total_colon">消费金额：</string>
    <string name="place_connect_camera">请先连接摄像头</string>
    <string name="camera_fail">获取相机失败</string>
    <string name="payment_face_tips0">识别中</string>
    <string name="payment_face_tips1">请保持框内一个人</string>
    <string name="payment_face_tips2">请将脸部置于中间</string>
    <string name="payment_face_tips3">人脸识别支付中</string>
    <string name="online_collection_combination">线上组合收款</string>
    <string name="pay_code">支付码</string>

    <!--权限-->
    <string name="authorize_remind">零售版收银机授权提醒</string>
    <string name="need_access_colon">零售版收银机需要访问：</string>
    <string name="agree_and_continue">同意并继续</string>
    <string name="disagree">不同意</string>
    <string name="do_not_remind_again">不再提醒</string>
    <string name="permission_content_camera_and_read_write">1.相机权限：为了让您能够拍摄照片和视频，我们请求相机权限。只有当您使用相机功能时，我们才会请求此权限，并且我们会严格遵守相关的隐私政策。" +
                        "\n2.读写权限：我们需要读写存储权限以便于保存您的文件和数据。我们承诺，我们不会查看或分享您的任何私人信息，并且我们将采取一切必要的安全措施来保护您的数据。</string>
    <string name="permission_content_location">位置权限：我们希望获得您的位置信息以便为您提供更准确的服务。如果您同意，我们将使用您的位置信息来定位您所在的位置，并向您推荐附近的地点和服务。</string>
    <string name="permission_content_camera">相机权限：为了让您能够拍摄照片和视频，我们请求相机权限。只有当您使用相机功能时，我们才会请求此权限，并且我们会严格遵守相关的隐私政策。</string>
    <string name="permission_content_read_write">读写权限：我们需要读写存储权限以便于保存您的文件和数据。我们承诺，我们不会查看或分享您的任何私人信息，并且我们将采取一切必要的安全措施来保护您的数据。</string>
    <string name="permission_content_call">打电话权限：为了让您能够使用我们的应用程序拨打电话，我们需要获得电话权限。我们承诺，我们不会监听您的通话或记录您的通话内容，并且我们将采取必要的措施来保护您的隐私。</string>
    <string name="permission_content_phone">电话信息权限：我们需要访问您的电话信息以便于提供更好的服务。例如，我们可以向您推荐与您联系的人或向您发送有关您已接电话的提醒。我们承诺，我们不会泄露您的任何私人信息，并且我们将采取一切必要的安全措施来保护您的数据。</string>
    <string name="permission_content_bluetooth">蓝牙权限：我们需要访问您的蓝牙以便于提供更好的服务。例如，我们可以蓝牙连接打印机打印小票。</string>
    <string name="no_permission">无权限</string>

    <!--选择图片-->
    <string name="camera">拍照</string>
    <string name="album_select">相册选择</string>

    <!--无码商品-->
    <string name="no_barcode_weight">无码称重</string>
    <string name="no_barcode_goods">无码商品</string>
    <string name="no_barcode_weight_add">添加无码称重</string>
    <string name="no_barcode_goods_add">添加无码商品</string>
    <string name="count_colon">数量：</string>
    <string name="price_colon">单价：</string>
    <string name="total_colon">总价：</string>
    <string name="yuan_goods">元商品</string>
    <!--*****************************弹窗end*****************************-->

    <!--SmartRefreshLayout列表start-->
    <string name="srl_header_failed">刷新失败</string>
    <string name="srl_header_finish">刷新完成</string>
    <string name="srl_header_loading">等待底部加载完成…</string>
    <string name="srl_header_refreshing">正在刷新…</string>
    <string name="srl_header_release">释放立即刷新</string>
    <string name="srl_header_pulling">下拉可以刷新</string>
    <string name="srl_header_secondary">释放进入二楼</string>
    <string name="srl_header_update">上次更新 M-d HH:mm</string>
    <string name="srl_footer_failed">加载失败</string>
    <string name="srl_footer_finish">加载完成</string>
    <string name="srl_footer_loading">正在加载…</string>
    <string name="srl_footer_nothing">没有更多数据了</string>
    <string name="srl_footer_pulling">上拉加载更多</string>
    <string name="srl_footer_refreshing">等待头部刷新完成…</string>
    <string name="srl_footer_release">释放立即加载</string>
    <!--SmartRefreshLayout列表end-->

    <!--*****************************启动页start*****************************-->
    <string name="launch_tips">数据准备开启中</string>
    <string name="technical_support">山东影响力智能科技有限公司提供技术支持 TEL：400-7688-365</string>
    <!--*****************************启动页end*****************************-->
</resources>