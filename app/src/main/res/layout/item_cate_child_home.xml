<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp48">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="" />

        <TextView
            android:id="@+id/tvItemDefault"
            style="@style/text_12_999"
            android:padding="@dimen/dp10"
            android:text="@string/cate_default"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/ivItemEdit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_edit002"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/ivItemDel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_del001"
            android:visibility="gone" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/color_line" />

</LinearLayout>