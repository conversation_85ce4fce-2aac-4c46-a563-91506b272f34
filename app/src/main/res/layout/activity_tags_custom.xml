<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/green">

        <ImageView
            android:id="@+id/ivLogo"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingHorizontal="@dimen/dp10"
            android:src="@mipmap/ic_logo004" />

        <TextView
            style="@style/text_18_white"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text="@string/tags_template"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/linDate"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toStartOf="@+id/tvCashier"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10">

            <TextClock
                style="@style/text_12_white"
                android:format24Hour="MM月dd日 HH:mm"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp2"
                android:background="@drawable/shape_black_tm_5"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp1">

                <ImageView
                    android:id="@+id/ivWifi"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5"
                    android:src="@mipmap/ic_network001" />

                <TextView
                    android:id="@+id/tvWifi"
                    style="@style/text_10_white"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="网络正常" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tvCashier"
            style="@style/text_14_green"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/white"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp20"
            android:text="@string/cashier_table"
            android:textStyle="bold"
            app:drawableLeftCompat="@mipmap/ic_cashier001" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="@dimen/dp10"
        android:layout_weight="1"
        android:background="@drawable/shape_white_5"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp12">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp30"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:text="@string/tags_size"
                    tools:ignore="NestedWeights" />

                <TextView
                    style="@style/text_12_black"
                    android:text="@string/mm"
                    tools:ignore="NestedWeights" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <TextView
                    style="@style/text_12_black"
                    android:layout_marginEnd="@dimen/dp6"
                    android:text="@string/width" />

                <EditText
                    style="@style/text_12_black"
                    android:layout_width="@dimen/dp48"
                    android:layout_marginEnd="@dimen/dp10"
                    android:background="@drawable/shape_d8_kuang_f0_5"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp8"
                    android:paddingVertical="@dimen/dp5"
                    android:text="0" />

                <TextView
                    style="@style/text_12_black"
                    android:layout_marginEnd="@dimen/dp6"
                    android:text="@string/height" />

                <EditText
                    style="@style/text_12_black"
                    android:layout_width="@dimen/dp48"
                    android:background="@drawable/shape_d8_kuang_f0_5"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp8"
                    android:paddingVertical="@dimen/dp5"
                    android:text="100" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp30"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:gravity="center_vertical"
                    android:padding="@dimen/dp10"
                    android:text="@string/tags_col"
                    app:drawableRightCompat="@mipmap/ic_balance001"
                    tools:ignore="NestedWeights" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    tools:ignore="NestedWeights" />

                <EditText
                    style="@style/text_12_black"
                    android:layout_width="@dimen/dp140"
                    android:background="@drawable/shape_d8_kuang_f0_5"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="0" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:gravity="center_vertical"
                    android:padding="@dimen/dp10"
                    android:text="@string/print_count"
                    app:drawableRightCompat="@mipmap/ic_balance001" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    tools:ignore="NestedWeights" />

                <EditText
                    style="@style/text_12_black"
                    android:layout_width="@dimen/dp140"
                    android:background="@drawable/shape_d8_kuang_f0_5"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="0" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp6"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp30"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:text="@string/tags_spacing"
                    tools:ignore="NestedWeights" />

                <TextView
                    style="@style/text_12_black"
                    android:text="@string/mm"
                    tools:ignore="NestedWeights" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <TextView
                    style="@style/text_12_black"
                    android:layout_marginEnd="@dimen/dp6"
                    android:text="@string/width" />

                <EditText
                    style="@style/text_12_black"
                    android:layout_width="@dimen/dp48"
                    android:layout_marginEnd="@dimen/dp10"
                    android:background="@drawable/shape_d8_kuang_f0_5"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp8"
                    android:paddingVertical="@dimen/dp5"
                    android:text="0" />

                <TextView
                    style="@style/text_12_black"
                    android:layout_marginEnd="@dimen/dp6"
                    android:text="@string/height" />

                <EditText
                    style="@style/text_12_black"
                    android:layout_width="@dimen/dp48"
                    android:background="@drawable/shape_d8_kuang_f0_5"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp8"
                    android:paddingVertical="@dimen/dp5"
                    android:text="100" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp30"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:gravity="center_vertical"
                    android:padding="@dimen/dp10"
                    android:text="@string/print_concentration"
                    app:drawableRightCompat="@mipmap/ic_balance001"
                    tools:ignore="NestedWeights" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    tools:ignore="NestedWeights" />

                <EditText
                    style="@style/text_12_black"
                    android:layout_width="@dimen/dp140"
                    android:background="@drawable/shape_d8_kuang_f0_5"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="0" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:gravity="center_vertical"
                    android:padding="@dimen/dp10"
                    android:text="@string/resolution_ratio"
                    app:drawableRightCompat="@mipmap/ic_balance001" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    tools:ignore="NestedWeights" />

                <EditText
                    style="@style/text_12_black"
                    android:layout_width="@dimen/dp140"
                    android:background="@drawable/shape_d8_kuang_f0_5"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="0" />

            </LinearLayout>

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="@dimen/dp10"
            android:background="@color/color_line" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp16"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp30"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:layout_marginEnd="@dimen/dp8"
                    android:text="@string/content_location" />

                <TextView
                    style="@style/text_12_999"
                    android:text="@string/content_location_tips" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp30"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:layout_marginEnd="@dimen/dp8"
                    android:text="@string/content_custom" />

                <TextView
                    style="@style/text_12_999"
                    android:text="@string/content_location_tips" />

            </LinearLayout>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="@dimen/dp8"
                    android:text="@string/goods_list" />

                <TextView
                    style="@style/text_12_white"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:background="@drawable/shape_green_5"
                    android:paddingHorizontal="@dimen/dp16"
                    android:paddingVertical="@dimen/dp8"
                    android:text="@string/goods_add"
                    android:textStyle="bold" />

            </RelativeLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp12"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp30"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:text="@string/goods_name"
                            tools:ignore="NestedWeights" />

                        <TextView
                            style="@style/text_12_black"
                            android:layout_marginEnd="@dimen/dp6"
                            android:text="X" />

                        <EditText
                            style="@style/text_12_black"
                            android:layout_width="@dimen/dp48"
                            android:layout_marginEnd="@dimen/dp10"
                            android:background="@drawable/shape_d8_kuang_f0_5"
                            android:gravity="center"
                            android:paddingHorizontal="@dimen/dp8"
                            android:paddingVertical="@dimen/dp5"
                            android:text="0" />

                        <TextView
                            style="@style/text_12_black"
                            android:layout_marginEnd="@dimen/dp6"
                            android:text="Y" />

                        <EditText
                            style="@style/text_12_black"
                            android:layout_width="@dimen/dp48"
                            android:background="@drawable/shape_d8_kuang_f0_5"
                            android:gravity="center"
                            android:paddingHorizontal="@dimen/dp8"
                            android:paddingVertical="@dimen/dp5"
                            android:text="100" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp30"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <EditText
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_marginEnd="@dimen/dp12"
                            android:layout_weight="1"
                            android:background="@drawable/shape_d8_kuang_f0_5"
                            android:ellipsize="end"
                            android:hint="@string/input_content"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5"
                            android:text=""
                            tools:ignore="NestedWeights" />

                        <TextView
                            style="@style/text_12_black"
                            android:layout_marginEnd="@dimen/dp6"
                            android:text="X" />

                        <EditText
                            style="@style/text_12_black"
                            android:layout_width="@dimen/dp48"
                            android:layout_marginEnd="@dimen/dp10"
                            android:background="@drawable/shape_d8_kuang_f0_5"
                            android:gravity="center"
                            android:paddingHorizontal="@dimen/dp8"
                            android:paddingVertical="@dimen/dp5"
                            android:text="0" />

                        <TextView
                            style="@style/text_12_black"
                            android:layout_marginEnd="@dimen/dp6"
                            android:text="Y" />

                        <EditText
                            style="@style/text_12_black"
                            android:layout_width="@dimen/dp48"
                            android:background="@drawable/shape_d8_kuang_f0_5"
                            android:gravity="center"
                            android:paddingHorizontal="@dimen/dp8"
                            android:paddingVertical="@dimen/dp5"
                            android:text="100" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp16"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp30"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:text="@string/goods_sale"
                            tools:ignore="NestedWeights" />

                        <TextView
                            style="@style/text_12_black"
                            android:layout_marginEnd="@dimen/dp6"
                            android:text="X" />

                        <EditText
                            style="@style/text_12_black"
                            android:layout_width="@dimen/dp48"
                            android:layout_marginEnd="@dimen/dp10"
                            android:background="@drawable/shape_d8_kuang_f0_5"
                            android:gravity="center"
                            android:paddingHorizontal="@dimen/dp8"
                            android:paddingVertical="@dimen/dp5"
                            android:text="0" />

                        <TextView
                            style="@style/text_12_black"
                            android:layout_marginEnd="@dimen/dp6"
                            android:text="Y" />

                        <EditText
                            style="@style/text_12_black"
                            android:layout_width="@dimen/dp48"
                            android:background="@drawable/shape_d8_kuang_f0_5"
                            android:gravity="center"
                            android:paddingHorizontal="@dimen/dp8"
                            android:paddingVertical="@dimen/dp5"
                            android:text="100" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp30"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <EditText
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_marginEnd="@dimen/dp12"
                            android:layout_weight="1"
                            android:background="@drawable/shape_d8_kuang_f0_5"
                            android:ellipsize="end"
                            android:hint="@string/input_content"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5"
                            android:text="" />

                        <TextView
                            style="@style/text_12_black"
                            android:layout_marginEnd="@dimen/dp6"
                            android:text="X" />

                        <EditText
                            style="@style/text_12_black"
                            android:layout_width="@dimen/dp48"
                            android:layout_marginEnd="@dimen/dp10"
                            android:background="@drawable/shape_d8_kuang_f0_5"
                            android:gravity="center"
                            android:paddingHorizontal="@dimen/dp8"
                            android:paddingVertical="@dimen/dp5"
                            android:text="0" />

                        <TextView
                            style="@style/text_12_black"
                            android:layout_marginEnd="@dimen/dp6"
                            android:text="Y" />

                        <EditText
                            style="@style/text_12_black"
                            android:layout_width="@dimen/dp48"
                            android:background="@drawable/shape_d8_kuang_f0_5"
                            android:gravity="center"
                            android:paddingHorizontal="@dimen/dp8"
                            android:paddingVertical="@dimen/dp5"
                            android:text="100" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp16"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp30"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:text="@string/goods_barcode"
                            tools:ignore="NestedWeights" />

                        <TextView
                            style="@style/text_12_black"
                            android:layout_marginEnd="@dimen/dp6"
                            android:text="X" />

                        <EditText
                            style="@style/text_12_black"
                            android:layout_width="@dimen/dp48"
                            android:layout_marginEnd="@dimen/dp10"
                            android:background="@drawable/shape_d8_kuang_f0_5"
                            android:gravity="center"
                            android:paddingHorizontal="@dimen/dp8"
                            android:paddingVertical="@dimen/dp5"
                            android:text="0" />

                        <TextView
                            style="@style/text_12_black"
                            android:layout_marginEnd="@dimen/dp6"
                            android:text="Y" />

                        <EditText
                            style="@style/text_12_black"
                            android:layout_width="@dimen/dp48"
                            android:background="@drawable/shape_d8_kuang_f0_5"
                            android:gravity="center"
                            android:paddingHorizontal="@dimen/dp8"
                            android:paddingVertical="@dimen/dp5"
                            android:text="100" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp30"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <EditText
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_marginEnd="@dimen/dp12"
                            android:layout_weight="1"
                            android:background="@drawable/shape_d8_kuang_f0_5"
                            android:ellipsize="end"
                            android:hint="@string/input_content"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5"
                            android:text="" />

                        <TextView
                            style="@style/text_12_black"
                            android:layout_marginEnd="@dimen/dp6"
                            android:text="X" />

                        <EditText
                            style="@style/text_12_black"
                            android:layout_width="@dimen/dp48"
                            android:layout_marginEnd="@dimen/dp10"
                            android:background="@drawable/shape_d8_kuang_f0_5"
                            android:gravity="center"
                            android:paddingHorizontal="@dimen/dp8"
                            android:paddingVertical="@dimen/dp5"
                            android:text="0" />

                        <TextView
                            style="@style/text_12_black"
                            android:layout_marginEnd="@dimen/dp6"
                            android:text="Y" />

                        <EditText
                            style="@style/text_12_black"
                            android:layout_width="@dimen/dp48"
                            android:background="@drawable/shape_d8_kuang_f0_5"
                            android:gravity="center"
                            android:paddingHorizontal="@dimen/dp8"
                            android:paddingVertical="@dimen/dp5"
                            android:text="100" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_f0_5"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp16"
        android:paddingVertical="@dimen/dp10">

        <TextView
            style="@style/text_14_white"
            android:layout_marginEnd="@dimen/dp16"
            android:background="@drawable/shape_orange_5"
            android:paddingHorizontal="@dimen/dp24"
            android:paddingVertical="@dimen/dp10"
            android:text="@string/print_test"
            android:textStyle="bold" />

        <TextView
            style="@style/text_12_999"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:text="@string/print_test_tips"
            app:drawableLeftCompat="@mipmap/ic_tips002" />


        <TextView
            style="@style/text_14_white"
            android:layout_marginEnd="@dimen/dp16"
            android:background="@drawable/shape_orange_5"
            android:paddingHorizontal="@dimen/dp24"
            android:paddingVertical="@dimen/dp10"
            android:text="@string/template_save"
            android:textStyle="bold" />

        <TextView
            style="@style/text_14_white"
            android:background="@drawable/shape_green_5"
            android:paddingHorizontal="@dimen/dp24"
            android:paddingVertical="@dimen/dp10"
            android:text="@string/confirm_print"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>