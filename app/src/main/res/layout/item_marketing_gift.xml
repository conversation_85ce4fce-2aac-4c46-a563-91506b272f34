<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="@dimen/dp10">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvItemName"
                style="@style/text_12_black"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="" />

            <TextView
                android:id="@+id/tvItemBarcode"
                style="@style/text_12_666"
                android:text="" />

            <TextView
                android:id="@+id/tvItemPrice"
                style="@style/text_12_666"
                android:layout_marginTop="@dimen/dp10"
                android:text="@string/price_old_colon" />

        </LinearLayout>

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/dp10"
            android:background="@color/color_line" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvItemName0"
                style="@style/text_12_black"
                android:text="" />

            <TextView
                android:id="@+id/tvItemPrice0"
                style="@style/text_12_666"
                android:layout_marginTop="@dimen/dp10"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="" />

        </LinearLayout>

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/dp10"
            android:background="@color/color_line" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvItemName1"
                style="@style/text_12_black"
                android:text="" />

            <TextView
                android:id="@+id/tvItemPrice1"
                style="@style/text_12_666"
                android:layout_marginTop="@dimen/dp10"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="" />

        </LinearLayout>

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/dp10"
            android:background="@color/color_line" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvItemName2"
                style="@style/text_12_black"
                android:text="" />

            <TextView
                android:id="@+id/tvItemPrice2"
                style="@style/text_12_666"
                android:layout_marginTop="@dimen/dp10"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="" />

        </LinearLayout>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/color_line" />

</LinearLayout>