<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/consumption_records"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp10"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/linDialogStartDate"
            android:layout_width="@dimen/dp125"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvDialogStartDate"
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:hint="@string/date_start"
                android:maxLines="1"
                android:paddingVertical="@dimen/dp6"
                android:text=""
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivDialogStartDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_arrow002" />

        </LinearLayout>

        <TextView
            style="@style/text_16_999"
            android:layout_marginHorizontal="@dimen/dp3"
            android:text="~" />

        <LinearLayout
            android:id="@+id/linDialogEndDate"
            android:layout_width="@dimen/dp125"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvDialogEndDate"
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:hint="@string/date_end"
                android:maxLines="1"
                android:paddingVertical="@dimen/dp6"
                android:text=""
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivDialogEndDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_arrow002" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/shape_f5_top_5"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp5">

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/order_no" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/order_total" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/pay_type" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/order_time" />

    </LinearLayout>

    <include
        android:id="@+id/vSmartrefreshlayout"
        layout="@layout/layout_smartrefreshlayout" />

</LinearLayout>