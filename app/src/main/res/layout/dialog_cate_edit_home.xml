<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvDialogTitle"
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text=""
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <TextView
        style="@style/text_14_black"
        android:layout_marginStart="@dimen/dp10"
        android:text="@string/cate_name" />

    <EditText
        android:id="@+id/etDialogName"
        style="@style/text_14_black"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="@dimen/dp10"
        android:layout_marginTop="@dimen/dp5"
        android:background="@drawable/shape_d8_kuang_5"
        android:hint="@string/input_cate_name"
        android:inputType="text"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp8" />

    <LinearLayout
        android:id="@+id/linDialogImg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            style="@style/text_14_black"
            android:layout_marginStart="@dimen/dp10"
            android:text="@string/cate_icon" />

        <ImageView
            android:id="@+id/ivDialogImg"
            android:layout_width="@dimen/dp60"
            android:layout_height="@dimen/dp60"
            android:layout_marginHorizontal="@dimen/dp10"
            android:layout_marginTop="@dimen/dp5"
            android:src="@mipmap/ic_camera001" />

    </LinearLayout>

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <TextView
        android:id="@+id/tvDialogConfirm"
        style="@style/text_14_white"
        android:layout_width="@dimen/dp200"
        android:layout_gravity="center"
        android:layout_marginVertical="@dimen/dp10"
        android:background="@drawable/shape_green_5"
        android:gravity="center"
        android:paddingVertical="@dimen/dp8"
        android:text="@string/save" />

</LinearLayout>