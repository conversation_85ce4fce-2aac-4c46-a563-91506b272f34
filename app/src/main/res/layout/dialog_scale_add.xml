<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/scale_add"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:background="@drawable/shape_green_kuang_5">

        <EditText
            android:id="@+id/etDialogName"
            style="@style/text_14_black"
            android:layout_width="match_parent"
            android:background="@null"
            android:gravity="center"
            android:hint="@string/input_scale_name"
            android:inputType="text"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/dp24"
            android:paddingVertical="@dimen/dp8" />

        <ImageView
            android:id="@+id/ivDialogClear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp6"
            android:src="@mipmap/ic_close001"
            android:visibility="gone" />

    </RelativeLayout>

    <TextView
        android:id="@+id/tvDialogConfirm"
        style="@style/text_14_white"
        android:layout_width="@dimen/dp245"
        android:layout_gravity="center"
        android:layout_marginVertical="@dimen/dp16"
        android:background="@drawable/shape_green_5"
        android:gravity="center"
        android:paddingVertical="@dimen/dp10"
        android:text="@string/confirm_add" />

</LinearLayout>