<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/color_line" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp5">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/ivItemImg"
                android:layout_width="@dimen/dp24"
                android:layout_height="@dimen/dp24"
                android:layout_marginEnd="@dimen/dp5"
                android:src="@mipmap/ic_default_img" />

            <TextView
                android:id="@+id/tvItemName"
                style="@style/text_12_black"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvItemCount"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="0" />

        <TextView
            android:id="@+id/tvItemDelivery"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="0" />

        <TextView
            android:id="@+id/tvItemPrice"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="0" />

        <TextView
            android:id="@+id/tvItemTotal"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="0" />

        <TextView
            android:id="@+id/tvItemSuggest"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="0" />

        <TextView
            android:id="@+id/tvItemReal"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="0" />

        <TextView
            android:id="@+id/tvItemOperate"
            style="@style/text_12_black"
            android:layout_width="@dimen/dp50"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5"
            android:text="" />

    </LinearLayout>

</LinearLayout>