<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:gravity="center"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp12"
            android:text="@string/select_restock_specs"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvDialog"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="@dimen/dp5"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    <TextView
        android:id="@+id/tvDialogConfirm"
        style="@style/text_14_white"
        android:layout_width="@dimen/dp120"
        android:layout_marginTop="@dimen/dp10"
        android:layout_marginBottom="@dimen/dp20"
        android:background="@drawable/shape_green_5"
        android:gravity="center"
        android:paddingVertical="@dimen/dp8"
        android:text="@string/confirm"
        android:textStyle="bold" />

</LinearLayout>