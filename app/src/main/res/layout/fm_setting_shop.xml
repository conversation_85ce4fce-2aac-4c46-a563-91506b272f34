<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_f5_top_5">

        <TextView
            style="@style/text_14_black"
            android:layout_height="@dimen/dp34"
            android:background="@drawable/shape_white_topleft_5"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp16"
            android:text="@string/setting_shop"
            android:textStyle="bold" />

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_white_bottom_5">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/shop_id" />

                <TextView
                    android:id="@+id/tvShopID"
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="3"
                    android:background="@drawable/shape_d8_kuang_f0_5"
                    android:hint=""
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6"
                    tools:ignore="SpeakableTextPresentCheck,TouchTargetSizeCheck" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="2" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/shop_name" />

                <EditText
                    android:id="@+id/etShopName"
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="3"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:ellipsize="end"
                    android:hint="@string/input_shop_name"
                    android:imeOptions="actionDone"
                    android:inputType="text"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6"
                    tools:ignore="SpeakableTextPresentCheck,TouchTargetSizeCheck" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="2" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/shop_address" />

                <EditText
                    android:id="@+id/etShopAddress"
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="3"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:ellipsize="end"
                    android:hint="@string/input_shop_address"
                    android:imeOptions="actionDone"
                    android:inputType="text"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6"
                    tools:ignore="SpeakableTextPresentCheck,TouchTargetSizeCheck" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="2" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/machine_unique" />

                <TextView
                    android:id="@+id/tvMachineUnique"
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="3"
                    android:background="@drawable/shape_d8_kuang_f0_5"
                    android:hint=""
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6"
                    tools:ignore="SpeakableTextPresentCheck,TouchTargetSizeCheck" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="2" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/staff_unique" />

                <TextView
                    android:id="@+id/tvStaffUnique"
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="3"
                    android:background="@drawable/shape_d8_kuang_f0_5"
                    android:hint=""
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6"
                    tools:ignore="SpeakableTextPresentCheck,TouchTargetSizeCheck" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="2" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/machine_mac" />

                <TextView
                    android:id="@+id/tvMachineMAC"
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="3"
                    android:background="@drawable/shape_d8_kuang_f0_5"
                    android:hint=""
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6"
                    tools:ignore="SpeakableTextPresentCheck,TouchTargetSizeCheck" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="2" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/contact_mobile" />

                <TextView
                    android:id="@+id/tvMobile"
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="3"
                    android:background="@drawable/shape_d8_kuang_f0_5"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="2" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/member_unique_four" />

                <EditText
                    android:id="@+id/etMemberUnique"
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="3"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:ellipsize="end"
                    android:hint="@string/input_member_mobile"
                    android:imeOptions="actionDone"
                    android:inputType="number"
                    android:maxLength="4"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6"
                    tools:ignore="SpeakableTextPresentCheck,TouchTargetSizeCheck" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="2" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>