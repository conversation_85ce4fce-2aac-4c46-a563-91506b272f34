<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/this_login_account"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp20"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvDialogName"
            style="@style/text_14_black"
            android:text="@string/staff_name_colon" />

        <TextView
            android:id="@+id/tvDialogMobile"
            style="@style/text_14_black"
            android:layout_marginTop="@dimen/dp10"
            android:text="@string/contact_mobile_colon" />

        <TextView
            android:id="@+id/tvDialogAccount"
            style="@style/text_14_black"
            android:layout_marginTop="@dimen/dp10"
            android:text="@string/login_account_colon" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_black"
                android:text="@string/login_pwd_colon" />

            <TextView
                android:id="@+id/tvDialogPwd"
                style="@style/text_14_red"
                android:paddingVertical="@dimen/dp10"
                android:paddingEnd="@dimen/dp10"
                android:text="@string/pwd_change" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvDialogShopName"
            style="@style/text_14_black"
            android:text="@string/belong_shop_colon" />

        <TextView
            android:id="@+id/tvDialogShopUnique"
            style="@style/text_14_black"
            android:layout_marginTop="@dimen/dp10"
            android:text="@string/shop_no_colon" />

        <TextView
            android:id="@+id/tvDialogPosition"
            style="@style/text_14_black"
            android:layout_marginTop="@dimen/dp10"
            android:text="@string/staff_position_colon" />

        <TextView
            android:id="@+id/tvDialogConfirm"
            style="@style/text_14_white"
            android:layout_gravity="center"
            android:layout_marginVertical="@dimen/dp20"
            android:background="@drawable/shape_orange_5"
            android:paddingHorizontal="@dimen/dp20"
            android:paddingVertical="@dimen/dp8"
            android:text="@string/shift_handover"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>