<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvDialogTitle"
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/cate_home_pc_edit"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <EditText
        android:id="@+id/etDialog"
        style="@style/text_14_black"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="@dimen/dp150"
        android:background="@drawable/shape_d8_kuang_5"
        android:gravity="center"
        android:hint="@string/input_cate_name"
        android:inputType="text"
        android:maxLines="1"
        android:padding="@dimen/dp8" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp66"
        android:layout_marginBottom="@dimen/dp16"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvDialogDel"
            style="@style/text_14_white"
            android:layout_width="@dimen/dp200"
            android:layout_gravity="center"
            android:layout_marginHorizontal="@dimen/dp8"
            android:background="@drawable/shape_red_5"
            android:gravity="center"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/del" />

        <TextView
            android:id="@+id/tvDialogConfirm"
            style="@style/text_14_white"
            android:layout_width="@dimen/dp200"
            android:layout_gravity="center"
            android:layout_marginHorizontal="@dimen/dp8"
            android:background="@drawable/shape_green_5"
            android:gravity="center"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/save" />

    </LinearLayout>

</LinearLayout>