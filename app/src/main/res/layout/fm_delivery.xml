<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_5"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dp20"
    android:paddingVertical="@dimen/dp10">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_14_black"
            android:text="@string/delivery_type_colon" />

        <ImageView
            android:id="@+id/ivType0"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@drawable/selector_checkbox003" />

        <TextView
            style="@style/text_14_black"
            android:layout_marginEnd="@dimen/dp40"
            android:text="@string/delivery_type0" />

        <ImageView
            android:id="@+id/ivType1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@drawable/selector_checkbox003"
            android:visibility="gone" />

        <TextView
            style="@style/text_14_black"
            android:text="@string/delivery_type1"
            android:visibility="gone" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_14_black"
            android:text="@string/delivery_range_colon" />

        <EditText
            android:id="@+id/etRange"
            style="@style/text_14_black"
            android:layout_width="@dimen/dp160"
            android:layout_marginEnd="@dimen/dp10"
            android:background="@drawable/shape_d8_kuang_5"
            android:hint="@string/input"
            android:imeOptions="actionDone"
            android:inputType="numberDecimal"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5" />

        <TextView
            style="@style/text_14_black"
            android:text="@string/km" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_14_black"
            android:text="@string/delivery_range_colon"
            android:visibility="invisible" />

        <TextView
            style="@style/text_12_999"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:text="@string/delivery_range_tips"
            app:drawableLeftCompat="@mipmap/ic_tips001" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp20"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_14_black"
            android:text="@string/delivery_start_order_colon" />

        <EditText
            android:id="@+id/etStartOrder"
            style="@style/text_14_black"
            android:layout_width="@dimen/dp160"
            android:layout_marginEnd="@dimen/dp10"
            android:background="@drawable/shape_d8_kuang_5"
            android:hint="@string/input"
            android:imeOptions="actionDone"
            android:inputType="numberDecimal"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5" />

        <TextView
            style="@style/text_14_black"
            android:text="@string/yuan" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_14_black"
            android:text="@string/delivery_start_order_colon"
            android:visibility="invisible" />

        <TextView
            style="@style/text_12_999"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:text="@string/delivery_start_order_tips"
            app:drawableLeftCompat="@mipmap/ic_tips001" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp20"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_14_black"
            android:text="@string/delivery_free_colon" />

        <EditText
            android:id="@+id/etFree"
            style="@style/text_14_black"
            android:layout_width="@dimen/dp160"
            android:layout_marginEnd="@dimen/dp10"
            android:background="@drawable/shape_d8_kuang_5"
            android:hint="@string/input"
            android:imeOptions="actionDone"
            android:inputType="numberDecimal"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5" />

        <TextView
            style="@style/text_14_black"
            android:text="@string/yuan" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_14_black"
            android:text="@string/delivery_free_colon"
            android:visibility="invisible" />

        <TextView
            style="@style/text_12_999"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:text="@string/delivery_free_tips"
            app:drawableLeftCompat="@mipmap/ic_tips001" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp20"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_14_black"
            android:text="@string/estimate_duration_colon" />

        <EditText
            android:id="@+id/etDuration"
            style="@style/text_14_black"
            android:layout_width="@dimen/dp160"
            android:layout_marginEnd="@dimen/dp10"
            android:background="@drawable/shape_d8_kuang_5"
            android:hint="@string/input"
            android:imeOptions="actionDone"
            android:inputType="number"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5" />

        <TextView
            style="@style/text_14_black"
            android:text="@string/minute" />

    </LinearLayout>

</LinearLayout>