<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginTop="@dimen/dp16"
            android:layout_marginBottom="@dimen/dp12"
            android:text="@string/member_add"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp56"
                android:gravity="center_vertical">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    tools:ignore="NestedWeights,RtlHardcoded">

                    <TextView
                        style="@style/text_12_red"
                        android:text="*" />

                    <TextView
                        style="@style/text_12_black"
                        android:text="@string/member_no" />

                </LinearLayout>

                <EditText
                    android:id="@+id/etDialogNo"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="2.5"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:hint="@string/input_member_no"
                    android:inputType="text"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp56"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    tools:ignore="NestedWeights,RtlHardcoded">

                    <TextView
                        style="@style/text_12_red"
                        android:text="*" />

                    <TextView
                        style="@style/text_12_black"
                        android:text="@string/member_mobile" />

                </LinearLayout>

                <EditText
                    android:id="@+id/etDialogMobile"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="2.5"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:hint="@string/input_member_mobile"
                    android:inputType="phone"
                    android:maxLength="11"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp56"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical">

                <TextView
                    style="@style/text_12_red"
                    android:text="*" />

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:text="@string/member_name" />

                <EditText
                    android:id="@+id/etDialogName"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="2.5"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:hint="@string/input_member_name"
                    android:inputType="text"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp56"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:text="@string/member_balance" />

                <EditText
                    android:id="@+id/etDialogBalance"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="2.5"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:hint="@string/input_member_balance"
                    android:inputType="numberDecimal"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp56"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:visibility="gone">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:text="@string/debt_limit" />

                <EditText
                    android:id="@+id/etDialogLimit"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="2.5"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:hint="@string/input_debt_limit"
                    android:inputType="numberDecimal"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5" />

            </LinearLayout>

            <!--会员类型写死：会员储值卡-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp56"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:text="@string/member_type" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="2.5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvDialogType0"
                        style="@style/text_12_green"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_green_tm_5"
                        android:gravity="center"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/member_type0"
                        tools:ignore="NestedWeights" />

                    <TextView
                        android:id="@+id/tvDialogType1"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:gravity="center"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/member_type1" />

                    <TextView
                        android:id="@+id/tvDialogType2"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:gravity="center"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/member_type2" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp56"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:text="@string/member_sex" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="2.5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvDialogSex0"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:gravity="center"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/sex0" />

                    <TextView
                        android:id="@+id/tvDialogSex1"
                        style="@style/text_12_green"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_green_tm_5"
                        android:gravity="center"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/sex1" />

                    <TextView
                        android:id="@+id/tvDialogSex2"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:gravity="center"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/sex2" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp56"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical">

                <TextView
                    style="@style/text_12_red"
                    android:text="*" />

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:text="@string/account_pwd" />

                <EditText
                    android:id="@+id/etDialogPwd"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="2.5"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:hint="@string/input_account_pwd"
                    android:inputType="text"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp56"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:text="@string/remarks_info" />

                <EditText
                    android:id="@+id/etDialogRemarks"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="2.5"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:hint="@string/input_remarks_info"
                    android:inputType="text"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvDialogConfirm"
                style="@style/text_14_white"
                android:layout_width="@dimen/dp200"
                android:layout_gravity="center_horizontal"
                android:layout_marginVertical="@dimen/dp20"
                android:background="@drawable/shape_green_5"
                android:gravity="center"
                android:paddingVertical="@dimen/dp8"
                android:text="@string/save"
                android:textStyle="bold" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>