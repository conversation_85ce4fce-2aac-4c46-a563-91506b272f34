<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/white">

        <ImageView
            android:id="@+id/ivBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_back002" />

        <TextView
            style="@style/text_16_black"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/ivBack"
            android:text="商品分类列表"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvTitle"
            style="@style/text_18_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text=""
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/linDate"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toStartOf="@+id/tvCashier"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10">

            <TextClock
                style="@style/text_12_black"
                android:format24Hour="MM月dd日 HH:mm"
                android:textStyle="bold" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvCashier"
            style="@style/text_14_white"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/green"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp20"
            android:text="@string/cashier_table"
            android:textStyle="bold"
            app:drawableLeftCompat="@mipmap/ic_cashier001" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/linSearch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp5"
        android:layout_marginTop="@dimen/dp46"
        android:background="@drawable/shape_white_5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="@dimen/dp240"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp10"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/etSearch"
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:background="@null"
                android:drawableStart="@mipmap/ic_search001"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:hint="搜索商品名称/条码"
                android:imeOptions="actionSearch"
                android:inputType="text"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp6" />

            <ImageView
                android:id="@+id/ivSearchClear"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp6"
                android:src="@mipmap/ic_close001"
                android:visibility="gone" />

        </LinearLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <TextView
            style="@style/text_12_black"
            android:layout_marginEnd="@dimen/dp20"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:text="0.00"
            app:drawableLeftCompat="@mipmap/ic_payment_img011" />

        <TextView
            style="@style/text_12_black"
            android:layout_marginEnd="@dimen/dp10"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:text="0.00"
            app:drawableLeftCompat="@mipmap/ic_payment_img011" />

        <TextView
            android:id="@+id/tvCredit"
            style="@style/text_12_black"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:padding="@dimen/dp10"
            android:text="赊销"
            app:drawableLeftCompat="@mipmap/ic_payment_img011" />

        <TextView
            android:id="@+id/tvOrder"
            style="@style/text_12_black"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:padding="@dimen/dp10"
            android:text="订单"
            app:drawableLeftCompat="@mipmap/ic_payment_img011" />

        <RelativeLayout
            android:id="@+id/relCart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <TextView
                style="@style/text_12_black"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:padding="@dimen/dp10"
                android:text="购物车"
                app:drawableLeftCompat="@mipmap/ic_payment_img011" />

            <TextView
                android:id="@+id/tvCartCount"
                style="@style/text_8_white"
                android:layout_width="@dimen/dp10"
                android:layout_height="@dimen/dp10"
                android:layout_marginStart="@dimen/dp10"
                android:layout_marginTop="@dimen/dp5"
                android:background="@drawable/shape_yuan_red"
                android:gravity="center"
                android:text="0" />

        </RelativeLayout>

        <TextView
            android:id="@+id/tvCoupons"
            style="@style/text_12_black"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:padding="@dimen/dp10"
            android:text="优惠券"
            app:drawableLeftCompat="@mipmap/ic_payment_img011" />

        <TextView
            style="@style/text_12_green"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:padding="@dimen/dp10"
            android:text="助农产品"
            app:drawableLeftCompat="@mipmap/ic_payment_img011" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/linSearch"
        android:layout_marginHorizontal="@dimen/dp5"
        android:layout_marginVertical="@dimen/dp5"
        android:background="@drawable/shape_f2_5"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp10"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="@dimen/dp120"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:paddingVertical="@dimen/dp6"
                    android:text="综合"
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="@dimen/dp120"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:paddingVertical="@dimen/dp6"
                    android:text="新品"
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="@dimen/dp120"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:paddingVertical="@dimen/dp6"
                    android:text="销量"
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="@dimen/dp120"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:paddingVertical="@dimen/dp6"
                    android:text="价格"
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

        </LinearLayout>

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/smartRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="@dimen/dp5">

            <com.scwang.smart.refresh.header.ClassicsHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:spanCount="4" />

                <LinearLayout
                    android:id="@+id/linEmpty"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/dp40"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/ivEmpty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_empty" />

                    <TextView
                        android:id="@+id/tvEmpty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp26"
                        android:textColor="@color/color_666"
                        android:textSize="@dimen/f14"
                        android:textStyle="bold" />

                </LinearLayout>

            </RelativeLayout>

            <com.scwang.smart.refresh.footer.ClassicsFooter
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    </LinearLayout>

</RelativeLayout>