<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/green">

        <ImageView
            android:id="@+id/ivLogo"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingHorizontal="@dimen/dp10"
            android:src="@mipmap/ic_logo004" />

        <LinearLayout
            android:layout_width="@dimen/dp200"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@id/ivLogo"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/etSearch"
                style="@style/text_12_333"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:background="@null"
                android:drawableStart="@mipmap/ic_search001"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:hint="@string/member_search"
                android:imeOptions="actionSearch"
                android:inputType="text"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp4"
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivSearchClear"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp4"
                android:src="@mipmap/ic_close001"
                android:visibility="gone" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvTitle"
            style="@style/text_18_white"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text="@string/member_manage"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/linDate"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toStartOf="@+id/tvCashier"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10">

            <TextClock
                style="@style/text_12_white"
                android:format24Hour="MM-dd HH:mm"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp2"
                android:background="@drawable/shape_black_tm_5"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp1">

                <ImageView
                    android:id="@+id/ivNetwork"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5" />

                <TextView
                    android:id="@+id/tvNetwork"
                    style="@style/text_10_white"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tvCashier"
            style="@style/text_14_green"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/white"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp20"
            android:text="@string/cashier_table"
            android:textStyle="bold"
            app:drawableLeftCompat="@mipmap/ic_cashier001" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dp41"
        android:orientation="horizontal">

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginVertical="@dimen/dp10"
            android:layout_marginStart="@dimen/dp10"
            android:layout_weight="2"
            android:background="@drawable/shape_white_5">

            <!--店内会员分析-->
            <LinearLayout
                android:id="@+id/linStatistic"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:padding="@dimen/dp10"
                android:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="@dimen/dp3"
                        android:layout_height="@dimen/dp15"
                        android:layout_marginEnd="@dimen/dp5"
                        android:background="@drawable/shape_green_2" />

                    <TextView
                        style="@style/text_16_black"
                        android:text="@string/member_analysis"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_green_tm_5"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingVertical="@dimen/dp5">

                        <TextView
                            android:id="@+id/tvTotalCount"
                            style="@style/text_12_black"
                            android:text="0"
                            android:textStyle="bold" />

                        <TextView
                            style="@style/text_10_666"
                            android:layout_marginTop="@dimen/dp5"
                            android:maxLines="1"
                            android:text="@string/member_total" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_orange_tm_5"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingVertical="@dimen/dp5">

                        <TextView
                            android:id="@+id/tvTotalStored"
                            style="@style/text_12_black"
                            android:text="0.00"
                            android:textStyle="bold" />

                        <TextView
                            style="@style/text_10_666"
                            android:layout_marginTop="@dimen/dp5"
                            android:maxLines="1"
                            android:text="@string/stored_total" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/shape_red_tm_5"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingVertical="@dimen/dp5">

                        <TextView
                            android:id="@+id/tvTotalCredit"
                            style="@style/text_12_black"
                            android:text="0.00"
                            android:textStyle="bold" />

                        <TextView
                            style="@style/text_10_666"
                            android:layout_marginTop="@dimen/dp5"
                            android:maxLines="1"
                            android:text="@string/credit_total" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp6"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="@dimen/dp6"
                        android:layout_weight="1"
                        android:background="@drawable/shape_blue_tm_5"
                        android:orientation="vertical">

                        <TextView
                            style="@style/text_10_black"
                            android:layout_marginStart="@dimen/dp8"
                            android:layout_marginTop="@dimen/dp5"
                            android:maxLines="1"
                            android:text="@string/proportion_member"
                            android:textStyle="bold" />

                        <com.github.mikephil.charting.charts.PieChart
                            android:id="@+id/chartType"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp84"
                            android:layout_marginStart="0dp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/shape_green_tm_5"
                        android:orientation="vertical">

                        <TextView
                            style="@style/text_10_black"
                            android:layout_marginStart="@dimen/dp8"
                            android:layout_marginTop="@dimen/dp5"
                            android:maxLines="1"
                            android:text="@string/proportion_stored_money"
                            android:textStyle="bold" />

                        <com.github.mikephil.charting.charts.HorizontalBarChart
                            android:id="@+id/chartStored"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp84" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp6"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp6"
                        android:layout_weight="1"
                        android:background="@drawable/shape_blue_tm_5"
                        android:orientation="vertical">

                        <TextView
                            style="@style/text_10_black"
                            android:layout_marginStart="@dimen/dp8"
                            android:layout_marginTop="@dimen/dp5"
                            android:maxLines="1"
                            android:text="@string/proportion_member_points"
                            android:textStyle="bold" />

                        <com.github.mikephil.charting.charts.PieChart
                            android:id="@+id/chartPoints"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp84"
                            android:layout_marginStart="0dp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:background="@drawable/shape_blue_tm_5"
                        android:orientation="vertical">

                        <TextView
                            style="@style/text_10_black"
                            android:layout_marginStart="@dimen/dp8"
                            android:layout_marginTop="@dimen/dp5"
                            android:maxLines="1"
                            android:text="@string/proportion_member_level"
                            android:textStyle="bold" />

                        <com.github.mikephil.charting.charts.PieChart
                            android:id="@+id/chartLevel"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dp130" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp6"
                    android:background="@drawable/shape_green_tm_5"
                    android:orientation="vertical">

                    <TextView
                        style="@style/text_10_black"
                        android:layout_marginStart="@dimen/dp8"
                        android:layout_marginTop="@dimen/dp5"
                        android:maxLines="1"
                        android:text="@string/proportion_member_time"
                        android:textStyle="bold" />

                    <com.github.mikephil.charting.charts.LineChart
                        android:id="@+id/chartTime"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp120" />

                </LinearLayout>

            </LinearLayout>

            <!--会员详情-->
            <LinearLayout
                android:id="@+id/linInfo"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:padding="@dimen/dp10"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical">

                    <View
                        android:layout_width="@dimen/dp3"
                        android:layout_height="@dimen/dp15"
                        android:layout_marginEnd="@dimen/dp6"
                        android:background="@drawable/shape_green_2" />

                    <TextView
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/member_info"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvUnique"
                        style="@style/text_12_333"
                        android:text="@string/no_colon" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/member_mobile" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2.5"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/etMobile"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:background="@null"
                            android:hint="@string/input_member_mobile"
                            android:inputType="phone"
                            android:maxLength="11"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5"
                            tools:ignore="NestedWeights" />

                        <ImageView
                            android:id="@+id/ivMobileClear"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/dp5"
                            android:src="@mipmap/ic_close001"
                            android:visibility="gone" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/member_name" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2.5"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/etName"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:background="@null"
                            android:hint="@string/input_member_name"
                            android:inputType="phone"
                            android:maxLength="11"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5"
                            tools:ignore="NestedWeights" />

                        <ImageView
                            android:id="@+id/ivNameClear"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/dp5"
                            android:src="@mipmap/ic_close001"
                            android:visibility="gone" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*"
                            android:visibility="invisible" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/member_balance" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2.5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvBalance"
                            style="@style/text_12_orange"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:text="0.00"
                            android:textStyle="bold"
                            tools:ignore="NestedWeights" />

                        <TextView
                            android:id="@+id/tvRecord"
                            style="@style/text_10_orange"
                            android:layout_width="0dp"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_weight="1"
                            android:background="@drawable/shape_orange_kuang_5"
                            android:gravity="center"
                            android:paddingVertical="@dimen/dp5"
                            android:text="@string/consumption_records" />

                        <TextView
                            android:id="@+id/tvRefund"
                            style="@style/text_10_white"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_weight="1"
                            android:background="@drawable/shape_red_5"
                            android:gravity="center"
                            android:paddingVertical="@dimen/dp5"
                            android:text="@string/refund_fee" />

                        <TextView
                            android:id="@+id/tvRecharge"
                            style="@style/text_10_white"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:background="@drawable/shape_green_5"
                            android:gravity="center"
                            android:paddingVertical="@dimen/dp5"
                            android:text="@string/recharge" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*"
                            android:visibility="invisible" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/member_points" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2.5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvPoints"
                            style="@style/text_12_green"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:text="0.00"
                            android:textStyle="bold"
                            tools:ignore="NestedWeights" />

                        <TextView
                            android:id="@+id/tvExchange"
                            style="@style/text_10_white"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_weight="1"
                            android:background="@drawable/shape_orange_5"
                            android:gravity="center"
                            android:paddingVertical="@dimen/dp5"
                            android:text="@string/exchange" />

                        <TextView
                            android:id="@+id/tvPointsAdd"
                            style="@style/text_10_green"
                            android:layout_width="0dp"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_weight="1"
                            android:background="@drawable/shape_green_kuang_5"
                            android:gravity="center"
                            android:paddingVertical="@dimen/dp5"
                            android:text="@string/points_add" />

                        <TextView
                            android:id="@+id/tvPointsSub"
                            style="@style/text_10_red"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:background="@drawable/shape_red_kuang_5"
                            android:gravity="center"
                            android:paddingVertical="@dimen/dp5"
                            android:text="@string/points_sub" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*"
                            android:visibility="invisible" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/member_level" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvLevel"
                        style="@style/text_12_666"
                        android:layout_width="0dp"
                        android:layout_weight="2.5"
                        android:background="@drawable/shape_f2_5"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*"
                            android:visibility="invisible" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/member_sex" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/linSex"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2.5"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvSex"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:background="@null"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5"
                            android:text=""
                            tools:ignore="NestedWeights" />

                        <ImageView
                            android:id="@+id/ivSex"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/dp5"
                            android:src="@mipmap/ic_arrow002" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*"
                            android:visibility="invisible" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/debt_limit" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2.5"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/etDebtLimit"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:background="@null"
                            android:hint="@string/input_debt_limit"
                            android:inputType="numberDecimal"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5"
                            tools:ignore="NestedWeights" />

                        <ImageView
                            android:id="@+id/ivDebtLimitClear"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/dp5"
                            android:src="@mipmap/ic_close001"
                            android:visibility="gone" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*"
                            android:visibility="invisible" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/remarks_info" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2.5"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/etRemarks"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:background="@null"
                            android:hint="@string/input_remarks_info"
                            android:inputType="text"
                            android:maxLength="50"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5"
                            tools:ignore="NestedWeights" />

                        <ImageView
                            android:id="@+id/ivRemarksClear"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/dp5"
                            android:src="@mipmap/ic_close001"
                            android:visibility="gone" />

                    </LinearLayout>

                </LinearLayout>

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <LinearLayout
                    android:id="@+id/linConfirm"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_green_5"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp8">

                    <TextView
                        style="@style/text_14_white"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:text="@string/save"
                        android:textStyle="bold"
                        app:drawableLeftCompat="@mipmap/ic_right001" />

                </LinearLayout>

            </LinearLayout>

        </RelativeLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginVertical="@dimen/dp10"
            android:layout_weight="3"
            android:orientation="vertical">

            <HorizontalScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp10"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvType0"
                        style="@style/text_14_white"
                        android:layout_marginEnd="@dimen/dp1"
                        android:background="@drawable/shape_green_left_5"
                        android:gravity="center"
                        android:paddingHorizontal="@dimen/dp20"
                        android:paddingVertical="@dimen/dp8"
                        android:text="@string/all"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvType1"
                        style="@style/text_14_black"
                        android:layout_marginEnd="@dimen/dp1"
                        android:background="@color/white"
                        android:gravity="center"
                        android:paddingHorizontal="@dimen/dp20"
                        android:paddingVertical="@dimen/dp8"
                        android:text="@string/member_credit" />

                    <TextView
                        android:id="@+id/tvType2"
                        style="@style/text_14_black"
                        android:layout_marginEnd="@dimen/dp1"
                        android:background="@color/white"
                        android:gravity="center"
                        android:paddingHorizontal="@dimen/dp20"
                        android:paddingVertical="@dimen/dp8"
                        android:text="@string/member_stored"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tvType3"
                        style="@style/text_14_black"
                        android:background="@drawable/shape_white_right_5"
                        android:gravity="center"
                        android:paddingHorizontal="@dimen/dp20"
                        android:paddingVertical="@dimen/dp8"
                        android:text="@string/member_common" />

                </LinearLayout>

            </HorizontalScrollView>

            <com.scwang.smart.refresh.layout.SmartRefreshLayout
                android:id="@+id/smartRefreshLayout"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                tools:ignore="NestedWeights">

                <com.scwang.smart.refresh.header.ClassicsHeader
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="7.5dp"
                        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                        app:spanCount="4" />

                    <LinearLayout
                        android:id="@+id/linEmpty"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingTop="@dimen/dp40"
                        android:visibility="gone">

                        <ImageView
                            android:id="@+id/ivEmpty"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/ic_empty" />

                        <TextView
                            android:id="@+id/tvEmpty"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp26"
                            android:textColor="@color/color_666"
                            android:textSize="@dimen/f14"
                            android:textStyle="bold" />

                    </LinearLayout>

                </RelativeLayout>

                <com.scwang.smart.refresh.footer.ClassicsFooter
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </com.scwang.smart.refresh.layout.SmartRefreshLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp60"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:gravity="bottom">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp10"
                    android:background="@drawable/shape_white_5"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    android:orientation="horizontal"
                    android:paddingHorizontal="2.5dp">

                    <RelativeLayout
                        android:id="@+id/relAdd"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:clipChildren="false"
                        android:clipToPadding="false">

                        <TextView
                            style="@style/text_14_white"
                            android:layout_height="@dimen/dp44"
                            android:layout_marginStart="2.5dp"
                            android:layout_marginTop="2.5dp"
                            android:layout_marginEnd="2.5dp"
                            android:layout_marginBottom="2.5dp"
                            android:background="@drawable/shape_green_5"
                            android:drawablePadding="@dimen/dp5"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="@dimen/dp10"
                            android:text="@string/member_add"
                            app:drawableLeftCompat="@mipmap/ic_add003" />

                        <TextView
                            style="@style/text_10_white"
                            android:layout_marginTop="-9dp"
                            android:background="@drawable/shape_white_kuang_red_10"
                            android:paddingHorizontal="@dimen/dp6"
                            android:paddingVertical="@dimen/dp2"
                            android:text="@string/setting_not"
                            android:visibility="invisible" />

                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/relLevel"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:clipChildren="false"
                        android:clipToPadding="false">

                        <TextView
                            style="@style/text_14_white"
                            android:layout_height="@dimen/dp44"
                            android:layout_marginStart="2.5dp"
                            android:layout_marginTop="2.5dp"
                            android:layout_marginEnd="2.5dp"
                            android:layout_marginBottom="2.5dp"
                            android:background="@drawable/shape_blue_5"
                            android:drawablePadding="@dimen/dp5"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="@dimen/dp10"
                            android:text="@string/level_manage"
                            app:drawableLeftCompat="@mipmap/ic_level001" />

                        <TextView
                            android:id="@+id/tvLevelSetting"
                            style="@style/text_10_white"
                            android:layout_marginTop="-9dp"
                            android:background="@drawable/shape_white_kuang_red_10"
                            android:paddingHorizontal="@dimen/dp6"
                            android:paddingVertical="@dimen/dp2"
                            android:text="@string/setting_not"
                            android:visibility="invisible" />

                    </RelativeLayout>

                    <RelativeLayout
                        android:id="@+id/relPoints"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:clipChildren="false"
                        android:clipToPadding="false">

                        <TextView
                            style="@style/text_14_white"
                            android:layout_height="@dimen/dp44"
                            android:layout_marginStart="2.5dp"
                            android:layout_marginTop="2.5dp"
                            android:layout_marginEnd="2.5dp"
                            android:layout_marginBottom="2.5dp"
                            android:background="@drawable/shape_orange_5"
                            android:drawablePadding="@dimen/dp5"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="@dimen/dp10"
                            android:text="@string/points_rule"
                            app:drawableLeftCompat="@mipmap/ic_points003" />

                        <TextView
                            android:id="@+id/tvPointsSetting"
                            style="@style/text_10_white"
                            android:layout_marginTop="-9dp"
                            android:background="@drawable/shape_white_kuang_red_10"
                            android:paddingHorizontal="@dimen/dp6"
                            android:paddingVertical="@dimen/dp2"
                            android:text="@string/setting_not"
                            android:visibility="invisible" />

                    </RelativeLayout>

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>