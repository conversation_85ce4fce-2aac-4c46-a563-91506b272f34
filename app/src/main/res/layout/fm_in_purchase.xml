<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="@dimen/dp5"
        android:background="@drawable/shape_white_5"
        android:orientation="vertical"
        android:padding="@dimen/dp10">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:background="@drawable/shape_d8_kuang_5"
                android:orientation="horizontal"
                android:padding="@dimen/dp1">

                <TextView
                    android:id="@+id/tvType0"
                    style="@style/text_12_white"
                    android:background="@drawable/shape_green_5"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/all"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvType1"
                    style="@style/text_12_black"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/order_status9" />

                <TextView
                    android:id="@+id/tvType2"
                    style="@style/text_12_black"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/order_status7" />

                <TextView
                    android:id="@+id/tvType3"
                    style="@style/text_12_black"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/order_status10" />

                <TextView
                    android:id="@+id/tvType4"
                    style="@style/text_12_black"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/order_status6" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:background="@drawable/shape_f5_top_5"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5">

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="2"
                android:text="@string/order_time" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="2"
                android:gravity="center"
                android:text="@string/order_no" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="2"
                android:gravity="center"
                android:text="@string/supplier" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/contacts" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/goods_type" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/total_amount" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/status_order" />

        </LinearLayout>

        <include
            android:id="@+id/vSmartrefreshlayout"
            layout="@layout/layout_smartrefreshlayout" />

    </LinearLayout>

</LinearLayout>