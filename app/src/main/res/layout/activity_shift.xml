<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/green">

        <ImageView
            android:id="@+id/ivLogo"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingHorizontal="@dimen/dp10"
            android:src="@mipmap/ic_logo004" />

        <TextView
            style="@style/text_18_white"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text="@string/shift_handover"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/linDate"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toStartOf="@+id/tvCashier"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10">

            <TextClock
                style="@style/text_12_white"
                android:format24Hour="MM-dd HH:mm"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp2"
                android:background="@drawable/shape_black_tm_5"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp1">

                <ImageView
                    android:id="@+id/ivNetwork"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5" />

                <TextView
                    android:id="@+id/tvNetwork"
                    style="@style/text_10_white"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tvCashier"
            style="@style/text_14_green"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/white"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp20"
            android:text="@string/cashier_table"
            android:textStyle="bold"
            app:drawableLeftCompat="@mipmap/ic_cashier001" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="@dimen/dp5"
        android:layout_marginTop="@dimen/dp46"
        android:layout_marginBottom="@dimen/dp5"
        android:background="@drawable/shape_white_5"
        android:orientation="vertical">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_12_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center"
                        android:text="@string/duty_cashier"
                        app:drawableTopCompat="@mipmap/ic_shift_img001" />

                    <TextView
                        style="@style/text_12_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center"
                        android:text="@string/time_shift_login"
                        app:drawableTopCompat="@mipmap/ic_shift_img002" />

                    <TextView
                        style="@style/text_12_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center"
                        android:text="@string/time_shift_out"
                        app:drawableTopCompat="@mipmap/ic_shift_img003" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvName"
                        style="@style/text_14_black"
                        android:layout_width="0dp"

                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="" />

                    <TextView
                        android:id="@+id/tvStartDate"
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="" />

                    <TextView
                        android:id="@+id/tvEndDate"
                        style="@style/text_14_red"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp10"
                    android:background="@color/color_f2" />

                <LinearLayout
                    android:id="@+id/linOrderOffline"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="@dimen/dp10">

                    <View
                        android:layout_width="@dimen/dp3"
                        android:layout_height="@dimen/dp15"
                        android:layout_marginEnd="@dimen/dp5"
                        android:background="@drawable/shape_green_2" />

                    <TextView
                        style="@style/text_14_black"
                        android:layout_marginEnd="@dimen/dp5"
                        android:text="@string/order_offline" />

                    <TextView
                        android:id="@+id/tvOrderOffline"
                        style="@style/text_14_black"
                        android:text="0"
                        app:drawableEndCompat="@mipmap/ic_more001" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp10"
                    android:background="@color/color_f2" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="@dimen/dp10">

                    <View
                        android:layout_width="@dimen/dp3"
                        android:layout_height="@dimen/dp15"
                        android:layout_marginEnd="@dimen/dp5"
                        android:background="@drawable/shape_green_2" />

                    <TextView
                        android:id="@+id/tvRechargeInfo"
                        style="@style/text_14_black"
                        android:layout_marginEnd="@dimen/dp5"
                        android:text="@string/recharge_info_colon" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp10"
                    android:background="@color/color_f2" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="@dimen/dp3"
                        android:layout_height="@dimen/dp15"
                        android:layout_marginEnd="@dimen/dp5"
                        android:background="@drawable/shape_green_2" />

                    <TextView
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/revenue_statistics" />

                    <TextView
                        android:id="@+id/tvTips"
                        style="@style/text_12_green"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:padding="@dimen/dp10"
                        android:text="@string/statistics_field_explain"
                        app:drawableLeftCompat="@mipmap/ic_tips002" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp5">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/dp5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:orientation="vertical"
                        android:padding="@dimen/dp10">

                        <TextView
                            android:id="@+id/tvCount"
                            style="@style/text_14_black"
                            android:text="0.00"
                            android:textStyle="bold" />

                        <TextView
                            style="@style/text_14_666"
                            android:layout_marginTop="@dimen/dp5"
                            android:text="@string/order_count" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/dp5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:orientation="vertical"
                        android:padding="@dimen/dp10">

                        <TextView
                            android:id="@+id/tvTotal"
                            style="@style/text_14_black"
                            android:text="0.00"
                            android:textStyle="bold" />

                        <TextView
                            style="@style/text_14_666"
                            android:layout_marginTop="@dimen/dp5"
                            android:text="@string/order_receivable" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/dp5"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:orientation="vertical"
                        android:padding="@dimen/dp10">

                        <TextView
                            android:id="@+id/tvReceivable"
                            style="@style/text_14_black"
                            android:text="0.00"
                            android:textStyle="bold" />

                        <TextView
                            style="@style/text_14_666"
                            android:layout_marginTop="@dimen/dp5"
                            android:text="@string/order_paid_in" />

                    </LinearLayout>

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_margin="@dimen/dp10"
                        android:layout_weight="2" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvCashier"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:spanCount="5" />

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp10"
            android:background="@color/color_f2" />

        <TextView
            android:id="@+id/tvConfirm"
            style="@style/text_16_white"
            android:layout_gravity="end"
            android:layout_margin="@dimen/dp10"
            android:background="@drawable/shape_green_5"
            android:paddingHorizontal="@dimen/dp20"
            android:paddingVertical="@dimen/dp10"
            android:text="@string/confirm_shift"
            android:textStyle="bold" />

    </LinearLayout>

</RelativeLayout>