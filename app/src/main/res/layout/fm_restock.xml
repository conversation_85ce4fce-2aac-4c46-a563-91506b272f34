<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp5"
        android:background="@drawable/shape_white_5"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp5">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:background="@drawable/shape_d8_kuang_5"
            android:orientation="horizontal"
            android:padding="@dimen/dp1">

            <TextView
                android:id="@+id/tvType0"
                style="@style/text_12_white"
                android:background="@drawable/shape_green_5"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/generate_no"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvType1"
                style="@style/text_12_black"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/generated" />

            <TextView
                android:id="@+id/tvType2"
                style="@style/text_12_black"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/canceled" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvAdd"
            style="@style/text_14_white"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="@drawable/shape_green_5"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/restock_create" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="@dimen/dp5"
        android:orientation="horizontal">

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/smartRefreshLayout"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="3"
            android:background="@drawable/shape_f2_5">

            <com.scwang.smart.refresh.header.ClassicsHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="@dimen/dp5"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:spanCount="2" />

                <LinearLayout
                    android:id="@+id/linEmpty"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/dp40"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/ivEmpty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_empty" />

                    <TextView
                        android:id="@+id/tvEmpty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp26"
                        android:textColor="@color/color_666"
                        android:textSize="@dimen/f14"
                        android:textStyle="bold" />

                </LinearLayout>

            </RelativeLayout>

            <com.scwang.smart.refresh.footer.ClassicsFooter
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@drawable/shape_white_5"
                android:orientation="vertical"
                tools:ignore="NestedWeights">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingVertical="@dimen/dp6">

                    <TextView
                        style="@style/text_12_999"
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_weight="1.5"
                        android:text="@string/goods_name" />

                    <TextView
                        style="@style/text_12_999"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/buy_count" />

                    <TextView
                        style="@style/text_12_999"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/price_estimate" />

                    <ImageView
                        android:id="@+id/ivDel"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingHorizontal="@dimen/dp10"
                        android:src="@mipmap/ic_del002"
                        android:visibility="invisible" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:background="@color/color_line" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvGoods"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                android:background="@drawable/shape_white_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <TextView
                    android:id="@+id/tvDel"
                    style="@style/text_14_white"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/dp5"
                    android:layout_weight="1"
                    android:background="@drawable/shape_red_5"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp8"
                    android:text="@string/del"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tvCancel"
                    style="@style/text_14_red"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/dp5"
                    android:layout_weight="1"
                    android:background="@drawable/shape_red_kuang_5"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp8"
                    android:text="@string/restock_cancel"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tvInfo"
                    style="@style/text_14_white"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/dp5"
                    android:layout_weight="1"
                    android:background="@drawable/shape_green_5"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp8"
                    android:text="@string/restock_add"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tvPreview"
                    style="@style/text_14_white"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/dp5"
                    android:layout_weight="1"
                    android:background="@drawable/shape_orange_5"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp8"
                    android:text="@string/preview"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tvAgain"
                    style="@style/text_14_white"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/dp5"
                    android:layout_weight="1"
                    android:background="@drawable/shape_blue_5"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp8"
                    android:text="@string/restock_again"
                    android:visibility="gone" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>