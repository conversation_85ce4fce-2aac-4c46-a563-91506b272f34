<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp5"
        android:paddingVertical="@dimen/dp8">

        <ImageView
            android:id="@+id/ivItemImg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp3"
            android:src="@drawable/selector_cate_show" />

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_12_white"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

</LinearLayout>