<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linItem"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/dp10"
    android:paddingVertical="@dimen/dp5">

    <TextView
        android:id="@+id/tvItemTime"
        style="@style/text_10_black"
        android:layout_width="0dp"
        android:layout_weight="1.5"
        android:gravity="center"
        android:text="" />

    <TextView
        android:id="@+id/tvItemName"
        style="@style/text_10_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="" />

    <TextView
        android:id="@+id/tvItemNo"
        style="@style/text_10_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="" />

    <TextView
        android:id="@+id/tvItemPayStatus"
        style="@style/text_10_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="" />

    <TextView
        android:id="@+id/tvItemMobile"
        style="@style/text_10_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="" />

    <TextView
        android:id="@+id/tvItemAddress"
        style="@style/text_10_black"
        android:layout_width="0dp"
        android:layout_weight="3"
        android:gravity="center"
        android:text="" />

    <TextView
        android:id="@+id/tvItemTotal"
        style="@style/text_10_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="0.00" />

    <TextView
        android:id="@+id/tvItemStatus"
        style="@style/text_10_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="" />

    <TextView
        android:id="@+id/tvItemWeight"
        style="@style/text_10_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="0" />

</LinearLayout>