<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_5"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvDialogCancel"
            style="@style/text_14_999"
            android:padding="@dimen/dp16"
            android:text="@string/cancel" />

        <TextView
            style="@style/text_16_black"
            android:layout_centerInParent="true"
            android:text="@string/select_date"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvDialogConfirm"
            style="@style/text_14_green"
            android:layout_alignParentEnd="true"
            android:padding="@dimen/dp16"
            android:text="@string/confirm" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/color_line" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp16">

        <LinearLayout
            android:id="@+id/linDialogDateStart"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvDialogDateStart"
                style="@style/text_14_green"
                android:layout_marginTop="@dimen/dp12"
                android:gravity="center"
                android:hint="@string/select_date_start" />

            <View
                android:id="@+id/vDialogDateStart"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="@dimen/dp12"
                android:background="@color/green" />

        </LinearLayout>

        <View
            android:layout_width="@dimen/dp10"
            android:layout_height="@dimen/dp1"
            android:layout_margin="@dimen/dp6"
            android:background="@color/color_333" />

        <LinearLayout
            android:id="@+id/linDialogDateEnd"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvDialogDateEnd"
                style="@style/text_14_999"
                android:layout_marginTop="@dimen/dp12"
                android:gravity="center"
                android:hint="@string/select_date_end" />

            <View
                android:id="@+id/vDialogDateEnd"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="@dimen/dp12"
                android:background="@color/color_line" />

        </LinearLayout>

    </LinearLayout>

    <com.yxl.cashier_retail.view.pickerview.DateWheelLayout
        android:id="@+id/wheelLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp200"
        app:wheel_dateMode="year_month_day" />

</LinearLayout>
