<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_f5_top_5">

        <TextView
            android:id="@+id/tvProfit"
            style="@style/text_14_black"
            android:layout_height="@dimen/dp34"
            android:background="@drawable/shape_white_topleft_5"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp16"
            android:text="@string/setting_profit"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvPay"
            style="@style/text_14_333"
            android:layout_height="@dimen/dp34"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp16"
            android:text="@string/setting_payment" />

    </LinearLayout>

    <!--利润设置-->
    <LinearLayout
        android:id="@+id/linProfit"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_white_bottom_5"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp12">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_black"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="@string/ratio_nobarcode_profit" />

            <ImageView
                android:id="@+id/ivSubNoBarcode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_sub001" />

            <TextView
                android:id="@+id/tvCountNoBarcode"
                style="@style/text_12_666"
                android:layout_width="@dimen/dp49"
                android:layout_height="@dimen/dp27"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center"
                android:text="0" />

            <ImageView
                android:id="@+id/ivAddNoBarcode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_add001" />

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

        </LinearLayout>

        <TextView
            style="@style/text_12_999"
            android:layout_marginTop="@dimen/dp5"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:text="@string/ratio_nobarcode_profit_tips"
            app:drawableLeftCompat="@mipmap/ic_tips002" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp15"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                style="@style/text_14_black"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="@string/ratio_standard_profit" />

            <ImageView
                android:id="@+id/ivSubBarcode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_sub001" />

            <TextView
                android:id="@+id/tvCountBarcode"
                style="@style/text_12_666"
                android:layout_width="@dimen/dp49"
                android:layout_height="@dimen/dp27"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center"
                android:text="0" />

            <ImageView
                android:id="@+id/ivAddBarcode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_add001" />

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

        </LinearLayout>

        <TextView
            style="@style/text_12_999"
            android:layout_marginTop="@dimen/dp5"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:text="@string/ratio_standard_profit_tips"
            android:visibility="gone"
            app:drawableLeftCompat="@mipmap/ic_tips002" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp15"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                style="@style/text_14_black"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="@string/ratio_points" />

            <ImageView
                android:id="@+id/ivSubPoints"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_sub001" />

            <TextView
                android:id="@+id/tvCountPoints"
                style="@style/text_12_666"
                android:layout_width="@dimen/dp49"
                android:layout_height="@dimen/dp27"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center"
                android:text="0" />

            <ImageView
                android:id="@+id/ivAddPoints"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_add001" />

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

        </LinearLayout>

        <TextView
            style="@style/text_12_999"
            android:layout_marginTop="@dimen/dp5"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:text="@string/ratio_points_tips"
            android:visibility="gone"
            app:drawableLeftCompat="@mipmap/ic_tips002" />

    </LinearLayout>

    <!--支付设置-->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/nslPay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_white_bottom_5"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <TextView
                style="@style/text_14_black"
                android:text="@string/place_select_shortcut_display" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvPaymentHor"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp54"
                android:layout_marginTop="@dimen/dp10"
                android:background="@drawable/shape_17262b_5"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:paddingHorizontal="2.5dp"
                android:paddingVertical="@dimen/dp3"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="7" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvPaymentVer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:layout_marginEnd="@dimen/dp280"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>