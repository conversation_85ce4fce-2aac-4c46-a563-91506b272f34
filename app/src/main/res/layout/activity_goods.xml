<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/green">

        <ImageView
            android:id="@+id/ivLogo"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingHorizontal="@dimen/dp10"
            android:src="@mipmap/ic_logo004" />

        <LinearLayout
            android:layout_width="@dimen/dp200"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/ivLogo"
            android:background="@drawable/shape_white_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/etSearch"
                style="@style/text_12_333"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:background="@null"
                android:drawableStart="@mipmap/ic_search001"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:hint="@string/search_tips"
                android:imeOptions="actionSearch"
                android:inputType="text"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp4"
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivSearchClear"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp4"
                android:src="@mipmap/ic_close001"
                android:visibility="gone" />

        </LinearLayout>

        <TextView
            style="@style/text_18_white"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text="@string/goods_manage"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/linDate"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toStartOf="@+id/tvCashier"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10">

            <TextClock
                style="@style/text_12_white"
                android:format24Hour="MM-dd HH:mm"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp2"
                android:background="@drawable/shape_black_tm_5"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp1">

                <ImageView
                    android:id="@+id/ivNetwork"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5" />

                <TextView
                    android:id="@+id/tvNetwork"
                    style="@style/text_10_white"
                    android:text="" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tvCashier"
            style="@style/text_14_green"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/white"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp20"
            android:text="@string/cashier_table"
            android:textStyle="bold"
            app:drawableLeftCompat="@mipmap/ic_cashier001" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dp46"
        android:layout_marginBottom="@dimen/dp5"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp5"
        tools:ignore="RtlSymmetry">

        <LinearLayout
            android:id="@+id/linNothing"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:background="@drawable/shape_white_5"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="@dimen/dp10"
            android:visibility="visible">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/ic_empty" />

            <TextView
                style="@style/text_14_black"
                android:layout_marginTop="@dimen/dp16"
                android:gravity="center"
                android:text="@string/goods_edit_to_right" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linInfo"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:background="@drawable/shape_white_5"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="@dimen/dp3"
                    android:layout_height="@dimen/dp15"
                    android:layout_marginEnd="@dimen/dp5"
                    android:background="@drawable/shape_green_2" />

                <TextView
                    style="@style/text_16_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/goods_info"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvPrint"
                    style="@style/text_14_999"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:drawablePadding="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6"
                    android:text="@string/print_tags"
                    android:visibility="gone"
                    app:drawableLeftCompat="@mipmap/ic_print002" />

            </LinearLayout>

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:scrollbars="none"
                tools:ignore="NestedWeights">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_14_red"
                                android:text="*" />

                            <TextView
                                style="@style/text_14_black"
                                android:text="@string/goods_barcode" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tvBarcode"
                            style="@style/text_14_black"
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:background="@drawable/shape_f2_5"
                            android:hint=""
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp6" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_14_red"
                                android:text="*" />

                            <TextView
                                style="@style/text_14_black"
                                android:text="@string/goods_name" />

                        </LinearLayout>

                        <EditText
                            android:id="@+id/etName"
                            style="@style/text_14_black"
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:background="@drawable/shape_d8_kuang_5"
                            android:hint="@string/input_goods_name"
                            android:inputType="text"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp6" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_14_red"
                                android:text="*" />

                            <TextView
                                style="@style/text_14_black"
                                android:text="@string/pricing_type" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <ImageView
                                android:id="@+id/ivChengType0"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:padding="@dimen/dp10"
                                android:src="@drawable/selector_checkbox003" />

                            <TextView
                                style="@style/text_14_black"
                                android:layout_marginEnd="@dimen/dp30"
                                android:text="@string/pricing_piece" />

                            <ImageView
                                android:id="@+id/ivChengType1"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:padding="@dimen/dp10"
                                android:src="@drawable/selector_checkbox003" />

                            <TextView
                                style="@style/text_14_black"
                                android:text="@string/pricing_weight" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_14_red"
                                android:text="*"
                                android:visibility="invisible" />

                            <TextView
                                style="@style/text_14_black"
                                android:text="@string/supplier" />
                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/linSupplier"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2"
                            android:background="@drawable/shape_d8_kuang_5"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tvSupplier"
                                style="@style/text_14_black"
                                android:layout_width="0dp"
                                android:layout_marginStart="@dimen/dp10"
                                android:layout_weight="1"
                                android:hint="@string/select_supplier"
                                android:maxLines="1"
                                android:paddingVertical="@dimen/dp6"
                                android:text=""
                                tools:ignore="NestedWeights" />

                            <ImageView
                                android:id="@+id/ivSupplier"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:padding="@dimen/dp6"
                                android:src="@mipmap/ic_arrow002" />

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_14_red"
                                android:text="*" />

                            <TextView
                                style="@style/text_14_black"
                                android:text="@string/price_sale" />

                        </LinearLayout>

                        <EditText
                            android:id="@+id/etSalePrice"
                            style="@style/text_14_black"
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:background="@drawable/shape_d8_kuang_5"
                            android:hint=""
                            android:inputType="text"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp6" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_14_red"
                                android:text="*"
                                android:visibility="invisible" />

                            <TextView
                                style="@style/text_14_black"
                                android:text="@string/price_online" />

                        </LinearLayout>

                        <EditText
                            android:id="@+id/etOnlinePrice"
                            style="@style/text_14_black"
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:background="@drawable/shape_d8_kuang_5"
                            android:hint=""
                            android:inputType="text"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp6" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_14_red"
                                android:text="*"
                                android:visibility="invisible" />

                            <TextView
                                style="@style/text_14_black"
                                android:text="@string/price_member" />
                        </LinearLayout>

                        <EditText
                            android:id="@+id/etMemberPrice"
                            style="@style/text_14_black"
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:background="@drawable/shape_d8_kuang_5"
                            android:hint=""
                            android:inputType="text"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp6" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_14_red"
                                android:text="*"
                                android:visibility="invisible" />

                            <TextView
                                style="@style/text_14_black"
                                android:text="@string/stock_price_average" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/tvAveragePrice"
                            style="@style/text_14_black"
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:background="@drawable/shape_f2_5"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp6" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_weight="1"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_14_red"
                                android:text="*"
                                android:visibility="invisible" />

                            <TextView
                                style="@style/text_14_black"
                                android:text="@string/goods_stock" />
                        </LinearLayout>

                        <TextView
                            android:id="@+id/tvStock"
                            style="@style/text_14_black"
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:background="@drawable/shape_f2_5"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp6"
                            android:text="0" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:id="@+id/linUnit"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_weight="1"
                            android:background="@drawable/shape_d8_kuang_5"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp6">

                            <TextView
                                android:id="@+id/tvUnit"
                                style="@style/text_14_black"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="" />

                            <ImageView
                                android:id="@+id/ivUnit"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@mipmap/ic_arrow002" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/tvIn"
                                style="@style/text_14_white"
                                android:layout_marginEnd="@dimen/dp5"
                                android:background="@drawable/shape_green_5"
                                android:gravity="center"
                                android:paddingHorizontal="@dimen/dp10"
                                android:paddingVertical="@dimen/dp6"
                                android:text="@string/in" />

                            <TextView
                                android:id="@+id/tvOut"
                                style="@style/text_14_white"
                                android:background="@drawable/shape_orange_5"
                                android:gravity="center"
                                android:paddingHorizontal="@dimen/dp10"
                                android:paddingVertical="@dimen/dp6"
                                android:text="@string/out" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </ScrollView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvDel"
                    style="@style/text_14_white"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:background="@drawable/shape_red_5"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp5"
                    android:paddingVertical="@dimen/dp10"
                    android:text="@string/del"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvMore"
                    style="@style/text_14_white"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:background="@drawable/shape_orange_5"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp5"
                    android:paddingVertical="@dimen/dp10"
                    android:text="@string/goods_information_more"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvConfirm"
                    style="@style/text_14_white"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:background="@drawable/shape_green_5"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp5"
                    android:paddingVertical="@dimen/dp10"
                    android:text="@string/save"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:orientation="vertical"
            android:visibility="visible">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvCate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="4.5dp"
                app:layoutManager="androidx.recyclerview.widget.StaggeredGridLayoutManager"
                app:spanCount="6" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="horizontal">

                <com.scwang.smart.refresh.layout.SmartRefreshLayout
                    android:id="@+id/smartRefreshLayout"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    tools:ignore="NestedWeights">

                    <com.scwang.smart.refresh.header.ClassicsHeader
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recyclerView"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:padding="2.5dp"
                            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                            app:spanCount="4" />

                        <LinearLayout
                            android:id="@+id/linEmpty"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:paddingTop="@dimen/dp40"
                            android:visibility="gone">

                            <ImageView
                                android:id="@+id/ivEmpty"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@mipmap/ic_empty" />

                            <TextView
                                android:id="@+id/tvEmpty"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp26"
                                android:textColor="@color/color_666"
                                android:textSize="@dimen/f14"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </RelativeLayout>

                    <com.scwang.smart.refresh.footer.ClassicsFooter
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </com.scwang.smart.refresh.layout.SmartRefreshLayout>

                <LinearLayout
                    android:layout_width="@dimen/dp31"
                    android:layout_height="match_parent"
                    android:background="@drawable/shape_17262b_bottomright_5"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp29"
                        android:gravity="center"
                        android:text="@string/pages"
                        android:textColor="@color/white80"
                        android:textSize="@dimen/f10" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvPage"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                </LinearLayout>

            </LinearLayout>

            <HorizontalScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp5"
                android:background="@drawable/shape_white_5"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="@dimen/dp5">

                    <TextView
                        android:id="@+id/tvCate"
                        style="@style/text_14_white"
                        android:layout_marginEnd="@dimen/dp5"
                        android:background="@drawable/shape_green_5"
                        android:padding="@dimen/dp10"
                        android:text="@string/edit_home_cate" />

                    <TextView
                        android:id="@+id/tvAddToCate"
                        style="@style/text_14_white"
                        android:layout_marginEnd="@dimen/dp5"
                        android:background="@drawable/shape_blue_5"
                        android:padding="@dimen/dp10"
                        android:text="@string/add_goods_to_cate" />

                    <TextView
                        android:id="@+id/tvAdd"
                        style="@style/text_14_white"
                        android:layout_marginEnd="@dimen/dp5"
                        android:background="@drawable/shape_blue_5"
                        android:padding="@dimen/dp10"
                        android:text="@string/goods_add"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tvAddQuick"
                        style="@style/text_14_white"
                        android:layout_marginEnd="@dimen/dp5"
                        android:background="@drawable/shape_green_5"
                        android:padding="@dimen/dp10"
                        android:text="@string/goods_add_quick" />

                    <TextView
                        android:id="@+id/tvExit"
                        style="@style/text_14_white"
                        android:background="@drawable/shape_red_5"
                        android:padding="@dimen/dp10"
                        android:text="@string/goods_manage_exit" />

                </LinearLayout>

            </HorizontalScrollView>

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>