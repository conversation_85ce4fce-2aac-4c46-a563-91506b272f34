<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/dp5"
    android:background="@drawable/shape_white_5"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="@dimen/dp5">

    <ImageView
        android:id="@+id/ivItemImg"
        android:layout_width="@dimen/dp100"
        android:layout_height="@dimen/dp100"
        android:layout_marginEnd="@dimen/dp5" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_12_black"
            android:ellipsize="end"
            android:maxLines="1"
            android:text=""
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvItemPrice"
            style="@style/text_12_green"
            android:layout_marginTop="@dimen/dp6"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="￥0.00"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvItemCount"
            style="@style/text_10_999"
            android:layout_marginTop="@dimen/dp10"
            android:text="已售0件" />

        <TextView
            android:id="@+id/tvItemSupplier"
            style="@style/text_10_666"
            android:layout_marginTop="@dimen/dp6"
            android:text="供货商：" />

        <TextView
            android:id="@+id/tvItemStartOrder"
            style="@style/text_10_666"
            android:layout_marginTop="@dimen/dp6"
            android:text="起订量：" />

    </LinearLayout>

</LinearLayout>