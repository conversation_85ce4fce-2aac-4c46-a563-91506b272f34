<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="0dp"
    android:layout_height="match_parent"
    android:layout_weight="2"
    android:background="@color/color_1E201E"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/green"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/ivLogo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_logo004" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/shape_white_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <com.yxl.cashier_retail.view.ScannerEditText
                android:id="@+id/etSearch"
                style="@style/text_14_333"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:background="@null"
                android:drawableStart="@mipmap/ic_search001"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:hint="@string/search_tips"
                android:imeOptions="actionSearch"
                android:inputType="text"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivSearchClear"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_close001"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp5"
        android:layout_marginTop="@dimen/dp5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvMemberSelect"
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp36"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:background="@drawable/shape_white_5"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp10"
            android:text="@string/click_select_member"
            app:drawableLeftCompat="@mipmap/ic_head002"
            app:drawableRightCompat="@mipmap/ic_arrow002" />

        <LinearLayout
            android:id="@+id/linMember"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp36"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:background="@drawable/shape_white_5"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/ivMemberHead"
                        android:layout_width="@dimen/dp10"
                        android:layout_height="@dimen/dp10"
                        android:layout_marginEnd="@dimen/dp4"
                        android:src="@mipmap/ic_head003" />

                    <TextView
                        android:id="@+id/tvMemberName"
                        style="@style/text_10_999"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tvMemberPoints"
                    style="@style/text_12_green"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:drawablePadding="@dimen/dp4"
                    android:gravity="center_vertical"
                    android:text=""
                    android:textStyle="bold"
                    app:drawableLeftCompat="@mipmap/ic_points001" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvMemberMobile"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="3"
                    android:text=""
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvMemberBalance"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:drawablePadding="@dimen/dp4"
                    android:text=""
                    android:textColor="@color/orange"
                    android:textStyle="bold"
                    app:drawableLeftCompat="@mipmap/ic_balance001" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linWeight"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp36"
            android:layout_weight="1"
            android:background="@drawable/shape_white_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp10">

            <ImageView
                android:layout_width="@dimen/dp20"
                android:layout_height="@dimen/dp20"
                android:layout_marginEnd="@dimen/dp6"
                android:src="@mipmap/ic_scale001" />

            <TextView
                style="@style/text_14_black"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/pricing_weight"
                tools:ignore="NestedWeights" />

            <TextView
                android:id="@+id/tvSerialPortOff"
                style="@style/text_12_999"
                android:drawablePadding="@dimen/dp3"
                android:gravity="center_vertical"
                android:text="@string/connect_no"
                app:drawableLeftCompat="@mipmap/ic_tips001" />

            <TextView
                android:id="@+id/tvWeight"
                style="@style/text_18_666"
                android:layout_marginEnd="@dimen/dp5"
                android:text="0.00"
                android:textStyle="bold"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvUnit"
                style="@style/text_12_black"
                android:text="kg"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp5"
        android:layout_marginTop="@dimen/dp5"
        android:gravity="center"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/shape_white_topleft_5"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp6">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp5"
                android:src="@mipmap/ic_cart001" />

            <TextView
                style="@style/text_14_black"
                android:text="@string/main_order"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linHangOrder"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/green"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp6">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp5"
                android:src="@mipmap/ic_hang002" />

            <TextView
                style="@style/text_14_white"
                android:text="@string/hang_order" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linPreviousOrder"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/shape_orange_topright5"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp6">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp5"
                android:src="@mipmap/ic_previous001" />

            <TextView
                style="@style/text_14_white"
                android:text="@string/previous_order" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp5"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_marginStart="@dimen/dp10"
            android:layout_weight="2"
            android:paddingVertical="@dimen/dp7"
            android:text="@string/goods_name" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/price" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/count" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/subtotal" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp5"
            android:paddingHorizontal="@dimen/dp5"
            android:src="@mipmap/ic_del001"
            android:visibility="invisible" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginStart="@dimen/dp5"
        android:background="@color/color_line" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvCart"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp5"
        android:layout_weight="1"
        android:background="@color/white"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp5"
        android:background="@color/color_f2"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="@dimen/dp5">

        <ImageView
            android:id="@+id/ivVerify"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp5"
            android:src="@drawable/selector_checkbox002" />

        <TextView
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/barcode_verify" />

        <ImageView
            android:id="@+id/ivPrint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp5"
            android:padding="@dimen/dp5"
            android:src="@drawable/selector_checkbox002" />

        <TextView
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="1"
            android:text="@string/print_receipt" />

        <TextView
            android:id="@+id/tvDiscount"
            style="@style/text_12_white"
            android:layout_marginEnd="@dimen/dp5"
            android:background="@drawable/shape_orange_5"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:maxWidth="@dimen/dp100"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp6"
            android:text="@string/discounts_order"
            app:drawableLeftCompat="@mipmap/ic_discount001" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp5"
        android:layout_marginBottom="@dimen/dp5"
        android:gravity="center"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/linClear"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/shape_red_bottomleft5"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp10">

            <TextView
                style="@style/text_16_white"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center"
                android:text="@string/clear_goods"
                app:drawableLeftCompat="@mipmap/ic_clear001" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2.1"
            android:background="@drawable/shape_17262b_bottomright_5"
            android:orientation="vertical"
            android:padding="@dimen/dp5">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_12_white"
                        android:text="@string/total_count_colon" />

                    <TextView
                        android:id="@+id/tvCount"
                        style="@style/text_14_white"
                        android:text="0.00" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_12_white"
                        android:text="@string/amount_colon" />

                    <TextView
                        android:id="@+id/tvTotal"
                        style="@style/text_14_white"
                        android:text="0.00" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp3"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_12_white"
                        android:text="@string/discount_colon" />

                    <TextView
                        android:id="@+id/tvDiscountMoney"
                        style="@style/text_14_red"
                        android:text="-0.00" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_12_white"
                        android:maxWidth="@dimen/dp60"
                        android:text="@string/receivable_colon" />

                    <TextView
                        android:id="@+id/tvTotalReal"
                        style="@style/text_16_orange"
                        android:text="0.00"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>