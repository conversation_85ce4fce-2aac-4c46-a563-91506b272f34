<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@mipmap/ic_launch_bg"
    android:fitsSystemWindows="true"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/dp20"
            android:src="@mipmap/ic_logo002" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="3"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/ivImg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp59"
            android:src="@mipmap/ic_launch_img001" />

        <TextView
            android:id="@+id/tvTips"
            style="@style/text_12_333"
            android:layout_marginTop="@dimen/dp26"
            android:text="@string/launch_tips" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp64"
            android:gravity="center"
            android:orientation="horizontal">

            <ProgressBar
                android:id="@+id/progressBar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp6"
                android:layout_marginEnd="@dimen/dp12"
                android:layout_weight="1"
                android:backgroundTint="@color/color_666"
                android:max="5"
                android:progress="2" />

            <TextView
                android:id="@+id/tvProgress"
                style="@style/text_10_green"
                android:text="0%" />

        </LinearLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1"
            tools:ignore="NestedWeights" />

        <TextView
            style="@style/text_10_999"
            android:layout_marginBottom="@dimen/dp20"
            android:gravity="bottom"
            android:text="@string/technical_support" />

    </LinearLayout>

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_weight="1" />

</LinearLayout>