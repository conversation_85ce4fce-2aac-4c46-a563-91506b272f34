<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/dp5"
        android:layout_weight="2"
        android:background="@drawable/shape_white_5"
        android:orientation="vertical"
        android:padding="@dimen/dp10">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/linType"
                android:layout_width="@dimen/dp125"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvType"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/cashier_all"
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:id="@+id/ivType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linStartDate"
                android:layout_width="@dimen/dp125"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvStartDate"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:hint="@string/date_start"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp5"
                    android:text=""
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:id="@+id/ivStartDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

            <TextView
                style="@style/text_16_999"
                android:layout_marginHorizontal="@dimen/dp3"
                android:text="~" />

            <LinearLayout
                android:id="@+id/linEndDate"
                android:layout_width="@dimen/dp125"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvEndDate"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:hint="@string/date_end"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp5"
                    android:text=""
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:id="@+id/ivEndDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:background="@drawable/shape_f5_top_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5">

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/cashier_name" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/cashier_no" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/time_shift_login" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/time_shift_out" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="0.5"
                android:gravity="center"
                android:text="@string/order_count" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="0.5"
                android:gravity="center"
                android:text="@string/business_money" />

        </LinearLayout>

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/smartRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.scwang.smart.refresh.header.ClassicsHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                <LinearLayout
                    android:id="@+id/linEmpty"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/dp40"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/ivEmpty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_empty" />

                    <TextView
                        android:id="@+id/tvEmpty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp26"
                        android:textColor="@color/color_666"
                        android:textSize="@dimen/f14"
                        android:textStyle="bold" />

                </LinearLayout>

            </RelativeLayout>

            <com.scwang.smart.refresh.footer.ClassicsFooter
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/linInfo"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:visibility="gone">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@drawable/shape_white_5"
            tools:ignore="NestedWeights">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:padding="@dimen/dp10"
                tools:ignore="NestedWeights">

                <!--充值统计start-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="@dimen/dp3"
                        android:layout_height="@dimen/dp15"
                        android:layout_marginEnd="@dimen/dp5"
                        android:background="@drawable/shape_green_2" />

                    <TextView
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/recharge_statistics"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvTotalRecharge"
                        style="@style/text_12_green"
                        android:text="@string/amount_colon"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:background="@drawable/shape_f5_top_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="@dimen/dp5">

                    <TextView
                        style="@style/text_10_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/way" />

                    <TextView
                        style="@style/text_10_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/amount_money" />

                    <TextView
                        style="@style/text_10_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/order_count" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvRecharge"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
                <!--充值统计end-->

                <!--收银统计start-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="@dimen/dp3"
                        android:layout_height="@dimen/dp15"
                        android:layout_marginEnd="@dimen/dp5"
                        android:background="@drawable/shape_green_2" />

                    <TextView
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/cashier_statistics"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvTotalCashier"
                        style="@style/text_12_green"
                        android:text="@string/amount_colon"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:background="@drawable/shape_f5_top_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="@dimen/dp5">

                    <TextView
                        style="@style/text_10_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/way" />

                    <TextView
                        style="@style/text_10_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/amount_money" />

                    <TextView
                        style="@style/text_10_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/order_count" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvCashier"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
                <!--收银统计end-->

                <!--收银订单start-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="@dimen/dp3"
                        android:layout_height="@dimen/dp15"
                        android:layout_marginEnd="@dimen/dp5"
                        android:background="@drawable/shape_green_2" />

                    <TextView
                        style="@style/text_12_black"
                        android:text="@string/cashier_order"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:background="@drawable/shape_f5_top_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="@dimen/dp5">

                    <TextView
                        style="@style/text_10_666"
                        android:layout_width="0dp"
                        android:layout_weight="2"
                        android:gravity="center"
                        android:text="@string/order_time" />

                    <TextView
                        style="@style/text_10_666"
                        android:layout_width="0dp"
                        android:layout_weight="2"
                        android:gravity="center"
                        android:text="@string/order_no" />

                    <TextView
                        style="@style/text_10_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/sale_total" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvOrder"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:background="@drawable/shape_white_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp5"
            android:visibility="gone">

            <TextView
                android:id="@+id/tvPrint"
                style="@style/text_14_white"
                android:layout_width="0dp"
                android:layout_marginHorizontal="@dimen/dp5"
                android:layout_weight="1"
                android:background="@drawable/shape_orange_5"
                android:gravity="center"
                android:paddingVertical="@dimen/dp10"
                android:text="@string/print_shift_receipt"
                android:textStyle="bold" />

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

        </LinearLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/tvNothing"
        style="@style/text_14_black"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@drawable/shape_white_5"
        android:gravity="center"
        android:text="@string/no_shift_information"
        android:visibility="visible" />

</LinearLayout>