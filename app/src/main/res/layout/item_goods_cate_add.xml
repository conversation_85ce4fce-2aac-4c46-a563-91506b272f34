<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/dp5"
    android:background="@drawable/shape_d8_kuang_f0_5">

    <ImageView
        android:id="@+id/ivItemScale"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="2.5dp"
        android:layout_marginTop="2.5dp"
        android:src="@mipmap/ic_scale002" />

    <TextView
        android:id="@+id/tvItemName"
        style="@style/text_14_black"
        android:layout_marginHorizontal="@dimen/dp5"
        android:layout_marginTop="@dimen/dp10"
        android:maxLines="1"
        android:text="" />

    <TextView
        android:id="@+id/tvItemBarcode"
        style="@style/text_10_666"
        android:layout_below="@+id/tvItemName"
        android:layout_marginHorizontal="@dimen/dp5"
        android:layout_marginTop="@dimen/dp5"
        android:text="" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tvItemBarcode"
        android:layout_margin="@dimen/dp5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvItemShofar"
            style="@style/text_10_green"
            android:layout_marginTop="@dimen/dp1"
            android:text="@string/money"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvItemPrice"
            style="@style/text_12_green"
            android:text="0.00"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvItemUnit"
            style="@style/text_10_666"
            android:text="" />

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvItemStock"
                style="@style/text_10_black"
                android:background="@drawable/shape_orange_tm_left_2"
                android:paddingHorizontal="@dimen/dp5"
                android:text="0" />

            <TextView
                android:id="@+id/tvItemHouse"
                style="@style/text_10_white"
                android:background="@drawable/shape_orange_right_2"
                android:paddingHorizontal="@dimen/dp2"
                android:text="库" />

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>