<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/points_rule"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp56"
        android:layout_marginEnd="@dimen/dp51">

        <TextView
            style="@style/text_14_black"
            android:layout_centerVertical="true"
            android:text="@string/use_points" />

        <ImageView
            android:id="@+id/ivDialogEnable"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp5"
            android:src="@drawable/selector_checkbox002" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/exchange_points" />

        <TextView
            style="@style/text_14_black"
            android:layout_marginEnd="@dimen/dp5"
            android:text="@string/one_yuan_equal" />

        <LinearLayout
            android:layout_width="@dimen/dp120"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5">

            <TextView
                android:id="@+id/tvDialogPoints"
                style="@style/text_14_black"
                android:text="" />

            <ImageView
                android:id="@+id/ivDialogCursor"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp15"
                android:src="@drawable/anim_cursor" />

            <TextView
                android:id="@+id/tvDialogHint"
                style="@style/text_14_black"
                android:hint="@string/equal_points"
                android:text="" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/count_type" />

        <ImageView
            android:id="@+id/ivDialogType0"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@drawable/selector_checkbox003" />

        <TextView
            style="@style/text_14_black"
            android:layout_marginEnd="@dimen/dp20"
            android:text="@string/half_adjust" />

        <ImageView
            android:id="@+id/ivDialogType1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@drawable/selector_checkbox003" />

        <TextView
            style="@style/text_14_black"
            android:text="@string/not_count" />

    </LinearLayout>

    <com.yxl.cashier_retail.view.NumberKeyBoardView
        android:id="@+id/numberKeyBoardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginVertical="@dimen/dp5" />

</LinearLayout>