<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/ivItemImg"
            android:layout_width="@dimen/dp28"
            android:layout_height="@dimen/dp28"
            android:layout_marginHorizontal="@dimen/dp10"
            android:src="@mipmap/ic_default_img" />

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_14_black"
            android:paddingVertical="@dimen/dp10"
            android:text="" />

        <ImageView
            android:id="@+id/ivItemMore"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_arrow006"
            android:visibility="gone" />

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tvItemDefault"
            style="@style/text_12_999"
            android:padding="@dimen/dp10"
            android:text="@string/cate_default"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/ivItemEdit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_edit002"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/ivItemDel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_del001"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/ivItemAdd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_add004"
            android:visibility="gone" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginHorizontal="@dimen/dp10"
        android:background="@color/color_line" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvItemCate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/color_f2"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

</LinearLayout>