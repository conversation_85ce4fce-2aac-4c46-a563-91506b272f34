<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/white">

        <ImageView
            android:id="@+id/ivBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_back002" />

        <TextView
            style="@style/text_16_black"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/ivBack"
            android:text="返回"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvTitle"
            style="@style/text_18_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text=""
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/linDate"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toStartOf="@+id/tvCashier"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10">

            <TextClock
                style="@style/text_12_black"
                android:format24Hour="MM月dd日 HH:mm"
                android:textStyle="bold" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvCashier"
            style="@style/text_14_white"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/green"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp20"
            android:text="@string/cashier_table"
            android:textStyle="bold"
            app:drawableLeftCompat="@mipmap/ic_cashier001" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="@dimen/dp41"
        android:background="@color/color_line" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dp43"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/ivQrcode"
            android:layout_width="@dimen/dp130"
            android:layout_height="@dimen/dp130"
            android:src="@mipmap/ic_default_img" />

        <ImageView
            android:id="@+id/ivStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/ic_payment_status001" />

        <TextView
            android:id="@+id/tvStatus"
            style="@style/text_10_black"
            android:layout_marginTop="@dimen/dp6"
            android:text="支付成功" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp12"
            android:orientation="horizontal">

            <TextView
                style="@style/text_12_orange"
                android:text="@string/money" />

            <TextView
                android:id="@+id/tvMoney"
                style="@style/text_18_orange"
                android:text="0.00"
                android:textStyle="bold" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvTips"
            style="@style/text_12_999"
            android:layout_marginTop="@dimen/dp18"
            android:text="请使用微信、支付宝或云闪付扫一扫进行付款" />

        <TextView
            android:id="@+id/tvOrderInfo"
            style="@style/text_12_green"
            android:layout_marginTop="@dimen/dp18"
            android:background="@drawable/shape_green_kuang_5"
            android:paddingHorizontal="@dimen/dp20"
            android:paddingVertical="@dimen/dp10"
            android:text="查看订单详情"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="@dimen/dp327"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp24"
            android:background="@drawable/shape_f2_5"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp32"
            android:paddingVertical="@dimen/dp16">

            <TextView
                android:id="@+id/tvOrderNo"
                style="@style/text_12_black"
                android:text="订单号：" />

            <TextView
                android:id="@+id/tvPayee"
                style="@style/text_12_black"
                android:layout_marginTop="@dimen/dp10"
                android:text="收款方：" />

            <TextView
                android:id="@+id/tvPaymentType"
                style="@style/text_12_black"
                android:layout_marginTop="@dimen/dp10"
                android:text="付款方式：" />

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>