<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/white">

        <ImageView
            android:id="@+id/ivBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_back002" />

        <TextView
            style="@style/text_16_black"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/ivBack"
            android:text="商品详情"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/linDate"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toStartOf="@+id/tvCashier"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10">

            <TextClock
                style="@style/text_12_black"
                android:format24Hour="MM月dd日 HH:mm"
                android:textStyle="bold" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvCashier"
            style="@style/text_14_white"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/green"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp20"
            android:text="@string/cashier_table"
            android:textStyle="bold"
            app:drawableLeftCompat="@mipmap/ic_cashier001" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/linTop"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp5"
        android:layout_marginTop="@dimen/dp46"
        android:background="@drawable/shape_white_5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <TextView
            style="@style/text_12_black"
            android:layout_marginEnd="@dimen/dp20"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:text="0.00"
            app:drawableLeftCompat="@mipmap/ic_payment_img011" />

        <TextView
            style="@style/text_12_black"
            android:layout_marginEnd="@dimen/dp10"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:text="0.00"
            app:drawableLeftCompat="@mipmap/ic_payment_img011" />

        <TextView
            android:id="@+id/tvCredit"
            style="@style/text_12_black"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:padding="@dimen/dp10"
            android:text="赊销"
            app:drawableLeftCompat="@mipmap/ic_payment_img011" />

        <TextView
            android:id="@+id/tvOrder"
            style="@style/text_12_black"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:padding="@dimen/dp10"
            android:text="订单"
            app:drawableLeftCompat="@mipmap/ic_payment_img011" />

        <RelativeLayout
            android:id="@+id/relCart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <TextView
                style="@style/text_12_black"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:padding="@dimen/dp10"
                android:text="购物车"
                app:drawableLeftCompat="@mipmap/ic_payment_img011" />

            <TextView
                android:id="@+id/tvCartCount"
                style="@style/text_8_white"
                android:layout_width="@dimen/dp10"
                android:layout_height="@dimen/dp10"
                android:layout_marginStart="@dimen/dp20"
                android:layout_marginTop="@dimen/dp5"
                android:background="@drawable/shape_yuan_green"
                android:gravity="center"
                android:text="0" />

        </RelativeLayout>

        <TextView
            android:id="@+id/tvCoupons"
            style="@style/text_12_black"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:padding="@dimen/dp10"
            android:text="优惠券"
            app:drawableLeftCompat="@mipmap/ic_payment_img011" />

        <TextView
            style="@style/text_12_green"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:padding="@dimen/dp10"
            android:text="助农产品"
            app:drawableLeftCompat="@mipmap/ic_payment_img011" />

    </LinearLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/linTop"
        android:layout_margin="@dimen/dp5"
        android:background="@drawable/shape_white_5">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp56"
            android:paddingVertical="@dimen/dp10">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <FrameLayout
                    android:id="@+id/flBanner"
                    android:layout_width="@dimen/dp280"
                    android:layout_height="@dimen/dp280"
                    android:layout_marginEnd="@dimen/dp10">

                    <!--无图片-->
                    <ImageView
                        android:id="@+id/ivNothing"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:adjustViewBounds="true"
                        android:clickable="true"
                        android:focusable="true"
                        android:focusableInTouchMode="true"
                        android:scaleType="centerCrop"
                        android:src="@mipmap/ic_default_img"
                        android:visibility="gone" />

                    <!--banner-->
                    <com.youth.banner.Banner
                        android:id="@+id/banner"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />

                    <!--视频banner-->
                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <FrameLayout
                            android:id="@+id/flVideo"
                            android:layout_width="match_parent"
                            android:layout_height="326dp"
                            android:scaleType="centerCrop"
                            android:src="@mipmap/ic_default_img" />

                        <ImageView
                            android:id="@+id/ivPlay"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:layout_gravity="center_vertical"
                            android:src="@mipmap/ic_play001"
                            android:visibility="gone" />

                    </RelativeLayout>

                    <TextView
                        android:id="@+id/tvImgCount"
                        style="@style/text_14_white"
                        android:layout_gravity="end|bottom"
                        android:layout_margin="@dimen/dp8"
                        android:background="@drawable/shape_black_tm_5"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text=""
                        android:visibility="gone" />

                </FrameLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_toEndOf="@+id/flBanner"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvName"
                        style="@style/text_14_black"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_999"
                            android:text="月销：" />

                        <TextView
                            android:id="@+id/tvSaleCount"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="0" />

                        <TextView
                            style="@style/text_12_999"
                            android:text="起订量：" />

                        <TextView
                            android:id="@+id/tvStartOrder"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="0" />

                        <TextView
                            style="@style/text_12_999"
                            android:text="库存：" />

                        <TextView
                            android:id="@+id/tvStock"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="0" />

                        <TextView
                            style="@style/text_12_999"
                            android:text="生产日期：" />

                        <TextView
                            android:id="@+id/tvStartDate"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2"
                            android:text="" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_green"
                            android:layout_marginTop="@dimen/dp1"
                            android:text="@string/money"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvPrice"
                            style="@style/text_18_green"
                            android:layout_marginEnd="@dimen/dp10"
                            android:text="0.00"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvPriceDeduct"
                            style="@style/text_10_white"
                            android:background="@drawable/shape_orange_5"
                            android:padding="@dimen/dp3"
                            android:text="金圈币抵扣0.00" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvPriceBefore"
                        style="@style/text_12_999"
                        android:layout_marginVertical="@dimen/dp10"
                        android:text="抵扣前20.00" />

                    <!--活动暂时不加-->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/dp10"
                        android:background="@drawable/shape_f2_5"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <!--优惠券-->
                        <LinearLayout
                            android:id="@+id/linCoupons"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_10_999"
                                android:layout_width="@dimen/dp60"
                                android:layout_marginHorizontal="@dimen/dp5"
                                android:gravity="right"
                                android:text="优惠券" />

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/rvCoupons"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="horizontal"
                                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                            <TextView
                                android:id="@+id/tvCouponsMore"
                                style="@style/text_10_red"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:drawablePadding="@dimen/dp5"
                                android:gravity="center_vertical"
                                android:padding="@dimen/dp10"
                                android:text="领取"
                                app:drawableRightCompat="@mipmap/ic_more003" />

                        </LinearLayout>

                        <!--限购-->
                        <LinearLayout
                            android:id="@+id/linQuota"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_10_999"
                                android:layout_width="@dimen/dp60"
                                android:layout_marginHorizontal="@dimen/dp5"
                                android:gravity="right"
                                android:text="促销" />

                            <TextView
                                style="@style/text_8_red"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="@dimen/dp10"
                                android:background="@drawable/shape_red_kuang_5"
                                android:padding="@dimen/dp3"
                                android:text="限购" />

                            <TextView
                                android:id="@+id/tvQuota"
                                style="@style/text_10_black"
                                android:text="一天仅限购买一次" />

                        </LinearLayout>

                        <!--满赠-->
                        <LinearLayout
                            android:id="@+id/linGift"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <View
                                    android:layout_width="@dimen/dp60"
                                    android:layout_height="0dp"
                                    android:layout_marginHorizontal="@dimen/dp5" />

                                <TextView
                                    style="@style/text_8_red"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="@dimen/dp10"
                                    android:background="@drawable/shape_red_kuang_5"
                                    android:padding="@dimen/dp3"
                                    android:text="满赠" />

                                <TextView
                                    android:id="@+id/tvGiftTips"
                                    style="@style/text_10_black"
                                    android:layout_width="0dp"
                                    android:layout_weight="1"
                                    android:ellipsize="end"
                                    android:maxLines="1"
                                    android:text="满￥1000，获得指定商品;满￥2000，获得指定商品;满￥3000，获得指.." />

                                <TextView
                                    android:id="@+id/tvGiftMore"
                                    style="@style/text_10_red"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:drawablePadding="@dimen/dp5"
                                    android:gravity="center_vertical"
                                    android:padding="@dimen/dp10"
                                    android:text="更多"
                                    app:drawableRightCompat="@mipmap/ic_more003" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <View
                                    android:layout_width="@dimen/dp60"
                                    android:layout_height="0dp"
                                    android:layout_marginHorizontal="@dimen/dp5" />

                                <TextView
                                    style="@style/text_8_red"
                                    android:layout_marginEnd="@dimen/dp10"
                                    android:background="@drawable/shape_red_kuang_5"
                                    android:padding="@dimen/dp3"
                                    android:text="限购"
                                    android:visibility="invisible" />

                                <androidx.recyclerview.widget.RecyclerView
                                    android:id="@+id/rvGift"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:orientation="horizontal"
                                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                            </LinearLayout>

                        </LinearLayout>

                        <!--捆绑-->
                        <LinearLayout
                            android:id="@+id/linBind"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp10"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <View
                                    android:layout_width="@dimen/dp60"
                                    android:layout_height="0dp"
                                    android:layout_marginHorizontal="@dimen/dp5" />

                                <TextView
                                    style="@style/text_8_red"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="@dimen/dp10"
                                    android:background="@drawable/shape_red_kuang_5"
                                    android:padding="@dimen/dp3"
                                    android:text="捆绑" />

                                <TextView
                                    style="@style/text_10_black"
                                    android:text="捆绑销售商品" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center_vertical"
                                android:orientation="horizontal">

                                <View
                                    android:layout_width="@dimen/dp60"
                                    android:layout_height="0dp"
                                    android:layout_marginHorizontal="@dimen/dp5" />

                                <TextView
                                    style="@style/text_8_red"
                                    android:layout_marginEnd="@dimen/dp10"
                                    android:background="@drawable/shape_red_kuang_5"
                                    android:padding="@dimen/dp3"
                                    android:text="限购"
                                    android:visibility="invisible" />

                                <androidx.recyclerview.widget.RecyclerView
                                    android:id="@+id/rvBind"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:orientation="horizontal"
                                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_black"
                            android:layout_marginEnd="@dimen/dp10"
                            android:text="商品规格" />

                        <TextView
                            android:id="@+id/tvSpecs"
                            style="@style/text_10_black"
                            android:background="@drawable/shape_d8_kuang_5"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp6"
                            android:text="" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/ivSub"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="-6dp"
                            android:padding="@dimen/dp6"
                            android:src="@mipmap/ic_sub001" />

                        <TextView
                            android:id="@+id/tvCount"
                            style="@style/text_16_666"
                            android:layout_width="@dimen/dp50"
                            android:layout_height="@dimen/dp28"
                            android:background="@drawable/shape_d8_kuang_5"
                            android:gravity="center"
                            android:text="0" />

                        <ImageView
                            android:id="@+id/ivAdd"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp14"
                            android:padding="@dimen/dp6"
                            android:src="@mipmap/ic_add001" />

                        <TextView
                            android:id="@+id/tvBuy"
                            style="@style/text_12_green"
                            android:layout_marginEnd="@dimen/dp10"
                            android:background="@drawable/shape_green_tm_kuang_5"
                            android:paddingHorizontal="@dimen/dp20"
                            android:paddingVertical="@dimen/dp8"
                            android:text="立即购买"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvCart"
                            style="@style/text_12_white"
                            android:background="@drawable/shape_green_5"
                            android:paddingHorizontal="@dimen/dp20"
                            android:paddingVertical="@dimen/dp8"
                            android:text="加入购物车"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvQuotaTips"
                        style="@style/text_12_red"
                        android:layout_marginTop="@dimen/dp6"
                        android:text="*限购："
                        android:visibility="gone" />

                </LinearLayout>

            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp20"
                android:background="@color/color_f2"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_white"
                    android:background="@color/green"
                    android:paddingHorizontal="@dimen/dp20"
                    android:paddingVertical="@dimen/dp8"
                    android:text="商品简介" />

            </LinearLayout>

            <TextView
                style="@style/text_12_999"
                android:layout_marginTop="@dimen/dp20"
                android:text="规格参数" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="@dimen/dp10"
                android:background="@color/color_line" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="0.5dp"
                    android:layout_height="match_parent"
                    android:background="@color/color_line" />

                <TextView
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:background="@color/color_f2"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp6"
                    android:text="销售价格" />

                <TextView
                    android:id="@+id/tvSpecsPrice"
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:background="@color/white"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/dp10"
                    android:text="0.00"
                    tools:ignore="RtlSymmetry" />

                <TextView
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:background="@color/color_f2"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp6"
                    android:text="商品规格" />

                <TextView
                    android:id="@+id/tvSpecsSpecs"
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:background="@color/white"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/dp10"
                    android:text=""
                    tools:ignore="RtlSymmetry" />

                <TextView
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:background="@color/color_f2"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp6"
                    android:text="销售规格" />

                <TextView
                    android:id="@+id/tvSpecsSpecs1"
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:background="@color/white"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/dp10"
                    android:text=""
                    tools:ignore="RtlSymmetry" />

                <View
                    android:layout_width="0.5dp"
                    android:layout_height="match_parent"
                    android:background="@color/color_line" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/color_line" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="0.5dp"
                    android:layout_height="match_parent"
                    android:background="@color/color_line" />

                <TextView
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:background="@color/color_f2"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp6"
                    android:text="销售单位" />

                <TextView
                    android:id="@+id/tvSpecsUnit"
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:background="@color/white"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/dp10"
                    android:text="0.00"
                    tools:ignore="RtlSymmetry" />

                <TextView
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:background="@color/color_f2"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp6"
                    android:text="销售单位净含量" />

                <TextView
                    android:id="@+id/tvSpecsWeight"
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:background="@color/white"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/dp10"
                    android:text=""
                    tools:ignore="RtlSymmetry" />

                <TextView
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:background="@color/color_f2"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp6"
                    android:text="生产日期" />

                <TextView
                    android:id="@+id/tvSpecsStartDate"
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:background="@color/white"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/dp10"
                    android:text=""
                    tools:ignore="RtlSymmetry" />

                <View
                    android:layout_width="0.5dp"
                    android:layout_height="match_parent"
                    android:background="@color/color_line" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/color_line" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="0.5dp"
                    android:layout_height="match_parent"
                    android:background="@color/color_line" />

                <TextView
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:background="@color/color_f2"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp6"
                    android:text="保质期" />

                <TextView
                    android:id="@+id/tvSpecsEndDate"
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:background="@color/white"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/dp10"
                    android:text="0.00"
                    tools:ignore="RtlSymmetry" />

                <TextView
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:background="@color/color_f2"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp6"
                    android:text="商品品牌" />

                <TextView
                    android:id="@+id/tvSpecsBrand"
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:background="@color/white"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/dp10"
                    android:text=""
                    tools:ignore="RtlSymmetry" />

                <TextView
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:background="@color/color_f2"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp6"
                    android:text="商品大类" />

                <TextView
                    android:id="@+id/tvSpecsCate"
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:background="@color/white"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/dp10"
                    android:text=""
                    tools:ignore="RtlSymmetry" />

                <View
                    android:layout_width="0.5dp"
                    android:layout_height="match_parent"
                    android:background="@color/color_line" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/color_line" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="0.5dp"
                    android:layout_height="match_parent"
                    android:background="@color/color_line" />

                <TextView
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:background="@color/color_f2"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp6"
                    android:text="商品小类" />

                <TextView
                    android:id="@+id/tvSpecsCateChild"
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="3"
                    android:background="@color/white"
                    android:gravity="center_vertical"
                    android:paddingStart="@dimen/dp10"
                    android:text="0.00"
                    tools:ignore="RtlSymmetry" />

                <View
                    android:layout_width="0.5dp"
                    android:layout_height="match_parent"
                    android:background="@color/color_line" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="10" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvIntro"
                style="@style/text_12_black"
                android:layout_marginTop="@dimen/dp20"
                android:text="" />

            <WebView
                android:id="@+id/webView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="@dimen/dp10"
                tools:ignore="WebViewLayout" />

            <LinearLayout
                android:id="@+id/linEmpty"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_empty" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp26"
                    android:text="暂无商品详情"
                    android:textColor="@color/color_666"
                    android:textSize="@dimen/f14"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</RelativeLayout>