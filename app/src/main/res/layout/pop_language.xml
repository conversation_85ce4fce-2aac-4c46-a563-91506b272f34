<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_d8_kuang_5"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvPopLanguage0"
        style="@style/text_14_black"
        android:drawablePadding="@dimen/dp5"
        android:gravity="center_vertical"
        android:padding="@dimen/dp8"
        android:text="中文"
        app:drawableLeftCompat="@mipmap/ic_language001" />

    <TextView
        android:id="@+id/tvPopLanguage1"
        style="@style/text_14_black"
        android:drawablePadding="@dimen/dp5"
        android:gravity="center_vertical"
        android:padding="@dimen/dp8"
        android:text="English"
        app:drawableLeftCompat="@mipmap/ic_language002" />

    <TextView
        android:id="@+id/tvPopLanguage2"
        style="@style/text_14_black"
        android:drawablePadding="@dimen/dp5"
        android:gravity="center_vertical"
        android:padding="@dimen/dp8"
        android:text="แบบไทย"
        app:drawableLeftCompat="@mipmap/ic_language003" />

    <TextView
        android:id="@+id/tvPopLanguage3"
        style="@style/text_14_black"
        android:drawablePadding="@dimen/dp5"
        android:gravity="center_vertical"
        android:padding="@dimen/dp8"
        android:text="Русский"
        android:visibility="gone"
        app:drawableLeftCompat="@mipmap/ic_language004" />

    <TextView
        android:id="@+id/tvPopLanguage4"
        style="@style/text_14_black"
        android:drawablePadding="@dimen/dp5"
        android:gravity="center_vertical"
        android:padding="@dimen/dp8"
        android:text="Melayu"
        app:drawableLeftCompat="@mipmap/ic_language005" />

    <TextView
        android:id="@+id/tvPopLanguage5"
        style="@style/text_14_black"
        android:drawablePadding="@dimen/dp5"
        android:gravity="center_vertical"
        android:padding="@dimen/dp8"
        android:text="قازاقشا"
        app:drawableLeftCompat="@mipmap/ic_language006" />

    <TextView
        android:id="@+id/tvPopLanguage6"
        style="@style/text_14_black"
        android:drawablePadding="@dimen/dp5"
        android:gravity="center_vertical"
        android:padding="@dimen/dp8"
        android:text="Tiếng Việt"
        app:drawableLeftCompat="@mipmap/ic_language007" />

</LinearLayout>