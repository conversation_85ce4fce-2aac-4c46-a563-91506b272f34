<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="2.5dp"
    android:background="@drawable/shape_d8_kuang_5"
    android:orientation="vertical"
    android:padding="@dimen/dp5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/ivItemSelect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp5"
            android:src="@drawable/selector_checkbox003" />

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text=""
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvItemStatus"
            style="@style/text_12_666"
            android:text="" />

    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/dp5">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvItem"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp50"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        <TextView
            android:id="@+id/tvItemCount"
            style="@style/text_12_333"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:gravity="center|end"
            android:text="0" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/total" />

        <TextView
            android:id="@+id/tvItemTotal"
            style="@style/text_12_black"
            android:text="0.00"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>