<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/goods_check_cancel"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="@dimen/dp10">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            tools:ignore="NestedWeights">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/ivDialogImg"
                    android:layout_width="@dimen/dp50"
                    android:layout_height="@dimen/dp50"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="@dimen/dp10"
                    android:scaleType="centerCrop"
                    android:src="@mipmap/ic_default_img" />

                <TextView
                    android:id="@+id/tvDialogName"
                    style="@style/text_12_black"
                    android:layout_marginTop="@dimen/dp5"
                    android:layout_toEndOf="@+id/ivDialogImg"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text=""
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvDialogCountPurchase"
                    style="@style/text_12_999"
                    android:layout_below="@+id/tvDialogName"
                    android:layout_marginTop="@dimen/dp5"
                    android:layout_toEndOf="@+id/ivDialogImg"
                    android:text="@string/ordered_count_colon" />

            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:background="@drawable/shape_f2_5"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingVertical="@dimen/dp8">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvDialogCountDelivery"
                        style="@style/text_12_black"
                        android:text="0" />

                    <TextView
                        style="@style/text_12_666"
                        android:layout_marginTop="@dimen/dp2"
                        android:text="@string/delivery_count" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvDialogInPrice"
                        style="@style/text_12_black"
                        android:text="0.00" />

                    <TextView
                        style="@style/text_12_666"
                        android:layout_marginTop="@dimen/dp2"
                        android:text="@string/purchase_price" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvDialogTotal"
                        style="@style/text_12_black"
                        android:text="0.00" />

                    <TextView
                        style="@style/text_12_666"
                        android:layout_marginTop="@dimen/dp2"
                        android:text="@string/goods_subtotal" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvDialogSuggest"
                        style="@style/text_12_black"
                        android:text="0.00" />

                    <TextView
                        style="@style/text_12_666"
                        android:layout_marginTop="@dimen/dp2"
                        android:text="@string/price_suggest" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    style="@style/text_12_black"
                    android:text="@string/actual_received_count_colon" />

                <TextView
                    android:id="@+id/tvDialogCount"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:background="@drawable/shape_f2_5"
                    android:gravity="center_horizontal"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6"
                    android:text="0" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linDialogStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="@dimen/dp5"
                    style="@style/text_12_black"
                    android:text="@string/actual_in_count_colon"
                    android:visibility="invisible" />

                <TextView
                    android:id="@+id/tvDialogStatus"
                    style="@style/text_12_red"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/goods_short_stock" />

            </LinearLayout>

        </LinearLayout>

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/dp10"
            android:background="@color/color_line" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_999"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:text="@string/price_now" />

                <TextView
                    style="@style/text_12_999"
                    android:layout_width="0dp"
                    android:layout_weight="4"
                    android:text="@string/change_to" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvDialogSalePrice"
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:text="@string/price_sale_colon" />

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/price_sale_colon" />

                <TextView
                    android:id="@+id/tvDialogSalePriceInput"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="3"
                    android:background="@drawable/shape_f2_5"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6"
                    android:text="" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvDialogOnlinePrice"
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:text="@string/price_online_colon" />

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/price_online_colon" />

                <TextView
                    android:id="@+id/tvDialogOnlinePriceInput"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="3"
                    android:background="@drawable/shape_f2_5"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6"
                    android:text="" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvDialogMemberPrice"
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:text="@string/price_member_colon" />

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/price_member_colon" />

                <TextView
                    android:id="@+id/tvDialogMemberPriceInput"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="3"
                    android:background="@drawable/shape_f2_5"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6"
                    android:text="" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/tvDialogCancel"
        style="@style/text_14_black"
        android:layout_width="@dimen/dp200"
        android:layout_gravity="center_horizontal"
        android:layout_margin="@dimen/dp20"
        android:background="@drawable/shape_d8_kuang_5"
        android:gravity="center"
        android:paddingVertical="@dimen/dp10"
        android:text="@string/goods_check_cancel" />

</LinearLayout>