<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/linTop"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp5"
        android:background="@drawable/shape_white_5"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp5">

        <LinearLayout
            android:id="@+id/linCate0"
            android:layout_width="@dimen/dp160"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp10"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvCate0"
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:ellipsize="end"
                android:hint="@string/select_cate"
                android:maxLines="1"
                android:paddingVertical="@dimen/dp6"
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivCate0"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_arrow002" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linCate1"
            android:layout_width="@dimen/dp160"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp10"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvCate1"
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:ellipsize="end"
                android:hint="@string/select_cate"
                android:maxLines="1"
                android:paddingVertical="@dimen/dp6"
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivCate1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_arrow002" />

        </LinearLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tvSyn"
            style="@style/text_12_white"
            android:layout_marginEnd="@dimen/dp10"
            android:background="@drawable/shape_orange_5"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/download_to_scale"
            android:visibility="gone"
            app:drawableStartCompat="@mipmap/ic_download001" />

        <TextView
            android:id="@+id/tvAdd"
            style="@style/text_12_white"
            android:background="@drawable/shape_green_5"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/goods_newly"
            app:drawableStartCompat="@mipmap/ic_add002" />

    </LinearLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/smartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.scwang.smart.refresh.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="2.5dp"
                android:visibility="gone"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="6" />

            <LinearLayout
                android:id="@+id/linEmpty"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="@dimen/dp40">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_empty" />

                <TextView
                    style="@style/text_14_666"
                    android:layout_marginTop="@dimen/dp26"
                    android:textStyle="bold" />

            </LinearLayout>

        </RelativeLayout>

        <com.scwang.smart.refresh.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</LinearLayout>