<?xml version="1.0" encoding="utf-8"?>
<com.scwang.smart.refresh.layout.SmartRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/smartRefreshLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.scwang.smart.refresh.header.ClassicsHeader
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="@dimen/dp3"
                    android:layout_height="@dimen/dp15"
                    android:layout_marginEnd="@dimen/dp5"
                    android:background="@drawable/shape_green_2" />

                <TextView
                    style="@style/text_14_black"
                    android:text="@string/goods_archived" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvGoods0"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="7.5dp"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="4" />

            <LinearLayout
                android:id="@+id/linEmptyGoods0"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="@dimen/dp10"
                android:visibility="gone">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_empty" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp26"
                    android:textColor="@color/color_666"
                    android:textSize="@dimen/f14"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="@dimen/dp3"
                    android:layout_height="@dimen/dp15"
                    android:layout_marginEnd="@dimen/dp5"
                    android:background="@drawable/shape_green_2" />

                <TextView
                    style="@style/text_14_black"
                    android:text="@string/goods_unfilled" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvGoods1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="7.5dp"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="4" />

            <LinearLayout
                android:id="@+id/linEmptyGoods1"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="@dimen/dp10"
                android:visibility="gone">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_empty" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp26"
                    android:textColor="@color/color_666"
                    android:textSize="@dimen/f14"
                    android:textStyle="bold" />

            </LinearLayout>


        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <com.scwang.smart.refresh.footer.ClassicsFooter
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

</com.scwang.smart.refresh.layout.SmartRefreshLayout>