<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@mipmap/ic_login_bg001"
    android:orientation="vertical"
    android:paddingVertical="@dimen/dp10">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvKefu"
            style="@style/text_14_green"
            android:layout_centerVertical="true"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:padding="@dimen/dp10"
            android:text="@string/kefu_phone_colon"
            app:drawableLeftCompat="@mipmap/ic_kefu001" />

        <LinearLayout
            android:id="@+id/linLanguage"
            android:layout_width="@dimen/dp120"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_gravity="right"
            android:layout_marginEnd="@dimen/dp10"
            android:background="@drawable/shape_white_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp8"
            tools:ignore="RtlHardcoded">

            <ImageView
                android:id="@+id/ivLanguage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp6"
                android:src="@mipmap/ic_language001" />

            <TextView
                android:id="@+id/tvLanguage"
                style="@style/text_14_black"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="中文" />

            <ImageView
                android:id="@+id/ivLanguageMore"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/ic_arrow001" />

        </LinearLayout>

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/ic_logo002" />

            <TextView
                style="@style/text_14_green"
                android:layout_marginHorizontal="@dimen/dp20"
                android:layout_marginTop="@dimen/dp14"
                android:text="@string/login_tips" />

            <Button
                android:id="@+id/butUrl"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_margin="@dimen/dp10"
                android:text="设置域名"
                android:textSize="@dimen/f24"
                android:visibility="gone" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <TextView
                    style="@style/text_16_black"
                    android:layout_width="0dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_weight="3"
                    android:gravity="center"
                    android:text="@string/account_login"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:text="@string/login_account"
                    tools:ignore="RtlHardcoded" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/etAccount"
                        style="@style/text_14_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:background="@null"
                        android:hint="@string/input_mobile"
                        android:inputType="phone"
                        android:maxLength="11"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp8"
                        tools:ignore="NestedWeights,TouchTargetSizeCheck" />

                    <ImageView
                        android:id="@+id/ivAccountClear"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/widget_size_6"
                        android:src="@mipmap/ic_close001"
                        android:visibility="gone" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:text="@string/login_pwd"
                    tools:ignore="RtlHardcoded" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/etPwd"
                        style="@style/text_14_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:background="@null"
                        android:hint="@string/input_pwd"
                        android:inputType="textPassword"
                        android:maxLength="16"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp8"
                        tools:ignore="NestedWeights,TouchTargetSizeCheck" />

                    <ImageView
                        android:id="@+id/ivPwdClear"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dp6"
                        android:src="@mipmap/ic_close001"
                        android:visibility="gone" />

                    <ImageView
                        android:id="@+id/ivEye"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dp6"
                        android:src="@drawable/selector_pwd_eye" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp6"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

                <RelativeLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3">

                    <LinearLayout
                        android:id="@+id/linRemember"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp10">

                        <ImageView
                            android:id="@+id/ivRemember"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="@dimen/dp6"
                            android:src="@drawable/selector_checkbox001" />

                        <TextView
                            style="@style/text_12_666"
                            android:text="@string/pwd_remember" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvForget"
                        style="@style/text_12_green"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:padding="@dimen/dp10"
                        android:text="@string/pwd_forget" />

                </RelativeLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp6"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/tvConfirm"
                    style="@style/text_16_white"
                    android:layout_width="0dp"
                    android:layout_weight="3"
                    android:background="@drawable/shape_green_tm_5"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp8"
                    android:text="@string/login" />

            </LinearLayout>

        </LinearLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="0.4" />

    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/dp10">

        <TextView
            android:id="@+id/tvShutdown"
            style="@style/text_12_white"
            android:background="@drawable/shape_red_5"
            android:drawableLeft="@mipmap/ic_shutdown"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp12"
            android:paddingVertical="@dimen/dp8"
            android:text="@string/close_software" />

        <TextView
            android:id="@+id/tvVersion"
            style="@style/text_14_666"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_gravity="right"
            android:text="@string/version"
            tools:ignore="RtlHardcoded" />

    </RelativeLayout>

</LinearLayout>