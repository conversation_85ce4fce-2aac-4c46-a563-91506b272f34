<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E"
    android:orientation="horizontal">

    <LinearLayout
        android:id="@+id/linNothing"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp5"
        android:layout_marginBottom="@dimen/dp5"
        android:layout_weight="1"
        android:background="@drawable/shape_white_5"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="visible">

        <TextView
            style="@style/text_14_black"
            android:drawablePadding="@dimen/dp16"
            android:gravity="center"
            android:text="@string/no_out_information"
            android:textStyle="bold"
            app:drawableTopCompat="@mipmap/ic_default_img" />

        <TextView
            style="@style/text_14_999"
            android:layout_marginTop="@dimen/dp16"
            android:text="@string/click_goods_to_out" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/linInfo"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp5"
        android:layout_marginBottom="@dimen/dp5"
        android:layout_weight="1"
        android:background="@drawable/shape_white_5"
        android:orientation="vertical"
        android:padding="@dimen/dp10"
        android:visibility="gone">

        <LinearLayout
            android:id="@+id/linSupplier"
            android:layout_width="@dimen/dp200"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvSupplier"
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:ellipsize="end"
                android:hint="@string/select_supplier"
                android:maxLines="1"
                android:paddingVertical="@dimen/dp6"
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivSupplier"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_arrow002" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:background="@drawable/shape_f5_top_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5">

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="2"
                android:text="@string/goods_name" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/out_price" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/out_count" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/out_total" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/operate" />

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvInBatch"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:ignore="NestedWeights" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                style="@style/text_12_black"
                android:layout_marginEnd="@dimen/dp10"
                android:text="@string/remarks_info" />

            <EditText
                android:id="@+id/etRemarks"
                style="@style/text_12_black"
                android:layout_width="match_parent"
                android:background="@drawable/shape_d8_kuang_5"
                android:hint="@string/input_remarks_info"
                android:inputType="text"
                android:maxLength="50"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp6" />

        </LinearLayout>

        <TextView
            style="@style/text_12_orange"
            android:layout_marginTop="@dimen/dp10"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:text="@string/out_batch_tips"
            android:visibility="gone"
            app:drawableLeftCompat="@mipmap/ic_tips003" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_black"
                android:text="@string/out_total_colon"
                android:textStyle="bold" />

            <TextView
                style="@style/text_14_red"
                android:text="@string/money"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvTotal"
                style="@style/text_14_red"
                android:text="0.00"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvPrint"
                style="@style/text_14_white"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp10"
                android:background="@drawable/shape_blue_5"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/print_out_order"
                android:visibility="gone"
                app:drawableLeftCompat="@mipmap/ic_print001" />

            <TextView
                android:id="@+id/tvConfirm"
                style="@style/text_14_white"
                android:layout_height="match_parent"
                android:background="@drawable/shape_green_5"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/confirm_out"
                app:drawableLeftCompat="@mipmap/ic_in001" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp5"
            android:background="@drawable/shape_white_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/etSearch"
                style="@style/text_14_333"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:background="@null"
                android:drawableStart="@mipmap/ic_search001"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:hint="@string/search_tips"
                android:inputType="text"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivSearchClear"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_close001"
                android:visibility="gone" />

        </LinearLayout>

        <TextView
            style="@style/text_14_white"
            android:layout_marginHorizontal="@dimen/dp5"
            android:layout_marginTop="@dimen/dp10"
            android:text="@string/click_goods_to_out" />

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/smartRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="7.5dp">

            <com.scwang.smart.refresh.header.ClassicsHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="2.5dp"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:spanCount="3" />

                <LinearLayout
                    android:id="@+id/linEmpty"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/dp40"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/ivEmpty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_empty" />

                    <TextView
                        android:id="@+id/tvEmpty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp26"
                        android:textColor="@color/color_666"
                        android:textSize="@dimen/f14"
                        android:textStyle="bold" />

                </LinearLayout>

            </RelativeLayout>

            <com.scwang.smart.refresh.footer.ClassicsFooter
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    </LinearLayout>

</LinearLayout>