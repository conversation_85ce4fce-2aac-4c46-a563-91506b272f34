<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_10"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="@dimen/dp20">

    <TextView
        style="@style/text_16_black"
        android:text="@string/statistics_field_explain"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp15"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_12_white"
            android:layout_width="@dimen/dp16"
            android:layout_height="@dimen/dp16"
            android:layout_marginEnd="@dimen/dp5"
            android:background="@drawable/shape_yuan_green"
            android:gravity="center"
            android:text="1" />

        <TextView
            style="@style/text_14_black"
            android:text="@string/statistics_field_explain_tips0" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_12_white"
            android:layout_width="@dimen/dp16"
            android:layout_height="@dimen/dp16"
            android:layout_marginEnd="@dimen/dp5"
            android:background="@drawable/shape_yuan_green"
            android:gravity="center"
            android:text="2" />

        <TextView
            style="@style/text_14_black"
            android:text="@string/statistics_field_explain_tips1" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_12_white"
            android:layout_width="@dimen/dp16"
            android:layout_height="@dimen/dp16"
            android:layout_marginEnd="@dimen/dp5"
            android:background="@drawable/shape_yuan_green"
            android:gravity="center"
            android:text="3" />

        <TextView
            style="@style/text_14_black"
            android:text="@string/statistics_field_explain_tips2" />

    </LinearLayout>

    <TextView
        android:id="@+id/tvDialogConfirm"
        style="@style/text_16_white"
        android:layout_marginTop="@dimen/dp20"
        android:background="@drawable/shape_green_5"
        android:paddingHorizontal="@dimen/dp20"
        android:paddingVertical="@dimen/dp10"
        android:text="@string/i_got_it"
        android:textStyle="bold" />

</LinearLayout>