<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp9">

        <TextView
            android:id="@+id/tvItemDiscount"
            style="@style/text_8_white"
            android:layout_marginHorizontal="@dimen/dp1"
            android:background="@drawable/shape_orange_bottom_5"
            android:paddingHorizontal="@dimen/dp4"
            android:paddingBottom="@dimen/dp1"
            android:text="@string/discount"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvItemDiscountMember"
            style="@style/text_8_white"
            android:layout_marginHorizontal="@dimen/dp1"
            android:background="@drawable/shape_orange_bottom_5"
            android:paddingHorizontal="@dimen/dp4"
            android:paddingBottom="@dimen/dp1"
            android:text="会员优惠"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvItemLost"
            style="@style/text_8_white"
            android:layout_marginHorizontal="@dimen/dp1"
            android:background="@drawable/shape_red_bottom_5"
            android:paddingHorizontal="@dimen/dp4"
            android:paddingBottom="@dimen/dp1"
            android:text="@string/sale_price_lower_in_price"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvItemActivity"
            style="@style/text_8_white"
            android:layout_marginHorizontal="@dimen/dp1"
            android:background="@drawable/shape_green_bottom_5"
            android:paddingHorizontal="@dimen/dp4"
            android:paddingBottom="@dimen/dp1"
            android:text="活动商品"
            android:visibility="gone" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/linItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_marginStart="@dimen/dp10"
            android:layout_weight="2"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="" />

        <RelativeLayout
            android:id="@+id/relItemPrice"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/dp5"
            android:layout_weight="1">

            <TextView
                android:id="@+id/tvItemPrice"
                style="@style/text_16_red"
                android:layout_centerHorizontal="true"
                android:gravity="center"
                android:paddingHorizontal="@dimen/dp5"
                android:paddingVertical="@dimen/dp2"
                android:text="0.00"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvItemPrice1"
                style="@style/text_12_999"
                android:layout_below="@+id/tvItemPrice"
                android:layout_centerHorizontal="true"
                android:text="0.00"
                android:visibility="gone" />

        </RelativeLayout>

        <TextView
            android:id="@+id/tvItemCount"
            style="@style/text_16_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:paddingVertical="@dimen/dp5"
            android:text="0.00"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvItemTotal"
            style="@style/text_16_red"
            android:layout_width="0dp"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="1"
            android:gravity="center|end"
            android:paddingVertical="@dimen/dp5"
            android:text="0.00"
            android:textStyle="bold" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_below="@+id/linItem"
        android:layout_marginHorizontal="@dimen/dp10"
        android:background="@color/color_line" />

</RelativeLayout>