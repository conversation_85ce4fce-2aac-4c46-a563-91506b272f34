<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/white">

        <ImageView
            android:id="@+id/ivBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_back002" />

        <TextView
            style="@style/text_16_black"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/ivBack"
            android:text="@string/order_offline"
            android:textStyle="bold" />

        <TextClock
            style="@style/text_12_black"
            android:layout_centerVertical="true"
            android:layout_toStartOf="@+id/tvCashier"
            android:format24Hour="MM-dd HH:mm:ss"
            android:paddingHorizontal="@dimen/dp10"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvCashier"
            style="@style/text_14_white"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/green"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp20"
            android:text="@string/cashier_table"
            android:textStyle="bold"
            app:drawableLeftCompat="@mipmap/ic_cashier001" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="@dimen/dp5"
        android:layout_marginTop="@dimen/dp46"
        android:layout_marginBottom="@dimen/dp5"
        android:background="@drawable/shape_white_5"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp10">

            <TextView
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/order_time" />

            <TextView
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/order_total" />

            <TextView
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/paid_in_money" />

            <TextView
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/goods_count" />

        </LinearLayout>

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/smartRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <com.scwang.smart.refresh.header.ClassicsHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                <LinearLayout
                    android:id="@+id/linEmpty"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/dp40"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_empty" />

                    <TextView
                        style="@style/text_14_666"
                        android:layout_marginTop="@dimen/dp26"
                        android:textStyle="bold" />

                </LinearLayout>

            </RelativeLayout>

            <com.scwang.smart.refresh.footer.ClassicsFooter
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

        <TextView
            android:id="@+id/tvConfirm"
            style="@style/text_14_white"
            android:layout_gravity="end"
            android:layout_margin="@dimen/dp10"
            android:background="@drawable/shape_green_5"
            android:paddingHorizontal="@dimen/dp16"
            android:paddingVertical="@dimen/dp8"
            android:text="@string/submit_one_touch" />

    </LinearLayout>

</RelativeLayout>