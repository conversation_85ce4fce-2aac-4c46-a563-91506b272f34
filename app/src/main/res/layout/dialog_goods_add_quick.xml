<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp6"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvDialogTitle"
            style="@style/text_16_black"
            android:layout_width="0dp"
            android:layout_marginStart="@dimen/dp12"
            android:layout_weight="1"
            android:text=""
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvDialogMore"
            style="@style/text_14_green"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:padding="@dimen/dp10"
            android:text="@string/goods_information_more"
            app:drawableEndCompat="@mipmap/ic_more004" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp12"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_red"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="*" />

            <TextView
                style="@style/text_14_black"
                android:text="@string/goods_barcode" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="2"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/etDialogBarcode"
                style="@style/text_14_black"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:background="@null"
                android:hint="@string/input_goods_barcode"
                android:inputType="number"
                android:maxLines="1"
                android:paddingVertical="@dimen/dp5"
                android:paddingStart="@dimen/dp10" />

            <ImageView
                android:id="@+id/ivDialogBarcode"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingHorizontal="@dimen/dp5"
                android:src="@mipmap/ic_barcode001" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_red"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="*" />

            <TextView
                style="@style/text_14_black"
                android:text="@string/goods_name" />

        </LinearLayout>

        <EditText
            android:id="@+id/etDialogName"
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:background="@drawable/shape_d8_kuang_5"
            android:hint="@string/input_goods_name"
            android:inputType="text"
            android:maxLines="1"
            android:paddingVertical="@dimen/dp5"
            android:paddingStart="@dimen/dp10" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp12"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_red"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="*" />

            <TextView
                style="@style/text_14_black"
                android:text="@string/goods_unit" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linDialogUnit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="2"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5">

            <TextView
                android:id="@+id/tvDialogUnit"
                style="@style/text_14_black"
                android:layout_width="0dp"
                android:layout_marginEnd="@dimen/dp5"
                android:layout_weight="1"
                android:hint="@string/select_goods_unit" />

            <ImageView
                android:id="@+id/ivDialogUnit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/ic_arrow002" />

        </LinearLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="3" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp12"
        android:layout_marginVertical="@dimen/dp5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <TextView
                style="@style/text_14_red"
                android:text="*" />

            <TextView
                style="@style/text_14_black"
                android:text="@string/pricing_type" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linDialogChengType0"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp30"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp10">

            <ImageView
                android:id="@+id/ivDialogChengType0"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp5"
                android:src="@drawable/selector_checkbox003" />

            <TextView
                style="@style/text_14_black"
                android:text="@string/pricing_piece" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linDialogChengType1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp10">

            <ImageView
                android:id="@+id/ivDialogChengType1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp5"
                android:src="@drawable/selector_checkbox003" />

            <TextView
                style="@style/text_14_black"
                android:text="@string/pricing_weight" />

        </LinearLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="3" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp12"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_red"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="*" />

            <TextView
                style="@style/text_14_black"
                android:text="@string/in_price" />

        </LinearLayout>

        <EditText
            android:id="@+id/etDialogInPrice"
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="2"
            android:background="@drawable/shape_d8_kuang_5"
            android:hint="@string/input"
            android:inputType="numberDecimal"
            android:maxLines="1"
            android:paddingVertical="@dimen/dp5"
            android:paddingStart="@dimen/dp10" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_red"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="*" />

            <TextView
                style="@style/text_14_black"
                android:text="@string/price_sale" />

        </LinearLayout>

        <EditText
            android:id="@+id/etDialogSalePrice"
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:background="@drawable/shape_d8_kuang_5"
            android:hint="@string/input"
            android:inputType="numberDecimal"
            android:maxLines="1"
            android:paddingVertical="@dimen/dp5"
            android:paddingStart="@dimen/dp10" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp12"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_black"
                android:text="@string/price_member" />

        </LinearLayout>

        <EditText
            android:id="@+id/etDialogMemberPrice"
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="2"
            android:background="@drawable/shape_d8_kuang_5"
            android:hint="@string/input"
            android:inputType="numberDecimal"
            android:maxLines="1"
            android:paddingVertical="@dimen/dp5"
            android:paddingStart="@dimen/dp10" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_black"
                android:text="@string/stock" />

        </LinearLayout>

        <EditText
            android:id="@+id/etDialogStock"
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:background="@drawable/shape_d8_kuang_5"
            android:hint="@string/input"
            android:inputType="numberDecimal"
            android:maxLines="1"
            android:paddingVertical="@dimen/dp5"
            android:paddingStart="@dimen/dp10" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/dp20"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvDialogCancel"
            style="@style/text_16_white"
            android:layout_width="@dimen/dp186"
            android:layout_marginEnd="@dimen/dp20"
            android:background="@drawable/shape_d8_5"
            android:gravity="center"
            android:padding="@dimen/dp6"
            android:text="取消"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvDialogConfirm"
            style="@style/text_16_white"
            android:layout_width="@dimen/dp186"
            android:background="@drawable/shape_green_5"
            android:gravity="center"
            android:padding="@dimen/dp6"
            android:text="@string/save"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>