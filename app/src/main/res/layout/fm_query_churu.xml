<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dp5"
    android:paddingBottom="@dimen/dp5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_white_5"
        android:orientation="vertical"
        android:padding="@dimen/dp10">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/linType"
                android:layout_width="@dimen/dp125"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvType"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/type_all" />

                <ImageView
                    android:id="@+id/ivType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linStartDate"
                android:layout_width="@dimen/dp125"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvStartDate"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:hint="@string/date_start"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp5"
                    android:text="" />

                <ImageView
                    android:id="@+id/ivStartDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

            <TextView
                style="@style/text_16_999"
                android:layout_marginHorizontal="@dimen/dp3"
                android:text="~" />

            <LinearLayout
                android:id="@+id/linEndDate"
                android:layout_width="@dimen/dp125"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvEndDate"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:hint="@string/date_end"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp5"
                    android:text="" />

                <ImageView
                    android:id="@+id/ivEndDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <TextView
                style="@style/text_14_black"
                android:text="@string/in_amount_colon"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvInTotal"
                style="@style/text_14_green"
                android:layout_marginEnd="@dimen/dp30"
                android:text="0.00"
                android:textStyle="bold" />

            <TextView
                style="@style/text_14_black"
                android:text="@string/out_amount_colon"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvOutTotal"
                style="@style/text_14_orange"
                android:text="0.00"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:background="@drawable/shape_f5_top_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5">

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="2"
                android:gravity="center"
                android:text="@string/goods_name" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="2"
                android:gravity="center"
                android:text="@string/goods_barcode" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/type" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="2"
                android:gravity="center"
                android:text="@string/operate_time" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/operate_before_count" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/modify_count" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/operate_after_count" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/operate_source" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/operate_way" />

        </LinearLayout>

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/smartRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.scwang.smart.refresh.header.ClassicsHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                <LinearLayout
                    android:id="@+id/linEmpty"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/dp40"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/ivEmpty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_empty" />

                    <TextView
                        android:id="@+id/tvEmpty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp26"
                        android:textColor="@color/color_666"
                        android:textSize="@dimen/f14"
                        android:textStyle="bold" />

                </LinearLayout>

            </RelativeLayout>

            <com.scwang.smart.refresh.footer.ClassicsFooter
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    </LinearLayout>

</LinearLayout>