<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/dp5"
    android:background="@drawable/shape_white_5"
    android:orientation="vertical"
    android:padding="@dimen/dp10">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="" />

        <TextView
            android:id="@+id/tvItemReal"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="￥0.00" />

        <TextView
            android:id="@+id/tvItemFree"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="￥0.00" />

        <TextView
            android:id="@+id/tvItemCash"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="￥0.00" />

        <TextView
            android:id="@+id/tvItemTotal"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="￥0.00" />

        <TextView
            android:id="@+id/tvItemDiscount"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="￥0.00" />

        <TextView
            android:id="@+id/tvItemRefund"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="￥0.00" />

        <TextView
            android:id="@+id/tvItemCount"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="0" />

        <TextView
            android:id="@+id/tvItemCountRefund"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="0" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/cashier_staff" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/real_revenue" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/no_password_paid_in" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/cash" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/business_money" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/discount_money" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/refund_money" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/order_count" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/refund_count" />

    </LinearLayout>

</LinearLayout>