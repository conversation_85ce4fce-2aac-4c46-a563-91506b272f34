<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/tips"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/dp20"
        android:src="@mipmap/ic_tips004" />

    <TextView
        style="@style/text_16_black"
        android:layout_marginHorizontal="@dimen/dp28"
        android:gravity="center"
        android:text="@string/restock_submit_tips" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp20"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvDialogCancel"
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="1"
            android:background="@drawable/shape_d8_kuang_f0_5"
            android:gravity="center"
            android:paddingVertical="@dimen/dp8"
            android:text="@string/cancel" />

        <TextView
            android:id="@+id/tvDialogConfirm"
            style="@style/text_12_white"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:background="@drawable/shape_green_5"
            android:gravity="center"
            android:paddingVertical="@dimen/dp8"
            android:text="@string/confirm" />

    </LinearLayout>

</LinearLayout>