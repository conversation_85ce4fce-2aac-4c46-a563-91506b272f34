<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:padding="@dimen/dp20">

    <ImageView
        android:layout_width="@dimen/dp107"
        android:layout_height="@dimen/dp70"
        android:src="@mipmap/ic_payment_status002" />

    <TextView
        style="@style/text_16_red"
        android:layout_marginTop="@dimen/dp10"
        android:text="@string/collection_fail"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvDialogTotal"
        style="@style/text_14_black"
        android:layout_marginTop="@dimen/dp5"
        android:text="0.00"
        android:textStyle="bold" />

    <TextView
        style="@style/text_12_666"
        android:layout_marginTop="@dimen/dp10"
        android:text="@string/fail_reason_colon" />

    <TextView
        android:id="@+id/tvDialogReason"
        style="@style/text_12_666"
        android:gravity="center"
        android:text="" />

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <TextView
        android:id="@+id/tvDialogConfirm"
        style="@style/text_14_white"
        android:background="@drawable/shape_green_5"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp20"
        android:paddingVertical="@dimen/dp8"
        android:text="@string/collection_again" />

</LinearLayout>