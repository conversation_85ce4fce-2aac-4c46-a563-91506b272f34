<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tvItemName"
        style="@style/text_14_black"
        android:layout_below="@+id/tvItemCount"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:layout_marginTop="-8dp"
        android:paddingBottom="@dimen/dp2"
        tools:text="" />

    <TextView
        android:id="@+id/tvItemCount"
        style="@style/text_8_white"
        android:layout_width="@dimen/dp15"
        android:layout_height="@dimen/dp15"
        android:layout_alignParentEnd="true"
        android:background="@drawable/shape_yuan_red"
        android:gravity="center"
        android:text=""
        android:visibility="gone" />

    <View
        android:id="@+id/vItem"
        android:layout_width="@dimen/dp24"
        android:layout_height="@dimen/dp2"
        android:layout_below="@+id/tvItemName"
        android:layout_centerHorizontal="true"
        android:background="@drawable/shape_green_2"
        android:visibility="gone" />

</RelativeLayout>