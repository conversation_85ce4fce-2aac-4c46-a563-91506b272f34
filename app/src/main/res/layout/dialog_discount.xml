<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvDialogTitle"
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/discounts_order"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvDialogEdit"
            style="@style/text_12_orange"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/dp10"
            android:layout_toEndOf="@+id/tvDialogTitle"
            android:background="@drawable/shape_orange_tm_22"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp8"
            android:paddingVertical="@dimen/dp6"
            android:text="@string/edit"
            app:drawableLeftCompat="@mipmap/ic_edit001" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/linDialogTotal"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="3"
            android:padding="@dimen/dp5"
            android:background="@drawable/shape_green_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_12_black"
                android:layout_marginEnd="@dimen/dp5"
                android:maxWidth="@dimen/dp100"
                android:text="@string/total_price" />

            <TextView
                android:id="@+id/tvDialogTotal"
                style="@style/text_12_999"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="0.00" />

            <TextView
                android:id="@+id/tvDialogMoney"
                style="@style/text_16_black"
                android:text="0.00"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/ivDialogCursor"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp15"
                android:layout_gravity="center_vertical"
                android:src="@drawable/anim_cursor" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvDialogZero0"
            style="@style/text_12_red"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:background="@drawable/shape_red_tm_5"
            android:gravity="center"
            android:padding="@dimen/dp5"
            android:text="@string/zero_5_corner" />

        <TextView
            android:id="@+id/tvDialogZero1"
            style="@style/text_12_red"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/shape_red_tm_5"
            android:gravity="center"
            android:padding="@dimen/dp5"
            android:text="@string/zero_1_yuan" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp16"
        android:orientation="horizontal">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvDialogDiscount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="3" />

        <LinearLayout
            android:id="@+id/linDialogCustom"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvDialogCustom"
                style="@style/text_14_black"
                android:text="" />

            <TextView
                android:id="@+id/tvDialogCustomHint"
                style="@style/text_14_black"
                android:gravity="center"
                android:hint="@string/custom" />

            <ImageView
                android:id="@+id/ivDialogCursorCustom"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp18"
                android:layout_gravity="center_vertical"
                android:src="@drawable/anim_cursor"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <com.yxl.cashier_retail.view.NumberKeyBoardView
        android:id="@+id/numberKeyBoardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp51"
        android:layout_marginVertical="@dimen/dp5" />

</LinearLayout>