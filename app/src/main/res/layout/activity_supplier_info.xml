<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/drawerLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_1E201E">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp41"
            android:background="@color/white">

            <ImageView
                android:id="@+id/ivBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:padding="@dimen/dp10"
                android:src="@mipmap/ic_back002" />

            <TextView
                style="@style/text_16_black"
                android:layout_centerVertical="true"
                android:layout_toEndOf="@+id/ivBack"
                android:text="@string/supplier_info"
                android:textStyle="bold" />

            <TextClock
                style="@style/text_12_black"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/dp10"
                android:layout_toStartOf="@id/tvCashier"
                android:format24Hour="MM-dd HH:mm:ss"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvCashier"
                style="@style/text_14_white"
                android:layout_height="match_parent"
                android:layout_alignParentEnd="true"
                android:background="@color/green"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/dp20"
                android:text="@string/cashier_table"
                android:textStyle="bold"
                app:drawableLeftCompat="@mipmap/ic_cashier001" />

        </RelativeLayout>

        <LinearLayout
            android:id="@+id/linTop"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp5"
            android:layout_marginTop="@dimen/dp46"
            android:background="@drawable/shape_white_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5">

            <ImageView
                android:id="@+id/ivHead"
                android:layout_width="@dimen/dp50"
                android:layout_height="@dimen/dp50"
                android:layout_marginEnd="@dimen/dp10"
                android:src="@mipmap/ic_head003" />

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvName"
                    style="@style/text_14_black"
                    android:layout_marginEnd="@dimen/dp5"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text=""
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/ivSupplierImg"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp2"
                    android:layout_toEndOf="@+id/tvName"
                    android:src="@mipmap/ic_supplier_img001" />

                <TextView
                    android:id="@+id/tvMobile"
                    style="@style/text_12_666"
                    android:layout_below="@+id/tvName"
                    android:layout_marginTop="@dimen/dp5"
                    android:text="" />

            </RelativeLayout>

            <View
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="@dimen/dp10"
                android:background="@color/color_line" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvCount"
                    style="@style/text_14_black"
                    android:text="0"
                    android:textStyle="bold" />

                <TextView
                    style="@style/text_12_666"
                    android:layout_marginTop="@dimen/dp5"
                    android:text="@string/order_count" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvTotal"
                    style="@style/text_14_green"
                    android:text="0.00"
                    android:textStyle="bold" />

                <TextView
                    style="@style/text_12_666"
                    android:layout_marginTop="@dimen/dp5"
                    android:text="@string/purchase_total" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvDebt"
                    style="@style/text_14_red"
                    android:text="0"
                    android:textStyle="bold" />

                <TextView
                    style="@style/text_12_666"
                    android:layout_marginTop="@dimen/dp5"
                    android:text="@string/debt_total" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvPayment"
                    style="@style/text_14_orange"
                    android:text="0"
                    android:textStyle="bold" />

                <TextView
                    style="@style/text_12_666"
                    android:layout_marginTop="@dimen/dp5"
                    android:text="@string/settled_total" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:gravity="end|center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvEdit"
                    style="@style/text_12_green"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/dp10"
                    android:background="@drawable/shape_green_kuang_5"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/edit" />

                <TextView
                    android:id="@+id/tvDel"
                    style="@style/text_12_red"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/dp10"
                    android:background="@drawable/shape_red_kuang_5"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/del" />

                <TextView
                    android:id="@+id/tvSettlement"
                    style="@style/text_12_white"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/dp10"
                    android:background="@drawable/shape_green_5"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/settlement" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/linTop"
            android:layout_margin="@dimen/dp5"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp5"
                android:layout_weight="1"
                android:background="@drawable/shape_white_5"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/lin0"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_green_top_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="@dimen/dp10">

                    <ImageView
                        android:id="@+id/iv0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:src="@mipmap/ic_supplier_tab001" />

                    <TextView
                        android:id="@+id/tv0"
                        style="@style/text_12_white"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/supplied_goods" />

                    <TextView
                        android:id="@+id/tvCount0"
                        style="@style/text_12_white"
                        android:layout_marginEnd="@dimen/dp5"
                        android:text="0" />

                    <ImageView
                        android:id="@+id/ivMore0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_more002" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:background="@color/color_line" />

                <LinearLayout
                    android:id="@+id/lin1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="@dimen/dp10">

                    <ImageView
                        android:id="@+id/iv1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:src="@mipmap/ic_supplier_tab012" />

                    <TextView
                        android:id="@+id/tv1"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/purchase_order" />

                    <TextView
                        android:id="@+id/tvCount1"
                        style="@style/text_12_green"
                        android:layout_marginEnd="@dimen/dp5"
                        android:text="0" />

                    <ImageView
                        android:id="@+id/ivMore1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_more001" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:background="@color/color_line" />

                <LinearLayout
                    android:id="@+id/lin2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="@dimen/dp10">

                    <ImageView
                        android:id="@+id/iv2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:src="@mipmap/ic_supplier_tab022" />

                    <TextView
                        android:id="@+id/tv2"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:text="@string/settlement_record" />

                    <TextView
                        android:id="@+id/tvCount2"
                        style="@style/text_12_green"
                        android:layout_marginEnd="@dimen/dp5"
                        android:text="0"
                        android:visibility="gone" />

                    <ImageView
                        android:id="@+id/ivMore2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_more001" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:background="@color/color_line" />

                <LinearLayout
                    android:id="@+id/lin3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="@dimen/dp10"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/iv3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:src="@mipmap/ic_supplier_tab032" />

                    <TextView
                        android:id="@+id/tv3"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/supplier_replace" />

                    <ImageView
                        android:id="@+id/ivMore3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_more001" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:background="@color/color_line"
                    android:visibility="gone" />

            </LinearLayout>

            <FrameLayout
                android:id="@+id/fragment_container"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="4"
                android:background="@drawable/shape_f2_5" />

        </LinearLayout>

    </RelativeLayout>

    <!--右侧边栏-->
    <RelativeLayout
        android:id="@+id/relSliding"
        android:layout_width="600dp"
        android:layout_height="match_parent"
        android:layout_gravity="end"
        android:background="@color/color_f2"
        android:clickable="true"
        android:focusable="true">

        <!--购销单详情-->
        <LinearLayout
            android:id="@+id/linPurchase"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/ivBackPurchase"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp10"
                    android:src="@mipmap/ic_back002" />

                <TextView
                    style="@style/text_16_black"
                    android:text="@string/purchase_info"
                    android:textStyle="bold" />

            </LinearLayout>

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/dp10">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/shape_white_5"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5">

                        <ImageView
                            android:id="@+id/ivPurchaseStatus"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="@dimen/dp10"
                            android:src="@mipmap/ic_order_status001" />

                        <TextView
                            android:id="@+id/tvPurchaseStatus"
                            style="@style/text_14_black"
                            android:layout_toEndOf="@+id/ivPurchaseStatus"
                            android:text=""
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvPurchaseStatusTips"
                            style="@style/text_12_999"
                            android:layout_below="@+id/tvPurchaseStatus"
                            android:layout_marginTop="@dimen/dp10"
                            android:layout_toEndOf="@+id/ivPurchaseStatus"
                            android:text="" />

                    </RelativeLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:background="@drawable/shape_white_5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp10">

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/remarks_colon" />

                        <TextView
                            android:id="@+id/tvPurchaseRemarks"
                            style="@style/text_12_black"
                            android:text="-" />

                    </LinearLayout>

                    <!--价格明细-->
                    <LinearLayout
                        android:id="@+id/linPurchaseGoods"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:background="@drawable/shape_white_5"
                        android:orientation="vertical">

                        <TextView
                            style="@style/text_12_black"
                            android:padding="@dimen/dp10"
                            android:text="@string/price_info" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.5dp"
                            android:background="@color/color_line" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5">

                            <TextView
                                style="@style/text_12_999"
                                android:layout_width="0dp"
                                android:layout_weight="2"
                                android:text="@string/goods_name" />

                            <TextView
                                style="@style/text_12_999"
                                android:layout_width="0dp"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:text="@string/ordered_count" />

                            <TextView
                                style="@style/text_12_999"
                                android:layout_width="0dp"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:text="@string/delivery_count" />

                            <TextView
                                style="@style/text_12_999"
                                android:layout_width="0dp"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:text="@string/purchase_price" />

                            <TextView
                                style="@style/text_12_999"
                                android:layout_width="0dp"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:text="@string/goods_subtotal" />

                            <TextView
                                style="@style/text_12_999"
                                android:layout_width="0dp"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:text="@string/price_suggest" />

                            <TextView
                                style="@style/text_12_999"
                                android:layout_width="0dp"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:text="@string/paid_in_count" />

                            <View
                                android:layout_width="@dimen/dp50"
                                android:layout_height="0dp" />

                        </LinearLayout>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rvPurchaseGoods"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                    </LinearLayout>

                    <!--批次信息-->
                    <LinearLayout
                        android:id="@+id/linPurchaseBatch"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:background="@drawable/shape_white_5"
                        android:orientation="vertical">

                        <TextView
                            style="@style/text_12_black"
                            android:padding="@dimen/dp10"
                            android:text="@string/batch_infos" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.5dp"
                            android:background="@color/color_line" />

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/dp10">

                            <TextView
                                style="@style/text_12_666"
                                android:layout_centerVertical="true"
                                android:text="@string/goods_type" />

                            <TextView
                                android:id="@+id/tvPurchaseCount"
                                style="@style/text_12_black"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:text="0"
                                android:textStyle="bold" />

                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/dp10">

                            <TextView
                                style="@style/text_12_666"
                                android:layout_centerVertical="true"
                                android:text="@string/ordered_total" />

                            <TextView
                                android:id="@+id/tvPurchasePrice"
                                style="@style/text_12_black"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:text="0.00"
                                android:textStyle="bold" />

                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/dp10">

                            <TextView
                                style="@style/text_12_666"
                                android:layout_centerVertical="true"
                                android:text="@string/payable_total" />

                            <TextView
                                android:id="@+id/tvPurchasePayable"
                                style="@style/text_12_black"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:text="0.00"
                                android:textStyle="bold" />

                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/dp10">

                            <TextView
                                style="@style/text_12_666"
                                android:layout_centerVertical="true"
                                android:text="@string/settlement_discount" />

                            <TextView
                                android:id="@+id/tvPurchaseDiscount"
                                style="@style/text_12_black"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:text="0.00"
                                android:textStyle="bold" />

                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/dp10">

                            <TextView
                                style="@style/text_12_666"
                                android:layout_centerVertical="true"
                                android:text="@string/settled_total" />

                            <TextView
                                android:id="@+id/tvPurchaseSettle"
                                style="@style/text_12_black"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:text="0.00"
                                android:textStyle="bold" />

                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/dp10"
                            android:layout_marginBottom="@dimen/dp10">

                            <TextView
                                style="@style/text_12_666"
                                android:layout_centerVertical="true"
                                android:text="@string/pending_total" />

                            <TextView
                                android:id="@+id/tvPurchasePending"
                                style="@style/text_12_black"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"
                                android:text="0.00"
                                android:textStyle="bold" />

                        </RelativeLayout>

                    </LinearLayout>

                    <!--订单信息-->
                    <LinearLayout
                        android:id="@+id/linPurchaseOrder"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp5"
                        android:background="@drawable/shape_white_5"
                        android:orientation="vertical">

                        <TextView
                            style="@style/text_12_black"
                            android:padding="@dimen/dp10"
                            android:text="@string/order_information" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.5dp"
                            android:background="@color/color_line" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp2"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_12_666"
                                android:layout_width="0dp"
                                android:layout_marginStart="@dimen/dp10"
                                android:layout_weight="1"
                                android:text="@string/purchase_no" />

                            <TextView
                                android:id="@+id/tvPurchaseNo"
                                style="@style/text_12_black"
                                android:text="" />

                            <TextView
                                android:id="@+id/tvPurchaseCopy"
                                style="@style/text_12_blue"
                                android:padding="@dimen/dp10"
                                android:text="@string/copy" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp2"
                            android:orientation="horizontal"
                            android:paddingHorizontal="@dimen/dp10">

                            <TextView
                                style="@style/text_12_666"
                                android:layout_width="0dp"
                                android:layout_weight="1"
                                android:text="@string/time_create" />

                            <TextView
                                android:id="@+id/tvPurchaseTime"
                                style="@style/text_12_black"
                                android:text="" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/dp10"
                            android:layout_marginTop="@dimen/dp10"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_12_666"
                                android:layout_width="0dp"
                                android:layout_weight="1"
                                android:text="@string/supplier_name" />

                            <TextView
                                android:id="@+id/tvPurchaseSupplier"
                                style="@style/text_12_333"
                                android:text="" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/dp10"
                            android:layout_marginTop="@dimen/dp10"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_12_666"
                                android:layout_width="0dp"
                                android:layout_weight="1"
                                android:text="@string/contacts" />

                            <TextView
                                android:id="@+id/tvPurchaseContact"
                                style="@style/text_12_333"
                                android:text="" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/dp10"
                            android:layout_marginTop="@dimen/dp10"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_12_666"
                                android:layout_width="0dp"
                                android:layout_weight="1"
                                android:text="@string/contact_mobile" />

                            <TextView
                                android:id="@+id/tvPurchaseMobile"
                                style="@style/text_12_blue"
                                android:text="" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_margin="@dimen/dp10"
                            android:orientation="horizontal">

                            <TextView
                                style="@style/text_12_666"
                                android:layout_marginEnd="@dimen/dp10"
                                android:text="@string/address_where" />

                            <TextView
                                android:id="@+id/tvPurchaseAddress"
                                style="@style/text_12_333"
                                android:layout_width="0dp"
                                android:layout_weight="1"
                                android:gravity="end"
                                android:text="" />

                        </LinearLayout>

                    </LinearLayout>

                    <!--单据凭证-->
                    <LinearLayout
                        android:id="@+id/linPurchaseVoucher"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:background="@drawable/shape_white_5"
                        android:orientation="vertical">

                        <TextView
                            style="@style/text_12_black"
                            android:padding="@dimen/dp10"
                            android:text="@string/purchase_voucher" />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="0.5dp"
                            android:background="@color/color_line" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rvPurchaseVoucher"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/dp5"
                            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                            app:spanCount="3" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.core.widget.NestedScrollView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                android:background="@color/white"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <TextView
                    android:id="@+id/tvPurchaseConfirm"
                    style="@style/text_12_white"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/dp5"
                    android:background="@drawable/shape_green_5"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/confirm_goods_in"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tvPurchasePayment"
                    style="@style/text_12_white"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/dp5"
                    android:background="@drawable/shape_green_5"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/repayment"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tvPurchaseVoucher"
                    style="@style/text_12_green"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/dp5"
                    android:background="@drawable/shape_green_kuang_5"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/purchase_voucher_look"
                    android:visibility="gone" />

            </LinearLayout>

        </LinearLayout>

        <!--结款详情-->
        <LinearLayout
            android:id="@+id/linSettlement"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/ivBackSettlement"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp10"
                    android:src="@mipmap/ic_back002" />

                <TextView
                    style="@style/text_16_black"
                    android:text="@string/settlement_info"
                    android:textStyle="bold" />

            </LinearLayout>

            <TextView
                style="@style/text_12_black"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginTop="@dimen/dp10"
                android:text="@string/repayment_intro" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginTop="@dimen/dp5"
                android:background="@drawable/shape_white_5"
                android:orientation="vertical"
                android:padding="@dimen/dp10">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_12_666"
                        android:text="@string/repayment_name_colon" />

                    <TextView
                        android:id="@+id/tvSettlementName"
                        style="@style/text_12_black"
                        android:text="" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_12_666"
                        android:text="@string/repayment_total_colon" />

                    <TextView
                        android:id="@+id/tvSettlementTotal"
                        style="@style/text_12_black"
                        android:text="" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_12_666"
                        android:text="@string/repayment_remarks_colon" />

                    <TextView
                        android:id="@+id/tvSettlementRemarks"
                        style="@style/text_12_black"
                        android:text="" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_12_666"
                        android:text="@string/repayment_voucher_colon" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvSettlementImg"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp72"
                        android:paddingHorizontal="@dimen/dp5"
                        android:visibility="gone"
                        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                        app:spanCount="3" />

                </LinearLayout>

            </LinearLayout>

            <TextView
                style="@style/text_12_black"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginTop="@dimen/dp10"
                android:text="@string/repayment_order" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginTop="@dimen/dp5"
                android:background="@drawable/shape_green_top_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5">

                <TextView
                    style="@style/text_12_white"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:text="@string/supplier_name" />

                <TextView
                    style="@style/text_12_white"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/contact_mobile" />

                <TextView
                    style="@style/text_12_white"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/goods_type" />

                <TextView
                    style="@style/text_12_white"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/total_amount" />

                <TextView
                    style="@style/text_12_white"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/status_order" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvSettlement"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp10"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        </LinearLayout>

    </RelativeLayout>

</androidx.drawerlayout.widget.DrawerLayout>