<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/green">

        <ImageView
            android:id="@+id/ivLogo"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingHorizontal="@dimen/dp10"
            android:src="@mipmap/ic_logo004" />

        <TextView
            style="@style/text_18_white"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text="@string/statistics"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toStartOf="@+id/tvCashier"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10">

            <TextClock
                style="@style/text_12_white"
                android:format24Hour="MM-dd HH:mm"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp2"
                android:background="@drawable/shape_black_tm_5"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp1">

                <ImageView
                    android:id="@+id/ivNetwork"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5" />

                <TextView
                    android:id="@+id/tvNetwork"
                    style="@style/text_10_white"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tvCashier"
            style="@style/text_14_green"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/white"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp20"
            android:text="@string/cashier_table"
            android:textStyle="bold"
            app:drawableLeftCompat="@mipmap/ic_cashier001" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvDay0"
            style="@style/text_14_white"
            android:layout_marginHorizontal="@dimen/dp5"
            android:background="@drawable/shape_green_5"
            android:paddingHorizontal="@dimen/dp20"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/today" />

        <TextView
            android:id="@+id/tvDay1"
            style="@style/text_14_black"
            android:layout_marginHorizontal="@dimen/dp5"
            android:background="@drawable/shape_white_5"
            android:paddingHorizontal="@dimen/dp20"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/near_day_seven" />

        <TextView
            style="@style/text_14_black"
            android:layout_marginHorizontal="@dimen/dp5"
            android:background="@drawable/shape_white_5"
            android:paddingHorizontal="@dimen/dp20"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/near_day_fifteen"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvDay2"
            style="@style/text_14_black"
            android:layout_marginHorizontal="@dimen/dp5"
            android:background="@drawable/shape_white_5"
            android:paddingHorizontal="@dimen/dp20"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/near_day_thirty" />

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tvType0"
            style="@style/text_14_white"
            android:layout_marginHorizontal="@dimen/dp5"
            android:background="@drawable/shape_green_5"
            android:paddingHorizontal="@dimen/dp20"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/statistics_operating"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvType1"
            style="@style/text_14_black"
            android:layout_marginHorizontal="@dimen/dp5"
            android:background="@drawable/shape_white_5"
            android:paddingHorizontal="@dimen/dp20"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/statistics_payment"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvType2"
            style="@style/text_14_black"
            android:layout_marginHorizontal="@dimen/dp5"
            android:background="@drawable/shape_white_5"
            android:paddingHorizontal="@dimen/dp20"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/statistics_staff"
            android:visibility="gone" />

    </LinearLayout>

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</LinearLayout>