<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dp20"
    android:paddingVertical="@dimen/dp15">

    <TextView
        style="@style/text_16_black"
        android:layout_gravity="center_horizontal"
        android:text="@string/average_price_describe"
        android:textStyle="bold" />

    <TextView
        style="@style/text_14_black"
        android:layout_marginTop="@dimen/dp15"
        android:text="@string/average_price_describe_tips" />

    <TextView
        style="@style/text_14_black"
        android:text="@string/average_price_describe_tips1" />

    <TextView
        android:id="@+id/tvDialogConfirm"
        style="@style/text_12_white"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp15"
        android:background="@drawable/shape_green_5"
        android:paddingHorizontal="@dimen/dp40"
        android:paddingVertical="@dimen/dp10"
        android:text="@string/i_got_it" />

</LinearLayout>