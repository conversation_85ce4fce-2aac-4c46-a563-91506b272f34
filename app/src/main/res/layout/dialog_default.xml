<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp250"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_5"
    android:gravity="center"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvMsg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp20"
        android:layout_marginTop="@dimen/dp35"
        android:layout_marginRight="@dimen/dp20"
        android:layout_marginBottom="@dimen/dp35"
        android:gravity="center"
        android:text=""
        android:textColor="@color/color_333"
        android:textSize="@dimen/f15"
        android:textStyle="bold" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp1"
        android:background="@color/color_line" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_tips_dialog_cancel"
            style="@style/text_14_999"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:padding="@dimen/dp11"
            android:text="@string/cancel" />

        <View
            android:layout_width="@dimen/dp1"
            android:layout_height="match_parent"
            android:background="@color/color_line" />

        <TextView
            android:id="@+id/tv_tips_dialog_confirm"
            style="@style/text_14_green"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:padding="@dimen/dp11"
            android:text="@string/confirm"
            android:textStyle="bold" />
    </LinearLayout>

</LinearLayout>


