<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/discounts_order_edit"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp56"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="1"
            android:gravity="right"
            android:text="@string/zero_default" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvDialogZero0"
                style="@style/text_12_red"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp5"
                android:layout_weight="1"
                android:background="@drawable/shape_red_tm_5"
                android:gravity="center"
                android:paddingVertical="@dimen/dp8"
                android:text="@string/zero_1_yuan"
                tools:ignore="NestedWeights" />

            <TextView
                android:id="@+id/tvDialogZero1"
                style="@style/text_12_red"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp5"
                android:layout_weight="1"
                android:background="@drawable/shape_red_kuang_5"
                android:gravity="center"
                android:paddingVertical="@dimen/dp8"
                android:text="@string/zero_5_corner" />

            <TextView
                android:id="@+id/tvDialogZero2"
                style="@style/text_12_red"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/shape_red_kuang_5"
                android:gravity="center"
                android:paddingVertical="@dimen/dp8"
                android:text="@string/zero_1_corner" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp51"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="1"
            android:gravity="right"
            android:text="@string/discounts_common" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvDialogDiscount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="3" />

    </LinearLayout>

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <com.yxl.cashier_retail.view.NumberKeyBoardView
        android:id="@+id/numberKeyBoardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp51"
        android:layout_marginVertical="@dimen/dp5" />

</LinearLayout>