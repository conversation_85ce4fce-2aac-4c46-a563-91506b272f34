<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/blue">

        <ImageView
            android:id="@+id/ivBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp6"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_back001" />

        <TextView
            style="@style/text_18_white"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text="选择域名"
            android:textStyle="bold" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/etInput"
            style="@style/text_16_333"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="请输入域名"
            android:padding="@dimen/dp15"
            android:text="http://***************:9000/"
            tools:ignore="SpeakableTextPresentCheck" />

        <Button
            android:id="@+id/butInput"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="确认" />

    </LinearLayout>

    <Button
        android:id="@+id/butDebug"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="@dimen/dp10"
        android:text="测试环境"
        android:textSize="@dimen/f24" />

    <Button
        android:id="@+id/butRelease"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="@dimen/dp10"
        android:text="正式环境"
        android:textSize="@dimen/f24" />

    <Button
        android:id="@+id/butDev"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="@dimen/dp10"
        android:text="开发环境"
        android:textSize="@dimen/f24" />

</LinearLayout>