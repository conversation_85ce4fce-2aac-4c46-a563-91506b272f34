<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/face_swiping"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <TextView
        android:id="@+id/tvDialogTips"
        style="@style/text_14_black"
        android:text="@string/payment_face_tips0" />

    <RelativeLayout
        android:layout_width="@dimen/dp300"
        android:layout_height="@dimen/dp250">

        <FrameLayout
            android:layout_width="@dimen/dp196"
            android:layout_height="@dimen/dp196"
            android:layout_centerInParent="true">

            <SurfaceView
                android:id="@+id/surfaceView"
                android:layout_width="@dimen/dp196"
                android:layout_height="@dimen/dp196" />

        </FrameLayout>

        <ImageView
            android:id="@+id/ivDialogImg"
            android:layout_width="@dimen/dp200"
            android:layout_height="@dimen/dp200"
            android:layout_centerInParent="true"
            android:scaleType="centerCrop"
            android:src="@mipmap/ic_default_img"
            android:visibility="gone" />

        <!--RGB预览-->
        <RelativeLayout
            android:layout_width="200dp"
            android:layout_height="200dp"
            android:layout_centerInParent="true"
            android:background="@mipmap/ic_face_bg001" />

    </RelativeLayout>

    <TextView
        android:id="@+id/tvDialogTotal"
        style="@style/text_14_black"
        android:layout_marginBottom="@dimen/dp20"
        android:text="@string/consumption_total_colon" />

</LinearLayout>