<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linItem"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/dp10"
    android:paddingVertical="@dimen/dp5">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="2"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_12_black"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="" />

        <TextView
            android:id="@+id/tvItemBarcode"
            style="@style/text_10_666"
            android:layout_marginTop="@dimen/dp5"
            android:text="" />

    </LinearLayout>

    <TextView
        android:id="@+id/tvItemPrice"
        style="@style/text_12_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="0.00" />

    <TextView
        android:id="@+id/tvItemCount"
        style="@style/text_12_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="0" />

    <TextView
        android:id="@+id/tvItemTotal"
        style="@style/text_12_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="0.00" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center">

        <ImageView
            android:id="@+id/ivItemDel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp5"
            android:src="@mipmap/ic_del002" />

    </LinearLayout>

</LinearLayout>