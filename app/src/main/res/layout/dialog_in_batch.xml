<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/goods_in"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56">

        <ImageView
            android:id="@+id/ivDialogImg"
            android:layout_width="@dimen/dp50"
            android:layout_height="@dimen/dp50"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/dp10"
            android:scaleType="centerCrop"
            android:src="@mipmap/ic_default_img" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@+id/ivDialogImg"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvDialogName"
                style="@style/text_12_black"
                android:layout_marginTop="@dimen/dp5"
                android:ellipsize="end"
                android:maxLines="1"
                android:text=""
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp3"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_10_666"
                    android:text="@string/stock_now_colon" />

                <TextView
                    android:id="@+id/tvDialogStock"
                    style="@style/text_12_green"
                    android:text="0"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp3"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_10_666"
                    android:text="@string/purchase_price_colon" />

                <TextView
                    android:id="@+id/tvDialogInPrice"
                    style="@style/text_12_green"
                    android:text="0.00"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_14_red"
            android:text="*" />

        <TextView
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="1"
            android:text="@string/in_count" />

        <LinearLayout
            android:id="@+id/linDialogCount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvDialogCount"
                    style="@style/text_12_black"
                    android:text="" />

                <ImageView
                    android:id="@+id/ivDialogCursorCount"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp15"
                    android:src="@drawable/anim_cursor" />

                <TextView
                    android:id="@+id/tvDialogCountHint"
                    style="@style/text_12_black"
                    android:hint="@string/input"
                    android:text="" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvDialogUnit"
                style="@style/text_12_black"
                android:text="" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_14_red"
            android:text="*" />

        <TextView
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="1"
            android:text="@string/in_price" />

        <LinearLayout
            android:id="@+id/linDialogPrice"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5">

            <TextView
                android:id="@+id/tvDialogPrice"
                style="@style/text_12_black"
                android:text="" />

            <ImageView
                android:id="@+id/ivDialogCursorPrice"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp15"
                android:src="@drawable/anim_cursor"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvDialogPriceHint"
                style="@style/text_12_black"
                android:hint="@string/input"
                android:text="" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="1"
            android:text="@string/in_total" />

        <TextView
            android:id="@+id/tvDialogTotal"
            style="@style/text_12_green"
            android:layout_width="0dp"
            android:layout_weight="3"
            android:text="@string/no_count" />

    </LinearLayout>

    <com.yxl.cashier_retail.view.NumberKeyBoardView
        android:id="@+id/numberKeyBoardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp51"
        android:layout_marginTop="@dimen/dp15"
        android:layout_marginBottom="@dimen/dp5" />

</LinearLayout>