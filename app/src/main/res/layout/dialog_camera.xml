<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/transparent"
    android:orientation="vertical"
    android:padding="@dimen/dp10">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_5"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvDialogCamera"
            style="@style/text_18_blue"
            android:layout_width="match_parent"
            android:gravity="center"
            android:padding="@dimen/dp15"
            android:text="@string/camera" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp1"
            android:background="@color/color_line" />

        <TextView
            android:id="@+id/tvDialogAlbum"
            style="@style/text_18_blue"
            android:layout_width="match_parent"
            android:gravity="center"
            android:padding="@dimen/dp15"
            android:text="@string/album_select" />

    </LinearLayout>

    <TextView
        android:id="@+id/tvDialogCancel"
        style="@style/text_18_blue"
        android:layout_width="match_parent"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/shape_white_5"
        android:gravity="center"
        android:padding="@dimen/dp15"
        android:text="@string/cancel"
        android:textStyle="bold" />

    <View
        android:layout_width="0dp"
        android:layout_height="@dimen/dp30" />

</LinearLayout>