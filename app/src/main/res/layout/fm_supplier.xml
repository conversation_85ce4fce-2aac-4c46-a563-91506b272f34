<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dp5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_5"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp5">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp20"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvTotal"
                style="@style/text_14_green"
                android:text="0.00"
                android:textStyle="bold" />

            <TextView
                style="@style/text_12_666"
                android:layout_marginTop="@dimen/dp5"
                android:text="@string/purchase_total" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp20"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvDebt"
                style="@style/text_14_red"
                android:text="0.00"
                android:textStyle="bold" />

            <TextView
                style="@style/text_12_666"
                android:layout_marginTop="@dimen/dp5"
                android:text="@string/debt_total" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvPayment"
                style="@style/text_14_orange"
                android:text="0.00"
                android:textStyle="bold" />

            <TextView
                style="@style/text_12_666"
                android:layout_marginTop="@dimen/dp5"
                android:text="@string/settled_total" />

        </LinearLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tvCate"
            style="@style/text_12_green"
            android:layout_marginEnd="@dimen/dp10"
            android:background="@drawable/shape_green_kuang_5"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/supplier_cate" />

        <TextView
            android:id="@+id/tvAdd"
            style="@style/text_12_white"
            android:background="@drawable/shape_green_5"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/supplier_add" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp5"
        android:background="@drawable/shape_white_top_5"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="@dimen/dp10">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp1">

            <TextView
                android:id="@+id/tvSupplier"
                style="@style/text_12_white"
                android:background="@drawable/shape_green_5"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/supplier"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvApply"
                style="@style/text_12_black"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/supplier_apply" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="@dimen/dp5"
        android:background="@drawable/shape_f2_bottom_5"
        android:orientation="vertical">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvCate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="@dimen/dp5"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        <include
            android:id="@+id/vSmartrefreshlayout"
            layout="@layout/layout_smartrefreshlayout" />

    </LinearLayout>

</LinearLayout>