<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/green">

        <ImageView
            android:id="@+id/ivLogo"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingHorizontal="@dimen/dp10"
            android:src="@mipmap/ic_logo004" />

        <LinearLayout
            android:layout_width="@dimen/dp200"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/ivLogo"
            android:background="@drawable/shape_white_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/etSearch"
                style="@style/text_12_333"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:background="@null"
                android:drawableStart="@mipmap/ic_search001"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:hint="@string/search_tips"
                android:imeOptions="actionSearch"
                android:inputType="text"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp4"
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivSearchClear"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp4"
                android:src="@mipmap/ic_close001"
                android:visibility="gone" />

        </LinearLayout>

        <TextView
            style="@style/text_18_white"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text="@string/add_goods_to_cate"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/linDate"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toStartOf="@+id/tvCashier"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10">

            <TextClock
                style="@style/text_12_white"
                android:format24Hour="MM-dd HH:mm"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp2"
                android:background="@drawable/shape_black_tm_5"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp1">

                <ImageView
                    android:id="@+id/ivNetwork"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5" />

                <TextView
                    android:id="@+id/tvNetwork"
                    style="@style/text_10_white"
                    android:text="" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tvCashier"
            style="@style/text_14_green"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/white"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp20"
            android:text="@string/cashier_table"
            android:textStyle="bold"
            app:drawableLeftCompat="@mipmap/ic_cashier001" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dp46"
        android:layout_marginBottom="@dimen/dp5"
        android:orientation="horizontal"
        android:paddingStart="@dimen/dp5"
        tools:ignore="RtlSymmetry">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:background="@drawable/shape_white_5"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="@dimen/dp3"
                    android:layout_height="@dimen/dp15"
                    android:layout_marginEnd="@dimen/dp5"
                    android:background="@drawable/shape_green_2" />

                <TextView
                    android:id="@+id/tvCate"
                    style="@style/text_14_black"
                    android:text="@string/add_goods_to_cate"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/linCate0"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5">

                    <TextView
                        android:id="@+id/tvCate0"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:hint="@string/select_cate"
                        android:maxLines="1" />

                    <ImageView
                        android:id="@+id/ivCate0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_arrow002" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/linCate1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5">

                    <TextView
                        android:id="@+id/tvCate1"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp5"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:hint="@string/select_cate"
                        android:maxLines="1" />

                    <ImageView
                        android:id="@+id/ivCate1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_arrow002" />

                </LinearLayout>

            </LinearLayout>

            <com.scwang.smart.refresh.layout.SmartRefreshLayout
                android:id="@+id/srlGoods"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                tools:ignore="NestedWeights">

                <com.scwang.smart.refresh.header.ClassicsHeader
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvGoods"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="@dimen/dp5"
                        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                        app:spanCount="2" />

                    <LinearLayout
                        android:id="@+id/linEmptyGoods"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingTop="@dimen/dp40">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/ic_empty" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp26"
                            android:textColor="@color/color_666"
                            android:textSize="@dimen/f14"
                            android:textStyle="bold" />

                    </LinearLayout>

                </RelativeLayout>

                <com.scwang.smart.refresh.footer.ClassicsFooter
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </com.scwang.smart.refresh.layout.SmartRefreshLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:orientation="vertical"
            android:visibility="visible">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvCate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="4.5dp"
                app:layoutManager="androidx.recyclerview.widget.StaggeredGridLayoutManager"
                app:spanCount="6" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <com.scwang.smart.refresh.layout.SmartRefreshLayout
                    android:id="@+id/smartRefreshLayout"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    tools:ignore="NestedWeights">

                    <com.scwang.smart.refresh.header.ClassicsHeader
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recyclerView"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:padding="2.5dp"
                            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                            app:spanCount="4" />

                        <LinearLayout
                            android:id="@+id/linEmpty"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:paddingTop="@dimen/dp40"
                            android:visibility="gone">

                            <ImageView
                                android:id="@+id/ivEmpty"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@mipmap/ic_empty" />

                            <TextView
                                android:id="@+id/tvEmpty"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp26"
                                android:textColor="@color/color_666"
                                android:textSize="@dimen/f14"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </RelativeLayout>

                    <com.scwang.smart.refresh.footer.ClassicsFooter
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </com.scwang.smart.refresh.layout.SmartRefreshLayout>

                <LinearLayout
                    android:layout_width="@dimen/dp31"
                    android:layout_height="match_parent"
                    android:background="@drawable/shape_17262b_bottomright_5"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp29"
                        android:gravity="center"
                        android:text="@string/pages"
                        android:textColor="@color/white80"
                        android:textSize="@dimen/f10" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvPage"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>