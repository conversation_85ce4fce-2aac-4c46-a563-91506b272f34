<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linToast"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/dp10"
    android:paddingVertical="@dimen/dp8">

    <ImageView
        android:id="@+id/ivToast"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp5"
        android:src="@drawable/selector_is_right"/>

    <TextView
        android:id="@+id/tvToast"
        style="@style/text_14_white"
        android:text="" />

</LinearLayout>