<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="0dp"
    android:layout_height="match_parent"
    android:layout_weight="3"
    android:background="@color/color_1E201E"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/green"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/linAccount"
            android:layout_width="@dimen/dp140"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp5"
            android:background="@drawable/shape_black_tm_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp8"
            android:paddingVertical="@dimen/dp4">

            <ImageView
                android:id="@+id/ivHead"
                android:layout_width="@dimen/dp20"
                android:layout_height="@dimen/dp20"
                android:layout_marginEnd="@dimen/dp5"
                android:src="@mipmap/ic_head001" />

            <TextView
                android:id="@+id/tvName"
                style="@style/text_12_white"
                android:ellipsize="end"
                android:maxLines="1"
                android:text=""
                android:textStyle="bold" />

        </LinearLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <FrameLayout
            android:id="@+id/flStatistics"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp5"
            android:background="@drawable/shape_black_tm_5">

            <TextView
                style="@style/text_12_white"
                android:layout_gravity="center"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginVertical="@dimen/dp5"
                android:drawablePadding="@dimen/dp2"
                android:gravity="center_vertical"
                android:text="@string/statistics"
                app:drawableLeftCompat="@mipmap/ic_main_top_img003" />

            <View
                android:id="@+id/vStatistics"
                android:layout_width="@dimen/dp8"
                android:layout_height="@dimen/dp8"
                android:layout_gravity="end"
                android:layout_margin="@dimen/dp2"
                android:background="@drawable/shape_yuan_jb_ffcb83"
                android:visibility="gone" />

        </FrameLayout>

        <FrameLayout
            android:id="@+id/flReplenishment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp5"
            android:background="@drawable/shape_black_tm_5"
            android:visibility="gone">

            <TextView
                style="@style/text_12_white"
                android:layout_gravity="center"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginVertical="@dimen/dp5"
                android:drawablePadding="@dimen/dp2"
                android:gravity="center_vertical"
                android:text="@string/replenishment"
                app:drawableLeftCompat="@mipmap/ic_main_top_img003" />

            <View
                android:id="@+id/vReplenishment"
                android:layout_width="@dimen/dp8"
                android:layout_height="@dimen/dp8"
                android:layout_gravity="end"
                android:layout_margin="@dimen/dp2"
                android:background="@drawable/shape_yuan_jb_ffcb83"
                android:visibility="gone" />

        </FrameLayout>

        <FrameLayout
            android:id="@+id/flOrder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp5"
            android:background="@drawable/shape_black_tm_5">

            <TextView
                style="@style/text_12_white"
                android:layout_gravity="center"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginVertical="@dimen/dp5"
                android:drawablePadding="@dimen/dp2"
                android:gravity="center_vertical"
                android:text="@string/netlist"
                app:drawableLeftCompat="@mipmap/ic_main_top_img003" />

            <View
                android:id="@+id/vOrder"
                android:layout_width="@dimen/dp8"
                android:layout_height="@dimen/dp8"
                android:layout_gravity="end"
                android:layout_margin="@dimen/dp2"
                android:background="@drawable/shape_yuan_jb_ffcb83"
                android:visibility="gone" />

        </FrameLayout>

        <FrameLayout
            android:id="@+id/flVersion"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp5"
            android:background="@drawable/shape_black_tm_5">

            <TextView
                style="@style/text_12_white"
                android:layout_gravity="center"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginVertical="@dimen/dp5"
                android:drawablePadding="@dimen/dp2"
                android:gravity="center_vertical"
                android:text="@string/renew"
                app:drawableLeftCompat="@mipmap/ic_main_top_img004" />

            <View
                android:id="@+id/vVersion"
                android:layout_width="@dimen/dp8"
                android:layout_height="@dimen/dp8"
                android:layout_gravity="end"
                android:layout_margin="@dimen/dp2"
                android:background="@drawable/shape_yuan_jb_ffcb83"
                android:visibility="gone" />

        </FrameLayout>

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/ic_line001" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp5"
            android:gravity="center"
            android:orientation="vertical">

            <TextClock
                style="@style/text_12_white"
                android:format24Hour="MM-dd HH:mm"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp2"
                android:background="@drawable/shape_black_tm_5"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp1">

                <ImageView
                    android:id="@+id/ivNetwork"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5" />

                <TextView
                    android:id="@+id/tvNetwork"
                    style="@style/text_10_white"
                    android:text="" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvCate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="4.5dp"
        android:layout_marginTop="4.5dp"
        app:layoutManager="androidx.recyclerview.widget.StaggeredGridLayoutManager"
        app:spanCount="6" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="horizontal">

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/smartRefreshLayout"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <com.scwang.smart.refresh.header.ClassicsHeader
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="2.5dp"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:spanCount="4" />

                <LinearLayout
                    android:id="@+id/linEmpty"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/dp40"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/ivEmpty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_empty" />

                    <TextView
                        android:id="@+id/tvEmpty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp26"
                        android:textColor="@color/color_666"
                        android:textSize="@dimen/f14"
                        android:textStyle="bold" />

                </LinearLayout>

            </RelativeLayout>

            <com.scwang.smart.refresh.footer.ClassicsFooter
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

        <LinearLayout
            android:layout_width="@dimen/dp31"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dp5"
            android:background="@drawable/shape_243940_left_5"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp29"
                android:gravity="center"
                android:text="页数"
                android:textColor="@color/white80"
                android:textSize="@dimen/f10" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvPage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        </LinearLayout>

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvPayment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp5"
        android:layout_marginTop="2.5dp"
        android:layout_marginBottom="@dimen/dp5"
        android:background="@drawable/shape_white_5"
        android:padding="2.5dp"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:spanCount="7" />

</LinearLayout>