<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_5"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="@dimen/dp20">

    <TextView
        style="@style/text_16_black"
        android:text="@string/member_refund"
        android:textStyle="bold" />

    <TextView
        style="@style/text_14_black"
        android:layout_width="match_parent"
        android:layout_marginTop="@dimen/dp20"
        android:text="@string/member_refund_tips" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp20"
        android:layout_marginTop="@dimen/dp20"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvDialogCancel"
            style="@style/text_14_white"
            android:layout_width="0dp"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="1"
            android:background="@drawable/shape_orange_5"
            android:gravity="center"
            android:paddingVertical="@dimen/dp8"
            android:text="@string/cancel"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvDialogConfirm"
            style="@style/text_14_white"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:background="@drawable/shape_red_5"
            android:gravity="center"
            android:paddingVertical="@dimen/dp8"
            android:text="@string/confirm"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>