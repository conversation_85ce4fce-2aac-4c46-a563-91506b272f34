<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:padding="@dimen/dp20">

    <com.yxl.cashier_retail.view.ScannerEditText
        android:id="@+id/etDialogScan"
        android:layout_width="1dp"
        android:layout_height="1dp" />

    <TextView
        style="@style/text_16_black"
        android:paddingHorizontal="@dimen/dp10"
        android:text="@string/jinquan_collection"
        android:textStyle="bold" />

    <ImageView
        android:id="@+id/ivDialogImg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp120"
        android:layout_marginTop="@dimen/dp10"
        android:scaleType="centerCrop"
        android:src="@drawable/cashiering002" />

    <TextView
        style="@style/text_14_999"
        android:layout_marginTop="@dimen/dp10"
        android:text="@string/place_scan_customer_payment_code" />

    <TextView
        android:id="@+id/tvDialogTotal"
        style="@style/text_18_black"
        android:layout_marginTop="@dimen/dp10"
        android:text="0.00"
        android:textStyle="bold" />

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <TextView
        android:id="@+id/tvDialogCancel"
        style="@style/text_14_red"
        android:drawablePadding="@dimen/dp5"
        android:padding="@dimen/dp10"
        android:text="@string/collection_cancel"
        app:drawableStartCompat="@mipmap/ic_close004" />

</LinearLayout>