<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linDialog"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#0F181B"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dp80">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal">

        <RelativeLayout
            android:id="@+id/relDialog0"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:src="@mipmap/ic_menu001" />

            <TextView
                style="@style/text_18_white"
                android:layout_width="@dimen/dp107"
                android:layout_height="@dimen/dp48"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp58"
                android:gravity="center"
                android:maxLines="2"
                android:text="@string/cashier"
                android:textStyle="bold" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/relDialog1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:src="@mipmap/ic_menu002" />

            <TextView
                style="@style/text_18_white"
                android:layout_width="@dimen/dp107"
                android:layout_height="@dimen/dp48"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp58"
                android:gravity="center"
                android:maxLines="2"
                android:text="@string/goods"
                android:textStyle="bold" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/relDialog5"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:src="@mipmap/ic_menu006" />

            <TextView
                style="@style/text_18_white"
                android:layout_width="@dimen/dp107"
                android:layout_height="@dimen/dp48"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp58"
                android:gravity="center"
                android:maxLines="2"
                android:text="@string/member"
                android:textStyle="bold" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/relDialog4"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:src="@mipmap/ic_menu005" />

            <TextView
                style="@style/text_18_white"
                android:layout_width="@dimen/dp107"
                android:layout_height="@dimen/dp48"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp58"
                android:gravity="center"
                android:maxLines="2"
                android:text="@string/netlist"
                android:textStyle="bold" />

        </RelativeLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp48"
        android:gravity="center"
        android:orientation="horizontal">

        <RelativeLayout
            android:id="@+id/relDialog2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:src="@mipmap/ic_menu003" />

            <TextView
                style="@style/text_18_white"
                android:layout_width="@dimen/dp107"
                android:layout_height="@dimen/dp48"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp58"
                android:gravity="center"
                android:maxLines="2"
                android:text="@string/query"
                android:textStyle="bold" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/relDialog3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:src="@mipmap/ic_menu004" />

            <TextView
                style="@style/text_18_white"
                android:layout_width="@dimen/dp107"
                android:layout_height="@dimen/dp48"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp58"
                android:gravity="center"
                android:maxLines="2"
                android:text="@string/statistics"
                android:textStyle="bold" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/relDialog6"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:visibility="gone">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:src="@mipmap/ic_menu007" />

            <TextView
                style="@style/text_18_white"
                android:layout_width="@dimen/dp107"
                android:layout_height="@dimen/dp48"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp58"
                android:gravity="center"
                android:maxLines="2"
                android:text="@string/replenishment"
                android:textStyle="bold" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/relDialog7"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:visibility="gone">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:src="@mipmap/ic_menu008" />

            <TextView
                style="@style/text_18_white"
                android:layout_width="@dimen/dp107"
                android:layout_height="@dimen/dp48"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp58"
                android:gravity="center"
                android:maxLines="2"
                android:text="@string/marketing"
                android:textStyle="bold" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/relDialog9"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:src="@mipmap/ic_menu010" />

            <TextView
                style="@style/text_18_white"
                android:layout_width="@dimen/dp107"
                android:layout_height="@dimen/dp48"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp58"
                android:gravity="center"
                android:maxLines="2"
                android:text="@string/setting"
                android:textStyle="bold" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/relDialog8"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:src="@mipmap/ic_menu009" />

            <TextView
                style="@style/text_18_white"
                android:layout_width="@dimen/dp107"
                android:layout_height="@dimen/dp48"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp58"
                android:gravity="center"
                android:maxLines="2"
                android:text="@string/shift_handover"
                android:textStyle="bold" />

        </RelativeLayout>

    </LinearLayout>

</LinearLayout>