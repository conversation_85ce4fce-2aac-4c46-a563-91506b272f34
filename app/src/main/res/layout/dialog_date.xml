<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_5"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvDialogCancel"
            style="@style/text_14_999"
            android:padding="@dimen/dp16"
            android:text="@string/cancel" />

        <TextView
            style="@style/text_16_black"
            android:layout_centerInParent="true"
            android:text="@string/select_date"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvDialogConfirm"
            style="@style/text_14_green"
            android:layout_alignParentEnd="true"
            android:padding="@dimen/dp16"
            android:text="@string/confirm" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp1"
        android:background="@color/color_f2" />

    <com.yxl.cashier_retail.view.pickerview.DateWheelLayout
        android:id="@+id/wheelLayout"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        app:wheel_dateMode="year_month_day" />

</LinearLayout>