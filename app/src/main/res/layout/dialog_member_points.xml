<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvDialogTitle"
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text=""
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:background="@drawable/shape_green_kuang_5"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp10">

        <TextView
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_marginVertical="@dimen/dp8"
            android:layout_weight="1"
            android:text="@string/points_count" />

        <TextView
            android:id="@+id/tvDialogPoints"
            style="@style/text_18_black"
            android:text=""
            android:textStyle="bold"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvDialogHints"
            style="@style/text_14_black"
            android:hint="@string/input_points_count" />

        <ImageView
            android:id="@+id/ivDialogCursor"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp18"
            android:layout_gravity="center_vertical"
            android:src="@drawable/anim_cursor" />

    </LinearLayout>

    <com.yxl.cashier_retail.view.NumberKeyBoardView
        android:id="@+id/numberKeyBoardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp25"
        android:layout_marginBottom="@dimen/dp5"
        android:layout_marginHorizontal="@dimen/dp56" />

</LinearLayout>