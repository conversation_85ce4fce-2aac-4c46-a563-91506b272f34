<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/dp5"
    android:background="@drawable/shape_white_5"
    android:gravity="center"
    android:orientation="horizontal"
    android:padding="@dimen/dp5">

    <TextView
        style="@style/text_12_black"
        android:layout_marginEnd="@dimen/dp10"
        android:text="@string/chong" />

    <TextView
        android:id="@+id/tvItemMoney"
        style="@style/text_14_red"
        android:layout_marginEnd="@dimen/dp10"
        android:text="0.00" />

    <TextView
        style="@style/text_12_black"
        android:layout_marginEnd="@dimen/dp10"
        android:text="@string/give" />

    <TextView
        android:id="@+id/tvItemGift"
        style="@style/text_12_black"
        android:text="0.00" />

</LinearLayout>