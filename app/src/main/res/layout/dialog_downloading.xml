<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical"
    android:padding="20dp">

    <androidx.core.widget.ContentLoadingProgressBar
        android:id="@+id/pb"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="15dp"
        android:max="100"
        android:progress="0"
        android:progressDrawable="@drawable/progressbar_horizontal" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="2dp"
            android:text="@string/version_downloading"
            android:textColor="@color/blue"
            android:textSize="15sp" />

        <TextView
            android:id="@+id/tv_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:textColor="@color/blue"
            android:textSize="15sp" />
    </RelativeLayout>
</LinearLayout>