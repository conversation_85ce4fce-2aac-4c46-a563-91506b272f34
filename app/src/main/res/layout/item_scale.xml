<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="@dimen/dp5"
    android:background="@drawable/shape_d8_kuang_5"
    android:orientation="horizontal"
    android:paddingVertical="@dimen/dp10">

    <ImageView
        android:layout_width="@dimen/dp60"
        android:layout_height="@dimen/dp60"
        android:layout_marginEnd="@dimen/dp16"
        android:src="@mipmap/ic_scale001" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tvItemName"
                style="@style/text_16_black"
                android:layout_centerVertical="true"
                android:text="条码秤1"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/ivItemDel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp6"
                android:src="@mipmap/ic_del002" />

        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="启用电子秤" />

            <ImageView
                android:id="@+id/ivItemSwitch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp6"
                android:src="@drawable/selector_checkbox002" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linItemIp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp6"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_black"
                android:layout_width="0dp"
                android:layout_marginEnd="@dimen/dp80"
                android:layout_weight="1"
                android:text="IP地址" />

            <EditText
                android:id="@+id/etItemIp"
                style="@style/text_12_black"
                android:layout_width="@dimen/dp140"
                android:layout_marginEnd="@dimen/dp10"
                android:background="@drawable/shape_d8_kuang_5"
                android:hint="请输入IP地址"
                android:inputType="text"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp8" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linItemStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp6"
            android:layout_marginEnd="@dimen/dp10"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_black"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="电子秤状态" />

            <View
                android:id="@+id/vItemStatus"
                android:layout_width="@dimen/dp10"
                android:layout_height="@dimen/dp10"
                android:layout_marginEnd="@dimen/dp6"
                android:background="@drawable/shape_yuan_red" />

            <TextView
                android:id="@+id/tvItemStatus"
                style="@style/text_14_red"
                android:text="未连接" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>