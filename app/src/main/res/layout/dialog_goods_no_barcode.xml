<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvDialogTitle"
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text=""
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvDialogCountValue"
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/count_colon" />

        <TextView
            android:id="@+id/tvDialogCount"
            style="@style/text_14_666"
            android:layout_width="0dp"
            android:layout_weight="5"
            android:background="@drawable/shape_d8_kuang_f0_5"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5"
            android:text="1" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/price_colon" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="2"
            android:background="@drawable/shape_green_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp5">

            <TextView
                android:id="@+id/tvDialogPrice"
                style="@style/text_14_black"
                android:layout_marginVertical="@dimen/dp5"
                android:text="0" />

            <ImageView
                android:id="@+id/ivDialogCursor"
                android:layout_width="@dimen/dp3"
                android:layout_height="@dimen/dp15"
                android:src="@drawable/anim_cursor" />

        </LinearLayout>

        <TextView
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/total_colon" />

        <TextView
            android:id="@+id/tvDialogTotal"
            style="@style/text_14_666"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:background="@drawable/shape_d8_kuang_f0_5"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5"
            android:text="0.00" />

    </LinearLayout>

    <com.yxl.cashier_retail.view.NumberKeyBoardView
        android:id="@+id/numberKeyBoardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp51"
        android:layout_marginTop="@dimen/dp15"
        android:layout_marginBottom="@dimen/dp5" />

</LinearLayout>