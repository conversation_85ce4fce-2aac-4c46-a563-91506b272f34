<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/dp10"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_f5_top_5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/ivItemSelect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@drawable/selector_checkbox003" />

        <TextView
            android:id="@+id/tvItemCompany"
            style="@style/text_12_black"
            android:layout_marginEnd="@dimen/dp20"
            android:text=""
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp20"
            android:layout_weight="1"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvItemDelivery"
                style="@style/text_12_red"
                android:text=""
                android:visibility="gone" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linItemShipped"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                style="@style/text_10_666"
                android:text="还差" />

            <TextView
                android:id="@+id/tvItemShippedMoney"
                style="@style/text_10_red"
                android:text="￥0.00" />

            <TextView
                style="@style/text_10_666"
                android:text="包邮，" />

            <TextView
                android:id="@+id/tvItemShipped"
                style="@style/text_10_red"
                android:gravity="center_vertical"
                android:text="去凑包邮"
                app:drawableRightCompat="@mipmap/ic_more003" />

        </LinearLayout>

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

</LinearLayout>
