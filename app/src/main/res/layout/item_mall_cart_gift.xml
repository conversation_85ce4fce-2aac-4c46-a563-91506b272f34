<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp148"
    android:layout_height="@dimen/dp48"
    android:layout_marginEnd="@dimen/dp20">

    <ImageView
        android:id="@+id/ivItemImg"
        android:layout_width="@dimen/dp48"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/dp5"
        android:src="@mipmap/ic_default_img" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_toEndOf="@+id/ivItemImg">

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_10_666"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true">

            <TextView
                android:id="@+id/tvItemPrice"
                style="@style/text_10_666"
                android:layout_centerVertical="true"
                android:text="￥0.00" />

            <TextView
                android:id="@+id/tvItemCount"
                style="@style/text_10_666"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:text="x0" />

        </RelativeLayout>

    </RelativeLayout>

</RelativeLayout>