<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="horizontal"
    android:padding="@dimen/dp4">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="4"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <Button
                android:id="@+id/but1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp1"
                android:layout_weight="1"
                android:background="@drawable/selector_nkb"
                android:text="1"
                android:textColor="@drawable/selector_nkb_textcolor"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />

            <Button
                android:id="@+id/but2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp1"
                android:layout_weight="1"
                android:background="@drawable/selector_nkb"
                android:text="2"
                android:textColor="@drawable/selector_nkb_textcolor"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />

            <Button
                android:id="@+id/but3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp1"
                android:layout_weight="1"
                android:background="@drawable/selector_nkb"
                android:text="3"
                android:textColor="@drawable/selector_nkb_textcolor"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />

            <Button
                android:id="@+id/but0"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp1"
                android:layout_weight="1"
                android:background="@drawable/selector_nkb"
                android:text="0"
                android:textColor="@drawable/selector_nkb_textcolor"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/but4"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp1"
                android:layout_weight="1"
                android:background="@drawable/selector_nkb"
                android:text="4"
                android:textColor="@drawable/selector_nkb_textcolor"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />

            <Button
                android:id="@+id/but5"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp1"
                android:layout_weight="1"
                android:background="@drawable/selector_nkb"
                android:text="5"
                android:textColor="@drawable/selector_nkb_textcolor"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />

            <Button
                android:id="@+id/but6"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp1"
                android:layout_weight="1"
                android:background="@drawable/selector_nkb"
                android:text="6"
                android:textColor="@drawable/selector_nkb_textcolor"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />

            <Button
                android:id="@+id/butDrop"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp1"
                android:layout_weight="1"
                android:background="@drawable/selector_nkb"
                android:text="•"
                android:textColor="@drawable/selector_nkb_textcolor"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/but7"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp1"
                android:layout_weight="1"
                android:background="@drawable/selector_nkb"
                android:text="7"
                android:textColor="@drawable/selector_nkb_textcolor"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />

            <Button
                android:id="@+id/but8"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp1"
                android:layout_weight="1"
                android:background="@drawable/selector_nkb"
                android:text="8"
                android:textColor="@drawable/selector_nkb_textcolor"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />

            <Button
                android:id="@+id/but9"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp1"
                android:layout_weight="1"
                android:background="@drawable/selector_nkb"
                android:text="9"
                android:textColor="@drawable/selector_nkb_textcolor"
                android:textSize="@dimen/f26"
                android:textStyle="bold" />

            <Button
                android:id="@+id/butClear"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/dp1"
                android:layout_weight="1"
                android:background="@drawable/selector_nkb"
                android:text="@string/clear"
                android:textColor="@drawable/selector_nkb_textcolor"
                android:textSize="@dimen/f14"
                android:textStyle="bold" />
        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/linMoney"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:visibility="gone">

        <LinearLayout
            android:id="@+id/linMoney0"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_margin="@dimen/dp1"
            android:layout_weight="1"
            android:background="@drawable/selector_nkb"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_orange"
                android:layout_marginTop="@dimen/dp2"
                android:text="@string/money"
                android:textStyle="bold" />

            <TextView
                style="@style/text_16_orange"
                android:text="100"
                android:textSize="@dimen/f20"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linMoney1"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_margin="@dimen/dp1"
            android:layout_weight="1"
            android:background="@drawable/selector_nkb"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_orange"
                android:layout_marginTop="@dimen/dp2"
                android:text="@string/money"
                android:textStyle="bold" />

            <TextView
                style="@style/text_16_orange"
                android:text="50"
                android:textSize="@dimen/f20"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linMoney2"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_margin="@dimen/dp1"
            android:layout_weight="1"
            android:background="@drawable/selector_nkb"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_orange"
                android:layout_marginTop="@dimen/dp2"
                android:text="@string/money"
                android:textStyle="bold" />

            <TextView
                style="@style/text_16_orange"
                android:text="10"
                android:textSize="@dimen/f20"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical">

        <ImageButton
            android:id="@+id/butDel"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_margin="@dimen/dp1"
            android:layout_weight="1"
            android:background="@drawable/shape_d8_kuang_5"
            android:src="@mipmap/ic_nkb_close"
            tools:ignore="NestedWeights" />

        <Button
            android:id="@+id/butConfirm"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_margin="@dimen/dp1"
            android:layout_weight="2"
            android:background="@drawable/shape_green_5"
            android:text="@string/confirm"
            android:textColor="@color/white"
            android:textSize="@dimen/f16"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>