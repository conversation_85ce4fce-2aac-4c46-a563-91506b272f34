<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/linItem"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/dp5"
    android:background="@drawable/shape_white_5"
    android:orientation="vertical"
    android:padding="@dimen/dp10">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_14_black"
            android:layout_marginEnd="@dimen/dp4"
            android:text=""
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivItemImg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/ic_supplier_img001"
            android:visibility="gone" />

    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvItem"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp50"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        <TextView
            android:id="@+id/tvItemCount"
            style="@style/text_12_333"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:gravity="center|end"
            android:text="0" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_12_black"
            android:text="@string/remarks_colon" />

        <EditText
            android:id="@+id/etItemRemarks"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:background="@null"
            android:gravity="end"
            android:hint="@string/input_remarks_info"
            android:paddingVertical="@dimen/dp10"
            android:paddingStart="@dimen/dp10" />

    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_12_black"
            android:layout_centerVertical="true"
            android:text="@string/price_estimate_colon" />

        <TextView
            android:id="@+id/tvItemTotal"
            style="@style/text_12_black"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="0.00"
            android:textStyle="bold" />

    </RelativeLayout>

</LinearLayout>