<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_10"
        android:orientation="vertical">

        <TextView
            style="@style/text_18_333"
            android:layout_gravity="center"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="15dp"
            android:text="@string/version_download_fail" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@android:color/white" />

        <TextView
            style="@style/text_14_999"
            android:layout_width="match_parent"
            android:paddingLeft="5dp"
            android:paddingTop="10dp"
            android:paddingEnd="5dp"
            android:paddingBottom="10dp"
            android:text="@string/version_download_tips" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@android:color/white" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="10dp"
            android:paddingBottom="10dp">

            <TextView
                android:id="@id/versionchecklib_failed_dialog_cancel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/cancel"
                android:textSize="15sp" />

            <View
                android:layout_width="1dp"
                android:layout_height="20dp"
                android:background="@color/color_f2" />

            <TextView
                android:id="@id/versionchecklib_failed_dialog_retry"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/retry"
                android:textColor="@color/blue"
                android:textSize="15sp" />

        </LinearLayout>
    </LinearLayout>
</LinearLayout>