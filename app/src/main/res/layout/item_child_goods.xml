<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp42"
    android:layout_height="@dimen/dp42"
    android:layout_marginEnd="@dimen/dp5">

    <ImageView
        android:id="@+id/ivItemImg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@mipmap/ic_default_img" />

    <TextView
        android:id="@+id/tvItemName"
        style="@style/text_8_white"
        android:layout_width="match_parent"
        android:layout_alignParentBottom="true"
        android:background="@drawable/shape_333_tm_bottom_2"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp5"
        android:paddingVertical="@dimen/dp1"
        android:text="" />

</RelativeLayout>