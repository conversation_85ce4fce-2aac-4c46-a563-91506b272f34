<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvDialogTitle"
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text=""
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <TextView
        style="@style/text_14_black"
        android:layout_marginHorizontal="@dimen/dp56"
        android:text="@string/supplier_name" />

    <EditText
        android:id="@+id/etDialogName"
        style="@style/text_14_black"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/shape_d8_kuang_5"
        android:hint="@string/input_supplier_name"
        android:inputType="text"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp5" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp51"
        android:layout_marginVertical="@dimen/dp15"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvDialogConfirm"
            style="@style/text_14_white"
            android:layout_width="0dp"
            android:layout_gravity="center"
            android:layout_margin="@dimen/dp5"
            android:layout_marginVertical="@dimen/dp10"
            android:layout_weight="1"
            android:background="@drawable/shape_green_5"
            android:gravity="center"
            android:paddingVertical="@dimen/dp10"
            android:text="@string/save" />

        <TextView
            android:id="@+id/tvDialogDel"
            style="@style/text_14_white"
            android:layout_width="0dp"
            android:layout_gravity="center"
            android:layout_margin="@dimen/dp5"
            android:layout_marginVertical="@dimen/dp10"
            android:layout_weight="1"
            android:background="@drawable/shape_red_5"
            android:gravity="center"
            android:paddingVertical="@dimen/dp10"
            android:text="@string/del"
            android:visibility="gone" />

    </LinearLayout>

</LinearLayout>