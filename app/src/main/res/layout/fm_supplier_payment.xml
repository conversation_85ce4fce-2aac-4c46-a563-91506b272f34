<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_f2_5"
    android:orientation="vertical"
    android:padding="@dimen/dp10">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_green_top_5"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp5">

        <TextView
            style="@style/text_12_white"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:text="@string/repayment_time" />

        <TextView
            style="@style/text_12_white"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:gravity="center"
            android:text="@string/repayment_name" />

        <TextView
            style="@style/text_12_white"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/repayment_total" />

        <TextView
            style="@style/text_12_white"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:gravity="center"
            android:text="@string/remarks" />

        <TextView
            style="@style/text_12_white"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/status_repayment" />

    </LinearLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/smartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.scwang.smart.refresh.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            <LinearLayout
                android:id="@+id/linEmpty"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="@dimen/dp40"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/ivEmpty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_empty" />

                <TextView
                    android:id="@+id/tvEmpty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp26"
                    android:textColor="@color/color_666"
                    android:textSize="@dimen/f14"
                    android:textStyle="bold" />

            </LinearLayout>

        </RelativeLayout>

        <com.scwang.smart.refresh.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</LinearLayout>