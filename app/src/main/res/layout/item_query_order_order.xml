<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/relItem"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/ivItemScale"
        android:layout_width="@dimen/dp14"
        android:layout_height="@dimen/dp14"
        android:src="@mipmap/ic_scale002"
        android:visibility="gone" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="@dimen/dp5">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvItemName"
                style="@style/text_12_black"
                android:layout_marginEnd="@dimen/dp2"
                android:text="" />

            <TextView
                android:id="@+id/tvItemRefund"
                style="@style/text_10_white"
                android:background="@drawable/shape_red_2"
                android:paddingHorizontal="@dimen/dp2"
                android:text="@string/refund_sign"
                android:visibility="gone" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvItemCount"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="0" />

        <TextView
            android:id="@+id/tvItemPrice"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="0.00" />

    </LinearLayout>

</RelativeLayout>