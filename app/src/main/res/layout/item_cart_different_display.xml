<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dp10">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_weight="3"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="" />

        <TextView
            android:id="@+id/tvItemPrice"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="0.00" />

        <TextView
            android:id="@+id/tvItemCount"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="x0" />

        <TextView
            android:id="@+id/tvItemTotal"
            style="@style/text_12_red"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="0.00" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="@dimen/dp10"
        android:background="@color/color_line" />

</LinearLayout>