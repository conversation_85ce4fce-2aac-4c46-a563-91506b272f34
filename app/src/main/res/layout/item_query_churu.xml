<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linItem"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_f5_top_5"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/dp10"
    android:paddingVertical="@dimen/dp5">

    <TextView
        android:id="@+id/tvItemName"
        style="@style/text_12_black"
        android:layout_width="0dp"
        android:layout_weight="2"
        android:gravity="center" />

    <TextView
        android:id="@+id/tvItemBarcode"
        style="@style/text_12_black"
        android:layout_width="0dp"
        android:layout_weight="2"
        android:gravity="center" />

    <TextView
        android:id="@+id/tvItemType"
        style="@style/text_12_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center" />

    <TextView
        android:id="@+id/tvItemTime"
        style="@style/text_12_black"
        android:layout_width="0dp"
        android:layout_weight="2"
        android:gravity="center" />

    <TextView
        android:id="@+id/tvItemCountBefore"
        style="@style/text_12_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="0" />

    <TextView
        android:id="@+id/tvItemCount"
        style="@style/text_12_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="0" />

    <TextView
        android:id="@+id/tvItemCountAfter"
        style="@style/text_12_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="0" />

    <TextView
        android:id="@+id/tvItemSource"
        style="@style/text_12_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center" />

    <TextView
        android:id="@+id/tvItemWay"
        style="@style/text_12_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center" />

</LinearLayout>