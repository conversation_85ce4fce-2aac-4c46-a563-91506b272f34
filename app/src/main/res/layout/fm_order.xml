<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/dp5"
        android:layout_weight="2"
        android:background="@drawable/shape_white_5"
        android:orientation="vertical"
        android:padding="@dimen/dp10"
        android:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/linType"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvType"
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/order_type"
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:id="@+id/ivType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linStartDate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvStartDate"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:hint="@string/date_start"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp5"
                    android:text=""
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:id="@+id/ivStartDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linEndDate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvEndDate"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:hint="@string/date_end"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp5"
                    android:text=""
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:id="@+id/ivEndDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

        </LinearLayout>

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tabGravity="fill"
            app:tabIndicatorColor="@color/transparent"
            app:tabIndicatorFullWidth="false"
            app:tabMaxWidth="@dimen/dp80"
            app:tabMode="scrollable"
            app:tabSelectedTextColor="@color/green"
            app:tabTextAppearance="@style/text_14_black" />

        <com.yxl.cashier_retail.view.NoScrollViewPager
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/linInfo"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:visibility="gone">

        <!--收件人信息-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_white_5"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvDeliveryType"
                style="@style/text_12_white"
                android:background="@drawable/shape_red_topleft_bottomright5"
                android:paddingHorizontal="@dimen/dp6"
                android:paddingVertical="@dimen/dp2"
                android:text="" />

            <TextView
                android:id="@+id/tvName"
                style="@style/text_14_black"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginTop="@dimen/dp5"
                android:text=""
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvAds"
                style="@style/text_12_black"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginBottom="@dimen/dp10"
                android:text="" />

        </LinearLayout>

        <!--订单信息-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:background="@drawable/shape_white_5"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    style="@style/text_12_666"
                    android:layout_centerVertical="true"
                    android:text="@string/receivable_money_colon" />

                <TextView
                    android:id="@+id/tvOrderTotal"
                    style="@style/text_12_black"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:text="0.00" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5">

                <TextView
                    style="@style/text_12_666"
                    android:layout_centerVertical="true"
                    android:text="@string/discount_money_colon" />

                <TextView
                    android:id="@+id/tvOrderDiscount"
                    style="@style/text_12_red"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:text="-0.00" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5">

                <TextView
                    style="@style/text_12_666"
                    android:layout_centerVertical="true"
                    android:text="@string/beans_deduction_colon" />

                <TextView
                    android:id="@+id/tvOrderBeans"
                    style="@style/text_12_red"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:text="-0.00" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5">

                <TextView
                    style="@style/text_12_666"
                    android:layout_centerVertical="true"
                    android:text="@string/paid_in_money_colon" />

                <TextView
                    android:id="@+id/tvOrderReceive"
                    style="@style/text_12_orange"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:text="0.00" />

            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="@dimen/dp5"
                android:background="@color/color_line" />

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5">

                <TextView
                    style="@style/text_12_666"
                    android:layout_centerVertical="true"
                    android:text="@string/order_no_colon" />

                <TextView
                    android:id="@+id/tvOrderNo"
                    style="@style/text_12_black"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:text="" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5">

                <TextView
                    style="@style/text_12_666"
                    android:layout_centerVertical="true"
                    android:text="@string/pay_time_colon" />

                <TextView
                    android:id="@+id/tvOrderPayTime"
                    style="@style/text_12_black"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:text="" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5">

                <TextView
                    style="@style/text_12_666"
                    android:layout_centerVertical="true"
                    android:text="@string/pay_type_colon" />

                <TextView
                    android:id="@+id/tvOrderPayType"
                    style="@style/text_12_black"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:text="" />

            </RelativeLayout>

        </LinearLayout>

        <!--商品信息-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp5"
            android:layout_weight="1"
            android:background="@drawable/shape_white_5"
            android:orientation="vertical"
            android:padding="@dimen/dp5"
            android:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_f5_top_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <TextView
                    style="@style/text_10_666"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:text="@string/goods_name" />

                <TextView
                    style="@style/text_10_666"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/price" />

                <TextView
                    style="@style/text_10_666"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/count_weight" />

                <TextView
                    style="@style/text_10_666"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/subtotal" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvGoods"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:background="@drawable/shape_white_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp5">

            <TextView
                android:id="@+id/tvOrderConfirm"
                style="@style/text_14_white"
                android:layout_marginHorizontal="@dimen/dp5"
                android:layout_marginVertical="@dimen/dp10"
                android:background="@drawable/shape_green_5"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/order_receive"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvOrderGet"
                style="@style/text_14_green"
                android:layout_marginHorizontal="@dimen/dp5"
                android:layout_marginVertical="@dimen/dp10"
                android:background="@drawable/shape_green_kuang_5"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/confirm_get"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvOrderDeliveryCancel"
                style="@style/text_14_red"
                android:layout_marginHorizontal="@dimen/dp5"
                android:layout_marginVertical="@dimen/dp10"
                android:background="@drawable/shape_red_kuang_5"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/delivery_cancel"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvOrderCancel"
                style="@style/text_14_red"
                android:layout_marginHorizontal="@dimen/dp5"
                android:layout_marginVertical="@dimen/dp10"
                android:background="@drawable/shape_red_kuang_5"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/order_cancel"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvOrderPick"
                style="@style/text_14_green"
                android:layout_marginHorizontal="@dimen/dp5"
                android:layout_marginVertical="@dimen/dp10"
                android:background="@drawable/shape_green_kuang_5"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/confirm_pick"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvOrderAgain"
                style="@style/text_14_white"
                android:layout_marginHorizontal="@dimen/dp5"
                android:layout_marginVertical="@dimen/dp10"
                android:background="@drawable/shape_green_5"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/order_receive_again"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/tvNothing"
        style="@style/text_14_black"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@drawable/shape_white_5"
        android:gravity="center"
        android:text="@string/no_order_information"
        android:visibility="visible" />

</LinearLayout>