<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingVertical="@dimen/dp5">

    <TextView
        android:id="@+id/tvItemName"
        style="@style/text_12_black"
        android:text="" />

    <LinearLayout
        android:id="@+id/linItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp5"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/shape_d8_kuang_5"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingVertical="@dimen/dp8">

        <TextView
            android:id="@+id/tvItemPoints"
            style="@style/text_12_black"
            android:text="" />

        <ImageView
            android:id="@+id/ivItemCursor"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp15"
            android:layout_gravity="center_vertical"
            android:src="@drawable/anim_cursor"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvItemHint"
            style="@style/text_12_black"
            android:hint="@string/input_points"
            android:visibility="gone" />

    </LinearLayout>

</LinearLayout>