<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linItem"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginEnd="@dimen/dp5"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingVertical="@dimen/dp6">

    <TextView
        android:id="@+id/tvItemName"
        style="@style/text_14_red"
        android:text=""
        android:visibility="gone" />

    <TextView
        android:id="@+id/tvItemHint"
        style="@style/text_14_red"
        android:hint="@string/input" />

    <ImageView
        android:id="@+id/ivItemCursor"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp18"
        android:layout_gravity="center_vertical"
        android:src="@drawable/anim_cursor"
        android:visibility="gone" />

</LinearLayout>