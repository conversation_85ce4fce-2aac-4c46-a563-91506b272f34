<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/white">

        <ImageView
            android:id="@+id/ivBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_back002" />

        <TextView
            style="@style/text_16_black"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/ivBack"
            android:text="@string/restock_preview"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvTitle"
            style="@style/text_18_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text=""
            android:textStyle="bold" />

        <TextClock
            style="@style/text_12_black"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_toStartOf="@+id/tvCashier"
            android:format24Hour="MM-dd HH:mm:ss"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvCashier"
            style="@style/text_14_white"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/green"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp20"
            android:text="@string/cashier_table"
            android:textStyle="bold"
            app:drawableLeftCompat="@mipmap/ic_cashier001" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="@dimen/dp5"
        android:layout_marginTop="@dimen/dp46"
        android:layout_marginBottom="@dimen/dp5"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="2"
            android:orientation="vertical">

            <com.scwang.smart.refresh.layout.SmartRefreshLayout
                android:id="@+id/srlRestock"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@drawable/shape_f2_5">

                <com.scwang.smart.refresh.header.ClassicsHeader
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvRestock"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="@dimen/dp5"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                    <LinearLayout
                        android:id="@+id/linEmptyRestock"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingTop="@dimen/dp40"
                        android:visibility="gone">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/ic_empty" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp26"
                            android:textColor="@color/color_666"
                            android:textSize="@dimen/f14"
                            android:textStyle="bold" />

                    </LinearLayout>

                </RelativeLayout>

                <com.scwang.smart.refresh.footer.ClassicsFooter
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </com.scwang.smart.refresh.layout.SmartRefreshLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                android:background="@drawable/shape_white_5"
                android:orientation="horizontal"
                android:padding="@dimen/dp10">

                <TextView
                    android:id="@+id/tvConfirm"
                    style="@style/text_14_white"
                    android:layout_width="match_parent"
                    android:background="@drawable/shape_green_5"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp8"
                    android:text="@string/restock_submit"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:background="@drawable/shape_f2_5">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_f2_5"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_white_top_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="@dimen/dp10">

                    <TextView
                        android:id="@+id/tvName"
                        style="@style/text_16_black"
                        android:layout_marginEnd="@dimen/dp5"
                        android:text=""
                        android:textStyle="bold" />

                    <ImageView
                        android:id="@+id/ivImg"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_supplier_img001"
                        android:visibility="gone" />

                </LinearLayout>

                <TextView
                    style="@style/text_12_999"
                    android:layout_marginHorizontal="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp10"
                    android:text="@string/supplier_info" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp10"
                    android:background="@drawable/shape_white_5"
                    android:orientation="vertical"
                    android:padding="@dimen/dp10">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_666"
                            android:text="@string/supplier_name_colon" />

                        <TextView
                            android:id="@+id/tvSupplier"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:text="" />

                        <TextView
                            style="@style/text_12_666"
                            android:text="@string/contacts_colon" />

                        <TextView
                            android:id="@+id/tvContacts"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:text="" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_666"
                            android:text="@string/contact_mobile_colon" />

                        <TextView
                            android:id="@+id/tvMobile"
                            style="@style/text_12_blue"
                            android:text="" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_666"
                            android:text="@string/address_where_colon" />

                        <TextView
                            android:id="@+id/tvAds"
                            style="@style/text_12_black"
                            android:text="" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp10"
                    android:background="@drawable/shape_white_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="@dimen/dp10">

                    <TextView
                        style="@style/text_12_black"
                        android:text="@string/remarks_colon" />

                    <TextView
                        android:id="@+id/tvRemarks"
                        style="@style/text_12_black"
                        android:hint="@string/input_remarks_info" />

                </LinearLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp10">

                    <TextView
                        style="@style/text_12_999"
                        android:layout_centerVertical="true"
                        android:text="@string/product_info" />

                    <TextView
                        android:id="@+id/tvCount"
                        style="@style/text_12_black"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="@dimen/dp48"
                        android:layout_toStartOf="@+id/tvTotal"
                        android:text="@string/goods_type_colon" />

                    <TextView
                        android:id="@+id/tvTotal"
                        style="@style/text_12_black"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:text="@string/price_estimate" />

                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp5"
                    android:background="@drawable/shape_white_5"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5">

                        <TextView
                            style="@style/text_12_999"
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:text="@string/goods_name" />

                        <TextView
                            style="@style/text_12_999"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="@string/buying_price" />

                        <TextView
                            style="@style/text_12_999"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="@string/ordered_count" />

                        <TextView
                            style="@style/text_12_999"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:text="@string/total_price" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/color_line" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvGoods"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                </LinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </LinearLayout>

</RelativeLayout>