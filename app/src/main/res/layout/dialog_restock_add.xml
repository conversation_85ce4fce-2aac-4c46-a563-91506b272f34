<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_10"
    android:gravity="center"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/restock_create"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp20"
        android:background="@drawable/shape_d8_kuang_5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/etDialogName"
            style="@style/text_14_black"
            android:layout_width="match_parent"
            android:background="@null"
            android:gravity="center"
            android:hint="@string/input_restock_name"
            android:inputType="text"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/dp20"
            android:paddingVertical="@dimen/dp8" />

        <ImageView
            android:id="@+id/ivDialogClear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp6"
            android:src="@mipmap/ic_close001"
            android:visibility="gone" />

    </RelativeLayout>

    <TextView
        android:id="@+id/tvDialogConfirm"
        style="@style/text_14_white"
        android:layout_width="@dimen/dp120"
        android:layout_marginVertical="@dimen/dp16"
        android:background="@drawable/shape_green_5"
        android:gravity="center"
        android:paddingVertical="@dimen/dp8"
        android:text="@string/confirm" />

</LinearLayout>