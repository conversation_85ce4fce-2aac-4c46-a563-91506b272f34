<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/dp10">

    <ImageView
        android:id="@+id/ivItemImg"
        android:layout_width="@dimen/dp72"
        android:layout_height="@dimen/dp72"
        android:layout_marginEnd="@dimen/dp10"
        android:src="@mipmap/ic_default_img" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp72"
        android:layout_marginEnd="@dimen/dp180"
        android:layout_toEndOf="@+id/ivItemImg">

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_12_black"
            android:text="" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvItemPrice"
                style="@style/text_12_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="￥0.00" />

            <TextView
                android:id="@+id/tvItemCount"
                style="@style/text_12_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="x0" />

            <TextView
                android:id="@+id/tvItemTotal"
                style="@style/text_14_green"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="end"
                android:text="￥0.00"
                android:textStyle="bold" />

        </LinearLayout>

    </RelativeLayout>

</RelativeLayout>