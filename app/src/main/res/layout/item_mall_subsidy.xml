<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp90"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/dp5"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/ivItemImg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp90" />

    <TextView
        android:id="@+id/tvItemName"
        style="@style/text_12_black"
        android:layout_marginTop="@dimen/dp8"
        android:ellipsize="end"
        android:maxLines="1"
        android:text=""
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp6"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvItemPrice"
            style="@style/text_12_green"
            android:text="￥0.00"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>