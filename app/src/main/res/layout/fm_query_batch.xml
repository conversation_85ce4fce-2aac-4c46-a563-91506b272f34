<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_5"
    android:orientation="vertical"
    android:padding="@dimen/dp10">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/linType"
            android:layout_width="@dimen/dp125"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp10"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvType"
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="1"
                android:paddingVertical="@dimen/dp6"
                android:text="全部来源"
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivType"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_arrow002" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linStartDate"
            android:layout_width="@dimen/dp125"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvStartDate"
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:ellipsize="end"
                android:hint="@string/date_start"
                android:maxLines="1"
                android:paddingVertical="@dimen/dp6"
                android:text=""
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivStartDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_arrow002" />

        </LinearLayout>

        <TextView
            style="@style/text_16_999"
            android:layout_marginHorizontal="@dimen/dp3"
            android:text="~" />

        <LinearLayout
            android:id="@+id/linEndDate"
            android:layout_width="@dimen/dp125"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvEndDate"
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:ellipsize="end"
                android:hint="@string/date_end"
                android:maxLines="1"
                android:paddingVertical="@dimen/dp6"
                android:text=""
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivEndDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_arrow002" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/shape_f5_top_5"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp5">

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:gravity="center"
            android:text="入库单号" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:gravity="center"
            android:text="时间" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="出入库类型" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="审核状态" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="提交人" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="操作类型" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="总数量" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/total_amount" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="操作平台" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:gravity="center"
            android:text="@string/remarks" />

    </LinearLayout>

    <include
        android:id="@+id/vSmartrefreshlayout"
        layout="@layout/layout_smartrefreshlayout" />

</LinearLayout>