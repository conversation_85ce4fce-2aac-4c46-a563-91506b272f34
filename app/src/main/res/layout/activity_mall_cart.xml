<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/white">

        <ImageView
            android:id="@+id/ivBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_back002" />

        <TextView
            style="@style/text_16_black"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/ivBack"
            android:text="购物车"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/linDate"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toStartOf="@+id/tvCashier"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10">

            <TextClock
                style="@style/text_12_black"
                android:format24Hour="MM月dd日 HH:mm"
                android:textStyle="bold" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvCashier"
            style="@style/text_14_white"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/green"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp20"
            android:text="@string/cashier_table"
            android:textStyle="bold"
            app:drawableLeftCompat="@mipmap/ic_cashier001" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/relAds"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp51"
        android:background="@drawable/shape_white_5"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp8">

        <ImageView
            android:id="@+id/ivAdsImg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/dp6"
            android:src="@mipmap/ic_address001" />

        <TextView
            android:id="@+id/tvAdsName"
            style="@style/text_14_black"
            android:layout_toEndOf="@+id/ivAdsImg"
            android:text="益农社服务员 13484879586"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvAds"
            style="@style/text_12_999"
            android:layout_below="@+id/tvAdsName"
            android:layout_marginTop="@dimen/dp10"
            android:layout_toEndOf="@+id/ivAdsImg"
            android:text="山东省临沂市兰山区南京路与我卧虎路交汇处临沂应用科学城" />

    </RelativeLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/smartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/linBottom"
        android:layout_below="@+id/relAds"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginVertical="@dimen/dp10"
        android:background="@drawable/shape_white_5">

        <com.scwang.smart.refresh.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            <LinearLayout
                android:id="@+id/linEmpty"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="@dimen/dp40"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/ivEmpty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_empty" />

                <TextView
                    android:id="@+id/tvEmpty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp26"
                    android:textColor="@color/color_666"
                    android:textSize="@dimen/f14"
                    android:textStyle="bold" />

            </LinearLayout>

        </RelativeLayout>

        <com.scwang.smart.refresh.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <LinearLayout
        android:id="@+id/linBottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvTips"
            style="@style/text_12_red"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp56"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="2"
            android:text="【下单赠送】金圈平台赠满2000减20优惠券" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvCount"
                style="@style/text_10_black"
                android:text="货品共：0类，0件" />

            <TextView
                android:id="@+id/tvTotal"
                style="@style/text_10_black"
                android:layout_marginTop="@dimen/dp2"
                android:text="订单总金额：0.00" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_10_black"
                    android:text="跨店满减：" />

                <TextView
                    android:id="@+id/tvDiscount"
                    style="@style/text_10_red"
                    android:text="-0.00" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp2"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_10_black"
                    android:text="金圈币抵扣：" />

                <TextView
                    android:id="@+id/tvDeduct"
                    style="@style/text_10_red"
                    android:text="-0.00" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            style="@style/text_12_black"
            android:text="实付金额：" />

        <TextView
            android:id="@+id/tvTotalReal"
            style="@style/text_12_green"
            android:layout_marginEnd="@dimen/dp10"
            android:text="0.00"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvBuy"
            style="@style/text_16_white"
            android:background="@color/green"
            android:padding="@dimen/dp20"
            android:text="立即购买" />

    </LinearLayout>

</RelativeLayout>