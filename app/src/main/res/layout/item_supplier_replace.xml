<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/dp5"
    android:background="@drawable/shape_white_5"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="@dimen/dp10">

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="2">

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_14_black"
            android:layout_marginEnd="@dimen/dp5"
            android:ellipsize="end"
            android:maxLines="1"
            android:text=""
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivItemImg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp2"
            android:layout_toEndOf="@+id/tvItemName"
            android:src="@mipmap/ic_supplier_img001"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvItemMobile"
            style="@style/text_12_666"
            android:layout_below="@+id/tvItemName"
            android:layout_marginTop="@dimen/dp5"
            android:text="" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:id="@+id/tvItemTotal"
            style="@style/text_14_green"
            android:text="0.00"
            android:textStyle="bold" />

        <TextView
            style="@style/text_12_666"
            android:layout_marginTop="@dimen/dp5"
            android:text="@string/purchase_total" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvItemDebt"
            style="@style/text_14_orange"
            android:text="0.00"
            android:textStyle="bold" />

        <TextView
            style="@style/text_12_666"
            android:layout_marginTop="@dimen/dp5"
            android:text="@string/debt_total" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvItemCount"
            style="@style/text_14_black"
            android:text="0"
            android:textStyle="bold" />

        <TextView
            style="@style/text_12_666"
            android:layout_marginTop="@dimen/dp5"
            android:text="@string/order_count" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="3"
        android:gravity="end"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvItemConfirm"
            style="@style/text_14_green"
            android:layout_marginStart="@dimen/dp10"
            android:background="@drawable/shape_green_kuang_5"
            android:paddingHorizontal="@dimen/dp20"
            android:paddingVertical="@dimen/dp8"
            android:text="@string/confirm_replace" />

    </LinearLayout>

</LinearLayout>