<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dp12">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp12"
        android:gravity="center"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_14_333"
            android:layout_width="0dp"
            android:layout_marginRight="@dimen/dp12"
            android:layout_weight="1"
            android:text=""
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvItemStatus"
            style="@style/text_14_blue"
            android:text=""
            android:textStyle="bold" />

    </LinearLayout>

    <TextView
        android:id="@+id/tvItemAddress"
        style="@style/text_12_333"
        android:layout_marginTop="@dimen/dp8"
        android:text="" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp1"
        android:layout_marginTop="@dimen/dp12"
        android:background="@color/color_f2" />

</LinearLayout>