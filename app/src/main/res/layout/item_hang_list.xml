<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp5"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <View
            android:layout_width="@dimen/dp2"
            android:layout_height="@dimen/dp10"
            android:layout_marginEnd="@dimen/dp5"
            android:background="@color/white" />

        <TextView
            android:id="@+id/tvItemMonth"
            style="@style/text_12_white"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="" />

        <TextView
            android:id="@+id/tvItemCount"
            style="@style/text_12_white"
            android:layout_marginEnd="@dimen/dp20"
            android:text="挂单数量：0" />

        <TextView
            android:id="@+id/tvItemTotal"
            style="@style/text_12_white"
            android:layout_marginEnd="@dimen/dp20"
            android:text="挂单金额：0" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="2.5dp"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:spanCount="4" />

</LinearLayout>