<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linItem"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tvItemName"
        style="@style/text_10_black"
        android:layout_width="0dp"
        android:layout_marginStart="@dimen/dp10"
        android:layout_weight="1.5"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="" />

    <TextView
        android:id="@+id/tvItemBarcode"
        style="@style/text_10_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="" />

    <TextView
        android:id="@+id/tvItemPrice"
        style="@style/text_10_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="0.00" />

    <TextView
        android:id="@+id/tvItemCount"
        style="@style/text_10_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="0" />

    <TextView
        android:id="@+id/tvItemTotal"
        style="@style/text_10_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="0.00" />

    <TextView
        android:id="@+id/tvItemRefunded"
        style="@style/text_10_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="0" />

    <RelativeLayout
        android:id="@+id/relItem"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/dp3"
        android:layout_weight="1"
        android:background="@drawable/shape_d8_kuang_5"
        android:padding="@dimen/dp5">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvItemRefund"
                style="@style/text_10_black"
                android:text="" />

            <ImageView
                android:id="@+id/ivItemRefund"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp12"
                android:src="@drawable/anim_cursor"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvItemRefundHint"
                style="@style/text_10_black"
                android:hint="@string/input"
                android:text="" />

        </LinearLayout>

    </RelativeLayout>

    <TextView
        android:id="@+id/tvItemRefundTotal"
        style="@style/text_10_black"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:text="0.00" />

</LinearLayout>