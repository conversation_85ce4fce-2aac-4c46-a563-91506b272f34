<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/green">

        <ImageView
            android:id="@+id/ivLogo"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingHorizontal="@dimen/dp10"
            android:src="@mipmap/ic_logo004" />

        <TextView
            android:id="@+id/tvTitle"
            style="@style/text_18_white"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text="@string/restock_self"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/linDate"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toStartOf="@+id/tvCashier"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10">

            <TextClock
                style="@style/text_12_white"
                android:format24Hour="MM-dd HH:mm"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp2"
                android:background="@drawable/shape_black_tm_5"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp1">

                <ImageView
                    android:id="@+id/ivNetwork"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5" />

                <TextView
                    android:id="@+id/tvNetwork"
                    style="@style/text_10_white"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tvCashier"
            style="@style/text_14_green"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/white"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp20"
            android:text="@string/cashier_table"
            android:textStyle="bold"
            app:drawableLeftCompat="@mipmap/ic_cashier001" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="@dimen/dp64"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dp46"
        android:layout_marginBottom="@dimen/dp5"
        android:background="@drawable/shape_white_5"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/linMall"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_green_5"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingVertical="@dimen/dp10"
            android:visibility="gone">

            <ImageView
                android:id="@+id/ivMall"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/ic_mall_tab001" />

            <TextView
                android:id="@+id/tvMall"
                style="@style/text_12_white"
                android:layout_marginTop="@dimen/dp3"
                android:gravity="center"
                android:text="@string/mall_jinquan" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linReplenishment"
            android:layout_width="@dimen/dp64"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_green_5"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingVertical="@dimen/dp10">

            <ImageView
                android:id="@+id/ivReplenishment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/ic_mall_replenishment_tab001" />

            <TextView
                android:id="@+id/tvReplenishment"
                style="@style/text_12_white"
                android:layout_marginTop="@dimen/dp3"
                android:gravity="center"
                android:text="@string/restock_self" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linSupplier"
            android:layout_width="@dimen/dp64"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingVertical="@dimen/dp10">

            <ImageView
                android:id="@+id/ivSupplier"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/ic_mall_supplier_tab002" />

            <TextView
                android:id="@+id/tvSupplier"
                style="@style/text_12_666"
                android:layout_marginTop="@dimen/dp3"
                android:gravity="center"
                android:text="@string/supplier_manage" />

        </LinearLayout>

    </LinearLayout>

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp69"
        android:layout_marginTop="@dimen/dp46"
        android:layout_marginEnd="@dimen/dp5"
        android:layout_marginBottom="@dimen/dp5" />

</RelativeLayout>