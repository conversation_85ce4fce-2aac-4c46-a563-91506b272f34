<!--
  ~ Copyright (c) 2016-present 贵州纳雍穿青人李裕江<<EMAIL>>
  ~
  ~ The software is licensed under the Mulan PSL v2.
  ~ You can use this software according to the terms and conditions of the Mulan PSL v2.
  ~ You may obtain a copy of Mulan PSL v2 at:
  ~     http://license.coscl.org.cn/MulanPSL2
  ~ THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR
  ~ IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR
  ~ PURPOSE.
  ~ See the Mulan PSL v2 for more details.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <com.github.gzuliyujiang.wheelview.widget.NumberWheelView
        android:id="@+id/wheel_picker_date_year_wheel"
        style="@style/WheelDefault"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.2"
        android:visibility="visible" />

    <TextView
        android:id="@+id/wheel_picker_date_year_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:text=""
        android:visibility="visible" />

    <com.github.gzuliyujiang.wheelview.widget.NumberWheelView
        android:id="@+id/wheel_picker_date_month_wheel"
        style="@style/WheelDefault"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.0"
        android:visibility="visible" />

    <TextView
        android:id="@+id/wheel_picker_date_month_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:text=""
        android:visibility="visible" />

    <com.github.gzuliyujiang.wheelview.widget.NumberWheelView
        android:id="@+id/wheel_picker_date_day_wheel"
        style="@style/WheelDefault"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.0"
        android:visibility="visible" />

    <TextView
        android:id="@+id/wheel_picker_date_day_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:text=""
        android:visibility="visible" />

</LinearLayout>
