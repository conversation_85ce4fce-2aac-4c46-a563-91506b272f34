<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linItem"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="2.5dp"
    android:background="@drawable/shape_white_5"
    android:gravity="center"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_12_999"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp5"
            android:text="挂单001" />

        <ImageView
            android:id="@+id/ivItemDel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close003" />

    </RelativeLayout>

    <TextView
        android:id="@+id/tvItemMobile"
        style="@style/text_14_black"
        android:text="散客"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tvItemTime"
        style="@style/text_12_999"
        android:layout_marginTop="@dimen/dp5"
        android:text="2023-02-03 15:05" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp5">

        <ImageView
            android:id="@+id/ivItemTotal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/dp5"
            android:src="@mipmap/ic_balance001" />

        <TextView
            android:id="@+id/tvItemTotal"
            style="@style/text_14_orange"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/ivItemTotal"
            android:text="0.00"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivItemCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_toStartOf="@+id/tvItemCount"
            android:src="@mipmap/ic_points001" />

        <TextView
            android:id="@+id/tvItemCount"
            style="@style/text_12_black"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="共0种" />

    </RelativeLayout>

</LinearLayout>