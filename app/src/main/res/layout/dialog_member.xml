<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/member_search"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:background="@drawable/shape_green_kuang_5">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp5"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp5"
                android:src="@mipmap/ic_search002" />

            <TextView
                android:id="@+id/tvDialogSearch"
                style="@style/text_14_black"
                android:maxLength="11"
                android:text="" />

            <ImageView
                android:id="@+id/ivDialogCursor"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp18"
                android:layout_gravity="center_vertical"
                android:src="@drawable/anim_cursor" />

            <TextView
                android:id="@+id/tvDialogHint"
                style="@style/text_14_black"
                android:hint="@string/member_search_phone"
                android:text="" />

        </LinearLayout>

        <ImageView
            android:id="@+id/ivDialogSearchClear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp5"
            android:src="@mipmap/ic_close001"
            android:visibility="gone" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_weight="1"
        android:orientation="vertical">

        <!--新增会员、会员管理-->
        <LinearLayout
            android:id="@+id/linDialogAdd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvDialogAdd"
                style="@style/text_12_333"
                android:layout_marginEnd="@dimen/dp32"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center"
                android:padding="@dimen/dp16"
                android:text="@string/member_add"
                app:drawableTopCompat="@mipmap/ic_member_add" />

            <TextView
                android:id="@+id/tvDialogManage"
                style="@style/text_12_333"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center"
                android:padding="@dimen/dp16"
                android:text="@string/member_manage"
                app:drawableTopCompat="@mipmap/ic_member_manage" />

        </LinearLayout>

        <!--未搜索到会员信息-->
        <LinearLayout
            android:id="@+id/linDialogEmpty"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp20"
            android:visibility="gone">

            <ImageView
                android:layout_width="@dimen/dp74"
                android:layout_height="@dimen/dp51"
                android:layout_marginEnd="@dimen/dp10"
                android:src="@mipmap/ic_empty" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    style="@style/text_14_black"
                    android:text="@string/member_search_notfind" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_12_999"
                        android:text="@string/member_tips" />

                    <TextView
                        android:id="@+id/tvDialogAdd1"
                        style="@style/text_12_green"
                        android:paddingVertical="@dimen/dp3"
                        android:paddingEnd="@dimen/dp10"
                        android:text="@string/member_add"
                        tools:ignore="RtlSymmetry" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!--会员列表-->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvDialog"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginVertical="@dimen/dp5"
            android:background="@drawable/shape_d8_kuang_5"
            android:visibility="gone"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        <!--会员详情-->
        <LinearLayout
            android:id="@+id/linDialogMember"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:background="@drawable/shape_f2_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp5"
            android:visibility="gone">

            <ImageView
                android:id="@+id/ivDialogMemberHead"
                android:layout_width="@dimen/dp45"
                android:layout_height="@dimen/dp45"
                android:layout_marginEnd="@dimen/dp5"
                android:src="@mipmap/ic_head003" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvDialogMemberMobile"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="3"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text=""
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvDialogMemberBalance"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="2"
                        android:text=""
                        android:textColor="@color/orange"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvDialogMemberPoints"
                        style="@style/text_12_green"
                        android:layout_width="0dp"
                        android:layout_weight="2"
                        android:text=""
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="3"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvDialogMemberName"
                            style="@style/text_12_999"
                            android:layout_marginEnd="@dimen/dp6"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="" />

                        <ImageView
                            android:id="@+id/ivDialogMemberLevel"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />

                    </LinearLayout>

                    <TextView
                        style="@style/text_12_666"
                        android:layout_width="0dp"
                        android:layout_weight="2"
                        android:drawablePadding="@dimen/dp2"
                        android:gravity="center_vertical"
                        android:text="@string/balance"
                        android:textColor="@color/orange"
                        android:textStyle="bold"
                        app:drawableLeftCompat="@mipmap/ic_balance001" />

                    <TextView
                        style="@style/text_12_666"
                        android:layout_width="0dp"
                        android:layout_weight="2"
                        android:drawablePadding="@dimen/dp2"
                        android:gravity="center_vertical"
                        android:text="@string/points"
                        android:textStyle="bold"
                        app:drawableLeftCompat="@mipmap/ic_points001" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <com.yxl.cashier_retail.view.NumberKeyBoardView
        android:id="@+id/numberKeyBoardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp51"
        android:layout_marginBottom="@dimen/dp5" />

</LinearLayout>