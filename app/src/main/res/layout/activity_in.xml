<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawerLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp41"
            android:background="@color/green">

            <ImageView
                android:id="@+id/ivLogo"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:paddingHorizontal="@dimen/dp10"
                android:src="@mipmap/ic_logo004" />

            <LinearLayout
                android:id="@+id/linSearch"
                android:layout_width="@dimen/dp200"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toEndOf="@+id/ivLogo"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/etSearch"
                    style="@style/text_12_333"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:background="@null"
                    android:drawableStart="@mipmap/ic_search002"
                    android:drawablePadding="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:hint="@string/search_tips_default"
                    android:imeOptions="actionSearch"
                    android:inputType="text"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5" />

                <ImageView
                    android:id="@+id/ivSearchClear"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_close001"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvTitle"
                style="@style/text_16_white"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:text="@string/goods_manage"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/linDate"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_toStartOf="@+id/tvCashier"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingHorizontal="@dimen/dp10">

                <TextClock
                    style="@style/text_12_white"
                    android:format24Hour="MM-dd HH:mm"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp2"
                    android:background="@drawable/shape_black_tm_5"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp1">

                    <ImageView
                        android:id="@+id/ivNetwork"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:src="@mipmap/ic_network001" />

                    <TextView
                        android:id="@+id/tvNetwork"
                        style="@style/text_10_white"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="网络正常" />

                </LinearLayout>

            </LinearLayout>

            <TextView
                android:id="@+id/tvCashier"
                style="@style/text_14_green"
                android:layout_height="match_parent"
                android:layout_alignParentEnd="true"
                android:background="@color/white"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/dp20"
                android:text="@string/cashier_table"
                android:textStyle="bold"
                app:drawableLeftCompat="@mipmap/ic_cashier001" />

        </RelativeLayout>

        <LinearLayout
            android:layout_width="@dimen/dp64"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dp46"
            android:layout_marginBottom="@dimen/dp5"
            android:background="@drawable/shape_white_5"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/linGoodsManage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_green_5"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingVertical="@dimen/dp10">

                <ImageView
                    android:id="@+id/ivGoodsManage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_in_tab001" />

                <TextView
                    android:id="@+id/tvGoodsManage"
                    style="@style/text_12_white"
                    android:layout_marginTop="@dimen/dp3"
                    android:gravity="center"
                    android:text="@string/goods_manage" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linOut"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingVertical="@dimen/dp10">

                <ImageView
                    android:id="@+id/ivOut"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_out_tab002" />

                <TextView
                    android:id="@+id/tvOut"
                    style="@style/text_12_666"
                    android:layout_marginTop="@dimen/dp3"
                    android:gravity="center"
                    android:text="@string/batch_out" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linIn"
                android:layout_width="@dimen/dp64"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingVertical="@dimen/dp10">

                <ImageView
                    android:id="@+id/ivIn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_in_tab022" />

                <TextView
                    android:id="@+id/tvIn"
                    style="@style/text_12_666"
                    android:layout_marginTop="@dimen/dp3"
                    android:gravity="center"
                    android:text="@string/in_batch" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linReplenishment"
                android:layout_width="@dimen/dp64"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingVertical="@dimen/dp10"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/ivReplenishment"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_mall_replenishment_tab002" />

                <TextView
                    android:id="@+id/tvReplenishment"
                    style="@style/text_12_666"
                    android:layout_marginTop="@dimen/dp3"
                    android:gravity="center"
                    android:text="@string/restock_self" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linGouX"
                android:layout_width="@dimen/dp64"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingVertical="@dimen/dp10"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/ivGouX"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_in_tab032" />

                <TextView
                    android:id="@+id/tvGouX"
                    style="@style/text_12_666"
                    android:layout_marginTop="@dimen/dp3"
                    android:gravity="center"
                    android:text="@string/purchase" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linSupplier"
                android:layout_width="@dimen/dp64"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingVertical="@dimen/dp10"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/ivSupplier"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_mall_supplier_tab002" />

                <TextView
                    android:id="@+id/tvSupplier"
                    style="@style/text_12_666"
                    android:layout_marginTop="@dimen/dp3"
                    android:gravity="center"
                    android:text="@string/supplier_manage" />

            </LinearLayout>

        </LinearLayout>

        <FrameLayout
            android:id="@+id/fragment_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/dp64"
            android:layout_marginTop="@dimen/dp46" />

    </RelativeLayout>

    <!--右侧边栏-->
    <LinearLayout
        android:id="@+id/linPurchase"
        android:layout_width="600dp"
        android:layout_height="match_parent"
        android:layout_gravity="end"
        android:background="@color/color_f2"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/ivBackPurchase"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp10"
                android:src="@mipmap/ic_back002" />

            <TextView
                style="@style/text_16_black"
                android:text="@string/purchase_info"
                android:textStyle="bold" />

        </LinearLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/dp10">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_white_5"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5">

                    <ImageView
                        android:id="@+id/ivPurchaseStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="@dimen/dp10"
                        android:src="@mipmap/ic_order_status001" />

                    <TextView
                        android:id="@+id/tvPurchaseStatus"
                        style="@style/text_14_black"
                        android:layout_toEndOf="@+id/ivPurchaseStatus"
                        android:text=""
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvPurchaseStatusTips"
                        style="@style/text_12_999"
                        android:layout_below="@+id/tvPurchaseStatus"
                        android:layout_marginTop="@dimen/dp10"
                        android:layout_toEndOf="@+id/ivPurchaseStatus"
                        android:text="" />

                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:background="@drawable/shape_white_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="@dimen/dp10">

                    <TextView
                        style="@style/text_12_black"
                        android:text="@string/remarks_colon" />

                    <TextView
                        android:id="@+id/tvPurchaseRemarks"
                        style="@style/text_12_black"
                        android:text="-" />

                </LinearLayout>

                <!--价格明细-->
                <LinearLayout
                    android:id="@+id/linPurchaseGoods"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:background="@drawable/shape_white_5"
                    android:orientation="vertical">

                    <TextView
                        style="@style/text_12_black"
                        android:padding="@dimen/dp10"
                        android:text="@string/price_info" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/color_line" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5">

                        <TextView
                            style="@style/text_12_999"
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:text="@string/goods_name" />

                        <TextView
                            style="@style/text_12_999"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="@string/ordered_count" />

                        <TextView
                            style="@style/text_12_999"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="@string/delivery_count" />

                        <TextView
                            style="@style/text_12_999"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="@string/purchase_price" />

                        <TextView
                            style="@style/text_12_999"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="@string/goods_subtotal" />

                        <TextView
                            style="@style/text_12_999"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="@string/price_suggest" />

                        <TextView
                            style="@style/text_12_999"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="@string/paid_in_count" />

                        <View
                            android:layout_width="@dimen/dp50"
                            android:layout_height="0dp" />

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvPurchaseGoods"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                </LinearLayout>

                <!--批次信息-->
                <LinearLayout
                    android:id="@+id/linPurchaseBatch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:background="@drawable/shape_white_5"
                    android:orientation="vertical">

                    <TextView
                        style="@style/text_12_black"
                        android:padding="@dimen/dp10"
                        android:text="@string/batch_infos" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/color_line" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/dp10">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_centerVertical="true"
                            android:text="@string/goods_type" />

                        <TextView
                            android:id="@+id/tvPurchaseCount"
                            style="@style/text_12_black"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:text="0"
                            android:textStyle="bold" />

                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp10">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_centerVertical="true"
                            android:text="@string/ordered_total" />

                        <TextView
                            android:id="@+id/tvPurchasePrice"
                            style="@style/text_12_black"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:text="0.00"
                            android:textStyle="bold" />

                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/dp10">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_centerVertical="true"
                            android:text="@string/payable_total" />

                        <TextView
                            android:id="@+id/tvPurchasePayable"
                            style="@style/text_12_black"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:text="0.00"
                            android:textStyle="bold" />

                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp10">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_centerVertical="true"
                            android:text="@string/settlement_discount" />

                        <TextView
                            android:id="@+id/tvPurchaseDiscount"
                            style="@style/text_12_black"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:text="0.00"
                            android:textStyle="bold" />

                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/dp10">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_centerVertical="true"
                            android:text="@string/settled_total" />

                        <TextView
                            android:id="@+id/tvPurchaseSettle"
                            style="@style/text_12_black"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:text="0.00"
                            android:textStyle="bold" />

                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp10"
                        android:layout_marginBottom="@dimen/dp10">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_centerVertical="true"
                            android:text="@string/pending_total" />

                        <TextView
                            android:id="@+id/tvPurchasePending"
                            style="@style/text_12_black"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:text="0.00"
                            android:textStyle="bold" />

                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/dp10"
                        android:visibility="gone">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_centerVertical="true"
                            android:text="@string/batch_profit" />

                        <TextView
                            android:id="@+id/tvPurchaseProfit"
                            style="@style/text_12_black"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:text="0.00"
                            android:textStyle="bold" />

                    </RelativeLayout>
                </LinearLayout>

                <!--订单信息-->
                <LinearLayout
                    android:id="@+id/linPurchaseOrder"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:background="@drawable/shape_white_5"
                    android:orientation="vertical">

                    <TextView
                        style="@style/text_12_black"
                        android:padding="@dimen/dp10"
                        android:text="@string/order_information" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/color_line" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp2"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_width="0dp"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="1"
                            android:text="@string/purchase_no" />

                        <TextView
                            android:id="@+id/tvPurchaseNo"
                            style="@style/text_12_black"
                            android:text="" />

                        <TextView
                            android:id="@+id/tvPurchaseCopy"
                            style="@style/text_12_blue"
                            android:padding="@dimen/dp10"
                            android:text="@string/copy" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp2"
                        android:orientation="horizontal"
                        android:paddingHorizontal="@dimen/dp10">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:text="@string/time_create" />

                        <TextView
                            android:id="@+id/tvPurchaseTime"
                            style="@style/text_12_black"
                            android:text="" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp10"
                        android:layout_marginTop="@dimen/dp10"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:text="@string/supplier_name" />

                        <TextView
                            android:id="@+id/tvPurchaseSupplier"
                            style="@style/text_12_333"
                            android:text="" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp10"
                        android:layout_marginTop="@dimen/dp10"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:text="@string/contacts" />

                        <TextView
                            android:id="@+id/tvPurchaseContact"
                            style="@style/text_12_333"
                            android:text="" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp10"
                        android:layout_marginTop="@dimen/dp10"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:text="@string/contact_mobile" />

                        <TextView
                            android:id="@+id/tvPurchaseMobile"
                            style="@style/text_12_blue"
                            android:text="" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/dp10"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_marginEnd="@dimen/dp10"
                            android:text="@string/address_where" />

                        <TextView
                            android:id="@+id/tvPurchaseAddress"
                            style="@style/text_12_333"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:text="" />

                    </LinearLayout>

                </LinearLayout>

                <!--单据凭证-->
                <LinearLayout
                    android:id="@+id/linPurchaseVoucher"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:background="@drawable/shape_white_5"
                    android:orientation="vertical">

                    <TextView
                        style="@style/text_12_black"
                        android:padding="@dimen/dp10"
                        android:text="@string/purchase_voucher" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/color_line" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvPurchaseVoucher"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dp5"
                        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                        app:spanCount="3" />

                </LinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:background="@color/white"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <TextView
                    android:id="@+id/tvPurchaseConfirm"
                    style="@style/text_12_white"
                    android:layout_margin="@dimen/dp5"
                    android:background="@drawable/shape_green_5"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/confirm_goods_in"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tvPurchasePayment"
                    style="@style/text_12_white"
                    android:layout_margin="@dimen/dp5"
                    android:background="@drawable/shape_green_5"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/repayment"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tvPurchaseVoucher"
                    style="@style/text_12_green"
                    android:layout_margin="@dimen/dp5"
                    android:background="@drawable/shape_green_kuang_5"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/purchase_voucher_look"
                    android:visibility="gone" />

            </LinearLayout>

        </HorizontalScrollView>

    </LinearLayout>

</androidx.drawerlayout.widget.DrawerLayout>