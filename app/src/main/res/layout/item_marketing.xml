<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dp10"
    android:background="@drawable/shape_white_5"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:padding="@dimen/dp10">

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_14_black"
            android:text=""
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvItemTime"
            style="@style/text_10_666"
            android:layout_marginTop="@dimen/dp5"
            android:text="" />

        <TextView
            android:id="@+id/tvItemTips"
            style="@style/text_12_green"
            android:layout_marginTop="@dimen/dp10"
            android:text="@string/member_price_join_discount"
            android:visibility="gone" />

    </LinearLayout>

    <View
        android:layout_width="0.5dp"
        android:layout_height="match_parent"
        android:background="@color/color_line" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvItem"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="3"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

</LinearLayout>