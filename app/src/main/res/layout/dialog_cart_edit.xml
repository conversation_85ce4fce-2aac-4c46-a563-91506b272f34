<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvDialogTitle"
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/linDialogPrice"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp10"
        android:background="@drawable/shape_green_kuang_5"
        android:gravity="center|left"
        android:orientation="horizontal"
        android:padding="@dimen/dp10">

        <TextView
            android:id="@+id/tvDialogPrice"
            style="@style/text_14_white"
            android:background="@drawable/shape_green_5"
            android:paddingHorizontal="@dimen/dp5"
            android:text="" />

        <ImageView
            android:id="@+id/ivDialogCursor"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp18"
            android:src="@drawable/anim_cursor"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvDialogPriceHint"
            style="@style/text_14_black"
            android:hint=""
            android:text=""
            android:visibility="gone" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/linDialogDiscount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:orientation="horizontal">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvDialogDiscount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="3" />

        <LinearLayout
            android:id="@+id/linDialogCustom"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvDialogCustom"
                style="@style/text_14_black"
                android:text="" />

            <TextView
                android:id="@+id/tvDialogCustomHint"
                style="@style/text_14_black"
                android:gravity="center"
                android:hint="@string/custom" />

            <ImageView
                android:id="@+id/ivDialogCursorCustom"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp18"
                android:layout_gravity="center_vertical"
                android:src="@drawable/anim_cursor"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

    <com.yxl.cashier_retail.view.NumberKeyBoardView
        android:id="@+id/numberKeyBoardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp5" />

</LinearLayout>