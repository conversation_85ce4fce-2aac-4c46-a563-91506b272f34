<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/select_goods_unit"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/color_line" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp10"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_weight="1"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/etDialogSearch"
                style="@style/text_12_333"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:background="@null"
                android:drawableStart="@mipmap/ic_search002"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:hint="@string/search_tips_default"
                android:imeOptions="actionSearch"
                android:inputType="text"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivDialogSearchClear"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_close001"
                android:visibility="gone" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvDialogAdd"
            style="@style/text_14_white"
            android:layout_gravity="center"
            android:background="@drawable/shape_green_5"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/newly" />

    </LinearLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/smartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:paddingHorizontal="@dimen/dp10">

        <com.scwang.smart.refresh.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            <LinearLayout
                android:id="@+id/linEmpty"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="@dimen/dp40"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/ivEmpty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_empty" />

                <TextView
                    android:id="@+id/tvEmpty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp26"
                    android:textColor="@color/color_666"
                    android:textSize="@dimen/f14"
                    android:textStyle="bold" />

            </LinearLayout>

        </RelativeLayout>

        <com.scwang.smart.refresh.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <TextView
        android:id="@+id/tvDialogConfirm"
        style="@style/text_14_white"
        android:layout_width="@dimen/dp200"
        android:layout_gravity="center"
        android:layout_marginVertical="@dimen/dp10"
        android:background="@drawable/shape_green_5"
        android:gravity="center"
        android:paddingVertical="@dimen/dp5"
        android:text="@string/confirm" />

</LinearLayout>