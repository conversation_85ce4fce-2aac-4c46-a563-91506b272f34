<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_f5_top_5">

        <TextView
            android:id="@+id/tvSystem"
            style="@style/text_14_black"
            android:layout_height="@dimen/dp34"
            android:background="@drawable/shape_white_topleft_5"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp16"
            android:text="@string/setting_system"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvVoice"
            style="@style/text_14_333"
            android:layout_height="@dimen/dp34"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp16"
            android:text="@string/setting_voice" />

        <TextView
            android:id="@+id/tvInlet"
            style="@style/text_14_333"
            android:layout_height="@dimen/dp34"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp16"
            android:text="@string/setting_inlet"
            android:visibility="gone" />

    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_bottom_5">

        <!--系统设置-->
        <LinearLayout
            android:id="@+id/linSystem"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/dp10"
            android:visibility="visible">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/cheng_weight_two" />

                    <EditText
                        android:id="@+id/etSystemGoodsWeightBarcodeFirstTwo"
                        style="@style/text_14_black"
                        android:layout_width="@dimen/dp140"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:inputType="number"
                        android:maxLength="2"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/cheng_standard_two" />

                    <EditText
                        android:id="@+id/etSystemGoodsCommonBarcodeFirstTwo"
                        style="@style/text_14_black"
                        android:layout_width="@dimen/dp140"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:inputType="number"
                        android:maxLength="2"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:visibility="gone">

                    <TextView
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/serial_port_weighing_scale" />

                    <ImageView
                        android:id="@+id/ivSystemSerialPortWeighingScale"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:padding="@dimen/dp5"
                        android:src="@drawable/selector_checkbox002" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:visibility="gone">

                    <TextView
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/goods_nobarcode_add_cart" />

                    <ImageView
                        android:id="@+id/ivSystemGoodsNoBarcodeAddCart"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:padding="@dimen/dp5"
                        android:src="@drawable/selector_checkbox002" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/startup" />

                    <ImageView
                        android:id="@+id/ivSystemStartUp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:padding="@dimen/dp5"
                        android:src="@drawable/selector_checkbox002" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/show_nobarcode_and_weight" />

                    <ImageView
                        android:id="@+id/ivSystemShowGoodsNoBarcode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:padding="@dimen/dp5"
                        android:src="@drawable/selector_checkbox002" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/change_nobarcode_and_weight" />

                    <ImageView
                        android:id="@+id/ivSystemChangeGoodsNoBarcodeAndWeight"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:padding="@dimen/dp5"
                        android:src="@drawable/selector_checkbox002" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/hang_print_receipts" />

                    <ImageView
                        android:id="@+id/ivSystemHangPrintReceipt"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:padding="@dimen/dp5"
                        android:src="@drawable/selector_checkbox002" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/goods_syn_again" />

                    <TextView
                        android:id="@+id/tvSyn"
                        style="@style/text_14_white"
                        android:background="@drawable/shape_green_5"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/goods_syb_start" />

                </LinearLayout>

            </LinearLayout>

            <View
                android:layout_width="0.5dp"
                android:layout_height="match_parent"
                android:background="@color/color_line" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                tools:ignore="RtlSymmetry">

                <LinearLayout
                    android:id="@+id/linLanguage"
                    android:layout_width="@dimen/dp140"
                    android:layout_height="wrap_content"
                    android:layout_gravity="right"
                    android:layout_marginEnd="@dimen/dp10"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6"
                    tools:ignore="RtlHardcoded">

                    <ImageView
                        android:id="@+id/ivLanguage"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp6"
                        android:src="@mipmap/ic_language001" />

                    <TextView
                        android:id="@+id/tvLanguage"
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="中文" />

                    <ImageView
                        android:id="@+id/ivLanguageMore"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_arrow001" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/home_input_digit_create_nobarcode_goods" />

                    <ImageView
                        android:id="@+id/ivSystemHomeInputDigitCreateGoodsNoBarcode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:padding="@dimen/dp5"
                        android:src="@drawable/selector_checkbox002" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/is_show_stock" />

                    <ImageView
                        android:id="@+id/ivSystemShowStock"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:padding="@dimen/dp5"
                        android:src="@drawable/selector_checkbox002" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:visibility="gone">

                    <TextView
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/is_exit_procedure" />

                    <ImageView
                        android:id="@+id/ivSystemExitClose"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:padding="@dimen/dp5"
                        android:src="@drawable/selector_checkbox002" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvVersion"
                        style="@style/text_14_white"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_green_5"
                        android:gravity="center"
                        android:paddingHorizontal="@dimen/dp5"
                        android:paddingVertical="@dimen/dp8"
                        android:text="@string/version_check"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvLog"
                        style="@style/text_14_white"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_orange_5"
                        android:gravity="center"
                        android:paddingHorizontal="@dimen/dp5"
                        android:paddingVertical="@dimen/dp8"
                        android:text="@string/upload_log"
                        android:textStyle="bold"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tvExit"
                        style="@style/text_14_white"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_red_5"
                        android:gravity="center"
                        android:paddingHorizontal="@dimen/dp5"
                        android:paddingVertical="@dimen/dp8"
                        android:text="@string/exit_procedure"
                        android:textStyle="bold" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!--声音设置-->
        <LinearLayout
            android:id="@+id/linVoice"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="@dimen/dp10"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/is_voice_offline_pay" />

                <ImageView
                    android:id="@+id/ivVoicePaymentOffline"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@drawable/selector_checkbox002" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/is_voice_member_pay" />

                <ImageView
                    android:id="@+id/ivVoicePaymentMember"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@drawable/selector_checkbox002" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/is_voice_combination_pay" />

                <ImageView
                    android:id="@+id/ivVoicePaymentCombination"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@drawable/selector_checkbox002" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/is_voice_online_pay" />

                <ImageView
                    android:id="@+id/ivVoicePaymentOnline"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@drawable/selector_checkbox002" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/is_voice_home" />

                <ImageView
                    android:id="@+id/ivVoiceHome"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@drawable/selector_checkbox002" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/is_voice_kewboard" />

                <ImageView
                    android:id="@+id/ivVoiceKeyboard"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@drawable/selector_checkbox002" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/is_voice_cash_pay" />

                <ImageView
                    android:id="@+id/ivVoicePaymentCash"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@drawable/selector_checkbox002" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/is_voice_applet_pay" />

                <ImageView
                    android:id="@+id/ivVoicePaymentApplet"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@drawable/selector_checkbox002" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/is_voice_applet_refund" />

                <ImageView
                    android:id="@+id/ivVoiceRefundApplet"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@drawable/selector_checkbox002" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/is_voice_applet_order" />

                <ImageView
                    android:id="@+id/ivVoiceOrderApplet"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@drawable/selector_checkbox002" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

            </LinearLayout>

        </LinearLayout>

        <!--入口设置-->
        <LinearLayout
            android:id="@+id/linInlet"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:visibility="gone">


        </LinearLayout>

    </RelativeLayout>

</LinearLayout>