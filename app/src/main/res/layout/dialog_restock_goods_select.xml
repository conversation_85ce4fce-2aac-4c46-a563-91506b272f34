<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp12"
            android:text="@string/goods_purchase"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp28"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/ivDialogImg"
            android:layout_width="@dimen/dp72"
            android:layout_height="@dimen/dp72"
            android:layout_marginEnd="@dimen/dp10"
            android:background="@drawable/shape_f2_5"
            android:src="@mipmap/ic_default_img" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvDialogName"
                style="@style/text_14_black"
                android:maxLines="1"
                android:text=""
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvDialogBarcode"
                style="@style/text_12_666"
                android:layout_marginTop="@dimen/dp5"
                android:text="" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvDialogPrice"
                    style="@style/text_14_blue"
                    android:text="0.00"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvDialogUnit"
                    style="@style/text_12_blue"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="" />

                <TextView
                    android:id="@+id/tvDialogSpecs"
                    style="@style/text_12_red"
                    android:drawablePadding="@dimen/dp5"
                    android:gravity="center"
                    android:padding="@dimen/dp10"
                    android:text="@string/specs"
                    android:visibility="gone" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp28"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/shape_f2_5"
        android:orientation="vertical"
        android:padding="@dimen/dp10">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvDialogSupplier"
                style="@style/text_10_666"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/dp4"
                android:text="@string/supplier_colon" />

            <ImageView
                android:id="@+id/ivDialogSupplierImg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toEndOf="@+id/tvDialogSupplier"
                android:src="@mipmap/ic_supplier_img001"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvDialogChange"
                style="@style/text_10_blue"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginTop="-10dp"
                android:layout_marginEnd="-10dp"
                android:padding="@dimen/dp10"
                android:text="@string/supplier_change" />

        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvDialogCount3"
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="@string/sales_three_colon" />

            <TextView
                android:id="@+id/tvDialogCount7"
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="@string/sales_seven_colon" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvDialogStock"
                style="@style/text_10_666"
                android:text="@string/stock_sale" />

            <TextView
                android:id="@+id/tvDialogSafe"
                style="@style/text_10_red"
                android:text="@string/stock_below_safe"
                android:visibility="gone" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvDialogLast"
            style="@style/text_10_666"
            android:layout_marginTop="@dimen/dp10"
            android:text="@string/buy_info_last" />

        <TextView
            android:id="@+id/tvDialogSuggest"
            style="@style/text_10_red"
            android:layout_marginTop="@dimen/dp10"
            android:text="@string/buy_count_suggest"
            android:textStyle="bold" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/linDialogPrice"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp18"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="gone">

        <ImageView
            android:id="@+id/ivDialogSubPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_sub001" />

        <RelativeLayout
            android:id="@+id/relDialogInPrice"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/shape_green_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/linDialogInPrice"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tvDialogInPrice"
                    style="@style/text_14_black"
                    android:hint=""
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp6" />

                <ImageView
                    android:id="@+id/ivDialogInPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp18"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/anim_cursor" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linDialogInPriceHint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvDialogInPriceHints"
                    style="@style/text_14_black"
                    android:hint="@string/input_in_price"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp6" />

                <ImageView
                    android:id="@+id/ivDialogInPriceHints"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp18"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/anim_cursor" />

            </LinearLayout>

        </RelativeLayout>

        <ImageView
            android:id="@+id/ivDialogAddPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_add001" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp18"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/ivDialogSubCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_sub001" />

        <RelativeLayout
            android:id="@+id/relDialogCount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/shape_green_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/linDialogCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tvDialogCount"
                    style="@style/text_14_black"
                    android:hint=""
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp6" />

                <ImageView
                    android:id="@+id/ivDialogCount"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp18"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/anim_cursor" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linDialogCountHint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvDialogCountHint"
                    style="@style/text_14_black"
                    android:hint="@string/input_restock_count"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp6" />

                <ImageView
                    android:id="@+id/ivDialogCountHint"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp18"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/anim_cursor" />

            </LinearLayout>

        </RelativeLayout>

        <ImageView
            android:id="@+id/ivDialogAddCount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_add001" />

    </LinearLayout>

    <com.yxl.cashier_retail.view.NumberKeyBoardView
        android:id="@+id/numberKeyBoardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp51"
        android:layout_marginVertical="@dimen/dp5" />

</LinearLayout>