<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_10"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <TextView
        android:gravity="center"
        android:padding="@dimen/dp16"
        style="@style/text_16_black"
        android:text="@string/authorize_remind"
        android:textStyle="bold" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/color_line" />

    <TextView
        style="@style/text_14_black"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="@dimen/dp20"
        android:layout_marginTop="@dimen/dp10"
        android:text="@string/need_access_colon" />

    <TextView
        android:id="@+id/tvDialogContent"
        style="@style/text_14_666"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="@dimen/dp20"
        android:layout_marginTop="@dimen/dp10"
        android:text="" />

    <TextView
        android:id="@+id/tvDialogConfirm"
        style="@style/text_14_white"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="@dimen/dp20"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/shape_blue_5"
        android:gravity="center"
        android:paddingVertical="@dimen/dp10"
        android:text="@string/agree_and_continue" />

    <TextView
        android:id="@+id/tvDialogCancel"
        style="@style/text_14_white"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="@dimen/dp20"
        android:layout_marginVertical="@dimen/dp10"
        android:background="@drawable/shape_blue_tm_5"
        android:gravity="center"
        android:paddingVertical="@dimen/dp10"
        android:text="@string/disagree" />

    <TextView
        android:id="@+id/tvDialogAgain"
        style="@style/text_16_666"
        android:padding="@dimen/dp16"
        android:text="@string/do_not_remind_again"
        android:visibility="gone" />

</LinearLayout>
