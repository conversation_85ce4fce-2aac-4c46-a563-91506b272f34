<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/white">

        <ImageView
            android:id="@+id/ivBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_back002" />

        <TextView
            style="@style/text_16_black"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/ivBack"
            android:text="@string/restock_create"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvTitle"
            style="@style/text_18_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text=""
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/linDate"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toStartOf="@+id/tvCashier"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10">

            <TextClock
                style="@style/text_12_black"
                android:format24Hour="MM-dd HH:mm"
                android:textStyle="bold" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvCashier"
            style="@style/text_14_white"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/green"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp20"
            android:text="@string/cashier_table"
            android:textStyle="bold"
            app:drawableLeftCompat="@mipmap/ic_cashier001" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="@dimen/dp5"
        android:layout_marginTop="@dimen/dp46"
        android:layout_marginBottom="@dimen/dp5"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="2"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@drawable/shape_white_5"
                android:orientation="vertical"
                tools:ignore="NestedWeights">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingVertical="@dimen/dp6">

                    <TextView
                        style="@style/text_12_999"
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_weight="1.5"
                        android:text="@string/goods_name" />

                    <TextView
                        style="@style/text_12_999"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/buy_count" />

                    <TextView
                        style="@style/text_12_999"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/price_estimate" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingHorizontal="@dimen/dp10"
                        android:src="@mipmap/ic_del002"
                        android:visibility="invisible" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:background="@color/color_line" />

                <com.scwang.smart.refresh.layout.SmartRefreshLayout
                    android:id="@+id/srlRestock"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <com.scwang.smart.refresh.header.ClassicsHeader
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rvRestock"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                        <LinearLayout
                            android:id="@+id/linEmptyRestock"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:paddingTop="@dimen/dp40"
                            android:visibility="gone">

                            <ImageView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@mipmap/ic_empty" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/dp26"
                                android:textColor="@color/color_666"
                                android:textSize="@dimen/f14"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </RelativeLayout>

                    <com.scwang.smart.refresh.footer.ClassicsFooter
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </com.scwang.smart.refresh.layout.SmartRefreshLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                android:background="@drawable/shape_white_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <TextView
                    android:id="@+id/tvCancel"
                    style="@style/text_14_red"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/dp5"
                    android:layout_weight="1"
                    android:background="@drawable/shape_red_kuang_5"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp8"
                    android:text="@string/restock_cancel" />

                <TextView
                    android:id="@+id/tvPreview"
                    style="@style/text_14_white"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/dp5"
                    android:layout_weight="1"
                    android:background="@drawable/shape_orange_5"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp8"
                    android:text="@string/preview" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:background="@drawable/shape_f2_5"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_white_top_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp10">

                <TextView
                    style="@style/text_16_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/restock_select_goods"
                    android:textStyle="bold" />

                <LinearLayout
                    android:id="@+id/linSupplier"
                    android:layout_width="@dimen/dp140"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp10"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="@dimen/dp6"
                    android:visibility="gone">

                    <TextView
                        android:id="@+id/tvSupplier"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:text="@string/supplier_all" />

                    <ImageView
                        android:id="@+id/ivSupplier"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_arrow002" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="@dimen/dp200"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/etSearch"
                        style="@style/text_12_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:background="@null"
                        android:drawableStart="@mipmap/ic_search001"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:hint="@string/search_tips"
                        android:imeOptions="actionSearch"
                        android:inputType="text"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp4"
                        tools:ignore="NestedWeights" />

                    <ImageView
                        android:id="@+id/ivSearchClear"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dp4"
                        android:src="@mipmap/ic_close001"
                        android:visibility="gone" />

                </LinearLayout>

            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="@dimen/dp100"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvCount"
                            style="@style/text_10_666"
                            android:layout_width="0dp"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="1"
                            android:text="@string/goods_all" />

                        <LinearLayout
                            android:id="@+id/linSort"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:padding="@dimen/dp10">

                            <TextView
                                android:id="@+id/tvSort"
                                style="@style/text_10_black"
                                android:layout_marginEnd="@dimen/dp3"
                                android:text="@string/sort" />

                            <ImageView
                                android:id="@+id/ivSort"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@mipmap/ic_arrow003" />

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/linStock"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:padding="@dimen/dp10">

                            <TextView
                                android:id="@+id/tvStock"
                                style="@style/text_10_black"
                                android:layout_marginEnd="@dimen/dp3"
                                android:text="@string/stock" />

                            <ImageView
                                android:id="@+id/ivStock"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@mipmap/ic_arrow003" />

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/linPrice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:padding="@dimen/dp10">

                            <TextView
                                android:id="@+id/tvPrice"
                                style="@style/text_10_black"
                                android:layout_marginEnd="@dimen/dp3"
                                android:text="@string/price" />

                            <ImageView
                                android:id="@+id/ivPrice"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@mipmap/ic_arrow003" />

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/linSale"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:padding="@dimen/dp10">

                            <TextView
                                android:id="@+id/tvSale"
                                style="@style/text_10_black"
                                android:layout_marginEnd="@dimen/dp3"
                                android:text="@string/sales" />

                            <ImageView
                                android:id="@+id/ivSale"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@mipmap/ic_arrow003" />

                        </LinearLayout>

                    </LinearLayout>

                    <com.scwang.smart.refresh.layout.SmartRefreshLayout
                        android:id="@+id/smartRefreshLayout"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <com.scwang.smart.refresh.header.ClassicsHeader
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recyclerView"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:paddingHorizontal="7.5dp"
                                android:paddingVertical="2.5dp"
                                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                                app:spanCount="3" />

                            <LinearLayout
                                android:id="@+id/linEmpty"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="vertical"
                                android:paddingTop="@dimen/dp40"
                                android:visibility="gone">

                                <ImageView
                                    android:id="@+id/ivEmpty"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:src="@mipmap/ic_empty" />

                                <TextView
                                    android:id="@+id/tvEmpty"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="@dimen/dp26"
                                    android:textColor="@color/color_666"
                                    android:textSize="@dimen/f14"
                                    android:textStyle="bold" />

                            </LinearLayout>

                        </RelativeLayout>

                        <com.scwang.smart.refresh.footer.ClassicsFooter
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />

                    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvCate"
                    android:layout_width="@dimen/dp90"
                    android:layout_height="match_parent"
                    android:layout_alignParentEnd="true"
                    android:layout_marginVertical="@dimen/dp10"
                    android:layout_marginEnd="@dimen/dp10"
                    android:background="@drawable/shape_17262b_5"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            </RelativeLayout>

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>