<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/purchase_voucher"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <TextView
        style="@style/text_14_black"
        android:layout_marginHorizontal="@dimen/dp10"
        android:text="@string/repayment_total" />

    <EditText
        android:id="@+id/etDialogMoney"
        style="@style/text_14_333"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/shape_f2_5"
        android:hint="@string/input_repayment_total"
        android:inputType="numberDecimal"
        android:maxLines="1"
        android:padding="@dimen/dp10" />

    <TextView
        style="@style/text_14_black"
        android:layout_marginHorizontal="@dimen/dp10"
        android:layout_marginTop="@dimen/dp15"
        android:text="@string/remarks_info" />

    <EditText
        android:id="@+id/etDialogRemarks"
        style="@style/text_14_black"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/shape_f2_5"
        android:hint="@string/input_remarks_info"
        android:inputType="text"
        android:maxLines="1"
        android:padding="@dimen/dp10" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp15"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp10">

        <TextView
            style="@style/text_14_black"
            android:layout_marginEnd="@dimen/dp5"
            android:text="@string/purchase_voucher_upload" />

        <TextView
            android:id="@+id/tvDialogCount"
            style="@style/text_14_black"
            android:text="0/3" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvDialog"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp5"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:spanCount="3" />

    <TextView
        android:id="@+id/tvDialogConfirm"
        style="@style/text_14_white"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="@dimen/dp60"
        android:layout_marginVertical="@dimen/dp10"
        android:layout_marginBottom="@dimen/dp10"
        android:background="@drawable/shape_green_5"
        android:gravity="center"
        android:paddingVertical="@dimen/dp10"
        android:text="@string/submit"
        android:textStyle="bold" />

</LinearLayout>