<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/collection_stored_card"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <!--会员详情-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:background="@drawable/shape_f2_5"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="@dimen/dp5">

        <ImageView
            android:id="@+id/ivDialogHead"
            android:layout_width="@dimen/dp45"
            android:layout_height="@dimen/dp45"
            android:layout_marginEnd="@dimen/dp5"
            android:src="@mipmap/ic_head003" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="3"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvDialogMemberMobile"
                style="@style/text_12_black"
                android:ellipsize="end"
                android:maxLines="1"
                android:text=""
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvDialogMemberName"
                    style="@style/text_12_999"
                    android:layout_marginEnd="@dimen/dp5"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="" />

                <ImageView
                    android:id="@+id/ivDialogMemberLevel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linDialogMemberBalance"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="2"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvDialogMemberBalance"
                style="@style/text_12_orange"
                android:text=""
                android:textStyle="bold" />

            <TextView
                style="@style/text_12_orange"
                android:layout_marginTop="@dimen/dp5"
                android:drawablePadding="@dimen/dp2"
                android:gravity="center_vertical"
                android:text="@string/balance"
                android:textStyle="bold"
                app:drawableLeftCompat="@mipmap/ic_balance001" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvDialogMemberPoints"
                style="@style/text_12_green"
                android:text=""
                android:textStyle="bold" />

            <TextView
                style="@style/text_12_666"
                android:layout_marginTop="@dimen/dp5"
                android:drawablePadding="@dimen/dp2"
                android:gravity="center_vertical"
                android:text="@string/points"
                android:textStyle="bold"
                app:drawableLeftCompat="@mipmap/ic_points001" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/shape_d8_kuang_left_5"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingVertical="@dimen/dp12">

            <TextView
                style="@style/text_12_666"
                android:gravity="center"
                android:paddingHorizontal="@dimen/dp5"
                android:text="@string/receivable" />

            <TextView
                android:id="@+id/tvDialogTotal"
                style="@style/text_14_black"
                android:layout_marginTop="@dimen/dp8"
                android:text="0.00"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/shape_d8_kuang"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingVertical="@dimen/dp12">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp5">

                <ImageView
                    android:layout_width="@dimen/dp16"
                    android:layout_height="@dimen/dp16"
                    android:layout_marginEnd="@dimen/dp2"
                    android:src="@mipmap/ic_payment_img014" />

                <TextView
                    style="@style/text_12_666"
                    android:text="@string/stored_card" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvDialogStore"
                style="@style/text_14_red"
                android:layout_marginTop="@dimen/dp8"
                android:text="@string/place_select_member" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linDialogJinQ"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/shape_d8_kuang"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingVertical="@dimen/dp12">

            <TextView
                style="@style/text_12_666"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/dp5"
                android:text="@string/jinquan_collection"
                app:drawableLeftCompat="@mipmap/ic_payment_img013" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp8"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvDialogJinQ"
                    style="@style/text_14_black"
                    android:text="" />

                <ImageView
                    android:id="@+id/ivDialogCursorJinQ"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp18"
                    android:src="@drawable/anim_cursor" />

                <TextView
                    android:id="@+id/tvDialogJinQHint"
                    style="@style/text_14_black"
                    android:hint="@string/input" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linDialogCash"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/shape_d8_kuang_right_5"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingVertical="@dimen/dp12">

            <TextView
                style="@style/text_12_666"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/dp5"
                android:text="@string/cash"
                app:drawableLeftCompat="@mipmap/ic_payment_img011" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp8"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvDialogCash"
                    style="@style/text_14_black"
                    android:text="" />

                <ImageView
                    android:id="@+id/ivDialogCursorCash"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp18"
                    android:src="@drawable/anim_cursor"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tvDialogCashHint"
                    style="@style/text_14_black"
                    android:hint="@string/input" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <com.yxl.cashier_retail.view.NumberKeyBoardView
        android:id="@+id/numberKeyBoardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp51"
        android:layout_marginVertical="@dimen/dp5" />

</LinearLayout>