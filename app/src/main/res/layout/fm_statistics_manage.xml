<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!--数据统计-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp5"
        android:layout_marginTop="@dimen/dp5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="4"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <!--销售额-->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp5"
                android:layout_weight="1"
                android:background="@drawable/shape_jb_fff0ef_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5"
                    android:src="@mipmap/ic_statistics_img001" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        style="@style/text_12_999"
                        android:maxLines="1"
                        android:text="@string/sales_volume" />

                    <TextView
                        android:id="@+id/tvSaleTotal"
                        style="@style/text_16_black"
                        android:layout_marginTop="@dimen/dp5"
                        android:text="0.00"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/ivSaleTotal"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/selector_statistics_profit" />

                    <TextView
                        android:id="@+id/tvSaleTotalRatio"
                        style="@style/text_12_green"
                        android:layout_marginTop="@dimen/dp5"
                        android:text="0.00%" />

                </LinearLayout>

            </LinearLayout>

            <!--毛利润-->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp5"
                android:layout_weight="1"
                android:background="@drawable/shape_jb_effff4_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5"
                    android:src="@mipmap/ic_statistics_img002" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        style="@style/text_12_999"
                        android:text="@string/gross_profit" />

                    <TextView
                        android:id="@+id/tvProfit"
                        style="@style/text_16_black"
                        android:layout_marginTop="@dimen/dp5"
                        android:text="0.00"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/ivProfit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/selector_statistics_profit" />

                    <TextView
                        android:id="@+id/tvProfitRatio"
                        style="@style/text_12_red"
                        android:layout_marginTop="@dimen/dp5"
                        android:text="0.00%" />

                </LinearLayout>

            </LinearLayout>

            <!--订单量-->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp5"
                android:layout_weight="1"
                android:background="@drawable/shape_jb_fff8ef_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5"
                    android:src="@mipmap/ic_statistics_img003" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        style="@style/text_12_999"
                        android:text="@string/order_quantity" />

                    <TextView
                        android:id="@+id/tvListCount"
                        style="@style/text_16_black"
                        android:layout_marginTop="@dimen/dp5"
                        android:text="0"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/ivListCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/selector_statistics_profit" />

                    <TextView
                        android:id="@+id/tvListCountRatio"
                        style="@style/text_12_red"
                        android:layout_marginTop="@dimen/dp5"
                        android:text="0.00%" />

                </LinearLayout>

            </LinearLayout>

            <!--客单价-->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/shape_jb_eff2ff_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5"
                    android:src="@mipmap/ic_statistics_img004" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        style="@style/text_12_999"
                        android:maxLines="1"
                        android:text="@string/aver_price" />

                    <TextView
                        android:id="@+id/tvAver"
                        style="@style/text_16_black"
                        android:layout_marginTop="@dimen/dp5"
                        android:text="0.00"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/ivAver"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/selector_statistics_profit" />

                    <TextView
                        android:id="@+id/tvAverRatio"
                        style="@style/text_12_red"
                        android:layout_marginTop="@dimen/dp5"
                        android:text="0.00%" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!--网单量-->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:background="@drawable/shape_jb_f6efff_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp5">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp5"
                android:src="@mipmap/ic_statistics_img005" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    style="@style/text_12_999"
                    android:maxLines="1"
                    android:text="@string/online_order_quantity" />

                <TextView
                    android:id="@+id/tvNetListCount"
                    style="@style/text_16_black"
                    android:layout_marginTop="@dimen/dp5"
                    android:text="0.00"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/ivNetListCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/selector_statistics_profit" />

                <TextView
                    android:id="@+id/tvNetListCountRatio"
                    style="@style/text_12_red"
                    android:layout_marginTop="@dimen/dp5"
                    android:text="0.00%" />

            </LinearLayout>

        </LinearLayout>

        <!--到店会员-->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/shape_jb_fff8ef_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp5">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp5"
                android:src="@mipmap/ic_statistics_img006" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    style="@style/text_12_999"
                    android:maxLines="1"
                    android:text="@string/member_to_shop" />

                <TextView
                    android:id="@+id/tvMemberCount"
                    style="@style/text_16_black"
                    android:layout_marginTop="@dimen/dp5"
                    android:text="0.00"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp5"
        android:layout_marginTop="@dimen/dp5"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="4"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <!--充值金额-->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp5"
                android:layout_weight="1"
                android:background="@drawable/shape_jb_eff2ff_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5"
                    android:src="@mipmap/ic_statistics_img007" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        style="@style/text_12_999"
                        android:maxLines="1"
                        android:text="@string/recharge_money" />

                    <TextView
                        android:id="@+id/tvRechargeMoney"
                        style="@style/text_16_black"
                        android:layout_marginTop="@dimen/dp5"
                        android:text="0.00"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

            <!--储值金额-->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp5"
                android:layout_weight="1"
                android:background="@drawable/shape_jb_fff8ef_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5"
                    android:src="@mipmap/ic_statistics_img008" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        style="@style/text_12_999"
                        android:text="@string/stored_money" />

                    <TextView
                        android:id="@+id/tvStoredMoney"
                        style="@style/text_16_black"
                        android:layout_marginTop="@dimen/dp5"
                        android:text="0.00"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

            <!--退款金额-->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp5"
                android:layout_weight="1"
                android:background="@drawable/shape_jb_fff8ef_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5"
                    android:src="@mipmap/ic_statistics_img009" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        style="@style/text_12_999"
                        android:text="@string/refund_money" />

                    <TextView
                        android:id="@+id/tvRefundMoney"
                        style="@style/text_16_black"
                        android:layout_marginTop="@dimen/dp5"
                        android:text="0.00"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

            <!--过期预警-->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/shape_jb_eff2ff_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5"
                    android:src="@mipmap/ic_statistics_img010" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        style="@style/text_12_999"
                        android:maxLines="1"
                        android:text="@string/expired_warning" />

                    <TextView
                        android:id="@+id/tvExpiredWarning"
                        style="@style/text_16_black"
                        android:layout_marginTop="@dimen/dp5"
                        android:text="0"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!--需补货商品-->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:background="@drawable/shape_jb_effff4_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp5">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp5"
                android:src="@mipmap/ic_statistics_img011" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    style="@style/text_12_999"
                    android:maxLines="1"
                    android:text="@string/restock_goods" />

                <TextView
                    android:id="@+id/tvRestockGoods"
                    style="@style/text_16_black"
                    android:layout_marginTop="@dimen/dp5"
                    android:text="0"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

        <!--商品总种类-->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/shape_jb_f6efff_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp5">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp5"
                android:src="@mipmap/ic_statistics_img012" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    style="@style/text_12_999"
                    android:maxLines="1"
                    android:text="@string/goods_type" />

                <TextView
                    android:id="@+id/tvGoodsTotalType"
                    style="@style/text_16_black"
                    android:layout_marginTop="@dimen/dp5"
                    android:text="0"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <!--收银界面新版-横轴界面(啥呀)-->
    <!--    <LinearLayout-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_margin="@dimen/dp5"-->
    <!--        android:gravity="center_vertical"-->
    <!--        android:orientation="horizontal">-->

    <!--        <LinearLayout-->
    <!--            android:layout_width="0dp"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:layout_marginEnd="@dimen/dp5"-->
    <!--            android:layout_weight="2"-->
    <!--            android:gravity="center_vertical"-->
    <!--            android:orientation="horizontal">-->

    <!--            &lt;!&ndash;今日营业额&ndash;&gt;-->
    <!--            <LinearLayout-->
    <!--                android:layout_width="0dp"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:layout_marginEnd="@dimen/dp5"-->
    <!--                android:layout_weight="1"-->
    <!--                android:background="@drawable/shape_f2_5"-->
    <!--                android:gravity="center_vertical"-->
    <!--                android:orientation="horizontal"-->
    <!--                android:padding="@dimen/dp5">-->

    <!--                <ImageView-->
    <!--                    android:layout_width="wrap_content"-->
    <!--                    android:layout_height="wrap_content"-->
    <!--                    android:layout_marginEnd="@dimen/dp5"-->
    <!--                    android:src="@mipmap/ic_statistics_img001" />-->

    <!--                <LinearLayout-->
    <!--                    android:layout_width="0dp"-->
    <!--                    android:layout_height="wrap_content"-->
    <!--                    android:layout_weight="1"-->
    <!--                    android:orientation="vertical">-->

    <!--                    <TextView-->
    <!--                        style="@style/text_12_999"-->
    <!--                        android:maxLines="1"-->
    <!--                        android:text="@string/today_turnover" />-->

    <!--                    <TextView-->
    <!--                        android:id="@+id/tvSaleTotal"-->
    <!--                        style="@style/text_16_black"-->
    <!--                        android:layout_marginTop="@dimen/dp5"-->
    <!--                        android:text="0.00"-->
    <!--                        android:textStyle="bold" />-->

    <!--                </LinearLayout>-->

    <!--                <LinearLayout-->
    <!--                    android:layout_width="wrap_content"-->
    <!--                    android:layout_height="wrap_content"-->
    <!--                    android:gravity="center"-->
    <!--                    android:orientation="vertical">-->

    <!--                    <ImageView-->
    <!--                        android:id="@+id/ivSaleTotal"-->
    <!--                        android:layout_width="wrap_content"-->
    <!--                        android:layout_height="wrap_content"-->
    <!--                        android:src="@drawable/selector_statistics_profit" />-->

    <!--                    <TextView-->
    <!--                        android:id="@+id/tvSaleTotalRatio"-->
    <!--                        style="@style/text_12_green"-->
    <!--                        android:layout_marginTop="@dimen/dp5"-->
    <!--                        android:text="0.00%" />-->

    <!--                </LinearLayout>-->

    <!--            </LinearLayout>-->

    <!--            &lt;!&ndash;毛利润&ndash;&gt;-->
    <!--            <LinearLayout-->
    <!--                android:layout_width="0dp"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:layout_weight="1"-->
    <!--                android:background="@drawable/shape_f2_5"-->
    <!--                android:gravity="center_vertical"-->
    <!--                android:orientation="horizontal"-->
    <!--                android:padding="@dimen/dp5">-->

    <!--                <ImageView-->
    <!--                    android:layout_width="wrap_content"-->
    <!--                    android:layout_height="wrap_content"-->
    <!--                    android:layout_marginEnd="@dimen/dp5"-->
    <!--                    android:src="@mipmap/ic_statistics_img002" />-->

    <!--                <LinearLayout-->
    <!--                    android:layout_width="0dp"-->
    <!--                    android:layout_height="wrap_content"-->
    <!--                    android:layout_weight="1"-->
    <!--                    android:orientation="vertical">-->

    <!--                    <TextView-->
    <!--                        style="@style/text_12_999"-->
    <!--                        android:text="@string/gross_profit" />-->

    <!--                    <TextView-->
    <!--                        android:id="@+id/tvProfit"-->
    <!--                        style="@style/text_16_black"-->
    <!--                        android:layout_marginTop="@dimen/dp5"-->
    <!--                        android:text="0.00"-->
    <!--                        android:textStyle="bold" />-->

    <!--                </LinearLayout>-->

    <!--                <LinearLayout-->
    <!--                    android:layout_width="wrap_content"-->
    <!--                    android:layout_height="wrap_content"-->
    <!--                    android:gravity="center"-->
    <!--                    android:orientation="vertical">-->

    <!--                    <ImageView-->
    <!--                        android:id="@+id/ivProfit"-->
    <!--                        android:layout_width="wrap_content"-->
    <!--                        android:layout_height="wrap_content"-->
    <!--                        android:src="@drawable/selector_statistics_profit" />-->

    <!--                    <TextView-->
    <!--                        android:id="@+id/tvProfitRatio"-->
    <!--                        style="@style/text_12_red"-->
    <!--                        android:layout_marginTop="@dimen/dp5"-->
    <!--                        android:text="0.00%" />-->

    <!--                </LinearLayout>-->

    <!--            </LinearLayout>-->

    <!--        </LinearLayout>-->

    <!--        &lt;!&ndash;订单量&ndash;&gt;-->
    <!--        <LinearLayout-->
    <!--            android:layout_width="0dp"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:layout_marginEnd="@dimen/dp5"-->
    <!--            android:layout_weight="1"-->
    <!--            android:background="@drawable/shape_f2_5"-->
    <!--            android:gravity="center_vertical"-->
    <!--            android:orientation="horizontal"-->
    <!--            android:padding="@dimen/dp5">-->

    <!--            <ImageView-->
    <!--                android:layout_width="wrap_content"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:layout_marginEnd="@dimen/dp5"-->
    <!--                android:src="@mipmap/ic_statistics_img003" />-->

    <!--            <LinearLayout-->
    <!--                android:layout_width="0dp"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:layout_weight="1"-->
    <!--                android:orientation="vertical">-->

    <!--                <TextView-->
    <!--                    style="@style/text_12_999"-->
    <!--                    android:text="@string/order_quantity" />-->

    <!--                <TextView-->
    <!--                    android:id="@+id/tvListCount"-->
    <!--                    style="@style/text_16_black"-->
    <!--                    android:layout_marginTop="@dimen/dp5"-->
    <!--                    android:text="0"-->
    <!--                    android:textStyle="bold" />-->

    <!--            </LinearLayout>-->

    <!--            <LinearLayout-->
    <!--                android:layout_width="wrap_content"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:gravity="center"-->
    <!--                android:orientation="vertical">-->

    <!--                <ImageView-->
    <!--                    android:id="@+id/ivListCount"-->
    <!--                    android:layout_width="wrap_content"-->
    <!--                    android:layout_height="wrap_content"-->
    <!--                    android:src="@drawable/selector_statistics_profit" />-->

    <!--                <TextView-->
    <!--                    android:id="@+id/tvListCountRatio"-->
    <!--                    style="@style/text_12_red"-->
    <!--                    android:layout_marginTop="@dimen/dp5"-->
    <!--                    android:text="0.00%" />-->

    <!--            </LinearLayout>-->

    <!--        </LinearLayout>-->

    <!--        &lt;!&ndash;客单价&ndash;&gt;-->
    <!--        <LinearLayout-->
    <!--            android:layout_width="0dp"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:layout_marginEnd="@dimen/dp5"-->
    <!--            android:layout_weight="1"-->
    <!--            android:background="@drawable/shape_f2_5"-->
    <!--            android:gravity="center_vertical"-->
    <!--            android:orientation="horizontal"-->
    <!--            android:padding="@dimen/dp5">-->

    <!--            <ImageView-->
    <!--                android:layout_width="wrap_content"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:layout_marginEnd="@dimen/dp5"-->
    <!--                android:src="@mipmap/ic_statistics_img004" />-->

    <!--            <LinearLayout-->
    <!--                android:layout_width="0dp"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:layout_weight="1"-->
    <!--                android:orientation="vertical">-->

    <!--                <TextView-->
    <!--                    style="@style/text_12_999"-->
    <!--                    android:maxLines="1"-->
    <!--                    android:text="@string/aver_price" />-->

    <!--                <TextView-->
    <!--                    android:id="@+id/tvAver"-->
    <!--                    style="@style/text_16_black"-->
    <!--                    android:layout_marginTop="@dimen/dp5"-->
    <!--                    android:text="0.00"-->
    <!--                    android:textStyle="bold" />-->

    <!--            </LinearLayout>-->

    <!--            <LinearLayout-->
    <!--                android:layout_width="wrap_content"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:gravity="center"-->
    <!--                android:orientation="vertical">-->

    <!--                <ImageView-->
    <!--                    android:id="@+id/ivAver"-->
    <!--                    android:layout_width="wrap_content"-->
    <!--                    android:layout_height="wrap_content"-->
    <!--                    android:src="@drawable/selector_statistics_profit" />-->

    <!--                <TextView-->
    <!--                    android:id="@+id/tvAverRatio"-->
    <!--                    style="@style/text_12_red"-->
    <!--                    android:layout_marginTop="@dimen/dp5"-->
    <!--                    android:text="0.00%" />-->

    <!--            </LinearLayout>-->

    <!--        </LinearLayout>-->

    <!--        &lt;!&ndash;网单量&ndash;&gt;-->
    <!--        <LinearLayout-->
    <!--            android:layout_width="0dp"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:layout_weight="1"-->
    <!--            android:background="@drawable/shape_f2_5"-->
    <!--            android:gravity="center_vertical"-->
    <!--            android:orientation="horizontal"-->
    <!--            android:padding="@dimen/dp5">-->

    <!--            <ImageView-->
    <!--                android:layout_width="wrap_content"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:layout_marginEnd="@dimen/dp5"-->
    <!--                android:src="@mipmap/ic_statistics_img005" />-->

    <!--            <LinearLayout-->
    <!--                android:layout_width="0dp"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:layout_weight="1"-->
    <!--                android:orientation="vertical">-->

    <!--                <TextView-->
    <!--                    style="@style/text_12_999"-->
    <!--                    android:maxLines="1"-->
    <!--                    android:text="@string/online_order_quantity" />-->

    <!--                <TextView-->
    <!--                    android:id="@+id/tvNetListCount"-->
    <!--                    style="@style/text_16_black"-->
    <!--                    android:layout_marginTop="@dimen/dp5"-->
    <!--                    android:text="0.00"-->
    <!--                    android:textStyle="bold" />-->

    <!--            </LinearLayout>-->

    <!--            <LinearLayout-->
    <!--                android:layout_width="wrap_content"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:gravity="center"-->
    <!--                android:orientation="vertical">-->

    <!--                <ImageView-->
    <!--                    android:id="@+id/ivNetListCount"-->
    <!--                    android:layout_width="wrap_content"-->
    <!--                    android:layout_height="wrap_content"-->
    <!--                    android:src="@drawable/selector_statistics_profit" />-->

    <!--                <TextView-->
    <!--                    android:id="@+id/tvNetListCountRatio"-->
    <!--                    style="@style/text_12_red"-->
    <!--                    android:layout_marginTop="@dimen/dp5"-->
    <!--                    android:text="0.00%" />-->

    <!--            </LinearLayout>-->

    <!--        </LinearLayout>-->

    <!--    </LinearLayout>-->

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp5"
        android:layout_marginTop="@dimen/dp5"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="4"
            android:background="@drawable/shape_white_5"
            android:orientation="vertical"
            android:paddingVertical="@dimen/dp10"
            tools:ignore="NestedWeights">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/sales_trend"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="@dimen/dp2">

                    <TextView
                        android:id="@+id/tvDaySales0"
                        style="@style/text_10_white"
                        android:background="@drawable/shape_green_left_5"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp3"
                        android:text="@string/week" />

                    <TextView
                        android:id="@+id/tvDaySales1"
                        style="@style/text_10_black"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp3"
                        android:text="@string/month" />

                    <TextView
                        android:id="@+id/tvDaySales2"
                        style="@style/text_10_black"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp3"
                        android:text="@string/year" />

                </LinearLayout>

            </LinearLayout>

            <com.github.mikephil.charting.charts.LineChart
                android:id="@+id/chartSales"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="@dimen/dp10" />

        </LinearLayout>

        <!--销售数据周期占比-->
        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:background="@drawable/shape_white_5"
            android:paddingVertical="@dimen/dp10">

            <TextView
                style="@style/text_12_black"
                android:layout_marginHorizontal="@dimen/dp10"
                android:text="@string/sales_cycle_ratio"
                android:textStyle="bold" />

            <com.yxl.cashier_retail.view.CircleProgress
                android:id="@+id/progress"
                android:layout_width="@dimen/dp100"
                android:layout_height="@dimen/dp100"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                app:antiAlias="true"
                app:arcWidth="@dimen/dp10"
                app:bgArcColor="@color/color_666"
                app:bgArcWidth="0dp"
                app:dottedLineWidth="10dp"
                app:foreEndColor="#F4F200"
                app:foreStartColor="#FE6615"
                app:hintSize="8dp"
                app:maxValue="100"
                app:startAngle="135"
                app:sweepAngle="270"
                app:textOffsetPercentInRadius="0.5"
                app:unit="@string/sales_volume"
                app:unitColor="@color/color_666"
                app:unitSize="10dp"
                app:value="0"
                app:valueColor="@color/black"
                app:valueSize="14dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvSalesCycleNetListCount"
                        style="@style/text_10_black"
                        android:text="0%" />

                    <TextView
                        style="@style/text_10_666"
                        android:layout_marginTop="@dimen/dp5"
                        android:maxLines="1"
                        android:text="@string/online_order_quantity" />

                </LinearLayout>

                <View
                    android:layout_width="@dimen/dp1"
                    android:layout_height="match_parent"
                    android:background="@color/color_line" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvSalesCycleProfit"
                        style="@style/text_10_black"
                        android:text="0%" />

                    <TextView
                        style="@style/text_10_666"
                        android:layout_marginTop="@dimen/dp5"
                        android:maxLines="1"
                        android:text="@string/gross_profit" />

                </LinearLayout>

                <View
                    android:layout_width="@dimen/dp1"
                    android:layout_height="match_parent"
                    android:background="@color/color_line" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvSalesCycleListCount"
                        style="@style/text_10_black"
                        android:text="0%" />

                    <TextView
                        style="@style/text_10_666"
                        android:layout_marginTop="@dimen/dp5"
                        android:maxLines="1"
                        android:text="@string/order_quantity" />

                </LinearLayout>

            </LinearLayout>

        </RelativeLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/shape_white_5"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:text="@string/sales_class_ratio"
                    android:textStyle="bold" />

            </LinearLayout>

            <com.github.mikephil.charting.charts.PieChart
                android:id="@+id/chartClass"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <ImageView
                android:id="@+id/ivEmptyClass"
                android:layout_width="@dimen/dp80"
                android:layout_height="@dimen/dp80"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/dp20"
                android:src="@mipmap/ic_empty"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="@dimen/dp5"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="4"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp5"
                android:layout_weight="2"
                android:background="@drawable/shape_white_5"
                android:orientation="vertical">

                <TextView
                    style="@style/text_12_black"
                    android:layout_marginHorizontal="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp10"
                    android:text="@string/sales_volume_hour"
                    android:textStyle="bold" />

                <com.github.mikephil.charting.charts.LineChart
                    android:id="@+id/chartSaleByHour"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="@dimen/dp10" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp5"
                android:layout_weight="1"
                android:background="@drawable/shape_white_5"
                android:orientation="vertical">

                <TextView
                    style="@style/text_12_black"
                    android:layout_marginHorizontal="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp10"
                    android:text="@string/payment_ratio"
                    android:textStyle="bold" />

                <com.github.mikephil.charting.charts.PieChart
                    android:id="@+id/chartPayType"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

                <ImageView
                    android:id="@+id/ivEmptyPayType"
                    android:layout_width="@dimen/dp80"
                    android:layout_height="@dimen/dp80"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/dp20"
                    android:src="@mipmap/ic_empty"
                    android:visibility="gone" />

            </LinearLayout>

            <!--热销商品-->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/shape_white_5"
                android:orientation="vertical"
                android:padding="@dimen/dp10">

                <TextView
                    style="@style/text_12_black"
                    android:text="@string/goods_hot_sales_top"
                    android:textStyle="bold" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvHot"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                <ImageView
                    android:id="@+id/ivEmptyHot"
                    android:layout_width="@dimen/dp80"
                    android:layout_height="@dimen/dp80"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/dp20"
                    android:src="@mipmap/ic_empty"
                    android:visibility="gone" />

            </LinearLayout>

        </LinearLayout>

        <!--累计利润商品TOP5-->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:background="@drawable/shape_white_5"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <TextView
                style="@style/text_12_black"
                android:text="@string/goods_profit_top"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvProfit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            <ImageView
                android:id="@+id/ivEmptyProfit"
                android:layout_width="@dimen/dp80"
                android:layout_height="@dimen/dp80"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/dp20"
                android:src="@mipmap/ic_empty"
                android:visibility="gone" />

        </LinearLayout>

        <!--滞销商品-->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/shape_white_5"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <TextView
                style="@style/text_12_black"
                android:text="@string/goods_unhot_sales_top"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvUnHot"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            <ImageView
                android:id="@+id/ivEmptyUnHot"
                android:layout_width="@dimen/dp80"
                android:layout_height="@dimen/dp80"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/dp20"
                android:src="@mipmap/ic_empty"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>

    <!--<com.scwang.smart.refresh.layout.SmartRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"-->
    <!--    xmlns:app="http://schemas.android.com/apk/res-auto"-->
    <!--    android:id="@+id/smartRefreshLayout"-->
    <!--    android:layout_width="match_parent"-->
    <!--    android:layout_height="match_parent"-->
    <!--    android:layout_weight="1">-->

    <!--    <com.scwang.smart.refresh.header.ClassicsHeader-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="wrap_content" />-->

    <!--    <androidx.core.widget.NestedScrollView-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="match_parent">-->

    <!--        <LinearLayout-->
    <!--            android:layout_width="match_parent"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:orientation="vertical">-->

    <!--            <androidx.recyclerview.widget.RecyclerView-->
    <!--                android:id="@+id/recyclerView"-->
    <!--                android:layout_width="match_parent"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:padding="2.5dp"-->
    <!--                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"-->
    <!--                app:spanCount="6" />-->

    <!--            <LinearLayout-->
    <!--                android:layout_width="match_parent"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:layout_marginHorizontal="@dimen/dp5"-->
    <!--                android:layout_marginTop="@dimen/dp5"-->
    <!--                android:gravity="center_vertical"-->
    <!--                android:orientation="horizontal">-->

    <!--                <LinearLayout-->
    <!--                    android:layout_width="0dp"-->
    <!--                    android:layout_height="wrap_content"-->
    <!--                    android:layout_marginEnd="@dimen/dp5"-->
    <!--                    android:layout_weight="4"-->
    <!--                    android:background="@drawable/shape_white_5"-->
    <!--                    android:orientation="vertical"-->
    <!--                    android:paddingVertical="@dimen/dp10">-->

    <!--                    <LinearLayout-->
    <!--                        android:layout_width="match_parent"-->
    <!--                        android:layout_height="wrap_content"-->
    <!--                        android:layout_marginHorizontal="@dimen/dp10"-->
    <!--                        android:gravity="center_horizontal"-->
    <!--                        android:orientation="horizontal">-->

    <!--                        <TextView-->
    <!--                            style="@style/text_12_black"-->
    <!--                            android:layout_width="0dp"-->
    <!--                            android:layout_weight="1"-->
    <!--                            android:text="今日销售走势"-->
    <!--                            android:textStyle="bold" />-->

    <!--                    </LinearLayout>-->

    <!--                    <com.github.mikephil.charting.charts.LineChart-->
    <!--                        android:id="@+id/lineChart"-->
    <!--                        android:layout_width="match_parent"-->
    <!--                        android:layout_height="@dimen/dp120" />-->

    <!--                </LinearLayout>-->

    <!--                <LinearLayout-->
    <!--                    android:layout_width="0dp"-->
    <!--                    android:layout_height="match_parent"-->
    <!--                    android:layout_marginEnd="@dimen/dp5"-->
    <!--                    android:layout_weight="1"-->
    <!--                    android:background="@drawable/shape_white_5"-->
    <!--                    android:orientation="vertical">-->

    <!--                    <TextView-->
    <!--                        style="@style/text_12_black"-->
    <!--                        android:layout_marginHorizontal="@dimen/dp10"-->
    <!--                        android:layout_marginTop="@dimen/dp10"-->
    <!--                        android:text="订单金额分布"-->
    <!--                        android:textStyle="bold" />-->

    <!--                    <com.github.mikephil.charting.charts.HorizontalBarChart-->
    <!--                        android:id="@+id/chartMoney"-->
    <!--                        android:layout_width="match_parent"-->
    <!--                        android:layout_height="@dimen/dp120" />-->

    <!--                </LinearLayout>-->

    <!--                <LinearLayout-->
    <!--                    android:layout_width="0dp"-->
    <!--                    android:layout_height="match_parent"-->
    <!--                    android:layout_weight="1"-->
    <!--                    android:background="@drawable/shape_white_5"-->
    <!--                    android:orientation="vertical">-->

    <!--                    <TextView-->
    <!--                        style="@style/text_12_black"-->
    <!--                        android:layout_marginHorizontal="@dimen/dp10"-->
    <!--                        android:layout_marginTop="@dimen/dp10"-->
    <!--                        android:text="销售品类占比"-->
    <!--                        android:textStyle="bold" />-->

    <!--                    <com.github.mikephil.charting.charts.PieChart-->
    <!--                        android:id="@+id/chartType"-->
    <!--                        android:layout_width="match_parent"-->
    <!--                        android:layout_height="@dimen/dp120" />-->

    <!--                </LinearLayout>-->

    <!--            </LinearLayout>-->

    <!--            <LinearLayout-->
    <!--                android:layout_width="match_parent"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:layout_marginHorizontal="@dimen/dp5"-->
    <!--                android:layout_marginVertical="@dimen/dp10"-->
    <!--                android:gravity="center_vertical"-->
    <!--                android:orientation="horizontal">-->

    <!--                <LinearLayout-->
    <!--                    android:layout_width="0dp"-->
    <!--                    android:layout_height="wrap_content"-->
    <!--                    android:layout_weight="4"-->
    <!--                    android:orientation="horizontal">-->

    <!--                    <LinearLayout-->
    <!--                        android:layout_width="0dp"-->
    <!--                        android:layout_height="match_parent"-->
    <!--                        android:layout_marginEnd="@dimen/dp5"-->
    <!--                        android:layout_weight="2"-->
    <!--                        android:background="@drawable/shape_white_5"-->
    <!--                        android:orientation="vertical">-->

    <!--                        <TextView-->
    <!--                            style="@style/text_12_black"-->
    <!--                            android:layout_marginHorizontal="@dimen/dp10"-->
    <!--                            android:layout_marginTop="@dimen/dp10"-->
    <!--                            android:text="商品利润占比"-->
    <!--                            android:textStyle="bold" />-->

    <!--                        <com.github.mikephil.charting.charts.BarChart-->
    <!--                            android:id="@+id/chartProfit"-->
    <!--                            android:layout_width="match_parent"-->
    <!--                            android:layout_height="@dimen/dp120" />-->

    <!--                    </LinearLayout>-->

    <!--                    &lt;!&ndash;热销商品&ndash;&gt;-->
    <!--                    <LinearLayout-->
    <!--                        android:layout_width="0dp"-->
    <!--                        android:layout_height="match_parent"-->
    <!--                        android:layout_marginEnd="@dimen/dp5"-->
    <!--                        android:layout_weight="1"-->
    <!--                        android:background="@drawable/shape_white_5"-->
    <!--                        android:orientation="vertical"-->
    <!--                        android:padding="@dimen/dp5">-->

    <!--                        <TextView-->
    <!--                            style="@style/text_12_black"-->
    <!--                            android:text="热销商品(3日)" />-->

    <!--                        <androidx.recyclerview.widget.RecyclerView-->
    <!--                            android:id="@+id/rvHotSales"-->
    <!--                            android:layout_width="match_parent"-->
    <!--                            android:layout_height="wrap_content"-->
    <!--                            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />-->

    <!--                    </LinearLayout>-->

    <!--                    <LinearLayout-->
    <!--                        android:layout_width="0dp"-->
    <!--                        android:layout_height="wrap_content"-->
    <!--                        android:layout_marginEnd="@dimen/dp5"-->
    <!--                        android:layout_weight="1"-->
    <!--                        android:background="@drawable/shape_white_5"-->
    <!--                        android:orientation="vertical">-->

    <!--                        <TextView-->
    <!--                            style="@style/text_12_black"-->
    <!--                            android:layout_marginHorizontal="@dimen/dp10"-->
    <!--                            android:layout_marginTop="@dimen/dp10"-->
    <!--                            android:text="会员消费占比"-->
    <!--                            android:textStyle="bold" />-->

    <!--                        <com.github.mikephil.charting.charts.PieChart-->
    <!--                            android:id="@+id/chartMember"-->
    <!--                            android:layout_width="match_parent"-->
    <!--                            android:layout_height="@dimen/dp120" />-->

    <!--                    </LinearLayout>-->

    <!--                </LinearLayout>-->

    <!--                <LinearLayout-->
    <!--                    android:layout_width="0dp"-->
    <!--                    android:layout_height="wrap_content"-->
    <!--                    android:layout_marginEnd="@dimen/dp5"-->
    <!--                    android:layout_weight="1"-->
    <!--                    android:background="@drawable/shape_white_5"-->
    <!--                    android:orientation="vertical">-->

    <!--                    <TextView-->
    <!--                        style="@style/text_12_black"-->
    <!--                        android:layout_marginHorizontal="@dimen/dp10"-->
    <!--                        android:layout_marginTop="@dimen/dp10"-->
    <!--                        android:text="收款类型占比"-->
    <!--                        android:textStyle="bold" />-->

    <!--                    <com.github.mikephil.charting.charts.PieChart-->
    <!--                        android:id="@+id/chartPayment"-->
    <!--                        android:layout_width="match_parent"-->
    <!--                        android:layout_height="@dimen/dp120" />-->

    <!--                </LinearLayout>-->

    <!--                &lt;!&ndash;滞销商品&ndash;&gt;-->
    <!--                <LinearLayout-->
    <!--                    android:layout_width="0dp"-->
    <!--                    android:layout_height="match_parent"-->
    <!--                    android:layout_weight="1"-->
    <!--                    android:background="@drawable/shape_white_5"-->
    <!--                    android:orientation="vertical"-->
    <!--                    android:padding="@dimen/dp5">-->

    <!--                    <TextView-->
    <!--                        style="@style/text_12_black"-->
    <!--                        android:text="滞销商品(30日)" />-->

    <!--                    <androidx.recyclerview.widget.RecyclerView-->
    <!--                        android:id="@+id/rvUnSales"-->
    <!--                        android:layout_width="match_parent"-->
    <!--                        android:layout_height="wrap_content"-->
    <!--                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />-->

    <!--                </LinearLayout>-->

    <!--            </LinearLayout>-->

    <!--        </LinearLayout>-->

    <!--    </androidx.core.widget.NestedScrollView>-->

    <!--    <com.scwang.smart.refresh.footer.ClassicsFooter-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="wrap_content" />-->

    <!--</com.scwang.smart.refresh.layout.SmartRefreshLayout>-->