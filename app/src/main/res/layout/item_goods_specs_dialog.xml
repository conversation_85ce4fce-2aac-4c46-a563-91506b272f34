<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/dp5"
    android:background="@drawable/shape_d8_kuang_5"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingVertical="@dimen/dp10">

    <ImageView
        android:id="@+id/ivItemSelect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp10"
        android:src="@drawable/selector_checkbox003" />

    <ImageView
        android:id="@+id/ivItemImg"
        android:layout_width="@dimen/dp72"
        android:layout_height="@dimen/dp72"
        android:layout_marginEnd="@dimen/dp10"
        android:background="@drawable/shape_f2_5"
        android:src="@mipmap/ic_default_img" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp10"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_14_black"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="" />

        <TextView
            android:id="@+id/tvItemBarcode"
            style="@style/text_12_666"
            android:layout_marginTop="@dimen/dp5"
            android:text="" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvItemPrice"
                style="@style/text_14_blue"
                android:text="0.00"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvItemUnit"
                style="@style/text_12_blue"
                android:text="" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>