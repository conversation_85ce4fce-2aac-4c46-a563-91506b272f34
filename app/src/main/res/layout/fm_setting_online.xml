<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_5"
    android:orientation="horizontal"
    android:padding="@dimen/dp10">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="3"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="-10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_black"
                android:layout_width="0dp"
                android:layout_weight="3"
                android:text="@string/print_auto_order" />

            <ImageView
                android:id="@+id/ivAutoOrderPrint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp10"
                android:src="@drawable/selector_checkbox002" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="-10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone">

            <TextView
                style="@style/text_14_black"
                android:layout_width="0dp"
                android:layout_weight="3"
                android:text="@string/print_auto_refund" />

            <ImageView
                android:id="@+id/ivRefundOrderPrint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp10"
                android:src="@drawable/selector_checkbox002" />

        </LinearLayout>

        <!--打印机连接方式-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_black"
                android:layout_width="0dp"
                android:layout_marginEnd="@dimen/dp10"
                android:layout_weight="1"
                android:text="@string/printer_connect_type" />

            <LinearLayout
                android:id="@+id/linReceiptPrint"
                android:layout_width="@dimen/dp140"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp5">

                <TextView
                    android:id="@+id/tvReceiptPrint"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp5"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:hint="@string/select_print_type"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp5" />

                <ImageView
                    android:id="@+id/ivReceiptPrint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

        </LinearLayout>

        <!--打印机状态-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_black"
                android:layout_width="0dp"
                android:layout_marginEnd="@dimen/dp10"
                android:layout_weight="1"
                android:text="@string/printer_status" />

            <View
                android:id="@+id/vReceiptPrinterStatus"
                android:layout_width="@dimen/dp10"
                android:layout_height="@dimen/dp10"
                android:layout_marginEnd="@dimen/dp5"
                android:background="@drawable/shape_yuan_red" />

            <TextView
                android:id="@+id/tvReceiptPrinterStatus"
                style="@style/text_14_red"
                android:text="@string/connect_no" />

        </LinearLayout>

        <!--默认打印数量-->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_black"
                android:layout_width="0dp"
                android:layout_marginEnd="@dimen/dp10"
                android:layout_weight="1"
                android:text="@string/printer_defaule_count" />

            <ImageView
                android:id="@+id/ivReceiptPrintCountSub"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_sub001" />

            <TextView
                android:id="@+id/tvReceiptPrintCount"
                style="@style/text_12_666"
                android:layout_width="@dimen/dp49"
                android:layout_height="@dimen/dp27"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center"
                android:text="0" />

            <ImageView
                android:id="@+id/ivReceiptPrintCountAdd"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="-5dp"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_add001" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_black"
                android:text="@string/tips_warm" />

            <TextView
                android:id="@+id/tvReceiptTipsCount"
                style="@style/text_14_black"
                android:text="(0/50)" />

        </LinearLayout>

        <EditText
            android:id="@+id/etReceiptTips"
            style="@style/text_12_black"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp64"
            android:layout_marginTop="@dimen/dp5"
            android:background="@drawable/shape_f2_5"
            android:gravity="top"
            android:imeOptions="actionDone"
            android:inputType="text"
            android:maxLength="50"
            android:padding="@dimen/dp5" />

        <TextView
            android:id="@+id/tvReceiptPrintTest"
            style="@style/text_14_white"
            android:layout_marginTop="@dimen/dp10"
            android:background="@drawable/shape_orange_5"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/print_test"
            android:textStyle="bold" />

        <TextView
            style="@style/text_12_999"
            android:layout_marginTop="@dimen/dp10"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:text="@string/print_test_tips"
            app:drawableLeftCompat="@mipmap/ic_tips002" />

    </LinearLayout>

    <View
        android:layout_width="0.5dp"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="@dimen/dp10"
        android:background="@color/color_line" />

    <!--小票打印模版-->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="2"
        android:orientation="vertical">

        <TextView
            style="@style/text_14_black"
            android:text="@string/print_template" />

        <ScrollView
            android:layout_width="@dimen/dp181"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dp10"
            android:background="@drawable/shape_d8_kuang_5">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/dp10">

                <TextView
                    style="@style/text_16_black"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/dp5"
                    android:text="@string/star_shopname"
                    android:textStyle="bold" />

                <TextView
                    style="@style/text_10_333"
                    android:layout_gravity="center_horizontal"
                    android:text="@string/order_receipt" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.8dp"
                    android:layout_marginTop="@dimen/dp5"
                    android:background="@drawable/shape_black_dashed_line" />

                <TextView
                    android:id="@+id/tvReceiptPrinter"
                    style="@style/text_10_333"
                    android:layout_marginTop="@dimen/dp5"
                    android:text="" />

                <TextView
                    android:id="@+id/tvReceiptStaff"
                    style="@style/text_10_333"
                    android:layout_marginTop="@dimen/dp2"
                    android:text="" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.8dp"
                    android:layout_marginVertical="@dimen/dp5"
                    android:background="@drawable/shape_black_dashed_line" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_10_333"
                        android:layout_width="0dp"
                        android:layout_weight="2"
                        android:text="@string/goods" />

                    <TextView
                        style="@style/text_10_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/count" />

                    <TextView
                        style="@style/text_10_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/price" />

                    <TextView
                        style="@style/text_10_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/amount" />

                </LinearLayout>

                <TextView
                    style="@style/text_10_333"
                    android:layout_marginTop="@dimen/dp2"
                    android:text="@string/jinquan" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp2"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_10_333"
                        android:layout_width="0dp"
                        android:layout_weight="2"
                        android:text="" />

                    <TextView
                        style="@style/text_10_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="x1" />

                    <TextView
                        style="@style/text_10_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="3.00" />

                    <TextView
                        style="@style/text_10_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="3.00" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.8dp"
                    android:layout_marginVertical="@dimen/dp5"
                    android:background="@drawable/shape_black_dashed_line" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_10_333"
                        android:layout_width="0dp"
                        android:layout_weight="2"
                        android:text="@string/amount" />

                    <TextView
                        style="@style/text_10_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="1" />

                    <TextView
                        style="@style/text_10_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="" />

                    <TextView
                        style="@style/text_10_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="3.00" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.8dp"
                    android:layout_marginVertical="@dimen/dp5"
                    android:background="@drawable/shape_black_dashed_line" />

                <TextView
                    android:id="@+id/tvReceiptPayment"
                    style="@style/text_10_333"
                    android:text="" />

                <TextView
                    android:id="@+id/tvReceiptDiscount"
                    style="@style/text_10_333"
                    android:layout_marginTop="@dimen/dp2"
                    android:text="" />

                <TextView
                    android:id="@+id/tvReceiptActualPayment"
                    style="@style/text_10_333"
                    android:layout_marginTop="@dimen/dp2"
                    android:text="" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.8dp"
                    android:layout_marginVertical="@dimen/dp5"
                    android:background="@drawable/shape_black_dashed_line" />

                <TextView
                    android:id="@+id/tvReceiptBalance"
                    style="@style/text_10_333"
                    android:text="" />

                <TextView
                    android:id="@+id/tvReceiptCash"
                    style="@style/text_10_333"
                    android:layout_marginTop="@dimen/dp2"
                    android:text="" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.8dp"
                    android:layout_marginVertical="@dimen/dp5"
                    android:background="@drawable/shape_black_dashed_line" />

                <TextView
                    android:id="@+id/tvReceiptMember"
                    style="@style/text_10_333"
                    android:text="" />

                <TextView
                    android:id="@+id/tvReceiptPoints"
                    style="@style/text_10_333"
                    android:layout_marginTop="@dimen/dp2"
                    android:text="" />

                <TextView
                    android:id="@+id/tvReceiptMemberPoints"
                    style="@style/text_10_333"
                    android:layout_marginTop="@dimen/dp2"
                    android:text="" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.8dp"
                    android:layout_marginVertical="@dimen/dp5"
                    android:background="@drawable/shape_black_dashed_line" />

                <TextView
                    style="@style/text_10_333"
                    android:text="@string/star_remarks" />

                <TextView
                    style="@style/text_10_333"
                    android:layout_marginTop="@dimen/dp2"
                    android:text="@string/thanks_welcome_again" />

                <TextView
                    android:id="@+id/tvReceiptDate"
                    style="@style/text_10_333"
                    android:layout_marginTop="@dimen/dp2"
                    android:text="" />

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp31"
                    android:layout_marginVertical="@dimen/dp2"
                    android:src="@mipmap/ic_barcode_test" />

                <TextView
                    style="@style/text_10_333"
                    android:text="DII12154878454878777" />

                <TextView
                    android:id="@+id/tvReceiptMobile"
                    style="@style/text_10_333"
                    android:layout_marginTop="@dimen/dp2"
                    android:text="" />

                <TextView
                    android:id="@+id/tvReceiptTips"
                    style="@style/text_10_333"
                    android:layout_marginTop="@dimen/dp2"
                    android:text="@string/tips_warm_colon" />

            </LinearLayout>
        </ScrollView>

    </LinearLayout>

</LinearLayout>