<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="2.5dp">

    <LinearLayout
        android:id="@+id/linItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_5"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/dp5">

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_14_black"
            android:maxLines="1"
            android:text="" />

        <TextView
            android:id="@+id/tvItemBarcode"
            style="@style/text_10_666"
            android:layout_marginTop="@dimen/dp5"
            android:text="" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvItemShofar"
                style="@style/text_10_green"
                android:layout_marginTop="@dimen/dp1"
                android:text="@string/money"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvItemPrice"
                style="@style/text_12_green"
                android:text="0.00"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvItemUnit"
                style="@style/text_12_666"
                android:text="" />

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/tvItemStock"
                style="@style/text_10_black"
                android:background="@drawable/shape_green_tm_left_2"
                android:paddingHorizontal="@dimen/dp5"
                android:text="0" />

            <TextView
                android:id="@+id/tvItemHouse"
                style="@style/text_10_white"
                android:background="@drawable/shape_green_right_2"
                android:paddingHorizontal="@dimen/dp2"
                android:text="@string/house" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvItemCount"
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="@string/sales_month" />

            <ImageView
                android:id="@+id/ivItemCashier"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp6"
                android:src="@drawable/selector_shelve_cashier" />

            <ImageView
                android:id="@+id/ivItemApplet"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/selector_shelve_applet" />

        </LinearLayout>

    </LinearLayout>

    <ImageView
        android:id="@+id/ivItemScale"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/ic_scale002" />

</RelativeLayout>