<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/white">

        <ImageView
            android:id="@+id/ivBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_back002" />

        <TextView
            android:id="@+id/tvTitle"
            style="@style/text_16_black"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/ivBack"
            android:text=""
            android:textStyle="bold" />

        <TextClock
            style="@style/text_12_black"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/dp10"
            android:layout_toStartOf="@id/tvCashier"
            android:format24Hour="MM-dd HH:mm:ss"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvCashier"
            style="@style/text_14_white"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/green"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp20"
            android:text="@string/cashier_table"
            android:textStyle="bold"
            app:drawableLeftCompat="@mipmap/ic_cashier001" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="@dimen/dp5"
        android:layout_marginTop="@dimen/dp46"
        android:layout_marginBottom="@dimen/dp5"
        android:background="@drawable/shape_white_5"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_red"
                    android:text="*" />

                <TextView
                    style="@style/text_14_333"
                    android:text="@string/supplier_name" />

            </LinearLayout>

            <EditText
                android:id="@+id/etName"
                style="@style/text_14_333"
                android:layout_width="0dp"
                android:layout_weight="2"
                android:background="@null"
                android:hint="@string/input_supplier_name"
                android:inputType="text"
                android:maxLength="13"
                android:maxLines="1"
                android:padding="@dimen/dp10" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginHorizontal="@dimen/dp10"
            android:background="@color/color_line" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_red"
                    android:text="*" />

                <TextView
                    style="@style/text_14_333"
                    android:text="@string/contacts" />

            </LinearLayout>

            <EditText
                android:id="@+id/etContact"
                style="@style/text_14_333"
                android:layout_width="0dp"
                android:layout_weight="2"
                android:background="@null"
                android:hint="@string/input_contacts"
                android:inputType="text"
                android:maxLength="13"
                android:maxLines="1"
                android:padding="@dimen/dp10" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginHorizontal="@dimen/dp10"
            android:background="@color/color_line" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_red"
                    android:text="*" />

                <TextView
                    style="@style/text_14_333"
                    android:text="@string/contact_mobile" />

            </LinearLayout>

            <EditText
                android:id="@+id/etMobile"
                style="@style/text_14_333"
                android:layout_width="0dp"
                android:layout_weight="2"
                android:background="@null"
                android:hint="@string/input_contact_moblie"
                android:inputType="phone"
                android:maxLength="11"
                android:maxLines="1"
                android:padding="@dimen/dp10" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginHorizontal="@dimen/dp10"
            android:background="@color/color_line" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_333"
                android:layout_width="0dp"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:text="@string/address_where" />

            <EditText
                android:id="@+id/etAddress"
                style="@style/text_14_333"
                android:layout_width="0dp"
                android:layout_weight="2"
                android:background="@null"
                android:hint="@string/input_address_where"
                android:inputType="text"
                android:maxLength="16"
                android:maxLines="1"
                android:padding="@dimen/dp10" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginHorizontal="@dimen/dp10"
            android:background="@color/color_line" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_red"
                    android:text="*" />

                <TextView
                    style="@style/text_14_333"
                    android:text="@string/cate_affiliation" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvCate"
                style="@style/text_14_333"
                android:layout_width="0dp"
                android:layout_weight="2"
                android:gravity="center_vertical"
                android:hint="@string/select_cate"
                android:padding="@dimen/dp10"
                app:drawableRightCompat="@mipmap/ic_more001" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginHorizontal="@dimen/dp10"
            android:background="@color/color_line" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_333"
                android:layout_width="0dp"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:text="@string/enable_status" />

            <ImageView
                android:id="@+id/ivEnable"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp10"
                android:src="@drawable/selector_checkbox002" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginHorizontal="@dimen/dp10"
            android:background="@color/color_line" />

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tvConfirm"
            style="@style/text_14_white"
            android:layout_width="match_parent"
            android:layout_margin="@dimen/dp10"
            android:background="@drawable/shape_green_5"
            android:gravity="center"
            android:paddingVertical="@dimen/dp10"
            android:text="@string/save"
            android:textStyle="bold" />

    </LinearLayout>

</RelativeLayout>