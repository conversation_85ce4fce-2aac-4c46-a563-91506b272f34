<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp10"
            android:background="@drawable/shape_f2_22"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp1">

            <TextView
                android:id="@+id/tvCash"
                style="@style/text_16_white"
                android:background="@drawable/shape_green_22"
                android:paddingHorizontal="@dimen/dp15"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/cash"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvCombination"
                style="@style/text_16_black"
                android:paddingHorizontal="@dimen/dp15"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/combination"
                android:textStyle="bold" />

        </LinearLayout>

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <!--现金收款-->
    <LinearLayout
        android:id="@+id/linCash"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp20"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_left_5"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:paddingVertical="@dimen/dp10">

                <TextView
                    android:id="@+id/tvDialogTotalName"
                    style="@style/text_12_666"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp5"
                    android:text="@string/receivable" />

                <TextView
                    android:id="@+id/tvDialogCashTotal"
                    style="@style/text_18_black"
                    android:layout_marginTop="@dimen/dp8"
                    android:text="0.00"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvDialogTotalOld"
                    style="@style/text_14_red"
                    android:layout_marginTop="@dimen/dp3"
                    android:text="0.00" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/shape_green_kuang"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:paddingVertical="@dimen/dp10">

                <TextView
                    style="@style/text_12_666"
                    android:gravity="center"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dp5"
                    android:text="@string/collection" />

                <TextView
                    android:id="@+id/tvDialogMoney"
                    style="@style/text_18_white"
                    android:layout_marginTop="@dimen/dp8"
                    android:background="@color/green"
                    android:text="0.00"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:paddingVertical="@dimen/dp10">

                <TextView
                    style="@style/text_12_666"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp5"
                    android:text="@string/give_change" />

                <TextView
                    android:id="@+id/tvDialogZero"
                    style="@style/text_18_blue"
                    android:layout_marginTop="@dimen/dp8"
                    android:text="0.00"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_right_5"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:paddingVertical="@dimen/dp10">

                <TextView
                    style="@style/text_12_666"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp5"
                    android:text="@string/zero_discount" />

                <TextView
                    android:id="@+id/tvDialogDiscount"
                    style="@style/text_18_red"
                    android:layout_marginTop="@dimen/dp8"
                    android:text="0.00"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="17.5dp"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvDialogZero0"
                style="@style/text_14_red"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="2.5dp"
                android:layout_weight="1"
                android:background="@drawable/shape_red_kuang_5"
                android:gravity="center"
                android:padding="@dimen/dp5"
                android:text="@string/zero_1_yuan" />

            <TextView
                android:id="@+id/tvDialogZero1"
                style="@style/text_14_red"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="2.5dp"
                android:layout_weight="1"
                android:background="@drawable/shape_red_kuang_5"
                android:gravity="center"
                android:padding="@dimen/dp5"
                android:text="@string/zero_5_corner" />

            <TextView
                android:id="@+id/tvDialogZero2"
                style="@style/text_14_red"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="2.5dp"
                android:layout_weight="1"
                android:background="@drawable/shape_red_kuang_5"
                android:gravity="center"
                android:padding="@dimen/dp5"
                android:text="@string/zero_1_corner" />

            <TextView
                android:id="@+id/tvDialogStore0"
                style="@style/text_14_white"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="2.5dp"
                android:layout_weight="1"
                android:background="@drawable/shape_green_5"
                android:gravity="center"
                android:padding="@dimen/dp5"
                android:text="@string/zero_store_all" />

            <TextView
                android:id="@+id/tvDialogStore1"
                style="@style/text_14_white"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="2.5dp"
                android:layout_weight="1"
                android:background="@drawable/shape_green_5"
                android:gravity="center"
                android:padding="@dimen/dp5"
                android:text="@string/zero_store_one" />

        </LinearLayout>

    </LinearLayout>

    <!--组合收款-->
    <LinearLayout
        android:id="@+id/linCombination"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp56"
            android:gravity="center"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/linDialogCash"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp5"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:drawablePadding="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:text="@string/cash"
                    app:drawableLeftCompat="@mipmap/ic_payment_img011"
                    tools:ignore="NestedWeights" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical|end"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvDialogCash"
                        style="@style/text_12_black"
                        android:background="@drawable/shape_green_5"
                        android:text="" />

                    <ImageView
                        android:id="@+id/ivDialogCursorCash"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp15"
                        android:src="@drawable/anim_cursor"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tvDialogCashHint"
                        style="@style/text_12_black"
                        android:hint="@string/input_money" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linDialogWechat"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:drawablePadding="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:text="@string/wechat"
                    app:drawableLeftCompat="@mipmap/ic_payment_img013"
                    tools:ignore="NestedWeights" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical|end"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvDialogWechat"
                        style="@style/text_12_black"
                        android:text="" />

                    <ImageView
                        android:id="@+id/ivDialogCursorWechat"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp15"
                        android:src="@drawable/anim_cursor"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tvDialogWechatHint"
                        style="@style/text_12_black"
                        android:hint="@string/input_money" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp56"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/linDialogAlipay"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp5"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:drawablePadding="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:text="@string/alipay"
                    app:drawableLeftCompat="@mipmap/ic_payment_img012"
                    tools:ignore="NestedWeights" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical|end"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvDialogAlipay"
                        style="@style/text_12_black"
                        android:text="" />

                    <ImageView
                        android:id="@+id/ivDialogCursorAlipay"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp15"
                        android:src="@drawable/anim_cursor"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tvDialogAlipayHint"
                        style="@style/text_12_black"
                        android:hint="@string/input_money" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linDialogBank"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:drawablePadding="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:text="@string/bank_card"
                    app:drawableLeftCompat="@mipmap/ic_payment_img014"
                    tools:ignore="NestedWeights" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical|end"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvDialogBank"
                        style="@style/text_12_black"
                        android:text="" />

                    <ImageView
                        android:id="@+id/ivDialogCursorBank"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp15"
                        android:src="@drawable/anim_cursor"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tvDialogBankHint"
                        style="@style/text_12_black"
                        android:hint="@string/input_money" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp56"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/linDialogMember"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp5"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_f0_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <ImageView
                    android:id="@+id/ivDialogMemberHead"
                    android:layout_width="@dimen/dp20"
                    android:layout_height="@dimen/dp20"
                    android:layout_marginEnd="@dimen/dp5"
                    android:src="@mipmap/ic_payment_img014" />

                <TextView
                    android:id="@+id/tvDialogMemberName"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:text="@string/stored_card"
                    tools:ignore="NestedWeights" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical|end"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvDialogMemberSelect"
                        style="@style/text_12_red"
                        android:text="@string/click_select_member" />

                    <TextView
                        android:id="@+id/tvDialogMember"
                        style="@style/text_12_black"
                        android:text="" />

                    <ImageView
                        android:id="@+id/ivDialogCursorMember"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp15"
                        android:src="@drawable/anim_cursor"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tvDialogMemberHint"
                        style="@style/text_12_black"
                        android:hint="@string/balance"
                        android:visibility="gone" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_vertical|right"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:maxWidth="@dimen/dp100"
                    android:text="@string/receivable_colon" />

                <TextView
                    android:id="@+id/tvDialogCombinationTotal"
                    style="@style/text_16_black"
                    android:text="0.00"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <com.yxl.cashier_retail.view.NumberKeyBoardView
        android:id="@+id/numberKeyBoardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp51"
        android:layout_marginVertical="@dimen/dp5" />

</LinearLayout>