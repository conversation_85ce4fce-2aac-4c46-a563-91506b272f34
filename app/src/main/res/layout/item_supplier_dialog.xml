<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_14_333"
            android:layout_marginStart="@dimen/dp10"
            android:layout_marginEnd="@dimen/dp4"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingVertical="@dimen/dp10"
            android:text=""
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivItemImg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/ic_supplier_img001"
            android:visibility="gone" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/color_line" />

</LinearLayout>