<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvDialogTitle"
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text=""
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:background="@drawable/shape_d8_kuang_5"
        android:orientation="vertical">

        <EditText
            android:id="@+id/etDialog"
            style="@style/text_12_black"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp200"
            android:background="@null"
            android:gravity="top"
            android:hint="@string/input_refuse_reason"
            android:inputType="text"
            android:maxLength="200"
            android:padding="@dimen/dp10" />

        <TextView
            android:id="@+id/tvDialogCount"
            style="@style/text_10_999"
            android:layout_gravity="end"
            android:layout_margin="@dimen/dp5"
            android:text="0/200" />

    </LinearLayout>

    <TextView
        android:id="@+id/tvDialogConfirm"
        style="@style/text_14_white"
        android:layout_width="@dimen/dp200"
        android:layout_gravity="center"
        android:layout_marginVertical="@dimen/dp10"
        android:background="@drawable/shape_green_5"
        android:gravity="center"
        android:paddingVertical="@dimen/dp10"
        android:text="@string/save" />

</LinearLayout>