<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_f2"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp16">

            <LinearLayout
                android:id="@+id/linPrinter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp12"
                android:background="@drawable/shape_white_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:paddingStart="@dimen/dp10">

                    <TextView
                        android:id="@+id/tvPrinterTitle"
                        style="@style/text_16_999"
                        android:text="设备未连接"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvPrinterName"
                        style="@style/text_12_999"
                        android:layout_marginTop="@dimen/dp6"
                        android:text="请连接蓝牙打印机" />

                </LinearLayout>

                <ImageView
                    android:id="@+id/ivPrinterImg"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_print001" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="@dimen/dp12"
                android:background="@drawable/shape_white_5"
                android:orientation="vertical">

                <TextView
                    style="@style/text_16_333"
                    android:layout_width="match_parent"
                    android:layout_marginTop="@dimen/dp4"
                    android:layout_marginBottom="@dimen/dp2"
                    android:padding="@dimen/dp10"
                    android:text="点击蓝牙地址连接" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp1"
                    android:background="@color/color_f2" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dp12"
                android:background="@drawable/shape_white_5"
                android:orientation="vertical">

                <TextView
                    style="@style/text_16_333"
                    android:layout_marginLeft="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp14"
                    android:layout_marginBottom="@dimen/dp2"
                    android:text="选择标签纸样式(70mm*38mm)" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center|left"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_14_333"
                        android:layout_marginLeft="@dimen/dp10"
                        android:text="标签方向：" />

                    <LinearLayout
                        android:id="@+id/linDirection0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp10">

                        <ImageView
                            android:id="@+id/ivDirection0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="@dimen/dp6"
                            android:src="@mipmap/ic_chosen001" />

                        <TextView
                            style="@style/text_14_333"
                            android:text="正向" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/linDirection1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp10">

                        <ImageView
                            android:id="@+id/ivDirection1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="@dimen/dp6"
                            android:src="@mipmap/ic_chose001" />

                        <TextView
                            style="@style/text_14_333"
                            android:text="反向" />

                    </LinearLayout>

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp1"
                    android:background="@color/color_f2" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp16">

                    <LinearLayout
                        android:id="@+id/linTag0"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="@dimen/dp12"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="match_parent"
                            android:layout_height="100dp"
                            android:src="@mipmap/ic_print_tags001" />

                        <CheckBox
                            android:id="@+id/cbTag0"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:button="@drawable/selector_checkbox003"
                            android:checked="false"
                            android:clickable="false" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/linTag1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="10dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="match_parent"
                            android:layout_height="100dp"
                            android:src="@mipmap/ic_print_tags001" />

                        <CheckBox
                            android:id="@+id/cbTag1"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:button="@drawable/selector_checkbox003"
                            android:checked="false"
                            android:clickable="false" />
                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/tvConfirm"
        style="@style/text_16_white"
        android:layout_width="match_parent"
        android:layout_margin="@dimen/dp15"
        android:background="@drawable/shape_blue_5"
        android:gravity="center"
        android:padding="@dimen/dp12"
        android:text="保存设置并打印"
        android:visibility="gone" />

</LinearLayout>