<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginTop="@dimen/dp16"
            android:layout_marginBottom="@dimen/dp6"
            android:text="@string/goods_out"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp56">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_14_red"
                        android:text="*" />

                    <TextView
                        style="@style/text_14_black"
                        android:layout_marginEnd="@dimen/dp10"
                        android:text="@string/out_reason" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvDialogReason"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="-5dp"
                    android:layout_marginEnd="-5dp"
                    android:layout_weight="3"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:spanCount="4" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_14_red"
                        android:text="*" />

                    <TextView
                        style="@style/text_14_black"
                        android:text="@string/out_count" />

                </LinearLayout>

                <EditText
                    android:id="@+id/etDialogCount"
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_weight="3"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:hint="@string/input_out_count"
                    android:inputType="number"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_14_red"
                        android:text="*" />

                    <TextView
                        style="@style/text_14_black"
                        android:text="@string/out_price" />

                </LinearLayout>

                <EditText
                    android:id="@+id/etDialogPrice"
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_weight="3"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:hint="@string/input_out_price"
                    android:inputType="numberDecimal"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:text="@string/out_total" />

                <TextView
                    android:id="@+id/tvDialogTotal"
                    style="@style/text_14_green"
                    android:layout_width="0dp"
                    android:layout_weight="3"
                    android:text="@string/no_count" />

            </LinearLayout>

            <!--出库批次-->
            <LinearLayout
                android:id="@+id/linDialogBatch"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    style="@style/text_14_black"
                    android:layout_marginTop="@dimen/dp10"
                    android:text="@string/out_batch" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:background="@drawable/shape_f5_top_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5">

                    <TextView
                        style="@style/text_10_666"
                        android:layout_width="0dp"
                        android:layout_weight="2"
                        android:text="@string/batch_no" />

                    <TextView
                        style="@style/text_10_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/in_time" />

                    <TextView
                        style="@style/text_10_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/date_become" />

                    <TextView
                        style="@style/text_10_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/in_price" />

                    <TextView
                        style="@style/text_10_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/count_left" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="2" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvDialogBatch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:text="@string/remarks_info" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/etDialogRemarks"
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:background="@null"
                        android:hint="@string/input_remarks_info"
                        android:inputType="text"
                        android:maxLength="50"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        tools:ignore="NestedWeights" />

                    <ImageView
                        android:id="@+id/ivDialogRemarksClear"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dp5"
                        android:src="@mipmap/ic_close002"
                        android:visibility="gone" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:visibility="gone">

                <TextView
                    style="@style/text_14_black"
                    android:text="@string/remarks_img" />

                <TextView
                    android:id="@+id/tvDialogImgCount"
                    style="@style/text_14_999"
                    android:text="(0/3)" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvDialogImg"
                android:layout_width="@dimen/dp300"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="3" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/tvDialogConfirm"
        style="@style/text_14_white"
        android:layout_width="@dimen/dp200"
        android:layout_gravity="center"
        android:layout_marginVertical="@dimen/dp10"
        android:background="@drawable/shape_green_5"
        android:gravity="center"
        android:paddingVertical="@dimen/dp5"
        android:text="@string/confirm" />

</LinearLayout>