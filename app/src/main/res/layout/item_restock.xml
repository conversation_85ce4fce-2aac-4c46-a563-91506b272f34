<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linItem"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/dp5"
    android:orientation="vertical"
    android:padding="@dimen/dp10">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_14_black"
            android:layout_marginEnd="@dimen/dp6"
            android:text=""
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvItemStatus"
            style="@style/text_10_white"
            android:background="@drawable/shape_green_5"
            android:paddingHorizontal="@dimen/dp5"
            android:paddingVertical="@dimen/dp1"
            android:text="" />

    </LinearLayout>

    <TextView
        android:id="@+id/tvItemTime"
        style="@style/text_12_666"
        android:layout_marginTop="@dimen/dp6"
        android:text="@string/time_create_colon" />

</LinearLayout>