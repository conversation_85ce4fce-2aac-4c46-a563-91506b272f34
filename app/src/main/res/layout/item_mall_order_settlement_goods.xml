<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dp10">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10">

        <ImageView
            android:id="@+id/ivItemImg"
            android:layout_width="@dimen/dp72"
            android:layout_height="@dimen/dp72"
            android:layout_marginEnd="@dimen/dp10"
            android:src="@mipmap/ic_default_img" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp72"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/ivItemImg"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvItemName"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp40"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:text="" />

                <TextView
                    style="@style/text_12_666"
                    android:gravity="center_horizontal"
                    android:text="起订量：" />

                <TextView
                    android:id="@+id/tvItemStartOrder"
                    style="@style/text_12_black"
                    android:text="0" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_marginTop="@dimen/dp4"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvItemPrice"
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="￥0.00" />

                <TextView
                    android:id="@+id/tvItemCount"
                    style="@style/text_12_red"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="x0" />

                <TextView
                    android:id="@+id/tvItemTotal"
                    style="@style/text_14_green"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="end"
                    android:text="￥0.00"
                    android:textStyle="bold" />

            </LinearLayout>

        </RelativeLayout>

    </RelativeLayout>

    <!--捆绑-->
    <LinearLayout
        android:id="@+id/linItemBind"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/shape_f2_5"
        android:orientation="vertical"
        android:padding="10dp"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_8_red"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:background="@drawable/shape_red_kuang_5"
                android:padding="@dimen/dp3"
                android:text="捆绑" />

            <TextView
                style="@style/text_10_black"
                android:text="捆绑销售商品" />

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvItemBind"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    </LinearLayout>

    <!--满赠-->
    <LinearLayout
        android:id="@+id/linItemGift"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/shape_f2_5"
        android:orientation="vertical"
        android:padding="10dp"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_8_red"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:background="@drawable/shape_red_kuang_5"
                android:padding="@dimen/dp3"
                android:text="满赠" />

            <TextView
                android:id="@+id/tvItemGift"
                style="@style/text_10_black"
                android:text="" />

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvItemGift"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginHorizontal="@dimen/dp10"
        android:layout_marginTop="10dp"
        android:background="@color/color_line" />

</LinearLayout>