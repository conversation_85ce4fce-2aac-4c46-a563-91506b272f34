<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/supplier_settlement"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp10"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_12_black"
            android:text="@string/common"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvDialogCount"
            style="@style/text_12_black"
            android:text="0"
            android:textStyle="bold" />

        <TextView
            style="@style/text_12_black"
            android:text="@string/batch_debt_total"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvDialogTotal"
            style="@style/text_12_red"
            android:text="0.00"
            android:textStyle="bold" />

        <TextView
            style="@style/text_12_black"
            android:text="@string/yuan"
            android:textStyle="bold" />

    </LinearLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/smartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <com.scwang.smart.refresh.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingHorizontal="7.5dp"
                android:paddingVertical="2.5dp"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            <LinearLayout
                android:id="@+id/linEmpty"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="@dimen/dp40"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/ivEmpty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_empty" />

                <TextView
                    android:id="@+id/tvEmpty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp26"
                    android:textColor="@color/color_666"
                    android:textSize="@dimen/f14"
                    android:textStyle="bold" />

            </LinearLayout>

        </RelativeLayout>

        <com.scwang.smart.refresh.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <LinearLayout
        android:id="@+id/linDialogTotal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/green"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp5"
        android:visibility="gone">

        <TextView
            style="@style/text_12_white"
            android:text="@string/selected" />

        <TextView
            android:id="@+id/tvDialogCount1"
            style="@style/text_12_white"
            android:text="0" />

        <TextView
            style="@style/text_12_white"
            android:text="@string/order_debt_total" />

        <TextView
            android:id="@+id/tvDialogTotal1"
            style="@style/text_12_white"
            android:text="0.00" />

        <TextView
            style="@style/text_12_white"
            android:text="@string/yuan" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/color_line" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingVertical="@dimen/dp5">

        <ImageView
            android:id="@+id/ivDialogAll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@drawable/selector_checkbox003" />

        <TextView
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/all_select" />

        <TextView
            android:id="@+id/tvDialogConfirm"
            style="@style/text_14_white"
            android:layout_marginEnd="@dimen/dp10"
            android:background="@drawable/shape_green_5"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/confirm_settlement"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>