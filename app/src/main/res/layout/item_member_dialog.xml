<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp16"
        android:paddingVertical="@dimen/dp8">

        <TextView
            android:id="@+id/tvItemNo"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="" />

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="" />

        <TextView
            android:id="@+id/tvItemMobile"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="right"
            android:maxLines="1"
            android:text=""
            tools:ignore="RtlHardcoded" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/color_line" />

</LinearLayout>