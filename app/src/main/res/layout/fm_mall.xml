<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_5"
        android:gravity="center_vertical|right"
        android:orientation="horizontal">

        <TextView
            style="@style/text_12_black"
            android:layout_marginEnd="@dimen/dp20"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:text="0.00"
            app:drawableLeftCompat="@mipmap/ic_payment_img011" />

        <TextView
            style="@style/text_12_black"
            android:layout_marginEnd="@dimen/dp10"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:text="0.00"
            app:drawableLeftCompat="@mipmap/ic_payment_img011" />

        <TextView
            android:id="@+id/tvCredit"
            style="@style/text_12_black"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:padding="@dimen/dp10"
            android:text="赊销"
            app:drawableLeftCompat="@mipmap/ic_payment_img011" />

        <TextView
            android:id="@+id/tvOrder"
            style="@style/text_12_black"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:padding="@dimen/dp10"
            android:text="订单"
            app:drawableLeftCompat="@mipmap/ic_payment_img011" />

        <RelativeLayout
            android:id="@+id/relCart"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <TextView
                style="@style/text_12_black"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:padding="@dimen/dp10"
                android:text="购物车"
                app:drawableLeftCompat="@mipmap/ic_payment_img011" />

            <TextView
                android:id="@+id/tvCartCount"
                style="@style/text_8_white"
                android:layout_width="@dimen/dp10"
                android:layout_height="@dimen/dp10"
                android:layout_marginStart="@dimen/dp10"
                android:layout_marginTop="@dimen/dp5"
                android:background="@drawable/shape_yuan_red"
                android:gravity="center"
                android:text="0" />

        </RelativeLayout>

        <TextView
            android:id="@+id/tvCoupons"
            style="@style/text_12_black"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:padding="@dimen/dp10"
            android:text="优惠券"
            app:drawableLeftCompat="@mipmap/ic_payment_img011" />

        <TextView
            style="@style/text_12_green"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:padding="@dimen/dp10"
            android:text="助农产品"
            app:drawableLeftCompat="@mipmap/ic_payment_img011" />

    </LinearLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/smartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dp5"
        android:background="@drawable/shape_f2_5">

        <com.scwang.smart.refresh.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!--分类-->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvCate"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:orientation="horizontal"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                <!--品牌精选-->
                <LinearLayout
                    android:id="@+id/linBrand"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp10"
                    android:background="@drawable/shape_jb_e67d36_5"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp10"
                        android:layout_marginTop="@dimen/dp10"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_white"
                            android:text="品牌精选" />

                        <View
                            android:layout_width="0.5dp"
                            android:layout_height="@dimen/dp12"
                            android:layout_marginHorizontal="@dimen/dp6"
                            android:background="@color/white" />

                        <TextView
                            style="@style/text_10_white"
                            android:text="优质商家任你挑选" />

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvBrand"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp5"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                </LinearLayout>

                <!--硬核补贴-->
                <LinearLayout
                    android:id="@+id/linSubsidy"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/dp10"
                    android:layout_marginTop="@dimen/dp10"
                    android:background="@drawable/shape_white_5"
                    android:orientation="vertical"
                    android:paddingBottom="@dimen/dp12">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/dp10"
                        android:layout_marginTop="@dimen/dp10"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_black"
                            android:text="硬核补贴" />

                        <View
                            android:layout_width="0.5dp"
                            android:layout_height="@dimen/dp12"
                            android:layout_marginHorizontal="@dimen/dp6"
                            android:background="@color/black" />

                        <TextView
                            style="@style/text_10_black"
                            android:text="优惠好货大爆料" />

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvSubsidy"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp5"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                </LinearLayout>

                <!--标签-->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvLabel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp5"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                <!--商品-->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvGoods"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:spanCount="4" />

                <LinearLayout
                    android:id="@+id/linEmpty"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingTop="@dimen/dp40"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/ivEmpty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_empty" />

                    <TextView
                        android:id="@+id/tvEmpty"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp26"
                        android:textColor="@color/color_666"
                        android:textSize="@dimen/f14"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <com.scwang.smart.refresh.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</LinearLayout>