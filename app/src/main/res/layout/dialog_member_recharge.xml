<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.yxl.cashier_retail.view.ScannerEditText
            android:id="@+id/etDialogScan"
            android:layout_width="1dp"
            android:layout_height="1dp" />

        <TextView
            android:id="@+id/tvDialogTitle"
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/member_recharge"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:background="@drawable/shape_green_kuang_5"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp10">

        <TextView
            android:id="@+id/tvDialogName"
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_marginVertical="@dimen/dp5"
            android:layout_weight="1"
            android:text="@string/recharge_money" />

        <TextView
            android:id="@+id/tvDialogMoney"
            style="@style/text_16_black"
            android:text=""
            android:textStyle="bold"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/ivDialogCursor"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp18"
            android:layout_gravity="center_vertical"
            android:src="@drawable/anim_cursor" />

        <TextView
            android:id="@+id/tvDialogHints"
            style="@style/text_14_black"
            android:hint="@string/input_recharge_money" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/linDialogJinQ"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="@dimen/dp5">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/ivDialogJinQ"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5"
                    android:src="@mipmap/ic_recharge00" />

                <TextView
                    android:id="@+id/tvDialogJinQ"
                    style="@style/text_14_black"
                    android:text="@string/jinquan_collection" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvDialogJinQHint"
                style="@style/text_12_999"
                android:layout_marginTop="@dimen/dp5"
                android:gravity="center"
                android:text="@string/jinquan_collection_tips" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linDialogCash"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:background="@drawable/shape_green_5"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="@dimen/dp5">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/ivDialogCash"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5"
                    android:src="@mipmap/ic_recharge11" />

                <TextView
                    android:id="@+id/tvDialogCash"
                    style="@style/text_14_white"
                    android:text="@string/cash" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvDialogCashHint"
                style="@style/text_12_white"
                android:layout_marginTop="@dimen/dp5"
                android:gravity="center"
                android:text="@string/offline1" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/linDialogWechat"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="@dimen/dp5">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/ivDialogWechat"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5"
                    android:src="@mipmap/ic_recharge20" />

                <TextView
                    android:id="@+id/tvDialogWechat"
                    style="@style/text_14_black"
                    android:text="@string/wechat" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvDialogWechatHint"
                style="@style/text_12_999"
                android:layout_marginTop="@dimen/dp5"
                android:gravity="center"
                android:text="@string/offline1" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linDialogAlipay"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="@dimen/dp5">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/ivDialogAlipay"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5"
                    android:src="@mipmap/ic_recharge30" />

                <TextView
                    android:id="@+id/tvDialogAlipay"
                    style="@style/text_14_black"
                    android:text="@string/alipay" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvDialogAlipayHint"
                style="@style/text_12_999"
                android:layout_marginTop="@dimen/dp5"
                android:gravity="center"
                android:text="@string/offline1" />

        </LinearLayout>

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvDialogConfig"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/shape_f2_5"
        android:padding="@dimen/dp5"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:spanCount="2" />

    <com.yxl.cashier_retail.view.NumberKeyBoardView
        android:id="@+id/numberKeyBoardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp51"
        android:layout_marginVertical="@dimen/dp5" />

</LinearLayout>