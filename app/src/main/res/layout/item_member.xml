<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linItem"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="2.5dp"
    android:background="@drawable/shape_white_5"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="@dimen/dp5">

    <TextView
        android:id="@+id/tvItemUnique"
        style="@style/text_12_999"
        android:gravity="center"
        android:text="" />

    <TextView
        android:id="@+id/tvItemMobile"
        style="@style/text_14_black"
        android:layout_marginTop="@dimen/dp5"
        android:gravity="center"
        android:text=""
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/ivItemLevel"
            android:layout_width="@dimen/dp10"
            android:layout_height="@dimen/dp11"
            android:layout_marginEnd="@dimen/dp5" />

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_12_333"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="" />

    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp5">

        <ImageView
            android:id="@+id/ivItemBalance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:src="@mipmap/ic_balance001" />

        <TextView
            android:id="@+id/tvItemBalance"
            style="@style/text_12_orange"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/ivItemBalance"
            android:text="0.00" />

        <ImageView
            android:id="@+id/ivItemPoints"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toStartOf="@+id/tvItemPoints"
            android:src="@mipmap/ic_points001" />

        <TextView
            android:id="@+id/tvItemPoints"
            style="@style/text_12_green"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="0.00" />

    </RelativeLayout>

</LinearLayout>