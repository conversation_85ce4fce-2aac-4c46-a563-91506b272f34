<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_5"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp10">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:src="@mipmap/ic_beans001" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    style="@style/text_10_666"
                    android:text="@string/beans_left" />

                <TextView
                    android:id="@+id/tvCount"
                    style="@style/text_14_black"
                    android:layout_marginTop="@dimen/dp6"
                    android:text="0" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <TextView
                style="@style/text_10_666"
                android:text="@string/order_count_total" />

            <TextView
                android:id="@+id/tvListCount"
                style="@style/text_14_black"
                android:layout_marginTop="@dimen/dp6"
                android:text="0" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <TextView
                style="@style/text_10_666"
                android:text="@string/order_total_total" />

            <TextView
                android:id="@+id/tvListTotal"
                style="@style/text_14_black"
                android:layout_marginTop="@dimen/dp6"
                android:text="0" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <TextView
                style="@style/text_10_666"
                android:text="@string/subsidy_total" />

            <TextView
                android:id="@+id/tvPlatformCount"
                style="@style/text_14_black"
                android:layout_marginTop="@dimen/dp6"
                android:text="0" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <TextView
                style="@style/text_10_666"
                android:text="@string/deduct_total" />

            <TextView
                android:id="@+id/tvUseCount"
                style="@style/text_14_black"
                android:layout_marginTop="@dimen/dp6"
                android:text="0" />

        </LinearLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tvRule"
            style="@style/text_14_white"
            android:layout_marginEnd="@dimen/dp10"
            android:background="@drawable/shape_green_5"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/rule_setting"
            android:visibility="gone" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/color_line" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/linTab0"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <TextView
                android:id="@+id/tvTab0"
                style="@style/text_14_green"
                android:text="@string/trading_record"
                android:textStyle="bold" />

            <View
                android:id="@+id/vTab0"
                android:layout_width="@dimen/dp24"
                android:layout_height="@dimen/dp2"
                android:layout_marginTop="@dimen/dp5"
                android:background="@drawable/shape_green_2" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linTab1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <TextView
                android:id="@+id/tvTab1"
                style="@style/text_14_black"
                android:text="@string/withdrawal_record" />

            <View
                android:id="@+id/vTab1"
                android:layout_width="@dimen/dp24"
                android:layout_height="@dimen/dp2"
                android:layout_marginTop="@dimen/dp5"
                android:background="@drawable/shape_green_2"
                android:visibility="invisible" />

        </LinearLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <LinearLayout
            android:id="@+id/linStartDate"
            android:layout_width="@dimen/dp125"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvStartDate"
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:ellipsize="end"
                android:hint="@string/date_start"
                android:maxLines="1"
                android:paddingVertical="@dimen/dp6"
                android:text=""
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivStartDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_arrow002" />

        </LinearLayout>

        <TextView
            style="@style/text_16_999"
            android:layout_marginHorizontal="@dimen/dp3"
            android:text="~" />

        <LinearLayout
            android:id="@+id/linEndDate"
            android:layout_width="@dimen/dp125"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/dp10"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvEndDate"
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:ellipsize="end"
                android:hint="@string/date_end"
                android:maxLines="1"
                android:paddingVertical="@dimen/dp6"
                android:text=""
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivEndDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_arrow002" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvReset"
            style="@style/text_12_green"
            android:layout_marginEnd="@dimen/dp10"
            android:background="@drawable/shape_green_kuang_5"
            android:drawablePadding="@dimen/dp5"
            android:padding="@dimen/dp6"
            android:text="@string/reset"
            app:drawableStartCompat="@mipmap/ic_reset001" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp10"
        android:background="@drawable/shape_f5_top_5"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp5">

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/order_no" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/member_name" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/deduct_count" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/trading_time" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/status_trading" />

    </LinearLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/smartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="@dimen/dp10">

        <com.scwang.smart.refresh.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            <LinearLayout
                android:id="@+id/linEmpty"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="@dimen/dp40"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/ivEmpty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_empty" />

                <TextView
                    android:id="@+id/tvEmpty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp26"
                    android:textColor="@color/color_666"
                    android:textSize="@dimen/f14"
                    android:textStyle="bold" />

            </LinearLayout>

        </RelativeLayout>

        <com.scwang.smart.refresh.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</LinearLayout>