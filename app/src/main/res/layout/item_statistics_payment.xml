<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/dp5"
    android:background="@drawable/shape_white_5"
    android:orientation="vertical"
    android:padding="@dimen/dp10">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvItemTime"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:text="" />

        <TextView
            android:id="@+id/tvItemMoney"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:text="￥0.00" />

        <TextView
            android:id="@+id/tvItemMoney1"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:text="￥0.00" />

        <TextView
            android:id="@+id/tvItemPayTime"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:text="" />

        <TextView
            android:id="@+id/tvItemTotal"
            style="@style/text_12_orange"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:text="￥0.00" />

        <TextView
            android:id="@+id/tvItemStatus"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="end"
            android:text="" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:text="日期" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:text="金圈收银打款" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:text="小程序收银打款" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:text="打款日期" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="2"
            android:text="预计打款总额" />

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="end"
            android:text="打款状态" />

    </LinearLayout>

</LinearLayout>