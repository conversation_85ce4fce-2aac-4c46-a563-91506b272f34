<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingVertical="2.5dp">

    <ImageView
        android:id="@+id/ivItemIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp5" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_14_333"
            android:text="" />

        <TextView
            android:id="@+id/tvItemTips"
            style="@style/text_12_999"
            android:text="" />

    </LinearLayout>

    <ImageView
        android:id="@+id/ivItemImg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp10"
        android:src="@drawable/selector_checkbox002" />

</LinearLayout>