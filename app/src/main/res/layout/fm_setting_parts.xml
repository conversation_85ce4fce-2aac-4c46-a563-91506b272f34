<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_f5_top_5"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvReceipt"
            style="@style/text_14_black"
            android:layout_height="@dimen/dp34"
            android:background="@drawable/shape_white_topleft_5"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp16"
            android:text="@string/setting_receipt"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvTags"
            style="@style/text_14_333"
            android:layout_height="@dimen/dp34"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp16"
            android:text="@string/setting_tags"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvWeight"
            style="@style/text_14_333"
            android:layout_height="@dimen/dp34"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp16"
            android:text="@string/setting_weight" />

        <TextView
            android:id="@+id/tvCamera"
            style="@style/text_14_333"
            android:layout_height="@dimen/dp34"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp16"
            android:text="@string/setting_camera"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvPos"
            style="@style/text_14_333"
            android:layout_height="@dimen/dp34"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp16"
            android:text="@string/setting_pos"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvOther"
            style="@style/text_14_333"
            android:layout_height="@dimen/dp34"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp16"
            android:text="@string/setting_other" />

    </LinearLayout>

    <!--小票设置-->
    <LinearLayout
        android:id="@+id/linReceipt"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_white_bottom_5"
        android:orientation="horizontal"
        android:padding="@dimen/dp10"
        android:visibility="visible">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:orientation="vertical">

            <!--打印机连接方式-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/printer_connect_type" />

                <LinearLayout
                    android:id="@+id/linReceiptPrint"
                    android:layout_width="@dimen/dp140"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp5">

                    <TextView
                        android:id="@+id/tvReceiptPrint"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp5"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:hint="@string/select_print_type"
                        android:maxLines="1"
                        android:paddingVertical="@dimen/dp5" />

                    <ImageView
                        android:id="@+id/ivReceiptPrint"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dp5"
                        android:src="@mipmap/ic_arrow002" />

                </LinearLayout>

            </LinearLayout>

            <!--打印机状态-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/printer_status" />

                <View
                    android:id="@+id/vReceiptPrinterStatus"
                    android:layout_width="@dimen/dp10"
                    android:layout_height="@dimen/dp10"
                    android:layout_marginEnd="@dimen/dp5"
                    android:background="@drawable/shape_yuan_red" />

                <TextView
                    android:id="@+id/tvReceiptPrinterStatus"
                    style="@style/text_14_red"
                    android:text="@string/connect_no" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/is_use_catering_receipt" />

                <ImageView
                    android:id="@+id/ivReceiptCatering"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="-5dp"
                    android:padding="@dimen/dp5"
                    android:src="@drawable/selector_checkbox002" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linReceiptCatering"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/reset_catering_receipt_count" />

                <TextView
                    android:id="@+id/tvReceiptCateringReset"
                    style="@style/text_12_white"
                    android:background="@drawable/shape_green_5"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/confirm_reset" />

            </LinearLayout>

            <!--默认打印数量-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/printer_defaule_count" />

                <ImageView
                    android:id="@+id/ivReceiptPrintCountSub"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_sub001" />

                <TextView
                    android:id="@+id/tvReceiptPrintCount"
                    style="@style/text_12_666"
                    android:layout_width="@dimen/dp49"
                    android:layout_height="@dimen/dp27"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center"
                    android:text="0" />

                <ImageView
                    android:id="@+id/ivReceiptPrintCountAdd"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="-5dp"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_add001" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    style="@style/text_14_black"
                    android:text="@string/tips_warm" />

                <TextView
                    android:id="@+id/tvReceiptTipsCount"
                    style="@style/text_14_black"
                    android:text="(0/50)" />

            </LinearLayout>

            <EditText
                android:id="@+id/etReceiptTips"
                style="@style/text_12_black"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp64"
                android:layout_marginTop="@dimen/dp5"
                android:background="@drawable/shape_f2_5"
                android:gravity="top"
                android:imeOptions="actionDone"
                android:inputType="text"
                android:maxLength="50"
                android:padding="@dimen/dp5"
                android:visibility="gone" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:text="@string/remarks_content" />

                <TextView
                    android:id="@+id/tvRemarksCount"
                    style="@style/text_14_black"
                    android:text="(0/50)" />

            </LinearLayout>

            <EditText
                android:id="@+id/etReceiptRemarks"
                style="@style/text_12_black"
                android:layout_width="match_parent"
                android:layout_marginTop="@dimen/dp5"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="top"
                android:hint="@string/thanks_welcome_again"
                android:imeOptions="actionDone"
                android:inputType="text"
                android:maxLength="50"
                android:padding="@dimen/dp5" />

            <TextView
                android:id="@+id/tvReceiptPrintTest"
                style="@style/text_14_white"
                android:layout_marginTop="@dimen/dp10"
                android:background="@drawable/shape_orange_5"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/print_test"
                android:textStyle="bold" />

            <TextView
                style="@style/text_12_999"
                android:layout_marginTop="@dimen/dp10"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:text="@string/print_test_tips"
                app:drawableLeftCompat="@mipmap/ic_tips002" />

        </LinearLayout>

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/dp10"
            android:background="@color/color_line" />

        <!--小票打印模版-->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:orientation="vertical">

            <TextView
                style="@style/text_14_black"
                android:text="@string/print_template" />

            <ScrollView
                android:layout_width="@dimen/dp181"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/dp10"
                android:background="@drawable/shape_d8_kuang_5">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/dp10">

                    <TextView
                        style="@style/text_16_black"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="@dimen/dp5"
                        android:text="@string/star_shopname"
                        android:textStyle="bold" />

                    <TextView
                        style="@style/text_10_333"
                        android:layout_gravity="center_horizontal"
                        android:text="@string/statement" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.8dp"
                        android:layout_marginTop="@dimen/dp5"
                        android:background="@drawable/shape_black_dashed_line" />

                    <TextView
                        android:id="@+id/tvReceiptPrinter"
                        style="@style/text_10_333"
                        android:text="" />

                    <TextView
                        android:id="@+id/tvReceiptStaff"
                        style="@style/text_10_333"
                        android:layout_marginTop="@dimen/dp2"
                        android:text="" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.8dp"
                        android:layout_marginVertical="@dimen/dp5"
                        android:background="@drawable/shape_black_dashed_line" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_10_333"
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:text="@string/goods" />

                        <TextView
                            style="@style/text_10_333"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="@string/count" />

                        <TextView
                            style="@style/text_10_333"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="@string/price" />

                        <TextView
                            style="@style/text_10_333"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="@string/amount" />

                    </LinearLayout>

                    <TextView
                        style="@style/text_10_333"
                        android:layout_marginTop="@dimen/dp2"
                        android:text="@string/jinquan" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp2"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_10_333"
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:text="" />

                        <TextView
                            style="@style/text_10_333"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="x1" />

                        <TextView
                            style="@style/text_10_333"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="3.00" />

                        <TextView
                            style="@style/text_10_333"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="3.00" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.8dp"
                        android:layout_marginVertical="@dimen/dp5"
                        android:background="@drawable/shape_black_dashed_line" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_10_333"
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:text="@string/amount" />

                        <TextView
                            style="@style/text_10_333"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="1" />

                        <TextView
                            style="@style/text_10_333"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="" />

                        <TextView
                            style="@style/text_10_333"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:text="3.00" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.8dp"
                        android:layout_marginVertical="@dimen/dp5"
                        android:background="@drawable/shape_black_dashed_line" />

                    <TextView
                        android:id="@+id/tvReceiptDiscount"
                        style="@style/text_10_333"
                        android:text="" />

                    <TextView
                        android:id="@+id/tvReceiptPayment"
                        style="@style/text_10_333"
                        android:layout_marginTop="@dimen/dp2"
                        android:text="" />

                    <TextView
                        android:id="@+id/tvReceiptActualPayment"
                        style="@style/text_10_333"
                        android:layout_marginTop="@dimen/dp2"
                        android:text="" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.8dp"
                        android:layout_marginVertical="@dimen/dp5"
                        android:background="@drawable/shape_black_dashed_line" />

                    <TextView
                        android:id="@+id/tvReceiptBalance"
                        style="@style/text_10_333"
                        android:text="" />

                    <TextView
                        android:id="@+id/tvReceiptCash"
                        style="@style/text_10_333"
                        android:layout_marginTop="@dimen/dp2"
                        android:text="" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.8dp"
                        android:layout_marginVertical="@dimen/dp5"
                        android:background="@drawable/shape_black_dashed_line" />

                    <TextView
                        android:id="@+id/tvReceiptMember"
                        style="@style/text_10_333"
                        android:text="" />

                    <TextView
                        android:id="@+id/tvReceiptPoints"
                        style="@style/text_10_333"
                        android:layout_marginTop="@dimen/dp2"
                        android:text="" />

                    <TextView
                        android:id="@+id/tvReceiptMemberPoints"
                        style="@style/text_10_333"
                        android:layout_marginTop="@dimen/dp2"
                        android:text="" />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.8dp"
                        android:layout_marginVertical="@dimen/dp5"
                        android:background="@drawable/shape_black_dashed_line" />

                    <TextView
                        style="@style/text_10_333"
                        android:text="@string/star_remarks" />

                    <TextView
                        android:id="@+id/tvReceiptRemarks"
                        style="@style/text_10_333"
                        android:layout_marginTop="@dimen/dp2"
                        android:text="@string/thanks_welcome_again" />

                    <TextView
                        android:id="@+id/tvReceiptDate"
                        style="@style/text_10_333"
                        android:layout_marginTop="@dimen/dp2"
                        android:text="" />

                    <ImageView
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp31"
                        android:layout_marginVertical="@dimen/dp2"
                        android:src="@mipmap/ic_barcode_test" />

                    <TextView
                        style="@style/text_10_333"
                        android:text="DII12154878454878777" />

                    <TextView
                        android:id="@+id/tvReceiptMobile"
                        style="@style/text_10_333"
                        android:layout_marginTop="@dimen/dp2"
                        android:text="" />

                    <TextView
                        android:id="@+id/tvReceiptTips"
                        style="@style/text_10_333"
                        android:layout_marginTop="@dimen/dp2"
                        android:text="@string/tips_warm_colon" />

                </LinearLayout>
            </ScrollView>

        </LinearLayout>

    </LinearLayout>

    <!--价签设置-->
    <LinearLayout
        android:id="@+id/linTags"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_white_bottom_5"
        android:orientation="vertical"
        android:padding="@dimen/dp10"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp20"
                android:layout_weight="3"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/printer_connect_type" />

                <LinearLayout
                    android:id="@+id/linTagsPrint"
                    android:layout_width="@dimen/dp140"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp5">

                    <TextView
                        android:id="@+id/tvTagsPrint"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp5"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:hint="@string/select_print_type"
                        android:maxLines="1"
                        android:paddingVertical="@dimen/dp5" />

                    <ImageView
                        android:id="@+id/ivTagsPrint"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dp5"
                        android:src="@mipmap/ic_arrow002" />

                </LinearLayout>

            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="2" />

        </LinearLayout>

        <TextView
            style="@style/text_14_black"
            android:layout_marginTop="@dimen/dp10"
            android:text="@string/print_template" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/linTagsTemplate"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <TextView
                    style="@style/text_12_green"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp96"
                    android:background="@drawable/shape_green_tm_kuang_5"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp16"
                    android:text="@string/tags_template"
                    app:drawableTopCompat="@mipmap/ic_printer001" />

                <TextView
                    style="@style/text_12_black"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center"
                    android:text="@string/tags_template" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linTagsType0"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp96"
                    android:background="@drawable/shape_d8_5"
                    android:src="@mipmap/ic_print_tags001" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/ivTagsType0"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:src="@drawable/selector_checkbox003" />

                    <TextView
                        style="@style/text_12_black"
                        android:text="95*38（毫米）" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linTagsType1"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp96"
                    android:background="@drawable/shape_d8_5"
                    android:src="@mipmap/ic_print_tags001" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/ivTagsType1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:src="@drawable/selector_checkbox003" />

                    <TextView
                        style="@style/text_12_black"
                        android:text="70*38（价签）" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linTagsType2"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp16"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp96"
                    android:background="@drawable/shape_d8_5"
                    android:src="@mipmap/ic_print_tags001" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/ivTagsType2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:src="@drawable/selector_checkbox003" />

                    <TextView
                        style="@style/text_12_black"
                        android:text="40*30（毫米）" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linTagsType3"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp96"
                    android:background="@drawable/shape_d8_5"
                    android:src="@mipmap/ic_print_tags001" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/ivTagsType3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:src="@drawable/selector_checkbox003" />

                    <TextView
                        style="@style/text_12_black"
                        android:text="40*30（毫米" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tvTagsPrintTest"
            style="@style/text_14_white"
            android:layout_marginTop="@dimen/dp10"
            android:background="@drawable/shape_orange_5"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/print_test"
            android:textStyle="bold" />

        <TextView
            style="@style/text_12_999"
            android:layout_marginTop="@dimen/dp10"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:text="@string/print_test_tips"
            app:drawableLeftCompat="@mipmap/ic_tips002" />

    </LinearLayout>

    <!--称重设置-->
    <LinearLayout
        android:id="@+id/linWeight"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_white_bottom_5"
        android:orientation="horizontal"
        android:padding="@dimen/dp10"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp10"
                    android:src="@mipmap/ic_scale003" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        style="@style/text_16_black"
                        android:text="@string/port_scale"
                        android:textStyle="bold" />

                    <TextView
                        style="@style/text_12_black"
                        android:layout_marginTop="@dimen/dp8"
                        android:text="@string/enable_port_scale" />

                </LinearLayout>

                <ImageView
                    android:id="@+id/ivWeightEnable"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6"
                    android:src="@drawable/selector_checkbox002" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/port_no" />

                <LinearLayout
                    android:id="@+id/linWeightPort"
                    android:layout_width="@dimen/dp140"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp5">

                    <TextView
                        android:id="@+id/tvWeightPort"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_margin="@dimen/dp5"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:hint="@string/select_port_no"
                        android:maxLines="1" />

                    <ImageView
                        android:id="@+id/ivWeightPort"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dp5"
                        android:src="@mipmap/ic_arrow002" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/BAUD" />

                <LinearLayout
                    android:id="@+id/linWeightBAUD"
                    android:layout_width="@dimen/dp140"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp5">

                    <TextView
                        android:id="@+id/tvWeightBAUD"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_margin="@dimen/dp5"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:hint="@string/select_BAUD"
                        android:maxLines="1" />

                    <ImageView
                        android:id="@+id/ivWeightBAUD"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dp5"
                        android:src="@mipmap/ic_arrow002" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/data_parsing_type" />

                <LinearLayout
                    android:id="@+id/linWeightDataParsingType"
                    android:layout_width="@dimen/dp140"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp5">

                    <TextView
                        android:id="@+id/tvWeightDataParsingType"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_margin="@dimen/dp5"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:hint="@string/select_data_parsing_type"
                        android:maxLines="1" />

                    <ImageView
                        android:id="@+id/ivWeightDataParsingType"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dp5"
                        android:src="@mipmap/ic_arrow002" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/scale_status" />

                <View
                    android:id="@+id/vWeightScaleStatus"
                    android:layout_width="@dimen/dp10"
                    android:layout_height="@dimen/dp10"
                    android:layout_marginEnd="@dimen/dp5"
                    android:background="@drawable/shape_yuan_red" />

                <TextView
                    android:id="@+id/tvWeightScaleStatus"
                    style="@style/text_14_red"
                    android:text="@string/connect_no" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/weight_now" />

                <TextView
                    android:id="@+id/tvWeightWeight"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0.00kg"
                    android:textColor="@color/green"
                    android:textSize="@dimen/f24"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/dp10"
            android:background="@color/color_line" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="right"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvWeightAdd"
                    style="@style/text_14_white"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="@dimen/dp10"
                    android:background="@drawable/shape_green_5"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/scale_add"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tvWeightDownload"
                    style="@style/text_14_white"
                    android:layout_height="match_parent"
                    android:background="@drawable/shape_orange_5"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/scale_download" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvWeightScale"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingVertical="@dimen/dp5"
                android:visibility="gone"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        </LinearLayout>

    </LinearLayout>

    <!--摄像头设置-->
    <LinearLayout
        android:id="@+id/linCamera"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_white_bottom_5"
        android:orientation="vertical"
        android:visibility="gone">

    </LinearLayout>

    <!--POS设置-->
    <LinearLayout
        android:id="@+id/linPos"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_white_bottom_5"
        android:orientation="vertical"
        android:padding="@dimen/dp10"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp15"
                android:layout_weight="3"
                android:gravity="center_horizontal"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/enable_pos_cashier" />

                <ImageView
                    android:id="@+id/ivPosEnable"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@drawable/selector_checkbox002" />

            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="2" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp15"
                android:layout_weight="3"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/pos_id" />

                <EditText
                    android:id="@+id/etPosId"
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:inputType="text"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="" />

            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="2" />

        </LinearLayout>

    </LinearLayout>

    <!--其他设置-->
    <LinearLayout
        android:id="@+id/linOther"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_white_bottom_5"
        android:orientation="vertical"
        android:padding="@dimen/dp10"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp15"
                android:layout_weight="3"
                android:gravity="center_horizontal"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/enable_secondary_screen" />

                <ImageView
                    android:id="@+id/ivOtherEnable"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@drawable/selector_checkbox002" />

            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="2" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="15dp"
                android:layout_weight="3"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/pay_money_box" />

                <ImageView
                    android:id="@+id/ivOtherEnableBox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@drawable/selector_checkbox002" />

            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="2" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp20"
                android:layout_weight="3"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/money_box_printer" />

                <LinearLayout
                    android:id="@+id/linOtherBox"
                    android:layout_width="@dimen/dp140"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp5">

                    <TextView
                        android:id="@+id/tvOtherBox"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_margin="@dimen/dp5"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:hint="@string/select_money_box_printer"
                        android:maxLines="1" />

                    <ImageView
                        android:id="@+id/ivOtherBox"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dp5"
                        android:src="@mipmap/ic_arrow002" />

                </LinearLayout>

            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="2" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp20"
                android:layout_weight="3"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/tts_engine" />

                <LinearLayout
                    android:id="@+id/linOtherTTSEngine"
                    android:layout_width="@dimen/dp140"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp5">

                    <TextView
                        android:id="@+id/tvOtherTTSEngine"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_margin="@dimen/dp5"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:hint="@string/select_tts_engine"
                        android:maxLines="1" />

                    <ImageView
                        android:id="@+id/ivOtherTTSEngine"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dp5"
                        android:src="@mipmap/ic_arrow002" />

                </LinearLayout>

            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="2" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp20"
                android:layout_weight="3"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/tts_language" />

                <LinearLayout
                    android:id="@+id/linOtherTTSLanguage"
                    android:layout_width="@dimen/dp140"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp5">

                    <TextView
                        android:id="@+id/tvOtherTTSLanguage"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_margin="@dimen/dp5"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:hint="@string/select_tts_language"
                        android:maxLines="1" />

                    <ImageView
                        android:id="@+id/ivOtherTTSLanguage"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dp5"
                        android:src="@mipmap/ic_arrow002" />

                </LinearLayout>

            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="2" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp20"
                android:layout_weight="3"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/tts_voice" />

                <LinearLayout
                    android:id="@+id/linOtherTTSVoice"
                    android:layout_width="@dimen/dp140"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp5">

                    <TextView
                        android:id="@+id/tvOtherTTSVoice"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_margin="@dimen/dp5"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:hint="@string/select_tts_voice"
                        android:maxLines="1" />

                    <ImageView
                        android:id="@+id/ivOtherTTSVoice"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dp5"
                        android:src="@mipmap/ic_arrow002" />

                </LinearLayout>

            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="2" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>