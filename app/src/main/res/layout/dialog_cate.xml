<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/select_goods_cate"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/color_line" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="horizontal">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvDialogCate"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/white"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            tools:ignore="NestedWeights" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvDialogCateChild"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:background="@color/color_f2"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    </LinearLayout>

    <TextView
        android:id="@+id/tvDialogConfirm"
        style="@style/text_14_white"
        android:layout_width="@dimen/dp200"
        android:layout_gravity="center"
        android:layout_marginVertical="@dimen/dp10"
        android:background="@drawable/shape_green_5"
        android:gravity="center"
        android:paddingVertical="@dimen/dp5"
        android:text="@string/confirm" />

</LinearLayout>