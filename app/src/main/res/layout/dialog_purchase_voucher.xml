<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_10"
    android:gravity="center"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/purchase_voucher"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <TextView
        style="@style/text_14_black"
        android:text="@string/total_amount" />

    <TextView
        android:id="@+id/tvDialogMoney"
        style="@style/text_14_blue"
        android:layout_marginTop="@dimen/dp10"
        android:text="0.00"
        android:textStyle="bold" />

    <TextView
        style="@style/text_14_black"
        android:layout_gravity="left"
        android:layout_marginHorizontal="@dimen/dp10"
        android:layout_marginTop="@dimen/dp15"
        android:text="@string/remarks_info" />

    <TextView
        android:id="@+id/tvDialogRemarks"
        style="@style/text_14_black"
        android:layout_width="match_parent"
        android:layout_marginHorizontal="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/shape_f2_5"
        android:padding="@dimen/dp10"
        android:text="@string/nothing" />

    <TextView
        style="@style/text_14_black"
        android:layout_gravity="left"
        android:layout_marginHorizontal="@dimen/dp10"
        android:layout_marginTop="@dimen/dp15"
        android:text="@string/purchase_voucher" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvDialog"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp5"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:spanCount="3" />

</LinearLayout>