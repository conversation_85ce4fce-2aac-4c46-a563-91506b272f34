<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.yxl.cashier_retail.view.ScannerEditText
            android:id="@+id/etDialogScan"
            android:layout_width="1dp"
            android:layout_height="1dp" />

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/online_collection_combination"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:background="@drawable/shape_d8_kuang_5"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp5">

        <TextView
            style="@style/text_12_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/receivable_money" />

        <TextView
            android:id="@+id/tvDialogTotal"
            style="@style/text_18_black"
            android:text="0.00"
            android:textStyle="bold" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/linDialogCash"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:background="@drawable/shape_green_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp5">

            <TextView
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:text="@string/cash"
                app:drawableLeftCompat="@mipmap/ic_payment_img011"
                tools:ignore="NestedWeights" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_vertical|end"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvDialogCash"
                    style="@style/text_12_black"
                    android:text="" />

                <ImageView
                    android:id="@+id/ivDialogCursorCash"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp15"
                    android:src="@drawable/anim_cursor"
                    android:visibility="visible" />

                <TextView
                    android:id="@+id/tvDialogCashHint"
                    style="@style/text_12_black"
                    android:hint="@string/input_money" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linDialogJinQ"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp5">

            <TextView
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:text="@string/pay_code"
                app:drawableLeftCompat="@mipmap/ic_payment_img019"
                tools:ignore="NestedWeights" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_vertical|end"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvDialogJinQ"
                    style="@style/text_12_black"
                    android:text="" />

                <ImageView
                    android:id="@+id/ivDialogCursorJinQ"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/dp15"
                    android:src="@drawable/anim_cursor"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tvDialogJinQHint"
                    style="@style/text_12_black"
                    android:hint="@string/input_money" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/linDialogKeyboard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/ivDialogOpen0"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp10"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_keyboard_select001" />

        <com.yxl.cashier_retail.view.NumberKeyBoardView
            android:id="@+id/numberKeyBoardView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp51"
            android:layout_marginVertical="@dimen/dp5" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/linDialogImg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:id="@+id/ivDialogImg"
            android:layout_width="@dimen/dp175"
            android:layout_height="@dimen/dp90"
            android:scaleType="centerCrop"
            android:src="@drawable/cashiering002" />

        <TextView
            style="@style/text_14_999"
            android:layout_marginTop="@dimen/dp10"
            android:text="@string/place_scan_customer_payment_code" />

        <ImageView
            android:id="@+id/ivDialogOpen1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp10"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_keyboard_select002" />

    </LinearLayout>

</LinearLayout>