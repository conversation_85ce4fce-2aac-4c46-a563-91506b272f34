<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/dp5"
        android:layout_weight="2"
        android:background="@drawable/shape_white_5"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp10"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/linType"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvType"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/collection_type"
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:id="@+id/ivType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linStartDate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvStartDate"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:hint="@string/date_start"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp5"
                    android:text=""
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:id="@+id/ivStartDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linEndDate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvEndDate"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:hint="@string/date_end"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp5"
                    android:text=""
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:id="@+id/ivEndDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp10"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="@dimen/dp3"
                    android:layout_height="@dimen/dp15"
                    android:layout_marginEnd="@dimen/dp6"
                    android:background="@drawable/shape_green_2" />

                <TextView
                    style="@style/text_12_black"
                    android:text="@string/sales_overview"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="@dimen/dp3"
                    android:layout_height="@dimen/dp15"
                    android:layout_marginEnd="@dimen/dp6"
                    android:background="@drawable/shape_green_2" />

                <TextView
                    style="@style/text_12_black"
                    android:text="@string/business_overview"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="@dimen/dp3"
                    android:layout_height="@dimen/dp15"
                    android:layout_marginEnd="@dimen/dp6"
                    android:background="@drawable/shape_green_2" />

                <TextView
                    style="@style/text_12_black"
                    android:text="@string/applet_overview"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp10"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvTotal"
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_weight="0.5"
                android:text="@string/business_money_colon" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvRecharge"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/member_recharge_colon"
                    tools:ignore="NestedWeights" />

                <TextView
                    android:id="@+id/tvWechat"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/wechat_colon" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvApplet"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/online_income_colon"
                    tools:ignore="NestedWeights" />

                <TextView
                    android:id="@+id/tvCouponsPlat"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/platform_coupons_colon" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linMore"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvCount"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:text="@string/order_count_colon" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvConsumption"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/member_consumption_colon"
                        tools:ignore="NestedWeights" />

                    <TextView
                        android:id="@+id/tvNoPwd"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/no_password_colon" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvNoCredit"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/to_be_credited_colon"
                        tools:ignore="NestedWeights" />

                    <TextView
                        android:id="@+id/tvBeansPlat"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/platform_beans_colon" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvProfit"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:text="@string/order_profit_colon" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvPoints"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/points_goods_colon"
                        tools:ignore="NestedWeights" />

                    <TextView
                        android:id="@+id/tvCash"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/cash_colon" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvWithdrawal"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/withdrawn_colon"
                        tools:ignore="NestedWeights" />

                    <TextView
                        android:id="@+id/tvCouponsShop"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/shop_coupons_colon" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="0.5" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvRefund"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/refund_colon"
                        tools:ignore="NestedWeights" />

                    <TextView
                        android:id="@+id/tvAlipay"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/alipay_colon" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvNoWithdrawal"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/pending_withdrawn_colon"
                        tools:ignore="NestedWeights" />

                    <TextView
                        android:id="@+id/tvBeansShop"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/shop_beans_colon" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="0.5" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvDebt"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/debt_colon"
                        tools:ignore="NestedWeights" />

                    <TextView
                        android:id="@+id/tvBank"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/bank_card_colon" />

                </LinearLayout>

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />

            </LinearLayout>

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="@dimen/dp6"
            android:background="@color/color_line" />

        <LinearLayout
            android:id="@+id/linOpen"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="@dimen/dp10">

            <TextView
                android:id="@+id/tvOpen"
                style="@style/text_12_green"
                android:layout_marginEnd="@dimen/dp6"
                android:text="@string/show_more_info" />

            <ImageView
                android:id="@+id/ivOpen"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/ic_arrow006" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp10"
            android:background="@drawable/shape_f5_top_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5">

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="2"
                android:text="@string/order_time" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/addressee" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1.2"
                android:gravity="center"
                android:text="@string/order_no" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/collection_type" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/total_amount" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/count" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1.2"
                android:gravity="center"
                android:text="@string/mobile" />

        </LinearLayout>

        <include
            android:id="@+id/vSmartrefreshlayout"
            layout="@layout/layout_smartrefreshlayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/dp10" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/linInfo"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_white_5"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:text="@string/order_no_colon" />

                <TextView
                    android:id="@+id/tvOrderNo"
                    style="@style/text_12_black"
                    android:text="" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:text="@string/order_total_colon" />

                <TextView
                    android:id="@+id/tvOrderTotal"
                    style="@style/text_12_black"
                    android:text="0.00" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:text="@string/order_time_colon" />

                <TextView
                    android:id="@+id/tvOrderTime"
                    style="@style/text_12_black"
                    android:text="" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:text="@string/discount_money_colon" />

                <TextView
                    android:id="@+id/tvOrderCoupons"
                    style="@style/text_12_red"
                    android:text="0.00" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:text="@string/status_order_colon" />

                <TextView
                    android:id="@+id/tvOrderStatus"
                    style="@style/text_12_black"
                    android:text="" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:text="@string/payment_info_colon" />

                <TextView
                    android:id="@+id/tvOrderInfo"
                    style="@style/text_12_black"
                    android:text="" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp5"
            android:layout_weight="1"
            android:background="@drawable/shape_white_5"
            android:orientation="vertical"
            android:padding="@dimen/dp10"
            tools:ignore="NestedWeights">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_f5_top_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <TextView
                    style="@style/text_10_666"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:text="@string/goods_name" />

                <TextView
                    style="@style/text_10_666"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/goods_count" />

                <TextView
                    style="@style/text_10_666"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/goods_price" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvGoods"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:background="@drawable/shape_white_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp5">

            <TextView
                android:id="@+id/tvPrint"
                style="@style/text_14_white"
                android:layout_width="0dp"
                android:layout_marginHorizontal="@dimen/dp5"
                android:layout_weight="1"
                android:background="@drawable/shape_orange_5"
                android:gravity="center"
                android:paddingVertical="@dimen/dp12"
                android:text="@string/print_receipt"
                android:textStyle="bold"
                android:visibility="gone" />

            <TextView
                android:id="@+id/tvOrderRefund"
                style="@style/text_14_white"
                android:layout_width="0dp"
                android:layout_marginHorizontal="@dimen/dp5"
                android:layout_weight="1"
                android:background="@drawable/shape_red_5"
                android:gravity="center"
                android:paddingVertical="@dimen/dp12"
                android:text="@string/order_refund"
                android:textStyle="bold"
                android:visibility="gone" />

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginHorizontal="@dimen/dp5"
                android:layout_weight="1" />

        </LinearLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/tvNothing"
        style="@style/text_14_black"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@drawable/shape_white_5"
        android:gravity="center"
        android:text="@string/no_order_information"
        android:visibility="visible" />

</LinearLayout>