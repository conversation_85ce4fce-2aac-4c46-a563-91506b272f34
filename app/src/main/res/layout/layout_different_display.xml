<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.youth.banner.Banner
        android:id="@+id/banner"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <LinearLayout
        android:id="@+id/linEmpty"
        android:layout_width="@dimen/dp384"
        android:layout_height="match_parent"
        android:layout_alignParentEnd="true"
        android:background="@color/white"
        android:gravity="center">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/ic_cart003" />

    </LinearLayout>

    <!--购物车-->
    <LinearLayout
        android:id="@+id/linCart"
        android:layout_width="@dimen/dp384"
        android:layout_height="match_parent"
        android:layout_alignParentEnd="true"
        android:background="@color/white"
        android:orientation="vertical"
        android:visibility="gone">

        <!--支付状态-->
        <LinearLayout
            android:id="@+id/linStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/green"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp20"
            android:paddingVertical="@dimen/dp30"
            android:visibility="gone">

            <ImageView
                android:id="@+id/ivStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:src="@mipmap/ic_right003" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvStatus"
                    style="@style/text_16_white"
                    android:text=""
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvStatusTips"
                    style="@style/text_12_white"
                    android:text="" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp10"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_12_333"
                android:layout_width="0dp"
                android:layout_weight="3"
                android:text="@string/goods_name" />

            <TextView
                style="@style/text_12_333"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/price" />

            <TextView
                style="@style/text_12_333"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/count_weight" />

            <TextView
                style="@style/text_12_333"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/subtotal" />

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvCart"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        <!--会员-->
        <RelativeLayout
            android:id="@+id/relMember"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/dp10"
            android:background="@mipmap/ic_member_bg001"
            android:padding="@dimen/dp10"
            android:visibility="gone">

            <ImageView
                android:id="@+id/ivMemberHead"
                android:layout_width="@dimen/dp50"
                android:layout_height="@dimen/dp50"
                android:layout_marginEnd="@dimen/dp10"
                android:src="@mipmap/ic_head003" />

            <TextView
                android:id="@+id/tvMemberName"
                style="@style/text_12_white"
                android:layout_toEndOf="@+id/ivMemberHead"
                android:text=""
                android:textColor="@color/color_e8cc81" />

            <TextView
                android:id="@+id/tvMemberMobile"
                style="@style/text_12_white"
                android:layout_below="@+id/tvMemberName"
                android:layout_marginTop="@dimen/dp2"
                android:layout_toEndOf="@+id/ivMemberHead"
                android:text=""
                android:textColor="@color/color_e8cc81" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tvMemberMobile"
                android:layout_marginTop="@dimen/dp5"
                android:layout_toEndOf="@+id/ivMemberHead"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvMemberBalance"
                    style="@style/text_12_white"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp5"
                    android:layout_weight="1"
                    android:text="@string/balance_colon"
                    android:textColor="@color/color_e8cc81" />

                <TextView
                    android:id="@+id/tvMemberPoints"
                    style="@style/text_12_white"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:text="@string/points_colon"
                    android:textColor="@color/color_e8cc81" />

            </LinearLayout>

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/color_line" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10">

            <TextView
                style="@style/text_12_black"
                android:layout_centerVertical="true"
                android:text="@string/discount_colon" />

            <TextView
                android:id="@+id/tvDiscount"
                style="@style/text_12_red"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:text="0.00" />

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/color_line" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10">

            <TextView
                style="@style/text_14_black"
                android:layout_centerVertical="true"
                android:text="@string/payable_colon"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvTotal"
                style="@style/text_14_red"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:text="0.00"
                android:textStyle="bold" />

        </RelativeLayout>

    </LinearLayout>

    <!--人脸-->
    <RelativeLayout
        android:id="@+id/relFace"
        android:layout_width="@dimen/dp300"
        android:layout_height="@dimen/dp300"
        android:layout_centerInParent="true"
        android:background="@drawable/shape_d8_kuang_5"
        android:visibility="gone">

        <TextView
            android:id="@+id/tvFaceTips"
            style="@style/text_14_black"
            android:layout_centerHorizontal="true"
            android:text="" />

        <ImageView
            android:id="@+id/ivFaceImg"
            android:layout_width="@dimen/dp196"
            android:layout_height="@dimen/dp196"
            android:layout_centerInParent="true"
            android:scaleType="centerCrop"
            android:src="@mipmap/ic_default_img" />

        <!--RGB预览-->
        <RelativeLayout
            android:layout_width="200dp"
            android:layout_height="200dp"
            android:layout_centerInParent="true"
            android:background="@mipmap/ic_face_bg001" />

    </RelativeLayout>

</RelativeLayout>