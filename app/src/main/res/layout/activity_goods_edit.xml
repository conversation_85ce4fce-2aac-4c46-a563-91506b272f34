<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/white">

        <ImageView
            android:id="@+id/ivBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp10"
            android:src="@mipmap/ic_back002" />

        <TextView
            android:id="@+id/tvTitle"
            style="@style/text_16_black"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/ivBack"
            android:text=""
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/linDate"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toStartOf="@+id/tvCashier"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10">

            <TextClock
                style="@style/text_12_black"
                android:format24Hour="MM-dd HH:mm"
                android:textStyle="bold" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvCashier"
            style="@style/text_14_white"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/green"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp20"
            android:text="@string/cashier_table"
            android:textStyle="bold"
            app:drawableLeftCompat="@mipmap/ic_cashier001" />

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/relTop"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp46"
        android:background="@drawable/shape_white_5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/lin0"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10">

            <TextView
                android:id="@+id/tv0"
                style="@style/text_14_green"
                android:layout_marginTop="@dimen/dp10"
                android:layout_marginBottom="@dimen/dp8"
                android:text="@string/pack0"
                android:textStyle="bold" />

            <View
                android:id="@+id/v0"
                android:layout_width="@dimen/dp24"
                android:layout_height="@dimen/dp2"
                android:background="@drawable/shape_green_2" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/lin1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/lin0"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10">

            <TextView
                android:id="@+id/tv1"
                style="@style/text_14_black"
                android:layout_marginTop="@dimen/dp10"
                android:layout_marginBottom="@dimen/dp8"
                android:text="@string/pack1" />

            <View
                android:id="@+id/v1"
                android:layout_width="@dimen/dp24"
                android:layout_height="@dimen/dp2"
                android:background="@drawable/shape_green_2"
                android:visibility="invisible" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/lin2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/lin1"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10">

            <TextView
                android:id="@+id/tv2"
                style="@style/text_14_black"
                android:layout_marginTop="@dimen/dp10"
                android:layout_marginBottom="@dimen/dp8"
                android:text="@string/pack2" />

            <View
                android:id="@+id/v2"
                android:layout_width="@dimen/dp24"
                android:layout_height="@dimen/dp2"
                android:background="@drawable/shape_green_2"
                android:visibility="invisible" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvType"
            style="@style/text_12_white"
            android:layout_centerHorizontal="true"
            android:background="@mipmap/ic_blue_bg002"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp2"
            android:text=""
            android:textStyle="bold"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvDel"
                style="@style/text_14_red"
                android:layout_marginEnd="@dimen/dp10"
                android:background="@drawable/shape_red_kuang_5"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/del"
                android:visibility="gone"
                app:drawableLeftCompat="@mipmap/ic_del002" />

            <TextView
                android:id="@+id/tvSyn"
                style="@style/text_14_white"
                android:layout_marginEnd="@dimen/dp10"
                android:background="@drawable/shape_orange_5"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/download_to_scale"
                android:visibility="gone"
                app:drawableLeftCompat="@mipmap/ic_download001" />

            <TextView
                android:id="@+id/tvConfirm"
                style="@style/text_14_white"
                android:layout_marginEnd="@dimen/dp10"
                android:background="@drawable/shape_green_5"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/save"
                app:drawableLeftCompat="@mipmap/ic_save001" />

        </LinearLayout>

    </RelativeLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/relTop"
        android:layout_marginTop="@dimen/dp5"
        android:background="@drawable/shape_white_5">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!--基础包装-->
            <LinearLayout
                android:id="@+id/linSpecs0"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/dp10"
                android:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="@dimen/dp3"
                        android:layout_height="@dimen/dp15"
                        android:layout_marginEnd="@dimen/dp5"
                        android:background="@drawable/shape_green_2" />

                    <TextView
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/pack0"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvIn0"
                        style="@style/text_14_white"
                        android:layout_marginEnd="@dimen/dp10"
                        android:background="@drawable/shape_green_5"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/in"
                        android:visibility="gone"
                        app:drawableStartCompat="@mipmap/ic_in001" />

                    <TextView
                        android:id="@+id/tvOut0"
                        style="@style/text_14_white"
                        android:layout_marginEnd="@dimen/dp10"
                        android:background="@drawable/shape_orange_5"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/out"
                        android:visibility="gone"
                        app:drawableStartCompat="@mipmap/ic_out001" />

                    <TextView
                        android:id="@+id/tvPrint0"
                        style="@style/text_14_white"
                        android:layout_marginEnd="@dimen/dp10"
                        android:background="@drawable/shape_blue_5"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/print_tags"
                        android:visibility="gone"
                        app:drawableLeftCompat="@mipmap/ic_print001" />

                    <TextView
                        android:id="@+id/tvClear0"
                        style="@style/text_14_red"
                        android:background="@drawable/shape_red_kuang_5"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/clear"
                        app:drawableLeftCompat="@mipmap/ic_del002" />

                </LinearLayout>

                <!--供货商、商品分类-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/supplier" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/goods_cate" />

                    </LinearLayout>

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/linSupplier"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5">

                        <TextView
                            android:id="@+id/tvSupplier"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_weight="1"
                            android:hint="@string/select_supplier" />

                        <ImageView
                            android:id="@+id/ivSupplier"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/ic_arrow002" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/linCate0"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5">

                        <TextView
                            android:id="@+id/tvCate0"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_weight="1"
                            android:hint="@string/select_cate" />

                        <ImageView
                            android:id="@+id/ivCate0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/ic_arrow002" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/linCate1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5">

                        <TextView
                            android:id="@+id/tvCate1"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_weight="1"
                            android:hint="@string/select_cate" />

                        <ImageView
                            android:id="@+id/ivCate1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/ic_arrow002" />

                    </LinearLayout>

                </LinearLayout>

                <!--商品品牌、保质期、生产日期-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/goods_brand" />

                        <EditText
                            android:id="@+id/etBrand"
                            style="@style/text_12_black"
                            android:layout_width="match_parent"
                            android:layout_marginTop="@dimen/dp5"
                            android:background="@drawable/shape_d8_kuang_5"
                            android:hint="@string/input_goods_brand"
                            android:inputType="text"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/goods_life" />

                        <EditText
                            android:id="@+id/etGoodsLife"
                            style="@style/text_12_black"
                            android:layout_width="match_parent"
                            android:layout_marginTop="@dimen/dp5"
                            android:background="@drawable/shape_d8_kuang_5"
                            android:hint="@string/input_goods_life"
                            android:inputType="number"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5"
                            tools:ignore="NestedWeights" />

                    </LinearLayout>

                    <!--生产日期-->
                    <LinearLayout
                        android:id="@+id/linGoodsDate"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/date_produce" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp5"
                            android:background="@drawable/shape_d8_kuang_5"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5">

                            <TextView
                                android:id="@+id/tvGoodsDate"
                                style="@style/text_12_black"
                                android:layout_width="0dp"
                                android:layout_marginEnd="@dimen/dp5"
                                android:layout_weight="1"
                                android:ellipsize="end"
                                android:hint="@string/select_date_produce"
                                android:maxLines="1" />

                            <ImageView
                                android:id="@+id/ivGoodsDate"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@mipmap/ic_arrow002" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

                <!--库存预警-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="@string/stock_warning" />

                        <ImageView
                            android:id="@+id/ivWarning"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/dp10"
                            android:src="@drawable/selector_checkbox002" />

                    </LinearLayout>

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="2" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/linWarning"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/dp10"
                    android:orientation="horizontal"
                    android:visibility="gone">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_marginEnd="@dimen/dp10"
                            android:text="@string/low_to_warning" />

                        <EditText
                            android:id="@+id/etWarningLow"
                            style="@style/text_12_black"
                            android:layout_width="match_parent"
                            android:layout_marginTop="@dimen/dp5"
                            android:background="@drawable/shape_d8_kuang_5"
                            android:hint="@string/stock_low_limit"
                            android:inputType="numberDecimal"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/linWarningTall"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/text_12_666"
                            android:text="@string/tall_to_warning" />

                        <EditText
                            android:id="@+id/etWarningTall"
                            style="@style/text_12_black"
                            android:layout_width="match_parent"
                            android:layout_marginTop="@dimen/dp5"
                            android:background="@drawable/shape_d8_kuang_5"
                            android:hint="@string/stock_tall_limit"
                            android:inputType="numberDecimal"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5" />

                    </LinearLayout>

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/goods_barcode" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/goods_name" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/pricing_type" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/linBarcode0"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/etBarcode0"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:background="@null"
                            android:hint="@string/input_goods_barcode"
                            android:inputType="number"
                            android:maxLines="1"
                            android:paddingVertical="@dimen/dp5"
                            android:paddingStart="@dimen/dp10" />

                        <ImageView
                            android:id="@+id/ivBarcode0"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:paddingHorizontal="@dimen/dp5"
                            android:src="@mipmap/ic_barcode001" />

                    </LinearLayout>

                    <EditText
                        android:id="@+id/etName0"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_goods_name"
                        android:inputType="text"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                    <LinearLayout
                        android:id="@+id/linChengType0"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingVertical="@dimen/dp10"
                        android:paddingEnd="@dimen/dp10">

                        <ImageView
                            android:id="@+id/ivChengType0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp5"
                            android:src="@drawable/selector_checkbox003" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/pricing_piece" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/linChengType1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="0.5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp10">

                        <ImageView
                            android:id="@+id/ivChengType1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/dp5"
                            android:src="@drawable/selector_checkbox003" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/pricing_weight" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:paddingVertical="@dimen/dp5">

                            <TextView
                                style="@style/text_12_red"
                                android:text="*" />

                            <TextView
                                style="@style/text_12_black"
                                android:text="@string/goods_unit" />

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/linUnit0"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@drawable/shape_d8_kuang_5"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5">

                            <TextView
                                android:id="@+id/tvUnit0"
                                style="@style/text_12_black"
                                android:layout_width="0dp"
                                android:layout_marginEnd="@dimen/dp5"
                                android:layout_weight="1"
                                android:ellipsize="end"
                                android:hint="@string/select_goods_unit"
                                android:maxLines="1" />

                            <ImageView
                                android:id="@+id/ivUnit0"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:src="@mipmap/ic_arrow002" />

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <LinearLayout
                            android:id="@+id/linInPrice0"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:paddingVertical="@dimen/dp5">

                            <TextView
                                style="@style/text_12_red"
                                android:text="*" />

                            <TextView
                                android:id="@+id/tvInPrice0"
                                style="@style/text_12_black"
                                android:text="@string/in_price" />

                            <ImageView
                                android:id="@+id/ivInPrice0"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:src="@mipmap/ic_question001"
                                android:visibility="gone" />

                        </LinearLayout>

                        <EditText
                            android:id="@+id/etInPrice0"
                            style="@style/text_12_black"
                            android:layout_width="match_parent"
                            android:background="@drawable/shape_d8_kuang_5"
                            android:hint="@string/input_in_price"
                            android:inputType="numberDecimal"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            style="@style/text_12_black"
                            android:paddingVertical="@dimen/dp5"
                            android:text="@string/goods_stock" />

                        <EditText
                            android:id="@+id/etStock0"
                            style="@style/text_12_black"
                            android:layout_width="match_parent"
                            android:layout_marginTop="@dimen/dp5"
                            android:background="@drawable/shape_d8_kuang_5"
                            android:hint="@string/input_goods_stock"
                            android:inputType="numberDecimal"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5" />

                    </LinearLayout>

                </LinearLayout>

                <!--最近入库价-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_marginEnd="@dimen/dp20"
                        android:layout_weight="2" />

                    <TextView
                        android:id="@+id/tvStockPrice0"
                        style="@style/text_10_black"
                        android:layout_width="0dp"
                        android:layout_marginTop="@dimen/dp5"
                        android:layout_weight="1"
                        android:text="@string/in_price_recently_colon"
                        android:visibility="gone" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/price_sale" />

                    </LinearLayout>

                    <TextView
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/price_member" />

                    <TextView
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/price_online" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/etSalePrice0"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_price_sale"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                    <EditText
                        android:id="@+id/etMemberPrice0"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_price_member"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                    <EditText
                        android:id="@+id/etOnlinePrice0"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_price_online"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/start_order_online" />

                    <TextView
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/goods_specs" />

                    <TextView
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/abbr" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/etStartOrder0"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_start_order_online"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                    <EditText
                        android:id="@+id/etSpecs0"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_goods_specs"
                        android:inputType="text"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                    <EditText
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_abbr"
                        android:inputType="text"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="@string/cashier_shelves" />

                        <ImageView
                            android:id="@+id/ivPrinter0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/dp10"
                            android:src="@drawable/selector_checkbox002" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="@string/applet_shelves" />

                        <ImageView
                            android:id="@+id/ivApplet0"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:padding="@dimen/dp10"
                            android:src="@drawable/selector_checkbox002" />

                    </LinearLayout>

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1" />

                </LinearLayout>

            </LinearLayout>

            <!--中间包装-->
            <LinearLayout
                android:id="@+id/linSpecs1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/dp10"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="@dimen/dp3"
                        android:layout_height="@dimen/dp15"
                        android:layout_marginEnd="@dimen/dp5"
                        android:background="@drawable/shape_green_2" />

                    <TextView
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/pack1"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvIn1"
                        style="@style/text_14_white"
                        android:layout_marginEnd="@dimen/dp10"
                        android:background="@drawable/shape_green_5"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/in"
                        android:visibility="gone"
                        app:drawableStartCompat="@mipmap/ic_in001" />

                    <TextView
                        android:id="@+id/tvOut1"
                        style="@style/text_14_white"
                        android:layout_marginEnd="@dimen/dp10"
                        android:background="@drawable/shape_orange_5"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/out"
                        android:visibility="gone"
                        app:drawableStartCompat="@mipmap/ic_out001" />

                    <TextView
                        android:id="@+id/tvPrint1"
                        style="@style/text_14_white"
                        android:layout_marginEnd="@dimen/dp10"
                        android:background="@drawable/shape_blue_5"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/print_tags"
                        android:visibility="gone"
                        app:drawableLeftCompat="@mipmap/ic_print001" />

                    <TextView
                        android:id="@+id/tvClear1"
                        style="@style/text_14_red"
                        android:background="@drawable/shape_red_kuang_5"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/clear"
                        app:drawableLeftCompat="@mipmap/ic_del002" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/goods_barcode" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/goods_name" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/goods_unit" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/linBarcode1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/etBarcode1"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:background="@null"
                            android:hint="@string/input_goods_barcode"
                            android:inputType="number"
                            android:maxLines="1"
                            android:paddingVertical="@dimen/dp5"
                            android:paddingStart="@dimen/dp10" />

                        <ImageView
                            android:id="@+id/ivBarcode1"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:paddingHorizontal="@dimen/dp5"
                            android:src="@mipmap/ic_barcode001" />

                    </LinearLayout>

                    <EditText
                        android:id="@+id/etName1"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_goods_name"
                        android:inputType="text"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                    <LinearLayout
                        android:id="@+id/linUnit1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5">

                        <TextView
                            android:id="@+id/tvUnit1"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:hint="@string/select_goods_unit"
                            android:maxLines="1" />

                        <ImageView
                            android:id="@+id/ivUnit1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/ic_arrow002" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:paddingVertical="@dimen/dp5">

                            <TextView
                                style="@style/text_12_red"
                                android:text="*" />

                            <TextView
                                style="@style/text_12_black"
                                android:text="@string/units" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <EditText
                                android:id="@+id/etCount1"
                                style="@style/text_12_black"
                                android:layout_width="0dp"
                                android:layout_marginEnd="@dimen/dp5"
                                android:layout_weight="1"
                                android:background="@drawable/shape_d8_kuang_5"
                                android:hint="@string/how_mush_base_pack"
                                android:inputType="number"
                                android:maxLines="1"
                                android:paddingHorizontal="@dimen/dp10"
                                android:paddingVertical="@dimen/dp5"
                                tools:ignore="NestedWeights" />

                            <TextView
                                style="@style/text_12_black"
                                android:text="@string/piece" />

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <LinearLayout
                            android:id="@+id/linInPrice1"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:paddingVertical="@dimen/dp5">

                            <TextView
                                style="@style/text_12_red"
                                android:text="*" />

                            <TextView
                                android:id="@+id/tvInPrice1"
                                style="@style/text_12_black"
                                android:text="@string/in_price" />

                            <ImageView
                                android:id="@+id/ivInPrice1"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:src="@mipmap/ic_question001"
                                android:visibility="gone" />

                        </LinearLayout>

                        <EditText
                            android:id="@+id/etInPrice1"
                            style="@style/text_12_black"
                            android:layout_width="match_parent"
                            android:background="@drawable/shape_d8_kuang_5"
                            android:hint="@string/input_in_price"
                            android:inputType="numberDecimal"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/linStock1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:visibility="invisible">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:paddingVertical="@dimen/dp5">

                            <TextView
                                style="@style/text_12_black"
                                android:text="@string/goods_stock" />

                            <TextView
                                style="@style/text_10_999"
                                android:text="@string/base_unit_to_conversion" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tvStock1"
                            style="@style/text_12_black"
                            android:layout_width="match_parent"
                            android:background="@drawable/shape_f2_5"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5" />

                    </LinearLayout>

                </LinearLayout>

                <!--最近入库价-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1" />

                    <TextView
                        android:id="@+id/tvStockPrice1"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginTop="@dimen/dp5"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/in_price_recently_colon"
                        android:visibility="gone" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/price_sale" />

                    </LinearLayout>

                    <TextView
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/price_member" />

                    <TextView
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/price_online" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/etSalePrice1"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_price_sale"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                    <EditText
                        android:id="@+id/etMemberPrice1"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_price_member"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                    <EditText
                        android:id="@+id/etOnlinePrice1"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_price_online"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/start_order_online" />

                    <TextView
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/goods_specs" />

                    <TextView
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/abbr" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/etStartOrder1"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_start_order_online"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                    <EditText
                        android:id="@+id/etSpecs1"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_goods_specs"
                        android:inputType="text"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                    <EditText
                        android:id="@+id/etAbbr1"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_abbr"
                        android:inputType="text"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="@string/cashier_shelves" />

                        <ImageView
                            android:id="@+id/ivPrinter1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingVertical="@dimen/dp10"
                            android:paddingStart="@dimen/dp10"
                            android:src="@drawable/selector_checkbox002" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="@string/applet_shelves" />

                        <ImageView
                            android:id="@+id/ivApplet1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingVertical="@dimen/dp10"
                            android:paddingStart="@dimen/dp10"
                            android:src="@drawable/selector_checkbox002" />

                    </LinearLayout>

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1" />

                </LinearLayout>

            </LinearLayout>

            <!--最大包装-->
            <LinearLayout
                android:id="@+id/linSpecs2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/dp10"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="@dimen/dp3"
                        android:layout_height="@dimen/dp15"
                        android:layout_marginEnd="@dimen/dp5"
                        android:background="@drawable/shape_green_2" />

                    <TextView
                        style="@style/text_14_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/pack2"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvIn2"
                        style="@style/text_14_white"
                        android:layout_marginEnd="@dimen/dp10"
                        android:background="@drawable/shape_green_5"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/in"
                        android:visibility="gone"
                        app:drawableStartCompat="@mipmap/ic_in001" />

                    <TextView
                        android:id="@+id/tvOut2"
                        style="@style/text_14_white"
                        android:layout_marginEnd="@dimen/dp10"
                        android:background="@drawable/shape_orange_5"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/out"
                        android:visibility="gone"
                        app:drawableStartCompat="@mipmap/ic_out001" />

                    <TextView
                        android:id="@+id/tvPrint2"
                        style="@style/text_14_white"
                        android:layout_marginEnd="@dimen/dp10"
                        android:background="@drawable/shape_blue_5"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/print_tags"
                        android:visibility="gone"
                        app:drawableLeftCompat="@mipmap/ic_print001" />

                    <TextView
                        android:id="@+id/tvClear2"
                        style="@style/text_14_red"
                        android:background="@drawable/shape_red_kuang_5"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5"
                        android:text="@string/clear"
                        app:drawableLeftCompat="@mipmap/ic_del002" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/goods_barcode" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/goods_name" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/goods_unit" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:id="@+id/linBarcode2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/etBarcode2"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:background="@null"
                            android:hint="@string/input_goods_barcode"
                            android:inputType="number"
                            android:maxLines="1"
                            android:paddingVertical="@dimen/dp5"
                            android:paddingStart="@dimen/dp10" />

                        <ImageView
                            android:id="@+id/ivBarcode2"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:paddingHorizontal="@dimen/dp5"
                            android:src="@mipmap/ic_barcode001" />

                    </LinearLayout>

                    <EditText
                        android:id="@+id/etName2"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_goods_name"
                        android:inputType="text"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                    <LinearLayout
                        android:id="@+id/linUnit2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5">

                        <TextView
                            android:id="@+id/tvUnit2"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_marginEnd="@dimen/dp5"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:hint="@string/select_goods_unit"
                            android:maxLines="1" />

                        <ImageView
                            android:id="@+id/ivUnit2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/ic_arrow002" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:paddingVertical="@dimen/dp5">

                            <TextView
                                style="@style/text_12_red"
                                android:text="*" />

                            <TextView
                                style="@style/text_12_black"
                                android:text="@string/units" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <EditText
                                android:id="@+id/etCount2"
                                style="@style/text_12_black"
                                android:layout_width="0dp"
                                android:layout_marginEnd="@dimen/dp5"
                                android:layout_weight="1"
                                android:background="@drawable/shape_d8_kuang_5"
                                android:hint="@string/how_mush_base_pack"
                                android:inputType="number"
                                android:maxLines="1"
                                android:paddingHorizontal="@dimen/dp10"
                                android:paddingVertical="@dimen/dp5"
                                tools:ignore="NestedWeights" />

                            <TextView
                                style="@style/text_12_black"
                                android:text="@string/piece" />

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <LinearLayout
                            android:id="@+id/linInPrice2"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:paddingVertical="@dimen/dp5">

                            <TextView
                                style="@style/text_12_red"
                                android:text="*" />

                            <TextView
                                android:id="@+id/tvInPrice2"
                                style="@style/text_12_black"
                                android:text="@string/in_price" />

                            <ImageView
                                android:id="@+id/ivInPrice2"
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:src="@mipmap/ic_question001"
                                android:visibility="gone" />

                        </LinearLayout>

                        <EditText
                            android:id="@+id/etInPrice2"
                            style="@style/text_12_black"
                            android:layout_width="match_parent"
                            android:background="@drawable/shape_d8_kuang_5"
                            android:hint="@string/input_in_price"
                            android:inputType="numberDecimal"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/linStock2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:visibility="invisible">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:paddingVertical="@dimen/dp5">

                            <TextView
                                style="@style/text_12_black"
                                android:text="@string/goods_stock" />

                            <TextView
                                style="@style/text_10_999"
                                android:text="@string/base_unit_to_conversion" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tvStock2"
                            style="@style/text_12_black"
                            android:layout_width="match_parent"
                            android:background="@drawable/shape_f2_5"
                            android:paddingHorizontal="@dimen/dp10"
                            android:paddingVertical="@dimen/dp5" />

                    </LinearLayout>

                </LinearLayout>

                <!--最近入库价-->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1" />

                    <TextView
                        android:id="@+id/tvStockPrice2"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginTop="@dimen/dp5"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/in_price_recently_colon"
                        android:visibility="gone" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:text="@string/price_sale" />

                    </LinearLayout>

                    <TextView
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/price_member" />

                    <TextView
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/price_online" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/etSalePrice2"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_price_sale"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                    <EditText
                        android:id="@+id/etMemberPrice2"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_price_member"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                    <EditText
                        android:id="@+id/etOnlinePrice2"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_price_online"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/start_order_online" />

                    <TextView
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:text="@string/goods_specs" />

                    <TextView
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:text="@string/abbr" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/etStartOrder2"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_start_order_online"
                        android:inputType="numberDecimal"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                    <EditText
                        android:id="@+id/etSpecs2"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_goods_specs"
                        android:inputType="text"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                    <EditText
                        android:id="@+id/etAbbr2"
                        style="@style/text_12_black"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/shape_d8_kuang_5"
                        android:hint="@string/input_abbr"
                        android:inputType="text"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp5" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="@string/cashier_shelves" />

                        <ImageView
                            android:id="@+id/ivPrinter2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingVertical="@dimen/dp10"
                            android:paddingStart="@dimen/dp10"
                            android:src="@drawable/selector_checkbox002" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp10"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_red"
                            android:text="*" />

                        <TextView
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="@string/applet_shelves" />

                        <ImageView
                            android:id="@+id/ivApplet2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingVertical="@dimen/dp10"
                            android:paddingStart="@dimen/dp10"
                            android:src="@drawable/selector_checkbox002" />

                    </LinearLayout>

                    <View
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1" />

                </LinearLayout>

            </LinearLayout>

        </RelativeLayout>

    </androidx.core.widget.NestedScrollView>

</RelativeLayout>