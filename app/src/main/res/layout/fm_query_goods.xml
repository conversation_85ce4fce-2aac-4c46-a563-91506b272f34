<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/dp5"
        android:layout_weight="2"
        android:background="@drawable/shape_white_5"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp12">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/linCate0"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvCate0"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:hint="@string/select_cate"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp6"
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:id="@+id/ivCate0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linCate1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvCate1"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:hint="@string/select_cate"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp6"
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:id="@+id/ivCate1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linSupplier"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvSupplier"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:hint="@string/select_supplier"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp6"
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:id="@+id/ivSupplier"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/linStartDate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvStartDate"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:hint="@string/date_start"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp6"
                    android:text=""
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:id="@+id/ivStartDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linEndDate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvEndDate"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:hint="@string/date_end"
                    android:maxLines="1"
                    android:paddingVertical="@dimen/dp6"
                    android:text=""
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:id="@+id/ivEndDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvCount"
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_marginEnd="@dimen/dp10"
                android:layout_weight="1"
                android:text="@string/sale_count_total_colon"
                tools:ignore="NestedWeights" />

            <TextView
                android:id="@+id/tvTotal"
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="@string/sale_amount_total_colon" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvPurchase"
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_marginEnd="@dimen/dp10"
                android:layout_weight="1"
                android:text="@string/sale_purchase_total_colon" />

            <TextView
                android:id="@+id/tvProfit"
                style="@style/text_12_black"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="@string/sale_profit_total_colon" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:background="@drawable/shape_f5_top_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5">

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="2"
                android:gravity="center"
                android:text="@string/goods_name" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="2"
                android:gravity="center"
                android:text="@string/goods_barcode" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/sale_count" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/gift_count" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/sale_total" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/sale_purchase" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/profit_rate" />

        </LinearLayout>

        <include
            android:id="@+id/vSmartrefreshlayout"
            layout="@layout/layout_smartrefreshlayout" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/linInfo"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_white_5"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="@dimen/dp3"
                    android:layout_height="@dimen/dp15"
                    android:layout_marginEnd="@dimen/dp5"
                    android:background="@drawable/shape_green_2" />

                <TextView
                    style="@style/text_14_black"
                    android:text="@string/sale_statistics" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/sale_count_total_colon" />

                <TextView
                    android:id="@+id/tvCountInfo"
                    style="@style/text_12_black"
                    android:text="0" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/sale_amount_total_colon" />

                <TextView
                    android:id="@+id/tvTotalInfo"
                    style="@style/text_12_black"
                    android:text="0.00" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/sale_purchase_total_colon" />

                <TextView
                    android:id="@+id/tvPurchaseInfo"
                    style="@style/text_12_black"
                    android:text="0.00" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:text="@string/sale_profit_total_colon" />

                <TextView
                    android:id="@+id/tvProfitInfo"
                    style="@style/text_12_black"
                    android:text="0.00" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/dp5"
            android:background="@drawable/shape_white_5"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="@dimen/dp3"
                    android:layout_height="@dimen/dp15"
                    android:layout_marginEnd="@dimen/dp5"
                    android:background="@drawable/shape_green_2" />

                <TextView
                    style="@style/text_14_black"
                    android:text="@string/sale_order" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                android:background="@drawable/shape_f5_top_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <TextView
                    style="@style/text_10_666"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/order_time" />

                <TextView
                    style="@style/text_10_666"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/order_no" />

                <TextView
                    style="@style/text_10_666"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/sale_total" />

            </LinearLayout>

            <com.scwang.smart.refresh.layout.SmartRefreshLayout
                android:id="@+id/srlInfo"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <com.scwang.smart.refresh.header.ClassicsHeader
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvOrder"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                    <LinearLayout
                        android:id="@+id/linEmptyInfo"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingTop="@dimen/dp40"
                        android:visibility="gone">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/ic_empty" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp26"
                            android:textColor="@color/color_666"
                            android:textSize="@dimen/f14"
                            android:textStyle="bold" />

                    </LinearLayout>

                </RelativeLayout>

                <com.scwang.smart.refresh.footer.ClassicsFooter
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </com.scwang.smart.refresh.layout.SmartRefreshLayout>

        </LinearLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/tvNothing"
        style="@style/text_14_black"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@drawable/shape_white_5"
        android:gravity="center"
        android:text="@string/no_goods_information"
        android:visibility="visible" />

</LinearLayout>