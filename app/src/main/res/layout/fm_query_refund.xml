<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/dp10"
        android:layout_weight="2"
        android:background="@drawable/shape_white_5"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp12">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/linType"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvType"
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:paddingVertical="@dimen/dp6"
                    android:text="@string/operator"
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:id="@+id/ivType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/linStartDate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvStartDate"
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:hint="@string/date_start"
                    android:paddingVertical="@dimen/dp6"
                    android:text=""
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:id="@+id/ivStartDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

            <TextView
                style="@style/text_16_999"
                android:layout_marginHorizontal="@dimen/dp3"
                android:text="~" />

            <LinearLayout
                android:id="@+id/linEndDate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp10"
                android:layout_weight="1"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvEndDate"
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1"
                    android:hint="@string/date_end"
                    android:paddingVertical="@dimen/dp6"
                    android:text=""
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:id="@+id/ivEndDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp5"
                    android:src="@mipmap/ic_arrow002" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="2"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/etSearch"
                    style="@style/text_12_333"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@null"
                    android:drawableStart="@mipmap/ic_search002"
                    android:drawablePadding="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:hint="商品条码/商品名称/助记码"
                    android:imeOptions="actionSearch"
                    android:inputType="text"
                    android:maxLines="1"
                    android:paddingHorizontal="@dimen/dp10"
                    tools:ignore="NestedWeights" />

                <ImageView
                    android:id="@+id/ivSearchClear"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/dp4"
                    android:src="@mipmap/ic_close001"
                    android:visibility="gone" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp12"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                style="@style/text_14_black"
                android:text="@string/refund_total_colon"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvRefundTotal"
                style="@style/text_14_red"
                android:text="0.00"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:background="@drawable/shape_f5_top_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5">

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="2"
                android:gravity="center"
                android:text="@string/refund_time" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/operator" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="2"
                android:gravity="center"
                android:text="@string/refund_no" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/refund_type" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/refund_money" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/refund_count" />

            <TextView
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1.5"
                android:gravity="center"
                android:text="@string/member_mobile" />

        </LinearLayout>

        <include
            android:id="@+id/vSmartrefreshlayout"
            layout="@layout/layout_smartrefreshlayout" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/linInfo"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:visibility="gone">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_white_5"
            android:orientation="vertical"
            android:paddingStart="@dimen/dp10"
            tools:ignore="RtlSymmetry">

            <TextView
                android:id="@+id/tvName"
                style="@style/text_14_black"
                android:layout_marginTop="@dimen/dp10"
                android:text="@string/fit"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvUnique"
                style="@style/text_12_666"
                android:layout_below="@+id/tvName"
                android:layout_marginTop="@dimen/dp6"
                android:layout_marginBottom="@dimen/dp10"
                android:text=""
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvOrderType"
                style="@style/text_12_white"
                android:layout_alignParentEnd="true"
                android:background="@drawable/shape_green_topright_bottomleft_5"
                android:paddingHorizontal="@dimen/dp6"
                android:paddingVertical="@dimen/dp3"
                android:text="" />

        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp10"
            android:layout_weight="1"
            android:background="@drawable/shape_white_5"
            android:orientation="vertical"
            tools:ignore="NestedWeights">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp52"
                android:layout_marginTop="@dimen/dp6"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp1">

                <TextView
                    android:id="@+id/tvGoods"
                    style="@style/text_12_white"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:background="@drawable/shape_green_5"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/goods_information"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvOrder"
                    style="@style/text_12_black"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/order_information"
                    android:textStyle="bold" />

            </LinearLayout>

            <!--商品信息-->
            <LinearLayout
                android:id="@+id/linGoods"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/linGoodsTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp10"
                    android:background="@drawable/shape_f5_top_5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6">

                    <TextView
                        style="@style/text_10_666"
                        android:layout_width="0dp"
                        android:layout_weight="2"
                        android:text="@string/goods_name" />

                    <TextView
                        style="@style/text_10_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/price" />

                    <TextView
                        style="@style/text_10_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/count_weight" />

                    <TextView
                        style="@style/text_10_666"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/subtotal" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvGoods"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:background="@color/color_line" />

                <TextView
                    android:id="@+id/tvRefund"
                    style="@style/text_12_red"
                    android:layout_gravity="right"
                    android:padding="@dimen/dp10"
                    android:text="@string/refund_colon" />

            </LinearLayout>

            <!--订单信息-->
            <ScrollView
                android:id="@+id/svOrder"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="@dimen/dp10"
                android:layout_weight="1"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/dp10">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_centerVertical="true"
                            android:text="@string/order_no_old_colon" />

                        <TextView
                            android:id="@+id/tvOrderNo"
                            style="@style/text_12_green"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:drawablePadding="@dimen/dp5"
                            android:gravity="center_vertical"
                            android:text=""
                            app:drawableRightCompat="@mipmap/ic_arrow001" />

                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp8">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_centerVertical="true"
                            android:text="@string/order_money_colon" />

                        <TextView
                            android:id="@+id/tvOrderMoney"
                            style="@style/text_12_black"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:text="￥0.00" />

                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp8">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_centerVertical="true"
                            android:text="@string/refund_money_colon" />

                        <TextView
                            android:id="@+id/tvRefundMoney"
                            style="@style/text_12_red"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:text="-0.00" />

                    </RelativeLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            style="@style/text_12_666"
                            android:text="@string/refund_type_colon" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:paddingVertical="@dimen/dp8"
                            android:paddingEnd="@dimen/dp8"
                            android:src="@mipmap/ic_tips002" />

                        <TextView
                            android:id="@+id/tvRefundType"
                            style="@style/text_12_black"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="right"
                            android:text="" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/color_line" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp8">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_centerVertical="true"
                            android:text="@string/operator_colon" />

                        <TextView
                            android:id="@+id/tvOperator"
                            style="@style/text_12_black"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:text="" />

                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp8">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_centerVertical="true"
                            android:text="@string/operat_end_colon" />

                        <TextView
                            android:id="@+id/tvOperateEnd"
                            style="@style/text_12_black"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:text="" />

                    </RelativeLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:layout_marginTop="@dimen/dp8"
                        android:background="@color/color_line" />

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp8">

                        <TextView
                            style="@style/text_12_666"
                            android:layout_centerVertical="true"
                            android:text="@string/refund_no_colon" />

                        <TextView
                            android:id="@+id/tvRefundNo"
                            style="@style/text_12_black"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:text="" />

                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp8">

                        <TextView
                            android:id="@+id/tvRefundTime"
                            style="@style/text_12_666"
                            android:layout_centerVertical="true"
                            android:text="@string/refund_time_colon" />

                        <TextView
                            style="@style/text_12_black"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:text="" />

                    </RelativeLayout>

                </LinearLayout>

            </ScrollView>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:background="@drawable/shape_white_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp5">

            <TextView
                android:id="@+id/tvPrint"
                style="@style/text_14_white"
                android:layout_width="0dp"
                android:layout_marginHorizontal="@dimen/dp5"
                android:layout_weight="1"
                android:background="@drawable/shape_orange_5"
                android:gravity="center"
                android:paddingVertical="@dimen/dp12"
                android:text="@string/print_refund_receipt"
                android:textStyle="bold" />

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

        </LinearLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/tvNothing"
        style="@style/text_14_black"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@drawable/shape_white_5"
        android:gravity="center"
        android:text="@string/no_order_information" />

</LinearLayout>