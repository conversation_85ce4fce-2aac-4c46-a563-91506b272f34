<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/green">

        <ImageView
            android:id="@+id/ivLogo"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingHorizontal="@dimen/dp10"
            android:src="@mipmap/ic_logo004" />

        <TextView
            android:id="@+id/tvTitle"
            style="@style/text_18_white"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text="@string/hang_order"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toStartOf="@+id/tvCashier"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10">

            <TextClock
                style="@style/text_12_white"
                android:format24Hour="MM-dd HH:mm"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp2"
                android:background="@drawable/shape_black_tm_5"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp1">

                <ImageView
                    android:id="@+id/ivNetwork"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5" />

                <TextView
                    android:id="@+id/tvNetwork"
                    style="@style/text_10_white"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tvCashier"
            style="@style/text_14_green"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/white"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp20"
            android:text="@string/cashier_table"
            android:textStyle="bold"
            app:drawableLeftCompat="@mipmap/ic_cashier001" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dp46"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/dp5"
            android:layout_marginBottom="@dimen/dp5"
            android:layout_weight="2"
            android:background="@drawable/shape_white_5"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/linMainOrder"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/shape_red_topleft_5"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp5"
                        android:src="@mipmap/ic_cart002" />

                    <TextView
                        style="@style/text_14_white"
                        android:text="@string/main_order" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@color/white"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp6"
                        android:src="@mipmap/ic_hang001" />

                    <TextView
                        style="@style/text_14_black"
                        android:text="@string/hang_order"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/linPreviousOrder"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/shape_orange_topright5"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp6"
                        android:src="@mipmap/ic_previous001" />

                    <TextView
                        style="@style/text_14_white"
                        android:text="@string/previous_order"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp6">

                <TextView
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_weight="2"
                    android:text="@string/goods_name" />

                <TextView
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/price" />

                <TextView
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:text="@string/count_weight" />

                <TextView
                    style="@style/text_12_666"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:text="@string/subtotal" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/color_line" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvInfo"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@color/white"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:ignore="NestedWeights" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_f2_bottom_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <LinearLayout
                    android:id="@+id/linDel"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="@dimen/dp5"
                    android:layout_weight="2"
                    android:background="@drawable/shape_red_5"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6">

                    <TextView
                        style="@style/text_14_white"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:text="@string/del"
                        android:textStyle="bold"
                        app:drawableLeftCompat="@mipmap/ic_del003" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/linPrint"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="@dimen/dp5"
                    android:layout_weight="2"
                    android:background="@drawable/shape_orange_5"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6">

                    <TextView
                        style="@style/text_14_white"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:text="@string/print"
                        android:textStyle="bold"
                        app:drawableLeftCompat="@mipmap/ic_print003" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/linConfirm"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="@dimen/dp5"
                    android:layout_weight="3"
                    android:background="@drawable/shape_green_5"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp6">

                    <TextView
                        style="@style/text_14_white"
                        android:drawablePadding="@dimen/dp5"
                        android:gravity="center_vertical"
                        android:text="@string/pick_order"
                        android:textStyle="bold"
                        app:drawableLeftCompat="@mipmap/ic_del003" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/dp5"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvType0"
                    style="@style/text_14_white"
                    android:layout_marginEnd="@dimen/dp1"
                    android:background="@drawable/shape_green_left_5"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/all"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvType1"
                    style="@style/text_14_black"
                    android:layout_marginEnd="@dimen/dp1"
                    android:background="@color/white"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/hang_order_fit"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvType2"
                    style="@style/text_14_black"
                    android:background="@drawable/shape_white_right_5"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp5"
                    android:text="@string/hang_order_member"
                    android:textStyle="bold" />

            </LinearLayout>

            <com.scwang.smart.refresh.layout.SmartRefreshLayout
                android:id="@+id/smartRefreshLayout"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                tools:ignore="NestedWeights">

                <com.scwang.smart.refresh.header.ClassicsHeader
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                    <LinearLayout
                        android:id="@+id/linEmpty"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingTop="@dimen/dp40"
                        android:visibility="gone">

                        <ImageView
                            android:id="@+id/ivEmpty"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@mipmap/ic_empty" />

                        <TextView
                            android:id="@+id/tvEmpty"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/dp26"
                            android:textColor="@color/color_666"
                            android:textSize="@dimen/f14"
                            android:textStyle="bold" />

                    </LinearLayout>

                </RelativeLayout>

                <com.scwang.smart.refresh.footer.ClassicsFooter
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </com.scwang.smart.refresh.layout.SmartRefreshLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/dp5"
                android:background="@drawable/shape_white_5"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp6">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp20"
                    android:orientation="vertical">

                    <TextView
                        style="@style/text_12_666"
                        android:text="@string/hang_order_count_colon" />

                    <TextView
                        android:id="@+id/tvCount"
                        style="@style/text_14_black"
                        android:text="0"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        style="@style/text_12_666"
                        android:text="@string/hang_order_total_colon" />

                    <TextView
                        android:id="@+id/tvTotal"
                        style="@style/text_14_black"
                        android:text="0.00"
                        android:textStyle="bold" />

                </LinearLayout>

                <TextView
                    android:id="@+id/tvClear"
                    style="@style/text_14_white"
                    android:background="@drawable/shape_red_5"
                    android:drawablePadding="@dimen/dp5"
                    android:gravity="center_vertical"
                    android:paddingHorizontal="@dimen/dp20"
                    android:paddingVertical="@dimen/dp10"
                    android:text="@string/hang_order_clear"
                    android:textStyle="bold"
                    app:drawableLeftCompat="@mipmap/ic_clear001" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>