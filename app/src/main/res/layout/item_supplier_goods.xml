<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="2.5dp"
    android:background="@drawable/shape_white_5"
    android:paddingBottom="@dimen/dp10">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10"
        android:paddingHorizontal="@dimen/dp5">

        <ImageView
            android:id="@+id/ivItemImg"
            android:layout_width="@dimen/dp36"
            android:layout_height="@dimen/dp36"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/dp5"
            android:background="@drawable/shape_f2_5"
            android:src="@mipmap/ic_default_img" />

        <TextView
            android:id="@+id/tvItemName"
            style="@style/text_12_black"
            android:layout_toEndOf="@+id/ivItemImg"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="" />

        <TextView
            android:id="@+id/tvItemBarcode"
            style="@style/text_10_666"
            android:layout_below="@+id/tvItemName"
            android:layout_marginTop="@dimen/dp5"
            android:layout_toEndOf="@+id/ivItemImg"
            android:text="" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp5"
        android:layout_marginTop="@dimen/dp56"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/linItemPrice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvItemShofar"
                style="@style/text_10_green"
                android:layout_marginTop="@dimen/dp1"
                android:text="@string/money"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvItemPrice"
                style="@style/text_12_green"
                android:text="0.00"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tvItemUnit"
                style="@style/text_12_666"
                android:text="" />

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/tvItemStock"
                style="@style/text_10_black"
                android:background="@drawable/shape_green_tm_left_2"
                android:paddingHorizontal="@dimen/dp5"
                android:text="0" />

            <TextView
                android:id="@+id/tvItemHouse"
                style="@style/text_10_white"
                android:background="@drawable/shape_green_right_2"
                android:paddingHorizontal="@dimen/dp2"
                android:text="@string/house" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linItemSales"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvItemCount"
                style="@style/text_10_666"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="@string/sales_month" />

            <ImageView
                android:id="@+id/ivItemCashier"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp6"
                android:src="@drawable/selector_shelve_cashier" />

            <ImageView
                android:id="@+id/ivItemApplet"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/selector_shelve_applet" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/linItemConfirm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp5"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvItemSuggest"
                style="@style/text_12_green"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:text="@string/price_suggest_colon" />

            <TextView
                android:id="@+id/tvItemConfirm"
                style="@style/text_12_white"
                android:background="@drawable/shape_green_5"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                android:text="@string/archived" />

        </LinearLayout>

    </LinearLayout>

    <ImageView
        android:id="@+id/ivItemScale"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="2.5dp"
        android:layout_marginTop="2.5dp"
        android:src="@mipmap/ic_scale002" />

</RelativeLayout>