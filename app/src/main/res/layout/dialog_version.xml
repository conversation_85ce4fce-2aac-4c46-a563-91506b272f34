<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@mipmap/ic_version_bg001"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="2.5"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/dp20"
        android:paddingTop="@dimen/dp15"
        android:paddingBottom="@dimen/dp20">

        <TextView
            android:id="@+id/tvDialogVersionNew"
            style="@style/text_18_black"
            android:text="@string/version_find"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvDialogVersion"
            style="@style/text_12_666"
            android:layout_marginTop="@dimen/dp8"
            android:text="@string/version_now" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/dp10"
            android:layout_weight="1"
            tools:ignore="NestedWeights">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <View
                    android:layout_width="@dimen/dp2"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="@dimen/dp8"
                    android:background="#D8D8D8" />

                <TextView
                    android:id="@+id/tvDialogContent"
                    style="@style/text_12_666"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="" />

            </LinearLayout>

        </ScrollView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp20"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/versionchecklib_version_dialog_cancel"
                style="@style/text_12_999"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/dp10"
                android:background="@drawable/shape_d8_kuang_5"
                android:gravity="center"
                android:padding="@dimen/dp10"
                android:text="@string/version_later" />

            <TextView
                android:id="@+id/versionchecklib_version_dialog_commit"
                style="@style/text_12_white"
                android:layout_height="match_parent"
                android:background="@drawable/shape_green_5"
                android:gravity="center"
                android:padding="@dimen/dp10"
                android:text="@string/version_update"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_weight="1" />

</LinearLayout>