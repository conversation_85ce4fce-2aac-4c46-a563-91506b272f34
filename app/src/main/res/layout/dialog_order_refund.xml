<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/order_refund"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <LinearLayout
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvDialogTotal"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="@string/order_total_colon" />

        <TextView
            style="@style/text_12_black"
            android:text="@string/refund_print_receipts"
            android:textStyle="bold"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/ivDialogPrint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp10"
            android:src="@drawable/selector_checkbox002"
            android:visibility="gone" />

    </LinearLayout>

    <TextView
        android:id="@+id/tvDialogPayment"
        style="@style/text_12_black"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp10"
        android:text="@string/collection_type_colon" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_12_black"
            android:text="@string/refund_money_colon" />

        <LinearLayout
            android:layout_width="@dimen/dp100"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_green_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="@dimen/dp4">

            <TextView
                android:id="@+id/tvDialogRefund"
                style="@style/text_12_black"
                android:text="" />

            <ImageView
                android:id="@+id/ivDialogCursor"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp15"
                android:src="@drawable/anim_cursor" />

            <TextView
                android:id="@+id/tvDialogRefundHint"
                style="@style/text_12_black"
                android:hint="@string/input"
                android:text="" />

        </LinearLayout>

    </LinearLayout>

    <TextView
        style="@style/text_12_black"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp10"
        android:text="@string/refund_type"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvDialogJinQ"
            style="@style/text_12_white"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:background="@drawable/shape_green_5"
            android:gravity="center"
            android:padding="@dimen/dp5"
            android:text="@string/jinquan_plat" />

        <TextView
            android:id="@+id/tvDialogStore"
            style="@style/text_12_white"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:background="@drawable/shape_green_5"
            android:gravity="center"
            android:padding="@dimen/dp5"
            android:text="@string/stored_card"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvDialogCash"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:background="@drawable/shape_d8_kuang_f0_5"
            android:gravity="center"
            android:padding="@dimen/dp5"
            android:text="@string/cash" />

        <TextView
            android:id="@+id/tvDialogAlipay"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="@dimen/dp5"
            android:layout_weight="1"
            android:background="@drawable/shape_d8_kuang_f0_5"
            android:gravity="center"
            android:padding="@dimen/dp5"
            android:text="@string/alipay"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tvDialogWechat"
            style="@style/text_12_black"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/shape_d8_kuang_f0_5"
            android:gravity="center"
            android:padding="@dimen/dp5"
            android:text="@string/wechat"
            android:visibility="gone" />

    </LinearLayout>

    <TextView
        android:id="@+id/tvDialogTips"
        style="@style/text_12_999"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp10"
        android:text="" />

    <com.yxl.cashier_retail.view.NumberKeyBoardView
        android:id="@+id/numberKeyBoardView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:layout_marginTop="@dimen/dp15"
        android:layout_marginBottom="@dimen/dp5" />

</LinearLayout>