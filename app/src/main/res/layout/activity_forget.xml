<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@mipmap/ic_login_bg001">

    <TextView
        android:id="@+id/tvBack"
        style="@style/text_14_white"
        android:layout_marginStart="@dimen/dp10"
        android:layout_marginTop="@dimen/dp10"
        android:layout_marginEnd="@dimen/dp10"
        android:layout_marginBottom="@dimen/dp10"
        android:background="@drawable/shape_orange_5"
        android:drawableLeft="@mipmap/ic_back001"
        android:drawablePadding="@dimen/dp5"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="@dimen/dp8"
        android:text="@string/backHome" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:layout_gravity="center"
        android:orientation="horizontal">

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1" />

                <TextView
                    style="@style/text_16_black"
                    android:layout_width="0dp"
                    android:layout_weight="3"
                    android:gravity="center"
                    android:text="@string/pwd_forget"
                    android:textStyle="bold"
                    tools:ignore="RtlHardcoded" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp24"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:text="@string/login_account"
                    tools:ignore="RtlHardcoded" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/etAccount"
                        style="@style/text_14_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:background="@null"
                        android:hint="@string/input_mobile"
                        android:inputType="phone"
                        android:maxLength="11"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp8"
                        tools:ignore="NestedWeights,TextFields,TouchTargetSizeCheck" />

                    <ImageView
                        android:id="@+id/ivAccountClear"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/widget_size_6"
                        android:src="@mipmap/ic_close001"
                        android:visibility="gone" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:text="@string/code"
                    tools:ignore="RtlHardcoded" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/etCode"
                        style="@style/text_14_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:background="@null"
                        android:hint="@string/input_code"
                        android:inputType="number"
                        android:maxLength="6"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp8"
                        tools:ignore="NestedWeights,TouchTargetSizeCheck" />

                    <ImageView
                        android:id="@+id/ivCodeClear"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/widget_size_6"
                        android:src="@mipmap/ic_close001"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tvCode"
                        style="@style/text_14_green"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:paddingHorizontal="@dimen/dp10"
                        android:text="@string/code_gain" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp16"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    style="@style/text_14_black"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1"
                    android:gravity="right"
                    android:text="@string/new_pwd"
                    tools:ignore="RtlHardcoded" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="3"
                    android:background="@drawable/shape_d8_kuang_5"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/etPwd"
                        style="@style/text_14_333"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:background="@null"
                        android:hint="@string/input_new_pwd"
                        android:inputType="textPassword"
                        android:maxLength="16"
                        android:maxLines="1"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="@dimen/dp8"
                        tools:ignore="NestedWeights,TouchTargetSizeCheck" />

                    <ImageView
                        android:id="@+id/ivPwdClear"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/widget_size_6"
                        android:src="@mipmap/ic_close001"
                        android:visibility="gone" />

                    <ImageView
                        android:id="@+id/ivEye"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dp6"
                        android:src="@drawable/selector_pwd_eye" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp24"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/tvConfirm"
                    style="@style/text_14_white"
                    android:layout_width="0dp"
                    android:layout_marginEnd="@dimen/dp10"
                    android:layout_weight="3"
                    android:background="@drawable/shape_green_tm_5"
                    android:gravity="center"
                    android:paddingVertical="@dimen/dp8"
                    android:text="@string/confirm_edit" />

            </LinearLayout>

        </LinearLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

    </LinearLayout>

</RelativeLayout>