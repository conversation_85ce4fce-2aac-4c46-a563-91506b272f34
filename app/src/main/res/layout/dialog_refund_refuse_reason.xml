<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/refuse_reason"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56"
        android:background="@drawable/shape_d8_kuang_5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <EditText
            android:id="@+id/etDialog"
            style="@style/text_14_black"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:background="@null"
            android:gravity="center_vertical"
            android:hint="@string/input_refuse_reason"
            android:imeOptions="actionDone"
            android:inputType="text"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/dp10"
            android:paddingVertical="@dimen/dp5"
            tools:ignore="NestedWeights" />

        <ImageView
            android:id="@+id/ivDialogClear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/dp6"
            android:src="@mipmap/ic_close001"
            android:visibility="gone" />

    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp56">

        <TextView
            style="@style/text_12_black"
            android:layout_centerVertical="true"
            android:text="@string/usually_script" />

        <TextView
            android:id="@+id/tvDialogAdd"
            style="@style/text_12_green"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:padding="@dimen/dp10"
            android:text="@string/newly"
            android:textStyle="bold" />

    </RelativeLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/smartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="@dimen/dp56">

        <com.scwang.smart.refresh.header.ClassicsHeader
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            <LinearLayout
                android:id="@+id/linEmpty"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingTop="@dimen/dp40"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/ivEmpty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_empty" />

                <TextView
                    android:id="@+id/tvEmpty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp26"
                    android:textColor="@color/color_666"
                    android:textSize="@dimen/f14"
                    android:textStyle="bold" />

            </LinearLayout>

        </RelativeLayout>

        <com.scwang.smart.refresh.footer.ClassicsFooter
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</LinearLayout>