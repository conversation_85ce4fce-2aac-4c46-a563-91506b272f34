<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_white_10"
    android:gravity="center"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/text_16_black"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginVertical="@dimen/dp16"
            android:text="@string/order_refund"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/ivDialogClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:src="@mipmap/ic_close002" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp10"
        android:background="@drawable/shape_f5_top_5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_marginStart="@dimen/dp10"
            android:layout_weight="1.5"
            android:text="@string/goods_name" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/goods_barcode" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/goods_price" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/goods_count" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/goods_total" />

        <TextView
            style="@style/text_10_666"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/refunded_count" />

        <TextView
            style="@style/text_10_white"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:background="@color/red"
            android:gravity="center"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/refund_goods_count" />

        <TextView
            style="@style/text_10_white"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@color/red"
            android:gravity="center"
            android:paddingVertical="@dimen/dp5"
            android:text="@string/refund_goods_total" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvDialog"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/dp10"
        android:layout_weight="1"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    <TextView
        android:id="@+id/tvDialogTotal"
        style="@style/text_14_red"
        android:text="@string/rtotal_refund_colon"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp156"
        android:layout_marginVertical="@dimen/dp5"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <RelativeLayout
            android:id="@+id/relDialogAll"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginVertical="@dimen/dp5"
            android:background="@drawable/shape_red_5"
            android:orientation="vertical">

            <TextView
                style="@style/text_16_white"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:layout_marginHorizontal="@dimen/dp10"
                android:text="@string/whole_order_refund"
                android:textStyle="bold" />

            <TextView
                style="@style/text_10_white"
                android:layout_above="@+id/tvDialogAll"
                android:layout_centerHorizontal="true"
                android:text="@string/total" />

            <TextView
                android:id="@+id/tvDialogAll"
                style="@style/text_10_white"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="@dimen/dp5"
                android:text="0.00" />

        </RelativeLayout>

        <com.yxl.cashier_retail.view.NumberKeyBoardView
            android:id="@+id/numberKeyBoardView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </LinearLayout>

</LinearLayout>