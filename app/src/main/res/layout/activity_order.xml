<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_1E201E"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp41"
        android:background="@color/green">

        <ImageView
            android:id="@+id/ivLogo"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:paddingHorizontal="@dimen/dp10"
            android:src="@mipmap/ic_logo004" />

        <LinearLayout
            android:id="@+id/linSearch"
            android:layout_width="@dimen/dp200"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/ivLogo"
            android:background="@drawable/shape_d8_kuang_5"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/etSearch"
                style="@style/text_12_333"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:background="@null"
                android:drawableStart="@mipmap/ic_search002"
                android:drawablePadding="@dimen/dp5"
                android:gravity="center_vertical"
                android:hint="@string/search_tips_default"
                android:imeOptions="actionSearch"
                android:inputType="text"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                tools:ignore="NestedWeights" />

            <ImageView
                android:id="@+id/ivSearchClear"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/dp5"
                android:src="@mipmap/ic_close001"
                android:visibility="gone" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvTitle"
            style="@style/text_18_white"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:text="@string/netlist_order"
            android:textStyle="bold" />

        <LinearLayout
            android:id="@+id/linDate"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_toStartOf="@+id/tvCashier"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/dp10">

            <TextClock
                style="@style/text_12_white"
                android:format24Hour="MM-dd HH:mm"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp2"
                android:background="@drawable/shape_black_tm_5"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp1">

                <ImageView
                    android:id="@+id/ivNetwork"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/dp5" />

                <TextView
                    android:id="@+id/tvNetwork"
                    style="@style/text_10_white"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/tvCashier"
            style="@style/text_14_green"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="@color/white"
            android:drawablePadding="@dimen/dp5"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/dp20"
            android:text="@string/cashier_table"
            android:textStyle="bold"
            app:drawableLeftCompat="@mipmap/ic_cashier001" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="@dimen/dp64"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dp46"
        android:layout_marginBottom="@dimen/dp5"
        android:background="@drawable/shape_white_5"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/relOrder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_green_5"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/ivOrder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp10"
                android:src="@mipmap/ic_order_tab001" />

            <TextView
                android:id="@+id/tvOrder"
                style="@style/text_12_white"
                android:layout_below="@+id/ivOrder"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp3"
                android:layout_marginBottom="@dimen/dp10"
                android:gravity="center"
                android:text="@string/netlist_order" />

            <TextView
                android:id="@+id/tvOrderCount"
                style="@style/text_8_white"
                android:layout_width="@dimen/dp15"
                android:layout_height="@dimen/dp15"
                android:layout_alignParentEnd="true"
                android:layout_marginStart="@dimen/dp5"
                android:layout_marginTop="@dimen/dp5"
                android:layout_marginEnd="@dimen/dp5"
                android:layout_marginBottom="@dimen/dp5"
                android:background="@drawable/shape_yuan_red"
                android:gravity="center"
                android:text="0"
                android:visibility="gone" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/relRefund"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/ivRefund"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp10"
                android:src="@mipmap/ic_order_tab012" />

            <TextView
                android:id="@+id/tvRefund"
                style="@style/text_12_666"
                android:layout_below="@+id/ivRefund"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp3"
                android:layout_marginBottom="@dimen/dp10"
                android:gravity="center"
                android:text="@string/netlist_refund" />

            <TextView
                android:id="@+id/tvRefundCount"
                style="@style/text_8_white"
                android:layout_width="@dimen/dp15"
                android:layout_height="@dimen/dp15"
                android:layout_alignParentEnd="true"
                android:layout_margin="@dimen/dp5"
                android:background="@drawable/shape_yuan_red"
                android:gravity="center"
                android:text="0"
                android:visibility="gone" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/relRider"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/ivRider"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp10"
                android:src="@mipmap/ic_order_tab022" />

            <TextView
                android:id="@+id/tvRider"
                style="@style/text_12_666"
                android:layout_below="@+id/ivRider"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp3"
                android:layout_marginBottom="@dimen/dp10"
                android:gravity="center"
                android:text="@string/rider_manage" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/relBeans"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/ivBeans"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp10"
                android:src="@mipmap/ic_order_tab032" />

            <TextView
                android:id="@+id/tvBeans"
                style="@style/text_12_666"
                android:layout_below="@+id/ivBeans"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp3"
                android:layout_marginBottom="@dimen/dp10"
                android:gravity="center"
                android:text="@string/beans" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/relDelivery"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/ivDelivery"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp10"
                android:src="@mipmap/ic_order_tab042" />

            <TextView
                android:id="@+id/tvDelivery"
                style="@style/text_12_666"
                android:layout_below="@+id/ivDelivery"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp3"
                android:layout_marginBottom="@dimen/dp10"
                android:gravity="center"
                android:text="@string/delivery_setting" />

        </RelativeLayout>

    </LinearLayout>

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/dp69"
        android:layout_marginTop="@dimen/dp46"
        android:layout_marginEnd="@dimen/dp5"
        android:layout_marginBottom="@dimen/dp5" />

</RelativeLayout>