<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/blue</item>
        <item name="colorPrimaryDark">@color/blue</item>
        <item name="colorAccent">@color/blue</item>
        <!--        <item name="android:windowAnimationStyle">@style/dialogAnim</item>-->
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <!--默认dialog样式-->
    <style name="dialog_style" parent="android:style/Theme.Dialog">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <!--底部滑出dialog-->
    <style name="dialog_bottom" parent="@style/Base.V7.Theme.AppCompat.Light.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <!--dialog滑出动画-->
    <style name="dialog_anim">
        <item name="android:windowEnterAnimation">@anim/actionsheet_dialog_in</item>
        <item name="android:windowExitAnimation">@anim/actionsheet_dialog_out</item>
    </style>

    <style name="text_10_333">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f10</item>
        <item name="android:textColor">@color/color_333</item>
    </style>

    <style name="text_12_333">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f12</item>
        <item name="android:textColor">@color/color_333</item>
    </style>

    <style name="text_14_333">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f14</item>
        <item name="android:textColor">@color/color_333</item>
    </style>

    <style name="text_16_333">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f16</item>
        <item name="android:textColor">@color/color_333</item>
    </style>

    <style name="text_18_333">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f18</item>
        <item name="android:textColor">@color/color_333</item>
    </style>

    <style name="text_10_666">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f10</item>
        <item name="android:textColor">@color/color_666</item>
    </style>

    <style name="text_12_666">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f12</item>
        <item name="android:textColor">@color/color_666</item>
    </style>

    <style name="text_14_666">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f14</item>
        <item name="android:textColor">@color/color_666</item>
    </style>

    <style name="text_16_666">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f16</item>
        <item name="android:textColor">@color/color_666</item>
    </style>

    <style name="text_18_666">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f18</item>
        <item name="android:textColor">@color/color_666</item>
    </style>

    <style name="text_10_999">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f10</item>
        <item name="android:textColor">@color/color_999</item>
    </style>

    <style name="text_12_999">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f12</item>
        <item name="android:textColor">@color/color_999</item>
    </style>

    <style name="text_14_999">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f14</item>
        <item name="android:textColor">@color/color_999</item>
    </style>

    <style name="text_16_999">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f16</item>
        <item name="android:textColor">@color/color_999</item>
    </style>

    <style name="text_18_999">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f18</item>
        <item name="android:textColor">@color/color_999</item>
    </style>

    <style name="text_8_white">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f8</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="text_10_white">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f10</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="text_12_white">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f12</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="text_14_white">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f14</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="text_16_white">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f16</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="text_18_white">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f18</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="text_10_black">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f10</item>
        <item name="android:textColor">@color/black</item>
    </style>

    <style name="text_12_black">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f12</item>
        <item name="android:textColor">@color/black</item>
    </style>

    <style name="text_14_black">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f14</item>
        <item name="android:textColor">@color/black</item>
    </style>

    <style name="text_16_black">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f16</item>
        <item name="android:textColor">@color/black</item>
    </style>

    <style name="text_18_black">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f18</item>
        <item name="android:textColor">@color/black</item>
    </style>

    <style name="text_10_blue">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f10</item>
        <item name="android:textColor">@color/blue</item>
    </style>

    <style name="text_12_blue">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f12</item>
        <item name="android:textColor">@color/blue</item>
    </style>

    <style name="text_14_blue">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f14</item>
        <item name="android:textColor">@color/blue</item>
    </style>

    <style name="text_16_blue">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f16</item>
        <item name="android:textColor">@color/blue</item>
    </style>

    <style name="text_18_blue">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f18</item>
        <item name="android:textColor">@color/blue</item>
    </style>

    <style name="text_8_red">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f8</item>
        <item name="android:textColor">@color/red</item>
    </style>

    <style name="text_10_red">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f10</item>
        <item name="android:textColor">@color/red</item>
    </style>

    <style name="text_12_red">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f12</item>
        <item name="android:textColor">@color/red</item>
    </style>

    <style name="text_14_red">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f14</item>
        <item name="android:textColor">@color/red</item>
    </style>

    <style name="text_16_red">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f16</item>
        <item name="android:textColor">@color/red</item>
    </style>

    <style name="text_18_red">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f18</item>
        <item name="android:textColor">@color/red</item>
    </style>

    <style name="text_10_green">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f10</item>
        <item name="android:textColor">@color/green</item>
    </style>

    <style name="text_12_green">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f12</item>
        <item name="android:textColor">@color/green</item>
    </style>

    <style name="text_14_green">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f14</item>
        <item name="android:textColor">@color/green</item>
    </style>

    <style name="text_16_green">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f16</item>
        <item name="android:textColor">@color/green</item>
    </style>

    <style name="text_18_green">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f18</item>
        <item name="android:textColor">@color/green</item>
    </style>

    <style name="text_8_orange">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f8</item>
        <item name="android:textColor">@color/orange</item>
    </style>

    <style name="text_10_orange">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f10</item>
        <item name="android:textColor">@color/orange</item>
    </style>

    <style name="text_12_orange">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f12</item>
        <item name="android:textColor">@color/orange</item>
    </style>

    <style name="text_14_orange">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f14</item>
        <item name="android:textColor">@color/orange</item>
    </style>

    <style name="text_16_orange">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f16</item>
        <item name="android:textColor">@color/orange</item>
    </style>

    <style name="text_18_orange">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/f18</item>
        <item name="android:textColor">@color/orange</item>
    </style>

</resources>