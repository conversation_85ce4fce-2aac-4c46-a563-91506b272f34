<!-- res/values/attrs.xml -->
<resources>
    <!--圆弧形进度条start-->
    <!-- 是否开启抗锯齿 -->
    <attr name="antiAlias" format="boolean" />
    <!-- 圆弧起始角度，3点钟方向为0，顺时针递增，小于0或大于360进行取余 -->
    <attr name="startAngle" format="float" />
    <!-- 圆弧度数 -->
    <attr name="sweepAngle" format="float" />
    <!-- 设置动画时间 -->
    <attr name="animTime" format="integer" />
    <!-- 绘制内容的数值 -->
    <attr name="maxValue" format="float" />
    <attr name="value" format="float" />
    <!-- 绘制内容的单位 -->
    <attr name="unit" format="string|reference" />
    <attr name="unitSize" format="dimension" />
    <attr name="unitColor" format="color|reference" />
    <!-- 绘制内容相应的提示语 -->
    <attr name="hint" format="string|reference" />
    <attr name="hintSize" format="dimension" />
    <attr name="hintColor" format="color|reference" />
    <!-- 精度，默认为0 -->
    <attr name="precision" format="integer" />
    <attr name="valueSize" format="dimension" />
    <attr name="valueColor" format="color|reference" />
    <!-- 背景圆弧颜色，默认白色 -->
    <attr name="bgArcColor" format="color|reference" />
    <!-- 圆弧宽度 -->
    <attr name="arcWidth" format="dimension" />
    <!-- 圆弧颜色， -->
    <attr name="arcColors" format="color|reference" />
    <!-- 文字的偏移量 相对于圆半径而言，默认三分之一 -->
    <attr name="textOffsetPercentInRadius" format="float" />
    <!-- 背景圆弧宽度 -->
    <attr name="bgArcWidth" format="dimension" />
    <!-- 线条数 -->
    <attr name="dottedLineCount" format="integer" />
    <!-- 圆弧跟虚线之间的距离 -->
    <attr name="lineDistance" format="integer" />
    <!-- 线条宽度 -->
    <attr name="dottedLineWidth" format="dimension" />
    <!-- 进度条的前景色 起始颜色-->
    <attr name="foreStartColor" format="color" />
    <!-- 进度条的前景色 结束颜色-->
    <attr name="foreEndColor" format="color" />

    <declare-styleable name="CircleProgressBar">
        <attr name="antiAlias" />
        <attr name="startAngle" />
        <attr name="sweepAngle" />
        <attr name="animTime" />
        <attr name="maxValue" />
        <attr name="value" />
        <attr name="unit" />
        <attr name="unitSize" />
        <attr name="unitColor" />
        <attr name="hint" />
        <attr name="hintSize" />
        <attr name="hintColor" />
        <attr name="precision" />
        <attr name="valueSize" />
        <attr name="valueColor" />
        <attr name="bgArcColor" />
        <attr name="arcWidth" />
        <attr name="arcColors" />
        <attr name="textOffsetPercentInRadius" />
        <attr name="bgArcWidth" />
        <attr name="dottedLineCount" />
        <attr name="lineDistance" />
        <attr name="dottedLineWidth" />
        <attr name="foreStartColor" />
        <attr name="foreEndColor" />

    </declare-styleable>
    <!--圆弧形进度条end-->
</resources>