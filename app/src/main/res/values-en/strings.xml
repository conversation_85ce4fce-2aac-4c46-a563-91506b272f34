<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">Retail version cash register</string>
    <string name="Multi_Language">ABC</string>

    <!--公共start-->
    <string name="versionchecklib_progress" translatable="false">%d/100</string>
    <string name="cashier_table">check-out</string>
    <string name="money">$</string>
    <string name="cancel" tools:ignore="ButtonCase">cancel</string>
    <string name="confirm">confirm</string>
    <string name="retry">retry</string>
    <string name="clear">clear</string>
    <string name="balance">balance</string>
    <string name="points">integral</string>
    <string name="points_colon">integral:</string>
    <string name="not_write">Not filled in</string>
    <string name="save">save</string>
    <string name="recharge">recharge</string>
    <string name="sex0">secrecy</string>
    <string name="sex1">man</string>
    <string name="sex2">woman</string>
    <string name="confirm_add">Confirm addition</string>
    <string name="setting_not">not set</string>
    <string name="all">all</string>
    <string name="no">number</string>
    <string name="no_colon">number:</string>
    <string name="exchange">exchange</string>
    <string name="more">more</string>
    <string name="piece">piece</string>
    <string name="remain">remain</string>
    <string name="supplier">supplier</string>
    <string name="contacts">contacts</string>
    <string name="del">delete</string>
    <string name="input">Please enter</string>
    <string name="custom">custom</string>
    <string name="edit">edit</string>
    <string name="total_price">Total price</string>
    <string name="search_tips">Product barcode/product name/mnemonic code</string>
    <string name="connect_no">not connected</string>
    <string name="clear_goods">Empty product</string>
    <string name="total_count_colon">Total:</string>
    <string name="amount_colon">Total:</string>
    <string name="collection">Collection</string>
    <string name="generate_no">To be generated</string>
    <string name="generated">generated</string>
    <string name="canceled">Canceled</string>
    <string name="preview">Preview</string>
    <string name="time_create_colon">Creation time:</string>
    <string name="sort">sort</string>
    <string name="stock">stock</string>
    <string name="sales">sales</string>
    <string name="sales_month">Monthly sales:</string>
    <string name="supplier_colon">supplier:</string>
    <string name="specs">specifications</string>
    <string name="tips">prompt</string>
    <string name="confirm_passed">Confirmation passed</string>
    <string name="local">(local)</string>
    <string name="enable_status">Enabling status</string>
    <string name="settlement">settlement</string>
    <string name="classX">class</string>
    <string name="no_permission_tips">This function cannot be used because the permission is not enabled. Please enable it in the settings.</string>
    <string name="house">library</string>
    <string name="remarks">remarks</string>
    <string name="remarks_colon">remarks:</string>
    <string name="confirm_replace">Confirm replacement</string>
    <string name="km">km</string>
    <string name="yuan">Dollar</string>
    <string name="minute">minute</string>
    <string name="reset">reset</string>
    <string name="newly">newly added</string>
    <string name="add">add</string>
    <string name="copy">copy</string>
    <string name="operate">operate</string>
    <string name="newly_success">New successfully added</string>
    <string name="add_success">Added successfully</string>
    <string name="search_tips_default">Please enter search keywords</string>
    <string name="day">day</string>
    <string name="connected">Connected</string>
    <string name="no_network">No network</string>
    <string name="renew">renew</string>
    <string name="confirm_del_img">Are you sure to delete this image?</string>
    <string name="refund_sign">refund</string>
    <string name="nothing">nothing</string>
    <string name="no_network_connect">No network connection</string>
    <string name="stock_not_enough">Insufficient inventory</string>
    <string name="submit_one_touch">One click submission</string>
    <string name="submit_success">Submitted successfully</string>
    <string name="print">Print</string>
    <string name="loading">Loading...</string>
    <string name="operate_success">Operation succeeded</string>
    <string name="name">Name</string>
    <string name="over">over</string>
    <string name="chong">Charge</string>
    <string name="give">Send</string>
    <!--公共end-->

    <!--菜单start-->
    <string name="cashier">Cashier</string>
    <string name="warehousing">Warehousing</string>
    <string name="query">query</string>
    <string name="statistics">statistics</string>
    <string name="netlist">netlist</string>
    <string name="member">member</string>
    <string name="replenishment">Replenishment</string>
    <string name="marketing">marketing</string>
    <string name="shift_handover">Shift handover</string>
    <string name="setting">set up</string>
    <string name="common">common</string>
    <!--菜单end-->

    <!--登录start-->
    <string name="kefu_contact">Contact service</string>
    <string name="kefu_phone_colon">Customer Service Phone:</string>
    <string name="login_tips">Intelligent new retail ecosystem platform</string>
    <string name="close_software">Close software</string>
    <string name="account_login">Account login</string>
    <string name="login_account">Login account</string>
    <string name="input_mobile">Please enter your phone number</string>
    <string name="login_pwd">Login password</string>
    <string name="input_pwd">Please enter your login password</string>
    <string name="pwd_remember">remember password</string>
    <string name="pwd_forget">forgot password</string>
    <string name="login">login</string>
    <string name="version">current version:</string>
    <string name="login_success">Login succeeded</string>
    <string name="login_fail">Login failed</string>
    <!--登录end-->

    <!--忘记密码start-->
    <string name="backHome">homepage</string>
    <string name="email_ads">e-mail address</string>
    <string name="input_email_ads">Please enter your email address</string>
    <string name="code">Code</string>
    <string name="input_code">Please enter the verification code</string>
    <string name="code_gain">Obtain verification code</string>
    <string name="new_pwd">New password</string>
    <string name="input_new_pwd">Please enter a new password</string>
    <string name="confirm_edit">Confirm modifications</string>
    <!--忘记密码end-->

    <!--主函数（收银）start-->
    <string name="main_order">MAWB</string>
    <string name="hang_order">Registration</string>
    <string name="previous_order">Previous order</string>
    <string name="barcode_verify">Barcode verification</string>
    <string name="click_select_member">Click to select a member</string>
    <string name="no_here_goods">No products available</string>
    <string name="member_information_fail">Member information acquisition failed</string>
    <string name="balance_not_enough">balance not enough</string>
    <string name="sale_price_lower_in_price">Selling price lower than purchase price</string>
    <string name="open_points_exchange">Activate point redemption</string>
    <!--主函数（收银）end-->

    <!--挂单start-->
    <string name="pick_order">Pick up the order</string>
    <string name="hang_order_fit">Individual booking</string>
    <string name="hang_order_member">Member pending orders</string>
    <string name="hang_order_count_colon">Total number of pending orders:</string>
    <string name="hang_order_total_colon">Total amount of pending orders:</string>
    <string name="hang_order_clear">Clear pending orders</string>
    <!--挂单end-->

    <!--版本更新弹窗start-->
    <string name="version_newed">It is already the latest version</string>
    <string name="version_find">Discovering the latest version</string>
    <string name="version_now">current version</string>
    <string name="version_later">Update later</string>
    <string name="version_update">Immediate Updating</string>
    <string name="version_download_fail">Download failed</string>
    <string name="version_download_tips">Please ensure that the network is in good condition and do not close the current page during the download process</string>
    <string name="version_downloading">Downloading…</string>
    <string name="version_notification_title">Gold Circle Cashier Upgrade</string>
    <string name="version_notification_content">Upgrading</string>
    <!--版本更新弹窗end-->

    <!--折扣start-->
    <string name="discounts">discounts</string>
    <string name="discounts_order">Whole order discount</string>
    <string name="discounts_order_edit">Whole order discount editing</string>
    <string name="discounts_common">Common discounts (discounts)</string>
    <string name="zero_default">Default erase</string>
    <string name="zero_no">No erasure</string>
    <string name="zero_1_yuan">Zero to 1 yuan</string>
    <string name="zero_5_corner">Zero to 5 corners</string>
    <string name="zero_1_corner">Zero to 1 corner</string>
    <!--折扣end-->

    <!--入库start-->
    <string name="place_search_goods">Please search for the product first</string>
    <!--批量出库-->
    <string name="no_out_information">No stock-out information temporarily</string>
    <string name="click_goods_to_out">Click the commodity to issue</string>
    <string name="out_batch_tips">Only the approved commodity inventory, average inventory price, modified selling price, etc. after stock-out will be changed and become effective</string>
    <string name="out_total_colon">Total receipt price:</string>
    <string name="print_out_order">Print the delivery order</string>
    <string name="confirm_out">Confirm stock-out</string>
    <!--批量入库-->
    <string name="in_batch">Batch storage</string>
    <string name="no_in_information">No inventory information available at the moment</string>
    <string name="click_goods_to_in">Click on the product to enter the inventory</string>
    <string name="guess_in_goods">Guess the product you want to store in the warehouse</string>
    <string name="in_batch_tips">The changes will only take effect after the inventory of the goods, the average inventory price, and the modified selling price are reviewed and approved upon receipt</string>
    <string name="in_total_colon">Total inventory price:</string>
    <string name="print_in_order">Print warehouse receipt</string>
    <string name="confirm_in">Confirm warehouse entry</string>
    <string name="select_goods">Please select a product</string>
    <!--购销单-->
    <string name="purchase">Purchase and sales order</string>
    <string name="purchase_order_status1">Waiting for confirmation of receipt and storage</string>
    <string name="purchase_order_status1_tips">Please carefully check the quantity and amount of the goods</string>
    <string name="purchase_order_status2">Waiting for merchant payment</string>
    <string name="purchase_order_status2_tips">All products have been stored in the warehouse</string>
    <string name="purchase_order_status3">The documents and vouchers have been uploaded</string>
    <string name="purchase_order_status3_tips">Waiting for the supplier to confirm receipt of the goods</string>
    <string name="ordered_count">Order quantity</string>
    <string name="delivery_count">Delivery quantity</string>
    <string name="goods_subtotal">Subtotal of goods</string>
    <string name="price_suggest">List price</string>
    <string name="paid_in_count">Received quantity</string>
    <string name="ordered_total">Purchase amount</string>
    <string name="payable_total">Payable amount</string>
    <string name="settlement_discount">Settlement discounts</string>
    <string name="pending_total">Pending amount</string>
    <string name="purchase_no">Purchase and sales order number</string>
    <string name="time_create">Creation time</string>
    <string name="time_update">update time</string>
    <string name="purchase_voucher">Document voucher</string>
    <string name="confirm_goods_in">Confirm the goods have been stored in the warehouse</string>
    <string name="repayment">repayment</string>
    <string name="purchase_voucher_look">View documents and vouchers</string>
    <string name="batch_out">Batch issue</string>
    <!--入库end-->

    <!--查询start-->
    <string name="query_order">Check orders</string>
    <string name="query_refund">Check refund</string>
    <string name="query_goods">Check products</string>
    <string name="query_shift">Check handover</string>
    <string name="query_batch">Batch check</string>
    <string name="query_chu_ru">Find out and put it into storage</string>
    <!--查订单-->
    <string name="collection_type">payment method</string>
    <string name="collection_type_colon">payment method:</string>
    <string name="date_start">Start date</string>
    <string name="date_end">End date</string>
    <string name="sales_overview">Sales Overview</string>
    <string name="business_money_colon">turnover:</string>
    <string name="order_count">Order Quantity</string>
    <string name="order_count_colon">Order Quantity:</string>
    <string name="order_profit_colon">Order profit:</string>
    <string name="business_overview">Business Overview</string>
    <string name="member_recharge_colon">Member recharge:</string>
    <string name="member_consumption_colon">Member consumption:</string>
    <string name="points_goods_colon">Integral products:</string>
    <string name="refund">refund</string>
    <string name="refund_colon">refund:</string>
    <string name="debt_colon">debt:</string>
    <string name="no_password_colon">no password:</string>
    <string name="applet_overview">Applet overview</string>
    <string name="online_income_colon">Online revenue:</string>
    <string name="to_be_credited_colon">To be credited:</string>
    <string name="withdrawn_colon">Withdrawn:</string>
    <string name="pending_withdrawn_colon">Pending withdrawal:</string>
    <string name="platform_coupons_colon">Platform coupons:</string>
    <string name="platform_beans_colon">Platform subsidy beans:</string>
    <string name="shop_coupons_colon">Shop coupons:</string>
    <string name="shop_beans_colon">Shop subsidy beans:</string>
    <string name="show_more_info">Show more information</string>
    <string name="hide_more_info">Put away more information</string>
    <string name="order_time">Order time</string>
    <string name="order_time_colon">Order time:</string>
    <string name="payment_info_colon">Payment details:</string>
    <string name="addressee">addressee</string>
    <string name="order_no">Order No.</string>
    <string name="order_no_colon">Order No.:</string>
    <string name="total_amount">Total amount</string>
    <string name="count">number</string>
    <string name="fit">FIT</string>
    <string name="goods_information">commodity information</string>
    <string name="order_information">Ordering Information</string>
    <string name="price">unit-price</string>
    <string name="count_weight">Quantity/Weight</string>
    <string name="weight">Weight</string>
    <string name="subtotal">subtotal</string>
    <string name="receivable_colon">receivable:</string>
    <string name="discount">discount</string>
    <string name="discount_colon">discount:</string>
    <string name="paid_in_colon">paid-in:</string>
    <string name="receivable_money">amount receivable</string>
    <string name="receivable_money_colon">amount receivable:</string>
    <string name="discount_money_colon">Discount amount:</string>
    <string name="beans_deduction_colon">Department Store Bean Deduction:</string>
    <string name="paid_in_money">Actual amount received</string>
    <string name="paid_in_money_colon">Actual amount received:</string>
    <string name="cashier_staff_colon">Cashier:</string>
    <string name="pay_time_colon">time of payment:</string>
    <string name="pay_type_colon">Payment method:</string>
    <string name="out_batch">Outbound Batch</string>
    <string name="out_batch_colon">Outbound batch:</string>
    <string name="print_receipt">Print receipts</string>
    <string name="order_refund">Order refund</string>
    <string name="no_order_information">There is currently no order information available</string>
    <string name="points_exchange">Points redemption</string>
    <string name="jinquan_plat">Golden Circle Platform</string>
    <string name="input_refund_total">Please enter the refund amount</string>
    <string name="refund_jinquan_plat_tips">Golden Circle Platform: Orders paid by scanning the customer payment code will have the refund amount refunded through the original path</string>
    <string name="refund_stored_tips">Prepaid card: refund to customer prepaid card</string>
    <string name="refund_cash_tips">Cash: Refund customers in cash</string>
    <string name="refund_alipay_tips">Alipay: refund customers by Alipay</string>
    <string name="refund_wechat_tips">WeChat: Use WeChat to refund customers</string>
    <string name="select_refund_goods">Please select the refund item</string>
    <!--查退款-->
    <string name="operator">operator</string>
    <string name="operator_colon">operator:</string>
    <string name="refund_total_colon">Total refund amount:</string>
    <string name="refund_time">Refund time</string>
    <string name="refund_time_colon">Refund time:</string>
    <string name="refund_no">Refund tracking number</string>
    <string name="refund_no_colon">Refund tracking number:</string>
    <string name="refund_type">Refund method</string>
    <string name="refund_type_colon">Refund method:</string>
    <string name="select_refund_type">Please select a refund method</string>
    <string name="refund_money">refund amount</string>
    <string name="refund_money_colon">refund amount:</string>
    <string name="refund_count">Refund quantity</string>
    <string name="order_no_old_colon">Original order number:</string>
    <string name="order_money_colon">Order amount:</string>
    <string name="operat_end_colon">Operating end:</string>
    <string name="print_refund_receipt">Print refund receipt</string>
    <!--查商品-->
    <string name="cate_all">All categories</string>
    <string name="goods_cate_all_colon">Product category:</string>
    <string name="goods_total_colon">Total amount of goods:</string>
    <string name="sell_count">quantity sold</string>
    <string name="sell_total">Total selling price</string>
    <string name="profit">profit</string>
    <string name="profit_colon">profit:</string>
    <string name="sales_seven">7-day sales</string>
    <string name="sales_thirty">30 day sales</string>
    <string name="sales_day_colon">Average daily sales:</string>
    <string name="on_sales_batch">On sale batches</string>
    <string name="chu_ru_record">Entry and exit records</string>
    <string name="print_tags">Print price tags</string>
    <string name="goods_edit">Edit Product</string>
    <string name="no_goods_information">There is currently no product information available</string>
    <string name="sale_count_total_colon">Total sales quantity:</string>
    <string name="sale_amount_total_colon">Total sales amount:</string>
    <string name="sale_purchase_total_colon">Total cost of sales:</string>
    <string name="sale_profit_total_colon">Total sales profit:</string>
    <string name="sale_count">sales volumes</string>
    <string name="gift_count">Gift quantity</string>
    <string name="sale_purchase">Cost of sales</string>
    <string name="profit_rate">profit margin</string>
    <string name="sale_statistics">Sales statistics</string>
    <string name="sale_order">sale order</string>
    <!--查交班-->
    <string name="no_shift_information">No handover information available at the moment</string>
    <string name="cashier_all">All cashiers</string>
    <string name="select_cashier">Please select cashier</string>
    <string name="input_cashier_no">Please enter the cashier number</string>
    <string name="cashier_name">Cashier name</string>
    <string name="cashier_no">Cashier ID</string>
    <string name="time_shift_login">Succession time</string>
    <string name="time_shift_out">Handover time</string>
    <string name="business_money">turnover</string>
    <string name="cashiering">In the cash register</string>
    <string name="recharge_statistics">Recharge statistics</string>
    <string name="way">mode</string>
    <string name="amount_money">amount of money</string>
    <string name="cashier_statistics">Cashier statistics</string>
    <string name="cashier_order">Cashier order</string>
    <string name="sale_total">sales amount</string>
    <string name="print_shift_receipt">Print shift handover receipt</string>
    <!--查出入库start-->
    <string name="type_all">All types</string>
    <string name="type">Type</string>
    <string name="operate_time">Operating time</string>
    <string name="operate_before_count">Quantity before operation</string>
    <string name="operate_after_count">Quantity after operation</string>
    <string name="operate_source">Operation source</string>
    <string name="operate_way">Mode of operation</string>
    <string name="in_amount_colon">Total receipt amount:</string>
    <string name="out_amount_colon">Total stock-out amount:</string>
    <!--查询end-->

    <!--商品管理start-->
    <string name="goods_edit_to_right">Click on the product on the right to edit it</string>
    <string name="goods_info">Product Details</string>
    <string name="input_goods_barcode">Please enter the product barcode</string>
    <string name="input_goods_name">Please enter the product name</string>
    <string name="pricing_type">Pricing type</string>
    <string name="pricing_piece">reckon by the piece</string>
    <string name="pricing_weight">weigh</string>
    <string name="stock_price_average">Average inventory price</string>
    <string name="supplier_name">Supplier Name</string>
    <string name="select_supplier">Please select the supplier</string>
    <string name="supplier_all">All suppliers</string>
    <string name="goods_stock">merchandise inventory</string>
    <string name="in">Warehousing</string>
    <string name="out">Outbound</string>
    <string name="goods_information_more">More product information</string>
    <string name="edit_home_cate">Edit homepage categories</string>
    <string name="add_goods_to_cate">Add products to the homepage category</string>
    <string name="in_price_last_colon">Last inventory price:</string>
    <string name="date_produce">date of manufacture</string>
    <string name="select_date_produce">Please select the production date</string>
    <string name="date_become">Due Date</string>
    <string name="select_date_become">Please select the expiration date</string>
    <string name="goods_out">Outbound quantity</string>
    <string name="out_reason">Reason for outbound shipment</string>
    <string name="select_out_reason">Please select the reason for outbound shipment</string>
    <string name="out_count">Outbound quantity</string>
    <string name="input_out_count">Please enter the outbound quantity</string>
    <string name="out_total">Total outbound price</string>
    <string name="out_price">Outbound unit price</string>
    <string name="input_out_price">Please enter the outbound unit price</string>
    <string name="out_type">Outbound type</string>
    <string name="goods_newly">New Product</string>
    <string name="pack0">Basic packaging</string>
    <string name="pack1">Intermediate packaging</string>
    <string name="pack2">Maximum packaging</string>
    <string name="local_goods">Local products</string>
    <string name="cloud_goods">Cloud based products</string>
    <string name="download_to_scale">Download to Scale</string>
    <string name="goods_cate">CATEGORY</string>
    <string name="select_goods_cate">Please select product category</string>
    <string name="select_goods_cate_big">Please choose a major category</string>
    <string name="select_goods_cate_small">Please select a subcategory</string>
    <string name="goods_life">Shelf life (days)</string>
    <string name="goods_life_colon">Shelf life:</string>
    <string name="input_goods_life">Please enter the shelf life</string>
    <string name="goods_specs">Product specifications</string>
    <string name="input_goods_specs">Please enter the product specifications</string>
    <string name="abbr">Abbreviation of product name</string>
    <string name="input_abbr">Please enter the abbreviation of the product name</string>
    <string name="cashier_shelves">Cash register on and off the shelves</string>
    <string name="applet_shelves">Add and remove mini programs</string>
    <string name="input_stock_price_average">Please enter the average inventory price</string>
    <string name="input_goods_stock">Please enter the inventory of the product</string>
    <string name="units">Units</string>
    <string name="how_mush_base_pack">=How much basic packaging</string>
    <string name="in_price_recently">Recent inventory price</string>
    <string name="in_price_recently_colon">Recent inventory price:</string>
    <string name="start_order_online">Online minimum order</string>
    <string name="input_start_order_online">Please enter the online minimum order</string>
    <string name="goods_brand">Product brand</string>
    <string name="input_goods_brand">Please enter the product brand</string>
    <string name="stock_warning">Inventory alert</string>
    <string name="low_to_warning">Below this value, a warning will be issued</string>
    <string name="stock_low_limit">Inventory quantity limit</string>
    <string name="tall_to_warning">Exceeding this value will trigger an alert</string>
    <string name="stock_tall_limit">Inventory quantity limit</string>
    <string name="base_unit_to_conversion">(Convert basic inventory to units and round to the nearest whole)</string>
    <string name="input_goods_barcode_specs1">Please enter the barcode of the intermediate packaging product</string>
    <string name="input_goods_name_specs1">Please enter the name of the intermediate packaging product</string>
    <string name="base_unit_count_than_one">The minimum unit quantity must be greater than 1</string>
    <string name="input_in_price_specs1">Please enter the unit price for intermediate packaging upon receipt</string>
    <string name="input_price_sale_specs1">Please enter the retail unit price of intermediate packaging</string>
    <string name="select_goods_unit_specs1">Please select the unit of intermediate packaging goods</string>
    <string name="input_goods_barcode_specs2">Please enter the maximum packaging product barcode</string>
    <string name="input_goods_name_specs2">Please enter the maximum packaging product name</string>
    <string name="base_unit_count_than_specs1">The minimum unit quantity must be greater than the minimum unit quantity of the intermediate packaging</string>
    <string name="input_in_price_specs2">Please enter the maximum packaging unit price upon receipt</string>
    <string name="input_price_sale_specs2">Please enter the maximum retail unit price for packaging</string>
    <string name="select_goods_unit_specs2">Please select the maximum packaging unit for the product</string>
    <string name="cate_default">System default classification</string>
    <string name="cate_add">Add a first level classification</string>
    <string name="cate_edit">Edit first level classification</string>
    <string name="cate_child_add">Add secondary classification</string>
    <string name="cate_child_edit">Edit secondary classification</string>
    <string name="cate_name">Classification name</string>
    <string name="input_cate_name">Please enter the category name</string>
    <string name="cate_icon">Classification icon</string>
    <string name="select_cate_icon">Please select the category icon</string>
    <string name="cate_home_pc_edit">Edit homepage virtual category</string>
    <string name="cate_home_pc_add">New homepage virtual category added</string>
    <string name="cate_home_add">Add Category</string>
    <string name="confirm_cate_del">Are you sure to delete this category?</string>
    <string name="pages">pages</string>
    <string name="goods_manage_exit">Exit commodity management</string>
    <string name="goods_add_quick">Quickly add new products</string>
    <string name="find_new_goods_or_add">New item found, add item base package to local?</string>
    <string name="wholesale_count">Starting batch quantity</string>
    <string name="input_wholesale_count">Please enter the starting batch quantity</string>
    <string name="wholesale_price">Wholesale unit price</string>
    <string name="input_wholesale_price">Please enter the wholesale unit price</string>
    <string name="goods_location">Commodity location</string>
    <string name="select_goods_location">Please select a commodity location</string>
    <string name="average_price_describe">Description of average inventory price</string>
    <string name="average_price_describe_tips">The average inventory price is calculated by moving weighted average;</string>
    <string name="average_price_describe_tips1">Moving weighted average unit price = (amount of inventory goods before the current receipt + amount of goods received this time)/ (quantity of inventory goods before the current receipt + quantity of goods received this time)</string>
    <string name="set_wholesale_price">Do you want to set wholesale prices</string>
    <string name="input_wholesale_count_specs1">Please enter the minimum batch quantity for intermediate packaging</string>
    <string name="input_wholesale_price_specs1">Please enter the wholesale unit price for intermediate packaging</string>
    <string name="input_wholesale_count_specs2">Please enter the maximum packaging batch quantity</string>
    <string name="input_wholesale_price_specs2">Please enter the maximum wholesale unit price for packaging</string>
    <string name="confirm_del_goods_in_cate">Are you sure you want to delete this item from this category?</string>
    <!--商品管理end-->

    <!--统计start-->
    <string name="statistics_operating">operating statistics</string>
    <string name="today">today</string>
    <string name="near_day_seven">Last 7 days</string>
    <string name="near_day_fifteen">Last 15 days</string>
    <string name="near_day_thirty">Last 30 days</string>
    <string name="today_turnover">Today revenue</string>
    <string name="gross_profit">gross profit</string>
    <string name="order_quantity">order quantity</string>
    <string name="aver_price">Customer unit price</string>
    <string name="online_order_quantity">Online order volume</string>
    <string name="sales_trend">Sales trend</string>
    <string name="week">week</string>
    <string name="month">month</string>
    <string name="year">year</string>
    <string name="sales_volume">sales volume</string>
    <string name="sales_cycle_ratio">Proportion of sales data cycle</string>
    <string name="sales_class_ratio">Proportion of sales categories</string>
    <string name="payment_ratio">Proportion of payment types</string>
    <string name="sales_volume_hour">24-hour distribution of revenue</string>
    <string name="yesterday_turnover">Yesterday revenue</string>
    <string name="goods_hot_sales_top">Top 5 best-selling products</string>
    <string name="goods_profit_top">Top 5 Accumulated Profit Products</string>
    <string name="goods_unhot_sales_top">Top 5 unsold products</string>
    <string name="statistics_payment">Payment statistics</string>
    <string name="statistics_staff">Employee statistics</string>
    <string name="real_revenue">Actual revenue</string>
    <string name="no_password_paid_in">Non-dense harvest</string>
    <string name="discount_money">Preferential amount</string>
    <string name="member_to_shop">To the store member</string>
    <string name="stored_money">Stored value amount</string>
    <string name="expired_warning">Overdue alert</string>
    <string name="restock_goods">Goods to be replenished</string>
    <!--统计end-->

    <!--网单start-->
    <string name="netlist_order">Online single order</string>
    <string name="netlist_refund">Online single refund</string>
    <string name="rider_manage">Rider Management</string>
    <string name="beans">Department Store Bean</string>
    <string name="delivery_setting">Delivery settings</string>
    <string name="order_type">order type</string>
    <string name="order_type0">In store cashier</string>
    <string name="order_type1">Store self pickup</string>
    <string name="order_type2">provide home delivery service</string>
    <string name="order_status0">To be shipped</string>
    <string name="order_status1">To be delivered</string>
    <string name="order_status2">Delivery exception</string>
    <string name="order_status3">To be received</string>
    <string name="order_status4">To be picked up by oneself</string>
    <string name="order_status5">To be evaluated</string>
    <string name="order_status6">Completed</string>
    <string name="order_status7">obligation</string>
    <string name="order_status8">Canceled</string>
    <string name="order_status9">To be stored</string>
    <string name="order_status10">To be confirmed</string>
    <string name="order_status11">Voided</string>
    <string name="order_status12">Pending payment</string>
    <string name="serial_number">serial number</string>
    <string name="contacts_type">contact information</string>
    <string name="get_type">Delivery method</string>
    <string name="goods_count">Product quantity</string>
    <string name="payable_colon">Payable:</string>
    <string name="paid_colon">Actual payment:</string>
    <string name="order_receive">Receiving orders</string>
    <string name="confirm_get">Confirm receipt</string>
    <string name="delivery_cancel">Cancel delivery</string>
    <string name="order_cancel">Cancel order</string>
    <string name="confirm_pick">Confirm pickup</string>
    <string name="order_receive_again">Reorder</string>
    <string name="goods_price">item pricing</string>
    <string name="goods_total">Total price of goods</string>
    <string name="refunded_count">Returned quantity</string>
    <string name="refund_goods_count">Return quantity</string>
    <string name="refund_goods_total">Subtotal for returns</string>
    <string name="rtotal_refund_colon">Total refund:</string>
    <string name="whole_order_refund">Full order \n Refund</string>
    <string name="total">total</string>
    <string name="refund_print_receipts">Refund printing receipt</string>
    <string name="refund_order">Refund order</string>
    <string name="order_no_receive">This order does not support receiving order</string>
    <string name="receive_address">Pick-up address</string>
    <string name="order_weight">Order weight</string>

    <!--网单退款-->
    <string name="refund_status0">Audit</string>
    <string name="refund_status1">Refunded</string>
    <string name="refund_status2">refused</string>
    <string name="apply_time">apply time</string>
    <string name="apply_time_colon">apply time:</string>
    <string name="refunder">Refunder</string>
    <string name="should_refund_colon">Should be refunded:</string>
    <string name="refund_reason_colon">Reason for refund:</string>
    <string name="apply_refund_money_colon">Refund amount:</string>
    <string name="shopper_colon">shopper:</string>
    <string name="mobile">phone number</string>
    <string name="mobile_colon">phone number:</string>
    <string name="refund_refuse">Refusal to refund</string>
    <string name="refund_confirm">Confirm refund</string>
    <string name="refuse_reason">Reject Reason</string>
    <string name="input_refuse_reason">Please enter the reason for rejection</string>
    <string name="usually_script">Common language skills</string>
    <string name="refuse_reason_add">Add rejection reason</string>
    <string name="refuse_reason_edit">Edit rejection reason</string>

    <!--骑手管理-->
    <string name="select_rider">Please select a rider</string>
    <string name="rider_add">New rider added</string>
    <string name="rider_edit">Edit rider</string>
    <string name="rider_name">Rider name</string>
    <string name="input_rider_name">Please enter the rider name</string>
    <string name="rider_mobile">Rider phone number</string>
    <string name="input_rider_mobile">Please enter the rider phone number</string>
    <string name="input_rider_mobile_right">Please enter the correct rider phone number</string>

    <!--百货豆-->
    <string name="beans_left">Remaining department store beans</string>
    <string name="order_count_total">Total number of Orders</string>
    <string name="order_total_total">Total order amount</string>
    <string name="subsidy_total">Total platform subsidies</string>
    <string name="deduct_total">Total number of member deductions</string>
    <string name="rule_setting">Rule settings</string>
    <string name="trading_record">Transaction records</string>
    <string name="withdrawal_record">Withdrawal records</string>
    <string name="order_total">Order amount</string>
    <string name="order_total_colon">Order amount:</string>
    <string name="deduct_count">Deduction quantity</string>
    <string name="shop_gift">Free gift from our store</string>
    <string name="subsidy">Platform subsidies</string>
    <string name="pay_type">Payment method</string>
    <string name="trading_time">Trading Hours</string>
    <string name="status_trading">Transaction Status</string>
    <string name="beans_status0">in review</string>
    <string name="beans_status1">Already received</string>
    <string name="beans_status2">Rejected</string>

    <!--配送设置-->
    <string name="delivery_type_colon">Delivery method:</string>
    <string name="delivery_type0">Self delivery</string>
    <string name="delivery_type1">quarter for delivery</string>
    <string name="delivery_range_colon">Delivery scope:</string>
    <string name="input_delivery_range">Please enter the delivery range</string>
    <string name="delivery_range_tips">Tip: Set the delivery diameter range for the store, with a maximum of 10 kilometers.</string>
    <string name="delivery_start_order_colon">Minimum delivery price:</string>
    <string name="input_delivery_start_order">Please enter the minimum delivery price</string>
    <string name="delivery_start_order_tips">Tip: Set the minimum delivery price for the store. Only when the total amount of goods ordered by the user is greater than or equal to this price, can the order be placed.</string>
    <string name="delivery_free_colon">Free allocation amount:</string>
    <string name="input_delivery_free">Please enter the free allocation amount</string>
    <string name="delivery_free_tips">Tip: Set a free delivery fee price for the store. If the total amount of goods ordered by the user is greater than or equal to this price, the delivery fee will be waived.</string>
    <string name="estimate_duration_colon">Estimated duration:</string>
    <string name="input_estimate_duration">Please enter the estimated duration</string>
    <!--网单end-->

    <!--会员start-->
    <string name="member_search">Search for members</string>
    <string name="member_search_phone">Search for member phone numbers</string>
    <string name="member_add">New members added</string>
    <string name="member_manage">Member Management</string>
    <string name="place_select_member">Please select a member</string>
    <string name="no_member">Number Member</string>
    <string name="member_search_notfind">No relevant members found in the search</string>
    <string name="member_tips">Please change the search information or click</string>
    <string name="member_mobile">Members mobile phone</string>
    <string name="input_member_mobile">Please enter the members mobile phone number</string>
    <string name="member_no">Membership Number</string>
    <string name="input_member_no">Please enter your membership number</string>
    <string name="member_name">Member name</string>
    <string name="input_member_name">Please enter the member name</string>
    <string name="member_balance">Account balance</string>
    <string name="input_member_balance">Please enter Account balance</string>
    <string name="member_points">Member points</string>
    <string name="points_add">Increase points</string>
    <string name="points_sub">Reduce points</string>
    <string name="debt_limit">Debt limit</string>
    <string name="input_debt_limit">Please enter the debt limit</string>
    <string name="member_sex">Member Gender</string>
    <string name="account_pwd">account password</string>
    <string name="input_account_pwd">Please enter account password</string>
    <string name="remarks_info">memo</string>
    <string name="input_remarks_info">Please enter note information</string>
    <string name="member_analysis">In store member analysis</string>
    <string name="member_total">Total number of members</string>
    <string name="stored_total">Total stored value</string>
    <string name="credit_total">Total amount of credit owed</string>
    <string name="proportion_member">Membership type proportion</string>
    <string name="proportion_stored_money">The proportion of stored value amount</string>
    <string name="proportion_member_points">Membership points percentage</string>
    <string name="proportion_member_level">Membership level proportion</string>
    <string name="proportion_member_time">The proportion of time slots for members to visit the store</string>
    <string name="level_manage">Level management</string>
    <string name="points_rule">Integral rule</string>
    <string name="member_credit">Credit owed to members</string>
    <string name="member_stored">Stored value member</string>
    <string name="member_common">Regular members</string>
    <string name="member_gold">Gold membership</string>
    <string name="member_platinum">Platinum membership</string>
    <string name="member_diamond">Diamond Member</string>
    <string name="member_info">Member details</string>
    <string name="consumption_records">Consumption records</string>
    <string name="member_level">Membership level</string>
    <string name="member_del">Delete Member</string>
    <string name="member_type">Member Type</string>
    <string name="member_type0">membership card</string>
    <string name="member_type1">Stored value card</string>
    <string name="member_type2">Member stored value card</string>
    <string name="register_just">Just register</string>
    <string name="input_points">Please enter points</string>
    <string name="input_points_gold">Please input Gold Member Points</string>
    <string name="input_points_platinum">Please input platinum membership points</string>
    <string name="input_points_diamond">Please input diamond membership points</string>
    <!--会员end-->

    <!--补货start-->
    <string name="mall_jinquan">Jinquan Mall</string>
    <string name="restock_self">Self procurement replenishment</string>
    <string name="restock_create">Create replenishment plan</string>
    <string name="input_restock_name">Please enter the name of the replenishment plan</string>
    <string name="restock_id_wrong">The replenishment plan ID is incorrect, please try again</string>
    <string name="buy_count">Purchase quantity</string>
    <string name="price_estimate">Estimated price</string>
    <string name="price_estimate_colon">Estimated price:</string>
    <string name="restock_cancel">Cancel restocking</string>
    <string name="restock_add">Add restocking</string>
    <string name="restock_again">once more restock</string>
    <string name="restock_select_goods">Click to select restocking products</string>
    <string name="sales_three_colon">3-day sales:</string>
    <string name="sales_seven_colon">7-day sales:</string>
    <string name="stock_sale">Marketable inventory:</string>
    <string name="stock_below_safe">(Below safety stock)</string>
    <string name="buy_info_last">Last procurement information:</string>
    <string name="buy_count_suggest">Suggested procurement quantity:</string>
    <string name="input_restock_count">Please enter the replenishment quantity</string>
    <string name="select_restock_specs">Please select replenishment specifications</string>
    <string name="restock_preview">Replenishment Plan Preview</string>
    <string name="restock_submit">Submit replenishment plan</string>
    <string name="address_where_colon">Where address:</string>
    <string name="address_where">Where address</string>
    <string name="input_address_where">Please enter your address</string>
    <string name="product_info">Product Details</string>
    <string name="goods_type">Product Types</string>
    <string name="goods_type_colon">Product types:</string>
    <string name="restock_submit_tips">After submitting the replenishment order, it cannot be changed. If modification is needed, please contact the supplier for modification!</string>
    <string name="place_add_goods">Please add product</string>
    <string name="supplier_manage">supplier management</string>
    <string name="supplier_apply">Supplier application</string>
    <string name="supplier_cate">Supplier classification</string>
    <string name="supplier_add">Add supplier</string>
    <string name="supplier_edit">Edit supplier</string>
    <string name="purchase_total">Total purchase amount</string>
    <string name="purchase_total_colon">Total purchase amount:</string>
    <string name="debt_total">Total amount owed</string>
    <string name="debt_total_colon">Total amount owed:</string>
    <string name="settled_total">Settled amount</string>
    <string name="settled_total_colon">Settled amount:</string>
    <string name="supplier_search_tips">Supplier Name/Phone Number</string>
    <string name="supplier_cate_manage">Supplier classification management</string>
    <string name="supplier_cate_add">Add supplier classification</string>
    <string name="supplier_cate_name">Supplier classification name</string>
    <string name="input_supplier_cate_name">Please enter the supplier classification name</string>
    <string name="input_supplier_name">Please enter the supplier name</string>
    <string name="input_contacts">Please enter the contact person</string>
    <string name="cate_affiliation">category</string>
    <string name="select_cate">Please select a category</string>
    <string name="supplier_info">Supplier details</string>
    <string name="supplied_goods">Goods supplied</string>
    <string name="purchase_order">Purchase and sales orders</string>
    <string name="settlement_record">Payment records</string>
    <string name="supplier_replace">Replace supplier</string>
    <string name="goods_archived">Archived products</string>
    <string name="goods_unfilled">Unfilled products</string>
    <string name="price_suggest_colon">suggested price:</string>
    <string name="archived">archived</string>
    <string name="status_payment">payment status</string>
    <string name="status_order">order status</string>
    <string name="status_order_colon">order status:</string>
    <string name="repayment_time">Repayment time</string>
    <string name="repayment_name">Reparer</string>
    <string name="repayment_order">Repayment order</string>
    <string name="repayment_total">Repayment amount</string>
    <string name="status_repayment">Repayment status</string>
    <string name="purchase_info">Purchase and sales order details</string>
    <string name="price_info">Price breakdown</string>
    <string name="batch_infos">Batch Information</string>
    <string name="batch_profit">Batch profit</string>
    <string name="goods_all">All products</string>
    <string name="supplier_information">Supplier Information</string>
    <string name="supplier_name_colon">Supplier name:</string>
    <string name="contacts_colon">contacts:</string>
    <string name="buying_price">buying price</string>
    <string name="goods_purchase">Product replenishment</string>
    <string name="supplier_change">Change supplier</string>
    <string name="settlement_info">Payment details</string>
    <string name="repayment_intro">Repayment Details</string>
    <string name="repayment_name_colon">Repayment person:</string>
    <string name="repayment_total_colon">Repayment amount:</string>
    <string name="repayment_remarks_colon">Repayment remarks:</string>
    <string name="repayment_voucher_colon">Repayment voucher:</string>
    <string name="copy_to_clipboard">Copy to clipboard</string>
    <string name="goods_not_check">There are products that have not been verified</string>
    <string name="supplier_settlement">Supplier repayment</string>
    <string name="batch_debt_total">Total debt of a batch</string>
    <string name="all_select">Select All</string>
    <string name="confirm_settlement">Confirm repayment</string>
    <string name="selected">selected</string>
    <string name="order_debt_total">An order, total outstanding debt</string>
    <string name="select_order">Please select an order</string>
    <string name="input_repayment_total">Please enter the repayment amount</string>
    <string name="purchase_voucher_upload">Upload documents and vouchers</string>
    <string name="plase_purchase_voucher_upload">Please upload the documents and vouchers</string>
    <string name="submit">submit</string>
    <string name="goods_check">Product verification</string>
    <string name="ordered_count_colon">Order quantity:</string>
    <string name="actual_received_count_colon">Actual received quantity:</string>
    <string name="actual_in_count_colon">Actual inventory quantity:</string>
    <string name="goods_short_stock">Product out of stock</string>
    <string name="goods_many_stock">Multiple products available</string>
    <string name="three_price_syn_suggest_price">Trivalent synchronous suggested price</string>
    <string name="goods_check_cancel">Revoke verification</string>
    <!--补货end-->

    <!--营销start-->
    <string name="goods_discount">merchandise discount</string>
    <string name="goods_gift">Goods Full Gift</string>
    <string name="order_promotion">Order promotion</string>
    <string name="goods_promotion">Single item promotion</string>
    <string name="member_price_join_discount">Member price participation discount</string>
    <string name="price_old_colon">original price:</string>
    <string name="full">full</string>
    <string name="piece_gift">Gift of items</string>
    <string name="yuan_discount">Dollar discount</string>
    <string name="yuan_gift">Dollar Gift</string>
    <!--营销end-->

    <!--交班start-->
    <string name="duty_cashier">Duty clerk</string>
    <string name="confirm_shift">Confirm handover</string>
    <string name="order_offline">Offline orders</string>
    <string name="no_order_offline">There are currently no offline orders available</string>
    <string name="recharge_info_colon">Recharge details:</string>
    <string name="revenue_statistics">Revenue statistics</string>
    <string name="order_receivable">Order receivable</string>
    <string name="order_paid_in">The order is actually received</string>
    <!--交班end-->

    <!--设置start-->
    <string name="setting_shop">Store Information</string>
    <string name="setting_cashier">Cashier settings</string>
    <string name="setting_parts">Accessories settings</string>
    <string name="setting_online">Online settings</string>
    <string name="setting_system">System settings</string>
    <!--店铺信息-->
    <string name="shop_id">Store ID</string>
    <string name="goods_syn_again">Resynchronize products</string>
    <string name="goods_syb_start">Start synchronization</string>
    <string name="shop_name">shop names</string>
    <string name="input_shop_name">Please enter the store name</string>
    <string name="shop_address">Store location</string>
    <string name="input_shop_address">Please enter the store location</string>
    <string name="machine_unique">Machine No</string>
    <string name="staff_unique">employee number</string>
    <string name="machine_mac">Machine MAC</string>
    <string name="contact_mobile">Telephone</string>
    <string name="input_contact_moblie">Please enter the contact phone number</string>
    <string name="member_unique_four">The top four digits of the membership card</string>
    <string name="input_member_unique_four">Please enter the top four digits of your membership card</string>
    <!--收银设置-利润设置-->
    <string name="setting_profit">Profit setting</string>
    <string name="setting_payment">Payment settings</string>
    <string name="ratio_nobarcode_profit">Profit ratio of no code products (profit/selling price)</string>
    <string name="ratio_nobarcode_profit_tips">Calculate profits according to the set proportion</string>
    <string name="ratio_standard_profit">Profit ratio of ordinary goods (profit/selling price)</string>
    <string name="ratio_standard_profit_tips">Profit will be calculated proportionally when the purchase price is not filled in for the goods entering the warehouse</string>
    <string name="ratio_points">Integral ratio</string>
    <string name="ratio_points_tips">Spending 1 yuan can redeem points</string>
    <!--收银设置-支付设置-->
    <string name="cash">cash</string>
    <string name="cash_colon">cash:</string>
    <string name="alipay">Alipay</string>
    <string name="alipay_colon">Alipay:</string>
    <string name="wechat">WeChat</string>
    <string name="wechat_colon">WeChat:</string>
    <string name="bank_card">Bank Card</string>
    <string name="bank_card_colon">Bank Card:</string>
    <string name="face_swiping">Face brush payment</string>
    <string name="stored_card">Stored value card</string>
    <string name="combination">Combination payment</string>
    <string name="goods_manage">Commodity management</string>
    <string name="online">online</string>
    <string name="offline">offline</string>
    <string name="offline1">(offline)</string>
    <string name="place_select_shortcut_display">Please select the shortcut operation display function on the homepage. Once enabled, the function button will be displayed on the homepage</string>
    <string name="payment_cash">Cash receipts</string>
    <string name="payment_cash_tips">(Consumers using cash payment)</string>
    <string name="payment_alipay">Alipay collection</string>
    <string name="payment_alipay_tips">(Consumers use Alipay to scan the payment code for payment)</string>
    <string name="payment_wechat">WeChat payment</string>
    <string name="payment_wechat_tips">(Consumers use WeChat to scan payment codes for payment)</string>
    <string name="payment_bank_card">Bank card receipts</string>
    <string name="payment_bank_card_tips">(Consumers using bank cards for payment)</string>
    <string name="payment_face">Facial payment</string>
    <string name="payment_face_tips">(Consumers can use facial recognition for payment after 15 minutes of facial authentication in the mini program)</string>
    <string name="payment_stored_card">Prepaid value card payment</string>
    <string name="payment_stored_card_tips">(Consumer uses member balance for payment)</string>
    <string name="payment_combination">Combination payment</string>
    <string name="payment_combination_tips">(Consumers use Alipay/WeChat+cash to pay)</string>
    <string name="goods_manage_tips">(Quickly add and modify product information)</string>
    <string name="shortcut_display_length">Select up to 7 shortcuts</string>
    <!--配件设置-->
    <string name="setting_receipt">Ticket settings</string>
    <string name="setting_tags">Price tag settings</string>
    <string name="setting_weight">Weighing settings</string>
    <string name="setting_camera">Camera settings</string>
    <string name="setting_pos">POS settings</string>
    <string name="enable_pos_cashier">Enable POS Cashier</string>
    <string name="pos_id">POS device ID</string>
    <string name="input_pos_id">Please enter POS device ID</string>
    <string name="set_pos_id">Please set the POS device ID</string>
    <string name="pos">POS machine</string>
    <string name="setting_other">Other settings</string>
    <string name="receipt_specs">Small ticket specifications</string>
    <string name="remarks_content">Remarks</string>
    <string name="print_test">Print test</string>
    <string name="print_test_tips">Reminder: Please test first and then use without any errors</string>
    <string name="tags_template">Custom price tag template</string>
    <string name="port_scale">Serial port scale</string>
    <string name="enable_port_scale">Enable serial port scaling</string>
    <string name="port_name">Serial port number</string>
    <string name="BAUD">BAUD</string>
    <string name="select_BAUD">Select Baud Rate</string>
    <string name="data_parsing_type">Data parsing method</string>
    <string name="select_data_parsing_type">Choose data parsing method</string>
    <string name="scale_status">Electronic scale status</string>
    <string name="port_jin">Serial port weighing market weight</string>
    <string name="weight_now">Current weight</string>
    <string name="scale_add">Add barcode scale</string>
    <string name="input_scale_name">Please enter the barcode scale name</string>
    <string name="scale_download">Download to scale</string>
    <string name="enable_secondary_screen">Enable secondary screen</string>
    <string name="pay_money_box">Pre payment money box</string>
    <string name="camera_face">Facial camera</string>
    <string name="select_camera_face">Choose a facial camera</string>
    <string name="money_box_printer">Cash box printer</string>
    <string name="select_money_box_printer">Choose a cash box printer</string>
    <string name="tts_engine">TTS engine</string>
    <string name="select_tts_engine">Select TTS engine</string>
    <string name="tts_language">TTS language</string>
    <string name="select_tts_language">Choose TTS language</string>
    <string name="tts_voice">TTS sound</string>
    <string name="select_tts_voice">Select TTS sound</string>
    <string name="mm">(millimeters)</string>
    <string name="tags_size">Label size</string>
    <string name="width">width</string>
    <string name="height">height</string>
    <string name="tags_col">Number of label columns</string>
    <string name="print_count">Print quantity</string>
    <string name="tags_spacing">Label spacing</string>
    <string name="print_concentration">print density</string>
    <string name="resolution_ratio">resolution ratio</string>
    <string name="content_location">Content Location</string>
    <string name="content_location_tips">If the coordinate is 0, do not print</string>
    <string name="content_custom">Custom Content</string>
    <string name="content_custom_tips">If the content is empty or the coordinates are 0, do not print</string>
    <string name="goods_list">Product list</string>
    <string name="goods_add">add product</string>
    <string name="goods_name">Trade name</string>
    <string name="goods_sale">Product selling price</string>
    <string name="goods_barcode">bar code</string>
    <string name="input_content">Please enter the content</string>
    <string name="template_save">Save Template</string>
    <string name="confirm_print">Confirm printing</string>
    <!--小票设置-->
    <string name="connect_bt">Bluetooth connection</string>
    <string name="connect_serail">port connection</string>
    <string name="connect_usb">USB connection</string>
    <string name="connect_wifi">WIFI connection</string>
    <string name="printer_no_connect">Printer not connected</string>
    <string name="send_success">Sent successfully</string>
    <string name="send_fail">fail in send</string>
    <string name="balance_colon">balance:</string>
    <string name="stored_card_colon">Stored value card:</string>
    <string name="star_shopname">** shop names **</string>
    <string name="order_receipt">Online billing statement</string>
    <string name="statement">Settle the bill</string>
    <string name="cash_register_colon">cash_register:</string>
    <string name="cash_staff_name">Name</string>
    <string name="goods">goods</string>
    <string name="amount">amount</string>
    <string name="jinquan">Golden Circle</string>
    <string name="member_colon">member:</string>
    <string name="order_points_colon">This order points:</string>
    <string name="member_points_colon">member points:</string>
    <string name="star_remarks">******remarks******</string>
    <string name="tips_warm">Warm tips</string>
    <string name="tips_warm_colon">Warm tips:</string>
    <string name="receipt_tips">Kind reminder: If you need to send a video, please contact the front desk</string>
    <string name="printer_connect_success">Printer connection successful</string>
    <string name="printer_connect_fail">Printer connection failed</string>
    <!--系统设置-->
    <string name="setting_voice">sound settings</string>
    <string name="setting_inlet">Entrance settings</string>
    <string name="cheng_weight_two">Weigh the first two digits of the product</string>
    <string name="cheng_standard_two">The first two digits of ordinary products</string>
    <string name="goods_nobarcode_add_cart">Add no code products directly to the shopping cart without creating them</string>
    <string name="topping">Program Top</string>
    <string name="startup">Self start upon startup</string>
    <string name="goods_barcode_first_no_zero">Commodity barcode cannot start with 0</string>
    <!--    <string name="show_nobarcode_and_weight">Display \'No code products/weighing'</string>-->
    <!--    <string name="change_nobarcode_and_weight">Exchange\'No code item/weighing' location</string>-->
    <string name="use_virtual_keyboard">Using a virtual keyboard</string>
    <string name="hang_print_receipts">Print receipts for hanging orders</string>
    <string name="xml_ratio">Interface proportion</string>
    <string name="home_input_digit_create_nobarcode_goods">Enter numbers on the homepage to create code free products</string>
    <string name="is_show_stock">Is inventory displayed</string>
    <string name="is_exit_procedure">Turn off computer on exit</string>
    <string name="version_check">Check for updates</string>
    <string name="upload_log">Upload current log</string>
    <string name="exit_procedure">Exit program</string>
    <string name="is_voice_home">Play homepage prompt sound</string>
    <string name="is_voice_kewboard">Play keyboard sound</string>
    <string name="is_voice_online_pay">Broadcast online payment</string>
    <string name="is_voice_cash_pay">Broadcast cash payment</string>
    <string name="is_voice_member_pay">Broadcast membership payment</string>
    <string name="is_voice_offline_pay">Broadcast offline payment</string>
    <string name="is_voice_applet_pay">Broadcast Mini Program Payment</string>
    <string name="is_voice_combination_pay">Broadcast combination payment</string>
    <!--线上设置-->
    <string name="print_auto_order">Automatic order receiving and printing</string>
    <string name="printer_connect_type">Printer connection method</string>
    <string name="select_port">Select serial port</string>
    <string name="printer_status">Printer Status</string>
    <string name="printer_defaule_count">Default print quantity</string>
    <string name="yourself_remarks">Independent note content</string>
    <string name="thanks_welcome_again">Thank you for your patronage. Welcome to visit again!</string>
    <string name="print_template">Print Template</string>

    <string name="select_print_type">Please select the printer connection method</string>
    <string name="is_use_catering_receipt">Do you use dining receipts</string>
    <string name="reset_catering_receipt_count">Reset catering receipt count</string>
    <string name="confirm_reset">Confirm reset</string>
    <string name="port_no">port number</string>
    <string name="select_port_no">Please select the port number</string>
    <string name="print_auto_refund">Automatically print refund form</string>
    <string name="serial_port_weighing_scale">Serial port weighing scale</string>
    <string name="show_nobarcode_and_weight">Display"No code product/weight"</string>
    <string name="change_nobarcode_and_weight">Exchange the location of "no code product/weighing"</string>
    <string name="is_voice_applet_refund">Report on Mini Program Refunds</string>
    <string name="is_voice_applet_order">Broadcast mini program accepting orders</string>
    <string name="data_parsing_type0">way 1</string>
    <string name="data_parsing_type1">way 2</string>
    <string name="data_parsing_type2">way 3</string>
    <string name="data_parsing_type3">way 4</string>
    <!--设置end-->

    <!--dialog（选择日期）start-->
    <string name="select_date">Select Date</string>
    <string name="select_date_start">Please select the start date</string>
    <string name="select_date_end">Please select the end date</string>
    <!--dialog（选择日期）end-->

    <!--*****************************弹窗start*****************************-->
    <!--当前登录账号-->
    <string name="this_login_account">Current login account</string>
    <string name="staff_name_colon">Employee Name:</string>
    <string name="contact_mobile_colon">Telephone:</string>
    <string name="login_account_colon">Login account:</string>
    <string name="login_pwd_colon">Login password:</string>
    <string name="pwd_change">Change Password</string>
    <string name="belong_shop_colon">Belonging store:</string>
    <string name="shop_no_colon">Store ID:</string>
    <string name="staff_position_colon">Employee roles:</string>
    <string name="shopowner">shopowner</string>
    <string name="cashier_staff">Cashier</string>

    <!--商品入库-->
    <string name="goods_in">Goods warehousing</string>
    <string name="in_reason">Reason for storage</string>
    <string name="select_in_reason">Please select the reason for storage</string>
    <string name="purchase_price">Purchase unit price</string>
    <string name="purchase_price_colon">Purchase unit price:</string>
    <string name="input_purchase_price">Please enter the purchase unit price</string>
    <string name="stock_now">current inventory</string>
    <string name="stock_now_colon">current inventory:</string>
    <string name="in_count">Inventory quantity</string>
    <string name="input_in_count">Please enter the inbound quantity</string>
    <string name="in_price">Receipt unit price</string>
    <string name="input_in_price">Please enter the inbound unit price</string>
    <string name="in_total">Total inbound price</string>
    <string name="price_now">Current price</string>
    <string name="change_to">Change to</string>
    <string name="price_sale">Retail unit price</string>
    <string name="price_sale_colon">Retail unit price:</string>
    <string name="input_price_sale">Please enter the retail unit price</string>
    <string name="price_online">Online shopping unit price</string>
    <string name="price_online_colon">Online shopping unit price:</string>
    <string name="input_price_online">Please enter the unit price for online shopping</string>
    <string name="price_member">Member unit price</string>
    <string name="price_member_colon">Member unit price:</string>
    <string name="input_price_member">Please enter the member unit price</string>
    <string name="no_count">To be calculated</string>
    <string name="remarks_img">Remark photo</string>
    <string name="batch_no">Batch number</string>
    <string name="in_time">Storage time</string>
    <string name="count_left">Remaining Quantity</string>
    <string name="out_count_total_no_than_count">The sum of the outbound quantities shall not exceed the outbound quantity of the goods</string>

    <!--会员退费-->
    <string name="refund_fee">refund</string>
    <string name="member_refund">Member refund</string>
    <string name="member_refund_tips">When refunding, the system only clears the member stored value balance. Please refund the balance to the consumer through cash or offline transfer!</string>
    <string name="refund_fee_money">Refund amount</string>
    <string name="input_refund_fee_money">Please enter the refund amount</string>
    <string name="member_refund_tips1">The refund amount cannot be greater than balance of the member</string>

    <!--会员充值-->
    <string name="member_recharge">Member recharge</string>
    <string name="recharge_money">Recharge amount</string>
    <string name="input_recharge_money">Please enter the recharge amount</string>
    <string name="recharge_activity_colon">Recharge activity:</string>
    <string name="jinquan_collection">Gold Circle Collection</string>
    <string name="jinquan_collection_tips">Simply scan the consumer payment code</string>
    <string name="recharge_success">Recharged successfully</string>
    <string name="refund_success">Refund successful</string>

    <!--兑换、增加、减少积分-->
    <string name="points_count">Number of points</string>
    <string name="input_points_count">Please enter the number of points</string>
    <string name="add_reason">Reason for increase</string>
    <string name="sub_reason">Reason for reduction</string>
    <string name="exchange_points">Redeem points</string>
    <string name="exchange_reason">Reason for exchange</string>

    <!--积分规则-->
    <string name="use_points">Using points</string>
    <string name="one_yuan_equal">1 yuan=</string>
    <string name="equal_points">How many points is equal to</string>
    <string name="count_type">Calculation method for less than 1 yuan</string>
    <string name="half_adjust">half adjust</string>
    <string name="not_count">Do not calculate</string>

    <!--交班-统计字段说明-->
    <string name="statistics_field_explain">Statistical Field Description</string>
    <string name="statistics_field_explain_tips0">Actual revenue=revenue - discount amount - refund amount;</string>
    <string name="statistics_field_explain_tips1">Turnover: refers to the total amount of completed orders within the selected time frame;</string>
    <string name="statistics_field_explain_tips2">Non confidential payment: refers to the total amount of orders received by scanning the customer payment code within the selected time range;</string>
    <string name="i_got_it">I got it</string>

    <!--选择商品单位-->
    <string name="goods_unit">Product unit</string>
    <string name="goods_unit_colon">Product unit:</string>
    <string name="select_goods_unit">Please select the product unit</string>
    <string name="goods_unit_add">New commodity unit</string>
    <string name="goods_unit_edit">Edit the unit of commodity</string>
    <string name="input_goods_unit">Please enter the name of the product unit</string>

    <!--修改单价、数量、小计-->
    <string name="modify_price">Modify unit price</string>
    <string name="input_price">Please enter the unit price</string>
    <string name="modify_count">Modify quantity</string>
    <string name="input_count">Please enter the quantity</string>
    <string name="modify_subtotal">Modify subtotal</string>
    <string name="input_subtotal">Please enter subtotal</string>

    <!--现金收款-->
    <string name="receivable">receivable</string>
    <string name="price_discount">discounted price</string>
    <string name="give_change">give change</string>
    <string name="zero_discount">Zero discount</string>
    <string name="zero_store_all">Store all zeros</string>
    <string name="zero_store_one">Deposit zero to 1 yuan</string>
    <string name="collection_success">Collection Successfully</string>
    <string name="print_repair_receipt">Supplement receipts</string>
    <string name="collectioning">Collection</string>
    <string name="collection_cancel">Cancel payment</string>
    <string name="collection_fail">Collection failed</string>
    <string name="fail_reason_colon">failure reason:</string>
    <string name="collection_again">Receipts received again</string>
    <string name="collection_stored_card">Prepaid card payment</string>
    <string name="collection_combination">Combination collection</string>
    <string name="input_money">Please enter the amount</string>
    <string name="place_scan_customer_payment_code">Please scan the customer payment code to complete the collection process</string>
    <string name="consumption_total_colon">Consumption amount：</string>
    <string name="place_connect_camera">Please connect the camera first</string>
    <string name="camera_fail">Failed to obtain camera</string>
    <string name="payment_face_tips0">In recognition</string>
    <string name="payment_face_tips1">Please keep one person inside the box</string>
    <string name="payment_face_tips2">Please place your face in the middle</string>
    <string name="payment_face_tips3">Facial recognition payment</string>
    <string name="online_collection_combination">Online combined collection</string>
    <string name="pay_code">Payment code</string>

    <!--权限-->
    <string name="authorize_remind">Retail version cash register authorization reminder</string>
    <string name="need_access_colon">Retail version cash register requires access:</string>
    <string name="agree_and_continue">Agree and continue</string>
    <string name="disagree">disagree</string>
    <string name="do_not_remind_again">Do not remind me again</string>
    <string name="permission_content_camera_and_read_write">1. Camera permission: In order for you to take photos and videos, we request camera permission. We will only request this permission when you use the camera function, and we will strictly comply with the relevant privacy policy. \n2. Read and write permissions: We need read and write storage permissions to save your files and data. We promise that we will not view or share any of your personal information, and we will take all necessary security measures to protect your data.</string>
    <string name="permission_content_location">Location permission: We would like to obtain your location information in order to provide you with more accurate services. If you agree, we will use your location information to locate your location and recommend nearby places and services to you.</string>
    <string name="permission_content_camera">Camera permission: In order for you to take photos and videos, we request camera permission. We will only request this permission when you use the camera function, and we will strictly comply with the relevant privacy policy.</string>
    <string name="permission_content_read_write">Read and write permissions: We need read and write storage permissions to save your files and data. We promise that we will not view or share any of your personal information, and we will take all necessary security measures to protect your data.</string>
    <string name="permission_content_call">Phone access: In order for you to make phone calls using our application, we need to obtain phone access. We promise that we will not monitor your calls or record their content, and we will take necessary measures to protect your privacy.</string>
    <string name="permission_content_phone">Phone information permission: We need to access your phone information in order to provide better service. For example, we can recommend people to contact you or send you reminders about phone calls you have answered. We promise not to disclose any of your personal information, and we will take all necessary security measures to protect your data.</string>
    <string name="permission_content_bluetooth">Bluetooth permission: We need to access your Bluetooth in order to provide better service. For example, we can connect to a printer via Bluetooth to print receipts.</string>
    <string name="no_permission">No permission</string>

    <!--选择图片-->
    <string name="camera">photograph</string>
    <string name="album_select">Album selection</string>

    <!--无码商品-->
    <string name="no_barcode_weight">No code weighing</string>
    <string name="no_barcode_goods">No code product</string>
    <string name="no_barcode_weight_add">Add no code weighing</string>
    <string name="no_barcode_goods_add">Add uncensored products</string>
    <string name="count_colon">quantity:</string>
    <string name="price_colon">unit-price:</string>
    <string name="total_colon">Total price:</string>
    <string name="yuan_goods">Dollar commodity</string>

    <!--*****************************弹窗end*****************************-->

    <!--SmartRefreshLayout列表start-->
    <string name="srl_header_failed">refresh failed</string>
    <string name="srl_header_finish">Refresh completed</string>
    <string name="srl_header_loading">Waiting for bottom loading to complete…</string>
    <string name="srl_header_pulling">Pull down to refresh</string>
    <string name="srl_header_refreshing">Refreshing…</string>
    <string name="srl_header_release">Release and refresh immediately</string>
    <string name="srl_header_secondary">Release and enter the second floor</string>
    <string name="srl_header_update">\'last update\' M-d HH:mm</string>
    <string name="srl_footer_failed">Load Fail</string>
    <string name="srl_footer_finish">Loading completed</string>
    <string name="srl_footer_loading">loading…</string>
    <string name="srl_footer_nothing">There is no more data available</string>
    <string name="srl_footer_pulling">Pull up to load more</string>
    <string name="srl_footer_refreshing">Waiting for the head refresh to complete…</string>
    <string name="srl_footer_release">Release and load immediately</string>
    <!--SmartRefreshLayout列表end-->

    <!--*****************************启动页start*****************************-->
    <string name="launch_tips">Data is ready to open</string>
    <string name="technical_support">Shandong Influential Intelligent Technology Co., Ltd. provides technical support TEL：400-7688-365</string>
    <!--*****************************启动页end*****************************-->
</resources>