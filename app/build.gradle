plugins {
    id 'com.android.application'
}

static def releaseTime() {
    return new Date().format("yyyy-MM-dd")
}

android {
    namespace 'com.yxl.cashier_retail'
    compileSdk 33

    signingConfigs {
        release {
            keyAlias 'key_cashier_retail'
            keyPassword 'android'
            storeFile file('cashier_retail.jks')
            storePassword 'android'
        }
    }
    defaultConfig {
        applicationId "com.yxl.cashier_retail"
        minSdk 21
        targetSdk 33
        versionCode 1
        versionName "1.0.1"
        multiDexEnabled true
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        ndk {
            abiFilters "armeabi", "armeabi-v7a", "arm64-v8a", "mips", 'mips64', "x86_64"//, "x86"
        }
    }
    //修改生成的apk名字
    android.applicationVariants.all { variant ->
        variant.outputs.all { output ->
            def outputFile = output.outputFile
            if (outputFile != null && outputFile.name.endsWith('.apk')) {
                outputFileName = "cashier" + "-" +
                        defaultConfig.versionName + "-" +
                        releaseTime() + "-" +
                        output.baseName + ".apk"
            }
        }
    }
//    signingConfigs {
//        config {
//            storeFile file('AYW.jks')
//            storePassword 'AYW2023'
//            keyAlias = 'AYW'
//            keyPassword 'AYW2023'
//            v1SigningEnabled true //是否开启V1签名
//            v2SigningEnabled true //是否开启V2签名
//            enableV3Signing true  //是否开启V3签名
//            enableV4Signing true  //是否开启V4签名
//        }
//    }
    buildTypes {
        release {
//            signingConfig signingConfigs.config
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

        }
        debug {
//            signingConfig signingConfigs.config
            minifyEnabled false
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    viewBinding {
        enabled = true
    }
    sourceSets {
        main {
            jniLibs.srcDir 'libs'
        }
    }
}

dependencies {
    api fileTree(include: ['*.aar', '*.jar'], dir: 'libs')
    implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'com.google.android.material:material:1.5.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.3'
    implementation project(path: ':libyxlcommon')
    implementation project(path: ':libupdate')
    implementation project(path: ':MQTTLibrary')
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
    //分包
    implementation 'androidx.multidex:multidex:2.0.0'
    //json工具
    implementation 'com.alibaba:fastjson:1.2.76'
    implementation 'com.google.code.gson:gson:2.9.0'
    //recycleview管理
    implementation 'com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.6'

    //smartRefreshLayout 下拉刷新
    implementation 'io.github.scwang90:refresh-layout-kernel:2.0.5'
    implementation 'io.github.scwang90:refresh-header-classics:2.0.5'    //经典刷新头
    implementation 'io.github.scwang90:refresh-footer-classics:2.0.5'    //经典加载
    //工具类-带缓存
    implementation 'com.blankj:utilcodex:1.31.0'

    //布局画布大小
    api 'me.jessyan:autosize:1.2.1'

    //MPAndroidChart
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'

    //AndroidPicker
    implementation 'com.github.gzu-liyujiang.AndroidPicker:WheelPicker:4.1.11'

    //本地数据库
    api 'org.litepal.guolindev:core:3.2.3'

    //刷脸
    api 'org.tensorflow:tensorflow-android:1.13.1'

    //视频播放器
    implementation('com.github.CarGuo.GSYVideoPlayer:GSYVideoPlayer:v8.3.4-release-jitpack') {
        exclude group: 'androidx.core'
    }

    //选择图片
    implementation 'io.github.lucksiege:pictureselector:v3.11.1'//基础
    implementation 'io.github.lucksiege:compress:v3.11.1'//图片压缩 (按需引入)
    implementation 'io.github.lucksiege:ucrop:v3.11.1'//图片裁剪 (按需引入)

    //串口读取
    api 'com.github.cl-6666:serialPort:v4.1.1'

    //zxing
    implementation 'com.github.jenly1314:zxing-lite:3.0.1'

    implementation 'com.jakewharton.threetenabp:threetenabp:1.4.0'

}