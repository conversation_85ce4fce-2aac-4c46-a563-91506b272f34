plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'kotlin-kapt'
}

android {
    compileSdk 33

    defaultConfig {
        minSdk 21
        targetSdk 33
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [
                        rxhttp_rxjava : '3.1.1',
                        rxhttp_package: 'rxhttp'
                ]
            }
        }
        renderscriptTargetApi 19
        renderscriptSupportModeEnabled true    // Enable RS support
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {

    api 'androidx.appcompat:appcompat:1.3.1'
    api 'com.google.android.material:material:1.4.0'
    api 'androidx.recyclerview:recyclerview:1.2.1'
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'

    implementation 'com.squareup.okhttp3:okhttp:4.9.1'
    implementation 'com.github.liujingxing.rxhttp:rxhttp:2.8.1'
    kapt 'com.github.liujingxing.rxhttp:rxhttp-compiler:2.8.1'
    api 'com.github.liujingxing.rxhttp:converter-fastjson:2.8.1'
    api 'io.reactivex.rxjava3:rxjava:3.1.1'
    api 'io.reactivex.rxjava3:rxandroid:3.0.0'
    api 'com.github.liujingxing.rxlife:rxlife-rxjava3:2.2.1' //管理RxJava3生命周期，页面销毁，关闭请求

    api 'com.github.li-xiaojun:XPopup:2.8.3'//https://github.com/li-xiaojun/XPopup
    api "com.github.kongzue.DialogX:DialogX:0.0.44.beta15"//https://github.com/kongzue/DialogX
    api 'io.github.youth5201314:banner:2.2.2'//https://github.com/youth5201314/banner
    api 'io.github.lucksiege:pictureselector:v2.7.3-rc10'
    api 'com.androidkun:XTabLayout:1.1.5'
    api 'q.rorbin:badgeview:1.1.3'
    api 'com.squareup.picasso:picasso:2.71828'
    api 'com.guolindev.permissionx:permissionx:1.6.1'
    api 'com.github.Chen-Xi-g:ToastUtils:1.2.0'

    api 'com.jakewharton:butterknife:10.2.1'
    annotationProcessor 'com.jakewharton:butterknife-compiler:10.2.1'
    api 'com.github.Ye-Miao:StatusBarUtil:1.7.5'
    api 'org.greenrobot:eventbus:3.1.1'


    api 'com.github.bumptech.glide:glide:4.12.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.12.0'

    compileOnly 'com.tencent.bugly:crashreport:4.1.9'
    implementation 'com.tencent.bugly:crashreport_upgrade:1.6.1'
    //1. 指定tinker依赖版本（注：应用升级1.3.5版本起，不再内置tinker）
    //2.为了便于解答问题，这里的tinker版本建议跟随此处demo设置，如果微信更新了tinker版本，bugly会定期同步更新
    implementation 'com.tencent.tinker:tinker-android-lib:1.9.14.22'

    //本地数据库
    api 'org.litepal.guolindev:core:3.2.3'

    //工具类-带缓存
    implementation 'com.blankj:utilcodex:1.31.0'
}