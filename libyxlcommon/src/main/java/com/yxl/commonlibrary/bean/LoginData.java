package com.yxl.commonlibrary.bean;

import org.litepal.crud.LitePalSupport;

import java.io.Serializable;
import java.util.List;

/**
 * Describe:用户信息（实体类）
 * Created by jingang on 2023/12/12
 */
public class LoginData extends LitePalSupport implements Serializable {
    /**
     * power_price : 1
     * power_name : 1
     * date : 2024-02-20
     * staff_position : 3
     * power_add : 1
     * power_count : 1
     * shop_longitude : 118.304593
     * power_delete : 1
     * powerPccus : 1
     * staff_recharge_cash : 0
     * shop_class : 0
     * powerPcstatis : 1
     * power_kind : 1
     * powePcstock : 1
     * negative_sale : 2
     * staff_phone : 15845565898
     * staff_modify_goods : 1
     * shopUnique : 1536215939565
     * powerPcselect : 1
     * staffName : 大象之家
     * mianmiStatus : 1
     * powerPcmodify : 1
     * power_supplier : 1
     * staff_no_change : 1
     * login_id : 189408
     * shop_unique : 1536215939565
     * shop_image_path : https://file.buyhoo.cc//image/1536215939565/1536215939565.jpeg
     * cashier_id : 3586
     * power_inprice : 1
     * powerPcrecharge : 1
     * powerPcset : 1
     * actionList : [{"code":"14b767798d4c448b9140be4db67c43e2","action_name":"充值","action_param":"pc_member_recharge","menu_code":"5cf8c5da2c6f4243ad9c0225feea11b0","id":111},{"code":"88b3d16dc58e4ccebfcdc5e3f5f6cb3c","action_name":"兑换","action_param":"pc_member_exchange","menu_code":"5cf8c5da2c6f4243ad9c0225feea11b0","id":112},{"code":"b7f843a865c04f709a281f676bbb634e","action_name":"更改","action_param":"pc_member_update","menu_code":"5cf8c5da2c6f4243ad9c0225feea11b0","id":113},{"code":"698aea822c764a0bb3a25cdf8147c025","action_name":"注册","action_param":"pc_member_register","menu_code":"5cf8c5da2c6f4243ad9c0225feea11b0","id":114},{"code":"dc862e5fc5f54898bc4a0030e5e9b702","action_name":"售价修改","action_param":"pc_update_selling_price","menu_code":"8e09dc5f82cd4b69a45069a44283aeaf","id":115},{"code":"f5584a4de9de4e5a913b9333128d23d2","action_name":"库存修改","action_param":"pc_update_stock","menu_code":"8e09dc5f82cd4b69a45069a44283aeaf","id":116},{"code":"3426c56c9dc545dc85ef227432bb4f68","action_name":"商品分类选择","action_param":"pc_stock_goods_calss","menu_code":"0c2da10c254042238fbe4387116baf69","id":117},{"code":"6a76475dc7554b01a195fc1948d4c5a6","action_name":"商品进价修改","action_param":"pc_update_purchase_price ","menu_code":"0c2da10c254042238fbe4387116baf69","id":118},{"code":"acb86e54ea5d4dec9133dffad3cfc7ae","action_name":"商品名称修改","action_param":"pc_update_goods_name","menu_code":"0c2da10c254042238fbe4387116baf69","id":119},{"code":"9405ea2eb1ff46af9b6323e631cea8f0","action_name":"商品删除","action_param":"pc_goods_delete","menu_code":"0c2da10c254042238fbe4387116baf69","id":120},{"code":"7ca6afbd2c4c4f8f91f0973aa1b89075","action_name":"商品保存","action_param":"pc_goods_add","menu_code":"0c2da10c254042238fbe4387116baf69","id":121},{"code":"ad3e0355c0bf4f219d8bc21965cb55ec","action_name":"入库","action_param":"pc_warehousing","menu_code":"0c2da10c254042238fbe4387116baf69","id":306},{"code":"4ffd4978078d434cb284b8dbfe54ca19","action_name":"出库","action_param":"pc_out_stock","menu_code":"0c2da10c254042238fbe4387116baf69","id":307},{"code":"93bc1b81f4504c519957eeb3079efc64","action_name":"商品会员价修改","action_param":"pc_update_member_price","menu_code":"0c2da10c254042238fbe4387116baf69","id":308},{"code":"57ae6b4069234724ae06f254a763d6cb","action_name":"增减","action_param":"pc_member_add_increase","menu_code":"5cf8c5da2c6f4243ad9c0225feea11b0","id":309},{"code":"95d51c4b17124d599c80588364ae93fd","action_name":"会员删除","action_param":"pc_member_delete","menu_code":"5cf8c5da2c6f4243ad9c0225feea11b0","id":310}]
     * powerPcnetorder : 1
     * shop_name : 金圈百货商家
     * token : Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjQ1LCJyblN0ciI6IjNDelRINjNKT2tYcjcya2x6YXdoa2x5OTVSQmRmZjg5In0.6ISVrUJBJom742vHq4Tn0HGmYJLufGMYRmIwptOy9D0
     * listLevel : [{"code":"7e70adaeab314257a79cc7e10f4b436b","level":"1","name":"收银","icon":"","parent_code":"-1","id":75,"terminal":"pc","type":"1","version":"ordinary","listLevelTwo":[{"code":"8e09dc5f82cd4b69a45069a44283aeaf","level":"2","name":"商品管理","icon":"","parent_code":"7e70adaeab314257a79cc7e10f4b436b","id":90,"terminal":"pc","type":"1","version":"ordinary","url":"","seq":1}],"url":"","seq":1},{"code":"1d89f180c4c349d4a53ae13398348015","level":"1","name":"查询","icon":"","parent_code":"-1","id":76,"terminal":"pc","type":"1","version":"ordinary","listLevelTwo":[{"code":"a7c52217bf14409d8bef21580fc33051","level":"2","name":"出入库记录","icon":"","parent_code":"1d89f180c4c349d4a53ae13398348015","id":81,"terminal":"pc","type":"1","version":"ordinary","url":"","seq":1},{"code":"0cb4f6d69c0346e1b8b459dfa88f3507","level":"2","name":"销售记录","icon":"","parent_code":"1d89f180c4c349d4a53ae13398348015","id":82,"terminal":"pc","type":"1","version":"ordinary","url":"","seq":2},{"code":"267594361dc74807b98b90e59f29b207","level":"2","name":"交接班记录","icon":"","parent_code":"1d89f180c4c349d4a53ae13398348015","id":83,"terminal":"pc","type":"1","version":"ordinary","url":"","seq":3},{"code":"a25fbec97d204fa980e3a1372ec435fc","level":"2","name":"商品信息列表","icon":"","parent_code":"1d89f180c4c349d4a53ae13398348015","id":84,"terminal":"pc","type":"1","version":"ordinary","url":"","seq":4},{"code":"9a172d316aad435b931198b3c6b4ef3c","level":"2","name":"商品销售统计","icon":"","parent_code":"1d89f180c4c349d4a53ae13398348015","id":114,"terminal":"pc","type":"1","version":"ordinary","url":"","seq":5}],"url":"","seq":2},{"code":"b8ed3ca9e2fa4f9eb51d719a15fc6834","level":"1","name":"网单","icon":"","parent_code":"-1","id":77,"terminal":"pc","type":"1","version":"ordinary","listLevelTwo":[{"code":"d20c0371ba30455eb38372529715d260","level":"2","name":"新订单","icon":"","parent_code":"b8ed3ca9e2fa4f9eb51d719a15fc6834","id":85,"terminal":"pc","type":"1","version":"ordinary","url":"","seq":1},{"code":"5ba1c215ab204f3a8e32273bbb33ad2a","level":"2","name":"待发货","icon":"","parent_code":"b8ed3ca9e2fa4f9eb51d719a15fc6834","id":86,"terminal":"pc","type":"1","version":"ordinary","url":"","seq":2},{"code":"9407ae064d654fd1a377e6bd5b98d131","level":"2","name":"配送中","icon":"","parent_code":"b8ed3ca9e2fa4f9eb51d719a15fc6834","id":87,"terminal":"pc","type":"1","version":"ordinary","url":"","seq":3},{"code":"a01df5df34b4495cacfa07f0b7df5df3","level":"2","name":"已完成","icon":"","parent_code":"b8ed3ca9e2fa4f9eb51d719a15fc6834","id":88,"terminal":"pc","type":"1","version":"ordinary","url":"","seq":4},{"code":"444f0de7afc44aef9034650cb4fedefa","level":"2","name":"已取消","icon":"","parent_code":"b8ed3ca9e2fa4f9eb51d719a15fc6834","id":89,"terminal":"pc","type":"1","version":"ordinary","url":"","seq":5}],"url":"","seq":3},{"code":"0c2da10c254042238fbe4387116baf69","level":"1","name":"入库","icon":"","parent_code":"-1","id":78,"terminal":"pc","type":"1","version":"ordinary","listLevelTwo":[],"url":"","seq":4},{"code":"5cf8c5da2c6f4243ad9c0225feea11b0","level":"1","name":"会员","icon":"","parent_code":"-1","id":79,"terminal":"pc","type":"1","version":"ordinary","listLevelTwo":[],"url":"","seq":5},{"code":"2db324a8a2754322bfa492201adc1aeb","level":"1","name":"统计","icon":"","parent_code":"-1","id":80,"terminal":"pc","type":"1","version":"ordinary","listLevelTwo":[],"url":"","seq":6},{"code":"abe928692ddb4e86a966da44b544f2d2","level":"1","name":"设置","icon":"","parent_code":"-1","id":793,"terminal":"pc","type":"1","version":"ordinary","listLevelTwo":[],"url":"","seq":7}]
     * below_cost : 1
     * powerPcgoods : 1
     * power_pur : 1
     * shop_hours : 00:00-23:59
     * shop_latitude : 35.134552
     * area_dict_num : 371302
     * shop_type : 1
     * power_change : 1
     * powerPcinprice : 1
     * shop_address_detail : 山东省临沂市兰山区南京路与卧虎山路交汇处 临沂应用科学城
     * time : 09:31:34
     */
    private String account;//登录账号
    private String pwd;//登录密码

    private int power_price;//修改商品售价权限 1.有 2.无
    private int power_name;//修改商品名称权限开关 1.有 2.无
    private String date;
    private int staff_position;//身份 3：店主，1：普通员工
    private int power_add;//新商品添加权限 1.有 2.无
    private int power_count;
    private double shop_longitude;
    private int power_delete;//商品删除权限 1.有 2.无
    private int powerPccus;//会员：1、开；2、关
    private int staff_recharge_cash;
    private int shop_class;
    private int powerPcstatis;//统计:1、开；2、关
    private int power_kind;//修改商品分类权限 1.有 2.无
    private int powePcstock;//出入库：1、开；2、关
    private int negative_sale;//负库存销售
    private String staff_phone;//雇员手机号
    private int staff_modify_goods;//雇员修改商品权限:1、有；2、无
    private String shopUnique;
    private int powerPcselect;//查询：1、开；2、关
    private String staffName;//雇员名称
    private int mianmiStatus;//免密支付：4、开通;其他未开通；
    private int powerPcmodify;//修改商品：1、开；2、关
    private int power_supplier;
    private int staff_no_change;
    private String login_id;//接班编号
    private long shop_unique;//重复了
    private String shop_image_path;//店铺头像
    private String cashier_id;//员工id
    private int power_inprice;
    private int powerPcrecharge;//收银员充值：1、开：2、关
    private int powerPcset;//设置：1、开；2、关
    private int powerPcnetorder;//网单:1、开；2、关
    private String shop_name;//店铺名称
    private String token;
    private int below_cost;//低于成本价销售
    private int powerPcgoods;//入库:1、开；2、关
    private int power_pur;//采购权限开关
    private String shop_hours;//营业时间
    private double shop_latitude;
    private String area_dict_num;//所在区县编号
    private int shop_type;
    private int power_change;//抹零权限
    private int powerPcinprice;//查看进价：1、开；2、关
    private String shop_address_detail;//店铺详细地址
    private String time;
    private List<ActionListBean> actionList;
    private List<ListLevelBean> listLevel;

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public int getPower_price() {
        return power_price;
    }

    public void setPower_price(int power_price) {
        this.power_price = power_price;
    }

    public int getPower_name() {
        return power_name;
    }

    public void setPower_name(int power_name) {
        this.power_name = power_name;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public int getStaff_position() {
        return staff_position;
    }

    public void setStaff_position(int staff_position) {
        this.staff_position = staff_position;
    }

    public int getPower_add() {
        return power_add;
    }

    public void setPower_add(int power_add) {
        this.power_add = power_add;
    }

    public int getPower_count() {
        return power_count;
    }

    public void setPower_count(int power_count) {
        this.power_count = power_count;
    }

    public double getShop_longitude() {
        return shop_longitude;
    }

    public void setShop_longitude(double shop_longitude) {
        this.shop_longitude = shop_longitude;
    }

    public int getPower_delete() {
        return power_delete;
    }

    public void setPower_delete(int power_delete) {
        this.power_delete = power_delete;
    }

    public int getPowerPccus() {
        return powerPccus;
    }

    public void setPowerPccus(int powerPccus) {
        this.powerPccus = powerPccus;
    }

    public int getStaff_recharge_cash() {
        return staff_recharge_cash;
    }

    public void setStaff_recharge_cash(int staff_recharge_cash) {
        this.staff_recharge_cash = staff_recharge_cash;
    }

    public int getShop_class() {
        return shop_class;
    }

    public void setShop_class(int shop_class) {
        this.shop_class = shop_class;
    }

    public int getPowerPcstatis() {
        return powerPcstatis;
    }

    public void setPowerPcstatis(int powerPcstatis) {
        this.powerPcstatis = powerPcstatis;
    }

    public int getPower_kind() {
        return power_kind;
    }

    public void setPower_kind(int power_kind) {
        this.power_kind = power_kind;
    }

    public int getPowePcstock() {
        return powePcstock;
    }

    public void setPowePcstock(int powePcstock) {
        this.powePcstock = powePcstock;
    }

    public int getNegative_sale() {
        return negative_sale;
    }

    public void setNegative_sale(int negative_sale) {
        this.negative_sale = negative_sale;
    }

    public String getStaff_phone() {
        return staff_phone;
    }

    public void setStaff_phone(String staff_phone) {
        this.staff_phone = staff_phone;
    }

    public int getStaff_modify_goods() {
        return staff_modify_goods;
    }

    public void setStaff_modify_goods(int staff_modify_goods) {
        this.staff_modify_goods = staff_modify_goods;
    }

    public String getShopUnique() {
        return shopUnique;
    }

    public void setShopUnique(String shopUnique) {
        this.shopUnique = shopUnique;
    }

    public int getPowerPcselect() {
        return powerPcselect;
    }

    public void setPowerPcselect(int powerPcselect) {
        this.powerPcselect = powerPcselect;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public int getMianmiStatus() {
        return mianmiStatus;
    }

    public void setMianmiStatus(int mianmiStatus) {
        this.mianmiStatus = mianmiStatus;
    }

    public int getPowerPcmodify() {
        return powerPcmodify;
    }

    public void setPowerPcmodify(int powerPcmodify) {
        this.powerPcmodify = powerPcmodify;
    }

    public int getPower_supplier() {
        return power_supplier;
    }

    public void setPower_supplier(int power_supplier) {
        this.power_supplier = power_supplier;
    }

    public int getStaff_no_change() {
        return staff_no_change;
    }

    public void setStaff_no_change(int staff_no_change) {
        this.staff_no_change = staff_no_change;
    }

    public String getLogin_id() {
        return login_id;
    }

    public void setLogin_id(String login_id) {
        this.login_id = login_id;
    }

    public long getShop_unique() {
        return shop_unique;
    }

    public void setShop_unique(long shop_unique) {
        this.shop_unique = shop_unique;
    }

    public String getShop_image_path() {
        return shop_image_path;
    }

    public void setShop_image_path(String shop_image_path) {
        this.shop_image_path = shop_image_path;
    }

    public String getCashier_id() {
        return cashier_id;
    }

    public void setCashier_id(String cashier_id) {
        this.cashier_id = cashier_id;
    }

    public int getPower_inprice() {
        return power_inprice;
    }

    public void setPower_inprice(int power_inprice) {
        this.power_inprice = power_inprice;
    }

    public int getPowerPcrecharge() {
        return powerPcrecharge;
    }

    public void setPowerPcrecharge(int powerPcrecharge) {
        this.powerPcrecharge = powerPcrecharge;
    }

    public int getPowerPcset() {
        return powerPcset;
    }

    public void setPowerPcset(int powerPcset) {
        this.powerPcset = powerPcset;
    }

    public int getPowerPcnetorder() {
        return powerPcnetorder;
    }

    public void setPowerPcnetorder(int powerPcnetorder) {
        this.powerPcnetorder = powerPcnetorder;
    }

    public String getShop_name() {
        return shop_name;
    }

    public void setShop_name(String shop_name) {
        this.shop_name = shop_name;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public int getBelow_cost() {
        return below_cost;
    }

    public void setBelow_cost(int below_cost) {
        this.below_cost = below_cost;
    }

    public int getPowerPcgoods() {
        return powerPcgoods;
    }

    public void setPowerPcgoods(int powerPcgoods) {
        this.powerPcgoods = powerPcgoods;
    }

    public int getPower_pur() {
        return power_pur;
    }

    public void setPower_pur(int power_pur) {
        this.power_pur = power_pur;
    }

    public String getShop_hours() {
        return shop_hours;
    }

    public void setShop_hours(String shop_hours) {
        this.shop_hours = shop_hours;
    }

    public double getShop_latitude() {
        return shop_latitude;
    }

    public void setShop_latitude(double shop_latitude) {
        this.shop_latitude = shop_latitude;
    }

    public String getArea_dict_num() {
        return area_dict_num;
    }

    public void setArea_dict_num(String area_dict_num) {
        this.area_dict_num = area_dict_num;
    }

    public int getShop_type() {
        return shop_type;
    }

    public void setShop_type(int shop_type) {
        this.shop_type = shop_type;
    }

    public int getPower_change() {
        return power_change;
    }

    public void setPower_change(int power_change) {
        this.power_change = power_change;
    }

    public int getPowerPcinprice() {
        return powerPcinprice;
    }

    public void setPowerPcinprice(int powerPcinprice) {
        this.powerPcinprice = powerPcinprice;
    }

    public String getShop_address_detail() {
        return shop_address_detail;
    }

    public void setShop_address_detail(String shop_address_detail) {
        this.shop_address_detail = shop_address_detail;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public List<ActionListBean> getActionList() {
        return actionList;
    }

    public void setActionList(List<ActionListBean> actionList) {
        this.actionList = actionList;
    }

    public List<ListLevelBean> getListLevel() {
        return listLevel;
    }

    public void setListLevel(List<ListLevelBean> listLevel) {
        this.listLevel = listLevel;
    }

    public static class ActionListBean {
        /**
         * code : 14b767798d4c448b9140be4db67c43e2
         * action_name : 充值
         * action_param : pc_member_recharge
         * menu_code : 5cf8c5da2c6f4243ad9c0225feea11b0
         * id : 111
         */

        private String code;
        private String action_name;
        private String action_param;
        private String menu_code;
        private int id;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getAction_name() {
            return action_name;
        }

        public void setAction_name(String action_name) {
            this.action_name = action_name;
        }

        public String getAction_param() {
            return action_param;
        }

        public void setAction_param(String action_param) {
            this.action_param = action_param;
        }

        public String getMenu_code() {
            return menu_code;
        }

        public void setMenu_code(String menu_code) {
            this.menu_code = menu_code;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }
    }

    public static class ListLevelBean {
        /**
         * code : 7e70adaeab314257a79cc7e10f4b436b
         * level : 1
         * name : 收银
         * icon :
         * parent_code : -1
         * id : 75
         * terminal : pc
         * type : 1
         * version : ordinary
         * listLevelTwo : [{"code":"8e09dc5f82cd4b69a45069a44283aeaf","level":"2","name":"商品管理","icon":"","parent_code":"7e70adaeab314257a79cc7e10f4b436b","id":90,"terminal":"pc","type":"1","version":"ordinary","url":"","seq":1}]
         * url :
         * seq : 1
         */

        private String code;
        private String level;
        private String name;
        private String icon;
        private String parent_code;
        private int id;
        private String terminal;
        private String type;
        private String version;
        private String url;
        private int seq;
        private List<ListLevelTwoBean> listLevelTwo;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getLevel() {
            return level;
        }

        public void setLevel(String level) {
            this.level = level;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getParent_code() {
            return parent_code;
        }

        public void setParent_code(String parent_code) {
            this.parent_code = parent_code;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getTerminal() {
            return terminal;
        }

        public void setTerminal(String terminal) {
            this.terminal = terminal;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public int getSeq() {
            return seq;
        }

        public void setSeq(int seq) {
            this.seq = seq;
        }

        public List<ListLevelTwoBean> getListLevelTwo() {
            return listLevelTwo;
        }

        public void setListLevelTwo(List<ListLevelTwoBean> listLevelTwo) {
            this.listLevelTwo = listLevelTwo;
        }

        public static class ListLevelTwoBean {
            /**
             * code : 8e09dc5f82cd4b69a45069a44283aeaf
             * level : 2
             * name : 商品管理
             * icon :
             * parent_code : 7e70adaeab314257a79cc7e10f4b436b
             * id : 90
             * terminal : pc
             * type : 1
             * version : ordinary
             * url :
             * seq : 1
             */

            private String code;
            private String level;
            private String name;
            private String icon;
            private String parent_code;
            private int id;
            private String terminal;
            private String type;
            private String version;
            private String url;
            private int seq;

            public String getCode() {
                return code;
            }

            public void setCode(String code) {
                this.code = code;
            }

            public String getLevel() {
                return level;
            }

            public void setLevel(String level) {
                this.level = level;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getIcon() {
                return icon;
            }

            public void setIcon(String icon) {
                this.icon = icon;
            }

            public String getParent_code() {
                return parent_code;
            }

            public void setParent_code(String parent_code) {
                this.parent_code = parent_code;
            }

            public int getId() {
                return id;
            }

            public void setId(int id) {
                this.id = id;
            }

            public String getTerminal() {
                return terminal;
            }

            public void setTerminal(String terminal) {
                this.terminal = terminal;
            }

            public String getType() {
                return type;
            }

            public void setType(String type) {
                this.type = type;
            }

            public String getVersion() {
                return version;
            }

            public void setVersion(String version) {
                this.version = version;
            }

            public String getUrl() {
                return url;
            }

            public void setUrl(String url) {
                this.url = url;
            }

            public int getSeq() {
                return seq;
            }

            public void setSeq(int seq) {
                this.seq = seq;
            }
        }
    }
}
