package com.yxl.commonlibrary.utils;

import org.greenrobot.eventbus.EventBus;

/**
 * Descride:事件总线管理
 * Created by jingang on 2020/11/26
 */
public class EventBusManager {
    private static EventBusManager busManager;
    private EventBus globalEventBus;

    private EventBusManager() {
    }

    public EventBus getGlobaEventBus() {
        if (globalEventBus == null) {
            globalEventBus = new EventBus();
        }
        return globalEventBus;
    }

    public static EventBusManager getInstance() {
        if (busManager == null) {
            synchronized (EventBusManager.class) {
                if (busManager == null) {
                    busManager = new EventBusManager();
                }
            }
        }
        return busManager;
    }
}
