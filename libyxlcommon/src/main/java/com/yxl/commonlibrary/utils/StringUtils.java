package com.yxl.commonlibrary.utils;

import android.text.TextUtils;

public class StringUtils {

    /**
     * 手机号中间四位***
     *
     * @param mobile
     * @return
     */
    public static String getStarMobile(String mobile) {
        if (!TextUtils.isEmpty(mobile)) {
            if (mobile.length() >= 11)
                return mobile.substring(0, 3) + "****" + mobile.substring(7);
        } else {
            return "";
        }
        return mobile;
    }

    /**
     * 显示第一个字，后面的字全部替换为星号（*）
     * @param name
     * @return
     */
    public static String formattedName(String name) {
        StringBuilder sb = new StringBuilder();
        sb.append(name.charAt(0));
        for (int i = 1; i < name.length(); i++) {
            sb.append('*');
        }
        return sb.toString();
    }

    /**
     * 重组图片地址
     * 1.判断非空 2.转换斜杠 3.判断是否含有"http"
     *
     * @param url
     * @return
     */
    public static String handledImgUrl(String url) {
        if (url == null) return "";
        url = url.replace("\\", "/");
        if (url.contains("http")) return url;
        return "https://file.buyhoo.cc/" + url;
//        return "https://file.allscm.net/" + url;
    }
}
