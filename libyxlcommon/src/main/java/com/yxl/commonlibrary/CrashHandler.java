package com.yxl.commonlibrary;

import android.content.Context;
import android.os.Build;



/**
 * 自定义的 异常处理类 , 实现了 UncaughtExceptionHandler接口
 * Created by sks on 2016/1/26.
 */
public class CrashHandler implements Thread.UncaughtExceptionHandler {
    // 需求是 整个应用程序 只有一个 MyCrash-Handler，so,单例
    private static CrashHandler INSTANCE=new CrashHandler();
    private Thread.UncaughtExceptionHandler defalutHandler; // 系统默认的UncaughtException处理类

    private Context mContext;
    //1.私有化构造方法
    private CrashHandler() {

    }

    public static synchronized CrashHandler getInstance() {
        /*if (INSTANCE == null) {
            synchronized (CrashHandler.class) {
                if (INSTANCE == null) {
                    INSTANCE = new CrashHandler();
                }
            }
        }*/
        return INSTANCE;
    }

    public void init(Context context) {
        // 获取系统默认的UncaughtException处理器
//        this.mContext = context.getApplicationContext();
        this.mContext = context;
        defalutHandler = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler(this);
        // 设置该CrashHandler为程序的默认处理器
    }

    @Override
    public void uncaughtException(Thread thread, Throwable ex) {


        if(!handleException(ex) && defalutHandler != null){
            //如果用户没有处理则让系统默认的异常处理器来处理
            defalutHandler.uncaughtException(thread, ex);
        }else{
            try{
                Thread.sleep(2000);
            }catch (InterruptedException e){
            }
            AppManager.getInstance().AppExit();
//            ActivityManager.getInstance().appExit();
            // 退出程序
//            ((MyApplication)mContext).exit();

//            Intent intent = new Intent(context.getApplicationContext(), WelcomeActivity.class);
//            PendingIntent restartIntent = PendingIntent.getActivity( context.getApplicationContext(), 0, intent, PendingIntent.FLAG_UPDATE_CURRENT);
//            //退出程序
//            AlarmManager mgr = (AlarmManager)context.getSystemService(Context.ALARM_SERVICE);
//            mgr.set(AlarmManager.RTC, System.currentTimeMillis() + 1000, restartIntent); // 1秒钟后重启应用



        }
    }
    /**
     * 自定义错误处理,收集错误信息 发送错误报告等操作均在此完成.
     *
     * @param ex
     * @return true:如果处理了该异常信息;否则返回false.
     */
    private boolean handleException(Throwable ex) {
        if (ex == null) {
            return false;
        }

        StringBuilder sb = new StringBuilder();
//        sb.append("天呐程序挂掉了：").append(BuildConfig.FLAVOR).append("，").append(Build.MODEL).append("_").append(Build.VERSION.SDK_INT).append("\n");
        sb.append("天呐程序挂掉了：").append("，").append(Build.MODEL).append("_").append(Build.VERSION.SDK_INT).append("\n");

        sb.append(ex.toString()).append(" \n ");

        StackTraceElement[] stacks = ex.getStackTrace();
        if(stacks!=null){
            for(StackTraceElement stack : stacks){
                sb.append(stack.toString()).append(" \n ");
            }
        }

//        PgyCrashManager.reportCaughtException(mContext, (Exception) ex);
        return true;
    }
}
