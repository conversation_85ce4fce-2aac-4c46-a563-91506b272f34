package com.yxl.commonlibrary.http;


import android.util.Log;

import java.util.List;

import okhttp3.RequestBody;
import rxhttp.wrapper.annotation.Param;
import rxhttp.wrapper.entity.KeyValuePair;
import rxhttp.wrapper.param.FormParam;
import rxhttp.wrapper.param.Method;

@Param(methodName = "PostMyFormParam")
public class PostMyFormParam extends FormParam {

    public PostMyFormParam(String url) {
        super(url, Method.POST);  //Method.POST代表post请求
    }
    @Override
    public RequestBody getRequestBody() {
        List<KeyValuePair> params= getBodyParam();
        if (params != null && params.size() > 0){
            StringBuilder stringBuilder = new StringBuilder();
            for (KeyValuePair keyValuePair : params) {
                if (stringBuilder.toString().length() == 0) {
                    stringBuilder.append(getUrl());
                    stringBuilder.append("?");
                }else {
                    stringBuilder.append("&");

                }
                stringBuilder.append(keyValuePair.getKey()).append("=").append(keyValuePair.getValue());
            }

            Log.e("PostMyFormParam","**********************param请求参数*************\n"+stringBuilder.toString());
        }


        return super.getRequestBody();
    }
}