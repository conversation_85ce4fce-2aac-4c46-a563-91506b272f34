package com.yxl.commonlibrary.http;


import android.util.Log;

import java.util.Map;

import okhttp3.RequestBody;
import rxhttp.wrapper.annotation.Param;
import rxhttp.wrapper.param.JsonParam;
import rxhttp.wrapper.param.Method;

@Param(methodName = "PostMyJsonFormParam")
public class PostMyJsonFormParam extends JsonParam {

    public PostMyJsonFormParam(String url) {
        super(url, Method.POST);  //Method.POST代表post请求
    }
    @Override
    public RequestBody getRequestBody() {
        Map<String, Object> params= getParams();
        if (params != null && params.size() > 0) {
            StringBuilder stringBuilder = new StringBuilder();

            for (String key : params.keySet()) {
                if (stringBuilder.toString().length() == 0) {
                    stringBuilder.append(getUrl());
                    stringBuilder.append("?");
                }else {
                    stringBuilder.append("&");

                }
                stringBuilder.append(key).append("=").append(params.get(key));
            }
            Log.e("PostMyJsonFormParam","**********************param请求参数*************\n"+stringBuilder.toString());

        }

        return super.getRequestBody();
    }
}