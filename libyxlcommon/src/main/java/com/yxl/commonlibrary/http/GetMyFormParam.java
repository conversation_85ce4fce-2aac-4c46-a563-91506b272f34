package com.yxl.commonlibrary.http;


import android.util.Log;

import java.util.List;

import okhttp3.HttpUrl;
import rxhttp.wrapper.annotation.Param;
import rxhttp.wrapper.entity.KeyValuePair;
import rxhttp.wrapper.param.Method;
import rxhttp.wrapper.param.NoBodyParam;

@Param(methodName = "GetMyForm")
public class GetMyFormParam extends NoBodyParam {

    public GetMyFormParam(String url) {
        super(url, Method.GET);  //Method.POST代表post请求
    }

    @Override
    public HttpUrl getHttpUrl() {
        List<KeyValuePair> keyValuePairs = getKeyValuePairs();
//        String encryptStr = "加密后的字符串";  //根据上面拿到的参数，自行实现加密逻辑
//        addHeader("encryptStr", encryptStr);
//        String data= (String) keyValuePairs.get(0).getValue();
//        String params = new String(Base64.decode(data, Base64.DEFAULT));
        StringBuilder stringBuilder = new StringBuilder();

        for (KeyValuePair keyValuePair : keyValuePairs) {
            if (stringBuilder.toString().length() == 0) {
                stringBuilder.append(getUrl());
                stringBuilder.append("?");
            }else {
                stringBuilder.append("&");

            }
            stringBuilder.append(keyValuePair.getKey()).append("=").append(keyValuePair.getValue());
        }
        Log.e("GetMyFormParam","**********************param请求参数*************\n"+stringBuilder.toString());
        return super.getHttpUrl();
    }

   /* @Override
    public RequestBody getRequestBody() {
        //这里拿到你添加的所有参数
        List<KeyValuePair> keyValuePairs = getKeyValuePairs();
//        String encryptStr = "加密后的字符串";  //根据上面拿到的参数，自行实现加密逻辑
//        addHeader("encryptStr", encryptStr);
        String data= (String) keyValuePairs.get(0).getValue();
        String params = new String(Base64.decode(data, Base64.DEFAULT));
        return super.getRequestBody();
    }*/
}