package com.yxl.commonlibrary;

import android.app.Activity;

import java.util.Iterator;
import java.util.Stack;

/**
 * 应用模块:
 * <p>
 * 类描述: Activity的堆栈管理者
 * <p>
 *
 * <AUTHOR>
 * @since 2020-02-25
 */
public class AppManager
{
    private static Stack<Activity> activityStack;
    
    private AppManager()
    {
    }
    
    private static class SingleHolder
    {
        private static AppManager instance = new AppManager();
    }
    
    public static AppManager getInstance()
    {
        return SingleHolder.instance;
    }
    
    /**
     * 添加Activity到堆栈
     */
    public void addActivity(Activity activity)
    {
        if (activityStack == null)
        {
            activityStack = new Stack<Activity>();
        }
        activityStack.add(activity);
    }
    
    /**
     * 移除指定的Activity
     */
    public void removeActivity(Activity activity)
    {
        if (activity != null)
        {
            activityStack.remove(activity);
        }
    }
    
    /**
     * 是否有activity
     */
    public boolean isActivity()
    {
        if (activityStack != null)
        {
            return !activityStack.isEmpty();
        }
        return false;
    }
    
    /**
     * 获取当前Activity（堆栈中最后一个压入的）
     */
    public Activity currentActivity()
    {
        Activity activity = activityStack.lastElement();
        return activity;
    }
    
    /**
     * 结束当前Activity（堆栈中最后一个压入的）
     */
    public void finishActivity()
    {
        Activity activity = activityStack.lastElement();
        finishActivity(activity);
    }
    
    /**
     * 结束指定的Activity
     */
    public void finishActivity(Activity activity)
    {
        if (activity != null)
        {
            if (!activity.isFinishing())
            {
                activity.finish();
            }
        }
    }
    
    /**
     * 结束指定类名的Activity
     */
    public void finishActivity(Class<?> cls)
    {
        for (Activity activity : activityStack)
        {
            if (activity.getClass().equals(cls))
            {
                finishActivity(activity);
                break;
            }
        }
    }
    
    /**
     * 结束所有Activity
     */
    public void finishAllActivity()
    {
        for (int i = 0, size = activityStack.size(); i < size; i++)
        {
            if (null != activityStack.get(i))
            {
                finishActivity(activityStack.get(i));
            }
        }
        activityStack.clear();
    }
    
    /**
     * 获取指定的Activity
     *
     * <AUTHOR>
     */
    public Activity getActivity(Class<?> cls)
    {
        if (activityStack != null)
        {
            for (Activity activity : activityStack)
            {
                if (activity.getClass().equals(cls))
                {
                    return activity;
                }
            }
        }
        return null;
    }
    /**
     * 结束全部Activity 除忽略的 Activity 外
     * @param clazz
     */
    public void finishAllActivityToIgnore(final Class<?> clazz) {
        if (clazz == null) return;
        synchronized (activityStack) {
            // 保存新的任务,防止出现同步问题
            Stack<Activity> stack = new Stack<>();
            stack.addAll(activityStack);
            // 清空全部,便于后续操作处理
            activityStack.clear();
            // 进行遍历移除
            Iterator<Activity> iterator = stack.iterator();
            while (iterator.hasNext()) {
                Activity activity = iterator.next();
                // 判断是否想要关闭的Activity
                if (activity != null) {
                    if (!(activity.getClass() == clazz)) {
                        // 如果 Activity 没有finish 则进行finish
                        if (!activity.isFinishing()) {
                            activity.finish();
                        }
                        // 删除对应的Item
                        iterator.remove();
                    }
                } else {
                    // 删除对应的Item
                    iterator.remove();
                }
            }
            // 把不符合条件的保存回去
            activityStack.addAll(stack);
            // 移除,并且清空内存
            stack.clear();
            stack = null;
        }
    }
    /**
     * 退出应用程序
     */
    public void AppExit()
    {
        try {
            finishAllActivity();
            // 退出JVM(java虚拟机),释放所占内存资源,0表示正常退出(非0的都为异常退出)
            System.exit(0);
            // 从操作系统中结束掉当前程序的进程
            android.os.Process.killProcess(android.os.Process.myPid());
        } catch (Exception e) {
            // =
            System.exit(-1);
        }
    }
}
