package com.yxl.commonlibrary.base;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.Gson;
import com.yxl.commonlibrary.AppManager;
import com.yxl.commonlibrary.bean.LoginData;

import org.litepal.LitePal;

/**
 * BaseApplication
 */
public class BaseApplication extends Application {
    private static BaseApplication sInstance;
    public static String login_address = "cn.bl.mobile.buyhoostore.ui_new.login.LoginActivity";

    @Override
    public void onCreate() {
        super.onCreate();
        LitePal.initialize(this);
    }

    /**
     * 当宿主没有继承自该Application时,可以使用set方法进行初始化baseApplication
     */
    public void setApplication(@NonNull BaseApplication application) {
        sInstance = application;
        application.registerActivityLifecycleCallbacks(new ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(@NonNull Activity activity,
                                          @Nullable Bundle savedInstanceState) {
                AppManager.getInstance().addActivity(activity);
            }

            @Override
            public void onActivityStarted(@NonNull Activity activity) {

            }

            @Override
            public void onActivityResumed(@NonNull Activity activity) {

            }

            @Override
            public void onActivityPaused(@NonNull Activity activity) {

            }

            @Override
            public void onActivityStopped(@NonNull Activity activity) {

            }

            @Override
            public void onActivitySaveInstanceState(
                    @NonNull Activity activity, @NonNull Bundle outState) {

            }

            @Override
            public void onActivityDestroyed(@NonNull Activity activity) {
                AppManager.getInstance().removeActivity(activity);
            }
        });
    }


    /**
     * 获得当前app运行的Application
     */
    public static BaseApplication getInstance() {
        if (sInstance == null) {
            throw new NullPointerException("please inherit BaseApplication or call setApplication.");
        }
        return sInstance;
    }

    /**********************************用户信息start***********************************/
    private LoginData loginData;

    /**
     * 登录信息
     *
     * @return
     */
    public LoginData getLoginData(String loginId) {
        if (TextUtils.isEmpty(loginId)) {
            loginData = null;
        } else {
            loginData = LitePal.where("login_id = ?", loginId).findFirst(LoginData.class);
        }
        return loginData;
    }

    /**
     * 登录信息json
     *0
     * @return
     */
    public String getLoginDataStr() {
        if (loginData == null) {
            return "";
        } else {
            return new Gson().toJson(loginData);
        }
    }

    /**
     * 店铺编号
     *
     * @return
     */
    public String getToken() {
        if (loginData == null) {
            return "";
        } else {
            return loginData.getToken();
        }
    }

    /**
     * 店铺编号
     *
     * @return
     */
    public String getShopUnique() {
        if (loginData == null) {
            return "";
        } else {
            return loginData.getShopUnique();
        }
    }

    /**
     * 店铺名称
     *
     * @return
     */
    public String getShopName() {
        if (loginData == null) {
            return "";
        } else {
            return loginData.getShop_name();
        }
    }

    /**
     * 获取员工编号
     */
    public String getStaffUnique() {
        if (loginData == null) {
            return "";
        } else {
            return loginData.getCashier_id();
        }
    }

    /**
     * 获取员工名称
     *
     * @return
     */
    public String getStaffName() {
        if (loginData == null) {
            return "";
        } else {
            return loginData.getStaffName();
        }
    }

    /**
     * 获取员工电话
     *
     * @return
     */
    public String getStaffMobile() {
        if (loginData == null) {
            return "";
        } else {
            return loginData.getStaff_phone();
        }
    }

}
