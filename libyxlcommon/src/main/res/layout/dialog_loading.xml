<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/dialog_loading_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="150dp"
        android:layout_height="110dp"
        android:background="@drawable/loading_bg"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingBottom="10dp"
        android:paddingLeft="21dp"
        android:paddingRight="21dp"
        android:paddingTop="10dp">

        <ProgressBar
            android:id="@+id/progressBar1"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:layout_gravity="center_horizontal"
            android:indeterminateBehavior="repeat"
            android:indeterminateDrawable="@drawable/dialog_loading"
            android:indeterminateOnly="true" />

        <TextView
            android:id="@+id/tipTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:text="loading"
            android:textColor="#f0f0f0"
            android:textSize="15sp" />
    </LinearLayout>

</LinearLayout>