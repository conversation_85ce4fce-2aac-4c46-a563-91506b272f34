<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- android:theme="Theme.Light.NoTitleBar" 白色背景并无标题栏 -->
    <!-- android:theme="Theme.Translucent.NoTitleBar"  透明背景并无标题 -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="SplashTheme" parent="AppTheme.NoActionBar">
        <!--<item name="android:windowIsTranslucent">true</item>  &lt;!&ndash; 透明背景 &ndash;&gt;-->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowBackground">@android:color/white</item>
        <!--        <item name="android:windowFullscreen">true</item>-->
        <!--<item name="android:windowIsTranslucent">false</item>-->
        <item name="android:windowDisablePreview">true</item>
    </style>


    <!-- ======= -->
    <style name="base_dialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:background">#26000000</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
        <item name="android:windowFrame">@null</item>

        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:colorBackgroundCacheHint">@null</item>

    </style>


    <style name="tab_layout_style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">16dp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="MyDialogStyle">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>

</resources>